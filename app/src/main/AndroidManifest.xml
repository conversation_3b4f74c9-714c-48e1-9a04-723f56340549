<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.enveu">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_INTERNAL_STORAGE" />


    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <application
        android:name=".OttApplication"
        android:allowBackup="false"
        android:fullBackupContent="@xml/backup_descriptor"
        android:hardwareAccelerated="true"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:resizeableActivity="false"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/MyMaterialTheme"
        android:usesCleartextTraffic="true"
        tools:replace="allowBackup">
        <activity
            android:name=".ugc.ShortVideoUploadActivity"
            android:exported="false" />
        <activity
            android:name=".activities.follow_follower.HashtagsActivity"
            android:exported="false" />
        <activity
            android:name=".activities.detail.ui.LiveLinearDetailsActivity"
            android:exported="false" />
        <activity
            android:name=".ugc.GridListActivity"
            android:exported="false" />
        <activity
            android:name=".jwplayer.player.LivePlayerActivity"
            android:exported="true"
            android:screenOrientation="landscape"
            android:supportsPictureInPicture="true"
            android:configChanges="locale|screenSize|smallestScreenSize|screenLayout|orientation"/>
        <activity
            android:name=".activities.follow_follower.ProfileReportActivity"
            android:exported="false"
            android:theme="@style/MyMaterialTheme"/>
        <activity
            android:name=".activities.settings.UserInterestActivity"
            android:exported="false" />
        <activity
            android:name=".activities.biography.BiographyActivity"
            android:exported="false"
            android:theme="@style/MyMaterialTheme"/>
        <activity
            android:name=".activities.secondary_profile.ParentalControlActivity"
            android:exported="false"
            android:theme="@style/MyMaterialTheme"/>
        <activity
            android:name=".activities.secondary_profile.AvatarImageActivity"
            android:exported="false"
            android:theme="@style/MyMaterialTheme"/>
        <activity
            android:name=".activities.secondary_profile.EditSecondaryProfileActivity"
            android:exported="false"
            android:theme="@style/MyMaterialTheme"/>
        <activity
            android:name=".activities.follow_follower.FollowerFollowingActivity"
            android:exported="false"
            android:theme="@style/MyMaterialTheme"/>
        <activity
            android:name=".activities.follow_follower.FollowFollowingProfileActivity"
            android:exported="false"
            android:theme="@style/MyMaterialTheme"/>
        <activity
            android:name=".activities.secondary_profile.SecondaryProfileActivity"
            android:exported="false"
            android:theme="@style/MyMaterialTheme"/>
        <activity
            android:name=".activities.contentpreferences.SettingContentPreferences"
            android:exported="false"
            android:theme="@style/MyMaterialTheme"/>
        <activity
            android:name=".jwplayer.cast.ExpandedControlsActivity"
            android:exported="true"
            android:launchMode="singleInstance"
            android:parentActivityName=".jwplayer.player.PlayerActivity"
            android:screenOrientation="landscape"
            android:theme="@style/playerTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>

            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.enveu.jwplayer.player.PlayerActivity" />
        </activity>
        <activity
            android:name=".activities.search.ui.ActivitySearch"
            android:configChanges="orientation|screenSize"
            android:launchMode="singleTop"
            android:windowSoftInputMode="stateVisible" /> <!-- <meta-data -->
        <!-- android:name="com.google.android.gms.cast.framework.OPTIONS_PROVIDER_CLASS_NAME" -->
        <!-- android:value="com.tv.jwplayer.cast.CastOptionsProvider" /> -->
        <activity
            android:name=".jwplayer.player.PlayerActivity"
            android:exported="true"
            android:screenOrientation="landscape"
            android:resizeableActivity="false"
            android:supportsPictureInPicture="true"
            android:theme="@style/playerTheme"
            android:configChanges="locale|screenSize|smallestScreenSize|screenLayout|orientation"/>
        <activity
            android:name=".activities.purchase.ui.PurchaseActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.profile.activate_device.ActivateDeviceActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.profile.ui.AccountSettingActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.profile.ui.ManageSubscriptionAccount"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.usermanagment.ui.ActivityForgotPassword"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/MyMaterialTheme"/>
        <activity
            android:name=".activities.usermanagment.ui.ActivityLogin"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.device_management.DeviceManagerActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.profile.CountryListActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.usermanagment.ui.ActivitySignUp"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.usermanagment.ui.EnterOTPActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.usermanagment.ui.ActivitySelectSubscriptionPlan"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.usermanagment.ui.ChangePasswordActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.sponsorArtist.ArtistAndSponserActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.manageAccounts.ManageAccount"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity android:name=".activities.settings.downloadsettings.changequality.ui.ChangeDownloadQuality" />
        <activity android:name=".activities.settings.downloadsettings.DownloadSettings" />
        <activity
            android:name=".utils.helpers.downloads.DownloadedVideoActivity"
            android:label="@string/title_activity_downloaded_video"
            android:screenOrientation="landscape"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activities.downloads.MyDownloads"
            android:screenOrientation="locked"
            android:theme="@style/MyMaterialTheme" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.ly.img.editor.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
<!--            <meta-data-->
<!--                android:name="android.support.FILE_PROVIDER_PATHS"-->
<!--                android:resource="@xml/file_paths" />-->
        </provider>

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/gma_ad_services_config"
            tools:replace="android:resource" />

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:exported="false"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:exported="false"
            android:value="@string/facebook_client_token" />

        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:exported="false"
            android:label="@string/app_name" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="@string/fb_login_protocol_scheme"
                    tools:ignore="ManifestResource" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.splash.ui.ActivitySplash"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/SplashScreenTheme">

            <!-- Handle custom scheme -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:scheme="enveushare"
                    android:host="@string/package_name"
                    android:pathPrefix="/detail" />
            </intent-filter>

            <!-- Default launcher -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>






        <meta-data
            android:name="io.branch.sdk.TestMode"
            android:value="false" />

        <activity
            android:name=".activities.profile.ui.ProfileActivityNew"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MyMaterialTheme"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".activities.homeactivity.ui.HomeActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:resizeableActivity="false"
            android:theme="@style/MyMaterialTheme"
            android:screenOrientation="portrait"/>
        <activity
            android:name=".activities.detail.ui.DetailActivity"
            android:configChanges="locale|screenSize|smallestScreenSize|screenLayout|orientation"
            android:resizeableActivity="false"
            android:supportsPictureInPicture="true"
            android:theme="@style/MyMaterialTheme" />
        <activity
            android:name=".activities.detail.ui.MatchDetailActivity"
            android:configChanges="locale|screenSize|smallestScreenSize|screenLayout|orientation"
            android:resizeableActivity="true"
            android:supportsPictureInPicture="true"
            android:theme="@style/MyMaterialTheme" />
        <activity
            android:name=".activities.detail.ui.EpisodeActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:screenOrientation="portrait"
            android:theme="@style/MyMaterialTheme" />

        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3940256099942544~**********" /> <!-- Branch install referrer tracking -->
        <activity
            android:name=".activities.watchList.ui.WatchListActivity"
            android:launchMode="singleTop" />
        <activity android:name=".activities.notification.ui.NotificationActivity" />
        <activity
            android:name=".activities.listing.ui.GridActivity"
            android:configChanges="orientation|screenSize"
            android:launchMode="singleTop" />
        <activity
            android:name=".activities.privacypolicy.ui.WebViewActivity"
            android:launchMode="singleTop" />
        <activity
            android:name=".activities.series.ui.SeriesDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>
        <activity
            android:name=".activities.search.ui.ActivityResults"
            android:configChanges="orientation|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.listing.ui.MyListActivity"
            android:configChanges="orientation|screenSize"
            android:launchMode="singleTop" />
        <activity
            android:name=".cms.HelpActivity"
            android:launchMode="singleTop" />
        <activity
            android:name=".activities.web_view.WebViewActivity"
            android:launchMode="singleTop" />
        <activity
            android:name=".activities.videoquality.ui.ChangeLanguageActivity"
            android:launchMode="singleTop" />
        <activity
            android:name=".activities.settings.ActivitySettings"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.listing.listui.ListActivity"
            android:configChanges="orientation|screenSize"
            android:launchMode="singleTop" />

        <service
            android:name=".OttMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_launcher" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/series_detail_all_episode_txt_color" />
        <meta-data
            android:name="brightcove_account_id"
            android:value="@string/brightcove_account_id" />
        <meta-data
            android:name="brightcove_policy_key"
            android:value="@string/brightcove_policy_key" />
        <meta-data
            android:name="ovp_api_key"
            android:value="@string/ovp_api_key" />
        <meta-data
            android:name="api_key_mobile"
            android:value="@string/api_key_mobile" />
        <meta-data
            android:name="api_key_tab"
            android:value="@string/api_key_tab" />
        <meta-data
            android:name="firebase_performance_logcat_enabled"
            android:value="true" />

        <activity
            android:name=".activities.videoquality.ui.VideoQualityActivity"
            android:screenOrientation="portrait"/>

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <meta-data
            android:name="com.google.android.gms.ads.AD_MANAGER_APP"
            android:value="true" />
        <meta-data
            android:name="preloaded_fonts"
            android:resource="@array/preloaded_fonts" />

        <receiver
            android:name=".jwplayer.receiver.ConnectivityReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>

        <activity
            android:name=".activities.profile.order_history.ui.OrderHistoryActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.usermanagment.ui.PaymentDetailPage"
            android:exported="false"
            android:screenOrientation="portrait" />

        <service
            android:name=".activities.service.BackgroundAudioService"
            android:exported="false"
            android:foregroundServiceType="specialUse"
            android:screenOrientation="portrait" />
        <service
            android:name=".activities.customservices.EntBackgroundAudioService"
            android:exported="false"
            android:foregroundServiceType="specialUse"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activities.customservices.EntBackgroundAudioActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
    </application>

</manifest>