{"data": {"config": {"user_setting": "User Settings", "login_sign_in": "Sign In", "login_forgot_pwd": "Forget Password", "login_or_sign_in_with": "or Sign In with", "artist_button_text": "Continue", "artist_result_not_found": "Sorry No Results Found!", "login_do_not_an_account": "Don't have an account?", "login_or_log_in_with": "or Log in With", "login_let_s_get_started_text": "Let's Get Started", "sign_up_with_tesliya": "Enter your details to get start on TESLIYA", "login_signup": "Sign Up", "sign_up_to_continue": "Sign up to continue", "app_setting": "App Settings", "display_name": "Enter your display Name", "this_subscibtion_web": "You purchased the subscription on Web. Please log in on your web device to cancel.", "this_subscibtion_ios": "You purchased the subscription on Web. Please log in on your ios device to cancel.", "subscription_activated": "Subscription Activated 🎉", "signup_email_address": "Email", "profile_enter_user_name": "Enter user name", "popup_payment_success": "Purchased Successfully", "hold_period_alert": "Account on Hold!", "hold_period_alert_desc": "Update your payment method to resuming viewing.", "grace_period_alert_desc": "Your Account is in Grace Period! Update your payment method to keep enjoying uninterrupted access.", "grace_period_alert": "<PERSON> Period Alert", "this_subscibtion_was_activated_by_bussiness": "This Subscription was activated by Business and can not be canceled. Please reach out to support team", "signup_enter_your_city": "Enter Your City", "signup_create_pwd": "Password", "signup_confirm_pwd": "Confirm Password", "signup_term_condition": "I accept Terms & Conditions and Privacy Policy", "signup_signup": "Sign Up", "sign_out": "Sign Out,Sign out of session", "signup_or_signup_with": "or Sign Up with", "signup_already_have_an_account": "Already have an account?", "login_email_text": "e-mail", "already_have_account_sign": "Already have an account", "signup_signin": "Sign In", "sign_up_now_and_enjoy_our_amazing_library_of_creation_research_content": "Please enter username, age & select gender it allows us to mark you as genuine user of TESLIYA.", "forgot_enter_email": "Forgot Password", "forgot_description": "Please key in your registered email Address", "forgot_continue": "Submit", "otp_enter_otp": "Enter OTP", "new_password": "New Password", "otp_digit_code_send": "Enter the 6-digit code send to", "otp_continue": "Verify OTP", "otp_resend_otp": "Resend OTP", "otp_has_been_expired": "OTP has been expired", "otp_code_valid_00_min": "OTP code valid for 00:00m", "otp_code_valid": "OTP code valid for 04:49min", "otp_code_valid_for": "OTP code valid for", "profile_enter_name": "Enter your Name", "update_playlist_button_title_text": "Update", "enter_your_email": "Enter your Email", "profile_mobile_number": "Mobile Number (Optional)", "profile_dob": "Enter your DOB", "profile_country": "Country (Optional)", "profile_city": "City (Optional)", "device_management": "Manage Devices,Control monitor connected devices", "enter_the_code_displayed_on_your_tv": "Enter the code displayed on your TV", "activate_tv_device": "Activate TV,Enable Streaming device access", "profile_update_btn": "Save Changes", "sponsor": "Sponsor Artist,Sponsor an artist of your choice", "profile_dlt_btn": "Delete Account?", "profile_my_profile": "Edit Profile", "change_pwd_change_pwd": "Change Password", "profile": "My Profile", "my_profile": "My Profile", "change_pwd_new_pwd": "New Password", "change_pwd_confirm_pwd": "Confirm Password", "ep_dialog_settings_title": "Settings", "change_pwd_update_pwd": "Update Password", "today": "Today", "change_language_comman": "Change Language", "buy_select_subscription": "Please Select Your \n Subscription", "buy_restore_subscription": "Restore Purchase", "buy_pay_now": "Buy Now", "buy_in_case_query": "In case of any query, email <NAME_EMAIL>", "order_history_title": "Transaction History", "order_history_payment_mode": "Payment Mode", "order_history_payment_status": "Payment Status", "order_history_order_Id": "Order Id", "order_history_transaction_type": "Transaction Type", "settings_title": "Settings and Preferences", "settings_change_lang": "Languages", "more_my_playlist": "Manage Playlists,Organize, curate and personalize playlists", "header_my_playlist": "Manage Playlists", "settings_streaming_settings": "Video Quality", "settings_interests": "Interests", "streaming_settings_title": "Video Quality", "ep_video_auto": "Auto", "streaming_auto": "Auto", "content_prefrences": "Content Preferences", "streaming_hd": "Medium", "streaming_full_hd": "High", "streaming_sd": "Low", "lang_change_lang": "Change Language", "lang_english": "English", "lang_arabic": "عربي", "language_english": "English", "cancel_text": "Cancel", "under_review": "Under Review", "please_enter_new_password": "Please enter New Password", "home": "Home", "could_not_find_any_result": "Sorry! we could not find any results.", "home_tabbar": "Home", "already_registered_just_sign_in": "Already registered? Just sign in below", "user_name": "User Name", "terms_and_privacy_policy_text": "I accept the Terms and Conditions and Privacy Policy", "vod_tabbar": "VOD", "related_videos": "Related Videos", "detail_page_episodes": "Episodes", "account_title": "Account", "detail_page_related_videos": "Related Videos", "detail_page_produced_by": "Produced by :", "detail_page_sponsored_by": "Sponsored by :", "detail_page_my_list": "My List", "detail_page_share": "Share", "program": "Program", "detail_page_like": "Like", "detail_page_play": "Watch Now", "detail_page_trailer": "Watch Trailer", "trailer": "Trailer", "skip": "<PERSON><PERSON>", "delete_account": "Delete Account", "heading_episodes": "Episodes", "noti_title": "Notifications", "noti_no_new_notifications": "No new notifications", "popular_search": "Popular Searches", "recent_searches": "Recent Searches", "cancel_search": "Cancel", "no_result": "No results found", "more_gaming": "Gaming", "my_playlist": "My WatchList,Selected shows for streaming", "ugc_create": "UGC Create,Create your shorts", "my_favorite_for_ugc": "My Favorite,Select Favorite for UGC", "my_list": "My WatchList", "my_watch_list_title": "My List", "remove_video_from_list": "Video removed from My List", "my_history_more": "My Watch History, Record of Viewed Content", "more_account": "Account", "manage_profile": "Manage Profiles,Manage your account profiles", "parental_control_menu_text": "Parental Control,Authorize and Update PIN", "account_title_text": "Account", "more_settings": "Settings & Preferences", "more_buy_now": "Subscribe", "ep_settings_subtitle": "Subtitle Settings", "ep_settings_audio": "Multi Audio", "more_order_history": "Transaction History,Record of financial activities", "more_privacy_policy": "Privacy Policy", "more_contact_us": "Contact Us", "more_term_condition": "Terms & Conditions", "more_manage_subscription": "Manage Subscription", "account_edit_profile": "Edit Profile", "account_change_password": "Change Password,Modify Access credentials securely", "detail_title": "Enter your registered email address below and we will send you instructions on resetting your password.", "account_logout": "Logout", "check_your_email": "Check your email", "popup_username_pwd_does_not_match": "Username or Password doesn't match.", "popup_request_sent": "Request Sent", "forgot_password_response": "Thanks for putting the request. If we find the email address in our system, we will send the reset link over the mail please enter new password", "popup_thanks_for_putting_request": "Thanks for putting the request. If we find the email address in our system, we will send the reset link over the mail please enter new password", "popup_check_your_email": "Check your email", "popup_pwd_must_be_8_to_16_char": "Passwords must be at least 6–20 characters", "popup_user_already_has_an_account": "User already has an account with this email or credential.", "popup_error": "Error", "user_already_exists": "User already has an account with this email or credential.", "popup_mobile_number_digit": "Mobile number should be 9 digit", "popup_update": "Update", "popup_update_successfully": "Update successfully", "popup_delete_account": "Delete Account", "popup_want_to_delete_account": "Your account will be permanently deleted", "popup_continue": "Continue", "popup_cancel": "Cancel", "popup_under_review": "Under Review", "popup_your_previous_account_already_review": "Your Previous Account deletion request is already under review. Our team will get back with the details on the same", "popup_pls_enter_new_pwd": "Please enter New Password", "popup_pwd_pwd_changed": "Password Changed", "no_internet_connection": "No internet connection found.\\nCheck your connection.", "popup_pwd_has_been_changed": "Your password has been changed successfully", "popup_check_internet_connection": "It seems like there is a problem with your internet connection. Please check your\nconnection and try again", "popup_no_internet_connection_found": "No internet connection found.\nCheck your connection.", "popup_remove_content_from_my_list": "Are you sure do you want to remove this content from My list?", "popup_no_new_notification": "No new notifications", "popup_recent_search": "Recent Searches", "yes_text": "Yes", "popup_delete_search_history": "Are you sure you want to delete the search history?", "popup_search_yes": "Yes", "popup_search_no": "No", "sign_out_all_device": "Sign Out All Other Devices?", "popup_subscribe_access_to_premium": "Access to premium content is just a click away. Please visit mobile apps or website", "popup_subscribe_purchase": "Purchase", "popup_logout": "Are you sure you want to Sign Out?", "popup_logout_you_want_to_logout": "You will be asked to sign in again to watch your favourites", "popup_change_language_tittle": "Change Language", "popup_do_you_want_to_change_lang": "Do you want to change language", "manage_profile_sub_heading": "Manage Profiles", "popup_enter_valid_otp": "Invalid OTP, please enter valid one.", "popup_expire_time": "Time Expire", "popup_please_click_on_the_resend_button": "Please click on the resend button to get new OTP", "popup_empty_email_tittle": "Empty Email", "popup_empty_last_name_tittle": "Empty Last Name", "Please Enter the Email Address": "Please Enter the Email Address", "popup_empty_last_name_subtitle": "Last name cannot be empty", "val popup_empty_first_name_subtitle": "First name cannot be empty", "popup_empty_email_subtitle": "Please Enter Email", "popup_invalid_email_tittle": "<PERSON><PERSON><PERSON>", "invalid_email_address": "Please Enter a valid Email Address", "popup_invalid_email_subtitle": "Enter a valid email address format. eg: <EMAIL>", "popup_continue_btn": "Continue", "popup_incorrect_pwd_tittle": "Incorrect Password", "Updated": "Updated", "popup_login_enter_pwd_tittle": "Please enter Password", "popup_profile_error": "Error", "popup_something_went_wrong": "Something went wrong", "popup_change_pwd_enter_confirm_pwd": "Please enter Confirm Password", "popup_change_pwd_your_pwd_does_not_match": "Sorry, your password doesn't match please check your password again", "popup_user_not_verify": "You need to verify your account to access the selected content", "popup_verify": "Verify", "popup_select_plan": "Access to premium content is just a click away. Please select the plan option", "no_result_desc": "Try searching for something else or try with a different spelling", "popup_ok": "Ok", "popup_minutes": "min", "popup_purchase": "Buy", "popup_remove": "Remove", "countinue": "Continue", "popup_user_does_not_exists": "User with this email id does not exist", "popup_payment_error": "There was an error processing your request, please try after some time. If additional support is required, please contact us at", "popup_payment_success_Desc": "Enjoy unlimited access to exclusive content. Drive in and start watching now!", "popup_confirm_pwd": "Confirm Password", "popup_your_account_has_been_verified": "Your Account Has been Verified Successfully", "popup_date_difference": "You must be 12 years old and above.", "popup_payment_description": "We’ve sent you an e-mail, please enter in your inbox and confirm your account to be able to login Bread Gang TV", "popup_empty_Password": "Empty password", "popup_empty_Confirm_Password": "Empty confirm password", "popup_invalid_Confirm_Password": "Invalid confirm password", "empty_password": "Please enter Password", "confirm_password": "Confirm Password", "otp_skip": "<PERSON><PERSON>", "re_enter_password": "Enter Password", "enter_email_address": "Enter Email Address", "popup_empty_message_Password": "Please Enter Password", "create_password": "Create 8-16 characters password", "sorry_your_password_doesn_t_match_please_check_your_password_again": "Sorry, your password doesn’t match please check your password again.", "podcast_tabbar": "Podcast", "kids_tabbar": "Kids", "title_free": "Podcast", "movie": "Movie", "series_tabbar": "Series &amp; Docs", "movie_tabbar": "Movie", "series": "Series", "more_tabbar": "More", "select_gender": "Select Gender", "ep_prev_episode": "Previous Episode", "ep_next_episode": "Next Episode", "play": "Play", "no_result1": "Try searching for another episode, movie or show", "more_title_btn": "More", "detail_page_no_data_available": "No data available", "geo_blocking_title": "Apologies. Our service is not available in your country", "related_data_see_all": "See all", "detail_page_now_playing": "Now playing", "search_hint": "Search", "search": "Search", "episode": "Episode", "type_subtitle": "Subtitle", "type_audio": "", "search_result": "Search Results", "detail_page_all_episode": "All Episode", "delete_notification_all": "Are you sure you want to delete all notifications?", "delete_notification_single": "Are you sure you want to delete this notification?", "no_internet": "No internet connection found.", "detail_page_season": "Season", "no_connection_title": "You’re Offline", "no_connection_description": "It seems like there is a problem with your\n internet connection. Please check your\n connection and try again", "no_connection_btn_txt": "Reload App", "popup_empty_dob_tittle": "Empty Dob", "popup_empty_dob_subtitle": "Please Enter the Dob", "email_verify_title": "Verify", "email_verified_title": "Verified", "popup_continue_watching": "Continue Watching", "popup_continue_watching_for": "Continue watching for", "popup_change_pwd_incorrect_confirm_pwd": "Incorrect confirm Password", "search_see_all": "See all", "search_movies": "Movies", "search_documentaries": "Documentaries", "documentaries": "Documentaries", "search_results": "Results", "more_activate_device": "Activate TV Device", "rate_the_app": "Rate the app", "free_trail": "Free Trial", "subscription_Active": "ACTIVE", "not_verified": "Not Verified", "exclusive_content": "All Yojma TV & Exclusive Contents", "popup_notEntitled": "You Need to Purchase This\nContent", "MATCH_START_ON": "Match will start on", "MATCH_READY": "Match is about to start", "MATCH_NOT_ATTENDED": "Match is yet to start", "MATCH_END": "Match is completed", "STREAM_ERROR": "We could not process your request. Please start again", "MATCH_ABOUT_END": "Match is about to end", "popup_invalid_mobile_subtitle": "Please Enter Valid mobile number", "popup_invalid_mobile_tittle": "Invalid Mobile", "nothing_your_watchHistory_tittle": "Your watch history is currently empty", "nothing_your_watchHistory_description": "Start listening your favorite songs, and they'll appear here", "logout_confirmation": "Are you sure you want to sign out?", "sign_out1": "Sign Out", "you_can_only_select_five_items": "You can only select up to 5 items.", "thanks_for_sponsership": "Thank you for your sponsorship", "full_name": "Full Name"}}, "responseCode": 2000, "debugMessage": null}