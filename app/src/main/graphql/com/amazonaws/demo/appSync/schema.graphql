query getUser($id : ID!) {
  getAppUser (id: $id) {
    id
	name
	email
	profilePicURL
	gender
	dateOfBirth
	phoneNumber
	isVerified
	registrationType
	profileStep
	isFbLinked
	isManualLinked
	isGPlusLinked
	status
	notificationCount
	showNotification
	bio
	dateCreated
	lastUpdated
  }
}

subscription fetchUser($id : ID!){
    onUpdatedAppUser(id : $id){
   id
	name
	email
	profilePicURL
	gender
	dateOfBirth
	phoneNumber
	isVerified
	registrationType
	profileStep
	isFbLinked
    isManualLinked
    isGPlusLinked
	status
	notificationCount
	showNotification
	bio
	dateCreated
	lastUpdated
    }
}