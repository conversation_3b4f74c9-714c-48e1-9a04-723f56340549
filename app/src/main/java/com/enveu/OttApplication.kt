package com.enveu

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.pm.PackageManager
import androidx.multidex.MultiDex
import androidx.multidex.MultiDexApplication
import com.enveu.utils.Logger
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.google.android.gms.tasks.OnCompleteListener
import com.google.firebase.FirebaseApp
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.messaging.FirebaseMessaging

class OttApplication : MultiDexApplication() {


    override fun onCreate() {
        super.onCreate()
        instance = this
        KsPreferenceKeys.getInstance()
        MultiDex.install(this)
        initializeFirebase()
        firebaseCrashlyticSetup()
        createNotificationChannel()
        setUpFirebase()
        // setUpMoengage();
    }

    private fun setUpFirebase() {
        FirebaseApp.initializeApp(this)
    }

    private fun initializeFirebase() {
        FirebaseMessaging.getInstance().token
            .addOnCompleteListener(OnCompleteListener { task ->
                if (!task.isSuccessful) {
                    Logger.d("Fetching FCM registration token failed", task.exception.toString())
                    return@OnCompleteListener
                }

                // Get new FCM registration token
                val token = task.result

                // Log and toast
                Logger.d("TokenIs", token!!)
                //    Toast.makeText(OttApplication.this, token, Toast.LENGTH_SHORT).show();
            })
    }

    /*  private void setUpMoengage() {
        MoEngageManager.INSTANCE.init(this);
        MoEFireBaseHelper.getInstance().addTokenListener(MoEngageManager.INSTANCE);
    }
*/
    private fun firebaseCrashlyticSetup() {
        if (KsPreferenceKeys.getInstance().appPrefLoginStatus.equals(
                AppConstants.UserStatus.Login.toString(),
                ignoreCase = true
            )
        ) {
            val userId = KsPreferenceKeys.getInstance().appPrefUserId
            FirebaseCrashlytics.getInstance().setUserId(userId)
        }
    }

    // Huh? Really?
    val version: Int
        get() {
            var v = 0
            try {
                v = packageManager.getPackageInfo(packageName, 0).versionCode
            } catch (e: PackageManager.NameNotFoundException) {
                // Huh? Really?
            }
            return v
        }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    // Huh? Really?
    val versionName: String
        get() {
            var v = ""
            try {
                v = packageManager.getPackageInfo(packageName, 0).versionName.toString()
            } catch (e: PackageManager.NameNotFoundException) {
                // Huh? Really?
            }
            return v
        }

    companion object {
        var instance: OttApplication? = null
            private set
        const val CHANNEL_ID = "JWPlayer"
        private const val CHANNEL_NAME = "JWPlayer Notifications"
        private const val CHANNEL_IMPORTANCE = NotificationManager.IMPORTANCE_LOW
        private const val CHANNEL_DESCRIPTION =
            "This is the default notification channel for the JW Player"
        val context: Context
            get() = instance!!.applicationContext

        fun getApplicationContext(context: Context): OttApplication {
            return context.applicationContext as OttApplication
        }
    }


    private fun createNotificationChannel() {
        //Create a notification channel
        val channel = NotificationChannel(
            CHANNEL_ID, CHANNEL_NAME,
            CHANNEL_IMPORTANCE
        )
        channel.description = CHANNEL_DESCRIPTION

        //Register channel with the System.
        val manager = getSystemService(
            NotificationManager::class.java
        )
        manager.createNotificationChannel(channel)
    }
}