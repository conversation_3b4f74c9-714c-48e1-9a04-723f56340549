package com.enveu.activities.biography

import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.databinding.DataBindingUtil
import com.enveu.R
import com.enveu.baseModels.BaseActivity
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.databinding.ActivityBiographyBinding
import com.enveu.utils.constants.AppConstants

class BiographyActivity : BaseBindingActivity<ActivityBiographyBinding>() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initUI()

    }

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityBiographyBinding {
        return ActivityBiographyBinding.inflate(layoutInflater)
    }

    private fun initUI() {
        binding.bioToolbar.searchIcon.visibility = View.GONE
        val bundle = intent?.getBundleExtra("bio_bundle")
        val bioTitle = bundle?.getString(AppConstants.BUNDLE_BIO_TITLE)
        binding.bioToolbar.titleToolbarBiography.text = bioTitle
        binding.bioToolbar.iconBackpress.setOnClickListener {
            finish()
        }

        val biographyDesription = bundle?.getString(AppConstants.BIO_DISCRIPTION)

        if (!biographyDesription.isNullOrEmpty()) {
            binding.bioWebview.settings.javaScriptEnabled=true
            binding.bioWebview.webViewClient= WebViewClient()
            binding.bioWebview.setBackgroundColor(Color.BLACK)
            binding.bioWebview.post {
                binding.bioWebview.evaluateJavascript("""
                (function() {
                    var style = document.createElement('style');
                    style.type = 'text/css';
                    style.innerHTML = 'body { color: white; background-color: black; font-size: 20px} a { color: cyan; }';
                    document.head.appendChild(style);
                })();
                """.trimIndent(), null)
            }
            binding.bioWebview.loadDataWithBaseURL(null,biographyDesription,"text/html", "UTF-8", null)
            binding.tvBioError.visibility = View.GONE
            binding.bioWebview.visibility=View.VISIBLE
        } else {
            binding.tvBioError.visibility = View.VISIBLE
        }




    }
}