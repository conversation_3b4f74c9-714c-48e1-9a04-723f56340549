package com.enveu.activities.contentpreferences

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.enveu.OttApplication.Companion.context
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.homeactivity.viewmodel.HomeViewModel
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.adapters.SettingContentPreferencesAdapter
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.userProfile.UserProfileResponse
import com.enveu.beanModelV3.searchGenres.SearchGenres
import com.enveu.callbacks.commonCallbacks.SaveCheckedGenreClickListener
import com.enveu.databinding.ActivitySettingContentPreferencesBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.utils.Logger
import com.enveu.utils.ObjectHelper
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import com.moengage.core.internal.utils.showToast
import java.util.Objects

class SettingContentPreferences : BaseBindingActivity<ActivitySettingContentPreferencesBinding>() ,CommonDialogFragment.EditDialogListener {
    private var railInjectionHelper: RailInjectionHelper? = null
    var viewModel: RegistrationLoginViewModel? = null
    private var homeViewModel: HomeViewModel? = null
    private lateinit var saveCheckedGenreClickListener : SaveCheckedGenreClickListener
    var mLastClickTime : Long = 0
    private var selectedGenre: SearchGenres.Data.Item?=null
    private var mIsLoading = true
    private var selectedArtistId=""
    private var contentPrefRecyclerViewAdapter: SettingContentPreferencesAdapter? = null
    private var appUserIntrests: ArrayList<Int>? = ArrayList()
    private val stringsHelper by lazy { StringsHelper }
    private var name :String = ""
    private var country :String = ""
    private var city :String = ""
    private var gender :String = ""
    private var lastName :String = ""
    private var mobileNumber :String = ""
    private var dob = 0.0

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivitySettingContentPreferencesBinding {
        return ActivitySettingContentPreferencesBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        AnalyticsUtils.trackScreenView(this, AppConstants.ARTISTS)
        super.onCreate(savedInstanceState)
        connectionObserver()
    }

    private fun setViewModel() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        viewModel = ViewModelProvider(this)[RegistrationLoginViewModel::class.java]
        homeViewModel = ViewModelProvider(this)[HomeViewModel::class.java]
    }

    private fun connectionObserver() {
        if (NetworkConnectivity.isOnline(this)) {
            connectionValidation(true)
        } else {
            connectionValidation(false)
        }
    }

    private fun connectionValidation(isConnected: Boolean) {
        if (isConnected){
            AnalyticsUtils.trackScreenView(this, AppConstants.SPONSOR_ARTIST)
            saveCheckedGenreClickListener = object : SaveCheckedGenreClickListener {
                override fun onSaveClick(save1: Boolean, save: MutableSet<String>?) {
                    binding.btnSave.visibility = View.VISIBLE
                }
            }
            binding?.rvCircleImages?.layoutManager = GridLayoutManager(this, 3)
            toolbar()
            setViewModel()
            setClick()
            getProfile()
        }else{
            noConnectionLayout()
        }
    }

    private fun getProfile() {
        val authToken = KsPreferenceKeys.getInstance().appPrefAccessToken
        viewModel?.hitUserProfile(this@SettingContentPreferences,authToken)?.observe(this) {
            if (it != null) {
                if (it.status) {
                    Logger.e("profileRes", it.toString())
                    if (it.data.deletionRequestStatus != null) {
                        val deletionRequestStatus: String = it.data.deletionRequestStatus
                        KsPreferenceKeys.getInstance().deleteAccountRequestStatus = deletionRequestStatus == "UNDER_REVIEW"
                    }
                    selectedArtistId= it.data.customData.sponsoredArtist?:""
                    KsPreferenceKeys.getInstance().sponsorArtistId = selectedArtistId

                    if(!it.data.appUserInterest.isNullOrEmpty()) {
                        appUserIntrests = it.data.appUserInterest
                    }
                    //   updateUI(it)
                    dismissLoading(binding?.progressBar)
                    getAllArtist(null,selectedArtistId )
                }
                if (it.responseCode == 4302) {
                    //   isloggedout = true
                    logoutCall()
                    ActivityLauncher.getInstance().goToLogin(this@SettingContentPreferences, ActivityLogin::class.java)

                    try {
                        runOnUiThread { onBackPressed() }
                    } catch (_: java.lang.Exception) {
                    }
                } else if (it.responseCode == 4019) {
                    commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ), it.debugMessage.toString(), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                } else if (it.responseCode == 4901) {
                    commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ), it.debugMessage.toString(), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                }
            }else{
                showToast(this, resources.getString(R.string.popup_something_went_wrong))
//                commonDialog(stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
//                    getString(R.string.popup_error)
//                ),stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
//                    getString(R.string.something_went_wrong)
//                ) , stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
            }
        }
    }
    private fun setClick() {
        binding?.btnSave?.setOnClickListener {
            binding?.progressBar?.visibility= View.VISIBLE
            updateProfile()
        }
    }

    private fun updateProfile() {
        val token = KsPreferenceKeys.getInstance().appPrefAccessToken
        val countryCode = KsPreferenceKeys.getInstance().countryCode
        val intList = contentPrefRecyclerViewAdapter?.selectedGenresList()?.mapNotNull { it.toIntOrNull() } // Safely convert to Int, skipping invalid entries
        val sendGenresList =  ArrayList<Int>()
        if (intList != null) {
            sendGenresList.addAll(intList)
        }
        viewModel?.updateSponsor(token, sendGenresList, selectedArtistId,name,lastName,countryCode,mobileNumber,dob.toString(),country,city,gender)?.observe(this
        ) {
            if (it != null) {
                if (it.status) {
                    val gson = Gson()
                    val userProfileData = gson.toJson(it)
                    KsPreferenceKeys.getInstance().userProfileData = userProfileData
                    KsPreferenceKeys.getInstance().sponsorArtistId = selectedGenre?.id.toString()
                    Logger.e("userdata",userProfileData)
                    getProfileData(it)
                    Toast.makeText(this, resources.getString(R.string.popup_update_successfully), Toast.LENGTH_SHORT).show()
                    finish()

                }else{
                    if(it.responseCode==4302){
                        dismissLoading(binding?.progressBar)
                        logoutCall()
                        ActivityLauncher.getInstance().goToLogin(this@SettingContentPreferences, ActivityLogin::class.java)
                        try {
                            runOnUiThread { onBackPressed() }
                        } catch (e: java.lang.Exception) {
                            Logger.d(e.toString())
                        }
                    }else if(it.responseCode==4019){
                        commonDialog(it.debugMessage.toString(),"",stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                    }else{
                        if (it.debugMessage!=null){
                            commonDialog(it.debugMessage.toString(),"",stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                        }else{
                            showToast(this, resources.getString(R.string.popup_something_went_wrong))
                        }
                    }
                }
            }else{
                showToast(this, resources.getString(R.string.popup_something_went_wrong))
            }
            binding?.progressBar?.visibility= View.GONE
        }
    }


    private fun getProfileData(it: UserProfileResponse) {
        try {
            name = it.data.name as String
            lastName = it.data?.customData?.lastName!!
            mobileNumber = it.data?.customData?.mobileNumber.toString()
            if (ObjectHelper.isNotEmpty(it.data.dateOfBirth)) {
                dob = it.data.dateOfBirth as Double
            }
            if (it.data.customData.country !=null) {
                country = it.data.customData.country
            }
            if (it.data.customData != null) {
                if (it.data.customData.city != null)
                    city =  it.data.customData.city
            }
            if (it.data.gender != null) {
                if (it.data.gender != null)
                    gender = it.data.gender.toString()
            }

            KsPreferenceKeys.getInstance().appPrefUserName = (it.data.name as String)
            KsPreferenceKeys.getInstance().appPrefUserLastName = (it.data?.customData?.lastName)
            KsPreferenceKeys.getInstance().appPrefMobileNumber = (it.data.customData.mobileNumber.toString())

        } catch (e: java.lang.Exception) {
            Logger.w(e)
        }
    }


    private fun logoutCall() {
        if (CheckInternetConnection.isOnline(Objects.requireNonNull(this))) {
            val preference = KsPreferenceKeys.getInstance()
            clearCredientials(preference)
            hitApiLogout(this, KsPreferenceKeys.getInstance().appPrefAccessToken)
        }else{
            commonDialog(stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                getString(R.string.popup_no_internet_connection_found)
            ), "", stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
        }
    }
    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun getAllArtist(keyword: String?,selectedId:String) {
        val preSelectedIds: List<String> = loadSelectedIdsFromStorage()
        binding?.progressBar?.visibility = View.VISIBLE

        viewModel?.selectedIds?.observe(this) { selectedIds ->
            if (contentPrefRecyclerViewAdapter != null) {
                contentPrefRecyclerViewAdapter?.saveSelectedIdsToStorage(context, selectedIds)
            }
        }
        if (viewModel?.selectedIds?.value.isNullOrEmpty()) {
            viewModel?.selectedIds?.value = preSelectedIds.toMutableSet()
        }

        homeViewModel?.getSearchGenres("GENRES", "CUSTOM")?.observe(this){ it ->
            binding?.progressBar?.visibility = View.GONE
            if (it != null && it.data?.items?.isNotEmpty() == true) {
                binding?.tvNoResultFound?.visibility = View.GONE
                binding?.rvCircleImages?.visibility = View.VISIBLE
                if (keyword == null) {
                    if (contentPrefRecyclerViewAdapter==null) {
                        val selectedGenresMutableList: MutableSet<String>? = appUserIntrests?.map { it.toString() }?.toMutableSet()
                        contentPrefRecyclerViewAdapter = SettingContentPreferencesAdapter(this,selectedId, it.data.items, saveCheckedGenreClickListener, selectedGenresMutableList)
                        binding?.rvCircleImages?.adapter = contentPrefRecyclerViewAdapter
                    }else{
                        contentPrefRecyclerViewAdapter?.updateData(it.data.items)
                        mIsLoading = it.data.items.size != contentPrefRecyclerViewAdapter!!.itemCount
                    }
                }
                else{
                    contentPrefRecyclerViewAdapter?.updateData(it.data.items)
                }
            }
            else{
                if (contentPrefRecyclerViewAdapter?.list?.isEmpty() == true){
                    binding?.tvNoResultFound?.visibility = View.VISIBLE
                    binding?.rvCircleImages?.visibility = View.GONE
                }else{
                    contentPrefRecyclerViewAdapter?.updateData(it.data?.items)
                }
                binding?.tvNoResultFound?.visibility = View.VISIBLE
                binding?.btnSave?.visibility = View.GONE
            }
        }
    }

    private fun loadSelectedIdsFromStorage(): List<String> {
        val sharedPreferences = context.getSharedPreferences(AppConstants.PREFS_SELECTED_IDS, Context.MODE_PRIVATE)
        val selectedIdsSet = sharedPreferences.getStringSet(AppConstants.SELECTED_IDS, emptySet())
        return selectedIdsSet?.toList() ?: emptyList()
    }

    private fun noConnectionLayout() {
        binding?.connection?.noConnectionLayout?.visibility = View.VISIBLE
        binding?.connection?.retryTxt?.setOnClickListener { connectionObserver() }
    }

    private fun toolbar() {
        binding?.toolbar?.apply {
            logoMain2.visibility = View.GONE
            searchIcon.visibility = View.GONE
            backLayout.visibility = View.VISIBLE
            titleMid.visibility = View.VISIBLE
            titleMid.text = getText(R.string.genere)
            skip.visibility = View.VISIBLE
            skip.setOnClickListener {
                ActivityLauncher.getInstance().homeScreen(this@SettingContentPreferences, HomeActivity::class.java,false,"","",0)
            }
            titleMid.setBackgroundResource(0)
            backLayout.setOnClickListener { onBackPressed() }
        }
    }


    override fun onActionBtnClicked() {
        onBackPressed()
    }

    override fun onCancelBtnClicked() {
    }
}