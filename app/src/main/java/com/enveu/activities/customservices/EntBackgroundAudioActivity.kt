package com.enveu.activities.customservices
import android.annotation.SuppressLint
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import android.view.View
import android.widget.RelativeLayout
import androidx.appcompat.app.AppCompatActivity
import com.enveu.BuildConfig
import com.enveu.R
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.jwplayer.controls.AudioPlayerControlView
import com.enveu.jwplayer.listener.AudioControlClickListener
import com.enveu.jwplayer.model.PlayBackSpeedItem
import com.enveu.utils.Logger
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.jwplayer.pub.api.PlayerState
import com.jwplayer.pub.api.license.LicenseUtil
import com.jwplayer.pub.view.JWPlayerView


class EntBackgroundAudioActivity : AppCompatActivity(), EntBackgroundAudioService.PlayerTimeCallBack,
    EntBackgroundAudioService.OnVideoComplete {
    private var jwLicenseKey: String = ""
    private var isDrmDisabled: Boolean? = false
    private var mPlayerView: JWPlayerView? = null
    private var audioPlayerControlView: AudioPlayerControlView? = null
    private var tittle:String? = null
    private var mService: EntBackgroundAudioService.ServiceBinder? = null
    private var externalRefId: String? = null
    private var assetId = 0
    private var mIsBound = false
    private var isBingeWatchEnable: Boolean? = false
    private var mContainer: RelativeLayout? = null
    var audioList: List<EnveuVideoItemBean>? = null
    private var currentPlayingIndex: Int? = -1
    var selectedSpeed = 1.0
    private var speedTracks: MutableList<PlayBackSpeedItem> = ArrayList()


    private val mConnection: ServiceConnection = object : ServiceConnection {
        @SuppressLint("UseCompatLoadingForDrawables")
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            val bundle = intent.extras
            if (bundle != null) {
                externalRefId = bundle.getString("externalRefId")
                tittle = bundle.getString("tittle")
                assetId = bundle.getInt("id")
                isDrmDisabled = bundle.getBoolean("isDrmDisabled")
                Log.d("tittle", "onServiceConnected: $tittle")
                jwLicenseKey = AppCommonMethod.getDrmKeyValue(isDrmDisabled!!)
                try {
                    if (bundle.getSerializable("audioList") as List<EnveuVideoItemBean> != null) {
                        audioList = bundle.getSerializable("audioList") as List<EnveuVideoItemBean>

                    }
                } catch (e: Exception) {
                    Logger.w(e)
                }

                if (audioList !=null && audioList!!.isNotEmpty()) {
                    currentPlayingIndex = getCurrentPlayingIndex(audioList!!)
                    isBingeWatchEnable = true
                }
            }
            handleCustomAudioUi()
            audioPlayerControlView!!.toggleControlVisibility()
            // private JWPlayerView mPlayerView;
            mService = service as EntBackgroundAudioService.ServiceBinder
            mService!!.createPlayer(this@EntBackgroundAudioActivity, externalRefId, this@EntBackgroundAudioActivity,this@EntBackgroundAudioActivity,jwLicenseKey)
            mPlayerView = mService!!.getPlayerView()
          //  mContainer!!.addView(mPlayerView, RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))
            val startIntent =
                Intent(this@EntBackgroundAudioActivity, EntBackgroundAudioService::class.java)
            startIntent.putExtra(EntBackgroundAudioService.ACTION, EntBackgroundAudioService.ACTION_START)
            startForegroundService(startIntent)


            mIsBound = true
        }

        override fun onServiceDisconnected(name: ComponentName) {
            mIsBound = false
        }
    }

    private fun handleCustomAudioUi(){
        if (currentPlayingIndex!! <= 0) {
            audioPlayerControlView!!.shouldShowPrevious(false)
        }else{
            audioPlayerControlView!!.shouldShowPrevious(true)
        }

        if (isBingeWatchEnable == true && currentPlayingIndex!! < audioList?.size!! - 1) {
            audioPlayerControlView!!.shouldShowNext(true)
        }else{
            audioPlayerControlView!!.shouldShowNext(false)
        }

        audioPlayerControlView?.setPodcastUi(tittle!!)
    }

    private val playerControlClickListener = object : AudioControlClickListener() {
        override fun onPlayPauseClicked() {
            super.onPlayPauseClicked()
            var playerState = false
            if (mService != null && mService!!.getPlayer() != null) {
                if (mService!!.getPlayer()?.state == PlayerState.PLAYING) {
                    mService!!.getPlayer()?.pause()
                    playerState = false
                } else if (mService!!.getPlayer()?.state == PlayerState.PAUSED) {
                    mService!!.getPlayer()?.play()
                    playerState = true
                }
                audioPlayerControlView?.updatePlayPauseIcon(playerState)
            }
        }



        override fun onNextClicked() {
            super.onNextClicked()
            nextAudioPlay()
        }

        override fun onPreviousClicked() {
            super.onPreviousClicked()
            previousAudio()
        }

        override fun onBackClicked() {
            super.onBackClicked()
            onBackPressed()

        }

        override fun onPlayBackSpeedSelected(userSelectedSpeed: Double) {
            super.onPlayBackSpeedSelected(userSelectedSpeed)
            mService!!.getPlayer()?.playbackRate = userSelectedSpeed
        }


        override fun onPlayBackSpeedClicked() {
            super.onPlayBackSpeedClicked()
            audioPlayerControlView?.setPlayBackSpeed(speedTracks,selectedSpeed)
        }


        override fun onRewindClick() {
            super.onRewindClick()
            if (mService != null && mService!!.getPlayer() != null) {
                mService!!.getPlayer().let {
                    val back10Ms = it?.position?.minus(10)
                    val backMs = if (back10Ms!! > 0) {
                        back10Ms
                    } else {
                        0
                    }
                    seekPlayerTo(backMs.toDouble())
                }
            }
        }

        override fun onFastForwardClick() {
            super.onFastForwardClick()
            if (mService != null && mService!!.getPlayer() != null) {
                mService!!.getPlayer().let {
                    val fwd10Ms = it?.position?.plus(10)
                    val fwdMs = if (fwd10Ms!! > 0) {
                        fwd10Ms
                    } else {
                        0
                    }
                    seekPlayerTo(fwdMs.toDouble())
                }
            }
        }

        override fun onProgressDragStop(position: Long) {
            super.onProgressDragStop(position)
            seekPlayerTo(position.toDouble())
        }

        override fun onOutsideClicked(root: View) {
            super.onOutsideClicked(root)
            audioPlayerControlView!!.toggleControlVisibility()
        }
    }

    override fun onBackPressed() {
        if (mService != null) {
            mService!!.clearNotification()
        }
        super.onBackPressed()

    }

    private fun seekPlayerTo(position: Double) {
        if (mService != null && mService!!.getPlayer() != null) {
            mService!!.getPlayer()?.seek(position)
            audioPlayerControlView?.updateProgress(position)
        }
    }


    @SuppressLint("SourceLockedOrientationActivity", "MissingInflatedId")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.audio_jwplayerview)
        // Set the orientation to portrait
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        mContainer = findViewById(R.id.AudioContainer)
        audioPlayerControlView = findViewById(R.id.ep_controls)
        audioPlayerControlView!!.attachListener(playerControlClickListener)
        getSpeedTracks()
        KsPreferenceKeys.getInstance().playBackSpeed = selectedSpeed.toString()
        if (BuildConfig.FLAVOR.equals("qa", ignoreCase = true)) {
            LicenseUtil().setLicenseKey(this, AppConstants.QA_LICENSE_KEY)
        } else {
            LicenseUtil().setLicenseKey(this, AppConstants.PROD_LICENSE_KEY)
        }
        bindService(
            Intent(this, EntBackgroundAudioService::class.java), mConnection,
            Context.BIND_AUTO_CREATE
        )
    }

    override fun onDataReceived(position: Double, duration: Double) {
        duration.let {audioPlayerControlView!!.updateDuration(it) }
        position.let {audioPlayerControlView!!.updateProgress(it) }
    }


    // Static method to get the list of speed tracks
    private fun getSpeedTracks(): List<PlayBackSpeedItem> {
        speedTracks = ArrayList()

        // Add speed tracks from 0.5 to 3.0 in increments of 0.5
        var speed = 1.0
        var id = 1
        while (speed <= 2.0) {
            val speedItem = PlayBackSpeedItem(speed, id)
            speedTracks.add(speedItem)
            speed += 0.25
            id++
        }
        return speedTracks
    }

    var total : Int? = 0
    private fun nextAudioPlay() {
        try {
            if (mService!=null && mService!!.getPlayer()!=null) {
                mService!!.removeListener()
            }
            currentPlayingIndex = currentPlayingIndex!! + 1
            if (shouldShowBingeWatch(audioList?.size) && isBingeWatchEnable == true) {
                if (audioList != null && audioList!!.isNotEmpty()) {
                    total = audioList!!.size
                    for (i in 0 until total!!) {
                        val id = audioList!![i].id
                        if (total != null) {
                            if (id == assetId && i < total!! - 1) {
                                Logger.d("id: $assetId")
                                val nextAudioItem = audioList?.get(i + 1)
                                this.tittle = nextAudioItem?.title
                                this.assetId = nextAudioItem?.id!!
                                this.externalRefId = nextAudioItem.externalRefId
                                this.jwLicenseKey = AppCommonMethod.getDrmKeyValue(nextAudioItem.drmDisabled)
                                mService!!.getPlayer()!!.stop()
                                handleCustomAudioUi()
                                audioPlayerControlView!!.toggleControlVisibility()
                                if (audioList?.isNotEmpty() == true) {
                                    if (isBingeWatchEnable == true && currentPlayingIndex!! < audioList?.size!! - 1) {
                                        audioPlayerControlView!!.shouldShowNext(true)
                                    }else{
                                        audioPlayerControlView!!.shouldShowNext(false)
                                    }
                                    if (currentPlayingIndex!! <= 0) {
                                        audioPlayerControlView!!.shouldShowPrevious(false)
                                    }else{
                                        audioPlayerControlView!!.shouldShowPrevious(true)
                                    }
                                }
                                mService!!.createPlayer(this@EntBackgroundAudioActivity, externalRefId, this@EntBackgroundAudioActivity,this@EntBackgroundAudioActivity,jwLicenseKey)
                                break
                            }
                        }
                    }

                }
            } else {
                mService!!.getPlayer()!!.stop()
                if (mService!=null && mService!!.getPlayer()!=null) {
                    mService!!.removeListener()
                }
                onBackPressed()
            }
        } catch (_:Exception) {

        }


    }


    private fun getCurrentPlayingIndex(audioList: List<EnveuVideoItemBean>): Int? {
        var currentPlayingIndex: Int? = 0
        val total = audioList.size
        for (i in 0 until total) {
            val id = audioList[i].id
            if (id == assetId){
                currentPlayingIndex = i
                break
            }
        }
        Log.d("currentPlayingIndex", "getCurrentPlayingIndex: $currentPlayingIndex")
        return currentPlayingIndex

    }

    private fun shouldShowBingeWatch(size: Int?): Boolean {
        var shouldBingeWatchShow : Boolean? = false
        Log.d("currentPlayingIndex", "getCurrentPlayingIndex: $currentPlayingIndex")
        if (size != null) {
            shouldBingeWatchShow = currentPlayingIndex!! < size
        }

        if (!shouldBingeWatchShow!!) {
            isBingeWatchEnable = false
        }

        return shouldBingeWatchShow
    }


    private fun previousAudio(){
        try {
            if (mService!=null && mService!!.getPlayer()!=null) {
                mService!!.removeListener()
            }
            currentPlayingIndex= currentPlayingIndex?.minus(1)
            if (shouldShowBingeWatch(audioList?.size) && isBingeWatchEnable == true) {
                if (audioList != null && audioList!!.isNotEmpty()) {
                    total = audioList!!.size
                    val previousAudioItem = audioList?.get(currentPlayingIndex!!)
                    this.tittle = previousAudioItem?.title
                    this.assetId = previousAudioItem?.id!!
                    this.externalRefId = previousAudioItem.externalRefId
                    this.jwLicenseKey = AppCommonMethod.getDrmKeyValue(previousAudioItem.drmDisabled)
                    mService!!.getPlayer()!!.stop()
                    handleCustomAudioUi()
                    audioPlayerControlView!!.toggleControlVisibility()
                    if (audioList?.isNotEmpty() == true) {
                        if (isBingeWatchEnable == true && currentPlayingIndex!! < audioList?.size!! - 1) {
                            audioPlayerControlView!!.shouldShowNext(true)
                        }else{
                            audioPlayerControlView!!.shouldShowNext(false)
                        }
                        if (currentPlayingIndex!! <=0) {
                            audioPlayerControlView!!.shouldShowPrevious(false)
                        }else{
                            audioPlayerControlView!!.shouldShowPrevious(true)
                        }
                    }
                    mService!!.createPlayer(this@EntBackgroundAudioActivity, externalRefId, this@EntBackgroundAudioActivity,this@EntBackgroundAudioActivity,jwLicenseKey)

                }
            } else {
                mService!!.getPlayer()!!.stop()
                if (mService!=null && mService!!.getPlayer()!=null) {
                    mService!!.removeListener()
                }
                onBackPressed()
            }
        } catch (_:Exception) {

        }

    }

    override fun onVideoCompletion() {
        nextAudioPlay()
    }


}