package com.enveu.activities.customservices

import android.Manifest
import android.app.Activity
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.support.v4.media.MediaMetadataCompat
import android.support.v4.media.session.MediaSessionCompat
import android.support.v4.media.session.PlaybackStateCompat
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.enveu.OttApplication
import com.enveu.R
import com.jwplayer.pub.api.JWPlayer
import com.jwplayer.pub.api.configuration.PlayerConfig
import com.jwplayer.pub.api.configuration.UiConfig
import com.jwplayer.pub.api.events.CompleteEvent
import com.jwplayer.pub.api.events.EventType
import com.jwplayer.pub.api.events.PauseEvent
import com.jwplayer.pub.api.events.PlayEvent
import com.jwplayer.pub.api.events.TimeEvent
import com.jwplayer.pub.api.events.listeners.VideoPlayerEvents
import com.jwplayer.pub.api.events.listeners.VideoPlayerEvents.OnCompleteListener
import com.jwplayer.pub.api.events.listeners.VideoPlayerEvents.OnPauseListener
import com.jwplayer.pub.api.events.listeners.VideoPlayerEvents.OnPlayListener
import com.jwplayer.pub.api.license.LicenseUtil
import com.jwplayer.pub.view.JWPlayerView





class EntBackgroundAudioService : Service() {
    private var mPlayer: JWPlayer? = null
    private var mMediaSessionCompat: MediaSessionCompat? = null
    private val eventHandler = PlayerEventHandler()
    private val mPlaybackStateBuilder = PlaybackStateCompat.Builder()
    private val mBinder = ServiceBinder()
    private var mPlayerView: JWPlayerView? = null
    private var callback: PlayerTimeCallBack? = null
    private var onVideoComplete: OnVideoComplete? = null



    /**
     * When the service is created, create and prepare a MediaSession
     */
    override fun onCreate() {
        mMediaSessionCompat = MediaSessionCompat(this, javaClass.simpleName)
        mMediaSessionCompat!!.setCallback(MediaSessionCallback())
        mMediaSessionCompat!!.isActive = true

    }






    /**
     * This method gets called each time a call to this service is made
     * we use the {@param intent} to determine the action to be performed.
     *
     * When the service is first bound, an intent with ACTION_START is launched, and the
     * startForeground method gets called, this creates the notification and also promotes the
     * service from background to foreground and prevents the system from killing it.
     *
     * Please note that startForeground() should be called within 5 seconds of calling
     * startForegroundService() in the activity, otherwise the service will be killed and the app
     * will crash with an error.
     *
     * When an ACTION intent is received, we call the TransportControls method to tell the media
     * session an event occurred and invoke it's callback that will forward the event to the
     * player. We also set the PlaybackState to keep the mediaSession and the JWPlayerView in
     * sync.
     *
     * Finally, the notification gets updated every time the Service receives an Intent.
     */
    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        mPlayer!!.allowBackgroundAudio(true)
        val action = intent.getStringExtra(ACTION)
        val transportControls = mMediaSessionCompat!!.controller.transportControls
        val item = mPlayer!!.playlistItem
        when (action) {
            ACTION_START -> {
                val builder = NotificationCompat.Builder(
                     this,
                    OttApplication.CHANNEL_ID
                )
                    // .setContentTitle(item.title)
                    // .setContentText(item.description)
                    .setSmallIcon(R.drawable.ic_bell)
                // .setContentText(item.description)
                startForeground(NOTIFICATION_ID, builder.build())
                setPlaybackState(PlaybackStateCompat.STATE_PAUSED)
            }

            ACTION_PLAY -> {
                transportControls.play()
                //  mPlayer.allowBackgroundAudio(true);
                setPlaybackState(PlaybackStateCompat.STATE_PLAYING)
            }

            ACTION_PAUSE -> {
                transportControls.pause()
                setPlaybackState(PlaybackStateCompat.STATE_PAUSED)
            }

            ACTION_STOP -> {
                transportControls.stop()
                setPlaybackState(PlaybackStateCompat.STATE_STOPPED)
            }
        }
        showNotification()
        return START_NOT_STICKY
    }

    /**
     * When an Activity bind to this service, an instance of the Binder interface is returned to
     * allow interaction.
     */
    override fun onBind(intent: Intent): IBinder? {
        return mBinder
    }

    /**
     * Free resources when the service is destroyed.
     * Clear the service's notification.
     * Stop the service.
     */
    override fun onDestroy() {
        super.onDestroy()
        mPlayer!!.removeListener(EventType.PLAY, eventHandler)
        mPlayer!!.removeListener(EventType.PAUSE, eventHandler)
        mPlayer!!.stop()
        mMediaSessionCompat!!.release()
        NotificationManagerCompat.from(this).cancel(NOTIFICATION_ID)
        stopSelf()
    }





    fun setCallback(callback: PlayerTimeCallBack) {
        this.callback = callback
    }

    fun setonVideoCompleteback(onVideoComplete: OnVideoComplete) {
        this.onVideoComplete = onVideoComplete
    }

    /**
     * Stop the service when the user swipes away the app from the recent app view
     */
    override fun onTaskRemoved(rootIntent: Intent) {
        super.onTaskRemoved(rootIntent)
        stopSelf()
    }

    /**
     * Updates the current payback state
     */
    private fun setPlaybackState(state: Int) {
        mPlaybackStateBuilder.setState(state, mPlayer!!.position.toLong() * 1000, PLAYBACK_SPEED)
        setActions(state)
        mMediaSessionCompat!!.setPlaybackState(mPlaybackStateBuilder.build())
    }

    /**
     * Sets the available actions for the current state.
     */
    private fun setActions(state: Int) {
        when (state) {
            PlaybackStateCompat.STATE_PLAYING -> {
                mPlaybackStateBuilder.setActions(PlaybackStateCompat.ACTION_PAUSE)
            }

            PlaybackStateCompat.STATE_PAUSED -> {
                mPlaybackStateBuilder.setActions(PlaybackStateCompat.ACTION_PLAY)
            }
        }
    }

    /**
     * Updates the notification to be shown. We use a notification builder to create a whole new
     * notification but we use the notify() method and the same notification id to update it. It
     * is important to use the same id so the system knows that the foreground service still has
     * a notification (created in startForeground).
     */
    var pendingIntent: PendingIntent? = null
    private fun showNotification() {
        //Sets the common parameters for all notifications
        val mNotificationBuilder = NotificationCompat.Builder(this, OttApplication.CHANNEL_ID)
        EntMediaStyleHelper.prepareNotification(
            mNotificationBuilder,
            mPlayerView!!.context,
            mPlayer!!.playlistItem
        )
        val metaDataBuilder = MediaMetadataCompat.Builder()
        metaDataBuilder.putLong(
            MediaMetadataCompat.METADATA_KEY_DURATION, Math.round(
                mPlayer!!.duration
            ) * 1000
        )
        mMediaSessionCompat!!.setMetadata(metaDataBuilder.build())
        mNotificationBuilder.setStyle(
            androidx.media.app.NotificationCompat.MediaStyle().setMediaSession(
                mMediaSessionCompat!!.sessionToken
            )
        )
        //Add Actions to the notification
        if (mMediaSessionCompat!!.controller.playbackState.state == PlaybackStateCompat.STATE_PLAYING) {
            val pauseIntent = Intent(this, EntBackgroundAudioService::class.java)
            pauseIntent.putExtra(ACTION, ACTION_PAUSE)
            pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                PendingIntent.getService(
                    this, 2, pauseIntent,
                    PendingIntent.FLAG_IMMUTABLE
                )
            } else {
                PendingIntent.getService(
                    this, 2, pauseIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT
                )
            }
            mNotificationBuilder.addAction(com.npaw.youbora.lib6.jwplayer.R.drawable.ic_jw_pause, "Pause", pendingIntent)
        }
        if (mMediaSessionCompat!!.controller.playbackState.state == PlaybackStateCompat.STATE_PAUSED) {
            val playIntent = Intent(this, EntBackgroundAudioService::class.java)
            playIntent.putExtra(ACTION, ACTION_PLAY)
            pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                PendingIntent.getService(
                    this, 3, playIntent,
                    PendingIntent.FLAG_IMMUTABLE
                )
            } else {
                PendingIntent.getService(
                    this, 3, playIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT
                )
            }
            mNotificationBuilder.addAction(com.npaw.youbora.lib6.jwplayer.R.drawable.ic_jw_play, "Play", pendingIntent)
        }
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.POST_NOTIFICATIONS
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            // TODO: Consider calling
            //    ActivityCompat#requestPermissions
            // here to request the missing permissions, and then overriding
            //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
            //                                          int[] grantResults)
            // to handle the case where the user grants the permission. See the documentation
            // for ActivityCompat#requestPermissions for more details.
            return
        }
        NotificationManagerCompat.from(this).notify(NOTIFICATION_ID, mNotificationBuilder.build())
    }

    /**
     * This Binder interface provides interaction between the service and the binding activity by
     * returning an instance of this Binder and allowing the activity to call its methods.
     *
     * Other custom methods can be defined here.
     *
     * Instead of defining several custom methods, the Binder interface can return an instance of
     * this service to allow the activity to call the service methods directly, but this approach
     * is more dangerous.
     */
    internal inner class ServiceBinder : Binder() {
        var mContect: Context? = null
        fun createPlayer(context: Context?, externalRefId: String?, callback: PlayerTimeCallBack, onVideoComplete: OnVideoComplete, jwLicenseKey: String) {
            this.mContect = context
            if (context is Activity) {
                LicenseUtil().setLicenseKey(context,jwLicenseKey)
                setCallback(callback)
                setonVideoCompleteback(onVideoComplete)
                val hideJwControlUiConfig = UiConfig.Builder()
                    .hideAllControls()
                    .build()
                val playerView: JWPlayerView? = mPlayerView
                playerView?.player.also { mPlayer = it }
                val config = PlayerConfig.Builder()
                    .playlistUrl("https://cdn.jwplayer.com/v2/media/$externalRefId")
                    .uiConfig(hideJwControlUiConfig)
                    .build()
                mPlayerView = JWPlayerView(context, null)

                mPlayer = mPlayerView!!.player
                mPlayer!!.addListener(EventType.PLAY, eventHandler)
                mPlayer!!.addListener(EventType.PAUSE, eventHandler)
                mPlayer?.addListener(EventType.TIME, eventHandler)
                mPlayer?.addListener(EventType.COMPLETE, eventHandler)
                mPlayer!!.setup(config)
                mPlayer!!.play()
            }


        }

        fun clearNotification(){
            mPlayer!!.stop()
            mMediaSessionCompat!!.release()
            NotificationManagerCompat.from(mContect!!).cancel(NOTIFICATION_ID)
            stopSelf()
            if (mContect!=null){
                mContect = null
            }
        }


        fun getPlayerView(): JWPlayerView? {
            return mPlayerView
        }

        fun getPlayer(): JWPlayer? {
            return mPlayer
        }

        fun removeListener() {
            mPlayer!!.removeListener(EventType.PLAY, eventHandler)
            mPlayer!!.removeListener(EventType.PAUSE, eventHandler)
            mPlayer!!.removeListener(EventType.TIME, eventHandler)
            mPlayer!!.removeListener(EventType.COMPLETE, eventHandler)
        }
    }

    /**
     * This class must override all the methods of the actions supported by each
     * application.
     * As minimum, it should override onPlay() and onPause().
     * If the application supports skipping tracks, this class should also implement onSkipToNext
     * () and onSkipToPrevious().
     *
     * Every method should forward the behavior to the JWPlayerView instance to keep sync between
     * itself and the mediaSession.
     */
    private inner class MediaSessionCallback : MediaSessionCompat.Callback() {
        override fun onPlay() {
            mPlayer!!.play()
            super.onPlay()
        }

        override fun onPause() {
            mPlayer!!.pause()
            super.onPause()
        }
    }

    /**
     * This class must implement all the player event listeners needed for each application.
     * As minimum, it should implement onPLayListener and onPauseListener and update the
     * playbackState and the notification, to keep sync between the JWPlayerView instance and the
     * mediaSession.
     */
    private inner class PlayerEventHandler : OnPlayListener, OnPauseListener, VideoPlayerEvents.OnTimeListener, OnCompleteListener {
        override fun onPause(pauseEvent: PauseEvent) {
            setPlaybackState(PlaybackStateCompat.STATE_PAUSED)
            showNotification()
        }

        override fun onPlay(playEvent: PlayEvent) {
            setPlaybackState(PlaybackStateCompat.STATE_PLAYING)
            showNotification()
        }

        override fun onTime(p0: TimeEvent?) {
            if (mPlayer != null && callback != null) {
                callback!!.onDataReceived(mPlayer!!.position,mPlayer!!.duration)
            }
        }

        override fun onComplete(p0: CompleteEvent?) {
            onVideoComplete?.onVideoCompletion()
        }


    }

    interface PlayerTimeCallBack {
        fun onDataReceived(position: Double, duration: Double)
        // Add more callback methods as needed
    }


    interface OnVideoComplete {
        fun onVideoCompletion()
        // Add more callback methods as needed
    }


    companion object {
        private const val PLAYBACK_SPEED = 1.0f
        const val ACTION = "ACTION"
        const val ACTION_START = "ACTION_START"
        const val ACTION_PLAY = "ACTION_PLAY"
        const val ACTION_PAUSE = "ACTION_PAUSE"
        const val ACTION_STOP = "ACTION_STOP"
        const val NOTIFICATION_ID = 1
    }

}