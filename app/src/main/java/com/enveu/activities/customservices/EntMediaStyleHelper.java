package com.enveu.activities.customservices;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;

import androidx.core.app.NotificationCompat;

import com.enveu.R;
import com.jwplayer.pub.api.media.playlists.PlaylistItem;

/**
 * Helper class to create a base notification with the default actions.
 *
 * ContentIntent defines the action to perform when the user clicks the notification.
 * DeleteIntent defines the action to perform when the user explicitly dismisses the notification.
 */
public class EntMediaStyleHelper {
    public static void prepareNotification(NotificationCompat.Builder builder, Context context,
                                           PlaylistItem item) {
        PendingIntent pendingIntentContent;
        PendingIntent pendingIntentDelete;
        Intent deleteIntent = new Intent(context, EntBackgroundAudioService.class);
        deleteIntent.putExtra(EntBackgroundAudioService.ACTION, EntBackgroundAudioService.ACTION_STOP);

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
             pendingIntentDelete = PendingIntent.getService(context, 0, deleteIntent,
                    PendingIntent.FLAG_IMMUTABLE);
        }else {
             pendingIntentDelete = PendingIntent.getService(context, 0, deleteIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT);
        }

        Intent contentIntent = new Intent(context, EntBackgroundAudioActivity.class);
        contentIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
             pendingIntentContent = PendingIntent.getActivity(context, 1, contentIntent,
                    PendingIntent.FLAG_IMMUTABLE);
        }else {
             pendingIntentContent = PendingIntent.getActivity(context, 1, contentIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT);
        }
        builder //.setContentTitle(item.getTitle())
                //.setContentText(item.getDescription())
                //.setSubText(item.getDescription())
                .setSmallIcon(R.drawable.ic_bell)
                .setContentIntent(pendingIntentContent)
                .setDeleteIntent(pendingIntentDelete)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
    }
}
