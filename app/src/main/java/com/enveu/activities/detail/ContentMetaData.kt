package com.enveu.activities.detail

import android.os.Parcel
import android.os.Parcelable

class ContentMetaData() : Parcelable {
    var playBackUrl: String? = null
    var contentTitle: String? = null
    var isLive: Boolean = false
    var isDrmDisabled: Boolean = false
    var contentType: String? = null
    var mediaType: String? = null
    var widevineUrl: String? = null
    var genres: String? = null
    var fileUrl: String? = null
    var contentId: Int? = 0

    constructor(parcel: Parcel) : this() {
        playBackUrl = parcel.readString()
        contentTitle = parcel.readString()
        isLive = parcel.readByte() != 0.toByte()
        isDrmDisabled = parcel.readByte() != 0.toByte()
        contentType = parcel.readString()
        mediaType = parcel.readString()
        widevineUrl = parcel.readString()
        genres = parcel.readString()
        fileUrl = parcel.readString()
        contentId = parcel.readInt()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(playBackUrl)
        parcel.writeString(contentTitle)
        parcel.writeByte(if (isLive) 1 else 0)
        parcel.writeByte(if (isDrmDisabled) 1 else 0)
        parcel.writeString(contentType)
        parcel.writeString(mediaType)
        parcel.writeString(widevineUrl)
        parcel.writeString(genres)
        parcel.writeString(fileUrl)
        parcel.writeInt(contentId!!)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ContentMetaData> {
        override fun createFromParcel(parcel: Parcel): ContentMetaData {
            return ContentMetaData(parcel)
        }

        override fun newArray(size: Int): Array<ContentMetaData?> {
            return arrayOfNulls(size)
        }
    }
}
