package com.enveu.activities.detail.adapter;

import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SnapHelper;

import com.enveu.R;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner;
import com.enveu.callbacks.commonCallbacks.MoreClickListner;
import com.enveu.databinding.HeadingRailsBinding;
import com.enveu.databinding.LandscapeRecyclerItemBinding;
import com.enveu.databinding.PosterPotraitRecyclerItemBinding;
import com.enveu.utils.Logger;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.helpers.GravitySnapHelper;
import com.enveu.utils.helpers.SpacingItemDecoration;
import com.enveu.utils.stringsJson.converter.StringsHelper;

import java.util.List;
import java.util.Locale;


public class CommonDetailAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private List<RailCommonData> mList;
    private final CommonRailtItemClickListner listner;
    private final MoreClickListner moreClickListner;

    private boolean showHeaderTitle = true;

    public CommonDetailAdapter(List<RailCommonData> mList, CommonRailtItemClickListner listner, MoreClickListner moreClickListner) {
        this.mList = mList;
        this.listner = listner;
        this.moreClickListner = moreClickListner;
    }

    public CommonDetailAdapter(List<RailCommonData> mList, CommonRailtItemClickListner listner, MoreClickListner moreClickListner , boolean showHeaderTitle) {
        this.mList = mList;
        this.listner = listner;
        this.moreClickListner = moreClickListner;
        this.showHeaderTitle = showHeaderTitle;
    }



    @Override
    public int getItemViewType(int position) {
        return mList.get(position).getRailType();

    }
    @Override
    public int getItemCount() {
        return mList.size();
    }

    @Override
    public void onBindViewHolder(@NonNull final RecyclerView.ViewHolder holder, final int position) {
        if (holder instanceof LandscapeHolder) {
            LandscapeRail((LandscapeHolder) holder, position);
        } else {
            posterPotraitRail((PosterPotraitHolder) holder, position);
        }
    }


    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LandscapeRecyclerItemBinding landscapeRecyclerItemBinding = DataBindingUtil.inflate(LayoutInflater.from(parent.getContext()), R.layout.landscape_recycler_item, parent, false);
        landscapeRecyclerItemBinding.shimmer.setColorsData(ColorsHelper.INSTANCE);
        LandscapeHolder landscapeHolder = new LandscapeHolder(landscapeRecyclerItemBinding);
        setRecylerProperties(landscapeHolder.landscapeRecyclerItemBinding.recyclerViewList3);
        return landscapeHolder;
    }
    

    public void setFadeAnimation(View view) {
        AlphaAnimation anim = new AlphaAnimation(0.0f, 1.0f);
        anim.setDuration(500);
        view.startAnimation(anim);
    }

    private void posterPotraitRail(PosterPotraitHolder viewHolder, int position) {
        RecyclerView recyclerView = viewHolder.itemBinding.recyclerViewList2;
        CommonDetailPotrailRailAdapter adapter = new CommonDetailPotrailRailAdapter(mList.get(position), position, listner);
        setCommonRailAdapter(viewHolder.itemBinding.titleHeading, recyclerView, position, adapter);
    }
    
    private void setCommonRailAdapter(HeadingRailsBinding headingRailsBinding, RecyclerView recyclerView, int position, RecyclerView.Adapter adapter) {
        boolean isRTL = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL;
        try {
            headingRailsBinding.setColorsData(ColorsHelper.INSTANCE);
            headingRailsBinding.setStringData(StringsHelper.INSTANCE);
            setTitle(headingRailsBinding, mList.get(position), position);
            if (mList.get(position).getEnveuVideoItemBeans().size() > 0) {
                recyclerView.setNestedScrollingEnabled(false);
                LinearLayoutManager layoutManager = new LinearLayoutManager(recyclerView.getContext(), LinearLayoutManager.HORIZONTAL, false);
                recyclerView.setLayoutManager(layoutManager);
                layoutManager.setReverseLayout(isRTL);
                layoutManager.setStackFromEnd(isRTL);
                recyclerView.setHasFixedSize(true);
                recyclerView.setAdapter(adapter);
            } else {
                recyclerView.setAdapter(null);
            }
        } catch (ClassCastException e) {
            Logger.e("CommonAdapter", "" + e);
        }
    }

    private void LandscapeRail(LandscapeHolder viewHolder, int position) {
        RecyclerView recyclerView = viewHolder.landscapeRecyclerItemBinding.recyclerViewList3;
        CommonDetailLandscapeRailAdapter adapter = new CommonDetailLandscapeRailAdapter(mList.get(position), position, listner);
        setCommonRailAdapter(viewHolder.landscapeRecyclerItemBinding.titleHeading, recyclerView, position, adapter);
    }
    
    private void setRecylerProperties(RecyclerView recyclerView) {
        recyclerView.setNestedScrollingEnabled(false);
        recyclerView.addItemDecoration(new SpacingItemDecoration(4, SpacingItemDecoration.HORIZONTAL));
        recyclerView.setLayoutManager(new LinearLayoutManager(recyclerView.getContext(), LinearLayoutManager.HORIZONTAL, false));
        recyclerView.setHasFixedSize(true);
        boolean isRTL = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL;
        SnapHelper snapHelper = new GravitySnapHelper(isRTL ? Gravity.END : Gravity.START);
        snapHelper.attachToRecyclerView(recyclerView);
    }
    

    public void setTitle(HeadingRailsBinding headingRailsBinding, RailCommonData item, int position) {
        headingRailsBinding.setColorsData(ColorsHelper.INSTANCE);
        headingRailsBinding.shimmerTitleLayout.setVisibility(View.GONE);
            if (item.getEnveuVideoItemBeans().size() > 0 && showHeaderTitle) {
                headingRailsBinding.headerTitleLayout.setVisibility(View.VISIBLE);
                headingRailsBinding.mainHeaderTitle.setVisibility(View.VISIBLE);
                headingRailsBinding.headingTitle.bringToFront();
                headingRailsBinding.moreText.setVisibility(View.GONE);
                headingRailsBinding.headingTitle.setText(item.getTabTittle());
                headingRailsBinding.moreText.setOnClickListener(v -> {

                });
            } else {
                headingRailsBinding.headingTitle.setVisibility(View.VISIBLE);
                headingRailsBinding.moreText.setVisibility(View.VISIBLE);
            }
    }



    public static class PosterPotraitHolder extends RecyclerView.ViewHolder { 
        PosterPotraitRecyclerItemBinding itemBinding;
        PosterPotraitHolder(PosterPotraitRecyclerItemBinding itemBinding) {
            super(itemBinding.getRoot());
            this.itemBinding = itemBinding;
        }
    }
    
    public static class LandscapeHolder extends RecyclerView.ViewHolder {
        final LandscapeRecyclerItemBinding landscapeRecyclerItemBinding;

        LandscapeHolder(LandscapeRecyclerItemBinding flightItemLayoutBinding) {
            super(flightItemLayoutBinding.getRoot());
            landscapeRecyclerItemBinding = flightItemLayoutBinding;
        }
    }
    
}
