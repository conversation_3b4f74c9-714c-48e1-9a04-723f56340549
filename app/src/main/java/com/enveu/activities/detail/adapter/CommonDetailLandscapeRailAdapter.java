package com.enveu.activities.detail.adapter;

import android.os.SystemClock;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.enveu.R;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner;
import com.enveu.databinding.LandscapeItemBinding;
import com.enveu.databinding.LandscapeItemLargeBinding;
import com.enveu.databinding.LandscapeItemSmallBinding;
import com.enveu.utils.Logger;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.helpers.ImageHelper;

import java.util.List;
import java.util.Objects;

import io.reactivex.annotations.NonNull;
public class CommonDetailLandscapeRailAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private long mLastClickTime = 0;
    private final RailCommonData railCommonData;
    private List<EnveuVideoItemBean> videos;
    private final CommonRailtItemClickListner listner;
    private String tabType = "";
    private final int pos;

    public CommonDetailLandscapeRailAdapter(RailCommonData railCommonData, int position, CommonRailtItemClickListner listner) {
        this.railCommonData = railCommonData;
        this.videos = railCommonData.getEnveuVideoItemBeans();
        this.listner = listner;
        this.pos = position;
    }


    @androidx.annotation.NonNull
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        LandscapeItemBinding binding = DataBindingUtil.inflate(
                LayoutInflater.from(parent.getContext()),
                R.layout.landscape_item, parent, false);
        binding.setColorsData(ColorsHelper.INSTANCE);
        return new CommonDetailLandscapeRailAdapter.NormalHolder(binding);
    }
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int i) {
        Log.w("callinGonBind","in");
        if (holder instanceof NormalHolder) {
            setNomalValues((( NormalHolder) holder).landscapeItemBinding,i);
        }

    }

    private void setNomalValues(LandscapeItemBinding itemBinding, int i) {

        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i);

        });

        if (videos.get(i).getCustomDataV3() != null  && videos.get(i).getCustomDataV3().getIsExclusive() != null){
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            }
            else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        }
        else {
            itemBinding.exclusive.setVisibility(View.GONE);
        }

        try{
            if (videos.get(i).getPosterURL().equalsIgnoreCase("") ) {
                Objects.requireNonNull(itemBinding.imageTitle).setVisibility(View.VISIBLE);
                itemBinding.imageTitle.bringToFront();
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                Objects.requireNonNull(itemBinding.imageTitle).setVisibility(View.GONE);
            }
        } catch (Exception ignored) {

        }

        try {
            if (videos.get(i).getPosterURL() != null && !videos.get(i).getPosterURL().equalsIgnoreCase("")) {
                ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadListImage(itemBinding.itemImage, AppCommonMethod.getListLDSImage(videos.get(i).getPosterURL(), itemBinding.itemImage.getContext()));
            }
        } catch (Exception e){
            Logger.w(e);
        }
    }

    public void itemClick(int position) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        listner.railItemClick(railCommonData, position);
    }

    @Override
    public int getItemCount() {
        return videos.size();
    }

    public class NormalHolder extends RecyclerView.ViewHolder {

        final LandscapeItemBinding landscapeItemBinding;

        NormalHolder(LandscapeItemBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.landscapeItemBinding = circularItemBind;

        }

    }

    public class SmallHolder extends RecyclerView.ViewHolder {

        final LandscapeItemSmallBinding landscapeItemBinding;

        SmallHolder(LandscapeItemSmallBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.landscapeItemBinding = circularItemBind;

        }

    }

    public class LargeHolder extends RecyclerView.ViewHolder {

        final LandscapeItemLargeBinding landscapeItemBinding;

        LargeHolder(LandscapeItemLargeBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.landscapeItemBinding = circularItemBind;

        }

    }


}
