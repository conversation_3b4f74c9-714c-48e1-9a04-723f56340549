package com.enveu.activities.detail.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.enveu.activities.detail.trailer_related.RelatedContentFragment
import com.enveu.activities.detail.trailer_related.TrailerAndMoreFragment

private const val NUM_TABS = 1

class ViewPagerAdapter(fragmentManager: FragmentManager, lifecycle: Lifecycle) :
    FragmentStateAdapter(fragmentManager, lifecycle) {

    override fun getItemCount(): Int {
        return NUM_TABS
    }

    override fun createFragment(position: Int): Fragment {
        return when (position) {
//            0 -> TrailerAndMoreFragment()
            0 -> RelatedContentFragment()
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }
}