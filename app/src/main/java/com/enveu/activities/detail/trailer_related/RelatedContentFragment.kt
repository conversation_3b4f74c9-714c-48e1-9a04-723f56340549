package com.enveu.activities.detail.trailer_related

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.detail.adapter.CommonDetailAdapter
import com.enveu.activities.detail.ui.DetailActivity
import com.enveu.activities.listing.listui.ListActivity
import com.enveu.baseModels.BaseFragment
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner
import com.enveu.callbacks.commonCallbacks.MoreClickListner
import com.enveu.databinding.FragmentRelatedContentBinding
import com.enveu.networking.apistatus.APIStatus
import com.enveu.utils.Logger
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher


class RelatedContentFragment : BaseFragment(), MoreClickListner, CommonRailtItemClickListner {
    private lateinit var binding: FragmentRelatedContentBinding
    private var railCommonDataList: MutableList<RailCommonData> = ArrayList()
    var railInjectionHelper: RailInjectionHelper? = null
    private var videoDetails: EnveuVideoItemBean? = null
    private var contentId: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_related_content, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        railInjectionHelper = ViewModelProvider(requireActivity())[RailInjectionHelper::class.java]

        initUI()
    }

    private fun initUI() {
        videoDetails = (activity as DetailActivity).getVideoDetails()
        contentId = (activity as DetailActivity).getContentId()
       callMoreLikeThisTab()
    }

    private fun callMoreLikeThisTab() {
        contentId?.toInt()?.let {
            railInjectionHelper!!.getRelatedContent(0, 20, videoDetails!!.assetType, it).observe(viewLifecycleOwner) { response ->
                if (response != null) {
                    if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {

                    } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                        if (response.baseCategory != null) {
                            binding.relatedRecycler.visibility = View.VISIBLE
                            binding.noResultFoundDesc.visibility = View.GONE
                            val enveuCommonResponse = response.baseCategory as RailCommonData
                            enveuCommonResponse.railType = AppConstants.MORE_LIKE_THIS_TAB
                            // enveuCommonResponse.tabTittle = mediaConfig!!.detailPage.tabs.moreLikeThis.displayLabel
                            railCommonDataList.add(enveuCommonResponse)
                            setCommonAdapter()
                            // binding!!.progressBar.visibility = View.GONE
                        }
                        else{
                            binding.relatedRecycler.visibility = View.GONE
                            binding.noResultFoundDesc.visibility = View.VISIBLE
                        }
                    } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                        if (response.errorModel.errorCode != 0) {
                            //  binding!!.progressBar.visibility = View.GONE
                        }
                    } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                        // binding!!.progressBar.visibility = View.GONE
                    }
                }
            }
        }
    }

    private fun setCommonAdapter() {
        binding.relatedRecycler.setHasFixedSize(true)
        binding.relatedRecycler.layoutManager = LinearLayoutManager(requireActivity(), RecyclerView.VERTICAL, false)
        RecyclerAnimator(requireActivity()).animate(binding.relatedRecycler)
        val  commonDetailAdapter = CommonDetailAdapter(railCommonDataList, this, this)
        binding.relatedRecycler.adapter = commonDetailAdapter
    }

    override fun railItemClick(item: RailCommonData, position: Int) {
        AppCommonMethod.redirectionLogic(
            requireActivity(),
            item,
            position,"","","")
    }

    override fun moreRailClick(data:RailCommonData, position: Int, multilingualTitle: String) {
        Logger.d(data.screenWidget.contentID + "  " + data.screenWidget.landingPageTitle + " " + 0 + " " + 0)
        if (data.screenWidget != null && data.screenWidget.contentID != null) {
            val playListId = data.screenWidget.contentID
            if (data.screenWidget.name != null) {
                ActivityLauncher.getInstance().listActivity(
                    requireActivity(),
                    ListActivity::class.java,
                    playListId,
                    data.screenWidget.name.toString(),
                    0,
                    0,
                    data.screenWidget
                )
            } else {
                ActivityLauncher.getInstance().listActivity(
                    requireActivity(),
                    ListActivity::class.java,
                    playListId,
                    "",
                    0,
                    0,
                    data.screenWidget
                )
            }
        }
    }
}