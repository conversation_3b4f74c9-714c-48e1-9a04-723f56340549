package com.enveu.activities.detail.ui

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.databinding.ItemMovieInfoBinding

class ActorAdapter(
    private val actors: List<Person>
) : RecyclerView.Adapter<ActorAdapter.ActorViewHolder>() {

    class ActorViewHolder(
        private val binding: ItemMovieInfoBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(actor: Person) {
            binding.actorsTv.text = actor.name
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ActorViewHolder {
        val binding = ItemMovieInfoBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return Actor<PERSON>iewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON><PERSON>Holder, position: Int) {
        holder.bind(actors[position])
    }

    override fun getItemCount(): Int = actors.size
}