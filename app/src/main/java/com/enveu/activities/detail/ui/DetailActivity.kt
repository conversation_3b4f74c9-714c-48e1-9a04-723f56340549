package com.enveu.activities.detail.ui

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Color
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.AudioManager.OnAudioFocusChangeListener
import android.os.Build
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.R
import com.enveu.activities.web_view.WebViewActivity
import com.enveu.activities.customservices.EntBackgroundAudioActivity
import com.enveu.activities.detail.ContentMetaData
import com.enveu.activities.detail.adapter.CommonDetailAdapter
import com.enveu.activities.detail.adapter.ViewPagerAdapter
import com.enveu.activities.detail.viewModel.DetailViewModel
import com.enveu.activities.downloads.NetworkHelper.isWifiEnabled
import com.enveu.activities.listing.listui.ListActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.EnterOTPActivity
import com.enveu.activities.usermanagment.ui.PaymentDetailPage
import com.enveu.adapters.LayoutVideoMetaTagsAdapter
import com.enveu.adapters.LayoutVideoMetaTagsCallback
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaConfig
import com.enveu.appLevelModel.TabConfig
import com.enveu.appLevelModel.TabsConfig
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModel.responseModels.detailPlayer.Data
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner
import com.enveu.callbacks.commonCallbacks.MoreClickListner
import com.enveu.callbacks.commonCallbacks.NetworkChangeReceiver
import com.enveu.callbacks.player.callback.OnAudioItemClickInteraction
import com.enveu.databinding.DetailScreenBinding
import com.enveu.epg.models.EPGItem
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstance
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstanceWithCancelBtn
import com.enveu.fragments.player.ui.AudioInteractionFragment
import com.enveu.fragments.player.ui.UserInteractionFragment
import com.enveu.jwplayer.player.PlayerActivity
import com.enveu.jwplayer.player.PlayerFragment
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.utils.Constants
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.tabs.TabLayoutMediator
import com.google.gson.Gson

class DetailActivity : BaseBindingActivity<DetailScreenBinding?>(), NetworkChangeReceiver.ConnectivityReceiverListener,
    OnAudioFocusChangeListener, CommonRailtItemClickListner, MoreClickListner, CommonDialogFragment.EditDialogListener,
    OnAudioItemClickInteraction {
    private var isLoggedOut = false
    private var audioManager: AudioManager? = null
    private var focusRequest: AudioFocusRequest? = null
    private var videoDetails: EnveuVideoItemBean? = null
    private var trailerDetails: EnveuVideoItemBean? = null
    private var viewModel: DetailViewModel? = null
    private var preference: KsPreferenceKeys? = KsPreferenceKeys.getInstance()
    private var commonDetailAdapter: CommonDetailAdapter? = null
    private var assetId = 0
    private var id = 0
    private var token: String? = null
    private var playerFragment : PlayerFragment?= null
    private var mediaType: String? = null
    private var isLogin: String? = null
    private var bookmarkingViewModel: BookmarkingViewModel? = null
    private var userInteractionFragment: UserInteractionFragment? = null
    private var audioInteractionFragment: AudioInteractionFragment?= null
    private var thumbnailImageUrl:String=""
    private var isLoggedIn = false
    private var isUserVerified: String? = null
    private var assetType: String? = null
    private var isUserNotVerify = false
    private var isGeoBlocking = false
    private var isUserNotEntitle = false
    private var keyword: String? = ""
    private var playbackUrl: String? = null
    private var trailerUrl: String? = null
    private var trailerExternalRefId: String? = null
    private var mediaConfig: MediaConfig? = null
    private var tabsConfigList: ArrayList<TabsConfig>? = null
    private var tabConfig: ArrayList<TabConfig>? = null
    private var featureList: FeatureFlagModel? = null
    private val colorsHelper by lazy { ColorsHelper }
    private val stringsHelper by lazy { StringsHelper }
    private var railCommonDataList: MutableList<RailCommonData> = ArrayList()
    private var signedUrl = ""
    private var drmBaseUrl = ""
    private var drmToken = ""
    private var isFromEpg=false
    private var isFromChannelClick = false
    private var hideWatchlistIcon = false
    private var channelId=""
    private var showId=""
    private var epgItem:EPGItem? = null

    override fun inflateBindingLayout(inflater: LayoutInflater): DetailScreenBinding {
        return DetailScreenBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        inItFun()
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        getIntentValue()
        setOrientation()
        parseColor()
        callBinding()

    }

    private var customType:String?=null
    private fun getIntentValue() {
        try {
            var arguments = intent.extras
            if (arguments != null) {
                isFromEpg = arguments?.getString(AppConstants.FROM_REDIRECTION)?.equals(AppConstants.EPG_FRAGMENT) ?: false
                if (!isFromEpg) {
                    arguments = arguments.getBundle(AppConstants.BUNDLE_ASSET_BUNDLE)
                }
                assetType = arguments?.getString(AppConstants.GAME_ASSET_TYPE)
                customType = arguments?.getString(AppConstants.GAME_CUSTOM_TYPE)
                contentSlug = arguments?.getString(AppConstants.BUNDLE_CONTENT_SLUG) ?: ""
                assetId = arguments?.getInt(AppConstants.BUNDLE_ASSET_ID) ?: -1
                channelId = arguments?.getString(AppConstants.EPG_CHANNEL_ID) ?: ""
                showId = arguments?.getString(AppConstants.EPG_SHOW_ID) ?: ""
                isFromChannelClick = arguments?.getBoolean(AppConstants.IS_FROM_CHANNEL_CLICK) == true

                if (assetType.equals(Constants.CUSTOM, ignoreCase = true) && customType.equals(Constants.GAME, ignoreCase = true))  {
                    binding?.metaDetails?.playBtn?.text = getString(R.string.play_game)
                    hideWatchlistIcon = true
                }else{
                    binding?.metaDetails?.playBtn?.text = getString(R.string.watcho_now_title)
                }
                val epgJson = arguments?.getString(AppConstants.EPG_ITEM)
                epgItem = Gson().fromJson(epgJson, EPGItem::class.java)
                if (isFromEpg){
                    setClicks()
                }
            } else {
                throw IllegalArgumentException("Activity cannot find extras Search_Show_All")
            }
        }catch (e:Exception){
            e.printStackTrace()
        }
    }

    private fun inItFun() {
        binding?.backButtonIcon?.let { rotateImageLocaleWise(it) }
        if (preference?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
        }
        isUserVerified = preference?.isVerified
        viewModel = ViewModelProvider(this@DetailActivity)[DetailViewModel::class.java]
        bookmarkingViewModel = ViewModelProvider(this)[BookmarkingViewModel::class.java]
        token = preference?.appPrefAccessToken
    }

    private fun getAppLevelJsonData() {
        if (mediaType != null) {
            mediaConfig = AppConfigMethod.getMediaConfigByType(mediaType!!)
        }
        featureList = AppConfigMethod.parseFeatureFlagList()
    }

    private fun prepareEnabledTabs(){
        // Initialize the ArrayList if it's null
        if (tabsConfigList == null) {
            tabsConfigList = ArrayList()
        }

        // Initialize the ArrayList if it's null
        if (tabConfig == null) {
            tabConfig = ArrayList()
        }


        // Add the mediaConfig to the list
        if (mediaConfig != null) {
            tabsConfigList?.add(mediaConfig!!.detailPage.tabs)
            for (i in tabsConfigList!!.indices) {
                if (tabsConfigList!![i].moreLikeThis.enabled) {
                    tabConfig!!.add(tabsConfigList!![i].moreLikeThis)
                }
                if (tabsConfigList!![i].trailersAndMore.enabled) {
                    tabConfig!!.add(tabsConfigList!![i].trailersAndMore)
                }
                if (tabsConfigList!![i].clipAndMore.enabled) {
                    tabConfig!!.add(tabsConfigList!![i].clipAndMore)
                }
            }
        }
       // setTabs()
//        trailerMoreAndRelated()
        callRelatedContent()
    }

    private fun callRelatedContent() {
        // HS please work with  conscious mind, for string key is added first it should be in default string file
//        binding?.tabItemText?.text = mediaConfig?.detailPage?.tabs?.moreLikeThis?.displayLabel
        binding?.tabItemText?.text = resources.getString(R.string.detail_tab_related)
        callMoreLikeThisTab()
    }


    private fun setOrientation() {
        window.setBackgroundDrawableResource(R.color.app_bg_color)
        val orientation = this.resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            setupPortraitView()
        } else {
            setupLandscapeView()
        }
    }

    private fun parseColor() {
        binding!!.stringData = stringsHelper
        binding!!.colorsData = colorsHelper
        binding!!.metaDetails.colorsData = colorsHelper
        binding!!.metaDetails.stringData = stringsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper

    }

    private fun trailerMoreAndRelated() {
        val adapter = ViewPagerAdapter(supportFragmentManager, lifecycle)
        binding!!.viewPager.adapter = adapter
        TabLayoutMediator( binding!!.tabLayout,  binding!!.viewPager) { tab, position ->
//            if (position == 0){
//                tab.text = mediaConfig?.detailPage?.tabs?.trailersAndMore?.displayLabel
//            }
//            else if (position == 1){
                binding?.tabItemText?.text = mediaConfig?.detailPage?.tabs?.moreLikeThis?.displayLabel
       //     }
        }.attach()
    }

    private fun connectionObserver() {
        connectionValidation(NetworkConnectivity.isOnline(this))
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            uiInitialisation()
        } else {
            noConnectionLayout()
        }
    }

    private fun callShimmer() {
        binding!!.seriesShimmer.visibility = View.VISIBLE
        binding!!.mShimmer.colorsData = colorsHelper
        binding!!.mShimmer.seriesShimmerScroll1.isEnabled = false
        binding!!.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding!!.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding!!.llParent.visibility = View.GONE
        binding!!.noConnectionLayout.visibility = View.GONE
        binding!!.mShimmer.sfShimmer1.startShimmer()
        binding!!.mShimmer.sfShimmer2.startShimmer()
        binding!!.mShimmer.sfShimmer3.startShimmer()
        binding!!.mShimmer.flBackIconImage.bringToFront()
        binding!!.mShimmer.flBackIconImage.setOnClickListener { onBackPressed() }
    }

    private fun stopShimmer() {
        binding!!.seriesShimmer.visibility = View.GONE
        binding!!.llParent.visibility = View.VISIBLE
        binding!!.noConnectionLayout.visibility = View.GONE
        binding!!.mShimmer.sfShimmer1.startShimmer()
        binding!!.mShimmer.sfShimmer2.startShimmer()
        binding!!.mShimmer.sfShimmer3.startShimmer()
    }


    private fun setTabs() {
        index=0
        processTabConfigRecursive(index)
    }

    private var index=0
    private fun processTabConfigRecursive(index: Int) {
        // Base case: check if the index is within the bounds of the list
        if (index < tabConfig!!.size) {
            // Make API call or perform processing for tabConfig[index]
            when (tabConfig!![index].displayLabel ) {
                mediaConfig!!.detailPage.tabs.moreLikeThis.displayLabel -> {
                    callMoreLikeThisTab()
                }
                mediaConfig!!.detailPage.tabs.trailersAndMore.displayLabel -> {
                    getLinkedTrailer()
                }
                mediaConfig!!.detailPage.tabs.clipAndMore.displayLabel -> {
                    getClips()
                }
            }
            // Continue the recursion with the next index
        }
    }
    private fun callMoreLikeThisTab() {
        railInjectionHelper!!.getRelatedContent(0, 20, videoDetails?.assetType, id).observe(this) { response ->
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {

                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    if (response.baseCategory != null) {
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                        enveuCommonResponse.railType = AppConstants.MORE_LIKE_THIS_TAB
                        enveuCommonResponse.tabTittle = resources.getString(R.string.detail_tab_related)
                        railCommonDataList.add(enveuCommonResponse)
                        setCommonAdapter()
                        binding!!.progressBar.visibility = View.GONE
                    }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        binding!!.progressBar.visibility = View.GONE
                        binding!!.noResultFoundDesc.visibility = View.VISIBLE
                    }
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    binding!!.progressBar.visibility = View.GONE
                    binding!!.noResultFoundDesc.visibility = View.VISIBLE
                }
            }
           // index ++
          //  processTabConfigRecursive(index)
        }
    }

    private fun setCommonAdapter() {
        binding!!.detailRecyclerView.setHasFixedSize(true)
        binding!!.detailRecyclerView.isNestedScrollingEnabled = false
        binding!!.detailRecyclerView.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false).apply { stackFromEnd = false }

       if (commonDetailAdapter == null) {
            RecyclerAnimator(this).animate(binding!!.detailRecyclerView)
            commonDetailAdapter = CommonDetailAdapter(railCommonDataList, this, this,  false)
            binding!!.detailRecyclerView.adapter = commonDetailAdapter
        } else {
           commonDetailAdapter?.notifyDataSetChanged()
       }
    }

    private fun getLinkedTrailer() {
        railInjectionHelper!!.getCommonListAll(0, 3, id.toString(),
            AppConstants.TRAILER_CUSTOM_DATA).observe(this) { response ->
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {

                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    if (response.baseCategory != null) {
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                         if (enveuCommonResponse?.enveuVideoItemBeans?.isNotEmpty() == true){
                             trailerDetails = enveuCommonResponse?.enveuVideoItemBeans?.get(0)
                             binding!!.progressBar.visibility = View.GONE
                             if (mediaConfig!!.detailPage.features.isTrailerEnabled && !trailerExternalRefId.equals("")) {
                                 binding!!.metaDetails.trailerButton.visibility = View.VISIBLE
                             }
                             else {
                                 binding!!.metaDetails.trailerButton.visibility = View.GONE
                             }
                         }
                         else {
                             binding!!.metaDetails.trailerButton.visibility = View.GONE
                         }
                    }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        binding!!.progressBar.visibility = View.GONE
                    }
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    binding!!.progressBar.visibility = View.GONE
                }
            }
            index++
            processTabConfigRecursive(index)
        }
    }


    private fun getClips() {
        railInjectionHelper!!.getCommonListAll(0, 20, id.toString(),
            AppConstants.CLIPS_AND_MORE_CUSTOM_DATA).observe(this) { response ->
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {

                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    if (response.baseCategory != null) {
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                        enveuCommonResponse.railType = AppConstants.TRAILER_AND_MORE
                        enveuCommonResponse.tabTittle = mediaConfig!!.detailPage.tabs.clipAndMore.displayLabel
                        railCommonDataList.add(enveuCommonResponse)
                        binding!!.progressBar.visibility = View.GONE
                        setCommonAdapter()
                    }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        binding!!.progressBar.visibility = View.GONE
                    }
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    binding!!.progressBar.visibility = View.GONE
                }
            }
            index++
                processTabConfigRecursive(index)
        }
    }

    override fun onResume() {
        super.onResume()
        requestAudioFocus()
        isLoggedOut = false
        dismissLoading(binding!!.progressBar)
        if (!isLoggedIn) {
            if (preference!!.appPrefLoginStatus.equals(
                    AppConstants.UserStatus.Login.toString(),
                    ignoreCase = true
                )
            ) {
                isLoggedIn = true
                refreshDetailPage()
            }
        }
        setBroadcast()
        if (preference != null && userInteractionFragment != null) {
            AppCommonMethod.callSocialAction(preference!!, userInteractionFragment)
        }
    }

    private fun requestAudioFocus() {
        audioManager = getSystemService(AUDIO_SERVICE) as AudioManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val playbackAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                .build()

            // AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
            focusRequest = AudioFocusRequest.Builder(AudioManager.STREAM_MUSIC)
                .setAudioAttributes(playbackAttributes)
                .setAcceptsDelayedFocusGain(true)
                .setOnAudioFocusChangeListener { focusChange: Int ->
                    if (focusChange == AudioManager.AUDIOFOCUS_LOSS) {
                        Logger.d("AudioFocus", "Loss")
                    }
                }
                .build()
            audioManager!!.requestAudioFocus(focusRequest!!)
        } else {
            audioManager!!.requestAudioFocus(
                this, AudioManager.STREAM_VOICE_CALL,
                AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
            )
        }
    }

    private fun releaseAudioFocusForMyApp() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            audioManager!!.abandonAudioFocusRequest(focusRequest!!)
        }
    }

    private fun setBroadcast() {
        receiver = NetworkChangeReceiver()
        val filter = IntentFilter()
        filter.addAction("android.net.conn.CONNECTIVITY_CHANGE")
        filter.addAction("android.net.wifi.WIFI_STATE_CHANGED")
        filter.addAction("android.net.wifi.STATE_CHANGE")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            <EMAIL>(receiver, filter, Context.RECEIVER_VISIBLE_TO_INSTANT_APPS)
        } else {
            <EMAIL>(receiver, filter)

        }
        setConnectivityListener(this)
    }

    fun getContentId(): String {
        return  id.toString()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if (intent.hasExtra(AppConstants.BUNDLE_ASSET_BUNDLE)) {
            val extras = intent.extras
            if (extras != null) {
                assetId = intent.extras!!.getBundle(AppConstants.BUNDLE_ASSET_BUNDLE)!!
                    .getInt(AppConstants.BUNDLE_ASSET_ID)
                refreshDetailPage()
            }
        } else {
            throw IllegalArgumentException("Activity cannot find  extras " + "Search_Show_All")
        }
    }

    private fun refreshDetailPage() {
        callBinding()
    }

    private fun callBinding() {
        modelCall()
    }
    private fun checkGeoBlocking(id: Int) {
        viewModel!!.getGeoBlocking(id.toString())
            .observe(this@DetailActivity) { response ->
                if (response != null && response.data != null) {
                    if(response.data.isIsBlocked) {
                        isGeoBlocking = true
                    }
                } else {
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
            }
    }

    private fun modelCall() {
        binding!!.connection.retryTxt.setOnClickListener {
            binding!!.llParent.visibility = View.VISIBLE
            binding!!.noConnectionLayout.visibility = View.GONE
            connectionObserver()
        }
        binding!!.backButton.setOnClickListener { onBackPressed() }
        connectionObserver()
    }

    fun openLoginPage(context: String?) {
        preference!!.setReturnTo(AppConstants.ContentType.VIDEO.toString())
        preference!!.appPrefJumpBack = true
        preference!!.appPrefIsEpisode = false
        preference!!.appPrefJumpBackId = assetId
        ActivityLauncher.getInstance().loginActivity(this@DetailActivity, ActivityLogin::class.java, "")
    }

    @SuppressLint("SuspiciousIndentation")
    fun uiInitialisation() {
        callShimmer()
        val data = Data()
        data.contentTitle = ""
        setupUI(binding!!.llParent)
        isLogin = preference!!.appPrefLoginStatus
        binding!!.noConnectionLayout.visibility = View.GONE
        preference!!.appPrefAssetId = assetId
            if (contentSlug.isNotEmpty()) {
                assetDetails
            } else if (assetId != 0) {
                assetDetailsByID
            } else {
                commonDialog(
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                        getString(R.string.popup_this_content_not_available)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                        getString(R.string.popup_continue)
                    ),
                )
            }
        }


    private fun setTagAndKeywordUi(videoItemBean: EnveuVideoItemBean?) {
        val list= arrayListOf(videoItemBean?.display_tags ?:"")
        val adapter= LayoutVideoMetaTagsAdapter(this@DetailActivity,list,object :
            LayoutVideoMetaTagsCallback {
            override fun tagClicked(position: Int) {

            }
        })

        binding!!.metaDetails.rvLvmTags.layoutManager = LinearLayoutManager(this@DetailActivity,LinearLayoutManager.HORIZONTAL,false)
        binding!!.metaDetails.rvLvmTags.adapter = adapter
        binding!!.metaDetails.rvLvmTags.visibility = View.GONE
    }


    private fun playContent(){
        if (isLoggedIn) {
            if (isGeoBlocking) {
                commonDialog(
                    stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.geo_blocking_title.toString(),
                        getString(R.string.geo_blocking_title)),
                    "",
                    stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_ok.toString(),getString(R.string.ok))
                )
            } else {
                if (!videoDetails!!.isPremium) {
                    if (isUserVerified.equals("true", ignoreCase = true)) {
                        if (null != videoDetails!!.externalRefId && !videoDetails?.externalRefId.equals("", ignoreCase = true)) {
                            if (videoDetails!!.drmDisabled != false){
                                viewModel?.getLicenseUrl("",videoDetails!!.sku,AppConstants.JW_DRM_KEY)?.observe(this){
                                    if (it != null){
                                         signedUrl = it.toString()
                                         drmBaseUrl = signedUrl.substringBefore("?")
                                         drmToken = signedUrl.substringAfter("token=")
                                        callJwApiToGetWidevineUrl(drmBaseUrl,drmToken)
                                    }else{
                                        commonDialog(
                                            stringsHelper.stringParse(
                                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                                getString(R.string.popup_error)
                                            ),
                                            stringsHelper.stringParse(
                                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                                getString(R.string.popup_something_went_wrong)
                                            ),
                                            stringsHelper.stringParse(
                                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                                getString(R.string.popup_continue)
                                            )
                                        )
                                    }
                                }

                            }else {
                                playbackUrl = videoDetails?.externalRefId
                                if (this.mediaType.equals(AppConstants.MOVIES_AUDIO) || this.mediaType.equals(AppConstants.SHOW_AUDIO) || this.mediaType.equals(AppConstants.TRAILER_AUDIO)) {
                                    startAudioPlayer(videoDetails!!.externalRefId, videoDetails!!.title, id)
                                } else {
                                    startPlayer(playbackUrl, videoDetails)
                                }
                            }
                        }
                    } else {
                        isUserNotVerify = true
                        commonDialog(
                            "",
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(),
                                getString(R.string.popup_user_not_verify)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_verify.toString(),
                                getString(R.string.popup_verify)
                            )
                        )
                    }
                }
                else {
                    binding?.progressBar?.visibility = View.VISIBLE
                    viewModel?.hitApiEntitlement(token, videoDetails!!.sku)?.observe(this@DetailActivity) { responseEntitle ->
                            binding!!.progressBar.visibility = View.GONE
                            if (responseEntitle != null && responseEntitle.data != null) {
                                resEntitle = responseEntitle
                                if (responseEntitle.data.entitled) {
                                    if (isUserVerified.equals("true", ignoreCase = true)) {
                                        viewModel?.externalRefID(responseEntitle.data?.accessToken, responseEntitle.data?.sku)?.observe(this){ drm->
                                            if (videoDetails!!.drmDisabled){
                                                viewModel?.getLicenseUrl(responseEntitle.data?.accessToken,responseEntitle.data?.sku,AppConstants.JW_DRM_KEY)?.observe(this){
                                                    if (it != null){
                                                        signedUrl = it.toString()
                                                        drmBaseUrl = signedUrl.substringBefore("?")
                                                        drmToken = signedUrl.substringAfter("token=")
                                                        callJwApiToGetWidevineUrl(drmBaseUrl,drmToken)
                                                    }else{
                                                        commonDialog(
                                                            stringsHelper.stringParse(
                                                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                                                getString(R.string.popup_error)
                                                            ),
                                                            stringsHelper.stringParse(
                                                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                                                getString(R.string.popup_something_went_wrong)
                                                            ),
                                                            stringsHelper.stringParse(
                                                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                                                getString(R.string.popup_continue)
                                                            )
                                                        )
                                                    }
                                                }
                                            }else {
                                                playbackUrl = drm.data?.externalRefId
                                                if (this.mediaType.equals(AppConstants.MOVIES_AUDIO) || this.mediaType.equals(AppConstants.SHOW_AUDIO) || this.mediaType.equals(AppConstants.TRAILER_AUDIO)) {
                                                    startAudioPlayer(videoDetails!!.externalRefId, videoDetails!!.title, id)
                                                } else {
                                                    startPlayer(playbackUrl, videoDetails)
                                                }
                                            }
                                        }
                                    } else {
                                        isUserNotVerify = true
                                        commonDialog(
                                            "",
                                            stringsHelper.stringParse(
                                                stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(),
                                                getString(R.string.popup_user_not_verify)
                                            ),
                                            stringsHelper.stringParse(
                                                stringsHelper.instance()?.data?.config?.popup_verify.toString(),
                                                getString(R.string.popup_verify)
                                            )
                                        )
                                    }
                                } else {
                                    isUserNotEntitle = true
                                    commonDialogWithCancel(
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                                            getString(R.string.popup_notEntitled)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_select_plan.toString(),
                                            getString(R.string.popup_select_plan)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_purchase.toString(),
                                            getString(R.string.popup_purchase)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                                            getString(R.string.popup_cancel)
                                        )
                                    )
                                }
                            } else {
                                if (responseEntitle?.responseCode != null && responseEntitle.responseCode == 4302) {
                                    preference?.let { clearCredientials(it) }
                                    ActivityLauncher.getInstance().loginActivity(
                                        this@DetailActivity,
                                        ActivityLogin::class.java, ""
                                    )
                                } else {
                                    commonDialog(
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                            getString(R.string.popup_error)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                            getString(R.string.popup_something_went_wrong)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                            getString(R.string.popup_continue)
                                        )
                                    )
                                }
                            }
                        }
                }
            }
        } else {
            ActivityLauncher.getInstance().loginActivity(this@DetailActivity, ActivityLogin::class.java, "")
        }
    }

    private var widevineUrl = ""
    private var fileUrl = ""
    private fun callJwApiToGetWidevineUrl(signedUrl: String, drmToken: String) {
        viewModel?.getWidevineUrl(signedUrl,drmToken)?.observe(this){
            widevineUrl = it?.playlist?.get(0)?.sources?.get(0)?.drm?.widevine?.url.toString()
            fileUrl = it?.playlist?.get(0)?.sources?.get(0)?.file.toString()
            if (widevineUrl != "" && fileUrl != "") {
                startPlayer(playbackUrl, videoDetails)
                Log.d("widevineUrl", widevineUrl)
                Log.d("widevineUrl>>", fileUrl)
            }
        }
    }

    private fun startAudioPlayer(
        externalRefId: String,
        title: String,
        id: Int
    ) {
        ActivityLauncher.getInstance().podcastPlayer(this, EntBackgroundAudioActivity::class.java,externalRefId,null,title,id,videoDetails!!.drmDisabled)
    }

    private var resEntitle: ResponseEntitle? = null
    @SuppressLint("SuspiciousIndentation")
    private fun setClicks() {
        if (featureList?.featureFlag?.AUTO_PLAY == true) {
            if (this.mediaType.equals(AppConstants.MOVIES_AUDIO) || this.mediaType.equals(
                    AppConstants.SHOW_AUDIO
                ) || this.mediaType.equals(AppConstants.TRAILER_AUDIO)
            ) {
                //stay at detail page and user can  play audio on click of watch now
            } else {
                playContent()
            }
        }

        binding!!.metaDetails.trailerButton.setOnClickListener {
//            if (this.mediaType.equals(AppConstants.MOVIES_AUDIO) || this.mediaType.equals(
//                    AppConstants.SHOW_AUDIO
//                ) || this.mediaType.equals(AppConstants.TRAILER_AUDIO)
//            ) {
//                startAudioPlayer(videoDetails!!.externalRefId, videoDetails!!.title, id)
//            } else {
//                startPlayer(trailerUrl)
//            }
            startVideoPlayer(trailerDetails?.externalRefId, trailerDetails)
        }

        binding!!.metaDetails.playButton.setOnClickListener {
            if(assetType.equals(Constants.CUSTOM, ignoreCase = true) && customType.equals(Constants.GAME, ignoreCase = true)){
                if (!videoDetails?.playPage.isNullOrEmpty()) {
                    val intent = Intent(this, WebViewActivity::class.java)
                    intent.putExtra(Constants.WEB_VIEW_URL, videoDetails?.playPage)
                    startActivity(intent)
                }
            }
            else{
                playContent()
            }
        }
    }

    private val detailsPlayerCallBack = object : PlayerFragment.DetailsPlayerCallBack {
        override fun onPlayerError() {
            binding?.playerImage?.visibility = View.VISIBLE
            binding?.backButton?.visibility = View.VISIBLE
        }

        override fun onCompleted() {
            binding?.playerImage?.visibility = View.VISIBLE
            binding?.backButton?.visibility = View.VISIBLE
        }
    }

    private fun startVideoPlayer(externalRefId: String?, videoDetails:EnveuVideoItemBean?) {
        val contentMetaData = ContentMetaData()
        contentMetaData.playBackUrl = externalRefId
        contentMetaData.contentTitle = videoDetails?.title
        contentMetaData.isLive = false
        contentMetaData.contentType = videoDetails?.contentType
        contentMetaData.mediaType = mediaType
        contentMetaData.contentId = videoDetails?.id
        contentMetaData.isDrmDisabled = videoDetails!!.drmDisabled
        contentMetaData.widevineUrl = widevineUrl
        contentMetaData.fileUrl = fileUrl
        val intent = Intent(this, PlayerActivity::class.java)
        intent.putExtra(AppConstants.CONTENT_META_DATA, contentMetaData)
        intent.putExtra(Constants.SINGLE_CONTENT_BUNDLE, Gson().toJson(videoDetails))
        startActivity(intent)
    }

    private fun startPlayer(playBackUrl: String?, videoDetails: EnveuVideoItemBean?) {
        if (featureList?.featureFlag?.PORTRAIT_MODE == true) {
            if (playerFragment != null) {
                if (playBackUrl != null) {
                    videoDetails?.id?.let {
                        playerFragment?.reloadPlayer(playBackUrl,videoDetails?.title.toString(),mediaType!!, false,
                            it, videoDetails?.contentType.toString(), true,videoDetails!!.drmDisabled,widevineUrl,fileUrl)
                    }
                }
            } else {
                playerFragment = PlayerFragment()
                detailsPlayerCallBack.let { playerFragment?.setDetailsPlayerCallBack(it) }
                val bundle = Bundle()
                bundle.putString("contentUrl", playBackUrl)
                bundle.putString("mediaType", mediaType)
                bundle.putString("contentTitle", videoDetails?.title)
                bundle.putInt("contentID", videoDetails?.id!!)
                bundle.putString("contentType", videoDetails?.contentType)
                bundle.putBoolean("IsLive", false)
                bundle.putBoolean("bookmarkingEnabled", true)
                bundle.putBoolean("isDrmDisabled", videoDetails!!.drmDisabled)
                bundle.putString("widevineUrl", widevineUrl)
                bundle.putString("fileUrl", fileUrl)
                playerFragment?.arguments = bundle
                supportFragmentManager.beginTransaction().add(R.id.player_frame, playerFragment!!, "PlayerFragment").commit()
                binding?.playerImage?.visibility = View.VISIBLE
                binding?.backButton?.visibility = View.GONE
            }
        }else {
            startVideoPlayer(playBackUrl, videoDetails)
        }
    }
    var railInjectionHelper: RailInjectionHelper? = null
    private val assetDetails: Unit
        get() {
            railInjectionHelper?.getAssetDetailsbySlug(contentSlug)?.observe(this) { assetResponse: ResponseModel<*>? ->
                    if (assetResponse != null) {
                        if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                        } else if (assetResponse.status.equals(
                                APIStatus.SUCCESS.name, ignoreCase = true
                            )
                        ) {
                            parseAssetDetails(assetResponse)
                        } else if (assetResponse.status.equals(
                                APIStatus.ERROR.name, ignoreCase = true
                            )
                        ) {
                            if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {

                            }
                        } else if (assetResponse.status.equals(
                                APIStatus.FAILURE.name, ignoreCase = true
                            )
                        ) {

                        }
                    }
                }
        }




    private val assetDetailsByID: Unit
        get() {
            railInjectionHelper!!.getAssetDetailsV3(assetId.toString())
                .observe(this@DetailActivity) { assetResponse: ResponseModel<*>? ->
                    if (assetResponse != null) {
                        val gson = Gson()
                        val json = gson.toJson(assetResponse.baseCategory)
                        Log.w("getAssetDetailsV2-->>>>>", json)

                        if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                        } else if (assetResponse.status.equals(
                                APIStatus.SUCCESS.name,
                                ignoreCase = true
                            )
                        ) {
                            parseAssetDetails(assetResponse)
                        } else if (assetResponse.status.equals(
                                APIStatus.ERROR.name,
                                ignoreCase = true
                            )
                        ) {
                            if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {
                                commonDialog(
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                        getString(R.string.popup_error)
                                    ),
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                        getString(R.string.popup_something_went_wrong)
                                    ),
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                        getString(R.string.popup_continue)
                                    )
                                )
                            }
                        } else if (assetResponse.status.equals(
                                APIStatus.FAILURE.name,
                                ignoreCase = true
                            )
                        ) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.popup_something_went_wrong)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                ),
                            )
                        }
                    }
                }
        }


    private fun parseAssetDetails(assetResponse: ResponseModel<*>) {
        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
        if (enveuCommonResponse.enveuVideoItemBeans.size > 0) {
            videoDetails = enveuCommonResponse.enveuVideoItemBeans[0]
            mediaType = if (videoDetails!!.assetType.equals(AppConstants.CUSTOM)) {
                videoDetails!!.customType
            }  else if (videoDetails!!.assetType.equals(AppConstants.LIVE,ignoreCase = true)) {
                videoDetails?.liveContent?.liveType!!
            } else {
                videoDetails?.videoDetailsV3?.videoType
            }
            if (!isFromEpg)
                checkGeoBlocking(videoDetails!!.id)
            AnalyticsUtils.trackScreenView(this,AppConstants.CONTENT_DETAIL + " - " + videoDetails?.title )
            getAppLevelJsonData()
            binding!!.metaDetails.descriptionText.ellipsize = TextUtils.TruncateAt.END
            keyword = videoDetails!!.display_tags
             id = videoDetails!!.id
            getLinkedTrailer()
            prepareEnabledTabs()
            if (videoDetails?.seriesCustomData!=null && videoDetails?.seriesCustomData?.trailer_reference_id != null) {
                videoDetails?.seriesCustomData?.trailer_reference_id?.let { getTrailer(it) }
            }
            stopShimmer()
            thumbnailImageUrl = videoDetails?.posterURL.toString()
            if (featureList?.featureFlag?.IS_MUSIC_APP == true){
                setAudioInteractionFragment(assetId)
            }else {
                setUserInteractionFragment(assetId)
            }
            setDetailUI(videoDetails)
        }
    }

    private fun getTrailer(trailerReferenceId: String) {
        railInjectionHelper!!.getAssetDetailsV2(trailerReferenceId, this@DetailActivity)
            .observe(this@DetailActivity) { assetResponse: ResponseModel<*>? ->
                if (assetResponse != null) {
                    if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                        Log.d(DETAIL_ACTIVITY, "getAssetDetails")
                    } else if (assetResponse.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
                        if (enveuCommonResponse.enveuVideoItemBeans.size > 0) {
                            if (!enveuCommonResponse.enveuVideoItemBeans[0].externalRefId.equals("", ignoreCase = true) && !enveuCommonResponse.enveuVideoItemBeans[0].externalRefId.equals(null, ignoreCase = true)) {
                                trailerExternalRefId = enveuCommonResponse.enveuVideoItemBeans[0].externalRefId
                                trailerUrl = enveuCommonResponse.enveuVideoItemBeans[0].externalRefId

                                if (mediaConfig!!.detailPage.features.isTrailerEnabled && !trailerExternalRefId.equals("")) {
                                    binding!!.metaDetails.trailerButton.visibility = View.VISIBLE
                                } else {
                                    binding!!.metaDetails.trailerButton.visibility = View.GONE
                                }
                            }
                        }
                    } else if (assetResponse.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                        if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.popup_something_went_wrong)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        }
                    } else if (assetResponse.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
                }
            }
    }

    private fun setUserInteractionFragment(id: Int) {
        val transaction = supportFragmentManager.beginTransaction()
        val args = Bundle()
        args.putInt(AppConstants.BUNDLE_ASSET_ID, id)
        args.putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
        args.putSerializable(AppConstants.BUNDLE_SERIES_DETAIL, videoDetails)
        args.putBoolean(Constants.HIDE_WATCHLIST_ICON, hideWatchlistIcon)
        if (isFromEpg) {
            val enveuVideoItemBean = EnveuVideoItemBean().apply {
                posterURL = epgItem?.mediaContent?.images?.get(0)?.imageContent?.src
                <EMAIL> = epgItem?.mediaContent?.title
                assetType = epgItem?.mediaContent?.contentType
                description = epgItem?.mediaContent?.description
                contentSlug = epgItem?.mediaContent?.contentSlug
            }
            args.putSerializable(AppConstants.BUNDLE_SERIES_DETAIL, enveuVideoItemBean)
            args.putString(AppConstants.FROM_REDIRECTION, AppConstants.EPG_FRAGMENT)
        }
        userInteractionFragment = UserInteractionFragment()
        userInteractionFragment!!.arguments = args
        transaction.replace(R.id.fragment_user_interaction, userInteractionFragment!!)
        transaction.addToBackStack(null)
        transaction.commit()
    }
    private fun setAudioInteractionFragment(assetId: Int) {
        val transaction = supportFragmentManager.beginTransaction()
        val args = Bundle()
        args.putInt(AppConstants.BUNDLE_ASSET_ID, id)
        args.putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
        args.putSerializable(AppConstants.BUNDLE_SERIES_DETAIL, videoDetails)
        args.putString(AppConstants.BUNDLE_CONTENT_SLUG, videoDetails?.contentSlug)
        args.putString(AppConstants.THUMBNAIL_IMG,thumbnailImageUrl)
        args.putString(AppConstants.AUDIO_INTERACTION_REDIRECTION,AppConstants.VIDEO)
        audioInteractionFragment = AudioInteractionFragment()
        audioInteractionFragment!!.arguments = args
        audioInteractionFragment!!.passInstance(this)
        setDataModleandSendToAudioInteractionFrag()
        transaction.replace(R.id.fragment_user_interaction, audioInteractionFragment!!)
        transaction.addToBackStack(null)
        transaction.commit()
    }
    private fun setDataModleandSendToAudioInteractionFrag() {
        val gson=Gson()
        val json=gson.toJson(videoDetails)
        val songDetail: DataItem =gson.fromJson(json, DataItem::class.java)
        audioInteractionFragment?.setAttachedFragmentDetails(songDetail)
    }
    private fun setDetailUI(responseDetailPlayer: EnveuVideoItemBean?) {
        if (responseDetailPlayer?.posterURL?.isEmpty() == true) {
            binding?.titleTv?.text = responseDetailPlayer.title
        }
        ImageHelper.getInstance(this@DetailActivity).loadListImage(binding!!.playerImage,responseDetailPlayer?.posterURL)
        if (responseDetailPlayer!!.assetType != null && responseDetailPlayer.duration > 0) {
            val durationInMinutes = (AppCommonMethod.stringForTime(responseDetailPlayer.duration))
            setCustomFields(responseDetailPlayer, durationInMinutes)
        } else {
            setCustomFields(responseDetailPlayer, "")
        }
        binding!!.responseApi = responseDetailPlayer
        setClicks()
    }

    private fun setCustomFields(videoItemBean: EnveuVideoItemBean?, duration: String?) {
        try {
            if (videoItemBean?.title != null) {
                binding!!.metaDetails.tvTitle.text = videoItemBean.title
            } else {
                binding!!.metaDetails.tvTitle.visibility = View.GONE
            }

            if (!videoItemBean?.customData?.showTitle.isNullOrEmpty()){
                if (videoItemBean?.customData?.showTitle.equals("true")){
                    binding?.metaDetails?.tvTitle?.show()
                }
                else{
                    binding?.metaDetails?.tvTitle?.hide()
                }
            }
            else{
                binding?.metaDetails?.tvTitle?.hide()
            }

            if (videoItemBean?.description != null) {
                binding!!.metaDetails.descriptionText.text = videoItemBean.description
            } else {
                binding!!.metaDetails.descriptionText.visibility = View.GONE
            }

            var movieArtistName = ""
            videoItemBean?.movieArtistsIds?.forEachIndexed { index, item ->
                movieArtistName += if (index == videoItemBean.movieArtistsIds?.size?.minus(1)) {
                    item.title
                } else {
                    "${item.title}, "
                }
            }
            if (movieArtistName.isNotBlank()) {
                binding?.metaDetails?.castText?.visibility = View.VISIBLE
                binding!!.metaDetails.castValveText.text = movieArtistName
            } else {
                binding!!.metaDetails.rootCast.visibility = View.GONE
            }

            if (videoItemBean?.customData?.director?.firstOrNull()?.title != null) {
                binding!!.metaDetails.directorTextView.visibility = View.VISIBLE
                binding!!.metaDetails.directorTextView.text = getString(R.string.director_text, videoItemBean.customData?.director?.firstOrNull()?.title)
            } else {
                binding!!.metaDetails.directorTextView.visibility = View.GONE
            }
            if (videoItemBean?.customData?.actor?.isNotEmpty() == true) {
                val titles = videoItemBean.customData?.actor?.map { it.title }
                val fullText = getString(R.string.staring, titles?.joinToString(", "))
                binding!!.metaDetails.actorsTextView.visibility = View.VISIBLE
                makeTextWithMore(binding!!.metaDetails.actorsTextView, fullText, videoItemBean)
                binding!!.metaDetails.actorsTextView.text = getString(R.string.staring, titles?.joinToString(", "))
            } else {
                binding!!.metaDetails.actorsTextView.visibility = View.GONE
            }

            var movieDirectorName = ""
            videoItemBean?.movieDirectorId?.forEachIndexed { index, item ->
                if (index == videoItemBean.movieDirectorId?.size?.minus(1)) {
                    movieDirectorName += item.title
                } else {
                    movieDirectorName += "${item.title}, "
                }
            }
            if (movieDirectorName.isNotBlank()) {
                binding?.metaDetails?.crewText?.visibility = View.VISIBLE
                binding!!.metaDetails.crewValveText.text = movieDirectorName
            } else {
                binding!!.metaDetails.rootCrew.visibility = View.GONE
            }

            if (duration != null) {
                setTextOrHide(binding!!.metaDetails.duration, duration)
            } else {
                binding!!.metaDetails.duration.visibility = View.GONE
            }

            setTagAndKeywordUi(videoItemBean)

            if(videoItemBean?.releaseYear != null && videoItemBean.releaseYear.isNotBlank()){
                binding!!.metaDetails.llKeyYear.text=videoItemBean.releaseYear
                binding!!.metaDetails.llKeyYear.visibility=View.VISIBLE
                binding!!.metaDetails.llKeyDotyear.visibility=View.VISIBLE
            }else{
                binding!!.metaDetails.llKeyYear.visibility=View.GONE
            }

            if(videoItemBean?.languages != null && videoItemBean.languages.isNotBlank()){
                binding!!.metaDetails.llKeyLanguage.text=videoItemBean.languages
                binding!!.metaDetails.llKeyLanguage.visibility=View.VISIBLE
                binding!!.metaDetails.llKeyDotlang.visibility=View.VISIBLE
            }else{
                binding!!.metaDetails.llKeyLanguage.visibility=View.GONE
            }

            if(videoItemBean?.certificate != null && videoItemBean.certificate.isNotBlank()){
                binding!!.metaDetails.llKeyCertification.text=videoItemBean.certificate
                binding!!.metaDetails.llKeyCertification.visibility=View.VISIBLE
                binding!!.metaDetails.llKeyDotcert.visibility=View.VISIBLE
            }else{
                binding!!.metaDetails.llKeyCertification.visibility=View.GONE
            }

            if (mediaConfig!!.detailPage.features.watchNowEnabled) {
                binding!!.metaDetails.playButton.visibility = View.VISIBLE
            } else {
                binding!!.metaDetails.playButton.visibility = View.GONE
            }
        } catch (e: Exception) {
            Logger.w(e)
        }
    }

    private fun makeTextWithMore(textView: TextView, fullText: String, videoItemBean: EnveuVideoItemBean?) {
        textView.maxLines = 2
        textView.post {
            val layout = textView.layout
            if (layout != null && layout.lineCount >= 2) {
                binding!!.metaDetails.directorTextView.visibility = View.GONE
                val endIndex = layout.getLineEnd(1)
                val visibleText = fullText.substring(0, endIndex).trimEnd()

                val ellipsis = "..."
                val moreText = " more"
                val displayText = "$visibleText$ellipsis$moreText"

                val spannable = SpannableString(displayText)
                val start = displayText.indexOf(moreText)

                spannable.setSpan(object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        openMovieDetailBottomSheet(videoItemBean)
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        ds.color = Color.RED
                        ds.isUnderlineText = false
                    }
                }, start, displayText.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

                textView.text = spannable
                textView.movementMethod = LinkMovementMethod.getInstance()
            } else {
                textView.text = fullText
            }
        }
    }

    @SuppressLint("ResourceAsColor")
    private fun openMovieDetailBottomSheet(videoItemBean: EnveuVideoItemBean?) {
        val dialog = BottomSheetDialog(this)
        val view = layoutInflater.inflate(R.layout.bottomsheet_movie_details, null)
        dialog.setContentView(view)

        val parent = view.parent as View
        parent.setBackgroundColor(R.color.background)

        val movieTitle = view.findViewById<TextView>(R.id.movie_title)
        val actorData = view.findViewById<RecyclerView>(R.id.actor_rv)
        val directorData = view.findViewById<RecyclerView>(R.id.director_rv)
        val cancelText = view.findViewById<ImageView>(R.id.cross)

        val customData = videoItemBean?.customData

        movieTitle.text = (videoItemBean?.title ?: "Untitled").toString()


        val actors = videoItemBean?.customData?.actor?.map { Person(it.title ?: "Unknown Actor") }?.takeIf { it.isNotEmpty() } ?: emptyList()
        val directors = videoItemBean?.customData?.director?.map { Person(it.title ?: "Unknown Director") }?.takeIf { it.isNotEmpty() } ?: emptyList()

        // Setup Actor RecyclerView
        actorData.apply {
            layoutManager = LinearLayoutManager(this@DetailActivity, LinearLayoutManager.VERTICAL, false)
            adapter = ActorAdapter(actors)
        }

        // Setup Director RecyclerView
        directorData.apply {
            layoutManager = LinearLayoutManager(this@DetailActivity, LinearLayoutManager.VERTICAL, false)
            adapter = DirectorAdapter(directors)
        }
        cancelText.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun noConnectionLayout() {
        binding!!.llParent.visibility = View.GONE
        binding!!.noConnectionLayout.visibility = View.VISIBLE
        binding!!.connection.retryTxt.setOnClickListener { connectionObserver() }
    }

    private var receiver: NetworkChangeReceiver? = null
    override fun onBackPressed() {
        super.onBackPressed()
        if (preference!!.appPrefJumpBack) {
            preference!!.appPrefJumpBackId = 0
            preference!!.appPrefVideoPosition = 0.toString()
            preference!!.appPrefJumpBack = false
            preference!!.appPrefGotoPurchase = false
            preference!!.appPrefIsEpisode = false
        }
        preference!!.appPrefAssetId = 0
        AppCommonMethod.seasonId = -1
        val orientation = this.resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            finish()
        }
    }

    override fun onPause() {
        releaseAudioFocusForMyApp()
        dismissLoading(binding!!.progressBar)
        super.onPause()
    }

    override fun onStop() {
        try {
            if (receiver != null) {
                unregisterReceiver(receiver)
                NetworkChangeReceiver.connectivityReceiverListener = null
            }
        } catch (e: Exception) {
            Logger.w(e)
        }
        super.onStop()
    }

    override fun onDestroy() {
        Logger.d("here")
        preference!!.appPrefAssetId = 0
        preference!!.appPrefJumpTo = ""
        preference!!.appPrefBranchIo = false
        AppCommonMethod.seasonId = -1
        super.onDestroy()
    }


    private fun setConnectivityListener(listener: NetworkChangeReceiver.ConnectivityReceiverListener?) {
        NetworkChangeReceiver.connectivityReceiverListener = listener
    }

    override fun onNetworkConnectionChanged(isConnected: Boolean) {
        if (KsPreferenceKeys.getInstance().downloadOverWifi == 1) {
            isWifiEnabled(this)
        } else {
            NetworkConnectivity.isOnline(this)
        }
    }

    override fun onAudioFocusChange(focusChange: Int) {
        val manager = this.getSystemService(AUDIO_SERVICE) as AudioManager
        if (manager.isMusicActive) {
            when (focusChange) {
                AudioManager.AUDIOFOCUS_GAIN -> Logger.i("AUDIOFOCUS_GAIN")
                AudioManager.AUDIOFOCUS_LOSS -> Logger.i("AUDIOFOCUS_LOSS")
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> Logger.i("AUDIOFOCUS_LOSS_TRANSIENT")
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> Logger.i("AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK")
                else -> {}
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            setupLandscapeView()
        } else {
            setupPortraitView()
        }
    }

    private fun setupPortraitView() {
        val params = binding!!.playerFrame.layoutParams as ConstraintLayout.LayoutParams
        params.width = ConstraintLayout.LayoutParams.MATCH_CONSTRAINT
        params.height = ConstraintLayout.LayoutParams.MATCH_CONSTRAINT
        binding!!.playerFrame.layoutParams = params
        val set = ConstraintSet()
        set.clone(binding!!.llParent)
        set.connect(
            R.id.player_frame,
            ConstraintSet.END,
            ConstraintSet.PARENT_ID,
            ConstraintSet.END
        )
        set.connect(
            R.id.player_frame,
            ConstraintSet.START,
            ConstraintSet.PARENT_ID,
            ConstraintSet.START
        )
        set.connect(
            R.id.player_frame,
            ConstraintSet.TOP,
            ConstraintSet.PARENT_ID,
            ConstraintSet.TOP
        )
        set.setDimensionRatio(R.id.player_frame, "H,16:9")
        set.applyTo(binding!!.llParent)
        binding!!.rootScroll.visibility = View.VISIBLE
        binding?.relatedContentLayout?.visibility = View.VISIBLE
    }


    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_IMMERSIVE
                        or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_LOW_PROFILE
                        or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                )
    }

    private fun setupLandscapeView() {
        supportActionBar?.hide()
        hideSystemUI()
        window?.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        //endregion
        //region To hide the navigation button
        window?.decorView?.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
        val params = binding?.playerFrame?.layoutParams as ConstraintLayout.LayoutParams
        val displayMetrics = resources.displayMetrics
        params.width = displayMetrics.widthPixels
        params.height = displayMetrics.heightPixels
        binding?.playerFrame?.layoutParams = params
        binding?.rootScroll?.visibility = View.GONE
        binding?.relatedContentLayout?.visibility = View.GONE
    }
    override fun railItemClick(item: RailCommonData, position: Int) {
        AppCommonMethod.redirectionLogic(
            this,
            item,
            position,"","","")
    }

    override fun moreRailClick(data:RailCommonData, position: Int, multilingualTitle: String) {
        Logger.d(data.screenWidget.contentID + "  " + data.screenWidget.landingPageTitle + " " + 0 + " " + 0)
        if (data.screenWidget != null && data.screenWidget.contentID != null) {
            val playListId = data.screenWidget.contentID
            if (data.screenWidget.name != null) {
                ActivityLauncher.getInstance().listActivity(
                    this@DetailActivity,
                    ListActivity::class.java,
                    playListId,
                    data.screenWidget.name.toString(),
                    0,
                    0,
                    data.screenWidget
                )
            } else {
                ActivityLauncher.getInstance().listActivity(
                    this@DetailActivity,
                    ListActivity::class.java,
                    playListId,
                    "",
                    0,
                    0,
                    data.screenWidget
                )
            }
        }
    }

    private var isPlayerError = false
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        super.onCreateOptionsMenu(menu)
        return true
    }

    fun getVideoDetails(): EnveuVideoItemBean? {
        return videoDetails
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun onActionBtnClicked() {
        if (isUserNotVerify) {
            ActivityLauncher.getInstance().goToEnterOTP(this, EnterOTPActivity::class.java, "DetailPage")
        } else if (isUserNotEntitle) {
            ActivityLauncher.getInstance().goToDetailPlanScreen(this, PaymentDetailPage::class.java, true, resEntitle)
        }else{
            onBackPressed()
        }
    }


    override fun onCancelBtnClicked() {

    }

    companion object {
        private const val DETAIL_ACTIVITY = "DetailActivity"
    }

    override fun onPlayClick(playQueueItems:Boolean?) {

    }

    override fun onShuffle() {

    }
}