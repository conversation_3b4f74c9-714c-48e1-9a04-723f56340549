package com.enveu.activities.detail.ui

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.databinding.ItemDirectorInfoBinding

class DirectorAdapter(
    private val directors: List<Person>
) : RecyclerView.Adapter<DirectorAdapter.DirectorViewHolder>() {

    class DirectorViewHolder(
        private val binding: ItemDirectorInfoBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(director: Person) {
            binding.directorTv.text = director.name
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DirectorViewHolder {
        val binding = ItemDirectorInfoBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return Director<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(binding)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, position: Int) {
        holder.bind(directors[position])
    }

    override fun getItemCount(): Int = directors.size
}