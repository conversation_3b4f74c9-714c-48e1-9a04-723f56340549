package com.enveu.activities.detail.ui

import android.app.AlertDialog
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Typeface
import android.media.AudioManager
import android.media.AudioManager.OnAudioFocusChangeListener
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager.OnPageChangeListener
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.detail.viewModel.DetailViewModel
import com.enveu.activities.listing.listui.ListActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.ActivitySelectSubscriptionPlan
import com.enveu.activities.usermanagment.ui.EnterOTPActivity
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.adapters.player.EpisodeTabAdapter
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaConfig
import com.enveu.appLevelModel.MediaInfo
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModel.selectedSeason.SelectedSeasonModel
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner
import com.enveu.callbacks.commonCallbacks.MoreClickListner
import com.enveu.callbacks.commonCallbacks.NetworkChangeReceiver
import com.enveu.databinding.EpisodeScreenBinding
import com.enveu.fragments.dialog.AlertDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstance
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstanceWithCancelBtn
import com.enveu.fragments.player.ui.CommonListAllFragment
import com.enveu.fragments.player.ui.MoreLikeThisFragment
import com.enveu.fragments.player.ui.SeasonTabFragment
import com.enveu.fragments.player.ui.UserInteractionFragment
import com.enveu.jwplayer.player.PlayerFragment
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.AppColors
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.config.LanguageLayer
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener
import com.google.gson.Gson


class EpisodeActivity : BaseBindingActivity<EpisodeScreenBinding?>(),
    AlertDialogFragment.AlertDialogListener, NetworkChangeReceiver.ConnectivityReceiverListener,
    OnAudioFocusChangeListener,
    CommonRailtItemClickListner,
    MoreClickListner, CommonDialogFragment.EditDialogListener {
    private var viewModel: DetailViewModel? = null
    private var preference: KsPreferenceKeys? = null
    private var assetId = 0
    private var seasonPosition = 0
    private var seriesId = 0
    private var token: String? = null
    private var isLogin: String? = null
    private var isLoggedOut = false
    private var isGeoBlocking = false
    private var trailerUrl: String? = null
    private var trailerExternalRefId: String? = null
    private var railInjectionHelper: RailInjectionHelper? = null
    private var seasonTabFragment: SeasonTabFragment? = null
    private var relatedContentFragment: MoreLikeThisFragment? = null
    private var commonListAllFragment: CommonListAllFragment? = null
    private var seriesDetailBean: EnveuVideoItemBean? = null
    private var keyword: String? = ""
    private var alertDialog: AlertDialog? = null
    private var episodeTabAdapter: EpisodeTabAdapter? = null

    @JvmField
    var isSeasonData = false

    @JvmField
    var isRailData = true
    private var videoDetails: EnveuVideoItemBean? = null
    private var userInteractionFragment: UserInteractionFragment? = null
    private var isLoggedIn = false
    private var id = 0
    private var isUserVerified: String? = null
    private var isUserNotVerify = false
    private var isUserNotEntitle = false
    private var playbackUrl: String? = null
    private var mediaType: String? = null
    private var currentEpisodeId = 0
    private var registrationLoginViewModel: RegistrationLoginViewModel? = null
    private val colorsHelper by lazy { ColorsHelper }
    private val stringsHelper by lazy { StringsHelper }
    override fun inflateBindingLayout(inflater: LayoutInflater): EpisodeScreenBinding {
        return EpisodeScreenBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parseColor()
        binding?.backButtonIcon?.let { rotateImageLocaleWise(it) }
        window.setBackgroundDrawableResource(R.color.app_bg_color)
        window.setFlags(
            WindowManager.LayoutParams.FLAG_SECURE,
            WindowManager.LayoutParams.FLAG_SECURE
        )
        preference = KsPreferenceKeys.getInstance()
        if (preference?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
        }
        isUserVerified = preference?.isVerified
        viewModel = ViewModelProvider(this@EpisodeActivity)[DetailViewModel::class.java]
        setupUI(binding!!.llParent)
        if (intent.hasExtra(AppConstants.BUNDLE_ASSET_BUNDLE)) {
            var extras = intent.extras
            if (extras != null) {
                extras = extras.getBundle(AppConstants.BUNDLE_ASSET_BUNDLE)
                assetId = extras?.getInt(AppConstants.BUNDLE_ASSET_ID)!!
                seasonPosition = extras.getInt(AppConstants.BUNDLE_SEASON_NUMBER)
                try {
                } catch (e: Exception) {
                    Logger.w(e)
                }
            }
        }
        setClicks()
        checkGeoBlocking()
        callBinding()
    }

    private fun parseColor() {
        binding!!.stringData = stringsHelper
        binding!!.colorsData = colorsHelper
        binding!!.metaDetails.colorsData = colorsHelper
        binding!!.metaDetails.stringData = stringsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
    }

    private fun callBinding() {
        modelCall()
    }

    private var mediaConfig: MediaConfig? = null
    private var featureList: FeatureFlagModel? = null
    private var stringMediaConfig: String? = null

    private fun getAppLevelJsonData(mediaType: String) {
        this.mediaType = mediaType
        Log.d("mediaType", "getAppLevelJsonData: $mediaType")
        featureList = AppConfigMethod.parseFeatureFlagList()
        val mediaConfigInfo: MediaInfo? = AppConfigMethod.getMediaMappingByType(mediaType)
        mediaConfig = AppConfigMethod.getMediaConfigByType(mediaConfigInfo?.pageType.toString())
    }


    private fun checkGeoBlocking() {
        viewModel!!.getGeoBlocking(assetId.toString())
            .observe(this@EpisodeActivity) { response ->
                if (response != null && response.data != null) {
                    if(response.data.isIsBlocked) {
                        isGeoBlocking = true
                    }
                } else {
                    commonDialog(stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_error.toString(), getString(R.string.popup_error)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(), getString(R.string.popup_something_went_wrong)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)
                        )
                    )
                }
            }
    }


    private fun modelCall() {
        binding!!.connection.retryTxt.setOnClickListener {
            binding!!.llParent.visibility = View.VISIBLE
            binding!!.noConnectionLayout.visibility = View.GONE
            connectionObserver()
        }
        binding!!.backButton.setOnClickListener { onBackPressed() }
        connectionObserver()
    }

    private var resEntitle: ResponseEntitle? = null

    private val detailsPlayerCallBack = object : PlayerFragment.DetailsPlayerCallBack {
        override fun onPlayerError() {
            binding?.playerImage?.visibility = View.VISIBLE
            binding?.backButton?.visibility = View.VISIBLE
        }

        override fun onCompleted() {
            binding?.playerImage?.visibility = View.VISIBLE
            binding?.backButton?.visibility = View.VISIBLE
        }
    }
    private fun setClicks() {
        binding!!.metaDetails.trailerButton.setOnClickListener {
            startPlayer(
                trailerUrl,
                bingeWatchEnable = false,
                isTrailer = true,
                trailerExternalRefId ?: ""
            )
        }

            try {
                currentEpisodeId = videoDetails!!.id
                if (isLoggedIn) {
                    if (isGeoBlocking) {
                        commonDialog(
                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.geo_blocking_title.toString(),
                                getString(R.string.geo_blocking_title)),
                            "",
                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_ok.toString(),getString(R.string.ok)
                            )
                        )
                    } else {
                        if (!videoDetails!!.isPremium) {
                            if (isUserVerified.equals("true", ignoreCase = true)) {
                                if (null != videoDetails!!.externalRefId && !videoDetails!!.externalRefId.equals("", ignoreCase = true)) {
                                    playbackUrl =  videoDetails!!.externalRefId
                                    startPlayer(playbackUrl, KsPreferenceKeys.getInstance().bingeWatchEnable, false,videoDetails!!.externalRefId)
                                }
                            } else {
                                isUserNotVerify = true
                                commonDialog(
                                    "",
                                    stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(), getString(R.string.popup_user_not_verify)),
                                    stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_verify.toString(), getString(R.string.popup_verify))
                                )
                            }
                        } else {
                            binding!!.progressBar.visibility = View.VISIBLE
                            viewModel?.hitApiEntitlement(token, videoDetails!!.sku)?.observe(this@EpisodeActivity) { responseEntitle ->
                                binding!!.progressBar.visibility = View.GONE
                                if (null != responseEntitle && null != responseEntitle.data) {
                                    resEntitle = responseEntitle
                                    if (responseEntitle.data.entitled) {
                                        if (isUserVerified.equals("true", ignoreCase = true)) {
                                            viewModel?.externalRefID(responseEntitle.data.accessToken,responseEntitle.data.sku)?.observe(this){
                                                    playbackUrl =  it?.data?.externalRefId
                                                    startPlayer(playbackUrl, KsPreferenceKeys.getInstance().bingeWatchEnable, false,it?.data?.externalRefId!!)
                                            }

                                        } else {
                                            isUserNotVerify = true
                                            commonDialog(
                                                "",
                                                stringsHelper.stringParse(
                                                    stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(),
                                                    getString(R.string.popup_user_not_verify)
                                                ),
                                                stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_verify.toString(), getString(R.string.popup_verify))
                                            )
                                        }
                                    } else {
                                        isUserNotEntitle = true
                                        commonDialogWithCancel(
                                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(), getString(R.string.popup_notEntitled)),
                                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_select_plan.toString(), getString(R.string.popup_select_plan)),
                                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_purchase.toString(), getString(R.string.popup_purchase)),
                                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_cancel.toString(), getString(R.string.popup_cancel))
                                        )
                                    }
                                } else {
                                    if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                                        preference?.let { clearCredientials(it) }
                                        ActivityLauncher.getInstance().loginActivity(this@EpisodeActivity, ActivityLogin::class.java, "home")
                                    } else {
                                        commonDialog(
                                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_error.toString(), getString(R.string.popup_error)),
                                            stringsHelper.stringParse(
                                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(), getString(R.string.popup_something_went_wrong)
                                            ),
                                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue))
                                        )
                                    }
                                }
                            }
                        }
                    }

                } else {
                    ActivityLauncher.getInstance()
                        .loginActivity(this@EpisodeActivity, ActivityLogin::class.java, "")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

    }


    private fun startPlayer(
        playbackUrl: String?,
        bingeWatchEnable: Boolean,
        isTrailer: Boolean,
        externalRefId: String
    ) {

        val playerFragment = PlayerFragment()
        detailsPlayerCallBack.let { playerFragment.setDetailsPlayerCallBack(it) }
        val bundle = Bundle()
        bundle.putString("contentUrl", playbackUrl)
        bundle.putString("mediaType", mediaType)
        bundle.putString("contentTitle", videoDetails!!.title)
        bundle.putInt("contentID",videoDetails?.id!!)
        bundle.putString("contentType", videoDetails!!.assetType)
        bundle.putBoolean("IsLive", false)
        playerFragment.arguments = bundle
        supportFragmentManager.beginTransaction()
            .add(R.id.player_frame, playerFragment, "PlayerFragment").commit()
    }

    private fun connectionObserver() {
        connectionValidation(NetworkConnectivity.isOnline(this))
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            uiInitialisation()
        } else {
            noConnectionLayout()
        }
    }

    private fun setTabs() {
        binding?.tabLayout?.setTabTextColors(AppColors.detailPageTabItemUnselectedTxtColor(), AppColors.detailPageTabItemSelectedTxtColor())

        binding!!.tabLayout.setSelectedTabIndicatorGravity(TabLayout.INDICATOR_GRAVITY_TOP)

        episodeTabAdapter =
            EpisodeTabAdapter(
                supportFragmentManager
            )

        if (mediaConfig!!.detailPage.tabs.episode.enabled) {
            callSeasonTabFragment()
        }
        if (mediaConfig!!.detailPage.tabs.moreLikeThis.enabled) {
            callMoreLikeThisFragment()
        }
        if (mediaConfig!!.detailPage.tabs.trailersAndMore.enabled) {
            callCommonListAllFragment(mediaConfig!!.detailPage.tabs.trailersAndMore.displayLabel,
                AppConstants.TRAILER_CUSTOM_DATA)
        }

        if (mediaConfig!!.detailPage.tabs.clipAndMore.enabled) {
            callCommonListAllFragment(mediaConfig!!.detailPage.tabs.clipAndMore.displayLabel,
                AppConstants.CLIPS_AND_MORE_CUSTOM_DATA)
        }

        if (mediaConfig!!.detailPage.tabs.highlights.enabled) {
            callCommonListAllFragment(mediaConfig!!.detailPage.tabs.highlights.displayLabel,
                AppConstants.HIGHLIHTS_CUSTOM_DATA)
        }

        if (mediaConfig!!.detailPage.tabs.interviews.enabled) {
            callCommonListAllFragment(mediaConfig!!.detailPage.tabs.interviews.displayLabel,
                AppConstants.INTERVIEW_CUSTOM_DATA)
        }

        if (mediaConfig!!.detailPage.tabs.replays.enabled) {
            callCommonListAllFragment(mediaConfig!!.detailPage.tabs.replays.displayLabel,
                AppConstants.REPLAYS_CUSTOM_DATA)
        }

        if (episodeTabAdapter!!.count > 2) {
            binding?.tabLayout!!.tabMode = TabLayout.MODE_SCROLLABLE
        } else {
            binding?.tabLayout!!.tabMode = TabLayout.MODE_FIXED
        }

        binding!!.viewPager.adapter = episodeTabAdapter
        binding!!.viewPager.offscreenPageLimit = 5
        binding!!.tabLayout.setupWithViewPager(binding!!.viewPager)

        binding?.tabLayout?.getTabAt(0)?.view?.background = ColorsHelper.strokeBgDrawable(
            AppColors.detailPageTabUnselectedBorderColor(),
            AppColors.detailPageTabUnselectedBorderColor(),
            0f
        )
        binding?.tabLayout?.background = ColorsHelper.strokeBgDrawable(
            AppColors.detailPageTabSelectedBorderColor(),
            AppColors.detailPageTabUnselectedBorderColor(),
            0f
        )
        binding!!.tabLayout.addOnTabSelectedListener(object : OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                showLoading(binding!!.progressBar, true)
                tab.view.background = ColorsHelper.strokeBgDrawable(
                    AppColors.detailPageTabUnselectedBorderColor(),
                    AppColors.detailPageTabUnselectedBorderColor(),
                    0f
                )
                Handler().postDelayed({ dismissLoading(binding!!.progressBar) }, 1500)
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                tab.view.background = ColorsHelper.strokeBgDrawable(
                    AppColors.detailPageTabSelectedBorderColor(),
                    AppColors.detailPageTabUnselectedBorderColor(),
                    0f
                )
            }

            override fun onTabReselected(tab: TabLayout.Tab) {}
        })
        binding!!.viewPager.addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                binding!!.viewPager.measure(
                    binding!!.viewPager.measuredWidth,
                    binding!!.viewPager.measuredHeight
                )
            }

            override fun onPageScrollStateChanged(state: Int) {}
        })
       // changeTabsFont()

    }


    private fun getTrailer(trailerReferenceId: String) {
        railInjectionHelper!!.getAssetDetailsV2(trailerReferenceId, this@EpisodeActivity)
            .observe(this@EpisodeActivity) { assetResponse: ResponseModel<*>? ->
                if (assetResponse != null) {
                    val gson = Gson()
                    val json = gson.toJson(assetResponse.baseCategory)
                    if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                    } else if (assetResponse.status.equals(
                            APIStatus.SUCCESS.name,
                            ignoreCase = true
                        )
                    ) {
                        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
                        if (enveuCommonResponse != null && enveuCommonResponse.enveuVideoItemBeans.size > 0) {
                            if (!enveuCommonResponse.enveuVideoItemBeans[0].externalRefId.equals("", ignoreCase = true) && !enveuCommonResponse.enveuVideoItemBeans[0].externalRefId.equals(null, ignoreCase = true)) {
                                trailerExternalRefId = enveuCommonResponse.enveuVideoItemBeans[0].externalRefId
                                trailerUrl =  enveuCommonResponse.enveuVideoItemBeans?.get(0)?.externalRefId

                                if (mediaConfig!!.detailPage.features.isTrailerEnabled && !trailerExternalRefId.equals("")) {
                                    binding!!.metaDetails.trailerButton.visibility = View.VISIBLE
                                } else {
                                    binding!!.metaDetails.trailerButton.visibility = View.GONE
                                }
                            }
                        }
                    } else if (assetResponse.status.equals(
                            APIStatus.ERROR.name,
                            ignoreCase = true
                        )
                    ) {
                        if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {
                            commonDialog(stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_error.toString(), getString(R.string.popup_error)),
                                stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(), getString(R.string.popup_something_went_wrong)),
                                stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)
                                )
                            )                        }
                    } else if (assetResponse.status.equals(
                            APIStatus.FAILURE.name,
                            ignoreCase = true
                        )
                    ) {
                        commonDialog(stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_error.toString(), getString(R.string.popup_error)),
                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(), getString(R.string.popup_something_went_wrong)),
                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)
                            )
                        )                    }
                }
            }
    }



    private fun callSeasonTabFragment() {
        seasonTabFragment = SeasonTabFragment()
        val bundleSeason = Bundle()
        bundleSeason.putInt(AppConstants.BUNDLE_SERIES_ID, seriesId)
        bundleSeason.putInt(AppConstants.BUNDLE_ASSET_ID, assetId)
        bundleSeason.putInt(AppConstants.BUNDLE_SEASON_NUMBER, seasonPosition)
        seasonTabFragment!!.arguments = bundleSeason
        episodeTabAdapter!!.addFragment(
            seasonTabFragment,
            mediaConfig!!.detailPage.tabs.episode.displayLabel
        )
    }

    private fun callMoreLikeThisFragment() {
        relatedContentFragment = MoreLikeThisFragment()
        val moreLkeThisValue = Bundle()
        moreLkeThisValue.putInt(AppConstants.ID, assetId)
        moreLkeThisValue.putString("videoType", AppConstants.Episode)
        moreLkeThisValue.putString("contentType", AppConstants.VIDEO)
        relatedContentFragment!!.arguments = moreLkeThisValue
        episodeTabAdapter!!.addFragment(relatedContentFragment, mediaConfig!!.detailPage.tabs.moreLikeThis.displayLabel)

    }


    private fun callCommonListAllFragment(tabTittle:String,customData:String) {
        commonListAllFragment = CommonListAllFragment()
        val bundleValue = Bundle()
        bundleValue.putInt(AppConstants.ID, seriesId)
        bundleValue.putString(AppConstants.TITLE, tabTittle)
        bundleValue.putString(AppConstants.CUSTOM_DATA, customData)
        commonListAllFragment!!.arguments = bundleValue
        episodeTabAdapter!!.addFragment(commonListAllFragment,tabTittle)

    }

    fun stopShimmercheck() {
        if (isSeasonData) {
            isSeasonData = false
            isRailData = true
            Handler().postDelayed({ stopShimmer() }, 1000)
        }
    }


    fun removeTab(position: Int) {
        if (binding!!.tabLayout.tabCount >= 1 && position <= binding!!.tabLayout.tabCount) {
            episodeTabAdapter!!.removeTabPage(position)
            // Update TabLayout width
            val params = binding?.tabLayout?.layoutParams
            params?.width = ViewGroup.LayoutParams.MATCH_PARENT
            binding?.tabLayout?.layoutParams = params

            if (binding!!.tabLayout.tabCount  > 2) {
                binding?.tabLayout!!.tabMode = TabLayout.MODE_SCROLLABLE
            } else {
                binding?.tabLayout!!.tabMode = TabLayout.MODE_FIXED
            }
        }
    }

    private fun callShimmer() {
        binding!!.seriesShimmer.visibility = View.VISIBLE
        binding!!.mShimmer.colorsData = ColorsHelper
        binding!!.mShimmer.seriesShimmerScroll1.isEnabled = false
        binding!!.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding!!.llParent.visibility = View.GONE
        binding!!.noConnectionLayout.visibility = View.GONE
        binding!!.mShimmer.sfShimmer1.startShimmer()
        binding!!.mShimmer.sfShimmer2.startShimmer()
        binding!!.mShimmer.sfShimmer3.startShimmer()
        binding!!.mShimmer.flBackIconImage.bringToFront()
        binding!!.mShimmer.flBackIconImage.setOnClickListener { onBackPressed() }
    }

    private fun stopShimmer() {
        binding!!.seriesShimmer.visibility = View.GONE
        binding!!.llParent.visibility = View.VISIBLE
        binding!!.noConnectionLayout.visibility = View.GONE
        binding!!.mShimmer.sfShimmer1.startShimmer()
        binding!!.mShimmer.sfShimmer2.startShimmer()
        binding!!.mShimmer.sfShimmer3.startShimmer()
    }


    override fun onResume() {
        super.onResume()
        isLoggedOut = false
        dismissLoading(binding!!.progressBar)
        if (!isLoggedIn && preference!!.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
            seasonTabFragment!!.seasonAdapter = null
            refreshDetailPage(assetId)
        }
        setBroadcast()
        if (preference != null && userInteractionFragment != null) {
            AppCommonMethod.callSocialAction(preference!!, userInteractionFragment)
        }
    }

    private fun setBroadcast() {
        receiver =
            NetworkChangeReceiver()
        val filter = IntentFilter()
        filter.addAction("android.net.conn.CONNECTIVITY_CHANGE")
        filter.addAction("android.net.wifi.WIFI_STATE_CHANGED")
        filter.addAction("android.net.wifi.STATE_CHANGE")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            <EMAIL>(receiver, filter, RECEIVER_NOT_EXPORTED)
        } else {
            <EMAIL>(receiver, filter)

        }
        setConnectivityListener(this)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if (intent.hasExtra(AppConstants.BUNDLE_ASSET_BUNDLE)) {
            val extras = intent.extras
            if (extras != null) {
                assetId = intent.extras!!.getBundle(AppConstants.BUNDLE_ASSET_BUNDLE)
                    ?.getInt(AppConstants.BUNDLE_ASSET_ID)!!

                refreshDetailPage(assetId)
            }
        } else {
            throw IllegalArgumentException("Activity cannot find  extras " + "Search_Show_All")
        }
    }

    private fun refreshDetailPage(assestId: Int) {
        assetId = assestId
        if (preference!!.appPrefHasSelectedId) {
            preference!!.appPrefHasSelectedId = false
            val tempId = preference!!.appPrefSelectodSeasonId
            if (tempId != -1) {
                preference!!.appPrefSelectodSeasonId = -1
            }
        }

        callBinding()
    }

    fun openLoginPage(message: String?) {
        preference!!.appPrefJumpTo =  AppConstants.episode
        preference!!.appPrefJumpBack = true
        preference!!.appPrefIsEpisode = true
        preference!!.appPrefJumpBackId = assetId
        preference!!.appPrefHasSelectedId = true
        ActivityLauncher.getInstance()
            .loginActivity(this@EpisodeActivity, ActivityLogin::class.java, "")
    }

    fun uiInitialisation() {
        callShimmer()
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        setupUI(binding!!.llParent)
        preference!!.appPrefAssetId = assetId
        isLogin = preference!!.appPrefLoginStatus
        token = preference!!.appPrefAccessToken
        showLoading(binding!!.progressBar, false)
        binding!!.noConnectionLayout.visibility = View.GONE
        episodeDetails

    }

    private val episodeDetails: Unit
        get() {
                railInjectionHelper!!.getSeriesDetailsV2(assetId.toString(), false).observe(this@EpisodeActivity) { response: ResponseModel<*>? ->
                    if (response != null) {
                        if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {
                        } else if (response.status.equals(
                                APIStatus.SUCCESS.name,
                                ignoreCase = true
                            )
                        ) {
                            if (response.baseCategory != null) {
                                val enveuCommonResponse = response.baseCategory as RailCommonData
                                if (enveuCommonResponse.enveuVideoItemBeans.size > 0 && enveuCommonResponse.enveuVideoItemBeans[0] != null) {
                                    keyword = enveuCommonResponse.enveuVideoItemBeans[0].display_tags
                                    videoDetails = enveuCommonResponse.enveuVideoItemBeans[0]

                                    if (videoDetails!!.assetType.equals(AppConstants.CUSTOM)) {
                                        getAppLevelJsonData(seriesDetailBean!!.customType)
                                    } else {
                                        getAppLevelJsonData(videoDetails!!.videoDetails.videoType)
                                    }


                                    try {
                                        if (videoDetails!!.seriesCustomData != null && !videoDetails!!.seriesCustomData.episode_series_id.equals("")) {
                                            seriesId = videoDetails!!.seriesCustomData.episode_series_id.id
                                        }

                                    } catch (_:Exception) {

                                    }

                                    try {
                                        if (videoDetails!!.seriesCustomData !=null && !videoDetails!!.seriesCustomData.trailer_reference_id.equals("")) {
                                            videoDetails!!.seriesCustomData.trailer_reference_id?.let { getTrailer(it) }
                                        }
                                    } catch (e:Exception) {
                                    }

                                    setTabs()
                                    id = enveuCommonResponse.enveuVideoItemBeans[0].id
                                    setUserInteractionFragment(id)
                                    parseVideoDetails(videoDetails)
                                }
                            }
                        } else if (response.status.equals(
                                APIStatus.ERROR.name,
                                ignoreCase = true
                            )
                        ) {
                            if (response.errorModel.errorCode != 0) {
                                stopShimmer()
                            }
                        } else if (response.status.equals(
                                APIStatus.FAILURE.name,
                                ignoreCase = true
                            )
                        ) {
                            stopShimmer()
                        }
                    }
                }
        }


    private fun parseVideoDetails(videoDetails: EnveuVideoItemBean?) {
        dismissLoading(binding!!.progressBar)
        ImageHelper.getInstance(this).loadListImage(binding!!.playerImage, AppCommonMethod.getListLDSImage(videoDetails!!.posterURL, this))
        setUI(videoDetails)
    }


    private fun setUserInteractionFragment(id: Int) {
        val transaction = supportFragmentManager.beginTransaction()
        val args = Bundle()
        args.putInt(AppConstants.BUNDLE_ASSET_ID, id)
        args.putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
        userInteractionFragment = UserInteractionFragment()
        userInteractionFragment!!.arguments = args
        transaction.replace(R.id.fragment_user_interaction, userInteractionFragment!!)
        transaction.addToBackStack(null)
        transaction.commit()
    }

    private fun setUI(responseDetailPlayer: EnveuVideoItemBean?) {
        setDetails(responseDetailPlayer)
    }

    private fun logoutUser() {
        isLoggedOut = false
        if (AppConstants.UserStatus.Login.toString().equals(isLogin, ignoreCase = true)
            && CheckInternetConnection.isOnline(this@EpisodeActivity)
        ) {
            preference?.let { clearCredientials(it) }
            hitApiLogout(this@EpisodeActivity, preference!!.appPrefAccessToken)
        }
    }



    private fun setDetails(responseDetailPlayer: EnveuVideoItemBean?) {
        if (responseDetailPlayer!!.assetType != null && responseDetailPlayer.duration > 0) {
            val durationInMinutes =
                AppCommonMethod.stringForTime(responseDetailPlayer.duration) + " " + stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_minutes.toString(),
                    getString(R.string.popup_minutes)
                )
            setCustomFields(responseDetailPlayer, durationInMinutes)
        } else {
            setCustomFields(responseDetailPlayer, "")
        }
        if (responseDetailPlayer.description != null && responseDetailPlayer.description.equals("", ignoreCase = true)) {
            binding!!.metaDetails.descriptionText.visibility = View.GONE
        }
        binding!!.responseApi = responseDetailPlayer
        if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) addToWatchHistory()
    }

    private fun setCustomFields(videoItemBean: EnveuVideoItemBean?, duration: String) {
        try {

            if (mediaConfig!!.detailPage.features.watchNowEnabled) {
                binding!!.metaDetails.playButton.visibility = View.VISIBLE
            } else {
                binding!!.metaDetails.playButton.visibility = View.GONE
            }

            if (videoItemBean?.title != null) {
                binding!!.metaDetails.tvTitle.text = videoItemBean.title
            } else {
                binding!!.metaDetails.tvTitle.visibility = View.GONE
            }
            if (videoItemBean!!.description != null) {
                binding!!.metaDetails.descriptionText.text = videoItemBean.description
            } else {
                binding!!.metaDetails.descriptionText.visibility = View.GONE
            }

            setTextOrHide(binding!!.metaDetails.duration, duration)

        } catch (e: Exception) {
            Logger.w(e)
        }
    }

    private fun addToWatchHistory() {
        val bookmarkingViewModel = ViewModelProvider(this)[BookmarkingViewModel::class.java]
        bookmarkingViewModel.addToWatchHistory(token, assetId)
    }

    private fun noConnectionLayout() {
        binding!!.llParent.visibility = View.GONE
        binding!!.noConnectionLayout.visibility = View.VISIBLE
        binding!!.connection.retryTxt.setOnClickListener { connectionObserver() }
    }

    private var receiver: NetworkChangeReceiver? = null
    override fun onBackPressed() {
        super.onBackPressed()
        if (preference!!.appPrefJumpBack) {
            preference!!.appPrefJumpBackId = 0
            preference!!.appPrefVideoPosition = 0.toString()
            preference!!.appPrefJumpBack = false
            preference!!.appPrefGotoPurchase = false
            preference!!.appPrefIsEpisode = false
        }
        preference!!.appPrefAssetId = 0
        AppCommonMethod.seasonId = -1
        val orientation = this.resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            finish()
        }
    }

    override fun onPause() {
        super.onPause()
        if (receiver != null) {
            unregisterReceiver(receiver)
            if (NetworkChangeReceiver.connectivityReceiverListener != null) NetworkChangeReceiver.connectivityReceiverListener =
                null
        }
    }

    override fun onDestroy() {
        preference!!.appPrefAssetId = 0
        preference!!.appPrefJumpTo = ""
        preference!!.appPrefBranchIo = false
        AppCommonMethod.seasonId = -1
        super.onDestroy()
    }

    override fun onFinishDialog() {
        if (isLoggedOut) logoutUser()
        if (isPlayerError) {
            binding!!.playerImage.visibility = View.VISIBLE
            ImageHelper.getInstance(this@EpisodeActivity)
                .loadListImage(binding!!.playerImage, videoDetails!!.posterURL)
            isPlayerError = false
        } else {
            finish()
        }
    }

    private fun setConnectivityListener(listener: NetworkChangeReceiver.ConnectivityReceiverListener?) {
        NetworkChangeReceiver.connectivityReceiverListener = listener
    }

    override fun onNetworkConnectionChanged(isConnected: Boolean) {}
    override fun onAudioFocusChange(focusChange: Int) {
        val manager = this.getSystemService(AUDIO_SERVICE) as AudioManager
        if (manager.isMusicActive) {
            when (focusChange) {
                AudioManager.AUDIOFOCUS_GAIN -> Logger.i("AUDIOFOCUS_GAIN")
                AudioManager.AUDIOFOCUS_LOSS -> Logger.e("AUDIOFOCUS_LOSS")
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> Logger.e("AUDIOFOCUS_LOSS_TRANSIENT")
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> Logger.e("AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK")
                else -> {}
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        try {
            Logger.d("configuration changed: " + newConfig.orientation)
            Logger.d("language: " + LanguageLayer.getCurrentLanguageCode())
            val isCastConnected = false
            if (!isCastConnected) {
                super.onConfigurationChanged(newConfig)
                if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    setupLandscapeView()
                } else {
                    setupPortraitView()
                }
            }
        } catch (e: Exception) {
            Logger.w(e)
        }
    }

    private fun setupPortraitView() {
        val params = binding!!.playerFrame.layoutParams as ConstraintLayout.LayoutParams
        params.width = ConstraintLayout.LayoutParams.MATCH_CONSTRAINT
        params.height = ConstraintLayout.LayoutParams.MATCH_CONSTRAINT
        binding!!.playerFrame.layoutParams = params
        val set = ConstraintSet()
        set.clone(binding!!.llParent)
        set.connect(
            R.id.player_frame,
            ConstraintSet.END,
            ConstraintSet.PARENT_ID,
            ConstraintSet.END
        )
        set.connect(
            R.id.player_frame,
            ConstraintSet.START,
            ConstraintSet.PARENT_ID,
            ConstraintSet.START
        )
        set.connect(
            R.id.player_frame,
            ConstraintSet.TOP,
            ConstraintSet.PARENT_ID,
            ConstraintSet.TOP
        )
        set.setDimensionRatio(R.id.player_frame, "H,16:9")
        set.applyTo(binding!!.llParent)
        binding!!.rootScroll.visibility = View.VISIBLE
    }

    private fun setupLandscapeView() {
        binding!!.rootScroll.visibility = View.GONE
        val params = binding!!.playerFrame.layoutParams as ConstraintLayout.LayoutParams
        params.width = ConstraintLayout.LayoutParams.MATCH_PARENT
        params.height = ConstraintLayout.LayoutParams.MATCH_PARENT
        binding!!.playerFrame.layoutParams = params
    }

    override fun railItemClick(item: RailCommonData, position: Int) {
        AppCommonMethod.redirectionLogic(
            this,
            item,
            position, "","",""
        )
    }

    override fun moreRailClick(data: RailCommonData, position: Int, multilingualTitle: String) {
        if (data.screenWidget != null && data.screenWidget.contentID != null) {
            val playListId = data.screenWidget.contentID
            var screenName: String? = ""
            if (data.screenWidget.name != null) {
                screenName = data.screenWidget.name as String?
            }
            val intent = Intent(this, ListActivity::class.java)
            intent.putExtra("playListId", playListId)
            intent.putExtra("title", screenName)
            intent.putExtra("flag", 0)
            intent.putExtra("shimmerType", 0)
            intent.putExtra("baseCategory", data.screenWidget)
            startActivityForResult(intent, 1001)
        }
    }

    private var nextEpisode: EnveuVideoItemBean? = null
    private var seasonEpisodesList: MutableList<EnveuVideoItemBean>? = null
    fun episodesList(seasonEpisodes: List<EnveuVideoItemBean>?) {
        try {
            if (seasonEpisodes != null) {
                seasonEpisodesList = ArrayList()
                seasonEpisodesList?.addAll(seasonEpisodes)
            }
        } catch (ex: Exception) {
            Logger.w(ex)
        }
    }

    fun showSeasonList(list: ArrayList<SelectedSeasonModel>) {
        binding!!.transparentLayout.visibility = View.VISIBLE
        val listAdapter = SeasonListAdapter(list)
        val builder = AlertDialog.Builder(this@EpisodeActivity)
        val inflater = LayoutInflater.from(this@EpisodeActivity)
        val content = inflater.inflate(R.layout.season_custom_dialog, null)
        builder.setView(content)
        val mRecyclerView = content.findViewById<RecyclerView>(R.id.my_recycler_view)
        val imageView = content.findViewById<ImageView>(R.id.close)
        imageView.setOnClickListener {
            alertDialog!!.cancel()
            binding!!.transparentLayout.visibility = View.GONE
        }
        mRecyclerView.layoutManager = LinearLayoutManager(this@EpisodeActivity)
        mRecyclerView.adapter = listAdapter
        alertDialog = builder.create()
        alertDialog?.window!!.setBackgroundDrawable(
            ContextCompat.getDrawable(this@EpisodeActivity, R.color.transparent)
        )
        alertDialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        if (alertDialog?.window != null) alertDialog?.window!!.attributes.windowAnimations =
            R.style.SlidingDialogAnimation
        alertDialog?.show()
        val lWindowParams = WindowManager.LayoutParams()
        lWindowParams.copyFrom(alertDialog?.window!!.attributes)
        lWindowParams.width = ViewGroup.LayoutParams.MATCH_PARENT // this is where the magic happens
        lWindowParams.height = WindowManager.LayoutParams.MATCH_PARENT
        alertDialog?.window!!.attributes = lWindowParams
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun onActionBtnClicked() {
        if (isUserNotVerify) {
            ActivityLauncher.getInstance()
                .goToEnterOTP(this, EnterOTPActivity::class.java, "DetailPage")
        }
        if (isUserNotEntitle) {
            ActivityLauncher.getInstance().goToPlanScreen(this@EpisodeActivity, ActivitySelectSubscriptionPlan::class.java,
                "")
        }
    }

    override fun onCancelBtnClicked() {

    }

    internal inner class SeasonListAdapter(
        private val list: ArrayList<SelectedSeasonModel>
    ) : RecyclerView.Adapter<SeasonListAdapter.ViewHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.all_season_listing, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.season.text = resources.getString(R.string.season) + " " + list[position].list.seriesCustomData.season_number
            if (list[position].isSelected) {
                holder.season.setTextColor(ContextCompat.getColor(holder.season.context,R.color.selected_indicator_color))
                val boldTypeface = Typeface.defaultFromStyle(Typeface.BOLD)
                holder.season.typeface = boldTypeface
            } else {
                holder.season.setTextColor(ContextCompat.getColor(holder.season.context, R.color.series_detail_description_text_color))
                val boldTypeface = Typeface.defaultFromStyle(Typeface.NORMAL)
                holder.season.typeface = boldTypeface
            }
            holder.season.setOnClickListener {
                alertDialog!!.cancel()
                binding!!.transparentLayout.visibility = View.GONE
                if (seasonTabFragment != null) {
                    seasonTabFragment!!.updateTotalPages()
                    seasonTabFragment!!.seasonAdapter = null
                    seasonTabFragment!!.selectedSeason = list[position].selectedId
                    showLoading(binding!!.progressBar, true)
                    seasonTabFragment!!.episodesListApi(list[position].list.id,list[position].list.seriesCustomData.season_number.toInt())
                }
            }
        }

        override fun getItemCount(): Int {
            return list.size
        }

        internal inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            var season: TextView
            init {
                season = itemView.findViewById(R.id.season_name)
            }
        }
    }

    override fun onUserLeaveHint() {
        super.onUserLeaveHint()
        supportsPiPMode()
    }

    private fun supportsPiPMode() {}
    private var isPlayerError = false


    private fun hitUserProfileApi() {
        registrationLoginViewModel = ViewModelProvider(this)[RegistrationLoginViewModel::class.java]
        registrationLoginViewModel!!.hitUserProfile(
            this@EpisodeActivity,
            preference!!.appPrefAccessToken
        ).observe(this@EpisodeActivity) { userProfileResponse ->
            dismissLoading(binding!!.progressBar)
            if (userProfileResponse != null) {
                if (userProfileResponse.data != null) {
                }
                if (userProfileResponse.status) {
                } else {
                    if (userProfileResponse.responseCode == 4302) {
                        clearCredientials(preference!!)
                        isLoggedIn = false
                    }
                }
            }
        }
    }
}