package com.enveu.activities.detail.ui

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.enveu.R
import com.enveu.baseModels.BaseActivity
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.databinding.DetailScreenBinding
import com.enveu.epg.EPGSchedulesFragment
import com.enveu.epg.EPGUtils
import com.enveu.epg.EPGViewModel
import com.enveu.epg.models.EPGItem
import com.enveu.epg.models.Program
import com.enveu.fragments.player.ui.UserInteractionFragment
import com.enveu.jwplayer.player.LivePlayerActivity
import com.enveu.utils.Constants
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show
import com.google.gson.Gson
import com.npaw.youbora.lib6.extensions.putBoolean
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.getValue

class LiveLinearDetailsActivity : BaseActivity() {
    private lateinit var binding: DetailScreenBinding
    private var userInteractionFragment: UserInteractionFragment? = null
    private var playOutData:EnveuVideoItemBean?= EnveuVideoItemBean()
    private var isFromEpg = false
    private var isFromChannelClick = false
    private var channelId = ""
    private val viewModel: EPGViewModel by viewModels()
    private var showId = ""
    private var epgItem: EPGItem? = null
    private var mediaType: String? = null
    private var assetId:Int? = 0
    private var epgSchedulesFragment:EPGSchedulesFragment? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.detail_screen)
        setUI()
    }

    private fun parsePlayOutDataToEPGCase(){
        playOutData?.assetType = epgItem?.mediaContent?.contentType
        playOutData?.contentSlug = epgItem?.mediaContent?.contentSlug
        playOutData?.contentSlug = epgItem?.mediaContent?.contentSlug
        playOutData?.title = epgItem?.mediaContent?.title
        playOutData?.description = epgItem?.mediaContent?.description
        val item = epgItem?.schedules?.find { it.id == showId }
        item?.let {
            playOutData?.posterURL = it.program?.images?.get(0)?.imageContent?.src
        }
        playOutData?.id = epgItem?.mediaContent?.id!!
    }

    private fun setUI() {
        val arguments = intent.extras
        isFromEpg = arguments?.getString(AppConstants.FROM_REDIRECTION)?.equals(AppConstants.EPG_FRAGMENT)?:false
        channelId = arguments?.getString(AppConstants.EPG_CHANNEL_ID) ?: ""
        showId = arguments?.getString(AppConstants.EPG_SHOW_ID) ?: ""
        isFromChannelClick = arguments?.getBoolean(AppConstants.IS_FROM_CHANNEL_CLICK) == true
        val epgJson = arguments?.getString(AppConstants.EPG_ITEM)
        epgItem = Gson().fromJson(epgJson, EPGItem::class.java)

        if (epgItem == null){
            viewModel?.callEPGData(startDate = EPGUtils.startTime, endDate = EPGUtils.endTime)
            viewModel?.epgItems?.observe(this) {
                epgItem = it.find { it.mediaContent?.id == epgItem?.mediaContent?.id }
            }
        }
        binding.backButtonIcon.let { rotateImageLocaleWise(it) }
        binding.metaDetails.tvTitle.text = playOutData?.title
        binding.metaDetails.descriptionText.text = playOutData?.description
        binding.sepratorLine.show()
        parsePlayOutDataToEPGCase()
        if (isFromEpg){
            setEPGDataAndUI()
        }
        binding.backButton.setOnClickListener { finish() }
        binding.metaDetails.playButton.setOnClickListener {
            if (epgItem?.mediaContent?.liveContent?.liveType?.equals(Constants.LINEAR_24_7) == true) {
                val playerUrl = epgItem?.mediaContent?.liveContent?.externalUrl
                if (!playerUrl.isNullOrEmpty()){
                    Intent(this, LivePlayerActivity::class.java).also {
                        it.putExtra(Constants.EXTERNAL_REF_ID, epgItem?.mediaContent?.externalRefId)
                        it.putExtra(Constants.PLAYER_URL, playerUrl)
                        it.putExtra(Constants.EPG_ITEM, Gson().toJson(epgItem))
                        startActivity(it)
                    }
                }
            }else if (epgItem?.mediaContent?.liveContent?.liveType?.equals(Constants.PLAY_OUT) == true){
                val playerUrl = epgItem?.mediaContent?.liveContent?.externalUrl
                if (!playerUrl.isNullOrEmpty()){
                    Intent(this, LivePlayerActivity::class.java).also {
                        it.putExtra(Constants.EXTERNAL_REF_ID, epgItem?.mediaContent?.externalRefId)
                        it.putExtra(Constants.PLAYER_URL, playerUrl)
                        it.putExtra(Constants.EPG_ITEM, Gson().toJson(epgItem))
                        startActivity(it)
                    }
                }
            }
        }
        setUserInteractionFragment()
    }

    private fun setUserInteractionFragment() {
        val transaction = supportFragmentManager.beginTransaction()
        val args = Bundle()
        args.putInt(AppConstants.BUNDLE_ASSET_ID, playOutData?.id?:-1)
        args.putString(AppConstants.BUNDLE_MEDIA_TYPE, playOutData?.assetType)
        args.putSerializable(AppConstants.BUNDLE_SERIES_DETAIL, playOutData)
        args.putBoolean(Constants.HIDE_WATCHLIST_ICON, true)
        args.putBoolean(AppConstants.IS_FROM_CHANNEL_CLICK, isFromChannelClick)
        userInteractionFragment = UserInteractionFragment()
        userInteractionFragment?.arguments = args
        transaction.replace(R.id.fragment_user_interaction, userInteractionFragment!!)
        transaction.commit()
    }

    private fun setEPGDataAndUI() {
        binding.tabItemText.text = getString(R.string.detail_page_schedule_title)
        val epgSchedule = epgItem?.schedules?.find { it.id == showId }
        epgSchedule?.let { it ->
            it?.program?.let {
                setEPGMetaFromProgram(it)
            }
            binding.metaDetails.actorsTextView.visibility = View.GONE
            binding.metaDetails.directorTextView.visibility = View.GONE
        }
        if (isFromChannelClick){
            epgItem?.mediaContent?.let {
                binding.tvChannelName.text = it.title
                val image = it.images?.find { images -> images.imageContent?.imageType == Constants.SIXTEEN_INTO_NINE }
                ImageHelper.getInstance(this).loadCircleImageTo(binding.ivChannel, image?.imageContent?.src?.let { it1 -> AppCommonMethod.getListLDSImage(it1,this) })
                binding.llChannelDetail.visibility = View.VISIBLE
            }
        }
        setScheduleDetailFragment()
    }

    private fun setEPGMetaFromProgram(program: Program?) {
        program?.title?.let { title->
            binding.metaDetails.tvTitle.text = title
        } ?: { binding.metaDetails.tvTitle.visibility = View.GONE }
        program?.description?.let { desc ->
            binding.metaDetails.descriptionText.maxLines = 4
            binding.metaDetails.descriptionText.ellipsize = TextUtils.TruncateAt.END
            binding.metaDetails.descriptionText.text = desc
        } ?: { binding.metaDetails.descriptionText.visibility = View.GONE }
        mediaType = program?.customContent?.customType ?: ""
        assetId = program?.id
        program?.images?.get(0)?.imageContent?.src?.let {
            ImageHelper.getInstance(binding.playerImage.context).loadImageTo(binding.playerImage,
                AppCommonMethod.getListLDSImage(it,this)
            )
        }
    }

    @SuppressLint("CommitTransaction")
    private fun setScheduleDetailFragment() {
        epgSchedulesFragment = EPGSchedulesFragment()
        val bundle = Bundle()
        bundle.putString(AppConstants.EPG_CHANNEL_ID, channelId)
        bundle.putString(AppConstants.EPG_SHOW_ID, showId)
        epgSchedulesFragment?.let {
            it.arguments = bundle
            supportFragmentManager.beginTransaction().replace(R.id.schedule_fragment, it).commit()
            lifecycleScope.launch {
                delay(100)
                it.setEPGData(epgItem)
            }
        }
    }
}