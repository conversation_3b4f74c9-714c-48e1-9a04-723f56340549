package com.enveu.activities.detail.ui

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.enveu.R
import com.enveu.activities.detail.ContentMetaData
import com.enveu.activities.detail.viewModel.DetailViewModel
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.ActivitySelectSubscriptionPlan
import com.enveu.activities.usermanagment.ui.EnterOTPActivity
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaConfig
import com.enveu.appLevelModel.MediaInfo
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.databinding.MatchDetailScreenBinding
import com.enveu.enums.EVENT_STATUS
import com.enveu.enums.StreamStatus
import com.enveu.fragments.detailPageTabFragment.EnveuTabFragment
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstance
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstanceWithCancelBtn
import com.enveu.fragments.player.ui.UserInteractionFragment
import com.enveu.jwplayer.player.PlayerActivity
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppCommonMethod.Companion.formatTimestamp
import com.enveu.utils.commonMethods.AppCommonMethod.Companion.getStreamStatus
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper

class MatchDetailActivity : BaseBindingActivity<MatchDetailScreenBinding?>(),
    CommonDialogFragment.EditDialogListener {
    private var assetId = 0
    private var seasonNumber = 0
    private var matchEventId = 0
    private var preference: KsPreferenceKeys? = null
    private var isLogin: String? = null
    private var shimmerCounter = 0
    private var videoDetails: EnveuVideoItemBean? = null
    private var matchDetails: EnveuVideoItemBean? = null
    private var enveuTabFragment : EnveuTabFragment?=null
    private var isGeoBlocking = false
    private var newIntentCall = false
    private var isLive = false
    private var seriesTittle = ""
    @JvmField
    var isSeasonData = false
    private var userInteractionFragment: UserInteractionFragment? = null
    private var keyword: String? = ""
    private var combinedStreamingStatus: String? = ""
    private var isUserVerified: String? = null
    private var isUserNotVerify = false
    private var isUserNotEntitle = false
    private var isLoggedIn = false
    private var playbackUrl: String? = null
    private var viewModel: DetailViewModel? = null
    private var token: String? = null
    private var isPremium = false
    private var refId: String? = ""
    private var externalUrl: String? = ""
    private var tittle = ""
    private var message = ""
    private var posterUrl = ""
    private var assetType = ""
    private var sku = ""
    private var startOn = ""
    private var mediaType = ""
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    override fun inflateBindingLayout(inflater: LayoutInflater): MatchDetailScreenBinding {
        return MatchDetailScreenBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding?.backImage?.let { rotateImageLocaleWise(it) }
        inItFun()
        getIntentValue()
        setOrientation()
        checkGeoBlocking()
        parserColor()
        setClicks()
        onSeriesCreate()

    }


    private fun inItFun(){
        window.setBackgroundDrawableResource(R.color.app_bg_color)
        shimmerCounter = 0
        preference = KsPreferenceKeys.getInstance()
        if (preference?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
        }
        isUserVerified = preference?.isVerified
        token = preference!!.appPrefAccessToken
        viewModel = ViewModelProvider(this)[DetailViewModel::class.java]
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]


    }

    private fun getIntentValue(){
        assetId = intent.getIntExtra("Id", 0)
    }

    private fun parserColor() {
        binding!!.stringData = stringsHelper
        binding!!.colorsData = colorsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
        binding!!.colorsData = colorsHelper
        binding!!.stringData = stringsHelper
    }

    private fun setOrientation() {
        window.setBackgroundDrawableResource(R.color.app_bg_color)
        val orientation = this.resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
         //   setupPortraitView()
        } else {
         //   setupLandscapeView()
        }
    }

    override fun onPause() {
        dismissLoading(binding!!.progressBar)
        super.onPause()
    }

    private fun onSeriesCreate() {
        if (shimmerCounter == 0) {
            callShimmer()
        }
        connectionObserver()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        shimmerCounter = 0
        setupUI(binding!!.llParent)
        assetId = intent.getIntExtra("Id", 0)
        newIntentCall = true
        onSeriesCreate()
    }





    private fun callShimmer() {
        shimmerCounter = 1
        binding!!.seriesShimmer.visibility = View.VISIBLE
        binding!!.mShimmer.colorsData = colorsHelper
        binding!!.mShimmer.seriesShimmerScroll1.isEnabled = false
        binding!!.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding!!.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding!!.noConnectionLayout.visibility = View.GONE
        binding!!.mShimmer.sfShimmer1.startShimmer()
        binding!!.mShimmer.sfShimmer2.startShimmer()
        binding!!.mShimmer.sfShimmer3.startShimmer()
        binding!!.mShimmer.flBackIconImage.bringToFront()
        binding!!.mShimmer.flBackIconImage.setOnClickListener { onBackPressed() }
    }

    fun stopShimmer() {
        if (isSeasonData) {
            isSeasonData = false
            binding!!.seriesShimmer.visibility = View.GONE
            binding!!.llParent.visibility = View.VISIBLE
            binding!!.noConnectionLayout.visibility = View.GONE
            binding!!.mShimmer.sfShimmer1.startShimmer()
            binding!!.mShimmer.sfShimmer2.startShimmer()
            binding!!.mShimmer.sfShimmer3.startShimmer()

        }
    }

    private fun modelCall() {
        binding!!.noConnectionLayout.visibility = View.GONE
        binding!!.player.visibility = View.VISIBLE
        binding!!.playerFooter.visibility = View.VISIBLE
        binding!!.flBackIconImage.visibility = View.VISIBLE
        binding!!.backImage.bringToFront()
        binding!!.flBackIconImage.bringToFront()
        isLogin = preference?.appPrefLoginStatus
    }

    private fun connectionObserver() {
        connectionValidation(NetworkConnectivity.isOnline(this))
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (java.lang.Boolean.TRUE == aBoolean) {
            getAssetDetail
        } else {
            noConnectionLayout()
        }
    }


    override fun onResume() {
        super.onResume()
        dismissLoading(binding!!.progressBar)
        if (NetworkConnectivity.isOnline(this)) {
            setUserInteractionFragment(assetId)
        } else {
            noConnectionLayout()
        }
        if (preference != null && userInteractionFragment != null) {
            AppCommonMethod.callSocialAction(preference!!, userInteractionFragment)
        }
    }


    private fun getLiveEventMatch(){
        railInjectionHelper!!.getLiveEventMatch(assetId,0,20)
            .observe(this@MatchDetailActivity) { assetResponse: ResponseModel<*>? ->
                if (assetResponse != null) {
                    if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                        //Do nothing
                    } else if (assetResponse.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                        parseLiveEventMatch(assetResponse)
                    } else if (assetResponse.status.equals(APIStatus.ERROR.name,ignoreCase = true)) {
                        setUiComponents(videoDetails)
                        if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.popup_something_went_wrong)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        }
                    } else if (assetResponse.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                        setUiComponents(videoDetails)
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
                }
            }
    }

    private fun parseLiveEventMatch(assetResponse: ResponseModel<*>) {
        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
        if (enveuCommonResponse.enveuVideoItemBeans.size > 0) {
            matchDetails = enveuCommonResponse.enveuVideoItemBeans[0]
            if (matchDetails!=null) {
                matchEventId = matchDetails!!.id
                isPremium = matchDetails!!.isPremium
                refId = matchDetails!!.externalRefId
                Log.d("liveData", "parseLiveEventMatch: "+ matchDetails!!.liveContent.streamSchedule.toString())
                if (matchDetails!!.liveContent.streamSchedule?.startsAt!=null) {
                    startOn = formatTimestamp(matchDetails!!.liveContent.streamSchedule?.startsAt!!)
                }
                externalUrl = matchDetails!!.liveContent.externalUrl

                if (matchDetails!!.assetType.equals(AppConstants.CUSTOM,ignoreCase = true)) {
                    this.mediaType = matchDetails!!.customType
                } else if (matchDetails!!.assetType.equals(AppConstants.LIVE,ignoreCase = true)) {
                    this.mediaType = matchDetails!!.liveContent.liveType!!
                    isLive = true
                } else {
                    this.mediaType = matchDetails!!.videoDetails.videoType!!
                }
            }
        }
        checkLiveStatus(matchEventId)
    }

    private fun checkGeoBlocking() {
        try {
            viewModel!!.getGeoBlocking(assetId.toString())
                .observe(this@MatchDetailActivity) { response ->
                    if (response != null && response.data != null) {
                        if (response.data.isIsBlocked) {
                            isGeoBlocking = true
                        }
                    } else {
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
                }
        } catch (_: Exception) {

        }

    }

    private fun checkLiveStatus(matchEventId:Int) {
        viewModel!!.checkLiveStatus(matchEventId).observe(this@MatchDetailActivity) { response ->
                if (response?.data != null) {
                    Log.d("checkLiveStatus", "checkLiveStatus: ${response.data.toString()}")
                    val streamStatus = StreamStatus.valueOf(response.data!!.streamStatus !!)
                    val eventStatus = EVENT_STATUS.valueOf(response.data!!.eventStatus !!)
                    combinedStreamingStatus = getStreamStatus(streamStatus, eventStatus).toString()
                    parseReplayData()
                    message = getValueString(combinedStreamingStatus!!)
                    println("Combined Status: $combinedStreamingStatus")
                    setUiComponents(videoDetails)
                } else {
                    setUiComponents(videoDetails)
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )
                }
        }

    }

    private fun parseReplayData() {
        if (combinedStreamingStatus.equals(AppConstants.REPLAY)) {
            if (replayList!=null) {
                isPremium =  replayList!![0].isPremium
                refId =  replayList!![0].externalRefId
               externalUrl = replayList!![0].videoDetails.externalUrl
            }
        }
    }

    var railInjectionHelper: RailInjectionHelper? = null
    private val getAssetDetail: Unit
        get() {
            modelCall()
            railInjectionHelper!!.getAssetDetailsV2(assetId.toString(), this@MatchDetailActivity)
                .observe(this@MatchDetailActivity) { assetResponse: ResponseModel<*>? ->
                    if (assetResponse != null) {
                        if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                            //do nothing
                        } else if (assetResponse.status.equals(
                                APIStatus.SUCCESS.name,
                                ignoreCase = true
                            )
                        ) {
                            parseAssetDetails(assetResponse)
                        } else if (assetResponse.status.equals(
                                APIStatus.ERROR.name,
                                ignoreCase = true
                            )
                        ) {
                            if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {
                                commonDialog(
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                        getString(R.string.popup_error)
                                    ),
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                        getString(R.string.popup_something_went_wrong)
                                    ),
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                        getString(R.string.popup_continue)
                                    )
                                )
                            }
                        } else if (assetResponse.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.popup_something_went_wrong)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        }
                    }
                }
        }

    private fun parseAssetDetails(assetResponse: ResponseModel<*>) {
        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
        if (enveuCommonResponse.enveuVideoItemBeans.size > 0) {
            videoDetails = enveuCommonResponse.enveuVideoItemBeans[0]
            assetId = videoDetails?.id!!
            if (mediaType.equals(AppConstants.MATCHES,ignoreCase = true)) {
                getLiveEventMatch()
            } else {
                if (videoDetails!!.assetType.equals(AppConstants.CUSTOM,ignoreCase = true)) {
                    getAppLevelJsonData(videoDetails!!.customType)
                } else if (videoDetails!!.assetType.equals(AppConstants.LIVE,ignoreCase = true)) {
                    getAppLevelJsonData(videoDetails!!.liveContent.liveType!!)
                } else {
                    getAppLevelJsonData(videoDetails!!.videoDetails.videoType!!)
                }
                matchEventId = videoDetails!!.id
                externalUrl = videoDetails!!.liveContent.externalUrl
                checkLiveStatus(matchEventId)
            }
            setEnveuTabFragment()
            binding!!.descriptionText.ellipsize = TextUtils.TruncateAt.END
            stopShimmer()
            setUserInteractionFragment(assetId)
        }
    }


    private fun setEnveuTabFragment() {
         enveuTabFragment = EnveuTabFragment()
        val args = Bundle().apply {
            putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
            putInt(AppConstants.BUNDLE_SERIES_ID, assetId)
            putInt(AppConstants.BUNDLE_ASSET_ID, assetId)
            putInt(AppConstants.BUNDLE_SEASON_NUMBER, seasonNumber)
        }
        enveuTabFragment?.arguments=args
        supportFragmentManager.beginTransaction().replace(R.id.enveu_tab_fragment,enveuTabFragment!!).addToBackStack("null").commit()
    }

    private fun setUserInteractionFragment(id: Int) {
        val transaction = supportFragmentManager.beginTransaction()
        val args = Bundle()
        args.putInt(AppConstants.BUNDLE_ASSET_ID, id)
        args.putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
        args.putSerializable(AppConstants.BUNDLE_SERIES_DETAIL, videoDetails)
        userInteractionFragment = UserInteractionFragment()
        userInteractionFragment!!.arguments = args
        transaction.replace(R.id.fragment_user_interaction, userInteractionFragment!!)
        transaction.addToBackStack(null)
        transaction.commit()
    }

    private var mediaConfig: MediaConfig? = null
    private var featureList: FeatureFlagModel? = null

    private fun getAppLevelJsonData(mediaType: String) {
        this.mediaType = mediaType
        val mediaConfigInfo: MediaInfo? = AppConfigMethod.getMediaMappingByType(mediaType)
        mediaConfig = AppConfigMethod.getMediaConfigByType(mediaConfigInfo?.pageType.toString())
        featureList = AppConfigMethod.parseFeatureFlagList()

    }



    override fun onStop() {
        super.onStop()
        AppCommonMethod.isSeriesPage = false
    }

    private fun noConnectionLayout() {
        stopShimmer()
        binding!!.llParent.visibility = View.GONE
        binding!!.noConnectionLayout.visibility = View.VISIBLE
        binding!!.connection.retryTxt.setOnClickListener {
            callShimmer()
            connectionObserver()
        }
    }


    override fun onBackPressed() {
        finish()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            Logger.d("ORIENTATION_LANDSCAPE")
        } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            Logger.d("ORIENTATION_PORTRAIT")
        }
    }


    private fun setUiComponents(seriesResponse: EnveuVideoItemBean?) {
        if (seriesResponse != null) {
            binding!!.playlistItem = seriesResponse
            posterUrl = seriesResponse.posterURL
            ImageHelper.getInstance(this)
                .loadListImage(binding!!.sliderImage, seriesResponse.posterURL)
            binding!!.responseApi = seriesResponse.description.trim { it <= ' ' }
            if (seriesResponse.title != null) {
                seriesTittle = seriesResponse.title
                binding!!.tvTitle.text = seriesResponse.title
            } else {
                binding!!.tvTitle.visibility = View.GONE
            }

            if (combinedStreamingStatus.equals(AppConstants.STARTED,ignoreCase = true)) {
                binding!!.liveTag.visibility = View.VISIBLE
            } else if (combinedStreamingStatus.equals(AppConstants.END,ignoreCase = true)){
                binding!!.liveTag.visibility = View.GONE
                binding!!.playBtn.text = getText(R.string.watch_replay)
            }

            if (combinedStreamingStatus.equals(AppConstants.SCHEDULED,ignoreCase = true) || combinedStreamingStatus.equals(AppConstants.READY,ignoreCase = true) && startOn != ""
                ||combinedStreamingStatus.equals(AppConstants.NOT_ATTENDED,ignoreCase = true)) {
                binding!!.streamingDate.visibility = View.VISIBLE
                binding!!.tvDateVale.text = startOn
            } else {
                binding!!.streamingDate.visibility = View.GONE
            }


            if (seriesResponse.description != null) {
                binding!!.descriptionText.text = seriesResponse.description
            } else {
                binding!!.descriptionText.visibility = View.GONE
            }
        }
    }



    private fun getValueString(value: String): String {
        return when (value) {
            AppConstants.SCHEDULED -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.MATCH_START_ON.toString(), getString(R.string.MATCH_START_ON))
            AppConstants.NOT_ATTENDED -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.MATCH_NOT_ATTENDED.toString(), getString(R.string.MATCH_NOT_ATTENDED))
            AppConstants.READY -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.MATCH_READY.toString(), getString(R.string.MATCH_READY))
            AppConstants.END -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.MATCH_END.toString(), getString(R.string.MATCH_END))
            AppConstants.ERROR -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.STREAM_ERROR.toString(), getString(R.string.STREAM_ERROR))
            AppConstants.ABOUT_END -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.MATCH_ABOUT_END.toString(), getString(R.string.MATCH_ABOUT_END))
            else -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.STREAM_ERROR.toString(), getString(R.string.STREAM_ERROR))
        }
    }
    private var replayList: MutableList<EnveuVideoItemBean>? = null

    fun episodesList(seasonEpisodes: List<EnveuVideoItemBean>?) {
        try {
            if (seasonEpisodes != null) {
                replayList = ArrayList()
                replayList?.addAll(seasonEpisodes)
            }
        } catch (ex: Exception) {
            Logger.w(ex)
        }
    }

    private var resEntitle: ResponseEntitle? = null
    private fun setClicks() {
        binding!!.flBackIconImage.setOnClickListener { onBackPressed() }
        try {
            binding?.playButton?.setOnClickListener {
                if (combinedStreamingStatus.equals(AppConstants.SCHEDULED) || combinedStreamingStatus.equals(AppConstants.READY) || combinedStreamingStatus.equals(AppConstants.NOT_ATTENDED)) {
                    commonDialog(
                        message + startOn,
                        "",
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                            getString(R.string.ok)
                        )
                    )
                } else {
                    if (isLoggedIn) {
                        if (isGeoBlocking) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.geo_blocking_title.toString(),
                                    getString(R.string.geo_blocking_title)
                                ),
                                "",
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                                    getString(R.string.ok)
                                )
                            )
                        } else {
                            if (!isPremium) {
                                if (isUserVerified.equals("true", ignoreCase = true)) {
                                    if (null != externalUrl && !externalUrl.equals("", ignoreCase = true)) {
                                        playbackUrl = externalUrl
                                        startPlayer(playbackUrl)
                                    }
                                } else {
                                    isUserNotVerify = true
                                    commonDialogWithCancel(
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                                            getString(R.string.popup_notEntitled)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(),
                                            getString(R.string.popup_user_not_verify)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_purchase.toString(),
                                            getString(R.string.popup_purchase)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                                            getString(R.string.popup_cancel)
                                        )
                                    )
                                }
                            } else {
                                binding!!.progressBar.visibility = View.VISIBLE
                                viewModel!!.hitApiEntitlement(token, sku)
                                    .observe(this@MatchDetailActivity) { responseEntitle ->
                                        binding!!.progressBar.visibility = View.GONE
                                        if (responseEntitle != null && responseEntitle.data != null) {
                                            resEntitle = responseEntitle
                                            if (responseEntitle.data.entitled) {
                                                if (isUserVerified.equals("true", ignoreCase = true)) {
                                                    viewModel?.externalRefID(responseEntitle.data.accessToken, responseEntitle.data.sku)?.observe(this) {
                                                        playbackUrl = it?.data?.externalUrl
                                                        startPlayer(playbackUrl)
                                                    }
                                                } else {
                                                    isUserNotVerify = true
                                                    commonDialog(
                                                        "",
                                                        stringsHelper.stringParse(
                                                            stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(),
                                                            getString(R.string.popup_user_not_verify)
                                                        ),
                                                        stringsHelper.stringParse(
                                                            stringsHelper.instance()?.data?.config?.popup_verify.toString(),
                                                            getString(R.string.popup_verify)
                                                        )
                                                    )
                                                }
                                            } else {
                                                isUserNotEntitle = true
                                                commonDialogWithCancel(
                                                    stringsHelper.stringParse(
                                                        stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                                                        getString(R.string.popup_notEntitled)
                                                    ),
                                                    stringsHelper.stringParse(
                                                        stringsHelper.instance()?.data?.config?.popup_select_plan.toString(),
                                                        getString(R.string.popup_select_plan)
                                                    ),
                                                    stringsHelper.stringParse(
                                                        stringsHelper.instance()?.data?.config?.popup_purchase.toString(),
                                                        getString(R.string.popup_purchase)
                                                    ),
                                                    stringsHelper.stringParse(
                                                        stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                                                        getString(R.string.popup_cancel)
                                                    )
                                                )
                                            }
                                        } else {
                                            if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                                                preference?.let { it1 -> clearCredientials(it1) }
                                                ActivityLauncher.getInstance().loginActivity(
                                                    this@MatchDetailActivity,
                                                    ActivityLogin::class.java, ""
                                                )
                                            } else {
                                                commonDialog(
                                                    stringsHelper.stringParse(
                                                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                                        getString(R.string.popup_error)
                                                    ),
                                                    stringsHelper.stringParse(
                                                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                                        getString(R.string.popup_something_went_wrong)
                                                    ),
                                                    stringsHelper.stringParse(
                                                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                                        getString(R.string.popup_continue)
                                                    )
                                                )
                                            }
                                        }
                                    }
                            }
                        }

                    } else {
                        ActivityLauncher.getInstance()
                            .loginActivity(this@MatchDetailActivity, ActivityLogin::class.java,"")
                    }
                }

            }
        } catch (e: Exception) {
            Logger.e(e)
        }
    }

    private fun startPlayer(playbackUrl: String?) {
        val contentMetaData = ContentMetaData()
        contentMetaData.playBackUrl = playbackUrl
        contentMetaData.contentTitle = tittle
        contentMetaData.isLive = true
        contentMetaData.contentType = assetType
        contentMetaData.mediaType = mediaType
        contentMetaData.contentId = matchEventId
        val intent = Intent(this, PlayerActivity::class.java)
        intent.putExtra(AppConstants.CONTENT_META_DATA, contentMetaData)
        startActivity(intent)
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun onActionBtnClicked() {
        if (isUserNotVerify) {
            ActivityLauncher.getInstance()
                .goToEnterOTP(this, EnterOTPActivity::class.java, "DetailPage")
        }
        if (isUserNotEntitle) {
            ActivityLauncher.getInstance().goToPlanScreen(
                this@MatchDetailActivity,
                ActivitySelectSubscriptionPlan::class.java,
                ""
            )
        }
    }

    override fun onCancelBtnClicked() {

    }

}