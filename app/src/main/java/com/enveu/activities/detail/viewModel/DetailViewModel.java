package com.enveu.activities.detail.viewModel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.enveu.beanModel.JwDrmResponse.DrmResponse;
import com.enveu.beanModelV3.RelatedRailsCommonData;
import com.enveu.bean_model_v2_0.listAll.ListAllContent;
import com.enveu.bean_model_v2_0.videoDetailBean.liveDetailBean.LiveStatusResponse;
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory;
import com.enveu.client.bookmarking.bean.GetBookmarkResponse;
import com.enveu.networking.servicelayer.APIServiceLayer;
import com.google.gson.JsonObject;
import com.enveu.beanModel.drm.DRM;
import com.enveu.activities.layers.EntitlementLayer;
import com.enveu.beanModel.AssetHistoryContinueWatching.ResponseAssetHistory;
import com.enveu.beanModel.addComment.ResponseAddComment;
import com.enveu.beanModel.allComments.ResponseAllComments;
import com.enveu.beanModel.deleteComment.ResponseDeleteComment;
import com.enveu.beanModel.emptyResponse.ResponseEmpty;
import com.enveu.beanModel.entitle.ResponseEntitle;
import com.enveu.beanModel.isLike.ResponseIsLike;
import com.enveu.beanModel.isWatchList.ResponseContentInWatchlist;
import com.enveu.beanModel.like.ResponseAddLike;
import com.enveu.beanModel.responseModels.detailPlayer.ResponseDetailPlayer;
import com.enveu.beanModel.responseModels.landingTabResponses.CommonRailData;
import com.enveu.beanModel.responseModels.series.SeriesResponse;
import com.enveu.beanModel.responseModels.series.season.SeasonResponse;
import com.enveu.beanModel.watchList.ResponseWatchList;
import com.enveu.callbacks.apicallback.EntitlementCallBack;
import com.enveu.jwplayer.cast.PlayDetailResponse;
import com.enveu.repository.bookmarking.BookmarkingRepository;
import com.enveu.repository.detail.DetailRepository;
import com.enveu.repository.home.HomeFragmentRepository;
import com.enveu.utils.constants.AppConstants;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Objects;

public class DetailViewModel extends DetailBaseViewModel {
    // private Context context;
    final DetailRepository detailRepository;


    MutableLiveData<Boolean> isShowMore= new MutableLiveData<Boolean>();

    public DetailViewModel(@NonNull Application application) {
        super(application);
        detailRepository = DetailRepository.getInstance();
    }

    @Override
    public LiveData<List<BaseCategory>> getAllCategories() {

        return HomeFragmentRepository.getInstance().getCategories(AppConstants.HOME_ENVEU);
    }

    public LiveData<ResponseDetailPlayer> hitApiDetailPlayer(boolean check, String token, int videoId) {
        return detailRepository.hitApiDetailPlayer(check, token, videoId);
    }

    public LiveData<List<CommonRailData>> hitApiYouMayLike(int videoId, int page, int size) {
        return detailRepository.hitApiYouMayLike(videoId, page, size);
    }

    public LiveData<ResponseWatchList> hitApiAddWatchList(String token, JsonObject data) {
        return detailRepository.hitApiAddToWatchList(token, data);
    }


    public LiveData<ResponseEmpty> hitApiRemoveWatchList(String token, String data) {
        return detailRepository.hitApiRemoveFromWatchList(token, data);
    }


    public LiveData<ResponseContentInWatchlist> hitApiIsWatchList(String token, JsonObject data) {
        return detailRepository.hitApiIsToWatchList(token, data);
    }


    public LiveData<ResponseIsLike> hitApiIsLike(String token, JsonObject data) {
        return detailRepository.hitApiIsLike(token, data);
    }

    public LiveData<ResponseAddLike> hitApiAddLike(String token, JsonObject data) {
        return detailRepository.hitApiAddLike(token, data);
    }

    public LiveData<ResponseEmpty> hitApiUnLike(String token, JsonObject data) {
        return detailRepository.hitApiUnLike(token, data);
    }

    public LiveData<DRM> externalRefID (String token , String Sku){
      return  EntitlementLayer.getInstance().hitAPIForREfID(token, Sku);
    }

    public LiveData<String> getLicenseUrl (String token , String Sku, String playerId){
        return  EntitlementLayer.getInstance().hitAPIForLicenseUrl(token, Sku, playerId);
    }
    public void checkEntitlement(String token, String sku, EntitlementCallBack entitlementCallBack) {
         EntitlementLayer.getInstance().checkEntitlement(token, sku,entitlementCallBack);
    }
    public LiveData<ResponseEntitle> hitApiEntitlement(String token, String sku) {
        return Objects.requireNonNull(EntitlementLayer.getInstance()).hitApiEntitlement(token, sku);
    }
    public LiveData<PlayDetailResponse> getPlayDetail(String url) {
        return EntitlementLayer.getInstance().getPlayDetails(url);
    }

    public LiveData<Response> getGeoBlocking( String mediaContentId) {
        return Objects.requireNonNull(EntitlementLayer.getInstance()).getGeoBlocking( mediaContentId);
    }

    public LiveData<LiveStatusResponse> checkLiveStatus(int eventId) {
        return Objects.requireNonNull(EntitlementLayer.getInstance()).checkLiveStatus(eventId);
    }

    public LiveData<RelatedRailsCommonData>  getArtistVideo(String customData){
        return APIServiceLayer.getInstance().getRelatedVideo(customData);
    }

    public LiveData<RelatedRailsCommonData>  getArtistAlbum(String customData){
        return APIServiceLayer.getInstance().getRelatedAlbum(customData);
    }
    public LiveData<RelatedRailsCommonData>  getRelateArticled(String customData){
        return APIServiceLayer.getInstance().getRelatedArticle(customData);
    }

    public LiveData<RelatedRailsCommonData>  getArtistDetails(String genres){
        return APIServiceLayer.getInstance().getRelatedArtist(genres);
    }

    public LiveData<ResponseAllComments> hitApiAllComents(String size, int page, JsonObject data) {
        return detailRepository.hitApiAllComment(size, page, data);
    }

    public LiveData<ResponseAddComment> hitApiAddComment(String token, JsonObject data) {
        return detailRepository.hitApiAddComment(token, data);
    }

    public LiveData<ResponseDeleteComment> hitApiDeleteComment(String token, String data) {
        return detailRepository.hitApiDeleteComment(token, data);
    }


    public LiveData<JsonObject> hitLogout(boolean session, String token) {
        return detailRepository.hitApiLogout(session, token);
    }

    public LiveData<SeriesResponse> getSeriesDetail(int seriesId) {
        return detailRepository.getSeriesDetail(seriesId);
    }

    public LiveData<SeasonResponse> getVOD(int seriesID, int pageNo, int length) {
        return detailRepository.getVOD(seriesID, pageNo, length);
    }

    public LiveData<List<SeasonResponse>> hitMultiRequestSeries(int size, SeriesResponse data, int railSize) {
        return detailRepository.multiRequestSeries(size, data, railSize);
    }


    public LiveData<SeasonResponse> singleRequestSeries(int id, int page, int size) {
        return detailRepository.singleRequestSeries(id, page, size);
    }

    public LiveData<ResponseEmpty> heartBeatApi(JsonObject assetRequest, String token) {
        return detailRepository.heartBeatApi(assetRequest, token);
    }

    public LiveData<ResponseAssetHistory> getMultiAssetHistory(String token, JsonObject data) {
        return detailRepository.getMultiAssetHistory(token, data);
    }

    @Override
    public void resetObject() {

    }

    public LiveData<GetBookmarkResponse> getBookMarkByVideoId(String token, int videoId) {
    return BookmarkingRepository.getInstance().getBookmarkByVideoId(token,videoId);
    }

    public MutableLiveData<Boolean> getIsShowMore() {
        return isShowMore;
    }

    public void setIsShowMore(boolean isShowMore) {
        this.isShowMore.postValue(isShowMore);
    }

    public @Nullable LiveData<DrmResponse> getWidevineUrl(@NotNull String drmBaseUrl, @NotNull String drmToken) {
        return  EntitlementLayer.getInstance().getWideVineUrl(drmBaseUrl,drmToken);
    }
}
