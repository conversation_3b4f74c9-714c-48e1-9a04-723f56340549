package com.enveu.activities.device_management

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import app.doxzilla.bean_model.active_device_management.startPLayBackSessionModel_AppLevel.StartSessionModel
import app.doxzilla.bean_model.active_device_management.stopPlaybackSession.StopPlaybackSessionModel
import app.doxzilla.bean_model.active_device_management.updatePlayBackSession.UpdatePlaybackSessionModel
import com.enveu.activities.device_management.remove_user_device.RemoveDevice
import com.enveu.activities.device_management.userDevices.GetUserDevice
import com.enveu.beanModel.LoginDeviceModel.LoginDeviceModel
import com.enveu.client.baseCollection.baseCategoryServices.BaseCategoryServices.Companion.instance
import com.enveu.client.deviceManagement.remove_user_device.callback.RemoveDeviceCallBack
import com.enveu.client.deviceManagement.userDevices.callback.GetAllDeviceCallBack
import com.enveu.networking.apiendpoints.ApiInterface
import com.enveu.networking.apiendpoints.RequestConfig
import com.google.gson.Gson
import org.json.JSONObject
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class DeviceManagementRepository {
    fun getLoginDevice(token: String?): LiveData<LoginDeviceModel> {
        val responseApi: MutableLiveData<LoginDeviceModel> = MutableLiveData<LoginDeviceModel>()
        val result = RequestConfig.getUserInteration(token).create(
            ApiInterface::class.java
        ).getLoginDeviceResponse(false)
        result.enqueue(object : Callback<LoginDeviceModel?> {
            override fun onResponse(
                call: Call<LoginDeviceModel?>, response: Response<LoginDeviceModel?>
            ) {
                if (response.body() != null) {
                    val gson = Gson()
                    val tmp = gson.toJson(response.body())
                    val loginItemBean = gson.fromJson(
                        tmp, LoginDeviceModel::class.java
                    )
                    responseApi.postValue(loginItemBean)
                } else {
                    val responseModel = LoginDeviceModel()
                    val errorObject = response.errorBody()?.string()?.let { JSONObject(it) }
                    val errorCode = errorObject?.getInt("responseCode")
                    if (errorCode != null) {
                        responseModel.responseCode = errorCode
                    }
                    responseApi.postValue(responseModel)
                }
            }

            override fun onFailure(call: Call<LoginDeviceModel?>, t: Throwable) {
                val cl = LoginDeviceModel()
                cl.responseCode = 500
                responseApi.postValue(cl)
            }
        })
        return responseApi
    }


    fun getUpdatedDevice(token: String?, deviceName: String?): LiveData<LoginDeviceModel> {
        val responseApi: MutableLiveData<LoginDeviceModel> = MutableLiveData<LoginDeviceModel>()
        val result = RequestConfig.getDeviceUpdateInteraction(token, deviceName)
            .create(ApiInterface::class.java).getUpdatedDeviceResponse()
        result.enqueue(object : Callback<LoginDeviceModel?> {
            override fun onResponse(
                call: Call<LoginDeviceModel?>, response: Response<LoginDeviceModel?>
            ) {
                if (response.body() != null) {
                    val gson = Gson()
                    val tmp = gson.toJson(response.body())
                    val loginItemBean = gson.fromJson(
                        tmp, LoginDeviceModel::class.java
                    )
                    responseApi.postValue(loginItemBean)
                } else {
                    val responseModel = LoginDeviceModel()
                    val errorObject = response.errorBody()?.string()?.let { JSONObject(it) }
                    val errorCode = errorObject?.getInt("responseCode")
                    if (errorCode != null) {
                        responseModel.responseCode = errorCode
                    }
                    responseApi.postValue(responseModel)
                }
            }

            override fun onFailure(call: Call<LoginDeviceModel?>, t: Throwable) {
                val cl = LoginDeviceModel()
                cl.responseCode = 500
                responseApi.postValue(cl)
            }
        })
        return responseApi
    }


    fun getAllDeviceLogin(token: String, status: String, page: Int): LiveData<GetUserDevice> {
        val mutableLiveData = MutableLiveData<GetUserDevice>()
        instance.getAllDevice(token, page, status, object : GetAllDeviceCallBack {
            override fun onSuccess(
                status: Boolean,
                response: Response<com.enveu.client.deviceManagement.userDevices.GetUserDevice>
            ) {
                if (response.body() != null) {
                    val gson = Gson()
                    val tmp = gson.toJson(response.body())
                    val loginItemBean: GetUserDevice = gson.fromJson(tmp, GetUserDevice::class.java)
                    mutableLiveData.postValue(loginItemBean)
                } else {
                    val cl = GetUserDevice()
                    mutableLiveData.postValue(cl)
                }
            }

            override fun onFailure(status: Boolean, errorMsg: String) {
                val cl = GetUserDevice()
                cl.responseCode = 500
                mutableLiveData.postValue(cl)
            }
        })
        return mutableLiveData
    }

    fun removeDevice(token: String, deviceID: String): LiveData<RemoveDevice> {
        val mutableLiveData = MutableLiveData<RemoveDevice>()
        instance.removeDevice(token, deviceID, object : RemoveDeviceCallBack {

            override fun onFailure(status: Boolean, errorMSG: String) {
                val cl = RemoveDevice()
                cl.responseCode = 500
                mutableLiveData.postValue(cl)
            }

            override fun onSuccess(
                status: Boolean,
                response: Response<com.enveu.client.deviceManagement.remove_user_device.model.RemoveDevice?>?
            ) {
                if (response?.body() != null) {
                    val gson = Gson()
                    val tmp = gson.toJson(response.body())
                    val removeDevice: RemoveDevice = gson.fromJson(tmp, RemoveDevice::class.java)
                    mutableLiveData.postValue(removeDevice)
                } else {
                    val cl = RemoveDevice()
                    mutableLiveData.postValue(cl)
                }
            }
        })
        return mutableLiveData
    }

    fun startPlaybackSession(
        token: String, contentId: String, contentName: String
    ): LiveData<StartSessionModel> {
        val requestLivedata: MutableLiveData<StartSessionModel> = MutableLiveData()
        val result = RequestConfig.getUserInteration(token).create(ApiInterface::class.java)
        result.getStartSessionPlayback(contentId, contentName)
            .enqueue(object : Callback<StartSessionModel> {
                override fun onResponse(
                    call: Call<StartSessionModel>, response: Response<StartSessionModel>
                ) {
                    if (response.isSuccessful) {
                        if (response.body() != null) {
                            requestLivedata.postValue(response.body())
                        } else {
                            requestLivedata.postValue(response.body())
                        }
                    } else {
                        val responseModel: StartSessionModel
                        val errorObject = response.errorBody()?.string()?.let { JSONObject(it) }
                        val errorCode = errorObject?.getInt("responseCode")
                        val debugMsg = errorObject?.getString("debugMessage")
                        responseModel = StartSessionModel(null, debugMsg, errorCode)
                        requestLivedata.postValue(responseModel)
                    }
                }

                override fun onFailure(call: Call<StartSessionModel>, t: Throwable) {
                    requestLivedata.postValue(null)
                }

            })

        return requestLivedata
    }


    fun updatePlaybackSession(
        token: String, sessionId: String
    ): LiveData<UpdatePlaybackSessionModel> {
        var mutableLivedata: MutableLiveData<UpdatePlaybackSessionModel> = MutableLiveData()
        val result = RequestConfig.getUserInteration(token).create(ApiInterface::class.java)
        result.getUpdatePlayback(sessionId).enqueue(object : Callback<UpdatePlaybackSessionModel> {
            override fun onResponse(
                call: Call<UpdatePlaybackSessionModel>,
                response: Response<UpdatePlaybackSessionModel>
            ) {
                if (response.isSuccessful) {
                    if (response.body() != null) {
                        mutableLivedata.postValue(response.body())
                    } else {
                        mutableLivedata.postValue(response.body())
                    }
                } else {
                    val responseModel: UpdatePlaybackSessionModel
                    val errorObject = response.errorBody()?.string()?.let { JSONObject(it) }
                    val errorCode = errorObject?.getInt("responseCode")
                    val debugMsg = errorObject?.getString("debugMessage")
                    responseModel = UpdatePlaybackSessionModel("", debugMsg, errorCode)
                    mutableLivedata.postValue(responseModel)
                }
            }

            override fun onFailure(call: Call<UpdatePlaybackSessionModel>, t: Throwable) {
                mutableLivedata.postValue(null)
            }
        })
        return mutableLivedata
    }

    fun stopPlaybackSession(token: String, sessionId: String): LiveData<StopPlaybackSessionModel> {
        var mutableLiveData: MutableLiveData<StopPlaybackSessionModel> = MutableLiveData()
        val result = RequestConfig.getUserInteration(token).create(ApiInterface::class.java)
        result.getStopPlayback(sessionId).enqueue(object : Callback<StopPlaybackSessionModel> {
            override fun onResponse(
                call: Call<StopPlaybackSessionModel>, response: Response<StopPlaybackSessionModel>
            ) {
                mutableLiveData.postValue(response.body())
            }

            override fun onFailure(call: Call<StopPlaybackSessionModel>, t: Throwable) {
                mutableLiveData.postValue(null)
            }

        })

        return mutableLiveData

    }

    fun removeAllDevice(token: String, logoutCurrentDevice: Boolean): LiveData<RemoveDevice> {
        val mutableLiveData = MutableLiveData<RemoveDevice>()
        instance.removeAllDevice(token, object : RemoveDeviceCallBack {
            override fun onSuccess(
                status: Boolean,
                response: Response<com.enveu.client.deviceManagement.remove_user_device.model.RemoveDevice?>?
            ) {
                if (response?.body() != null) {
                    val gson = Gson()
                    val tmp = gson.toJson(response.body())
                    val removeDevice: RemoveDevice = gson.fromJson(tmp, RemoveDevice::class.java)
                    mutableLiveData.postValue(removeDevice)
                } else {
                    val cl = RemoveDevice()
                    mutableLiveData.postValue(cl)
                }
            }

            override fun onFailure(status: Boolean, errorMSG: String) {
                val cl = RemoveDevice()
                cl.responseCode = 500
                mutableLiveData.postValue(cl)
            }
        })
        return mutableLiveData
    }
}


