package com.enveu.activities.device_management

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import app.doxzilla.bean_model.active_device_management.startPLayBackSessionModel_AppLevel.StartSessionModel
import app.doxzilla.bean_model.active_device_management.stopPlaybackSession.StopPlaybackSessionModel
import app.doxzilla.bean_model.active_device_management.updatePlayBackSession.UpdatePlaybackSessionModel
import com.enveu.activities.device_management.userDevices.GetUserDevice
import com.enveu.beanModel.LoginDeviceModel.LoginDeviceModel

class DeviceManagementViewModel  : ViewModel(){

    private val deviceManagementRepository: DeviceManagementRepository = DeviceManagementRepository()


    fun removeAllDevices(token: String?, logoutCurrentDevice : Boolean): LiveData<com.enveu.activities.device_management.remove_user_device.RemoveDevice>? {
        return token?.let { deviceManagementRepository.removeAllDevice(it,logoutCurrentDevice) }
    }

    fun getLoginDevice(token: String?): LiveData<LoginDeviceModel> {
        return deviceManagementRepository.getLoginDevice(token)
    }

    fun getUpdatedDevice(token: String?,deviceName:String): LiveData<LoginDeviceModel> {
        return deviceManagementRepository.getUpdatedDevice(token,deviceName)
    }

    fun removeDevice(token: String?, deviceID: String): LiveData<com.enveu.activities.device_management.remove_user_device.RemoveDevice>? {
        return token?.let { deviceManagementRepository.removeDevice(it, deviceID) }
    }

    fun getAllDevices(page : Int , status : String, token: String?): LiveData<GetUserDevice>? {
        return token?.let { deviceManagementRepository.getAllDeviceLogin(it,status,page) }
    }

    fun startSessionPlayBack(token: String, contentId: String, contentName: String) : LiveData<StartSessionModel>{
        return deviceManagementRepository.startPlaybackSession(token, contentId, contentName)
    }

    fun updatePlaybackSession(token: String , sessionID : String) : LiveData<UpdatePlaybackSessionModel>{
        return deviceManagementRepository.updatePlaybackSession(token,sessionID)
    }

    fun stopPlaybackSession(token: String ,sessionID: String) : LiveData<StopPlaybackSessionModel>{
        return deviceManagementRepository.stopPlaybackSession(token,sessionID)
    }

}