package com.enveu.activities.device_management

import android.annotation.SuppressLint
import android.app.Dialog
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.SimpleItemAnimator
import com.enveu.OttApplication
import com.enveu.R
import com.enveu.activities.device_management.adapter.DevicesAdapter
import com.enveu.activities.device_management.userDevices.GetUserDevice
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.databinding.ActivityDeviceManagementBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.ToastHandler
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper
import java.lang.Math.abs
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/*

Business Requirements for Device Manager:

1. Overview:
- The Device Manager functionality allows users to view and manage their devices within the application.

2. Initial Screen:
- Upon opening the screen, users should be presented with a list of all their devices.

3. Device Management:
- Users can perform the following actions on their devices:
a. View device details.
b. Remove individual devices.
c. Remove all devices.

4. Implementation Details:
a. Layout Setup:
- Inflate the appropriate layout for the Device Manager screen.
- Configure the toolbar with back navigation and a title ("Manage Devices").

b. Internet Connection:
- Check for an active internet connection before proceeding.
- If online, hide the no connection layout, display a progress bar, and animate the device list.

c. Device Retrieval:
- Call the getAllDevices API to fetch the list of user devices.
- Handle API response:
- If successful, dismiss the loader and display the device list.
- If unsuccessful, dismiss the loader.

d. Device List UI:
- Display devices in a RecyclerView with appropriate item animations.
- Identify the user's current device and position it at the top of the list.

e. Device Removal:
- Allow users to remove individual devices:
- Display a confirmation dialog with the device name.
- On confirmation, call the removeDevice API and update the UI accordingly.

- Allow users to remove all devices:
- Display a confirmation dialog for removing all devices.
- On confirmation, call the removeAllDevices API and update the UI accordingly.

f. UI Elements:
- Each device item should include:
- Device name.
- Last active timestamp.
- Device type image (mobile or laptop).
- Remove device button (visible for all devices except the current one).

g. Error Handling:
- Properly handle cases where API calls fail or return unexpected data.

5. Notes:
- The device list should be dynamic and update in real-time based on user actions.
*/

/*
   * Business Requirement:
   * When user open the screen then they see all devices
   * User can manage there devices (User Can remove there devices too)
   *
   *
   * Implementation Details:
   *  Layout Inflate
   *  Toolbar setup
   *  Check Connection for Internet
   *  Load progressbar
   *  Call getAllDevices API check its response and failure
   *  if response is success dismiss loader and set data in UI
   *  if failure then dismiss loader
   *  handle user click
   *  if user select remove all device then call removeAllDevice API and handle there response to
   *  if user remove only one device then call removeDevice API and handle there response to
   *
   * */



class DeviceManagerActivity : BaseBindingActivity<ActivityDeviceManagementBinding?>(),CommonDialogFragment.EditDialogListener {

    private var viewModel: DeviceManagementViewModel? = null
    private var preference: KsPreferenceKeys? = null
    private var adapter: DevicesAdapter? = null
    private val stringsHelper by lazy { StringsHelper }
    private var removeAll = false
    private var gDeviceID = ""
    private var gName = ""
    private var singleDevice = false
    private var dialogTitle: EditText?= null
    private var dialogButton : Button?= null
    private var dialogText : TextView?= null
    private var dialog : Dialog?= null
    private var currentDeviceName : String? = null

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityDeviceManagementBinding {
        return ActivityDeviceManagementBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        connectionValidation(NetworkConnectivity.isOnline(this))
        setUpToolBar()
        binding?.removeAll?.setOnClickListener {
            removeAll = true
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.logout_confirmation.toString(),
                    getString(R.string.logout_confirmation)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.sign_out_all_device.toString(),
                    getString(R.string.sign_out_all_device)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.sign_out1.toString(),
                    getString(R.string.sign_out1)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                    getString(R.string.cancel)
                )
            )
        }
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            AnalyticsUtils.trackScreenView(this, AppConstants.MANAGE_DEVICES)
            binding?.noConnectionLayout?.visibility = View.GONE
            binding?.progressBar?.visibility = View.VISIBLE
            RecyclerAnimator(this@DeviceManagerActivity)
                .animate(
                    binding?.recycleViewDevice
                )
            (binding?.recycleViewDevice?.itemAnimator as SimpleItemAnimator?)?.supportsChangeAnimations =
                false
            // set viewModel
            viewModel = ViewModelProvider(this@DeviceManagerActivity)[DeviceManagementViewModel::class.java]
            // set preference
            preference = KsPreferenceKeys.getInstance()
            dialog = Dialog(this)
            val dialogView = LayoutInflater.from(this).inflate(R.layout.cutom_edit_device_ui, null)
            dialog?.setContentView(dialogView)
            dialogTitle = dialogView.findViewById(R.id.playlist_title)
            dialogButton = dialogView.findViewById(R.id.create_btn)
            dialogText = dialogView.findViewById(R.id.dialog_title)
            binding?.editDevice?.setOnClickListener {
                // Show the dialog
                dialogTitle?.setText(currentDeviceName)
                dialogButton!!.background.alpha = 128
                dialog?.show()
            }

            dialogButton?.setOnClickListener {
                val input = dialogTitle?.text.toString()
                if (input.matches(Regex("[a-zA-Z0-9 ]+"))) { // Check if the input is valid
                    dialog?.dismiss() // Dismiss the dialog
                    editDevice() // Call the editDevice() function
                } else {
//                    dialogTitle?.error = "Please enter only letters and numbers"
                }
            }

            dialogTitle!!.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    val input = s.toString()
                    if (!input.matches(Regex("[a-zA-Z0-9 ]*"))) {
                        dialogTitle!!.error = "Please enter only letters and numbers"
                        dialogButton!!.isEnabled = false
                        dialogButton!!.background.alpha = 128
                    }else if (input.isEmpty()){
                        dialogTitle!!.error = "Please enter device name"
                        dialogButton!!.isEnabled = false
                        dialogButton!!.background.alpha = 128
                    }else{
                        dialogButton!!.isEnabled = true
                        dialogButton!!.background.alpha = 250
                    }
                }

                override fun afterTextChanged(s: Editable?) {
                    val input = s.toString()
                    if (!input.matches(Regex("[a-zA-Z0-9 ]*"))) {
                        if (input.isEmpty()){
                            dialogTitle!!.error = "Please enter only letters and numbers"
                            dialogButton!!.isEnabled = false
                            dialogButton!!.background.alpha = 128
                        }
                    }else{
                        dialogButton!!.isEnabled = true
                        dialogButton!!.background.alpha = 250
                    }
                }
            })

            getDevices()
        } else {
            // connection layout visibility set
            binding?.removeAll?.visibility = View.GONE
            binding?.noConnectionLayout?.visibility = View.VISIBLE
            binding?.noConnectionLayout?.bringToFront()
            // connection retry click handle
            binding?.connection?.retryTxt?.setOnClickListener {
                connectionObserver()
            }
        }
    }

    private fun connectionObserver() {
        connectionValidation(NetworkConnectivity.isOnline(this))
    }

    private fun editDevice(){
        viewModel?.getUpdatedDevice(KsPreferenceKeys.getInstance().appPrefAccessToken,dialogTitle!!.text.toString())
            ?.observe(this) {
                if (it != null) {
                    when (it.responseCode) {
                        2000 -> {
                            dismissLoading(binding?.progressBar)
                            getDevices()
                        }
                        4500 -> {
                            dismissLoading(binding?.progressBar)

                        }
                        else -> {
                            dismissLoading(binding?.progressBar)
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ), stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.something_went_wrong)
                                ), stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                                    getString(R.string.cancel)
                                )
                            )
                        }
                    }
                }
            }

    }

    //region getDeviceApiCall
    private fun getDevices() {
        viewModel?.getAllDevices(0, "ACTIVE", preference?.appPrefAccessToken)?.observe(this) {
            if (it?.responseCode == 2000) {
                binding?.progressBar?.visibility = View.GONE
                binding?.childLayout?.visibility = View.VISIBLE
                binding?.recycleViewDevice?.setHasFixedSize(true)
                checkDeviceIdAndPrintName(
                    it, AppCommonMethod.getDeviceId(
                        OttApplication.instance!!.contentResolver
                    )
                )
                val items = it.data?.items?.toMutableList()
                val newitem = items?.indexOfFirst {
                    it.deviceId == AppCommonMethod.getDeviceId(
                        OttApplication.instance!!.contentResolver
                    )
                }
                if (newitem != -1) {
                    val device32 = newitem?.let { it1 -> items.removeAt(it1) }
                    items?.add(0, device32!!)
                }
                //Set data in adapter and handle the onclickItem Response
                adapter = items?.filter { it->
                    it.deviceId != AppCommonMethod.getDeviceId(
                        OttApplication.instance!!.contentResolver
                    )
                }?.let { it1 ->
                    binding?.otherDevice?.visibility = View.VISIBLE
                    binding?.removeAll?.visibility = View.VISIBLE
                    DevicesAdapter(this, it1, object : DevicesAdapter.DeviceItemOnClick {
                        override fun onClickItem(deviceID: String, name: String) {
                            singleDevice = true
                            gDeviceID = deviceID
                            gName = name

                            binding?.removeAll?.visibility = View.VISIBLE
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.logout_confirmation.toString(),
                                    getString(R.string.logout_confirmation)
                                ),
                                name,
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.sign_out1.toString(),
                                    getString(R.string.sign_out1)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                                    getString(R.string.cancel)
                                )
                            )
                        }
                    })
                }!!
                if (items.size == 1) {
                    binding?.removeAll?.visibility = View.GONE
                    binding?.otherDevice?.visibility = View.GONE
                    binding?.recycleViewDevice?.visibility = View.GONE
                }
                // set adapter to recycleView
                binding?.recycleViewDevice?.adapter = adapter
                //Update List
                adapter?.updateList(it.data.items)
                binding?.progressBar?.visibility = View.GONE

            } else {
                binding?.progressBar?.visibility = View.GONE
            }
        }
    }
    //endregion

    // region getTimeAgo
    private fun comparingDate(currentDate: String, lastLoginDate: String): String {
        val dateFormatter = SimpleDateFormat("dd:MM:yyyy:zzzz", Locale.US)
        val currDate = dateFormatter.parse(currentDate)
        val lastDate = dateFormatter.parse(lastLoginDate)
        val calendar = Calendar.getInstance()

        currDate?.let { cd ->
            lastDate?.let { ld ->
                calendar.time = cd
                val currentDateComponents = calendar.clone() as Calendar
                calendar.time = ld
                val lastLoginComponents = calendar.clone() as Calendar

                if (currentDateComponents[Calendar.DAY_OF_MONTH] != lastLoginComponents[Calendar.DAY_OF_MONTH]) {
                    val lastLoginDay =
                        abs(currentDateComponents[Calendar.DAY_OF_MONTH] - lastLoginComponents[Calendar.DAY_OF_MONTH])
                    return if (lastLoginDay > 1) {
                        "$lastLoginDay ${resources.getString(R.string.days_ago)}"
                    } else {
                        "$lastLoginDay ${resources.getString(R.string.day_ago)}"
                    }
                }
                if (currentDateComponents[Calendar.MONTH] != lastLoginComponents[Calendar.MONTH]) {
                    val lastLoginMonth =
                        abs(currentDateComponents[Calendar.MONTH] - lastLoginComponents[Calendar.MONTH])
                    return if (lastLoginMonth > 1) {
                        "$lastLoginMonth ${resources.getString(R.string.months_Ago)}"
                    } else {
                        "$lastLoginMonth ${resources.getString(R.string.month_ago)}"
                    }
                }
                if (currentDateComponents[Calendar.YEAR] != lastLoginComponents[Calendar.YEAR]) {
                    val lastLoginYear =
                        abs(currentDateComponents[Calendar.YEAR] - lastLoginComponents[Calendar.YEAR])
                    return if (lastLoginYear > 1) {
                        "$lastLoginYear ${resources.getString(R.string.years_ago)}"
                    } else {
                        "$lastLoginYear ${resources.getString(R.string.year_ago)}"
                    }
                }
            }
        }
        return resources.getString(R.string.today)
    }
    //endregion

    //region checkDeviceIdAndPrintName
    @SuppressLint("SetTextI18n")
    fun checkDeviceIdAndPrintName(apiResponse: GetUserDevice, deviceIdToCheck: String) {
        val devices = apiResponse.data?.items
        val sdf = SimpleDateFormat("dd:MM:yyyy:zzz", Locale.US)
        val currentDate = Date()

        val formattedDate = sdf.format(currentDate)
        if (devices != null) {
            for (device in devices) {
                if (device.deviceId == deviceIdToCheck) {
                    binding?.this1?.visibility = View.VISIBLE
                    binding?.thisDeviceLayout?.visibility = View.VISIBLE
                    binding?.view1?.visibility = View.VISIBLE
                    binding?.view2?.visibility = View.VISIBLE
                    binding?.textview?.text = device.deviceName
                    currentDeviceName = device.deviceName.toString()
                    binding?.lastActive?.text = getString(R.string.last_Active) + " : ${
                        device.lastLogin?.let {
                            comparingDate(
                                formattedDate,
                                sdf.format(it)
                            )
                        }
                    }"

                    when (device.deviceType) {
                        "MOBILE", "TABLET" -> {
                            binding?.deviceImage?.setImageResource(R.drawable.iphone_device)
                        }

                        "TV" -> {
                            binding?.deviceImage?.setImageResource(R.drawable.activate_tv_icon)
                        }

                        else -> {
                            binding?.deviceImage?.setImageResource(R.drawable.lap_device)
                        }
                    }

                    return
                }
            }
        }
    }
    //endregion

    //<editor-fold desc="Logout all Device">
    private fun logoutAllDevices(isCurrentLogout: Boolean) {
        binding?.progressBar?.visibility = View.VISIBLE
        viewModel?.removeAllDevices(preference?.appPrefAccessToken, isCurrentLogout)
            ?.observe(this) {
                if (it.responseCode == 2000) {
                    getDevices().apply {
                        binding?.removeAll?.visibility = View.GONE
                        binding?.otherDevice?.visibility = View.GONE
                        binding?.recycleViewDevice?.visibility = View.GONE
                    }
                    binding?.progressBar?.visibility = View.GONE
                    ToastHandler.getInstance().show(
                        this@DeviceManagerActivity,
                        <EMAIL>(R.string.all_device_signout_succefully)
                    )
                } else {
                    binding?.progressBar?.visibility = View.GONE
                }
            }
    }
    //</editor-fold>

    private fun logoutDevice(deviceID: String, name: String) {
        viewModel?.removeDevice(preference?.appPrefAccessToken, deviceID)?.observe(this) {
            if (it.responseCode == 2000) {
                getDevices().apply {
                    ToastHandler.getInstance()
                        .show(
                            this@DeviceManagerActivity,
                            name + <EMAIL>(R.string.sign_out_successfully)
                        )
                }
                binding?.progressBar?.visibility = View.GONE
            } else {
                binding?.progressBar?.visibility = View.GONE
            }
        }
    }
    private fun setUpToolBar(){
        binding?.toolbarMange?.backArrow?.let { rotateImageLocaleWise(it) }
        binding?.toolbarMange?.backLayout?.setOnClickListener{
            finish()
        }
        binding?.toolbarMange?.searchIcon?.visibility = View.GONE
        binding?.toolbarMange?.logoMain2?.visibility = View.GONE
        binding?.toolbarMange?.titleMid?.visibility = View.VISIBLE
        binding?.toolbarMange?.titleMid?.text = getString(R.string.device_management_logo)
    }
    private fun commonDialog(title: String, description: String, actionBtn: String, cancelBtn : String?) {
        val fm = supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm!!, AppConstants.MESSAGE)
    }


    override fun onActionBtnClicked() {

        if (singleDevice) {
            singleDevice = false
            binding?.progressBar?.visibility = View.VISIBLE
            logoutDevice(gDeviceID, gName)
        } else if (removeAll) {
            removeAll = false
            logoutAllDevices(false)
        }
    }

    override fun onCancelBtnClicked() {

    }
}

