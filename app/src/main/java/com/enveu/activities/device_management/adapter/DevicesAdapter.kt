package com.enveu.activities.device_management.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.OttApplication.Companion.instance
import com.enveu.R
import com.enveu.activities.device_management.userDevices.Item
import com.enveu.databinding.DevicesItemBinding
import com.enveu.utils.config.ConfigManager.getDeviceId
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * Adapter class for managing the display of devices in a RecyclerView.
 *
 * @param list The list of devices to be displayed.
 * @param click The click listener for handling item clicks.
 */
class DevicesAdapter(private val context : Context, private val list: List<Item>, private val click: DeviceItemOnClick) :
    RecyclerView.Adapter<DevicesAdapter.DevicesViewHolder>() {

    /**
     * ViewHolder class for holding the views of each device item.
     *
     * @param binding The view binding for the device item.
     */
    class DevicesViewHolder(binding: DevicesItemBinding) : RecyclerView.ViewHolder(binding.root) {
        val device = binding.textview
        val lastLogin = binding.lastActive
        val deviceType = binding.deviceImage
        val removeDevice = binding.removeDevice
    }


    /**
     * Inflates the layout for each device item.
     */
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DevicesViewHolder {
        val binding = DevicesItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return DevicesViewHolder(binding)
    }

    /**
     * Gets the total number of devices in the list.
     */
    override fun getItemCount(): Int {
        return list.size
    }

    /**
     * Binds the data to the views of each device item.
     */
    @SuppressLint("SetTextI18n", "SuspiciousIndentation")
    override fun onBindViewHolder(holder: DevicesViewHolder, position: Int) {
        val sdf = SimpleDateFormat("dd:MM:yyyy:zzz", Locale.US)
        val currentDate = Date()

        val formattedDate = sdf.format(currentDate)
        holder.device.text = list[position].deviceName
        holder.removeDevice.setOnClickListener{
            click.onClickItem(list[position].deviceId.toString(), list[position].deviceName.toString())
        }
        holder.lastLogin.text =
           context.resources.getString(R.string.last_Active)+ " : ${list[position].lastLogin?.let { comparingDate(formattedDate,sdf.format(it)) }}"
        when (list[position].deviceType) {
            "MOBILE" -> {
                holder.deviceType.setImageResource(R.drawable.iphone_device)
            }
            "TABLET" ->{
                holder.deviceType.setImageResource(R.drawable.iphone_device)
            }
            "TAB" ->{
                holder.deviceType.setImageResource(R.drawable.iphone_device)
            }
            "TV" -> {
                holder.deviceType.setImageResource(R.drawable.activate_tv_icon)
            }
            else -> {
                holder.deviceType.setImageResource(R.drawable.lap_device)
            }
        }
        if (list[position].deviceId?.equals(
                getDeviceId(
                    instance?.contentResolver
                )
            ) == true) {
            holder.removeDevice.visibility = View.GONE
            Log.d("checkRes","viewGONE")
        }
    }

    /**
     * Converts a timestamp to a readable date format.
     */
    private fun comparingDate(currentDate: String, lastLoginDate: String): String {
        val dateFormatter = SimpleDateFormat("dd:MM:yyyy:zzzz", Locale.US)
        val currDate = dateFormatter.parse(currentDate)
        val lastDate = dateFormatter.parse(lastLoginDate)
        val calendar = Calendar.getInstance()

        currDate?.let { cd ->
            lastDate?.let { ld ->
                calendar.time = cd
                val currentDateComponents = calendar.clone() as Calendar
                calendar.time = ld
                val lastLoginComponents = calendar.clone() as Calendar

                if (currentDateComponents[Calendar.DAY_OF_MONTH] != lastLoginComponents[Calendar.DAY_OF_MONTH]) {
                    val lastLoginDay =
                        Math.abs(currentDateComponents[Calendar.DAY_OF_MONTH] - lastLoginComponents[Calendar.DAY_OF_MONTH])
                    return if (lastLoginDay > 1) {
                        "$lastLoginDay ${context.resources.getString(R.string.days_ago)}"
                    } else {
                        "$lastLoginDay ${context.resources.getString(R.string.day_ago)}"
                    }
                }
                if (currentDateComponents[Calendar.MONTH] != lastLoginComponents[Calendar.MONTH]) {
                    val lastLoginMonth =
                        Math.abs(currentDateComponents[Calendar.MONTH] - lastLoginComponents[Calendar.MONTH])
                    return if (lastLoginMonth > 1) {
                        "$lastLoginMonth ${context.resources.getString(R.string.months_Ago)}"
                    } else {
                        "$lastLoginMonth ${context.resources.getString(R.string.month_ago)}"
                    }
                }
                if (currentDateComponents[Calendar.YEAR] != lastLoginComponents[Calendar.YEAR]) {
                    val lastLoginYear =
                        Math.abs(currentDateComponents[Calendar.YEAR] - lastLoginComponents[Calendar.YEAR])
                    return if (lastLoginYear > 1) {
                        "$lastLoginYear ${context.resources.getString(R.string.years_ago)}"
                    } else {
                        "$lastLoginYear ${context.resources.getString(R.string.year_ago)}"
                    }
                }
            }
        }
        return context.resources.getString(R.string.today)
    }
    /**
     * Updates the list of devices in the adapter.
     */
    fun updateList(list: List<Item>) {
        this.list == list
    }

    /**
     * Interface for handling device item clicks.
     */
    interface DeviceItemOnClick {
        fun onClickItem(deviceID: String, name: String)
    }
}
