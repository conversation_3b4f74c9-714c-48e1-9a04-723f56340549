package com.enveu.activities.follow_follower

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.enveu.OttApplication.Companion.context
import com.enveu.R
import com.enveu.activities.follow_follower.adapter.ProfileReelsAdapter
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.profile.ui.ProfileActivityNew
import com.enveu.baseModels.BaseActivity
import com.enveu.callbacks.ReelItemClickListener
import com.enveu.databinding.ActivityFollowFollowingProfileBinding
import com.enveu.databinding.ItemReportReasonLayoutBinding
import com.enveu.networking.response.AllReelsListItems
import com.enveu.networking.response.CreateContentData
import com.enveu.networking.response.ReelCreatorId
import com.enveu.networking.response.ReelsContentItem
import com.enveu.networking.response.ReelsNavigationInfo
import com.enveu.utils.Constants
import com.enveu.utils.PaginationGridScrollListener
import com.enveu.utils.ReelsDataHolder
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show
import com.enveu.utils.toSuffixFormat
import com.enveu.view_model.ShortsViewModel
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.moengage.core.internal.utils.isTablet
import java.util.Locale


class FollowFollowingProfileActivity : BaseActivity() {
    private lateinit var binding: ActivityFollowFollowingProfileBinding
    private lateinit var viewModel:ShortsViewModel
    private var profileReelsAdapter:ProfileReelsAdapter? = null
    private var preference:KsPreferenceKeys? = null
    private var createContentData: CreateContentData? = null
    private var reelCreatorData: ReelCreatorId? = null
    private var creatorReelsList:ArrayList<ReelsContentItem?>? = ArrayList()
    private var pageNumber: Int = 0
    private var pageSize: Int = 10
    private var totalPages : Int? = 0
    private val RESPONSE_CODE = 1001
    private val RESPONSE_CODE_FOLOW = 1002
    private var hasReelsData:Boolean = false
    private var authToken:String = ""
    private var profileImageUrl:String = ""
    private var isFollowing : Boolean = false
    private var followerCount = MutableLiveData<Long?>()
    private var followingCount = MutableLiveData<Long?>()
    private var reelsNavigationInfo = ReelsNavigationInfo()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_follow_following_profile)
        initUI()
    }

    private fun initUI() {
        if (intent.getSerializableExtra(Constants.CREATOR_CONTENT_BUNDLE) != null) {
            reelCreatorData = intent.getSerializableExtra(Constants.CREATOR_CONTENT_BUNDLE) as ReelCreatorId
        }
        profileImageUrl = intent.getStringExtra(Constants.IMAGE_URL).toString()
        reelsNavigationInfo.reelHonorId = reelCreatorData?.id
        preference = KsPreferenceKeys.getInstance()
        authToken = preference?.appPrefAccessToken.toString()
        rotateImageLocaleWise(binding.backBtn)
        viewModel = ViewModelProvider(this)[ShortsViewModel::class.java]
        binding.progressBar.show()
        viewModel.getMediaContentDetails(reelCreatorData?.id)
        getCreatorShorts(reelCreatorData?.id)
        viewModel.checkAndCallFollow(reelCreatorData?.id.toString())
        setObserver()
        setProfileImage()

        binding.followUnfollowBtn.setOnClickListener {
            if (preference?.userId.toString() == createContentData?.externalRefId) {
                val intent = Intent(this, ProfileActivityNew::class.java)
                startActivityForResult(intent, RESPONSE_CODE)
            } else {
                if (isFollowing) {
                    viewModel.setUnFollowUser(authToken, reelCreatorData?.externalIdentifier?.toInt())
                    followerCount.value = followerCount.value?.minus(1)
                    isFollowing = false
                } else {
                    viewModel.setFollowUser(authToken, reelCreatorData?.externalIdentifier?.toInt())
                    followerCount.value = followerCount.value?.plus(1)
                    isFollowing = true
                }
                updateFollowButtonUI(isFollowing)
            }
        }

        binding.followingView.setOnClickListener {
            openFollowFollowingActivity(1)
        }

        binding.followerView.setOnClickListener {
            openFollowFollowingActivity(0)
        }
        binding.backBtn.setOnClickListener {
            updateFollowFollowing()
        }

        binding.moreImageView.setOnClickListener {
            openReportBottomSheet()
        }
    }


    private fun updateFollowFollowing(){
        val intent = Intent().apply {
            putExtra(Constants.UPDATE_FOLLOW, isFollowing)
        }
        setResult(RESULT_OK, intent)
        finish()
    }

    override fun onResume() {
        super.onResume()
        setProfileImage()
    }

    private fun updateFollowButtonUI(isFollowing: Boolean) {
        Log.d("isFollowing", "updateButton: $isFollowing")
        val localizedContent = context.setLocale(Locale.getDefault().toString())
        if (isFollowing) {
            binding.followUnfollowBtn.text = localizedContent.getString(R.string.following)
            binding.followUnfollowBtn.background = ContextCompat.getDrawable(context, R.drawable.following_background)
        } else {
            binding.followUnfollowBtn.text =  localizedContent.getString(R.string.follow)
            binding.followUnfollowBtn.background = ContextCompat.getDrawable(context, R.drawable.follower_background)
        }
    }

    fun Context.setLocale(language: String): Context {
        val locale = Locale(language)
        Locale.setDefault(locale)
        val config = Configuration()
        config.setLocale(locale)
        return createConfigurationContext(config)
    }

    private fun setProfileImage() {
        val fromMoreFragment = intent.getBooleanExtra(Constants.FROM_MORE_FRAGMENT, false)
        val fromUgcFragment = intent.getBooleanExtra(Constants.FROM_UGC_FRAGMENT, false)
        val profilePic = if (fromMoreFragment){
            preference?.isActiveUserProfileData?.profilePicURL
        } else if (fromUgcFragment){
            profileImageUrl
        } else {
            reelCreatorData?.posterUrl
        }
        binding.let { Glide.with(this).load(profilePic).placeholder(R.drawable.profile_avtar_logo).error(R.drawable.profile_avtar_logo).into(it.userProfilePic) }
    }

    private fun openReportBottomSheet() {
        val dialog = BottomSheetDialog(this)
        val binding = ItemReportReasonLayoutBinding.inflate(layoutInflater)
        dialog.setContentView(binding.root)
        binding.titleText.text = createContentData?.title
        binding.cancelText.setOnClickListener {
            dialog.dismiss()
        }

        binding.reportText.setOnClickListener {
            Intent(this, ProfileReportActivity::class.java).also {
                it.putExtra(Constants.ENTITY_TYPE, Constants.CUSTOMER)
                it.putExtra(Constants.ENTITY_ID, createContentData?.externalRefId)
                startActivity(it)
            }
            dialog.dismiss()
        }
        dialog.show()
    }

    private fun setObserver() {
        viewModel.mediaContentDetailsResponse.observe(this){ mediaContentDetails ->
            binding.progressBar.hide()
            if (mediaContentDetails != null){
                createContentData = mediaContentDetails.data
                binding.userName.text = mediaContentDetails.data?.title
                if (mediaContentDetails.data?.description != null){
                    binding.bio.text = mediaContentDetails.data.description.toString()
                }
                followerCount.value = mediaContentDetails.data?.customData?.followers?.toLongOrNull()
                binding.followingNumber.text = mediaContentDetails.data?.customData?.following?:"0"
                binding.followUnfollowBtn.show()
                binding.toolBarUserName.text = mediaContentDetails.data?.customData?.userName
                if (preference?.userId.toString() != mediaContentDetails.data?.externalRefId){
                    binding.followUnfollowBtn.text = getString(R.string.follow)
                    binding.moreImageView.show()
                    viewModel.userInteractionOfContentsResponse.observe(this) { contents ->
                        contents.forEach {
                            isFollowing = it.follow
                            updateFollowButtonUI(isFollowing)
                        }
                    }
                }
                else{
                    binding.followUnfollowBtn.text = getString(R.string.edit_profile_text)
                    binding.followUnfollowBtn.background = ContextCompat.getDrawable(context, R.drawable.follower_background)
                }
                reelCreatorData?.externalIdentifier  = mediaContentDetails.data?.externalRefId
            }
        }
        followerCount.observe(this){
            binding.followerNumber.text = it?.toSuffixFormat()
        }

        followingCount.observe(this){
            binding.followingNumber.text = it?.toSuffixFormat()
        }
    }

    private fun openFollowFollowingActivity(tabIndex: Int) {
        Intent(this, FollowerFollowingActivity::class.java).also {
            it.putExtra(Constants.TAB_INDEX, tabIndex)
            it.putExtra(Constants.CREATOR_CONTENT_BUNDLE, reelCreatorData)
            it.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            startActivityForResult(it, RESPONSE_CODE_FOLOW)
        }
    }

    private fun getCreatorShorts(creatorId: Long?){
      viewModel.getCreatorShorts(creatorId, pageNumber, pageSize).observe(this) { creatorShortsData ->
          if (creatorShortsData.data?.items?.size != 0){
              creatorShortsData.data?.items?.let { creatorReelsList?.addAll(it) }
              totalPages = creatorShortsData.data?.totalPages
              hasReelsData = true
              pageNumber += 1
              reelsNavigationInfo.loadedPageNumber = pageNumber
              setReelsAdapter()
          }
          else{
              if (!hasReelsData) {
                  binding.noDataFound.show()
              }
          }
      }
    }

    private fun callGetReelsPagination(layoutManager: GridLayoutManager) {
        binding.reelsRecyclerView.addOnScrollListener(object : PaginationGridScrollListener(layoutManager) {
            override fun onLoadMore(page: Int, totalItemsCount: Int) {
                if ((totalPages?:0) > page) {
                    getCreatorShorts(reelCreatorData?.id)
                    reelsNavigationInfo.shouldExtraApiCall = true
                }
                else{
                    reelsNavigationInfo.shouldExtraApiCall = false
                }
            }
        })
    }

    private val reelsContentItem = object : ReelItemClickListener {
        override fun onReelItemClickListener(position: Int) {
            reelsNavigationInfo.reelsPosition = position
            reelsNavigationInfo.loadedPageNumber = pageNumber
            reelsNavigationInfo.totalReels = totalPages
            val allReelsListItems = AllReelsListItems()
            allReelsListItems.reelsItemsList = creatorReelsList
            ReelsDataHolder.allReelsListItems = allReelsListItems
            reelsNavigationInfo.navigatePage = Constants.CREATOR_SECTION
            openShortsFragment(creatorReelsList?.get(position))
        }
    }

    private fun openShortsFragment(reelsContentItem: ReelsContentItem?) {
        reelsContentItem?.reelsNavigationInfo = reelsNavigationInfo
        if (intent.getBooleanExtra(Constants.SHOULD_FOLLOW_FOLLOWING_PROFILE, false)){
            Intent(this@FollowFollowingProfileActivity, HomeActivity::class.java).also {
                it.putExtra(Constants.OPEN_SHORTS_FRAGMENT, true)
                it.putExtra(Constants.SHORTS_REELS_BUNDLE, reelsContentItem)
                it.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(it)
            }
        }
        else {
            val intent = Intent().apply {
                putExtra(Constants.SHORTS_REELS_BUNDLE, reelsContentItem)
                putExtra(Constants.OPEN_SHORTS_FRAGMENT, true)
                putExtra(Constants.UPDATE_FOLLOW, isFollowing)
            }
            setResult(RESULT_OK, intent)
            finish()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setReelsAdapter() {
        if (profileReelsAdapter == null) {
            RecyclerAnimator(this).animate(binding.reelsRecyclerView)
            profileReelsAdapter = ProfileReelsAdapter(this, creatorReelsList, reelsContentItem)

          val spanCount =  if (isTablet(this)){ 5 }
          else { 3 }
            val layoutManager = GridLayoutManager(this, spanCount)
            binding.reelsRecyclerView.layoutManager = layoutManager
            binding.reelsRecyclerView.adapter = profileReelsAdapter
            callGetReelsPagination(layoutManager)
        }
        else{
            profileReelsAdapter?.notifyDataSetChanged()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == RESPONSE_CODE) {
            val updatedData = data?.getStringExtra(AppConstants.UPDATE_NAME)
            val updatedProfile = data?.getStringExtra(AppConstants.UPDATE_PROFILE)
            val updatedBio = data?.getStringExtra(AppConstants.UPDATE_BIO)
            binding.userName.text = updatedData
            binding.bio.text = updatedBio
            context.let { Glide.with(it).load(updatedProfile).into(binding.userProfilePic) }
        }

        if (resultCode == Activity.RESULT_OK && requestCode == RESPONSE_CODE_FOLOW) {
            if (data?.getLongExtra(Constants.FOLLOWER_COUNT, 0L) != null){
                followerCount.value = data.getLongExtra(Constants.FOLLOWER_COUNT, 0)
            }
            if (data?.getLongExtra(Constants.FOLLOWING_COUNT, 0L) != null){
                followingCount.value = data.getLongExtra(Constants.FOLLOWING_COUNT, 0)
            }
        }
    }
}
