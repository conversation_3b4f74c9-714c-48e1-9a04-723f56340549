package com.enveu.activities.follow_follower

import android.content.Intent
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.databinding.DataBindingUtil
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.enveu.R
import com.enveu.activities.follow_follower.adapter.TabLayoutAdapter
import com.enveu.baseModels.BaseActivity
import com.enveu.databinding.ActivityFollowerFollowingBinding
import com.enveu.networking.response.FollowingListItem
import com.enveu.networking.response.ReelCreatorId
import com.enveu.networking.response.ReelsContentItem
import com.enveu.utils.Constants
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.toSuffixFormat
import com.google.android.material.tabs.TabLayout

class FollowerFollowingActivity : BaseActivity() {
    private lateinit var binding: ActivityFollowerFollowingBinding
    private var tabIndex: Int = 0
    var reelCreatorData: ReelCreatorId? = null
    private var followerCount:Long = 0L
    private var followingCount:Long = 0L

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_follower_following)
        initUI()
    }

    private fun initUI() {
         if (intent.getSerializableExtra(Constants.CREATOR_CONTENT_BUNDLE) != null) {
             reelCreatorData = intent.getSerializableExtra(Constants.CREATOR_CONTENT_BUNDLE) as ReelCreatorId
         }
        rotateImageLocaleWise(binding.backBtn)
        followingTabItem()
        updateFollowerTabItem(0)
        binding.tabLayout.tabRippleColor = null
        binding.userName.text = reelCreatorData?.title
        binding.backBtn.setOnClickListener { updateFollowFollowingCount() }
        val adapter = TabLayoutAdapter(supportFragmentManager, lifecycle)
        binding.viewPager.adapter = adapter
        binding.viewPager.offscreenPageLimit = 2
        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                binding.viewPager.currentItem = tab.position

            }
            override fun onTabUnselected(tab: TabLayout.Tab) {}
            override fun onTabReselected(tab: TabLayout.Tab) {}
        })
        binding.viewPager.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                binding.tabLayout.selectTab(binding.tabLayout.getTabAt(position))
            }
        })
        binding.viewPager.currentItem = tabIndex
        selectFollowFollowingPage()

        onBackPressedDispatcher.addCallback(this , object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                updateFollowFollowingCount()
            }
        })
    }

    private fun selectFollowFollowingPage() {
        val tabIndex = intent.getIntExtra(Constants.TAB_INDEX, 0)
        binding.tabLayout.setScrollPosition(tabIndex, 0f, true)
        binding.viewPager.currentItem = tabIndex
    }

    private fun followingTabItem() {
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText(getString(R.string.followers)))
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText(getString(R.string.following)))
    }

    fun updateFollowerTabItem(count:Long?){
        followerCount = count?:0L
        binding.tabLayout.getTabAt(0)?.text = "${count?.toSuffixFormat()} ${getString(R.string.followers)}"
    }

    fun updateFollowingTabItem(count:Long?){
        followingCount = count?:0L
        binding.tabLayout.getTabAt(1)?.text = "${count?.toSuffixFormat()} ${getString(R.string.following)}"
    }

    private fun updateFollowFollowingCount() {
        val intent = Intent().apply {
            putExtra(Constants.FOLLOWING_COUNT, followingCount)
            putExtra(Constants.FOLLOWER_COUNT, followerCount)
        }
        setResult(RESULT_OK, intent)
        finish()
    }

     fun openCreatorProfile(followingListItem: FollowingListItem?) {
        val reelCreatorId = ReelCreatorId()
        reelCreatorId.id = followingListItem?.creatorContentId?.toLong()
        reelCreatorId.externalIdentifier = followingListItem?.id.toString()
        reelCreatorId.posterUrl = followingListItem?.profilePicURL
        Intent(this, FollowFollowingProfileActivity::class.java).also {
            it.putExtra(Constants.CREATOR_CONTENT_BUNDLE, reelCreatorId)
            if (followingListItem?.profilePicURL != null){
                it.putExtra(Constants.IMAGE_URL, followingListItem.profilePicURL)
            }
            startActivity(it)
        }
    }
}