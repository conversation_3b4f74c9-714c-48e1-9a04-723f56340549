package com.enveu.activities.follow_follower

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.activities.follow_follower.adapter.FollowerAdapter
import com.enveu.activities.follow_follower.listener.FollowFollowingClickListener
import com.enveu.baseModels.BaseFragment
import com.enveu.databinding.FragmentFollowerBinding
import com.enveu.databinding.RemoveFollowerBottomSheetDialogBinding
import com.enveu.networking.response.FollowingListItem
import com.enveu.networking.response.FollowingListResponse
import com.enveu.networking.response.ReelCreatorId
import com.enveu.utils.Constants
import com.enveu.utils.PaginationScrollListener
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.show
import com.enveu.view_model.ShortsViewModel
import com.google.android.material.bottomsheet.BottomSheetDialog

class FollowerFragment : BaseFragment() {
    private lateinit var binding: FragmentFollowerBinding
    private lateinit var viewModel: ShortsViewModel
    private  var followerAdapter: FollowerAdapter? = null
    private var preference:KsPreferenceKeys? = null
    private var authToken:String = ""
    private var pageNumber:Int = 0
    private var pageSize:Int = 10
    private var allFollowerList:ArrayList<FollowingListItem?>? = ArrayList()
    private var tempFollowerList:ArrayList<FollowingListItem?>? = ArrayList()
    private val followerCount: MutableLiveData<Long> = MutableLiveData()
    private var reelCreatorData: ReelCreatorId? = null
    private var totalPages: Int = 0
    private var itemPosition:Int = -1



    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_follower, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()
    }

    private fun initUI() {
        preference = KsPreferenceKeys.getInstance()
        val parentActivityReference = (activity as FollowerFollowingActivity)
        reelCreatorData = parentActivityReference.reelCreatorData
        authToken = preference?.appPrefAccessToken.toString()
        viewModel = ViewModelProvider(this)[ShortsViewModel::class.java]
        binding.progressBar.show()
        getFollowerListApi()
        setFollowerCount()
        setObserver(viewModel)
    }

    private fun setFollowerCount() {
        followerCount.observe(viewLifecycleOwner) { followerCount ->
            (activity as FollowerFollowingActivity).updateFollowerTabItem(followerCount)
        }
    }


    private fun getFollowerListApi() {
        viewModel.getFollowersList(authToken, reelCreatorData?.externalIdentifier, pageNumber, pageSize, DESC).observe(viewLifecycleOwner) { followerData ->
            if (followerData.data?.items?.isNotEmpty() == true) {
                tempFollowerList = followerData.data.items
                pageNumber += 1
                followerCount.value = followerData?.data?.totalElements?:0L
                totalPages = followerData.data.totalPages?:0
                if (preference?.userId.toString() == reelCreatorData?.externalIdentifier){
                    binding.progressBar.hide()
                    tempFollowerList?.let { allFollowerList?.addAll(it) }
                    setFollowerAdapter()
                }else{
                    callUserInteractionApi(followerData)
                }
            }
            else{
                binding.progressBar.hide()
                followerCount.value = 0
                binding.noDataFound.show()
            }
        }
    }

    private fun callUserInteractionApi(followerData: FollowingListResponse?){
        val contentIds = StringBuilder()
        followerData?.data?.items?.forEach {
            it?.creatorContentId?.let { id->
                contentIds.append(id).append(",")
            }
        }
        viewModel.checkAndCallFollow(contentIds.removeSuffix(",").toString())
    }

    private fun setRemoveFollower(appUserId:Int?, position:Int){
        binding.progressBar.show()
        viewModel.setRemoveFollower(authToken, appUserId).observe(viewLifecycleOwner) { removeFollowerResponse->
            binding.progressBar.hide()
            if (removeFollowerResponse.responseCode == 2000){
                followerAdapter?.removeFollowerUser(position)
                followerCount.value = followerCount.value?.minus(1)
                if (followerAdapter?.itemCount == 0){
                    binding.noDataFound.show()
                }
                else{
                    binding.noDataFound.hide()
                }
            }
        }
    }

    private val removeFollowerClickListener = object : FollowFollowingClickListener {

        override fun onItemRemoveClickListener(position: Int) {
            openRemoveFollowerBottomDialog(position)
        }

        override fun onFollowFollowingClickListener(position: Int) {
            itemPosition = position
            binding.progressBar.show()
            if (allFollowerList?.get(position)?.isFollowing == true) {
                viewModel.setUnFollowUser(authToken, allFollowerList?.get(position)?.id)
            }
            else{
                viewModel.setFollowUser(authToken, allFollowerList?.get(position)?.id)
            }
        }

        override fun onCreatorItemClickListener(position: Int) {
            (activity as FollowerFollowingActivity).openCreatorProfile(allFollowerList?.get(position))
        }
    }

    private fun openRemoveFollowerBottomDialog(position: Int) {
        val followerItem = allFollowerList?.get(position)
        val dialog = BottomSheetDialog(requireActivity())
        val binding = RemoveFollowerBottomSheetDialogBinding.inflate(layoutInflater)
        dialog.setContentView(binding.root)
        Glide.with(requireActivity()).load(followerItem?.profilePicURL).placeholder(R.drawable.profile_avtar_logo).error(R.drawable.profile_avtar_logo).into(binding.userProfile)
        binding.titleText.text = getString(R.string.we_won_t_notify_albert, followerItem?.name)
        binding.cancelBtn.setOnClickListener {
            dialog.dismiss()
        }
        binding.removeBtn.setOnClickListener {
            setRemoveFollower(followerItem?.id, position)
            dialog.dismiss()
        }
        dialog.show()
    }

    private fun setObserver(viewModel: ShortsViewModel) {
        viewModel.userInteractionOfContentsResponse.observe(viewLifecycleOwner) { contents ->
            binding.progressBar.hide()
            contents.forEach { content ->
                tempFollowerList?.find { it?.creatorContentId == content.contentId }?.apply {
                    isFollowing = content.follow
                }
            }
            tempFollowerList?.let { allFollowerList?.addAll(it) }
            setFollowerAdapter()
        }

        viewModel.createFollowResponse.observe(viewLifecycleOwner){ followResponse ->
            binding.progressBar.hide()
            if (followResponse.responseCode == 2000){
                followerAdapter?.updateFollowFollowingState(itemPosition, true)
            }
        }

        viewModel.createUnFollowResponse.observe(viewLifecycleOwner){ followResponse ->
            binding.progressBar.hide()
            if (followResponse.responseCode == 2000){
                followerAdapter?.updateFollowFollowingState(itemPosition, false)
            }
        }
    }

    private fun callFollowerPagination(layoutManager: LinearLayoutManager) {
        binding.followerRecyclerView.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun onLoadMore(page: Int) {
                if (totalPages > page) {
                    getFollowerListApi()
                }
            }
        })
    }


    @SuppressLint("NotifyDataSetChanged")
    private fun setFollowerAdapter() {
        if (followerAdapter == null){
            followerAdapter = FollowerAdapter(requireActivity(), allFollowerList, reelCreatorData?.externalIdentifier, preference?.userId.toString(), removeFollowerClickListener)
            binding.followerRecyclerView.adapter = followerAdapter
            val layoutManager = LinearLayoutManager(requireActivity())
            binding.followerRecyclerView.layoutManager = layoutManager
            callFollowerPagination(layoutManager)
        }
        else{
            followerAdapter?.notifyDataSetChanged()
        }
    }

    companion object {
       const val DESC = "id:desc"
    }
}