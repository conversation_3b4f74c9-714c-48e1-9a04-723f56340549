package com.enveu.activities.follow_follower

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.enveu.R
import com.enveu.activities.follow_follower.adapter.FollowingAdapter
import com.enveu.activities.follow_follower.listener.FollowFollowingClickListener
import com.enveu.baseModels.BaseFragment
import com.enveu.databinding.FragmentFollowingBinding
import com.enveu.networking.response.FollowingListItem
import com.enveu.networking.response.FollowingListResponse
import com.enveu.networking.response.ReelCreatorId
import com.enveu.utils.Constants
import com.enveu.utils.PaginationScrollListener
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.show
import com.enveu.view_model.ShortsViewModel


class FollowingFragment : BaseFragment() {
    private lateinit var binding: FragmentFollowingBinding
    private lateinit var viewModel: ShortsViewModel
    private  var followingAdapter: FollowingAdapter? = null
    private var authToken:String = ""
    private var pageNumber:Int = 0
    private var pageSize:Int = 10
    private var preference:KsPreferenceKeys? = null
    private var allFollowingList:ArrayList<FollowingListItem?>? = ArrayList()
    private var tempFollowerList:ArrayList<FollowingListItem?>? = ArrayList()
    private var itemPosition:Int = -1
    private val followingCount: MutableLiveData<Long> =  MutableLiveData()
    private var reelCreatorData: ReelCreatorId? = null
    private var totalPages: Int = 0



    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_following, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()

    }

    private fun initUI() {
        preference = KsPreferenceKeys.getInstance()
        authToken = preference?.appPrefAccessToken.toString()
        val parentActivityReference = (activity as FollowerFollowingActivity)
        reelCreatorData = parentActivityReference.reelCreatorData
        viewModel = ViewModelProvider(this)[ShortsViewModel::class.java]
        binding.progressBar.show()
        getFollowingListApi()
        setObserver()
    }

    private val followFollowingClickListener = object: FollowFollowingClickListener {
        override fun onItemRemoveClickListener(position: Int) {

        }
        override fun onFollowFollowingClickListener(position: Int) {
            itemPosition = position
            binding.progressBar.show()
            if (allFollowingList?.get(position)?.isFollowing == true) {
                viewModel.setUnFollowUser(authToken, allFollowingList?.get(position)?.id)
            }
            else{
                viewModel.setFollowUser(authToken, allFollowingList?.get(position)?.id)
            }
        }

        override fun onCreatorItemClickListener(position: Int) {
            (activity as FollowerFollowingActivity).openCreatorProfile(allFollowingList?.get(position))
        }
    }

    private fun getFollowingListApi(){
        viewModel.getFollowingList(authToken, reelCreatorData?.externalIdentifier, pageNumber, pageSize, DESC).observe(viewLifecycleOwner) { followingData ->
           if (followingData.data?.items?.isNotEmpty() == true) {
               pageNumber += 1
               tempFollowerList = followingData.data.items
               followingCount.value = followingData?.data?.totalElements?:0L
               totalPages = followingData.data.totalPages?:0
               if (preference?.userId.toString() == reelCreatorData?.externalIdentifier){
                   binding.progressBar.hide()
                   tempFollowerList?.map { it?.isFollowing = true }
                   tempFollowerList?.let {allFollowingList?.addAll(it)  }
                   setFollowingAdapter()
               }
               else{
                   callUserInteractionApi(followingData)
               }
           }
            else{
                followingCount.value = 0L
                binding.progressBar.hide()
                binding.noDataFound.show()
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setFollowingAdapter() {
        if (followingAdapter == null) {
            followingAdapter = FollowingAdapter(requireActivity(), allFollowingList, reelCreatorData?.externalIdentifier, followFollowingClickListener)
            val layoutManager = LinearLayoutManager(requireActivity())
            binding.followingRecyclerView.layoutManager = layoutManager
            binding.followingRecyclerView.adapter = followingAdapter
            callFollowerPagination(layoutManager)
        }
        else{
            followingAdapter?.notifyDataSetChanged()
        }
    }

    private fun setObserver() {
      viewModel.createFollowResponse.observe(viewLifecycleOwner) { followResponse ->
          binding.progressBar.hide()
          if (followResponse.responseCode == 2000){
              followingAdapter?.updateFollowFollowingState(itemPosition, true)
              followingCount.value = followingCount.value?.plus(1)
          }
      }

        viewModel.createUnFollowResponse.observe(viewLifecycleOwner) { followResponse ->
            binding.progressBar.hide()
          if (followResponse.responseCode == 2000){
              followingAdapter?.updateFollowFollowingState(itemPosition, false)
              followingCount.value = followingCount.value?.minus(1)
          }
      }

        viewModel.userInteractionOfContentsResponse.observe(viewLifecycleOwner) { contents ->
            binding.progressBar.hide()
            contents.forEach { content ->
                tempFollowerList?.find { it?.creatorContentId == content.contentId }?.apply {
                    isFollowing = content.follow
                }
            }
            tempFollowerList?.let { allFollowingList?.addAll(it) }
            setFollowingAdapter()
        }

        followingCount.observe(viewLifecycleOwner){ followingCount ->
            (activity as FollowerFollowingActivity).updateFollowingTabItem(followingCount)
        }
    }

    private fun callFollowerPagination(layoutManager: LinearLayoutManager) {
        binding.followingRecyclerView.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun onLoadMore(page: Int) {
                if (totalPages > page) {
                    getFollowingListApi()
                }
            }
        })
    }

    private fun callUserInteractionApi(followingData: FollowingListResponse?){
        val contentIds = StringBuilder()
        followingData?.data?.items?.forEach {
            it?.creatorContentId?.let { id->
                contentIds.append(id).append(",")
            }
        }
        viewModel.checkAndCallFollow(contentIds.removeSuffix(",").toString())
    }

    companion object {
        const val DESC = "id:desc"
    }
}