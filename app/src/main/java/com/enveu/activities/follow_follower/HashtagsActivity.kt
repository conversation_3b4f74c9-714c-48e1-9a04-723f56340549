package com.enveu.activities.follow_follower

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.enveu.R
import com.enveu.activities.follow_follower.adapter.ProfileReelsAdapter
import com.enveu.baseModels.BaseActivity
import com.enveu.callbacks.ReelItemClickListener
import com.enveu.databinding.ActivityHastagsBinding
import com.enveu.networking.response.ReelsContentItem
import com.enveu.utils.Constants
import com.enveu.utils.PaginationGridScrollListener
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show
import com.enveu.view_model.ShortsViewModel

class HashtagsActivity : BaseActivity() {
    private lateinit var binding : ActivityHastagsBinding
    private lateinit var viewModel: ShortsViewModel
    private var profileReelsAdapter: ProfileReelsAdapter? = null
    private var creatorReelsList:ArrayList<ReelsContentItem?>? = ArrayList()
    private var offSet: Int = 0
    private var totalPages : Int? = 0
    private var hasReelsData:Boolean = false
    private var hashtag : String = ""
    private var totalPostsCount : Int? = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_hastags)
        initUi()
    }

    private fun initUi() {
        hashtag = intent.getStringExtra(Constants.HASHTAGS).toString()
        viewModel = ViewModelProvider(this)[ShortsViewModel::class.java]
        getHashtagsReels()
        rotateImageLocaleWise(binding.backBtn)
        binding.backBtn.setOnClickListener {
            finish()
        }
        binding.hashtagsName.text = hashtag
        binding.progressBar.show()
    }

    private fun getHashtagsReels(){
        viewModel.getHasTagsReels(hashtag, Constants.CONTENT_TYPE_VIDEO, Constants.REEL, offSet, 10, true).observe(this){ creatorShortsData ->
            if (creatorShortsData?.data?.items?.size != 0){
                binding.progressBar.hide()
                creatorShortsData?.data?.items?.let { creatorReelsList?.addAll(it) }
                totalPages = creatorShortsData?.data?.pageInfo?.pages
                totalPostsCount = creatorShortsData?.data?.pageInfo?.total
                hasReelsData = true
                offSet += 10
                setHashtagsReelsAdapter()
            }
            else{
                if (!hasReelsData) {
                    binding.noDataFound.show()
                }
            }
        }
        binding.postsNumber.text = totalPostsCount.toString()
    }

    private fun callGetReelsPagination(layoutManager: GridLayoutManager) {
        binding.reelsRecyclerView.addOnScrollListener(object : PaginationGridScrollListener(layoutManager) {
            override fun onLoadMore(page: Int, totalItemsCount: Int) {
                if ((totalPages?:0) > page) {
                    getHashtagsReels()
                }
            }
        })
    }

    private val reelsContentItem = object : ReelItemClickListener {
        override fun onReelItemClickListener(position: Int) {
            openShortsFragment(creatorReelsList?.get(position))
        }
    }

    private fun openShortsFragment(reelsContentItem: ReelsContentItem?) {
        val intent = Intent().apply {
            putExtra(Constants.SHORTS_REELS_BUNDLE, reelsContentItem)
            putExtra(Constants.OPEN_SHORTS_FRAGMENT, true)
        }
        setResult(RESULT_OK, intent)
        finish()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setHashtagsReelsAdapter() {
        if (profileReelsAdapter == null) {
            RecyclerAnimator(this).animate(binding.reelsRecyclerView)
            profileReelsAdapter = ProfileReelsAdapter(this, creatorReelsList, reelsContentItem)
            // need to change tablet and mobile case:span count
            val layoutManager = GridLayoutManager(this, 3)
            binding.reelsRecyclerView.layoutManager = layoutManager
            binding.reelsRecyclerView.adapter = profileReelsAdapter
            callGetReelsPagination(layoutManager)
        }
        else{
            profileReelsAdapter?.notifyDataSetChanged()
        }
    }
}