package com.enveu.activities.follow_follower

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.enveu.R
import com.enveu.activities.follow_follower.adapter.ProfileReportAdapter
import com.enveu.activities.follow_follower.listener.OnProfileReportListener
import com.enveu.baseModels.BaseActivity
import com.enveu.databinding.ActivityProfileReportBinding
import com.enveu.networking.response.ProfileReportItem
import com.enveu.utils.Constants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show
import com.enveu.view_model.ShortsViewModel
import com.google.gson.JsonObject
import java.util.Locale

class ProfileReportActivity : BaseActivity() {
    private lateinit var binding: ActivityProfileReportBinding
    private lateinit var viewModel: ShortsViewModel
    private var profileReportAdapter:ProfileReportAdapter? = null
    private var allProfileReportList:ArrayList<ProfileReportItem?>? = ArrayList()
    private var profileReportItem:ProfileReportItem? = null
    private var preference:KsPreferenceKeys? = null
    private var entityType:String? = null
    private var entityId:String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProfileReportBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initUI()
    }

    private fun initUI() {
        preference = KsPreferenceKeys.getInstance()
        binding.progressBar.show()
        rotateImageLocaleWise(binding.iconBackPress)
        entityType = intent.getStringExtra(Constants.ENTITY_TYPE)
        entityId = intent.getStringExtra(Constants.ENTITY_ID)
        viewModel = ViewModelProvider(this)[ShortsViewModel::class.java]
        viewModel.getAllReasons(entityType).observe(this){ allProfileReportData ->
            binding.progressBar.hide()
            if (allProfileReportData.data?.items?.isNotEmpty() == true){
                allProfileReportList?.addAll(allProfileReportData.data.items)
                allProfileReportList  = allProfileReportList?.sortedBy {it?.reasonOrder}?.let{ ArrayList(it) }
                setProfileReportAdapter()
            }
        }

        binding.iconBackPress.setOnClickListener {
            finish()
        }
        binding.submitText.setOnClickListener {
              profileReportItem = profileReportAdapter?.getSelectedReportItem()
              submitSelectedReason()
        }
    }

    private fun submitSelectedReason() {
        binding.progressBar.show()
        val jsonObject = JsonObject()
        jsonObject.addProperty(ENTITY_ID, entityId)
        jsonObject.addProperty(ENTITY_TYPE, entityType)
        jsonObject.addProperty(REASON_ID, profileReportItem?.id)
        jsonObject.addProperty(OTHER, profileReportItem?.customReason)
        viewModel.selectReasonSubmit(preference?.appPrefAccessToken.toString(), jsonObject).observe(this) { submitReasonResponse ->
            binding.progressBar.hide()
            if (submitReasonResponse.responseCode == 2001){
                if (entityType == Constants.CONTENT) {
                    Toast.makeText(this, getString(R.string.content_reported_successfully), Toast.LENGTH_LONG).show()
                }
                else{
                    Toast.makeText(this, getString(R.string.user_reported_successfully), Toast.LENGTH_LONG).show()
                }
                finish()
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setProfileReportAdapter(){
        if (profileReportAdapter == null) {
            profileReportAdapter = ProfileReportAdapter(allProfileReportList)
            binding.profileReportRecyclerView.layoutManager = LinearLayoutManager(this)
            binding.profileReportRecyclerView.adapter = profileReportAdapter
        }
        else{
            profileReportAdapter?.notifyDataSetChanged()
        }
    }

    companion object {
        const val ENTITY_ID = "entityId"
        const val ENTITY_TYPE = "entityType"
        const val REASON_ID = "reasonId"
        const val OTHER = "other"
    }

}