package com.enveu.activities.follow_follower.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.activities.follow_follower.adapter.FollowingAdapter.ViewHolder
import com.enveu.activities.follow_follower.listener.FollowFollowingClickListener
import com.enveu.databinding.ItemFollowerLayoutBinding
import com.enveu.networking.response.FollowingListItem
import com.enveu.utils.hide
import com.enveu.utils.show


class FollowerAdapter(
    private val context: Context,
    private val followerList: ArrayList<FollowingListItem?>?,
    private val  reelCreatorId:String?,
    private val  primaryUserId:String?,
    private val followFollowingClickListener: FollowFollowingClickListener,
) : RecyclerView.Adapter<FollowerAdapter.ViewFollower>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewFollower {
        val binding = ItemFollowerLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewFollower(binding)
    }

    override fun onBindViewHolder(holder: ViewFollower, position: Int) {
        val followerData = followerList?.get(position)
        holder.binding.apply {
            userName.text = followerData?.userName
            displayName.text = followerData?.name
            Glide.with(context).load(followerData?.profilePicURL).placeholder(R.drawable.profile_avtar_logo).error(R.drawable.profile_avtar_logo).into(userProfilePic)
            if (primaryUserId == followerData?.id.toString()){ remove.hide() }
            else { remove.show() }

            if (reelCreatorId == primaryUserId){
                remove.text = context.getString(R.string.remove)
            } else{
                if (followerData?.isFollowing == true) {
                    remove.text = context.getString(R.string.following)
                    remove.background = ContextCompat.getDrawable(context, R.drawable.following_background)
                } else {
                    remove.text =  context.getString(R.string.follow)
                    remove.background = ContextCompat.getDrawable(context, R.drawable.follower_background)
                }
            }
        }
    }

    override fun onBindViewHolder(holder: ViewFollower, position: Int, payloads: MutableList<Any>) {
        if (payloads.isEmpty()) {
            onBindViewHolder(holder, position)
        } else {
            val payload = payloads[0]
            if (payload is Boolean) {
                holder.binding.apply {
                    if (payload == true) {
                        remove.text = context.getString(R.string.following)
                        remove.background = ContextCompat.getDrawable(context, R.drawable.following_background)
                    } else {
                        remove.text =  context.getString(R.string.follow)
                        remove.background = ContextCompat.getDrawable(context, R.drawable.follower_background)
                    }
                }
            }
        }
    }

    fun updateFollowFollowingState(position: Int, isFollowing: Boolean) {
        followerList?.get(position)?.isFollowing = isFollowing
        notifyItemChanged(position, isFollowing)
    }

    override fun getItemCount(): Int = followerList?.size?:0

    @SuppressLint("NotifyDataSetChanged")
    fun removeFollowerUser(position: Int) {
        followerList?.removeAt(position)
        notifyItemRemoved(position)
        notifyDataSetChanged()
    }

    inner class ViewFollower(val binding: ItemFollowerLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.remove.setOnClickListener {
                if (reelCreatorId == primaryUserId){
                    followFollowingClickListener.onItemRemoveClickListener(bindingAdapterPosition)
                } else {
                    followFollowingClickListener.onFollowFollowingClickListener(bindingAdapterPosition)
                }
            }
            binding.parentLayout.setOnClickListener {
                followFollowingClickListener.onCreatorItemClickListener(bindingAdapterPosition)
            }
        }
    }
}

