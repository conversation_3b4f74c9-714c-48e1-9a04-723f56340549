package com.enveu.activities.follow_follower.adapter

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.enveu.R
import com.enveu.networking.response.FollowingListItem
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class FollowerBottomSheet(
    private val follower: FollowingListItem,
    private val onRemoveClick: () -> Unit
) : BottomSheetDialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = inflater.inflate(R.layout.remove_follower_bottom_sheet_dialog, container, false)

//        val displayName = view.findViewById<TextView>(R.id.displayNameTextView)
//        val removeBtn = view.findViewById<Button>(R.id.removeBtn)
//
//        displayName.text = follower.displayName

//        removeBtn.setOnClickListener {
//            onRemoveClick()
//            dismiss()
//        }

        return view
    }
}