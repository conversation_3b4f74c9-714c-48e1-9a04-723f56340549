package com.enveu.activities.follow_follower.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.activities.follow_follower.listener.FollowFollowingClickListener
import com.enveu.databinding.ItemFollowerLayoutBinding
import com.enveu.networking.response.FollowingListItem
import com.enveu.utils.hide
import com.enveu.utils.show


class FollowingAdapter(
    private val context: Context,
    private val followingList: ArrayList<FollowingListItem?>?,
    private val appUserId:String?,
    private val followFollowingClickListener: FollowFollowingClickListener
) : RecyclerView.Adapter<FollowingAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemFollowerLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val followingItem = followingList?.get(position)
        holder.binding.apply {
            userName.text = followingItem?.userName
            displayName.text = followingItem?.name
            Glide.with(context).load(followingItem?.profilePicURL).placeholder(R.drawable.profile_avtar_logo).error(R.drawable.profile_avtar_logo).into(userProfilePic)
            if (appUserId == followingItem?.id.toString()){ remove.hide() }
            else { remove.show() }
            if (followingItem?.isFollowing == true) {
                 remove.text = context.getString(R.string.following)
                 remove.background = ContextCompat.getDrawable(context, R.drawable.following_background)
            } else {
                 remove.text =  context.getString(R.string.follow)
                remove.background = ContextCompat.getDrawable(context, R.drawable.follower_background)
            }
        }
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isEmpty()) {
            onBindViewHolder(holder, position)
        } else {
            val payload = payloads[0]
            if (payload is Boolean) {
                holder.binding.apply {
                    if (payload == true) {
                        remove.text = context.getString(R.string.following)
                        remove.background = ContextCompat.getDrawable(context, R.drawable.following_background)
                    } else {
                        remove.text =  context.getString(R.string.follow)
                        remove.background = ContextCompat.getDrawable(context, R.drawable.follower_background)
                    }
                }
            }
        }
    }


    fun updateFollowFollowingState(position: Int, isFollowing: Boolean) {
        followingList?.get(position)?.isFollowing = isFollowing
        notifyItemChanged(position, isFollowing)
    }

    override fun getItemCount(): Int = followingList?.size?:0

    inner class ViewHolder(val binding: ItemFollowerLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.remove.setOnClickListener {
                followFollowingClickListener.onFollowFollowingClickListener(bindingAdapterPosition)
            }
            binding.parentLayout.setOnClickListener {
                followFollowingClickListener.onCreatorItemClickListener(bindingAdapterPosition)
            }
        }
    }
}