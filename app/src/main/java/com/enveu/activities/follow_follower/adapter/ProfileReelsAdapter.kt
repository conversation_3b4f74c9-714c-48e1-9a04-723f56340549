package com.enveu.activities.follow_follower.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.callbacks.ReelItemClickListener
import com.enveu.databinding.ItemShortLayoutBinding
import com.enveu.networking.response.ReelsContentItem
import com.enveu.utils.Constants
import com.enveu.utils.getImageUrl
import com.enveu.utils.setImageWithGlide


class ProfileReelsAdapter(
    private val context: Context,
    private val creatorReelsList: ArrayList<ReelsContentItem?>?,
    private val reelItemClickListener: ReelItemClickListener,
) : RecyclerView.Adapter<ProfileReelsAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemShortLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }


    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val creatorReelsItem = creatorReelsList?.get(position)
        holder.binding.apply {
            if (!creatorReelsItem?.images.isNullOrEmpty()) {
                itemImage.setImageWithGlide(context, getImageUrl(Constants.NINE_INTO_SIXTEEN,  creatorReelsItem?.images, itemImage.context))
            }
            viewCount.text = creatorReelsItem?.playCount.toString()
            val playCount: Int? = creatorReelsItem?.playCount
            if (playCount != null) {
                if (playCount > 0) {
                    viewCount.setVisibility(View.VISIBLE)
                } else {
                    viewCount.setVisibility(View.GONE)
                }
            }
        }
    }

    override fun getItemCount(): Int = creatorReelsList?.size?:0

    inner class ViewHolder(val binding: ItemShortLayoutBinding) : RecyclerView.ViewHolder(binding.root){
        init {
            binding.rippleView.setOnClickListener {
                reelItemClickListener.onReelItemClickListener(bindingAdapterPosition)
            }
        }
    }
}