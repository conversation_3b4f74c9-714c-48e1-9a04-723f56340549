package com.enveu.activities.follow_follower.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.EditText
import androidx.recyclerview.widget.RecyclerView
import com.enveu.activities.follow_follower.listener.OnProfileReportListener
import com.enveu.databinding.ItemProfileReportLayoutBinding
import com.enveu.networking.response.ProfileReportItem
import com.enveu.utils.Constants
import com.enveu.utils.hide
import com.enveu.utils.show

class ProfileReportAdapter(
    private val allProfileReportList: ArrayList<ProfileReportItem?>?,
) :RecyclerView.Adapter<ProfileReportAdapter.ViewHolder>() {

    private var selectedPosition:Int = -1

    private var customReasonEditText:EditText? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemProfileReportLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun getItemCount(): Int {
       return allProfileReportList?.size?:0
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
       val allProfileReport = allProfileReportList?.get(position)
        holder.binding.antiSocialText.text = allProfileReport?.reason

        if (position == allProfileReportList?.size?.minus(1)){
            holder.binding.viewLine.hide()
        }
        else{
            holder.binding.viewLine.show()
        }

        if (selectedPosition == position){
            if (allProfileReport?.reason.equals(Constants.OTHER, true)){
                holder.binding.selectionClick.show()
                customReasonEditText = holder.binding.typeHereEt
                holder.binding.typeHereEt.show()
            }
            else{
                holder.binding.selectionClick.show()
                holder.binding.typeHereEt.hide()
            }
        }
        else{
            holder.binding.selectionClick.hide()
            holder.binding.typeHereEt.hide()
        }
    }

    fun getSelectedReportItem(): ProfileReportItem? {
        var profileReportItem:ProfileReportItem? = ProfileReportItem()
        if (selectedPosition != -1){
            profileReportItem = allProfileReportList?.get(selectedPosition)
            profileReportItem?.customReason = customReasonEditText?.text.toString().trim()
            if (profileReportItem?.customReason.isNullOrEmpty() || profileReportItem?.customReason == "null"){
                profileReportItem?.customReason = null
            }
            customReasonEditText?.text = null
        }
        return profileReportItem
    }

   @SuppressLint("NotifyDataSetChanged")
   inner class ViewHolder(val binding: ItemProfileReportLayoutBinding):RecyclerView.ViewHolder(binding.root){
       init {
           binding.parentLayout.setOnClickListener {
               selectedPosition = bindingAdapterPosition
               notifyDataSetChanged()
           }
       }
   }
}