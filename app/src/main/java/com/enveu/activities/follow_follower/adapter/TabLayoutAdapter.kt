package com.enveu.activities.follow_follower.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.enveu.activities.follow_follower.FollowerFragment
import com.enveu.activities.follow_follower.FollowingFragment


class TabLayoutAdapter(fragmentManager: FragmentManager, lifecycle: Lifecycle) :
    FragmentStateAdapter(fragmentManager, lifecycle) {
    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> {
                FollowerFragment()
            }

            1 -> {
                FollowingFragment()
            }

            else -> {
                FollowerFragment()
            }
        }
    }

    override fun getItemCount(): Int {
        return 2
    }
}
