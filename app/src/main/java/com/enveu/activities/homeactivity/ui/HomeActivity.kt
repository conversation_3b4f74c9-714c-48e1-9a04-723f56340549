package com.enveu.activities.homeactivity.ui

import android.Manifest
import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.telephony.TelephonyManager
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.NonNull
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.BuildConfig
import com.enveu.OttApplication
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.PlayableCallBack
import com.enveu.activities.detail.ui.DetailActivity
import com.enveu.activities.detail.ui.LiveLinearDetailsActivity
import com.enveu.activities.device_management.DeviceManagementViewModel
import com.enveu.activities.homeactivity.ApiInterface
import com.enveu.activities.listing.ui.MyListActivity
import com.enveu.activities.mainPLayer.MainPlayerActivity
import com.enveu.activities.multiplePlaylist.MyPlaylistFragment
import com.enveu.activities.new_search.NewSearchFragment
import com.enveu.activities.purchase.plans_layer.GetPlansLayer
import com.enveu.activities.search.ui.ActivitySearch
import com.enveu.activities.service.BackgroundAudioService
import com.enveu.activities.splash.ui.ActivitySplash
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.baseModels.HomeBaseBinding
import com.enveu.beanModel.drm.DRM
import com.enveu.beanModel.emptyResponse.ResponseEmpty
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModel.responseGetWatchlist.ResponseGetIsWatchlist
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent
import com.enveu.beanModelV3.videoDetailV3.queue_response.AddToQueueRequestModel
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.client.api_callback.NetworkResultCallback
import com.enveu.client.utils.ClickHandler
import com.enveu.databinding.ActivityMainBinding
import com.enveu.epg.EPGFragment
import com.enveu.epg.EPGUtils
import com.enveu.epg.models.EPGItem
import com.enveu.epg.models.EPGResponseModel
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.home.ui.HomeFragment
import com.enveu.fragments.more.ui.MoreFragment
import com.enveu.fragments.myList.ui.MyListFragment
import com.enveu.fragments.player.ui.AudioInteractionFragment
import com.enveu.fragments.search.SearchFragment
import com.enveu.homeSubMenu.SubMenuRecycleView
import com.enveu.jwplayer.extension.percent
import com.enveu.menuManager.MenuCommonFunction
import com.enveu.menuManager.model.MenuManagerModel.Data.OrderedMenuItem
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.response.ReelsContentItem
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.networking.servicelayer.APIServiceLayer
import com.enveu.ugc.GridListActivity
import com.enveu.ugc.ShortVideoUploadActivity
import com.enveu.ugc.UgcFragment
import com.enveu.utils.Constants
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.AppColors
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.config.bean.dmsResponse.ConfigBean
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.ActivityTrackers
import com.enveu.utils.helpers.AnalyticsController
import com.enveu.utils.helpers.BottomLyricsDialogFragment
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.QueueBottomDialogFragment
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.StringUtils
import com.enveu.utils.helpers.ToastHandler
import com.enveu.utils.helpers.ToolBarHandler
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.inAppUpdate.AppUpdateCallBack
import com.enveu.utils.inAppUpdate.ApplicationUpdateManager
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.enveu.view_model.ShortsViewModel
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.navigation.NavigationBarView
import com.google.android.material.snackbar.Snackbar
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.install.InstallState
import com.google.android.play.core.install.InstallStateUpdatedListener
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.InstallStatus
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.jwplayer.pub.api.JWPlayer
import com.jwplayer.pub.api.PlayerState
import com.jwplayer.pub.api.license.LicenseUtil
import com.jwplayer.pub.view.JWPlayerView
import com.npaw.youbora.lib6.jwplayer.JWPlayerAdapter
import com.npaw.youbora.lib6.jwplayer.JWPlayerAdsAdapter
import com.npaw.youbora.lib6.plugin.Options
import com.npaw.youbora.lib6.plugin.Plugin
import com.sdk.imgly.VideoImgActivity
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import retrofit2.Response
import org.joda.time.DateTime
import java.util.Collections
import java.util.Objects


class HomeActivity : HomeBaseBinding<ActivityMainBinding?>(), AppUpdateCallBack,
    BackgroundAudioService.PlayerTimeCallBack, MainPlayerActivity.PlayerListener,
    OnSongItemClick, CommonDialogFragment.EditDialogListener,
    MoreFragment.OnMyListInteractionListener {
    private var backStakeValue: Int = 1
    private var stringList: List<OrderedMenuItem>? = null
    private var featureList: FeatureFlagModel? = null
    private var active: Fragment? = null
    private var tabId: String? = null
    private var preference: KsPreferenceKeys? = null
    private var homeFragment: Fragment? = null
    private var moreFragment: Fragment? = null
    private var ugcFragment: Fragment? = null
    private var playListFragment: Fragment? = null
    private var epgFragment: Fragment? = null
    private lateinit var bottomNavigationView: BottomNavigationView
    private val fragmentList: HashMap<Int, Fragment> = HashMap()
    private var menuItemActionType: String? = null
    private var bookmarkingViewModel: BookmarkingViewModel? = null
    private var personalizedValue: String? = null
    private var type: String? = null
    private var isMoreGroup = false
    private var position = 0
    private var epgDay=0
    private var fragmentManager: FragmentManager? = null
    private var mService: BackgroundAudioService.ServiceBinder? = null
    private var mPlayerView: JWPlayerView? = null
    private var mIsBound = false
    private var externalRefId: String = ""
    private var assetId: Int = 0
    private var videoDuration: Long = 0
    private var currentPlayerTime: Double = 0.0
    var songList = mutableListOf<DataItem>()
    private var previousSongList: List<DataItem> = ArrayList()
    private var imageContent: ImageContent? = null
    private var src: String? = null
    private var title: String? = null
    private var song: DataItem? = null
    private var description: String? = null
    private var allContentIdList: ArrayList<Int>? = ArrayList()
    var railInjectionHelper: RailInjectionHelper? = null
    private val dynamicBottomNavigation = CompositeDisposable()
    private var videoDetails: EnveuVideoItemBean? = null
    private var reelsContentItem: ReelsContentItem? = null
    private var isLoggedOut = false
    private var fromRedirection: Boolean? = false
    private var contentSlugFromRedirection: String? = null
    private val colorsHelper by lazy { ColorsHelper }
    private val stringsHelper by lazy { StringsHelper }
    private var plugin: Plugin? = null
    private lateinit var assetType: String
    private var isBackPressedCallFromMoreFragment: Boolean = false
    private var id: Int = 0
    private var from: Boolean = false
    private var isLoggedIn: Boolean = false
    private var isPauseMiniPLayer: Boolean = false
    private var isSlugContent: Boolean = false
    private var isRelatedContentApi: Boolean = true
    private var relatedContentSize: Int = 0
    private var deviceManagementViewModel: DeviceManagementViewModel? = null
    private var countDownTimer: CountDownTimer? = null
    private var startSessionApiStart = false
    private var concurrancyError = false
    private var openShortsFragment: Boolean? = false
    private lateinit var exportLauncher: ActivityResultLauncher<Intent>



    private val mConnection: ServiceConnection = object : ServiceConnection {
        @SuppressLint("UseCompatLoadingForDrawables")
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
//            mService = service as BackgroundAudioService.ServiceBinder
//            title?.let {
//                mService?.createPlayer(
//                    this@HomeActivity, isPauseMiniPLayer, externalRefId, this@HomeActivity, src,
//                    it
//                )
//            }
////            mService?.createPlayer(this@HomeActivity, externalRefId, this@HomeActivity,src,
////                "Title"
////            )
//            mPlayerView = mService?.getPlayerView()
//            addToWatchHistory()
//            val startIntent = Intent(this@HomeActivity, BackgroundAudioService::class.java)
//            startIntent.putExtra(BackgroundAudioService.ACTION, BackgroundAudioService.ACTION_START)
//            startForegroundService(startIntent)
//            mIsBound = true
        }

        override fun onServiceDisconnected(name: ComponentName) {
            mIsBound = false
        }
    }

    private val phoneStateReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            if (intent.action == TelephonyManager.ACTION_PHONE_STATE_CHANGED) {
                val state = intent.getStringExtra(TelephonyManager.EXTRA_STATE)

                if (TelephonyManager.EXTRA_STATE_RINGING == state) {
                    Log.d("TelephonyManager", "Incoming call detected!")
                    playPauseThePlayer(false)
                } else if (TelephonyManager.EXTRA_STATE_OFFHOOK == state) {
                    Log.d("TelephonyManager", "Call picked up")
                } else if (TelephonyManager.EXTRA_STATE_IDLE == state) {
                    Log.d("TelephonyManager", "Call ended or idle")
                    playPauseThePlayer(true)
                }
            }
        }
    }

    private fun requestPermissionPhoneState() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
        }
        else if (shouldShowRequestPermissionRationale(Manifest.permission.READ_PHONE_STATE)) {
        }
        else {
            requestPermissionLauncher.launch(Manifest.permission.READ_PHONE_STATE)
        }
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityMainBinding {
        return ActivityMainBinding.inflate(inflater)
    }

    private val gridListLauncher: ActivityResultLauncher<Intent> = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                if (result.data?.getBooleanExtra(Constants.OPEN_SHORTS_FRAGMENT, false) == true) {
                    val reelsContentItem = result?.data?.getSerializableExtra(Constants.SHORTS_REELS_BUNDLE) as ReelsContentItem
                    openShortsReelsFragment(reelsContentItem)
                }
            }
        }

    private val requestPermissionLauncher: ActivityResultLauncher<String> =
        registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted: Boolean? -> }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPermission()
        } else {
            Logger.e("First_Screen", "Device_below_Android_13")
        }
        ToolBarHandler.getInstance().setSubMenuAction(binding, View.VISIBLE)
        preference = KsPreferenceKeys.getInstance()
        deviceManagementViewModel = ViewModelProvider(this)[DeviceManagementViewModel::class.java]
        setViewModel()
        val featureList = AppConfigMethod.parseFeatureFlagList()
        KsPreferenceKeys.getInstance().concurrencyEnable =
            featureList.featureFlag.IS_CONCURRENCY_ENABLE
        if (preference?.appPrefLoginStatus.equals(
                AppConstants.UserStatus.Login.toString(),
                ignoreCase = true
            )
        ) {
            isLoggedIn = true
            checkPlansForUser()
        }
        val strCurrentTheme = KsPreferenceKeys.getInstance().currentTheme
        binding?.toolbar?.colorsData = colorsHelper
        binding?.colorsData = colorsHelper
        val extras = intent
        if (extras != null) {
            position = intent.getIntExtra(AppConstants.HOME_TAG, 0)
            assetId = intent.getIntExtra(AppConstants.ASSET_ID, 0)
            fromRedirection = intent.getBooleanExtra(AppConstants.FROM_REDIRECTION, false)
            if (fromRedirection!!) {
                contentSlugFromRedirection = intent.getStringExtra(AppConstants.BUNDLE_CONTENT_SLUG)
                contentType = intent.getStringExtra(AppConstants.CONTENT_TYPE).toString()
            }
        }
        Logger.d("CurrentThemeIs", strCurrentTheme)
        if (AppConstants.LIGHT_THEME.equals(strCurrentTheme, ignoreCase = true)) {
            setTheme(R.style.MyMaterialTheme_Base_Light)
        } else {
            setTheme(R.style.MyMaterialTheme_Base_Dark)
        }
        Log.d("TokenUser", preference?.appPrefAccessToken.toString())
        if (KsPreferenceKeys.getInstance().contentSlug != null && KsPreferenceKeys.getInstance().contentSlug != "") {
            getSlugContentDetail()
        } else {
            if (assetId != 0 && !contentType.equals("")) {
                getContentDetail()
            }
        }
        getAppLevelJsonData()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && featureList?.featureFlag?.IS_PHONE_STATE_RECEIVE == true) {
            requestPermissionPhoneState()
        }
        setupBottomNavigationView()
        callInitials()
        ApplicationUpdateManager.getInstance(applicationContext).setAppUpdateCallBack(this)
        ApplicationUpdateManager.getInstance(applicationContext).appUpdateManager.registerListener(
            listener
        )
        ApplicationUpdateManager.getInstance(applicationContext).isUpdateAvailable()
        ApplicationUpdateManager.getInstance(applicationContext).appUpdateManager.appUpdateInfo.addOnSuccessListener { appUpdateInfo: AppUpdateInfo ->
            if (appUpdateInfo.installStatus() == InstallStatus.DOWNLOADED) {
                popupSnackbarForCompleteUpdate()
                ApplicationUpdateManager.getInstance(applicationContext).appUpdateManager.unregisterListener(
                    listener
                )
            }
        }
        AnalyticsController(this@HomeActivity)
            .callAnalytics("home_activity", "Action", "Launch")

        if (BuildConfig.FLAVOR.equals("qa", ignoreCase = true)) {
            LicenseUtil().setLicenseKey(this, AppConstants.QA_LICENSE_KEY)
        } else {
            LicenseUtil().setLicenseKey(this, AppConstants.PROD_LICENSE_KEY)
        }
        binding?.miniPlayer?.setOnClickListener {
            popMainPlayer()
            binding?.navigation?.visibility = View.INVISIBLE
        }

        binding?.miniPlayerViews?.epTimeBar?.max = 100

        // Disabled Seekbar Dragging
        binding?.miniPlayerViews?.epTimeBar?.setOnTouchListener { _, event ->
            // Prevent seek bar from being dragged
            event.action == MotionEvent.ACTION_MOVE
        }
        binding?.miniPlayerViews?.playPause?.setOnClickListener {
            if (mService != null) {
                var isPlaying = mService!!.getPlayPauseState()
                updatePlayPauseIcon(isPlaying)
            }
        }

        binding?.miniPlayerViews?.playNext?.setOnClickListener {
            playNextEpisode()
        }

        if (featureList?.featureFlag?.IS_QUEUE_ON_MINI_PLAYER == true) {
            binding?.miniPlayerViews?.mic?.visibility = View.VISIBLE
        } else {
            binding?.miniPlayerViews?.mic?.visibility = View.GONE
        }
        binding?.miniPlayerViews?.viewQueueList?.setOnClickListener {
            try {
                onQueue()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        if (featureList?.featureFlag?.LYRICS_MINI_PLAYER == true) {
            binding?.miniPlayerViews?.mic?.visibility = View.VISIBLE
        } else {
            binding?.miniPlayerViews?.mic?.visibility = View.GONE
        }

        binding?.miniPlayerViews?.mic?.setOnClickListener {
            val bottomSheetDialog = BottomLyricsDialogFragment.getInstance(song, "")
            bottomSheetDialog.show(this.supportFragmentManager, "Bottom Sheet Dialog Fragment")
        }

        if (!contentType.isNullOrEmpty() && (!contentSlugFromRedirection.isNullOrEmpty() || assetId != null)) {
            AppCommonMethod.launchArtistAndAlbum(
                this,
                contentType!!,
                contentSlugFromRedirection!!,
                assetId
            )
        }
        getdata()

        if (featureList?.featureFlag?.IS_PHONE_STATE_RECEIVE == true) {
            val filter = IntentFilter(TelephonyManager.ACTION_PHONE_STATE_CHANGED)
            registerReceiver(phoneStateReceiver, filter)
        }

        if (KsPreferenceKeys.getInstance().reel) {
            getSlugContentDetail()
        }
        if (KsPreferenceKeys.getInstance().live){
            getSlugContentDetail()
        }


        exportLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                val path = result.data?.getStringExtra("extra_video_path") // <-- use correct key
                Log.d("HomeActivity", "✅ Video exported at: $path")

                // Optional: launch ShareActivity
                path?.let {
                    val intent = Intent(this, ShortVideoUploadActivity::class.java)
                    intent.putExtra("filePath", it)
                    intent.putExtra("fromImgly", true)
                    shortVideoLauncher.launch(intent)
                }
            }
        }
    }


    fun playPauseThePlayer(isplay: Boolean) {
        if (mService != null) {
            var isPlaying = false
            if (isplay) {
                isPlaying = mService!!.setPlayState()
            } else {
                isPlaying = mService!!.setPauseState()
            }
            updatePlayPauseIcon(isPlaying)
        }
    }

    private fun getdata() {
        val intent = intent
        assetType = intent.getStringExtra("assetType") ?: ""
        contentSlug = intent.getStringExtra(AppConstants.BUNDLE_CONTENT_SLUG) ?: ""
        id = intent.getIntExtra("id", 0)
        from = intent.getBooleanExtra("from", false)
        if (from) {
            if (assetType != null && contentSlug != null && id != null) {
                AppCommonMethod.launchArtistAndAlbum(this, assetType, contentSlug!!, id)
            }
        }
    }

    override fun onQueue() {
        if (songList != null && songList.size > 0) {
            openQueueBottomSheet()
        }
    }

    private var bottomSheetDialog: QueueBottomDialogFragment? = null
    private fun openQueueBottomSheet() {
        val distinctlist = songList.distinctBy { it.id }
        songList.clear()
        songList.addAll(distinctlist)
        playingIndex = getCurrentPlayingIndex(songList)
        if (playingIndex!! < songList.size) {
            bottomSheetDialog =
                QueueBottomDialogFragment.getInstance(
                    songList,
                    "",
                    songList[playingIndex!!],
                    playingIndex!!
                )
            bottomSheetDialog?.show(supportFragmentManager, "Bottom Sheet Dialog Fragment")
        } else {
            ToastHandler.getInstance().show(this, getString(R.string.queue_complete))
        }
    }

    private fun getSlugContentDetail() {
            railInjectionHelper!!.getAssetDetailsbySlug(contentSlug)
                .observe(this@HomeActivity) { assetResponse: ResponseModel<*>? ->
                    if (assetResponse != null) {
                        if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                            //Do nothing
                        } else if (assetResponse.status.equals(
                                APIStatus.SUCCESS.name,
                                ignoreCase = true
                            )
                        ) {

                            val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
                            if (enveuCommonResponse.enveuVideoItemBeans.size > 0) {
                                isPauseMiniPLayer = true
                                isSlugContent = true
                                videoDetails = enveuCommonResponse.enveuVideoItemBeans[0]
                                playSongs(videoDetails!!)
                                if (KsPreferenceKeys.getInstance().reel){
                                    openUgcFragment(videoDetails)
                                    KsPreferenceKeys.getInstance().reel = false
                                }
                                if (KsPreferenceKeys.getInstance().live){
                                    val epgChannelId = videoDetails?.epgLiveContent?.epgChannelId
                                    openEpgFragmentDetail(epgChannelId)
                                    KsPreferenceKeys.getInstance().live = false
                                }
                            }
                        } else if (assetResponse.status.equals(
                                APIStatus.ERROR.name,
                                ignoreCase = true
                            )
                        ) {
                            if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {

                            }
                        } else if (assetResponse.status.equals(
                                APIStatus.FAILURE.name,
                                ignoreCase = true
                            )
                        ) {

                        }
                    }
                }

    }

    private fun openEpgFragmentDetail(epgChannelId: String?) {
        if (epgChannelId.isNullOrEmpty()) {
            Log.e("EPG", "epgChannelId is null or empty")
            return
        }

        val dateTime = if (epgDay == 0) {
            "Today ${DateTime().plusDays(epgDay).toString("MMMM dd yyyy")}"
        } else {
            DateTime().plusDays(epgDay).toString("MMMM dd yyyy")
        }

        val startDate = EPGUtils.getStartTimeByDayShift(epgDay)
        val endDate = EPGUtils.getEndTimeByStartTime(startDate)

        APIServiceLayer.getInstance()
            .getEPGData(epgChannelId, startDate.millis, endDate.millis, 100)
            .observe(this) { response ->

                val epgResponseModelData =
                    AppCommonMethod.convertResponse(response, EPGResponseModel::class.java)

                val epgItems = epgResponseModelData?.data?.epgItems
                if (!epgItems.isNullOrEmpty()) {
                    val epgItem = epgItems[0]

                    val schedules = epgItem.schedules
                    if (!schedules.isNullOrEmpty()) {
                        val program = schedules[0].program
                        val showId = program?.id?.toString() ?: ""

                        if (showId.isNotEmpty()) {
                            goToDetailPage(epgItem, epgChannelId, showId, true)
                        } else {
                            Log.w("EPG", "Show ID is missing")
                        }
                    } else {
                        goToDetailPage(epgItem, epgChannelId,
                            epgItem.mediaContent?.id.toString(), true)
                    }
                } else {
                    Log.w("EPG", "EPG items are empty or null")
                }
            }
    }

    private fun goToDetailPage(epgItem: EPGItem, channelId: String, showId: String, isFromChannelClick: Boolean) {
        val epgJson=Gson().toJson(epgItem)
        val bundle = Bundle()
        bundle.putString(AppConstants.FROM_REDIRECTION, AppConstants.EPG_FRAGMENT)
        bundle.putString(AppConstants.EPG_ITEM, epgJson.toString())
        bundle.putString(AppConstants.EPG_CHANNEL_ID, channelId)
        bundle.putString(AppConstants.EPG_SHOW_ID, showId)
        bundle.putBoolean(AppConstants.IS_FROM_CHANNEL_CLICK, isFromChannelClick)
        startActivity(Intent(this, LiveLinearDetailsActivity::class.java).also {
            it.putExtras(bundle)
        })
    }

    @SuppressLint("SuspiciousIndentation")
    fun openUgcFragment(content:EnveuVideoItemBean?){
        val json = Gson().toJson(content)
        val reelsContentItem = Gson().fromJson(json, ReelsContentItem::class.java)
        openShortsReelsFragment(reelsContentItem)
        KsPreferenceKeys.getInstance().reel = false

    }




    private fun getContentDetail() {
        railInjectionHelper!!.getAssetDetailsV2(assetId.toString(), this)
            .observe(this@HomeActivity) { assetResponse: ResponseModel<*>? ->
                if (assetResponse != null) {
                    if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                        //Do nothing
                    } else if (assetResponse.status.equals(
                            APIStatus.SUCCESS.name,
                            ignoreCase = true
                        )
                    ) {
                        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
                        if (enveuCommonResponse.enveuVideoItemBeans.size > 0) {
                            videoDetails = enveuCommonResponse.enveuVideoItemBeans[0]
                            playSongs(videoDetails!!)
                        }
                    } else if (assetResponse.status.equals(
                            APIStatus.ERROR.name,
                            ignoreCase = true
                        )
                    ) {
                        if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {

                        }
                    } else if (assetResponse.status.equals(
                            APIStatus.FAILURE.name,
                            ignoreCase = true
                        )
                    ) {

                    }
                }
            }
    }


    override fun setMiniPlayerState(mPlayer: JWPlayer?) {
        if (isPauseMiniPLayer) {
            binding?.shimmerPlayer?.visibility = View.GONE
            binding?.miniPlayerViews?.pBar?.visibility = View.GONE
            binding?.miniPlayerViews?.playPause?.visibility = View.VISIBLE
            binding?.shimmer?.shimmerViewContainer?.stopShimmer()
            updatePlayPauseIcon(false)
            mPlayer?.pause()
        }
    }


    private fun setViewModel() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        bookmarkingViewModel = ViewModelProvider(this)[BookmarkingViewModel::class.java]
    }

    @RequiresApi(api = 33)
    private fun requestPermission() {
        if (ContextCompat.checkSelfPermission(
                this, Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        ) {
        } else if (shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS)) {
        } else {
            requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
        }
    }

    private fun getAppLevelJsonData() {
        featureList = AppConfigMethod.parseFeatureFlagList()
    }

    fun toolFrame(value: Int) {
        //val fragmentManager = supportFragmentManager
        var fragmentTag = ""
        val currentFragment = fragmentManager?.findFragmentById(R.id.content_frame)
        if (currentFragment != null) {
            fragmentTag = currentFragment.tag.toString()
            Log.d("FragmentCheck", "Current fragment tag: $fragmentTag")
        } else {
            Log.d("FragmentCheck", "No fragment in container")
        }

        if (fragmentTag == AppConstants.TAG_ARTIST_FRAGMENT
            || fragmentTag == AppConstants.TAG_ALBUM_FRAGMENT || fragmentTag == AppConstants.TAG_PLAYLIST_DETAIL_FRAGMENT || fragmentTag == AppConstants.TAG_GRID_FRAGMENT
        ) {
            isBackPressedCallFromMoreFragment = true
            backStakeValue = 1
        } else {
            isBackPressedCallFromMoreFragment = false
            backStakeValue = value
        }

    }

    fun detailFrameVisibility() {
//        if (featureList?.featureFlag?.IS_MUSIC_APP == true) {
//            if (supportFragmentManager.backStackEntryCount == 0) {
//                binding?.toolbar?.root?.visibility = View.VISIBLE
//            } else {
//                if (backStakeValue == 0) {
//                    binding?.toolbar?.root?.visibility = View.VISIBLE
//                } else {
//                    binding?.toolbar?.root?.visibility = View.GONE
//                }
//            }
//        } else if (featureList?.featureFlag?.IS_ENT_MUSIC_ENABLED == true) {
//            if (supportFragmentManager.backStackEntryCount == 0) {
//                binding?.toolbar?.root?.visibility = View.VISIBLE
//            } else {
//                if (backStakeValue == 0) {
//                    binding?.toolbar?.root?.visibility = View.VISIBLE
//                } else {
//                    binding?.toolbar?.root?.visibility = View.GONE
//                }
//            }
//        } else {
//            if (backStakeValue == 0) {
//                binding?.toolbar?.root?.visibility = View.VISIBLE
//            } else {
//                binding?.toolbar?.root?.visibility = View.GONE
//            }
//        }
    }

    private fun callInitials() {
        if (StringUtils.isNullOrEmptyOrZero(AppCommonMethod.urlPoints)) {
            AppCommonMethod.urlPoints = preference!!.appPrefCfep
        }
       // ToolBarHandler.getInstance().setHomeAction(binding?.toolbar, this@HomeActivity)
        binding?.toolbar?.llSearchIcon?.setOnClickListener {
            if (featureList?.featureFlag?.NEW_SEARCH_UI == true) {
                binding?.toolbar?.root?.visibility = View.VISIBLE
                fragmentManager?.beginTransaction()
                    ?.add(R.id.content_frame, NewSearchFragment(), Constants.NEW_SEARCH_FRAGMENT)
                    ?.addToBackStack(null)?.commit()
            } else {
                openSearch()
            }
        }
    }


    fun openSearch() {
        if (featureList?.featureFlag?.IS_ENT_MUSIC_ENABLED!!) {
            if (mService != null && mService!!.getPlayer() != null) {
                if (mService?.getPlayer()?.state == PlayerState.PLAYING || mService?.getPlayer()?.state == PlayerState.PAUSED) {
                    backStakeValue = 1
                    fragmentManager?.beginTransaction()?.add(
                        R.id.content_frame,
                        SearchFragment(),
                        AppConstants.TAG_SEARCH_FRAGMENT
                    )?.addToBackStack(null)?.commit()
                } else {
                    startActivity(Intent(this, ActivitySearch::class.java))
                }
            } else {
                startActivity(Intent(this, ActivitySearch::class.java))
            }
        } else {
            if (featureList?.featureFlag?.IS_MUSIC_APP == true) {
                backStakeValue = 1
                fragmentManager?.beginTransaction()
                    ?.add(R.id.content_frame, SearchFragment(), AppConstants.TAG_SEARCH_FRAGMENT)
                    ?.addToBackStack(null)?.commit()
            } else {
                startActivity(Intent(this, ActivitySearch::class.java))
            }
        }

    }

    private val navigationItemListener = NavigationBarView.OnItemSelectedListener { item: MenuItem ->
            removeSearchFragment()
           // reelsContentItem = null
            stringList?.let { list ->
                ToolBarHandler.getInstance().setSubMenuAction(binding, View.GONE)
                for (i in list.indices) {
                    val menuItem = list[i].menuItem
                    val displayName = menuItem?.displayName
                    val menuItemActionType = menuItem?.menuItemActionType
                    val personalizedValue = menuItem?.personalizedValue
                    val type = menuItem?.type
                    isMoreGroup = menuItem?.isMoreGroup == true
                    when {
                        !type.isNullOrEmpty() && type == AppConstants.GROUP && menuItemActionType == null && menuItem.childMenuItems == null && personalizedValue == null && isMoreGroup -> {
                            if (featureList?.featureFlag?.IS_MUSIC_APP == true) {
                                onBack()
                            }
                            AnalyticsUtils.trackScreenView(this, item.title.toString())
                            if (moreFragment != null) {
                                finishShortsPlayer()
                                hideSubMenu()
                                Handler(Looper.getMainLooper()).postDelayed(
                                    { (moreFragment as MoreFragment).clickEvent() },
                                    0
                                )
                                switchToMoreFragment()
                            } else {
                                finishShortsPlayer()
                                moreFragment = MoreFragment()
                                fragmentManager!!.beginTransaction()
                                    .add(R.id.content_frame, moreFragment as MoreFragment, "4")
                                    .hide(moreFragment as MoreFragment).commit()
                                switchToMoreFragment()
                                Handler(Looper.getMainLooper()).postDelayed(
                                    { (moreFragment as MoreFragment).clickEvent() },
                                    0
                                )
                            }
                            return@OnItemSelectedListener true
                        }

                        item.title == displayName && (type == AppConstants.MAIN || type == AppConstants.GROUP) -> {
                            AnalyticsUtils.trackScreenView(this, item.title.toString())
                            isBackPressedCallFromMoreFragment = false
                            when {
                                menuItemActionType == AppConstants.PERSONALIZED && personalizedValue == AppConstants.MY_PLAYLIST -> {
                                    if (!menuItem.childMenuItems.isNullOrEmpty()) {
                                        subMenuRecycleView(menuItem.childMenuItems as List<OrderedMenuItem>)
                                    }
                                    AnalyticsUtils.trackScreenView(this, item.title.toString())
                                    if (playListFragment != null) {
                                        if (featureList?.featureFlag?.IS_MUSIC_APP == true) {
                                            fragmentManager?.popBackStackImmediate(
                                                null,
                                                FragmentManager.POP_BACK_STACK_INCLUSIVE
                                            )
                                        }
                                        switchToPlayListFragment()
                                    } else {

                                        playListFragment = MyPlaylistFragment().apply {
                                            arguments = Bundle().apply { putString("from", "Home") }
                                        }
                                        fragmentManager?.beginTransaction()?.add(
                                            R.id.content_frame,
                                            playListFragment as MyPlaylistFragment
                                        )?.commitNow()
                                        switchToPlayListFragment()
                                    }
                                    return@OnItemSelectedListener true
                                }

                                (type == AppConstants.MAIN || type == AppConstants.GROUP) && !isMoreGroup -> {
                                    var tabId = ""
                                    if (menuItem.childMenuItems.isNullOrEmpty()) {
                                        tabId = (menuItem?.screenId ?: 0).toString()
                                    } else {
                                        tabId = menuItem.childMenuItems[0]?.menuItem?.screenId!!
                                        menuItem.childMenuItems?.let { subMenuRecycleView(it as List<OrderedMenuItem>) }
                                    }
//                                    if (ugcFragment != null)
//                                        (ugcFragment as UgcFragment)?.let { it.setPlayerPause(true) }

                                    if (!StringUtils.isNullOrEmpty(tabId)) {
                                        if (featureList?.featureFlag?.SEARCH == true) {
                                            binding?.toolbar?.llSearchIcon?.visibility = View.VISIBLE
                                            binding?.toolbar?.searchIcon?.visibility = View.VISIBLE
                                            binding?.topGradient?.visibility = View.VISIBLE
                                            binding?.topSecondGradient?.visibility = View.VISIBLE
                                        } else {
                                            binding?.toolbar?.llSearchIcon?.visibility = View.GONE
                                            binding?.toolbar?.searchIcon?.visibility = View.INVISIBLE
                                        }
                                        if (featureList?.featureFlag?.IS_MUSIC_APP == true) fragmentManager?.popBackStackImmediate(
                                            null, FragmentManager.POP_BACK_STACK_INCLUSIVE
                                        )

                                        // open ugc fragment here if displayName is equal Shorts
                                        if (menuItemActionType == Constants.CUSTOM_VALUE && menuItem?.customValue == Constants.REEL_VALUE) {
                                            finishShortsPlayer()
                                            hideSubMenu()
                                            binding?.topGradient?.visibility = View.GONE
                                            binding?.topSecondGradient?.visibility = View.GONE
                                            binding?.toolbar?.llSearchIcon?.visibility = View.GONE
                                            binding?.toolbar?.searchIcon?.visibility = View.INVISIBLE
                                            ugcFragment = UgcFragment()
                                            val data = Bundle()
                                            data.putSerializable(Constants.SHORTS_REELS_BUNDLE, reelsContentItem)
                                            ugcFragment?.arguments = data
                                            fragmentManager!!.beginTransaction().add(R.id.content_frame, ugcFragment as UgcFragment, "8").hide(ugcFragment as UgcFragment).commit()
                                            switchToUgcFragment()
                                            active = ugcFragment
                                            reelsContentItem = null
                                            return@OnItemSelectedListener true
                                        } else if(menuItemActionType == Constants.CUSTOM_VALUE && menuItem?.customValue == Constants.EPG){
                                                finishShortsPlayer()
                                                hideSubMenu()
                                                switchToEpgFragment()
                                        }
                                        else if(menuItemActionType == Constants.CUSTOM_VALUE && menuItem?.customValue == Constants.CREATE){
                                            binding?.toolbar?.searchIcon?.hide()
                                            launchShortVideoUploadActivity()
                                            return@OnItemSelectedListener false
                                        }
                                        else {
                                            finishShortsPlayer()
                                             showSubMenu()
                                            if (fragmentList[item.itemId] == null) {
                                                homeFragment = tabId?.let { HomeFragment(it, binding) }
                                                ToolBarHandler.getInstance()
                                                    .setSubMenuAction(binding, View.VISIBLE)
                                                binding?.toolbar?.recLayoutSubMenu?.visibility = View.VISIBLE
                                                fragmentManager?.beginTransaction()
                                                    ?.add(
                                                        R.id.content_frame,
                                                        homeFragment as HomeFragment,
                                                        "1"
                                                    )
                                                    ?.hide(homeFragment as HomeFragment)
                                                    ?.commitNow()
                                                detailFrameVisibility()
                                                active?.let { activity ->
                                                    homeFragment?.let { homeFragment ->
                                                        fragmentManager?.beginTransaction()
                                                            ?.hide(activity)
                                                            ?.show(homeFragment)?.commitNow()
                                                    }
                                                }
                                                active = homeFragment
                                                fragmentList[item.itemId] = homeFragment!!
                                            } else {
                                                finishShortsPlayer()
                                                fragmentList[item.itemId]?.let {
                                                    if (item.title?.equals(displayName) == true && !isMoreGroup) {
                                                        ToolBarHandler.getInstance().setSubMenuAction(binding, View.VISIBLE)
                                                        binding?.toolbar?.recLayoutSubMenu?.visibility = View.VISIBLE
                                                        binding?.topGradient?.visibility = View.VISIBLE
                                                        binding?.topSecondGradient?.visibility = View.VISIBLE
                                                    } else {
                                                        switchToMoreFragment()
                                                    }
                                                    active?.let { activeFragment ->
                                                        fragmentManager?.beginTransaction()
                                                            ?.hide(activeFragment)?.show(it)
                                                            ?.commitNow()
                                                    }
                                                }
                                                active = fragmentList[item.itemId]
                                            }
                                        }
                                    } else {
                                        if (item.title?.toString()?.equals("Channel", true) == true) {
                                            finishShortsPlayer()
                                            switchToEpgFragment()
                                        }
                                    }
                                    return@OnItemSelectedListener true
                                }
                            }
                        }
                    }
                }
            }
            false
        }

    private fun removeSearchFragment() {
        val fragment = supportFragmentManager.findFragmentByTag(Constants.NEW_SEARCH_FRAGMENT)
        fragment?.let { fragmentId ->
            supportFragmentManager.beginTransaction()
                .remove(fragmentId)
                .commit()
        }
        showSubMenu()
    }

    private fun finishShortsPlayer() {
        if (ugcFragment != null) {
            (ugcFragment as UgcFragment).finishPlayer()
        }
    }

    private fun switchToHomeFragment() {
        AnalyticsUtils.trackScreenView(this, "Home")
        if (featureList?.featureFlag?.SEARCH == true) {
            binding?.toolbar?.llSearchIcon?.visibility = View.VISIBLE
            binding?.toolbar?.searchIcon?.visibility = View.VISIBLE
        }
        homeFragment?.let {
            active?.let { it1 ->
                fragmentManager?.beginTransaction()?.hide(it1)?.show(it)?.commit()
            }
        }
        active = homeFragment
    }

    private fun switchToMoreFragment() {
        val fragment = fragmentManager?.findFragmentById(R.id.fragment_audio_interaction)
        if (fragment is AudioInteractionFragment) {
            fragmentManager?.popBackStack() // Remove child fragment
        }
        if (isBackPressedCallFromMoreFragment) {
            onBackPressedDispatcher.onBackPressed()
        }
        binding?.topGradient?.visibility = View.GONE
        binding?.topSecondGradient?.visibility = View.GONE
        binding?.toolbar?.logoMain2?.visibility = View.GONE
        binding?.toolbar?.llSearchIcon?.visibility = View.GONE
        binding?.toolbar?.searchIcon?.visibility = View.INVISIBLE
        moreFragment?.let { moreFragment ->
            active?.let {
                fragmentManager!!.beginTransaction().hide(it).show(moreFragment).commit()
            }
        }
        active = moreFragment
    }

    private fun switchToEpgFragment() {
        if (epgFragment == null) {
            epgFragment = EPGFragment()
            fragmentManager?.beginTransaction()
                ?.add(R.id.content_frame, epgFragment as EPGFragment, AppConstants.EPG_FRAGMENT)
                ?.commit()
            active = epgFragment
        } else {
            active?.let { activeFrag ->
                fragmentManager?.beginTransaction()
                    ?.hide(activeFrag)
                    ?.show(epgFragment as EPGFragment)
                    ?.commit()
                active = epgFragment
            }
        }
        //binding?.toolbar?.logoLeft?.show()
        binding?.toolbar?.llSearchIcon?.hide()
    }

    private fun switchToUgcFragment() {
        ugcFragment?.let { ugcFragment ->
            active?.let {
                fragmentManager!!.beginTransaction().hide(it).show(ugcFragment).commit()
            }
        }
        active = ugcFragment
    }

    private fun switchToPlayListFragment() {
        val fragment = fragmentManager?.findFragmentById(R.id.fragment_audio_interaction)
        if (fragment is AudioInteractionFragment) {
            fragmentManager?.popBackStack() // Remove child fragment
        }
        if (isBackPressedCallFromMoreFragment) {
            onBackPressedDispatcher.onBackPressed()
        }
        binding?.toolbar?.llSearchIcon?.visibility = View.GONE
        binding?.toolbar?.searchIcon?.visibility = View.INVISIBLE
        fragmentManager!!.beginTransaction().hide(active!!).show(playListFragment!!).commit()
        active = playListFragment
    }

    private fun initialFragment(callFrom: Boolean) {
        if (null != stringList && stringList!!.isNotEmpty()) {
            if (callFrom) {
                if (stringList!![0].menuItem?.screenId.isNullOrEmpty() && !stringList!![0].menuItem?.childMenuItems.isNullOrEmpty()) {
                    tabId = stringList!![0].menuItem?.childMenuItems!![0]?.menuItem?.screenId
                    menuItemActionType =
                        stringList!![0].menuItem?.childMenuItems!![0]?.menuItem?.menuItemActionType
                    personalizedValue =
                        stringList!![0].menuItem?.childMenuItems!![0]?.menuItem?.personalizedValue
                    type = stringList!![0].menuItem?.childMenuItems!![0]?.menuItem?.type
                } else {
                    stringList!![0].menuItem?.screenId.also { tabId = it }
                    menuItemActionType = stringList!![0].menuItem?.menuItemActionType
                    personalizedValue = stringList!![0].menuItem?.personalizedValue
                    type = stringList!![0].menuItem?.type
                    isMoreGroup = stringList!![0].menuItem?.isMoreGroup == true
                }
            }

            fragmentManager = supportFragmentManager
            if (!StringUtils.isNullOrEmpty(type) && type == AppConstants.GROUP && menuItemActionType == null && personalizedValue == null && isMoreGroup) {
                moreFragment = MoreFragment()
                active = moreFragment
                fragmentManager?.beginTransaction()
                    ?.add(R.id.content_frame, moreFragment as MoreFragment, "5")
                    ?.hide(moreFragment as MoreFragment)?.commit()
                backStakeValue = 0
                detailFrameVisibility()
                switchToMoreFragment()
                Handler(Looper.getMainLooper()).postDelayed(
                    { (moreFragment as MoreFragment).clickEvent() }, 0
                )
            } else if (!StringUtils.isNullOrEmpty(menuItemActionType) && menuItemActionType == AppConstants.PERSONALIZED && !StringUtils.isNullOrEmpty(
                    personalizedValue
                ) && personalizedValue == AppConstants.MY_FAVOURITE
            ) {
                // create function for my list
            } else if (!StringUtils.isNullOrEmpty(menuItemActionType) && menuItemActionType == AppConstants.SCREEN && !StringUtils.isNullOrEmpty(type) && type == AppConstants.MAIN) {
                if (!StringUtils.isNullOrEmpty(tabId)) {
                    if (active !is HomeFragment || !callFrom) {
                        initializeHomeFragment()
                        switchToHomeFragment()
                        if (homeFragment != null) {
                            fragmentList[0] = homeFragment!!
                        }
                    }
                }
            }
        }
        if (active == null) {
            initializeHomeFragment()
            switchToHomeFragment()
        }
        uiInitialisation()
    }

    private fun initializeHomeFragment() {
        homeFragment = tabId?.let { HomeFragment(it, binding) }
        active = homeFragment
        fragmentManager?.beginTransaction()
            ?.add(R.id.content_frame, homeFragment as HomeFragment, "1")
            ?.hide(homeFragment as HomeFragment)?.commitAllowingStateLoss()
        fragmentManager?.beginTransaction()?.hide(active!!)?.show(homeFragment as HomeFragment)
            ?.commitAllowingStateLoss()
        backStakeValue = 0
        detailFrameVisibility()
        fragmentList[id] = homeFragment!!
    }


    @SuppressLint("SuspiciousIndentation")
    private fun setupBottomNavigationView() {
        val localData = MenuCommonFunction.getParseMenu(this@HomeActivity)?.data?.orderedMenuItems
        bottomNavigationView = binding?.navigation!!
        val menuItems: List<OrderedMenuItem>
        if (preference?.dataMenuKeyValue?.isNotEmpty() == true) {
            menuItems = preference?.dataMenuKeyValue?.mapNotNull {
                it
            }!!
            stringList = menuItems
            initialFragment(true)
            if (menuItems.isNotEmpty()) {
                val childMenuItems =
                    menuItems[0].menuItem?.childMenuItems as? List<OrderedMenuItem> ?: emptyList()
                subMenuRecycleView(childMenuItems)
            } else {

                Log.e("setupBottomNavigationView", "menuItems is empty")
            }
            Log.d("stringListAPI", menuItems.toString())
//            menuItems.take(5).forEach { menuItem ->
//                menuItem.menuItem?.id?.let {
//                    bottomNavigationView.menu.add(
//                        0,
//                        it,
//                        0,
//                        menuItem.menuItem.displayName
//                    ).setIcon(menuItem.menuItem.iconIdentifier?.let { it1 ->
//                        getResourceDrawable(it1)
//                    })
//                }
//                bottomNavigationView.menu.getItem(0).isChecked = true
//                // Use coroutine scope to launch a new coroutine
//            }
            setMenuItems(menuItems)
        } else if (localData?.size != 0 && localData != null) {
            menuItems = localData.mapNotNull {
                it
            }
            stringList = localData as List<OrderedMenuItem>
            initialFragment(true)
            Log.d("stringList", "Local Menu")
            menuItems.take(5).forEach { menuItem ->
                menuItem.menuItem?.id?.let {
                    bottomNavigationView.menu.add(
                        0,
                        it,
                        0,
                        menuItem.menuItem.displayName
                    ).setIcon(menuItem.menuItem.iconIdentifier?.let { it1 ->
                        getResourceDrawable(
                            it1
                        )
                    })
                }
                bottomNavigationView.menu.getItem(0).isChecked = true
            }
        }

        openShortsFragment = intent?.getBooleanExtra(Constants.OPEN_SHORTS_FRAGMENT, false)
        if (openShortsFragment == true) {
            openShortsReelsFragment()
        }
    }

    fun openShortsReelsFragment(reelsContentItem: ReelsContentItem? = null) {
        if (reelsContentItem == null) {
            this.reelsContentItem = intent.getSerializableExtra(Constants.SHORTS_REELS_BUNDLE) as ReelsContentItem
        } else {
            this.reelsContentItem = reelsContentItem
        }
       val index = stringList?.indexOfFirst { it.menuItem?.customValue == Constants.REEL_VALUE && it.menuItem.menuItemActionType == Constants.CUSTOM_VALUE }
        index?.let {
            val item = bottomNavigationView.menu.getItem(it)
            bottomNavigationView.selectedItemId = item.itemId
        }
    }

    private fun getResourceDrawable(iconIdentifier: String): Drawable? {
        val resourceId = resources.getIdentifier(iconIdentifier, "drawable", packageName)
        return if (resourceId != 0) {
            ResourcesCompat.getDrawable(resources, resourceId, null)
        } else {
            ResourcesCompat.getDrawable(resources, resources.getIdentifier("icon6", "drawable", packageName), null)
        }
    }

    private fun uiInitialisation() {
        val navigation = findViewById<BottomNavigationView>(R.id.navigation)
        binding!!.toolbar.homeIconKids.visibility = View.GONE
        binding!!.toolbar.homeIcon.visibility = View.GONE
        binding!!.toolbar.backLayout.visibility = View.GONE

        parseColor(navigation)
        navigation.setOnItemSelectedListener(navigationItemListener)
    }

    private fun parseColor(navigation: BottomNavigationView) {
        navigation.background =
            colorsHelper.strokeBgDrawable(AppColors.appBgColor(), AppColors.appBgColor(), 0f)

        val textColorStates = ColorStateList(
            arrayOf(
                intArrayOf(-android.R.attr.state_checked), intArrayOf(android.R.attr.state_checked)
            ), intArrayOf(
                AppColors.bottomNavUnselectedTextColor(), AppColors.bottomNavSelectedTextColor()
            )
        )
        navigation.itemTextColor = textColorStates
        val iconColorStates = ColorStateList(
            arrayOf(
                intArrayOf(-android.R.attr.state_checked), intArrayOf(android.R.attr.state_checked)
            ), intArrayOf(
                AppColors.bottomNavUnselectedIconColor(),
                AppColors.bottomNavSelectedIconColor(),
            )
        )
        navigation.itemIconTintList = iconColorStates
    }

    private fun bottomNavigationTextFromJson(
        navigation: BottomNavigationView, itemID: Int, jsonString: String, localString: Int,
    ) {
        val itemTitle = navigation.menu.findItem(itemID)
        val itemTabBar: String = if (jsonString != "null" && jsonString != "") {
            jsonString
        } else {
            getString(localString)
        }
        itemTitle.title = itemTabBar
    }

    private var isActivityRecreated = false
    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        if (preference == null) preference = KsPreferenceKeys.getInstance()
        preference!!.appPrefIsRestoreState = true
        // Check if the activity was recreated
        isActivityRecreated = savedInstanceState.getBoolean("ACTIVITY_RECREATED", false)

        if (isActivityRecreated) {
            // Activity was killed and is now being recreated
            this.finish()
            val intent = Intent(this, ActivitySplash::class.java)
            startActivity(intent)
            Log.d("ActivityLifecycle", "Activity was killed and recreated")
        } else {
            // Activity is just resuming (not recreated after being killed)
            Log.d("ActivityLifecycle", "Activity was resumed")
        }

        callInitials()
    }

    var listener = InstallStateUpdatedListener { installState: InstallState ->
        if (installState.installStatus() == InstallStatus.DOWNLOADED) {
            // After the update is downloaded, show a notification
            // and request user confirmation to restart the app.
            popupSnackbarForCompleteUpdate()
        }
    }

    override fun getAppUpdateCallBack(appUpdateInfo: AppUpdateInfo) {
        if (appUpdateInfo != null) {
            ApplicationUpdateManager.getInstance(applicationContext).startUpdate(
                appUpdateInfo,
                AppUpdateType.FLEXIBLE,
                this,
                ApplicationUpdateManager.APP_UPDATE_REQUEST_CODE
            )
        } else {
            Logger.w("InApp update", "NoUpdate available")
        }
    }

    /* Displays the snackbar notification and call to action. */
    private fun popupSnackbarForCompleteUpdate() {
        val snackbar = Snackbar.make(
            binding!!.blurredBackgroundImageView,
            resources.getString(R.string.update_has_downloaded),
            Snackbar.LENGTH_INDEFINITE
        )
        snackbar.setAction(resources.getString(R.string.restart)) {
            ApplicationUpdateManager.getInstance(
                applicationContext
            ).appUpdateManager.completeUpdate()
        }
        snackbar.setActionTextColor(
            ContextCompat.getColor(
                this, R.color.series_detail_episode_unselected_btn_txt_color
            )
        )
        snackbar.show()
    }

    override fun onStart() {
        super.onStart()
        if (mService != null) {
            mService!!.setCallback(this@HomeActivity)
        }
        try {
            (homeFragment as HomeFragment?)?.updateAdList()
        } catch (e: Exception) {
            Logger.w(e)
        }
    }


    private fun checkPlayerStateForIconUpdate() {
        if (mService != null && mService?.getPlayer() != null) {
            if (mService?.getPlayer()?.state == PlayerState.PLAYING) {
                updatePlayPauseIcon(true)
            } else {
                updatePlayPauseIcon(false)
            }
        }
    }

    override fun onSaveInstanceState(@NonNull outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean("ACTIVITY_RECREATED", true)
    }

    override fun onResume() {
        super.onResume()
        Logger.d("onResume")
       // setTheme(R.style.MyMaterialTheme)
        AppCommonMethod.resetFilter(this@HomeActivity)
        checkPlayerStateForIconUpdate()
        if (preference == null) preference = KsPreferenceKeys.getInstance()
    }

     fun showMainToolbar() {
        ToolBarHandler.getInstance().setSubMenuAction(binding, View.VISIBLE)
        binding?.toolbar?.llSearchIcon?.visibility = View.VISIBLE
        binding?.toolbar?.searchIcon?.visibility = View.VISIBLE
    }

    fun hideMainToolbar() {
        ToolBarHandler.getInstance().setSubMenuAction(binding, View.GONE)
        binding?.toolbar?.llSearchIcon?.visibility = View.GONE
        binding?.toolbar?.searchIcon?.visibility = View.GONE
    }

    override fun onDestroy() {
        if (preference!!.appPrefIsRestoreState) {
            preference!!.appPrefIsRestoreState = false
        }
        if (featureList?.featureFlag?.IS_PHONE_STATE_RECEIVE == true) {
            unregisterReceiver(phoneStateReceiver)
        }
        super.onDestroy()
    }


    override fun onDataReceived(position: Double, duration: Double, playerProgressTime: Double) {
        videoDuration = duration.toLong()
        currentPlayerTime = playerProgressTime
//        currentPosition = position
        position.let { updateProgress(it) }
        playerFragment?.let {
            it.onDataReceived(position, duration)
        }


    }

    override fun onBuffer(isBuffering: Boolean) {
        Logger.d("DataRetrived", "True")
    }

    override fun onPlay(isBuffering: Boolean, position: Double, duration: Double) {
        binding?.shimmerPlayer?.visibility = View.GONE
        binding?.miniPlayerViews?.pBar?.visibility = View.GONE
        binding?.miniPlayerViews?.playPause?.visibility = View.VISIBLE
        binding?.shimmer?.shimmerViewContainer?.stopShimmer()
        binding?.miniPlayer?.visibility = View.GONE
        AnalyticsUtils.logPlayerEvent(
            this@HomeActivity,
            AppConstants.CONTENT_PLAY,
            song?.id.toString(),
            song?.title,
            song?.contentType,
            position.toString(),
            duration.toString()
        )
        updatePlayPauseIcon(true)
        playerFragment?.let {
            it.onPlay(true)
        }
        if (startSessionApiStart) {
            Handler(Looper.getMainLooper()).postDelayed({
                startSessionUpdateTimer()
            }, 30000)
        }
    }

    override fun onPause(position: Double, duration: Double) {
        AnalyticsUtils.logPlayerEvent(
            this@HomeActivity,
            AppConstants.CONTENT_PAUSE,
            song?.id.toString(),
            song?.title,
            song?.contentType,
            position.toString(),
            duration.toString()
        )
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return when (keyCode) {
            KeyEvent.KEYCODE_BACK -> if (isTaskRoot) {

                val playerFrame =
                    supportFragmentManager.findFragmentByTag(AppConstants.TAG_PLAYER_FRAGMENT)
                if (playerFrame != null && playerFrame.isAdded) {
                    onBack()
                } else {
                    val playerFrame = supportFragmentManager.findFragmentById(R.id.content_frame)
                    if (playerFrame is HomeFragment) {
                        val homeIntent = Intent(Intent.ACTION_MAIN)
                        homeIntent.addCategory(Intent.CATEGORY_HOME)
                        homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        startActivity(homeIntent)
                    } else {
                        onBackPressed()
                    }
                }

                true
            } else {
                //  super.onKeyDown(keyCode, event)
                onBack()
                false
            }

            else -> {
                super.onKeyDown(keyCode, event)
                false
            }
        }
    }

    override fun updateIconThroughNotification(isBuffering: Boolean) {
        updatePlayPauseIcon(isBuffering)
        if (playerFragment != null) {
            playerFragment?.updatePlayPauseIcon(isBuffering)
        }
    }

    override fun onMetaDataUpdate() {
        if (featureList?.featureFlag?.YOUBORA_ENABLE == true) {
            sendYouboraLogs(song)
        }
    }

    private fun sendYouboraLogs(song: DataItem?) {
        plugin = null

        val options = Options()
        if (BuildConfig.FLAVOR.equals("qa", ignoreCase = true)) {
            options.accountCode = AppConstants.QA_YOUBORA_ACCOUNT_CODE
        } else {
            options.accountCode = AppConstants.PROD_YOUBORA_ACCOUNT_CODE
        }

        options.username = KsPreferenceKeys.getInstance().appPrefUserId
        options.contentTitle = song?.title
        //  options.contentDuration = contentDurationInLong
        options.contentIsLive = false
        options.contentTvShow = ""
        options.contentSeason = ""
        options.contentEpisodeTitle = ""
        options.contentChannel = ""
        options.contentId = song?.id.toString()
        options.contentType = song?.contentType
        options.contentGenre = ""
        options.appName = "Valid Music"
        options.appReleaseVersion = BuildConfig.VERSION_NAME
        options.adCustomDimension7 = KsPreferenceKeys.getInstance().sponsorArtistId
        options.contentCustomDimension8 = "Valid Music"
        plugin = Plugin(options, this@HomeActivity)
        plugin?.activity = this // Activity where the player is
        plugin?.fireInit()
    }

    override fun onVideoComplete(position: Double, duration: Double) {
        AnalyticsUtils.logPlayerEvent(
            this@HomeActivity,
            AppConstants.CONTENT_COMPLETED,
            song?.id.toString(),
            song?.title,
            song?.contentType,
            position.toString(),
            duration.toString()
        )
        stopPlugin()
        if (KsPreferenceKeys.getInstance().singleSongRepeatEnable) {
            if (playerFragment != null) {
                playerFragment!!.hideSeekBar()
                playerFragment!!.seekToStart()
            }
        } else {
            playNextEpisode()
        }
    }

    override fun setJwPlayerAdapter(mPlayer: JWPlayer?) {
        plugin?.adapter = JWPlayerAdapter(mPlayer)
        plugin?.adsAdapter = JWPlayerAdsAdapter(mPlayer)
    }

    override fun onSkipPrevious() {
        onPrevious()
    }

    override fun onSkipNext() {
        stopPlugin()
        playNextEpisode()
    }

    private fun stopPlugin() {
        if (plugin != null) {
            plugin?.fireStop()
            plugin?.removeAdapter()
        }
    }


    private fun updatePlayPauseIcon(isPlaying: Boolean) {
        if (isPlaying) {
            binding?.miniPlayerViews?.playPause?.setImageResource(R.drawable.ic_pause)
        } else {
            binding?.miniPlayerViews?.playPause?.setImageResource(R.drawable.ic_play_icon)
        }
    }

    private var playerFragment: MainPlayerActivity? = null
    private fun popMainPlayer() {
        backStakeValue = 1
        // binding?.toolbar?.root?.visibility = View.GONE
//        var dataBundle = Bundle().apply {
//            putString(AppConstants.PLAYER_STATE, mService?.getPlayer()?.state?.name)
//            putInt(AppConstants.ASSET_ID, assetId)
//            putParcelableArrayList(AppConstants.SONG_LIST, ArrayList<DataItem>(songList))
//            putString(AppConstants.SRC, src)
//            putString(AppConstants.TITLE, title)
//            putString(AppConstants.DESCRIPTION, description)
//            putSerializable(AppConstants.IMAGE_CONTENT, imageContent)
//            putParcelable(AppConstants.SONG, song)
//        }
//        playerFragment = MainPlayerActivity()
//        playerFragment?.setPlayerListener(this)
//        playerFragment?.arguments = dataBundle
//        supportFragmentManager.beginTransaction()
//            .add(R.id.player_frame, playerFragment!!, AppConstants.TAG_PLAYER_FRAGMENT)
//            .addToBackStack(null)
//            .commit()
//
//        binding?.playerFrame?.visibility = View.VISIBLE

    }


    private fun updateProgress(progress: Double) {
        binding?.miniPlayerViews?.epTimeBar?.progress = progress.toLong().percent(videoDuration)
    }

    override fun songItemClick(
        songList: List<DataItem>,
        song: DataItem,
        extarnalRefId: String,
        imageContent: ImageContent?,
        image: String?,
        playQueueItems: Boolean?,
        isQueueItemClick: Boolean,
        songsPosition: Int,
    ) {
        if (song.isPremium && preference!!.concurrencyEnable) {
            // The flow begins here
            deviceCheckStatus { success ->
                if (success) {
                    startPlayer(
                        songList,
                        song,
                        extarnalRefId,
                        imageContent,
                        image,
                        playQueueItems,
                        isQueueItemClick,
                        songsPosition
                    )
                } else {
                    concurrancyError = true
                }
            }
        } else {
            startPlayer(
                songList,
                song,
                extarnalRefId,
                imageContent,
                image,
                playQueueItems,
                isQueueItemClick,
                songsPosition
            )
        }
    }

    private fun startPlayer(
        songList: List<DataItem>,
        song: DataItem,
        extarnalRefId: String,
        imageContent: ImageContent?,
        image: String?,
        playQueueItems: Boolean?,
        isQueueItemClick: Boolean,
        songsPosition: Int,
    ) {

        isRelatedContentApi = true
        isPauseMiniPLayer = false
        relatedContentSize = 0
        // binding?.miniPlayerViews?.pBar?.visibility = View.VISIBLE
        // binding?.miniPlayerViews?.playPause?.visibility = View.GONE
        if (bottomSheetDialog != null) {
            bottomSheetDialog?.dismiss()
        }

        playingIndex = songsPosition
        binding?.shimmerPlayer?.visibility = View.GONE
        binding?.shimmer?.shimmerViewContainer?.startShimmer()
        this.externalRefId = extarnalRefId
        this.assetId = song.id
        //TODO Check null pointer here for contentSlug
        KsPreferenceKeys.getInstance().contentSlug = song.contentSlug
        this.imageContent = imageContent
        this.src = image ?: ""
        this.title = song.title
        this.song = song
        this.description = song.description
        if (featureList!!.featureFlag.ALLOW_MINI_PLAYER) {
            binding?.miniPlayer?.visibility = View.GONE
            if (playerFragment != null) {
                playerFragment?.updateUI(song)
            }
            updateMiniPLayerUI(imageContent, image)
            src = image ?: ""

            if (mService != null && mService!!.getPlayer() != null) {
                mService!!.getPlayer()!!.stop()
                mService!!.createPlayer(
                    this@HomeActivity,
                    isPauseMiniPLayer,
                    extarnalRefId,
                    this@HomeActivity,
                    src,
                    title!!
                )

            } else {
                addToWatchHistory()
                bindService(
                    Intent(this, BackgroundAudioService::class.java), mConnection,
                    Context.BIND_AUTO_CREATE
                )
            }
            if (!extarnalRefId.isNullOrEmpty()) {
                hitApiAddToWatchHistory(assetId);
            }
        }
        if (songList.isNotEmpty()) {
            if (!isQueueItemClick) {
                if (playQueueItems == true) {
                    railInjectionHelper?.removeAllQueueApi()?.observe(this) {
                        if (songList.size <= 20 && isRelatedContentApi) {
                            relatedContentSize = 20 - songList.size
                            getRelatedSongs(song)
                        } else {
                            addToQueueApi(songList)
                        }
                    }
                } else {
                    railInjectionHelper?.removeAllQueueApi()?.observe(this) {
                        if (songList.size <= 20 && isRelatedContentApi) {
                            relatedContentSize = 20 - songList.size
                            getRelatedSongs(song)
                        } else {
                            addToQueueApi(songList)
                        }
                    }
                }
            } else {
                callGetQueueApi()
            }
            KsPreferenceKeys.getInstance().singleSongRepeatEnable = false
        } else {
            callSingleSongQueueLogic()
        }
        if (songList.isNotEmpty()) {
            val tempSongList = ArrayList<DataItem>()
            tempSongList.addAll(songList)
            this.songList.clear()
            if (KsPreferenceKeys.getInstance().isShuffleEnabled) {
                Collections.shuffle(tempSongList)
                this.songList = tempSongList.toMutableList()
            } else {
                this.songList = tempSongList.toMutableList()
            }
            this.previousSongList = tempSongList
        }
    }

    private fun hitApiAddToWatchHistory(assetId: Int) {
        bookmarkingViewModel?.addToWatchHistory(preference?.appPrefAccessToken, assetId)
//        railInjectionHelper?.let {
//            it.hitApiAddWatchHistory(preference?.appPrefAccessToken,assetId).observe(this@HomeActivity){response->
//                if (response.isStatus){
//                    Logger.d("asset added to watch history")
//                }else{
//                    Logger.d("response fail for add watch")
//                }
//            }
//        }
    }

    private fun callSingleSongQueueLogic() {
        railInjectionHelper?.removeAllQueueApi()?.observe(this) {
            relatedContentSize = 19
            getRelatedSongs(song!!)
        }
    }

    private fun addToQueueApi(songsList: List<DataItem>) {
        val addToQueueRequestModel = AddToQueueRequestModel()
        addToQueueRequestModel.addToTop = true
        addToQueueRequestModel.type = QUEUED
        addToQueueRequestModel.mediaContentIds = getAllIdsByAlbums(songsList)
        railInjectionHelper?.addToQueueApi(addToQueueRequestModel)?.observe(this) { addToQueueResponse ->
                if (addToQueueResponse != null) {
                    callGetQueueApi()
                }
            }
    }

    private fun callGetQueueApi() {
        railInjectionHelper?.allGetQueueListApi(QUEUED)?.observe(this) { getQueueResponse ->
            if (getQueueResponse?.data != null) {
                if (!getQueueResponse.data.orderedContents.isNullOrEmpty()) {
                    this.songList.clear()
                    Log.d("ApiDataListCheck", getQueueResponse.data.orderedContents.size.toString())
                    getQueueResponse.data.orderedContents.forEach { getQueueList ->
                        getQueueList?.content?.let { it1 ->
                            it1.imageContent = AppCommonMethod.getImageContent(it1)
                            this.songList.add(it1)
                        }
                    }
                    Log.d("QueueSize", "addToQueueApi: " + songList.size.toString())
                }

            }
        }
    }

    private fun addToWatchHistory() {
        bookmarkingViewModel?.addToWatchHistory(preference?.appPrefAccessToken, assetId)
    }

    private fun getAllIdsByAlbums(songListItems: List<DataItem>): ArrayList<Int>? {
        allContentIdList?.clear()
        songListItems.forEach { dataItems ->
            allContentIdList?.add(dataItems.id)
        }
        return allContentIdList
    }

    private fun updateMiniPLayerUI(imageContent: ImageContent?, image: String?) {
        binding?.miniPlayerViews?.textSongName?.text = song?.title
        binding?.miniPlayerViews?.textSongName?.isSelected = true
        if (!song?.customData?.songsArtistIds.isNullOrEmpty()) {
            binding?.miniPlayerViews?.textArtistName?.text =
                song?.customData?.songsArtistIds?.get(0)?.title
        }
        ImageHelper.getInstance(binding!!.miniPlayerViews.songImage.context)
            .loadListImage(
                binding!!.miniPlayerViews.songImage,
                imageContent?.src ?: ""
            )

        binding?.miniPlayerViews?.miniPlayerCard?.background =
            AppCommonMethod.setGradientBackgroundColor(
                Color.parseColor(
                    AppCommonMethod
                        .getDominantColor(imageContent)
                ),
                Color.parseColor("#00000000"),
                "RIGHT_TO_LEFT"
            )
    }

    override fun onBack() {
        val playerFrame = supportFragmentManager.findFragmentByTag(AppConstants.TAG_PLAYER_FRAGMENT)
        if (playerFrame != null && playerFrame.isAdded) {
            supportFragmentManager.beginTransaction().remove(playerFrame).commit()
        }
        checkPlayerStateForIconUpdate()
        binding?.navigation?.visibility = View.VISIBLE
    }

    override fun onPlay(externalRefId: String) {
        if (bottomSheetDialog != null) {
            bottomSheetDialog?.updateFirstSongs(songList[playingIndex!!])
        }
        binding?.shimmerPlayer?.visibility = View.GONE
        binding?.shimmer?.shimmerViewContainer?.startShimmer()
        this.externalRefId = externalRefId
        if (mService != null) {
            if (mService!!.getPlayer() != null) {
                mService!!.getPlayer()!!.stop()
            }
            playerFragment?.updateUI(song)
            updateMiniPLayerUI(
                song?.imageContent,
                if (!AppCommonMethod.getSongsPosterImageUrl(song).equals("")
                ) AppCommonMethod.getSongsPosterImageUrl(song) else src
            )
            song?.images?.let {
                // updateMiniPLayerUI(AppCommonMethod.getImageContent(it))
                imageContent = song?.imageContent
                src = song?.imageContent?.src
            }

            mService!!.createPlayer(
                this@HomeActivity,
                isPauseMiniPLayer,
                externalRefId,
                this@HomeActivity,
                src,
                song!!.title
            )
            if (song?.customData?.lyrics != null) {
//                Logger.e("layricssssssssss", song?.customData?.lyrics!!)
//                Logger.e("layricssssssssss1", song?.audioContent!!.lyrics)
            }
        }
        hitApiAddToWatchHistory(assetId)
    }

    override fun onPlayPause() {
        if (mService != null) {
            val isPlaying = mService!!.getPlayPauseState()
            playerFragment?.let {
                it.updatePlayPauseIcon(isPlaying)
            }
        }
    }

    override fun seekTo(position: Double) {
        if (mService != null && mService!!.getPlayer() != null) {
            mService!!.getPlayer()?.seek(position)
        }
    }

    override fun onNext() {
        playNextEpisode()
    }

    override fun shufflePlaylist() {
        if (KsPreferenceKeys.getInstance().isShuffleEnabled) {
            Collections.shuffle(songList)
        } else {
            if (songList != null) {
                val mutableList = songList.toMutableList()
                mutableList.clear()
                mutableList.addAll(previousSongList)
                this.songList = mutableList
            }
        }
    }

    override fun onPrevious() {
        if (currentPlayerTime >= AppConstants.PREVIOUS_SONG_CHANGE_COUNTER && playerFragment != null) {
            playFromStart()
        } else {
            if (songList.isNotEmpty()) {
                currentPlayingIndex = getCurrentPlayingIndex(songList)
                isBingeWatchEnable = true
            }
            currentPlayingIndex = currentPlayingIndex?.minus(1)
            if (shouldShowBingeWatch(songList.size) && isBingeWatchEnable == true && currentPlayingIndex != -1) {
                if (songList != null && songList.isNotEmpty()) {
                    val totalListSize = songList.size
                    val previousAudioItem = songList[currentPlayingIndex!!]
                    this.assetId = previousAudioItem.id
                    this.song = previousAudioItem
                    playingIndex = currentPlayingIndex
                    if (this.song?.contentSlug.isNullOrEmpty()) {
                        KsPreferenceKeys.getInstance().contentSlug =
                            KsPreferenceKeys.getInstance().contentSlug
                    } else {
                        KsPreferenceKeys.getInstance().contentSlug = this.song?.contentSlug
                    }
                    if (previousAudioItem.isPremium) {
                        if (preference!!.concurrencyEnable) {
                            deviceCheckStatus { success ->
                                if (success) {
                                    checkEntitlement(
                                        KsPreferenceKeys.getInstance().appPrefAccessToken,
                                        previousAudioItem.sku
                                    )
                                } else {
                                    concurrancyError = true
                                }
                            }
                        } else {
                            checkEntitlement(
                                KsPreferenceKeys.getInstance().appPrefAccessToken,
                                previousAudioItem.sku
                            )
                        }
                    } else {
                        onPlay(previousAudioItem.externalRefId)
                    }
                    // mService!!.createPlayer(this@MainPlayerActivity, previousAudioItem.externalRefId, this@MainPlayerActivity,this@MainPlayerActivity)

                }
            } else {
                playFromStart()
            }
        }
    }

    private fun playFromStart() {
        if (playerFragment != null) {
            playerFragment?.hideSeekBar()
            currentPlayerTime = 0.0
            playerFragment?.seekToStart()
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
      //  updateToolbarVisibility()
//        val playerFrame = supportFragmentManager.findFragmentByTag(AppConstants.TAG_PLAYER_FRAGMENT)
//        if (playerFrame != null && playerFrame.isAdded) {
//            supportFragmentManager.beginTransaction().remove(playerFrame).commit()
//        }
//        if (binding?.navigation?.isInvisible == true){
//            binding?.navigation?.visibility = View.VISIBLE
//        }
    }

    private var currentPlayingIndex: Int? = -1
    private var playingIndex: Int? = 0
    private var isBingeWatchEnable: Boolean? = false
    private fun playNextEpisode() {
        try {
            if (songList.isNotEmpty()) {
                currentPlayingIndex = getCurrentPlayingIndex(songList)
                isBingeWatchEnable = true
            }

            currentPlayingIndex = currentPlayingIndex!! + 1
            playingIndex = currentPlayingIndex
            if (shouldShowBingeWatch(songList.size) && isBingeWatchEnable == true && preference?.isRepeatEnabled == false) {
                if (songList != null && songList.isNotEmpty()) {
                    val audioListSize = songList.size
                    for (i in 0 until audioListSize!!) {
                        val id = songList[i].id
                        if (audioListSize != null) {
                            if (id == assetId && i < audioListSize - 1) {
                                com.enveu.player.utils.Logger.d("id: $assetId")
                                val nextAudioItem = songList[i + 1]
                                this.assetId = nextAudioItem.id
                                this.song = nextAudioItem

                                if (this.song?.contentSlug.isNullOrEmpty()) {
                                    KsPreferenceKeys.getInstance().contentSlug =
                                        KsPreferenceKeys.getInstance().contentSlug
                                } else {
                                    KsPreferenceKeys.getInstance().contentSlug =
                                        this.song?.contentSlug
                                }
                                if (nextAudioItem.isPremium) {
                                    if (preference!!.concurrencyEnable) {
                                        deviceCheckStatus { success ->
                                            if (success) {
                                                checkEntitlement(
                                                    KsPreferenceKeys.getInstance().appPrefAccessToken,
                                                    nextAudioItem.sku
                                                )
                                            } else {
                                                concurrancyError = true
                                            }
                                        }
                                    } else {
                                        checkEntitlement(
                                            KsPreferenceKeys.getInstance().appPrefAccessToken,
                                            nextAudioItem.sku
                                        )
                                    }
                                } else {
                                    onPlay(nextAudioItem.externalRefId)
                                }

                                break
                            }
                        }
                    }

                }
            } else {
                if (playerFragment != null) {
                    playerFragment!!.hideSeekBar()
                }
                if (KsPreferenceKeys.getInstance().isRepeatEnabled) {
                    // Retrieve the current playing index or initialize if not set
                    var currentPlayingIndex = getCurrentPlayingIndex(songList)

                    // Check if the index is valid
                    if (currentPlayingIndex != null) {
                        if (currentPlayingIndex >= songList.size - 1) {
                            currentPlayingIndex = 0
                        } else currentPlayingIndex = currentPlayingIndex + 1
                    } else {
                        currentPlayingIndex = 0
                    }
                    // Get the current song based on the index
                    val song = songList[currentPlayingIndex]
                    this.song = song
                    this.assetId = song.id

                    // Save content slug if not null or empty
                    if (!song.contentSlug.isNullOrEmpty()) {
                        KsPreferenceKeys.getInstance().contentSlug = song.contentSlug
                    }

                    // Check if the song is premium
                    if (song.isPremium) {
                        checkEntitlement(
                            KsPreferenceKeys.getInstance().appPrefAccessToken,
                            song.sku
                        )
                    } else {
                        this.playingIndex = currentPlayingIndex
                        onPlay(
                            song.externalRefId
                        )
                    }

                    // Update the current playing index for next time
                    currentPlayingIndex = (currentPlayingIndex + 1) % songList.size
                    KsPreferenceKeys.getInstance().currentPlayingPosition = currentPlayingIndex
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            dismissMiniPlayer()
            ToastHandler.getInstance().show(this, getString(R.string.something_went_wrong))
        }
    }

    private fun getCurrentPlayingIndex(audioList: List<DataItem>): Int? {
        var currentPlayingIndex: Int? = 0
        val total = audioList.size
        for (i in 0 until total) {
            val id = audioList[i].id
            if (id == assetId) {
                currentPlayingIndex = i
                break
            }
        }
        Log.d("currentPlayingIndex", "getCurrentPlayingIndex: $currentPlayingIndex")
        return currentPlayingIndex
    }

    private fun onPlayerFailure() {
        playerFragment?.dismissProgress()
        mService?.getPlayer()?.stop()
        if (mService != null) {
            mService!!.removeListener()
        }
        binding?.shimmerPlayer?.visibility = View.GONE
        binding?.miniPlayer?.visibility = View.GONE
    }

    private fun shouldShowBingeWatch(size: Int?): Boolean {
        var shouldBingeWatchShow: Boolean? = false
        Log.d("currentPlayingIndex", "getCurrentPlayingIndex: $currentPlayingIndex")
        if (size != null) {
            shouldBingeWatchShow = currentPlayingIndex!! < size
        }

        if (!shouldBingeWatchShow!!) {
            isBingeWatchEnable = false
        }

        return shouldBingeWatchShow
    }

    private fun checkEntitlement(
        token: String?,
        sku: String,
        imageContent: ImageContent? = null,
        enveuVideoItemBean: EnveuVideoItemBean? = null
    ) {
        // binding?.progressBar?.visibility = View.VISIBLE

        APIServiceLayer.getInstance().hitApiEntitlement(token, sku, object : ApiInterface {
            override fun onSuccess(responseEntitle: ResponseEntitle) {
                if (responseEntitle != null && responseEntitle.data != null) {
                    if (responseEntitle.data.entitled) {
                        APIServiceLayer.getInstance().getPlayableID(
                            responseEntitle.data?.accessToken,
                            responseEntitle.data.sku,
                            object : PlayableCallBack {
                                override fun onSuccess(drm: DRM) {
                                    binding?.miniPlayer?.visibility = View.GONE
                                    drm.data?.externalRefId?.let { externalRefId1 ->
                                        externalRefId = externalRefId1
                                        if (mService != null && mService!!.getPlayer() != null) {
                                            onPlay(externalRefId)
                                        } else {
                                            addToWatchHistory()
                                            bindService(
                                                Intent(
                                                    this@HomeActivity,
                                                    BackgroundAudioService::class.java
                                                ),
                                                mConnection,
                                                Context.BIND_AUTO_CREATE
                                            )
                                        }
                                    }
                                }

                                override fun onFailure() {
                                    binding?.miniPlayer?.visibility = View.GONE
                                    commonDialog(
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                            getString(R.string.popup_error)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                            getString(R.string.popup_something_went_wrong)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                            getString(R.string.popup_continue)
                                        ), ""
                                    )

                                }
                            })
                    } else {
                        dismissMiniPlayer()
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                                getString(R.string.popup_notEntitled)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_select_plan.toString(),
                                getString(R.string.popup_select_plan)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_purchase.toString(),
                                getString(R.string.popup_purchase)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                                getString(R.string.popup_cancel)
                            )
                        )
                    }

                } else {
                    if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                        dismissMiniPlayer()
                        ActivityLauncher.getInstance().loginActivity(
                            this@HomeActivity,
                            ActivityLogin::class.java, ""
                        )
                    } else {
                        dismissMiniPlayer()
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            ), ""
                        )
                    }
                }
            }

            override fun onFailure() {
                commonDialog(
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                        getString(R.string.popup_something_went_wrong)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                        getString(R.string.popup_continue)
                    ), ""
                )
            }
        })
    }

    private fun commonDialog(
        title: String,
        description: String,
        actionBtn: String,
        cancelBtn: String
    ) {
        val fm = supportFragmentManager
        val commonDialogFragment =
            CommonDialogFragment.newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm!!, AppConstants.MESSAGE)
    }

    private fun setMenuItems(bottomNavigationIconsList: List<OrderedMenuItem>) {
        data class Tuple(val menuItem: OrderedMenuItem, val bitmap: Bitmap)
        bottomNavigationIconsList.take(5).forEachIndexed { index, menuItemSingleObject ->
            binding?.navigation?.menu?.add(
                Menu.NONE,
                menuItemSingleObject.menuItem?.id ?: 0,
                index,
                menuItemSingleObject.menuItem?.displayName
            )
        }
        dynamicBottomNavigation.add(Observable.fromIterable(bottomNavigationIconsList)
            .switchMap {
//                Observable.just(
//                    Tuple(
//                        it,
//                        setBottomNavigationIconsWithGlide(it.menuItem?.icon)
//                    )
//                )
                    menuItem ->
                Observable.create<Tuple> { emitter ->
                    if (!menuItem.menuItem?.icon.isNullOrEmpty()) {
                        val bitmap = setBottomNavigationIconsWithGlide(menuItem.menuItem?.icon)
                        emitter.onNext(Tuple(menuItem, bitmap))
                        emitter.onComplete()
                    }
                }
            }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({
                val menuItem = binding?.navigation?.menu?.findItem(it.menuItem.menuItem?.id ?: 0)
                menuItem?.icon = BitmapDrawable(resources, it.bitmap)
            },
                {
                    // handle your error code
                    Log.d("BottomSheetCreated", "bottom sheet not Successfully created")

                },
                {
                    // bottom sheet successfully created
                    Log.d("BottomSheetCreated", "bottom sheet successfully created")
                }
            )
        )
    }

    private fun setBottomNavigationIconsWithGlide(image: String?): Bitmap {
        return Glide.with(this).asBitmap().load(image).diskCacheStrategy(DiskCacheStrategy.ALL)
            .skipMemoryCache(true).into(500, 500).get()
    }

    private fun fillData(enveuVideoItemBean: EnveuVideoItemBean?) {
        val gson = Gson()
        val json = gson.toJson(enveuVideoItemBean)
        song = gson.fromJson(json, DataItem::class.java)
        this.imageContent = song?.imageContent
        this.assetId = song!!.id
        if (this.song!!.contentSlug.isNullOrEmpty()) {
            KsPreferenceKeys.getInstance().contentSlug = KsPreferenceKeys.getInstance().contentSlug
        } else {
            KsPreferenceKeys.getInstance().contentSlug = song?.contentSlug
        }
        if (this.song!!.externalRefId != null) {
            this.externalRefId = song!!.externalRefId
        }
        if (featureList!!.featureFlag.ALLOW_MINI_PLAYER) {
            KsPreferenceKeys.getInstance().setShuffleEnable(false)
            KsPreferenceKeys.getInstance().setRepeatEnable(false)
            KsPreferenceKeys.getInstance().singleSongRepeatEnable = false
            src = enveuVideoItemBean?.posterURL ?: ""
            title = enveuVideoItemBean?.title
        }
    }

    @SuppressLint("SuspiciousIndentation")
    fun playSongs(enveuVideoItemBean: EnveuVideoItemBean) {
        binding?.shimmerPlayer?.visibility = View.GONE
        binding?.shimmer?.shimmerViewContainer?.startShimmer()
        this.assetId = enveuVideoItemBean.id
        if (enveuVideoItemBean.isPremium) {
            if (mService?.getPlayer()?.state == PlayerState.PLAYING) {
                mService?.getPlayer()?.pause()
            }
            if (enveuVideoItemBean != null) {
                val gson = Gson()
                val json = gson.toJson(enveuVideoItemBean)
                song = gson.fromJson(json, DataItem::class.java)
                fillData(enveuVideoItemBean)
                updateMiniPLayerUI(song?.imageContent, AppCommonMethod.getSongsPosterImageUrl(song))
            }
            if (preference!!.concurrencyEnable) {
                deviceCheckStatus { success ->
                    if (success) {
                        checkEntitlement(
                            KsPreferenceKeys.getInstance().appPrefAccessToken,
                            enveuVideoItemBean.sku!!,
                            AppCommonMethod.getImageContent(song)
                        )
                    } else {
                        concurrancyError = true
                    }
                }
            } else {
                checkEntitlement(
                    KsPreferenceKeys.getInstance().appPrefAccessToken,
                    enveuVideoItemBean.sku!!,
                    AppCommonMethod.getImageContent(song)
                )
            }
        } else {
            fillData(enveuVideoItemBean)
            binding?.miniPlayer?.visibility = View.GONE
            val gson = Gson()
            val json = gson.toJson(enveuVideoItemBean)
            song = gson.fromJson(json, DataItem::class.java)
            updateMiniPLayerUI(
                AppCommonMethod.getImageContent(song),
                AppCommonMethod.getSongsPosterImageUrl(song)
            )
            if (mService != null && mService!!.getPlayer() != null) {
                onPlay(externalRefId)
            } else {
                addToWatchHistory()
                bindService(
                    Intent(this, BackgroundAudioService::class.java), mConnection,
                    Context.BIND_AUTO_CREATE
                )
            }
        }
        if (isSlugContent) {
            isSlugContent = false
            callGetQueueApi()
        } else {
            callSingleSongQueueLogic()
        }

    }

    fun dismissMiniPlayer() {
        playerFragment?.dismissProgress()
        if (mService != null) {
            mService?.clearNotification()
        }
        binding?.miniPlayer?.visibility = View.GONE
    }

    private fun getRelatedSongs(song: DataItem) {
        railInjectionHelper?.getRelatedContentV3(song.id, 0, relatedContentSize, "AUDIO")
            ?.observe(this@HomeActivity) { assetResponse: ResponseModel<*>? ->
                if (assetResponse != null) {
                    if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                    } else if (assetResponse.status.equals(
                            APIStatus.SUCCESS.name,
                            ignoreCase = true
                        )
                    ) {
                        isRelatedContentApi = false
                        val envMediaDetailList =
                            assetResponse.baseCategory as com.enveu.beanModelV3.videoDetailV3.list.EnvSongDetail
                        val gson = Gson()
                        val type = object :
                            TypeToken<DataItem>() {}.type // Define type for single DataItem

                        // Iterate through each item in envMediaDetailList.data.items
                        val songList: MutableList<DataItem> = mutableListOf()
                        songList.add(song)
                        envMediaDetailList.data.items.forEach { item ->
                            // Convert the current item to JSON
                            val json = gson.toJson(item.content)
                            // Parse the JSON into a single DataItem object
                            val dataItem = gson.fromJson<DataItem>(json, type)
                            dataItem.imageContent = AppCommonMethod.getImageContent(dataItem)
                            // Add the DataItem to songList
                            songList.add(dataItem)
                        }
                        this.songList.addAll(songList)
                        addToQueueApi(this.songList)
                        Log.d("QueueSize", "addToQueueApi: " + this.songList.size.toString())

                    } else if (assetResponse.status.equals(
                            APIStatus.ERROR.name,
                            ignoreCase = true
                        )
                    ) {
                        isRelatedContentApi = false
                    }
                } else {
                    isRelatedContentApi = false
                }
            }
    }


    override fun updateControls() {
        if (playerFragment != null && mService != null && mService?.getPlayer() != null) {
            playerFragment?.onDataReceived(
                mService?.getPlayer()!!.position,
                mService?.getPlayer()!!.duration
            )
        }
    }

    override fun onPause() {
        super.onPause()

//        Logger.d("serviceCalled", "True")
//        if (mService != null && mService!!.getPlayer() != null) {
//            mService!!.getPlayer()!!.allowBackgroundAudio(true)
//            mService!!.getPlayer()!!.play()
//        }
    }

    override fun isContentWatchlist() {
        if (preference!!.appPrefLoginStatus.equals(
                AppConstants.UserStatus.Login.toString(),
                ignoreCase = true
            )
        ) {
            railInjectionHelper!!.hitApiIsWatchList(preference!!.appPrefAccessToken, assetId)
                .observe(this) { responseEmpty: ResponseGetIsWatchlist ->
                    if (Objects.requireNonNull(responseEmpty).isStatus) {
                        if (playerFragment != null) {
                            playerFragment!!.setWatchlist()
                        }
                    } else {
                        if (responseEmpty.responseCode == 4302) {
                            isLoggedOut = true
                            logoutCall()
                        } else if (responseEmpty.responseCode == 500) {
                            // showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                        }
                    }
                }
        }
    }

    override fun callWatchlistApi(watchlistCounter: Int) {
        val isLogin = preference!!.appPrefLoginStatus
        if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            setWatchListForAsset(1, watchlistCounter)
        } else {
            playerFragment?.resetWatchList()
            ActivityTrackers.getInstance().setAction(
                ActivityTrackers.WATCHLIST
            )
            goToLogin()
        }
    }

    private fun setWatchListForAsset(from: Int, watchListCounter: Int) {
        if (watchListCounter == 0) {
            playerFragment?.setWatchlist()
            hitApiAddWatchList(from)
        } else {
            playerFragment?.resetWatchList()
            hitApiRemoveList()
        }
    }

    private fun hitApiAddWatchList(from: Int) {
        railInjectionHelper!!.hitApiAddWatchList(preference!!.appPrefAccessToken, assetId)
            .observe(this) { responseEmpty: ResponseEmpty ->
//            binding!!.wProgressBar.visibility = View.GONE
//            binding!!.addIcon.visibility = View.VISIBLE
                if (Objects.requireNonNull(responseEmpty).isStatus) {
                    if (playerFragment != null) {
                        // playerFragment!!.setWatchlist()
                    }
                } else {
                    if (responseEmpty.responseCode == 4302) {
                        isLoggedOut = true
                        logoutCall()
                    } else if (responseEmpty.responseCode == 4904) {
                        if (playerFragment != null) {
                            playerFragment!!.setWatchlist()
                        }
                        val debugMessage = responseEmpty.debugMessage
                        //from value will bedepends on how user click of watchlist icon-->>if loggedout=2 else=2
                        if (from == 1) {
                            //  showDialog(getString(R.string.error), debugMessage)
                        }
                    } else if (responseEmpty.responseCode == 500) {
                        //showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                    } else {
                        // showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                    }
                }
            }

    }

    private fun hitApiRemoveList() {
        railInjectionHelper!!.hitRemoveWatchlist(preference!!.appPrefAccessToken, assetId)
            .observe(this) { responseEmpty: ResponseEmpty ->
                if (Objects.requireNonNull(responseEmpty).isStatus) {
                    if (playerFragment != null) {
                        // playerFragment!!.resetWatchList()
                    }
                } else {
                    if (responseEmpty.responseCode == 4302) {
                        isLoggedOut = true
                        logoutCall()
                    } else if (responseEmpty.responseCode == 500) {
                        //  showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                    }
                }
            }
    }


    private fun goToLogin() {
        ActivityLauncher.getInstance().loginActivity(this, ActivityLogin::class.java, "")
    }

    private fun logoutCall() {
        if (CheckInternetConnection.isOnline(this)) {
            preference?.let { clearCredientials(it) }
            hitApiLogout(this, preference!!.appPrefAccessToken)
        } else {
            ToastHandler.getInstance().show(this, getString(R.string.no_internet_connection))
        }
    }

    override fun openShareDialog() {
        var assetType: String? = null
        val id = song!!.id
        val title = song!!.title
        if (song?.contentType != null && song?.contentType.equals("SONGS", ignoreCase = true)) {
            assetType = song?.contentType
        } else if (song?.contentType.equals("AUDIO", ignoreCase = true)) {
            assetType = song?.audioContent?.audioType
        }

        contentSlug = KsPreferenceKeys.getInstance().contentSlug
        var imgUrl = AppCommonMethod.getImageContent(song)
        if (ClickHandler.disallowClick()) {
            return
        }
        val enveuVideoItemBean = EnveuVideoItemBean().apply {
            posterURL = song?.images?.get(0)?.src
            <EMAIL> = song?.title
            assetType = song?.contentType
            description = song?.description
            contentSlug = song?.contentSlug
        }
        // imgUrl = AppCommonMethod.getListSQRImage(imgUrl!!, this)
        AppCommonMethod.openShareFirebaseDynamicLinks(this, enveuVideoItemBean)

    }

    override fun onActionBtnClicked() {
        if (concurrancyError) {
            concurrancyError = false
            onPlayerFailure()
        }
    }

    override fun onCancelBtnClicked() {
        onPlayerFailure()
    }

    override fun onFragmentInteraction() {
        if (featureList?.featureFlag?.IS_ENT_MUSIC_ENABLED!!) {
            if (mService != null && mService!!.getPlayer() != null) {
                if (mService?.getPlayer()?.state == PlayerState.PLAYING || mService?.getPlayer()?.state == PlayerState.PAUSED) {
                    fragmentManager?.beginTransaction()
                        ?.add(R.id.content_frame, MyListFragment(), "24")?.addToBackStack(null)
                        ?.commit()
                } else {
                    ActivityLauncher.getInstance().gotoList(this, MyListActivity::class.java)
                }
            } else {
                ActivityLauncher.getInstance().gotoList(this, MyListActivity::class.java)
            }
        } else {
            if (featureList?.featureFlag?.IS_MUSIC_APP == true) {
                fragmentManager?.beginTransaction()
                    ?.add(R.id.content_frame, MyListFragment(), "24")?.addToBackStack(null)
                    ?.commit()
            } else {
                ActivityLauncher.getInstance()
                    .gotoList(this, MyListActivity::class.java)
            }
        }
    }


    private fun deviceCheckStatus(callback: (Boolean) -> Unit) {
        var activeDeviceID = false

        deviceManagementViewModel?.getAllDevices(
            0,
            "ACTIVE",
            preference?.appPrefAccessToken
        )?.observe(this) { response ->
            if (response.responseCode == 2000) {
                // Check if the device ID matches the active device
                for (item in response.data?.items!!) {
                    if (item.deviceId == AppCommonMethod.getDeviceId(OttApplication.instance!!.contentResolver)) {
                        activeDeviceID = true
                        break
                    }
                }

                if (activeDeviceID) {
                    // Device is active, attempt to start session playback
                    startSessionPlayback { success ->
                        callback(success)  // Notify the callback based on the session playback result
                    }
                } else {
                    callback(false)  // Device not active, cannot start session playback
                }
            } else {
                showConcurrencyDialog(
                    this.resources.getString(R.string.error),
                    resources.getString(R.string.something_went_wrong),
                    resources.getString(R.string.ok)
                )
                callback(false)  // Error fetching device status
            }
        }
    }


    private fun startSessionUpdateTimer() {
        countDownTimer = object : CountDownTimer(
            Long.MAX_VALUE,
            preference?.updatePlaybackSessionTime?.toLong()!! * 1000
        ) {
            override fun onTick(millisUntilFinished: Long) {
                if (startSessionApiStart) {
                    preference?.appPrefAccessToken?.let { token ->
                        preference?.sessionId?.let { sessionID ->
                            deviceManagementViewModel?.updatePlaybackSession(
                                token,
                                sessionID
                            )?.observe(this@HomeActivity) {
                                if (it != null) {
                                    when (it.responseCode) {
                                        2000 -> {}
                                        4098 -> {
                                            stopSessionUpdateTimer()
                                            mPlayerView?.player?.pause()
                                            showConcurrencyDialog(
                                                "",
                                                resources.getString(R.string.no_season_found_this_session_id),
                                                resources.getString(R.string.ok)
                                            )
                                        }

                                        4302 -> {
                                            stopSessionUpdateTimer()
                                            mPlayerView?.player?.pause()
                                            showConcurrencyDialog(
                                                "",
                                                resources.getString(R.string.user_login_limit),
                                                resources.getString(R.string.ok)
                                            )
                                        }

                                        else -> {
                                            stopSessionUpdateTimer()
                                            mPlayerView?.player?.pause()
                                            showConcurrencyDialog(
                                                "",
                                                resources.getString(R.string.something_went_wrong),
                                                resources.getString(R.string.ok)
                                            )
                                        }
                                    }
                                } else {
                                    stopSessionUpdateTimer()
                                    mPlayerView?.player?.pause()
                                    showConcurrencyDialog(
                                        "",
                                        resources.getString(R.string.something_went_wrong),
                                        resources.getString(R.string.ok)
                                    )
                                }
                            }
                        }
                    }
                }
            }

            override fun onFinish() {
                // Perform any final actions here if needed
            }
        }

        // Start the countdown timer
        countDownTimer?.start()
    }


    private fun stopSessionUpdateTimer() {
        countDownTimer?.cancel()
        countDownTimer = null
    }


    private fun stopPlaySessionID(isStartPlay: Boolean? = null, callback: (Boolean) -> Unit) {
        preference?.appPrefAccessToken?.let { token ->
            preference?.sessionId?.let { sessionId ->
                deviceManagementViewModel?.stopPlaybackSession(token, sessionId)
                    ?.observe(this) { response ->
                        if (response != null) {
                            stopSessionUpdateTimer()
                            if (response.responseCode == 2000) {
                                preference?.sessionId = ""
                                // If stopping the session was successful and we need to start playback
                                if (isStartPlay == true) {
                                    deviceCheckStatus { success ->
                                        callback(success)  // Send result back to callback
                                    }
                                } else {
                                    callback(false)
                                }
                            } else {
                                showConcurrencyDialog(
                                    "",
                                    resources.getString(R.string.something_went_wrong),
                                    "ok"
                                )
                                callback(false)  // Failed to stop session
                            }
                        } else {
                            showConcurrencyDialog(
                                "",
                                resources.getString(R.string.something_went_wrong),
                                "ok"
                            )
                            callback(false)  // Failed to stop session due to null response
                        }
                    }
            }
        }
    }

    private fun showConcurrencyDialog(title: String, description: String, actionBtn: String) {
        try {
            var fm: FragmentManager = this.supportFragmentManager
            var commonDialogFragment =
                CommonDialogFragment.newInstance(title, description, actionBtn)
            commonDialogFragment.setEditDialogCallBack(this)
            commonDialogFragment.show(fm, AppConstants.MESSAGE)
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    private fun startSessionPlayback(callback: (Boolean) -> Unit) {
        if (!preference?.sessionId.isNullOrEmpty()) {
            stopPlaySessionID(true) { success ->
                callback(success)  // If stopping session was successful, continue to the next callback
            }
            return
        } else {
            deviceManagementViewModel?.startSessionPlayBack(
                preference?.appPrefAccessToken!!,
                assetId.toString(),
                ""
            )?.observe(this@HomeActivity) { response ->
                when {
                    response != null && response.responseCode == 2000 -> {
                        startSessionApiStart = true
                        preference?.sessionId = response.data?.sessionId ?: ""
                        callback(true)  // Session started successfully
                    }

                    response.responseCode == 4099 -> {
                        mPlayerView?.player?.pause()
                        showConcurrencyDialog(
                            resources.getString(R.string.screen_limit),
                            resources.getString(R.string.You_can_play) + " ${preference?.planMaxAllowedConcurrency} " + resources.getString(
                                R.string.screen_at_a_time_Please_stop_playing_another_screen
                            ),
                            resources.getString(R.string.ok)
                        )
                        callback(false)  // Concurrency issue, cannot start session
                    }

                    else -> {
                        mPlayerView?.player?.pause()
                        showConcurrencyDialog(
                            "",
                            resources.getString(R.string.something_went_wrong),
                            resources.getString(R.string.ok)
                        )
                        callback(false)  // Something went wrong, cannot start session
                    }
                }
            }
        }
        callback(false)  // Default case, handle unexpected conditions
    }

    private fun subMenuRecycleView(list: List<OrderedMenuItem>) {
        if (list.size > 3) {
            binding?.toolbar?.rightShadow?.hide()
            binding?.toolbar?.rightScroll?.hide()
            rotateImageLocaleWise(binding!!.toolbar.rightScroll)
        } else {
            binding?.toolbar?.rightShadow?.visibility = View.GONE
            binding?.toolbar?.rightScroll?.visibility = View.GONE
        }
        val adapter = SubMenuRecycleView(list) { orderedMenuItem ->
            tabId = orderedMenuItem.menuItem?.screenId
            Log.d("SubMenuActivity", "Screen ID: $tabId")
            menuItemActionType = orderedMenuItem.menuItem?.menuItemActionType
            personalizedValue = orderedMenuItem.menuItem?.personalizedValue
            type = orderedMenuItem.menuItem?.type
            isMoreGroup = orderedMenuItem.menuItem?.isMoreGroup == true
            binding?.toolbar?.recLayoutSubMenu?.visibility = View.VISIBLE

            if (!StringUtils.isNullOrEmpty(tabId)) {
                Log.d("SubMenuActivity", "New Fragment Created: $homeFragment")
                if (featureList?.featureFlag?.SEARCH == true) {
                    binding?.toolbar?.llSearchIcon?.visibility = View.VISIBLE
                    binding?.toolbar?.searchIcon?.visibility = View.VISIBLE
                } else {
                    binding?.toolbar?.llSearchIcon?.visibility = View.GONE
                    binding?.toolbar?.searchIcon?.visibility = View.INVISIBLE
                }
                if (featureList?.featureFlag?.IS_MUSIC_APP == true) fragmentManager?.popBackStackImmediate(
                    null, FragmentManager.POP_BACK_STACK_INCLUSIVE
                )
                ////add fragment here
                if (fragmentList[orderedMenuItem.menuItem?.id] == null) {
                    homeFragment = tabId?.let { HomeFragment(it, binding) }
                    ToolBarHandler.getInstance().setSubMenuAction(binding, View.VISIBLE)
                    binding?.toolbar?.recLayoutSubMenu?.visibility = View.VISIBLE
                    fragmentManager?.beginTransaction()
                        ?.add(R.id.content_frame, homeFragment as HomeFragment, "1")
                        ?.hide(homeFragment as HomeFragment)
                        ?.commitNow()
                    detailFrameVisibility()
                    active?.let { activity ->
                        homeFragment?.let { homeFragment ->
                            fragmentManager?.beginTransaction()?.hide(activity)
                                ?.show(homeFragment)?.commitNow()
                        }
                    }
                    active = homeFragment
                    orderedMenuItem.menuItem?.id?.let { id ->
                        fragmentList[id] = homeFragment!!
                    }
                } else {
                    fragmentList[orderedMenuItem.menuItem?.id]?.let {
                        ToolBarHandler.getInstance().setSubMenuAction(binding, View.VISIBLE)
                        binding?.toolbar?.recLayoutSubMenu?.visibility = View.VISIBLE
                        active?.let { activeFragment ->
                            fragmentManager?.beginTransaction()?.hide(activeFragment)?.show(it)
                                ?.commitNow()
                        }
                    }
                    active = fragmentList[orderedMenuItem.menuItem?.id]
                }
            }
            active = homeFragment
        }
        binding?.toolbar?.recLayoutSubMenu?.adapter = adapter

        val layoutManager = binding?.toolbar?.recLayoutSubMenu?.layoutManager as LinearLayoutManager
        val adapterSize = adapter.itemCount

        binding?.toolbar?.recLayoutSubMenu?.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                val firstVisible = layoutManager.findFirstCompletelyVisibleItemPosition()
                val lastVisible = layoutManager.findLastCompletelyVisibleItemPosition()

                binding?.toolbar?.rightShadow?.hide()
                binding?.toolbar?.rightScroll?.hide()
                rotateImageLocaleWise(binding!!.toolbar.rightScroll)

                // Show/hide left arrow
//               binding?.toolbar?.leftScroll?.visibility = if (firstVisible > 0){
//                   View.VISIBLE
//               }
//               else View.GONE
//
//               // Show/hide right arrow
//               binding?.toolbar?.rightScroll?.visibility = if (lastVisible < adapterSize - 1){
//                   View.VISIBLE
//               } else View.GONE
//
//               binding?.toolbar?.leftShadow?.visibility = if (firstVisible > 0){ View.VISIBLE } else View.GONE
//               binding?.toolbar?.rightShadow?.visibility = if (lastVisible < adapterSize - 1){ View.VISIBLE } else View.GONE
            }
        })
    }


    fun showSubMenu() {
        binding?.toolbar?.mainLay?.show()
        binding?.topGradient?.show()
    }

    fun hideSubMenu() {
        binding?.toolbar?.mainLay?.hide()
        binding?.topGradient?.hide()
    }


    private fun checkPlansForUser() {
        val token: String = KsPreferenceKeys.getInstance().appPrefAccessToken
        GetPlansLayer.getInstance().getEntitlementStatus(
            KsPreferenceKeys.getInstance(),
            token
        ) { entitlementStatus, apiStatus, _, _, _ ->

        }
    }

    fun launchGridList(screenTitle: String) {
        val args = Bundle()
        args.putString("assetType", assetType)
        args.putString(AppConstants.TITLE, screenTitle)
        val intent = Intent(this, GridListActivity::class.java)
        intent.putExtra(AppConstants.BUNDLE_ASSET_BUNDLE, args)
        gridListLauncher.launch(intent)
    }

    var reelDescription = ""
    var reelUserInterest = ""
    var thumbnailBitmap : Bitmap? = null

    private val shortVideoLauncher: ActivityResultLauncher<Intent> =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == RESULT_OK) {
            Log.d("VideoLauncher","${result.data}")
            reelDescription = result.data?.getStringExtra(ShortVideoUploadActivity.REEL_DESC) ?: ""
            reelUserInterest = result.data?.getStringExtra(ShortVideoUploadActivity.USER_INTEREST) ?: ""
            createNotification()
            registerLocalBroadCast()
            result.data?.getBundleExtra(AppConstants.BUNDLE_ASSET_BUNDLE)?.let { 
                val uri = it.getParcelable<Uri>(ShortVideoUploadActivity.VIDEO_URI)
                uri?.let { 
                    thumbnailBitmap = getVideoThumbnail(this,it)
                }
            }
            setUploadNotifiOnApp()
//            openShortsReelsFragment()
        }
    }

    private fun setUploadNotifiOnApp() {
        binding?.miniPlayerViews?.clVideoUpload?.visibility = View.VISIBLE
        binding?.miniPlayerViews?.linearLayoutView?.visibility = View.GONE
        binding?.miniPlayerViews?.epTimeBar?.visibility = View.GONE
        binding?.miniPlayerViews?.tvVideoUploadStatus?.text = getString(R.string.video_upload_in_progress_txt)
        binding?.miniPlayerViews?.progressBar?.progress = 0
        thumbnailBitmap?.let { 
            binding?.miniPlayerViews?.ivTumbnaileVideo?.setImageBitmap(it)
        }
        binding?.miniPlayer?.visibility = View.VISIBLE
    }

    private fun registerLocalBroadCast() {
        val filter = IntentFilter(UPLOAD_PROGRESS_ACTION)
        LocalBroadcastManager.getInstance(this).registerReceiver(uploadProgressReceiver, filter)
    }

    private fun unRegisterLocalBroadCast(){
        LocalBroadcastManager.getInstance(this).unregisterReceiver(uploadProgressReceiver)
    }

    private var notificationBuilder: NotificationCompat.Builder? = null
    private var notificationManager: NotificationManager? = null

    private fun createNotification() {
        notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        createNotificationChannel(this)

        notificationBuilder = NotificationCompat.Builder(this, "upload_channel")
            .setSmallIcon(R.drawable.ic_app_logo_icon)
            .setLargeIcon(BitmapFactory.decodeResource(resources, R.drawable.ic_app_logo_icon))
            .setContentTitle(getString(R.string.video_upload_notification_start_txt))
            .setProgress(100, 0, false)
            .setOngoing(true)
    }

    private val uploadProgressReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Log.d("VideoUpload","At uploadProgressReceiver, onReceive, broadcast received, action: ${intent?.action}")
            if (intent?.action == UPLOAD_PROGRESS_ACTION) {
                val progress = intent.getIntExtra(PROGRESS, 0)
                val uploadText = intent.getStringExtra(VIDEO_STATUS)
                val fileName = intent.getStringExtra(FILE_NAME)
                Log.d("VideoUpload","At uploadProgressReceiver, onReceive, initiate notification, progress: $progress, uploadText: $uploadText, fileName: $fileName")
                setNotificationStatus(uploadText!!,progress,fileName)
            }
        }
    }

    private fun createNotificationChannel(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                "upload_channel",
                "Upload Progress",
                NotificationManager.IMPORTANCE_LOW
            )
            val manager = context.getSystemService(NotificationManager::class.java)
            manager.createNotificationChannel(channel)
        }
    }

    private fun setNotificationStatus(status: String, progress: Int, fileName: String?) {
        Log.d("VideoUpload","At setNotificationStatus, status: $status, file name:$fileName, progress:$progress")
        when(status){
            "Upload In Progress"-> {
                notificationBuilder?.setProgress(100, progress, false)
                notifiProgressInApp(progress)
                notificationManager?.notify(NOTIFICATION_ID, notificationBuilder?.build())
            }
            "Upload Complete"-> {
                notificationBuilder
                    ?.setContentText(getString(R.string.video_upload_complete_text))
                    ?.setProgress(progress, progress, false)
                    ?.setOngoing(false)
                notifiProgressCompleteInApp(status)
                notificationManager?.notify(NOTIFICATION_ID, notificationBuilder?.build())
                callApiForUploadCreate(fileName)
                unRegisterLocalBroadCast()
            }
            else-> {
                notificationBuilder
                    ?.setContentText(status)
                    ?.setProgress(progress, progress, false)
                    ?.setOngoing(false)
                notificationManager?.notify(NOTIFICATION_ID, notificationBuilder?.build())
            }
        }
    }

    private fun notifiProgressCompleteInApp(status: String) {
        if (status.equals("Upload Complete",true)) {
            binding?.miniPlayerViews?.tvVideoUploadStatus?.text = getString(R.string.video_upload_complete_text)
        }else{
            binding?.miniPlayerViews?.tvVideoUploadStatus?.text = status
        }
        lifecycleScope.launch {
            delay(4000)
            binding?.miniPlayerViews?.llVideoUploadInfo?.visibility = View.GONE
            binding?.miniPlayer?.visibility = View.GONE
        }
    }

    private fun notifiProgressInApp(progress: Int) {
        binding?.miniPlayerViews?.progressBar?.progress = progress
    }

    private fun callApiForUploadCreate(fileName: String?) {
        val requestBody = JsonObject()
        requestBody.addProperty("title", fileName?.replace(".mp4","",true))
        requestBody.addProperty("contentType", AppConstants.VIDEO)
        val videoObject = JsonObject()
        videoObject.addProperty("storageURL", "$fileName")
        requestBody.add("video",videoObject)
        if(reelDescription.isNotEmpty()){
            requestBody.addProperty("description", reelDescription)
        }
        if(reelUserInterest.isNotEmpty()){
            requestBody.addProperty("keywords", reelUserInterest)
        }
        Log.d("VideoUpload","At callApiForUploadCreate, requestBody: $requestBody")
        APIServiceLayer.getInstance().callApiForUploadCreate(requestBody, object :
            NetworkResultCallback<JsonObject> {
            override fun loading(isLoading: Boolean) {}
            override fun success(status: Boolean?, response: Response<JsonObject>?) {
                Log.d("VideoUpload","At callApiForUploadCreate, success, response: ${response?.body()}")
                if (status == true){
                    Log.d("UploadCreate","${response?.body()}")
                }
            }
            override fun failure(status: Boolean, errorCode: Int, message: String) {
                Log.d("VideoUpload","At callApiForUploadCreate, failure, error: $message")
                Log.d("UploadCreate","API failure message: $message")
            }
        })
    }

    private fun launchShortVideoUploadActivity() {
        val intent = Intent(this, VideoImgActivity::class.java)
        intent.putExtra("CESDK_LICENSE", SDKConfig.IMGLY_LICENCE)
        exportLauncher.launch(intent)
    }

    private fun getVideoThumbnail(context: Context, videoUri: Uri): Bitmap? {
        val retriever = MediaMetadataRetriever()
        return try {
            retriever.setDataSource(context, videoUri)

            retriever.getFrameAtTime(0, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        } finally {
            retriever.release()
        }
    }
    companion object {
        const val QUEUED = "QUEUED"
        const val UPLOAD_PROGRESS_ACTION = "com.enveu.UPLOAD_PROGRESS"
        const val PROGRESS = "PROGRESS"
        const val VIDEO_STATUS = "VIDEO_STATUS"
        const val FILE_NAME = "FILE_NAME"
        const val NOTIFICATION_ID = 2501
    }
}