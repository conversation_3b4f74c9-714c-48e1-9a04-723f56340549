package com.enveu.activities.homeactivity.viewmodel;

import android.app.Application;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;

import com.enveu.beanModel.emptyResponse.ResponseEmpty;
import com.enveu.beanModel.responseModels.switchUserDetail.SwitchUser;
import com.enveu.beanModel.userProfile.UserProfileResponse;
import com.enveu.beanModelV3.searchGenres.SearchGenres;
import com.enveu.beanModelV3.searchHistory.SearchHistory;
import com.enveu.beanModelV3.searchRating.SearchRating;
import com.enveu.beanModelV3.searchV2.ResponseSearch;
import com.enveu.networking.servicelayer.APIServiceLayer;
import com.enveu.repository.home.HomeRepository;
import com.enveu.repository.userManagement.RegistrationLoginRepository;
import com.google.gson.JsonObject;


public class HomeViewModel extends AndroidViewModel {

    final HomeRepository homeRepository;
    final RegistrationLoginRepository loginRepository;

    public HomeViewModel(@NonNull Application application) {
        super(application);

        homeRepository = HomeRepository.getInstance();
        loginRepository = RegistrationLoginRepository.getInstance();
    }

    public LiveData<JsonObject> hitLogout(boolean session, String token) {
        return homeRepository.hitApiLogout(session, token);
    }

    public LiveData<ResponseEmpty> hitVerify(String token) {
        return homeRepository.hitApiVerifyUser(token);
    }

    public LiveData<UserProfileResponse> hitUserProfile(Context context, String token) {
        return loginRepository.getUserProfile(context,token);
    }

    public LiveData<SwitchUser> hitSwitchUser(String token, String  id) {
        return homeRepository.getSwitchUserAPIReponse(token,id);
    }

    public LiveData<SearchHistory> getSearchHistory(String token) {
        return APIServiceLayer.getInstance().getSearchHistory( token);
    }
   public LiveData<SearchHistory> deleteSearchHistory(String token, String local , String keyword , Boolean clearAll) {
        return APIServiceLayer.getInstance().deleteSearchHistory(token, local , keyword, clearAll);
    }
   public LiveData<SearchRating> getSearchRating(String token, String customType , String contentType) {
        return APIServiceLayer.getInstance().getSearchRating(token,contentType ,customType );
    }
   public LiveData<SearchGenres> getSearchGenres( String customType , String contentType) {
        return APIServiceLayer.getInstance().getSearchGenres(contentType ,customType );
    }

}
