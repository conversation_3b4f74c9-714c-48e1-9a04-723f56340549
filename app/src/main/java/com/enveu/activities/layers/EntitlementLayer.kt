package com.enveu.activities.layers

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.enveu.beanModel.JwDrmResponse.DrmResponse
import com.enveu.beanModel.drm.DRM
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.bean_model_v2_0.videoDetailBean.liveDetailBean.LiveStatusResponse
import com.enveu.callbacks.apicallback.EntitlementCallBack
import com.enveu.jwplayer.cast.PlayDetailResponse
import com.enveu.networking.apiendpoints.RequestConfig
import com.enveu.networking.detailPlayer.APIDetails
import com.enveu.networking.intercepter.ErrorCodesIntercepter
import com.google.gson.Gson
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.util.Objects

class EntitlementLayer {
    fun checkEntitlement(token: String?, sku: String?, entitlementCallBack: EntitlementCallBack) {
        val endpoint = RequestConfig.getUserInteration(token).create(
            APIDetails::class.java
        )
        val call = endpoint.checkEntitlement(sku)
        call.enqueue(object : Callback<ResponseEntitle?> {
            override fun onResponse(call: Call<ResponseEntitle?>, response: Response<ResponseEntitle?>, ) {
                if (response.code() == 200) {
                    val responseEntitlement = ResponseEntitle()
                    responseEntitlement.responseCode = response.code()
                    responseEntitlement.status = true
                    responseEntitlement.data = Objects.requireNonNull(response.body())?.data
                    val gson = Gson()
                    entitlementCallBack.entitlementStatus(responseEntitlement)
                } else {
                    val responseModel = ErrorCodesIntercepter.instance!!.checkEntitlement(response)
                    entitlementCallBack.entitlementStatus(responseModel)
                }            }

            override fun onFailure(call: Call<ResponseEntitle?>, t: Throwable) {
                val responseEntitlement = ResponseEntitle()
                responseEntitlement.status = false
                entitlementCallBack.entitlementStatus(responseEntitlement)            }
        })
    }

    fun hitAPIForREfID(accessToken : String?, sku: String?): LiveData<DRM?> {
        val requestAPI: MutableLiveData<DRM?> = MutableLiveData()
        val endpoint = RequestConfig.getEnveuClient().create(APIDetails::class.java);
        val call = endpoint.getSKUForPLayer(accessToken,sku)
        call.enqueue(object : Callback<DRM?> {
            override fun onResponse(call: Call<DRM?>, response: Response<DRM?>) {
                if (response.isSuccessful) {
                    if (response.body()?.responseCode == 2000 && response.body() != null) {
                        requestAPI.postValue(response.body())
                    }
                }
            }

            override fun onFailure(call: Call<DRM?>, t: Throwable) {
                requestAPI.postValue(null)
            }
        })
        return requestAPI
    }

    fun hitAPIForLicenseUrl(accessToken : String, sku: String?, playerId: String?): LiveData<String?> {
        val requestAPI: MutableLiveData<String?> = MutableLiveData()
        val endpoint = RequestConfig.getEnveuClient().create(
            APIDetails::class.java);
        val call = endpoint.getSignedUrlForDrmPlayback(accessToken,sku,playerId)
        call.enqueue(object : Callback<DRM?> {
            override fun onResponse(call: Call<DRM?>, response: Response<DRM?>) {
                if (response.isSuccessful) {
                    if (response.body() != null) {
                        val signedUrl = response.headers().get("SignedURL")
                        requestAPI.postValue(signedUrl)
                    }else{
                        requestAPI.postValue(null)
                    }
                }else{
                    requestAPI.postValue(null)
                }
            }

            override fun onFailure(call: Call<DRM?>, t: Throwable) {
                requestAPI.postValue(null)
            }
        })
        return requestAPI
    }

    fun hitApiEntitlement(token: String?, sku: String?): LiveData<ResponseEntitle> {
        val responseOutput = MutableLiveData<ResponseEntitle>()
        val endpoint = RequestConfig.getUserInteration(token).create(APIDetails::class.java)
        val call = endpoint.checkEntitlement(sku)
        call.enqueue(object : Callback<ResponseEntitle?> {
            override fun onResponse(call: Call<ResponseEntitle?>, response: Response<ResponseEntitle?>, ) {
                if (response.code() == 200) {
                    val responseEntitlement = ResponseEntitle()
                    responseEntitlement.responseCode = response.code()
                    responseEntitlement.status = true
                    responseEntitlement.data = Objects.requireNonNull(response.body())?.data
                    responseOutput.postValue(responseEntitlement)
                } else {
                    val responseModel = ErrorCodesIntercepter.instance!!.checkEntitlement(response)
                    responseOutput.postValue(responseModel)
                }
            }
            override fun onFailure(call: Call<ResponseEntitle?>, t: Throwable) {
                val responseEntitlement = ResponseEntitle()
                responseEntitlement.status = false
                responseOutput.postValue(responseEntitlement)            }
        })
        return responseOutput
    }

    fun getPlayDetails(url: String?): LiveData<PlayDetailResponse?> {
        val responseOutput = MutableLiveData<PlayDetailResponse?>()
        val endpoint = RequestConfig.getPlayRetrofit().create(
            APIDetails::class.java)
        val call = endpoint.getSubtitle(url)
        call.enqueue(object : Callback<PlayDetailResponse?> {
            override fun onResponse(
                call: Call<PlayDetailResponse?>,
                response: Response<PlayDetailResponse?>,
            ) {
                if (response.code() == 200) {
                    responseOutput.postValue(response.body())
                }            }

            override fun onFailure(call: Call<PlayDetailResponse?>, t: Throwable) {}
        })
        return responseOutput
    }

    fun getGeoBlocking(mediaContentId: String?): LiveData<com.enveu.activities.detail.viewModel.Response?> {
        val responseOutput =
            MutableLiveData<com.enveu.activities.detail.viewModel.Response?>()
        val endpoint = RequestConfig.getGeoBlocking().create(APIDetails::class.java)
        val call = endpoint.getGeoBlocking(mediaContentId)
        call.enqueue(object : Callback<com.enveu.activities.detail.viewModel.Response?> {
            override fun onResponse(
                call: Call<com.enveu.activities.detail.viewModel.Response?>,
                response: Response<com.enveu.activities.detail.viewModel.Response?>,
            ) {
                if (response.code() == 200) {
                    val response1 = com.enveu.activities.detail.viewModel.Response()
                    response1.responseCode = response.code()
                    response1.data = Objects.requireNonNull(response.body())?.data
                    responseOutput.postValue(response1)
                } else {
                    responseOutput.postValue(response.body())
                }            }

            override fun onFailure(
                call: Call<com.enveu.activities.detail.viewModel.Response?>,
                t: Throwable,
            ) {
                val response = com.enveu.activities.detail.viewModel.Response()
                responseOutput.postValue(response)
            }
        })
        return responseOutput
    }



    fun checkLiveStatus(eventId: Int?): LiveData<LiveStatusResponse?> {
        val responseOutput = MutableLiveData<LiveStatusResponse?>()
        val endpoint = RequestConfig.getCommonClient().create(APIDetails::class.java)
        val call = endpoint.checkLiveStatus(eventId!!)
        call.enqueue(object : Callback<LiveStatusResponse?> {
            override fun onResponse(
                call: Call<LiveStatusResponse?>,
                response: Response<LiveStatusResponse?>,
            ) {
                if (response.code() == 200) {
                    val response1 = LiveStatusResponse()
                    response1.responseCode = response.code()
                    response1.data = Objects.requireNonNull(response.body())?.data
                    responseOutput.postValue(response1)
                } else {
                    responseOutput.postValue(response.body())
                }            }

            override fun onFailure(
                call: Call<LiveStatusResponse?>,
                t: Throwable,
            ) {
                val response = LiveStatusResponse()
                responseOutput.postValue(response)
            }
        })
        return responseOutput
    }

    fun getWideVineUrl(drmBaseUrl: String, drmTokken: String): LiveData<DrmResponse> {
        val baseUrl = drmBaseUrl.substringBefore("v2/")
        val endpointAndToken = drmBaseUrl.substringAfter(baseUrl)
        val jwPlayerData: MutableLiveData<DrmResponse> = MutableLiveData()
        val endpoint = RequestConfig.getDrmClient(baseUrl)?.create(
            APIDetails::class.java)
        val call = endpoint?.getWidevineUrl(endpointAndToken,drmTokken)

        call?.enqueue(object : Callback<DrmResponse>{
            override fun onResponse(call: Call<DrmResponse>, response: Response<DrmResponse>) {
                if (response.isSuccessful && response.body() != null) {
                    jwPlayerData.postValue(response.body())
                }else{
                    jwPlayerData.postValue(null)
                }
            }

            override fun onFailure(call: Call<DrmResponse>, t: Throwable) {
                    jwPlayerData.postValue(null)
            }

        })
        return jwPlayerData
    }


    companion object {
        private var entitlement: EntitlementLayer? = null

        @JvmStatic
        @get:Synchronized
        val instance: EntitlementLayer?
            get() {
                if (entitlement == null) {
                    entitlement = EntitlementLayer()
                }
                return entitlement
            }
    }
}
