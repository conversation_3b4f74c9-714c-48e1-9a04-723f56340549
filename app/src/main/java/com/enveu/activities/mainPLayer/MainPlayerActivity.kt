package com.enveu.activities.mainPLayer

import android.graphics.Color
import android.os.Bundle
import android.transition.TransitionInflater
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.SeekBar
import androidx.lifecycle.ViewModelProvider
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.R
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.emptyResponse.ResponseEmpty
import com.enveu.beanModel.responseGetWatchlist.ResponseGetIsWatchlist
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent
import com.enveu.databinding.FragmentMainPlayerBinding
import com.enveu.fragments.dialog.AlertDialogFragment
import com.enveu.fragments.dialog.AlertDialogSingleButtonFragment
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.jwplayer.extension.percent
import com.enveu.jwplayer.extension.percentOf
import com.enveu.player.utils.Logger
import com.enveu.player.utils.TimeUtils
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.BottomDialogFragment
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.ToastHandler
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.moengage.core.internal.utils.showToast
import java.util.Objects

class MainPlayerActivity : BaseBindingFragment<FragmentMainPlayerBinding>(), AlertDialogFragment.AlertDialogListener, CommonDialogFragment.EditDialogListener {
    private var playerState: String? = null
    private var videoDuration: Long = 0
    private var assetId: Int = 0
    private var songList: List<DataItem>? = ArrayList()
    private var currentPlayingIndex: Int? = -1
    private var isBingeWatchEnable: Boolean? = false
    private var token: String? = null
    private var src: String? = null
    private var imageContent: ImageContent? = null
    private var preference: KsPreferenceKeys? = null
    var railInjectionHelper: RailInjectionHelper? = null
    private val stringsHelper by lazy { StringsHelper }
    private var mIsBound = false
    private var playerListener: PlayerListener? = null
    private var song:DataItem? = null
    private var watchListCounter = 0
    private var dragging: Boolean = false
    private var isLoggedOut = false
    private var bookmarkingViewModel: BookmarkingViewModel? = null


    override fun inflateBindingLayout(inflater: LayoutInflater): FragmentMainPlayerBinding {
        return FragmentMainPlayerBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        bookmarkingViewModel = ViewModelProvider(this)[BookmarkingViewModel::class.java]
        val inflater = TransitionInflater.from(requireContext())
        enterTransition = inflater.inflateTransition(R.transition.slide_up)
        exitTransition = inflater.inflateTransition(R.transition.slide_up)
        preference = KsPreferenceKeys.getInstance()
        token = preference?.appPrefAccessToken
        songList = arguments?.getParcelableArrayList(AppConstants.SONG_LIST)
        assetId = arguments?.getInt(AppConstants.ASSET_ID, 0)!!
        src = arguments?.getString(AppConstants.SRC)
        imageContent = arguments?.getSerializable(AppConstants.IMAGE_CONTENT) as ImageContent
        song = arguments?.getParcelable(AppConstants.SONG)
    }

    fun setPlayerListener(playerListener: PlayerListener) {
        this.playerListener = playerListener
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        playerState = arguments?.getString(AppConstants.PLAYER_STATE)
        if (playerState != null) {
            if (playerState!! == "PLAYING") {
                updatePlayPauseIcon(true)
            } else {
                updatePlayPauseIcon(false)
            }
        }
        hitApiIsWatchList()
        binding.backButton.setOnClickListener {
            backCall()
        }
        binding.pBar.elevation = 10F
        clickListener()
        song?.let {
            setImageAndBackground(song,imageContent)
            setTitleAndDescription(song)
        }

        binding?.textSongName?.let {
            it.setOnClickListener {
                if (song?.customData?.songsAlbumsId != null) {
                    backCall()
                    AppCommonMethod.setAlbumFragment(
                        requireActivity(),
                        song!!.customData.songsAlbumsId.contentSlug,
                        song!!.customData.songsAlbumsId.mediaType,
                        song!!.customData.songsAlbumsId.id.toString()
                    )
                }
            }
        } ?: run {
            Log.e("TextSongName", "textSongName view is null")
        }



        binding?.textArtistName?.setOnClickListener {
            it.setOnClickListener {
                if (song?.customData?.songsArtistIds != null) {
                    backCall()
                    AppCommonMethod.setArtistFragment(
                        requireActivity(),
                        song!!.customData.songsArtistIds[0].contentSlug,
                        song!!.customData.songsArtistIds[0].mediaType,
                        song!!.customData.songsArtistIds[0].id
                    )
                }
            }
        } ?: run {
            Log.e("TextSongName", "textSongName view is null")
        }
        setStateForShuffleIcon()
        setStateForRepeatIcon()
        isContentWatchlisted()
    }

    private fun backCall() {
        activity?.onBackPressed()
        playerListener?.onBack()
    }

    private fun isContentWatchlisted() {
        if (playerListener != null) {
            playerListener!!.isContentWatchlist()
        }
    }

    private fun setStateForShuffleIcon() {
        if (KsPreferenceKeys.getInstance().isShuffleEnabled) {
            binding.shuffle.setColorFilter(requireContext().resources.getColor(R.color.signup_radio_btn_checked_color))

        }else{
            binding.shuffle.setColorFilter(requireContext().resources.getColor(R.color.white))
        }
    }



    private fun setStateForRepeatIcon() {
        if (KsPreferenceKeys.getInstance().isRepeatEnabled) {
            binding.loop.setImageDrawable(requireActivity().getDrawable(R.drawable.loop_new))
            binding.loop.setColorFilter(requireContext().resources.getColor(R.color.signup_radio_btn_checked_color))

        } else if (KsPreferenceKeys.getInstance().singleSongRepeatEnable){
            binding.loop.setImageDrawable(requireActivity().getDrawable(R.drawable.loop_single))
            binding.loop.setColorFilter(requireContext().getResources().getColor(R.color.signup_radio_btn_checked_color))
        }else {
            binding.loop.setImageDrawable(requireActivity().getDrawable(R.drawable.loop_new))
            binding.loop.setColorFilter(requireContext().getResources().getColor(R.color.white))
        }
    }

    private fun setTitleAndDescription(song: DataItem?) {
        try {
            binding.titleText.text = song?.title
            binding?.textSongName?.text = song?.title
            binding?.textSongName?.isSelected = true
            if (song?.description !=null){
                binding?.textArtistName?.text = song?.customData?.songsAlbumsId?.title
            }else{
                binding?.textArtistName?.text = song?.customData?.songsArtistIds?.get(0)?.title
            }
        } catch (e:Exception) {
            Log.d("setTitleAndDescription", "setTitleAndDescription: ")
        }

    }

    private fun disableControls() {
        binding?.play?.isEnabled = false
        binding?.skipNext?.isEnabled = false
        binding?.skipPrevious?.isEnabled = false
    }


    private fun setImageAndBackground(songItem: DataItem?= null, imageContent: ImageContent?) {
        if (imageContent?.src.isNullOrEmpty()){
            binding?.imageText?.visibility = View.VISIBLE
            binding?.imageText?.text = songItem?.title
        }else {
            ImageHelper.getInstance(binding?.songImage?.context)
                .loadListImage(
                    binding?.songImage,
                   imageContent?.src
                )
        }
        binding?.mainPlayer?.background = AppCommonMethod.setGradientBackgroundColor(
            Color.parseColor(
                AppCommonMethod
                    .getDominantColor(imageContent)
            ),
            Color.parseColor("#00000000"),
            "TOP_TO_BOTTOM"
        )
    }

    private fun clickListener() {
        binding.play.setOnClickListener {
            playerListener?.onPlayPause()
        }

        binding.miniPlayer.setOnClickListener {

        }

        binding.queueParent.setOnClickListener{
            if (playerListener != null) {
                if (songList!= null && songList?.isNotEmpty()!!) {
                    playerListener?.onQueue()
                }
            }
        }

        binding.skipNext.setOnClickListener {
            if (playerListener != null) {
                if (songList!= null && songList!!.isNotEmpty()) {
                    binding.pBar.visibility = View.VISIBLE
                    playerListener!!.onNext()
                    playerListener?.isContentWatchlist()
                }
            }
        }

        binding.skipPrevious.setOnClickListener {
            if (playerListener != null) {
                if (songList!= null && songList!!.isNotEmpty()) {
                    binding.pBar.visibility = View.VISIBLE
                    playerListener!!.onPrevious()
                    playerListener?.isContentWatchlist()
                }
            }
        }

        binding.shuffle.setOnClickListener {
            if (playerListener != null) {
                if (KsPreferenceKeys.getInstance().isShuffleEnabled) {
                    KsPreferenceKeys.getInstance().setShuffleEnable(false)
                }else{
                    KsPreferenceKeys.getInstance().setShuffleEnable(true)
                }
                setStateForShuffleIcon()
                playerListener!!.shufflePlaylist()
            }
        }

        binding.loop.setOnClickListener {
            if (KsPreferenceKeys.getInstance().isRepeatEnabled) {
                KsPreferenceKeys.getInstance().singleSongRepeatEnable=true
                KsPreferenceKeys.getInstance().setRepeatEnable(false)
            }else if (KsPreferenceKeys.getInstance().singleSongRepeatEnable){
                KsPreferenceKeys.getInstance().singleSongRepeatEnable=false
            }else {
                KsPreferenceKeys.getInstance().setRepeatEnable(true)
            }
            setStateForRepeatIcon()
        }
        binding.epTimeBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                //binding.epTimeBar.progress = progress
                binding.tvEpPosition.text = TimeUtils.formatDurationJw(progress.toLong())
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                dragging = true
//                controlClickListener.onProgressDragStart()
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                dragging = false
                binding.epTimeBar.progress = seekBar.progress
                seekPlayerTo(seekBar.progress.percentOf(videoDuration).toDouble())
            }
        })

        binding.watchList.setOnClickListener {
            if (playerListener != null) {
                val bottomSheetDialog = BottomDialogFragment.getInstance(song,AppConstants.FULL_PLAYER)
                bottomSheetDialog.PlayerListener(playerListener)
                imageContent?.src?.let {
                    bottomSheetDialog.setDefaultImgOfTopPoster(it)
                }
                bottomSheetDialog.show(activity?.supportFragmentManager!!, "Bottom Sheet Dialog Fragment")
            }
        }
        binding.watchListParent.setOnClickListener {
            if (binding.addProgressBar.visibility != View.VISIBLE) {
                val isLogin = preference!!.appPrefLoginStatus
                if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
                    setWatchListForAsset(1)
                }
            }
        }

//        binding.shareWith.setOnClickListener {
//            if (playerListener != null) {
//                playerListener!!.openShareDialog()
//            }
//        }

    }

    private fun showDialog(title: String, message: String) {
        val fm = requireActivity().supportFragmentManager
        val alertDialog = AlertDialogSingleButtonFragment.newInstance(
            title,
            message,
            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_ok.toString(), getString(R.string.popup_ok))
        )
        alertDialog.isCancelable = false
        alertDialog.setAlertDialogCallBack(this)
        alertDialog.show(fm, "fragment_alert")
    }



    override fun onFinishDialog() {
        if (isLoggedOut) {
            logoutCall()
        }
    }
    private fun setWatchListForAsset(from: Int) {
        binding!!.addProgressBar.visibility = View.VISIBLE
        binding!!.addWatchlist.visibility = View.GONE
        if (watchListCounter == 0) {
            hitApiAddWatchList(from)
        } else {
            hitApiRemoveList()
        }
    }

    private fun hitApiAddWatchList(from: Int) {
        if (activity is SeriesDetailActivity) {
            (activity as SeriesDetailActivity?)!!.seriesLoader()
        }
        bookmarkingViewModel!!.hitApiAddWatchList(token, assetId).observe(viewLifecycleOwner) { responseEmpty: ResponseEmpty ->
            binding!!.addProgressBar.visibility = View.GONE
            binding!!.addWatchlist.visibility = View.VISIBLE
            if (Objects.requireNonNull(responseEmpty).isStatus) {
                AnalyticsUtils.logUserInteractionEvent(context,AppConstants.ADD_TO_WATCHLIST,assetId.toString(),song?.title,song?.contentType)
                setWatchListSong()
            } else {
                if (responseEmpty.responseCode == 4302) {
                    isLoggedOut = true
                    logoutCall()
                } else if (responseEmpty.responseCode == 4904) {
                    setWatchListSong()
                    val debugMessage = responseEmpty.debugMessage
                    //from value will bedepends on how user click of watchlist icon-->>if loggedout=2 else=2
                    if (from == 1) {
                        showToast(requireActivity(), getString(R.string.error))
                    }
                } else if (responseEmpty.responseCode == 500) {
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )
                } else {
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )
                }
            }
        }
    }

    private fun hitApiRemoveList() {
        bookmarkingViewModel!!.hitRemoveWatchlist(token, assetId).observe(viewLifecycleOwner) { responseEmpty: ResponseEmpty ->
            binding!!.addProgressBar.visibility = View.GONE
            binding!!.addWatchlist.visibility = View.VISIBLE
            if (Objects.requireNonNull(responseEmpty).isStatus) {
                AnalyticsUtils.logUserInteractionEvent(context,AppConstants.REMOVE_WATCHLIST,assetId.toString(),song?.title,song?.contentType)
                resetWatchListSong()
            } else {
                if (responseEmpty.responseCode == 4302) {
                    isLoggedOut = true
                    logoutCall()
                } else if (responseEmpty.responseCode == 500) {
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )
                }
            }
        }
    }

    private fun hitApiIsWatchList() {
        if (preference!!.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            bookmarkingViewModel!!.hitApiIsWatchList(token, assetId).observe(viewLifecycleOwner) { responseEmpty: ResponseGetIsWatchlist ->
                if (Objects.requireNonNull(responseEmpty).isStatus) {
                    setWatchListSong()
                } else {
                    if (responseEmpty.responseCode == 4302) {
                        isLoggedOut = true
                        logoutCall()
                    } else if (responseEmpty.responseCode == 500) {
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
                }
            }
        }
    }

    private fun setWatchListSong() {
        binding!!.addProgressBar.visibility = View.GONE
        binding!!.addWatchlist.visibility = View.VISIBLE
        watchListCounter = 1
        binding!!.addWatchlist.setImageResource(R.drawable.added_to_watch_list)
    }

    private fun resetWatchListSong() {
        binding!!.addProgressBar.visibility = View.GONE
        binding!!.addWatchlist.visibility = View.VISIBLE
        watchListCounter = 0
        binding!!.addWatchlist.setImageResource(R.drawable.add_watch_list)
    }

    private fun logoutCall() {
        if (CheckInternetConnection.isOnline(requireActivity())) {
            clearCredientials(preference)
            hitApiLogout(baseActivity, preference!!.appPrefAccessToken)
        } else {
            ToastHandler.getInstance().show(activity, getString(R.string.no_internet_connection))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun onStart() {
        super.onStart()
    }

    override fun onStop() {
        super.onStop()
    }


    fun updatePlayPauseIcon(isPlaying: Boolean) {
        if (isPlaying) {
            binding.play.setImageResource(R.drawable.pause_circle_filled)
        } else {
            binding.play.setImageResource(R.drawable.play_circle_filled)
            updatePlayerControls()
        }
    }

    fun onDataReceived(position: Double, duration: Double) {
        duration.let { updateDuration(it, position) }
        position.let { updateProgress(it) }
    }

    fun onBuffer(isBuffering: Boolean) {
        Logger.d("IsDataReceiving", "Buffer")
    }

    fun onPlay(isBuffering: Boolean) {
        updatePlayPauseIcon(true)
    }

    fun updateDuration(duration: Double, progress: Double) {
        videoDuration = duration.toLong()
        if (binding?.epTimeBar!=null&& binding?.tvEpDuration!=null) {
            val remainingDuration = duration-progress
            binding.epTimeBar.max = 100
            binding.tvEpDuration.text = "- " + TimeUtils.formatDurationJw(remainingDuration.toLong())

        }
    }

    fun updateProgress(progress: Double) {
        if (binding?.epTimeBar!=null&& binding?.tvEpDuration!=null) {
            if (!dragging) {
                binding.epTimeBar.progress = progress.toLong().percent(videoDuration)
            }
            binding.tvEpPosition.text = TimeUtils.formatDurationJw(progress.toLong())
        }
    }

    private fun seekPlayerTo(position: Double) {
        updateProgress(position)
        playerListener?.seekTo(position)
    }

    private fun shouldShowBingeWatch(size: Int?): Boolean {
        var shouldBingeWatchShow: Boolean? = false
        Log.d("currentPlayingIndex", "getCurrentPlayingIndex: $currentPlayingIndex")
        if (size != null) {
            shouldBingeWatchShow = currentPlayingIndex!! < size
        }

        if (!shouldBingeWatchShow!!) {
            isBingeWatchEnable = false
        }

        return shouldBingeWatchShow
    }

    private fun parseColor() {
        binding!!.stringData = stringsHelper
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = activity?.supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm!!, AppConstants.MESSAGE)
    }

    override fun onActionBtnClicked() {

    }

    override fun onCancelBtnClicked() {
    }

    private fun updatePlayerControls() {
        if (playerListener != null) {
            playerListener?.updateControls()
        }
    }

    fun dismissProgress() {
        binding.pBar.visibility = View.GONE
    }

    fun updateUI(songItem: DataItem?) {
        songItem?.images?.let {
            setImageAndBackground(songItem,songItem.imageContent)
        }
        setTitleAndDescription(songItem)
        binding.pBar.visibility = View.GONE
    }

    fun hideSeekBar() {
        binding.pBar.visibility = View.GONE
    }

    fun setWatchlist() {
        binding!!.wProgressBar.visibility = View.GONE
        binding!!.addIcon.visibility = View.VISIBLE
        watchListCounter = 1
//        binding!!.addIcon.setImageResource(R.drawable.ic_addedlist)
    }

    fun resetWatchList() {
        binding!!.wProgressBar.visibility = View.GONE
        binding!!.addIcon.visibility = View.VISIBLE
        watchListCounter = 0
        binding!!.addIcon.setImageResource(R.drawable.add_to_watchlist)

    }
    fun seekToStart() {
        seekPlayerTo(0.0)
    }

    interface PlayerListener {
        fun onBack()

        fun onPlayPause()

        fun seekTo(position: Double)

        fun onPlay(externalRefId: String)

        fun onNext()
        fun onQueue()

        fun onPrevious()

        fun shufflePlaylist()

        fun isContentWatchlist()

        fun callWatchlistApi(watchlistCounter: Int)

        fun openShareDialog()

        fun updateControls()
    }

}