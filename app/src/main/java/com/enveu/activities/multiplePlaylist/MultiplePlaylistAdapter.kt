package com.enveu.activities.multiplePlaylist

import android.annotation.SuppressLint
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.bean_model_v2_0.MusicPlaylist.MyPlaylistData
import com.enveu.bean_model_v2_0.MusicPlaylist.PlaylistDetails
import com.enveu.databinding.MyPlaylistItemBinding
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys

class MultiplePlaylistAdapter(var data: MutableList<MyPlaylistData.Data.Item?>?, private val itemCLick: OnItemCLick) : RecyclerView.Adapter<MultiplePlaylistAdapter.PlaylistViewHolder>() {
    class PlaylistViewHolder(val binding: MyPlaylistItemBinding) : RecyclerView.ViewHolder(binding.root){
        var title = binding.title
        val desc = binding.desc
        val threeDot = binding.threeDot
        val view = binding.view
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PlaylistViewHolder {
        return PlaylistViewHolder(MyPlaylistItemBinding.inflate(LayoutInflater.from(parent.context),parent, false))
    }

    override fun getItemCount(): Int {
        return data?.size!!
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: PlaylistViewHolder, position: Int) {

        if (data?.get(position)?.type?.equals("USER_DEFINED", ignoreCase = true) == true){
            holder.threeDot.visibility = View.VISIBLE
        }else{
            holder.threeDot.visibility = View.GONE
        }

        holder.title.text = data?.get(position)?.title!!
        if (KsPreferenceKeys.getInstance()?.appLanguage?.equals(AppConstants.LANGUAGE_ARABIC, true) == true){
            holder.title.gravity = Gravity.END
            holder.desc.gravity =  Gravity.END
        }
        if (data?.get(position)?.contentCount == null){
            holder.desc.text =  "0 Tracks"
        }else{
            holder.desc.text = data?.get(position)?.contentCount.toString()  + " " + holder.desc.context.getString(R.string.tracks)
        }

        holder.threeDot.setOnClickListener {
            itemCLick.threeDotClick(data?.get(position)?.id.toString(), adapterItemPosition =  position)
    }
        holder.itemView.setOnClickListener {
            data?.get(position)?.playlistSlug?.let {
                itemCLick.itemCLick(it, playlistID = data!![position]?.id,position =  position, orderCount = data!![position]?.contentCount, title =  data?.get(position)?.title)
            }
        }
        if (position == data?.size!! - 1) {
            holder.view.visibility = View.GONE
        } else {
            holder.view.visibility = View.VISIBLE
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateList(data: ArrayList<MyPlaylistData.Data.Item?>?){
        this.data = data
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun removeAdapterItems(position: Int?){
        if (position != null) {
            data?.removeAt(position)
            notifyItemRemoved(position)
        }
        notifyDataSetChanged()

    }


    interface OnItemCLick{
        fun threeDotClick(position: String, type : String?= null, mediaContentID : Int?= null, adapterItemPosition: Int)
        fun itemCLick(playlistSlug : String, playlistID: String?= null, data : PlaylistDetails?= null, position : Int, orderCount: Int ?= null, title:String?= null)
    }
}