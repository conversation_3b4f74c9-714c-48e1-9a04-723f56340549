package com.enveu.activities.multiplePlaylist

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.enveu.bean_model_v2_0.MusicPlaylist.AddSongPlaylist
import com.enveu.bean_model_v2_0.MusicPlaylist.CheckContentIsLike
import com.enveu.bean_model_v2_0.MusicPlaylist.CreatePlaylist
import com.enveu.bean_model_v2_0.MusicPlaylist.DeleteMusicPlaylist
import com.enveu.bean_model_v2_0.MusicPlaylist.MyPlaylistData
import com.enveu.bean_model_v2_0.MusicPlaylist.PlaylistDetails
import com.enveu.networking.apiendpoints.ApiInterface
import com.enveu.networking.apiendpoints.RequestConfig
import com.google.gson.JsonObject
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

object MultiplePlaylistRepo {

    fun createPlaylist(token: String?, title: String?): LiveData<CreatePlaylist?> {
        val createPlaylist = MutableLiveData<CreatePlaylist?>()
        val body = JsonObject()
        body.addProperty("title", title)
        RequestConfig.getMonetizationBaseURL(token).create(ApiInterface::class.java).createMusicPlaylist(body)?.enqueue(object : Callback<CreatePlaylist?> {
                override fun onResponse(call: Call<CreatePlaylist?>, response: Response<CreatePlaylist?>) {
                    if (response.body() != null && response.code() == 200) {
                        createPlaylist.postValue(response.body())
                    } else {
                        createPlaylist.postValue(response.body())
                    }
                }

                override fun onFailure(call: Call<CreatePlaylist?>, t: Throwable) {
                    createPlaylist.postValue(null)
                }
            })

        return createPlaylist
    }


    fun deleteMusicPlaylist(token: String?, playlistID: String?): LiveData<DeleteMusicPlaylist?> {
        val deleteMusicPlaylistMutableLiveData = MutableLiveData<DeleteMusicPlaylist?>()

        RequestConfig.getMonetizationBaseURL(token).create(ApiInterface::class.java)
            .deleteMusicPlaylist(playlistID)?.enqueue(object : Callback<DeleteMusicPlaylist?> {
                override fun onResponse(
                    call: Call<DeleteMusicPlaylist?>, response: Response<DeleteMusicPlaylist?>
                ) {
                    if (response.body() != null && response.code() == 200) {
                        deleteMusicPlaylistMutableLiveData.postValue(response.body())
                    } else {
                        deleteMusicPlaylistMutableLiveData.postValue(null)
                    }
                }

                override fun onFailure(call: Call<DeleteMusicPlaylist?>, t: Throwable) {
                    deleteMusicPlaylistMutableLiveData.postValue(DeleteMusicPlaylist(null, t.message, 400))
                }
            })

        return deleteMusicPlaylistMutableLiveData
    }


    fun getDetailPlaylistData(token: String?, playlistSlug: String?): LiveData<PlaylistDetails?> {
        val detailsMutableLiveData = MutableLiveData<PlaylistDetails?>()

        RequestConfig.getMonetizationBaseURL(token).create(ApiInterface::class.java)
            .getMyPlaylistDetails(playlistSlug, "en-US")
            ?.enqueue(object : Callback<PlaylistDetails?> {
                override fun onResponse(
                    call: Call<PlaylistDetails?>, response: Response<PlaylistDetails?>
                ) {
                    if (response.body() != null && response.code() == 200) {
                        detailsMutableLiveData.postValue(response.body())
                    } else {
                        detailsMutableLiveData.postValue(null)
                    }
                }

                override fun onFailure(call: Call<PlaylistDetails?>, t: Throwable) {
                    detailsMutableLiveData.postValue(null)
                }
            })


        return detailsMutableLiveData
    }

    fun getPlaylists(token: String?,): LiveData<MyPlaylistData?> {
        val playlistDataMutableLiveData = MutableLiveData<MyPlaylistData?>()
        RequestConfig.getMonetizationBaseURL(token).create(ApiInterface::class.java)
            .getMusicPlaylists("QUEUED,LIKED,USER_DEFINED")?.enqueue(object : Callback<MyPlaylistData?> {
                override fun onResponse(
                    call: Call<MyPlaylistData?>, response: Response<MyPlaylistData?>
                ) {
                    if (response.body() != null && response.code() == 200) {
                        playlistDataMutableLiveData.postValue(response.body())
                    } else {
                        playlistDataMutableLiveData.postValue(null)
                    }
                }

                override fun onFailure(call: Call<MyPlaylistData?>, t: Throwable) {
                    playlistDataMutableLiveData.postValue(null)
                }
            })

        return playlistDataMutableLiveData
    }

    fun addContentToPlaylist(token: String, mediaContentIds: Int, type: String, playlistID: String?, isPlayList: Boolean) : LiveData<AddSongPlaylist>{
        val mutableLiveData : MutableLiveData<AddSongPlaylist> = MutableLiveData()
        if (type == "") {
            RequestConfig.getMonetizationBaseURL(token).create(ApiInterface::class.java)
                .addContentToPlaylist(ApiRequestAddContent(listOf(mediaContentIds),if(playlistID != null) listOf(playlistID) else listOf(),true)).enqueue(object : Callback<AddSongPlaylist> {
                    override fun onResponse(
                        call: Call<AddSongPlaylist>,
                        response: Response<AddSongPlaylist>
                    ) {
                        if (response.body() != null && response.code() == 200) {
                            mutableLiveData.postValue(response.body())
                        } else {
                            mutableLiveData.postValue(AddSongPlaylist(null, "", response.code()))
                        }
                    }
                    override fun onFailure(call: Call<AddSongPlaylist>, t: Throwable) {
                        mutableLiveData.postValue(null)
                    }

                })
        } else {
            if (isPlayList){
                RequestConfig.getMonetizationBaseURL(token).create(ApiInterface::class.java)
                    .addContentToPlaylist(ApiRequestForAddContentOne(mediaContentIds, if(playlistID != null) listOf(playlistID) else listOf(), true,type)).enqueue(object : Callback<AddSongPlaylist> {
                        override fun onResponse(
                            call: Call<AddSongPlaylist>,
                            response: Response<AddSongPlaylist>
                        ) {
                            if (response.body() != null && response.code() == 200) {
                                mutableLiveData.postValue(response.body())
                            } else {
                                mutableLiveData.postValue(AddSongPlaylist(null, "", response.code()))
                            }
                        }

                        override fun onFailure(call: Call<AddSongPlaylist>, t: Throwable) {
                            mutableLiveData.postValue(null)
                        }

                    })
            }else{
                RequestConfig.getMonetizationBaseURL(token).create(ApiInterface::class.java)
                    .addContentToPlaylistForLike(ApiRequestForAddContent(listOf(mediaContentIds),true,type)).enqueue(object : Callback<AddSongPlaylist> {
                        override fun onResponse(
                            call: Call<AddSongPlaylist>,
                            response: Response<AddSongPlaylist>
                        ) {
                            if (response.body() != null && response.code() == 200) {
                                mutableLiveData.postValue(response.body())
                            } else {
                                mutableLiveData.postValue(AddSongPlaylist(null, "", response.code()))
                            }
                        }

                        override fun onFailure(call: Call<AddSongPlaylist>, t: Throwable) {
                            mutableLiveData.postValue(null)
                        }

                    })
            }
        }

        return mutableLiveData
    }


    fun removeContentToPlaylist(
        token: String,
        mediaContentIds: Int,
        type: String,
        playlistID: String,
        from: Boolean
    ) : LiveData<MyPlaylistData>{
        val mutableLiveData : MutableLiveData<MyPlaylistData> = MutableLiveData()
        if (from){
            RequestConfig.getMonetizationBaseURL(token).create(ApiInterface::class.java)
                .removeSongFromPlaylist(
                    ApiRequestForRemoveContent(
                        playlistID,
                        listOf(mediaContentIds),type
                    )
                ).enqueue(object : Callback<MyPlaylistData> {
                    override fun onResponse(
                        call: Call<MyPlaylistData>,
                        response: Response<MyPlaylistData>
                    ) {
                        if (response.body() != null && response.code() == 200) {
                            mutableLiveData.postValue(response.body())
                        } else {
                            mutableLiveData.postValue(MyPlaylistData(null,response.message(), response.code()))
                        }
                    }

                    override fun onFailure(call: Call<MyPlaylistData>, t: Throwable) {
                        mutableLiveData.postValue(MyPlaylistData(null,t.message, 400))
                    }

                })

        }else{
            RequestConfig.getMonetizationBaseURL(token).create(ApiInterface::class.java)
                .removeSongFromPlaylist(playlistID?.let {
                    ApiRequestForRemoveContent(
                        it,
                        listOf(mediaContentIds)
                    )
                }).enqueue(object : Callback<MyPlaylistData> {
                    override fun onResponse(
                        call: Call<MyPlaylistData>,
                        response: Response<MyPlaylistData>
                    ) {
                        if (response.body() != null && response.code() == 200) {
                            mutableLiveData.postValue(response.body())
                        } else {
                            mutableLiveData.postValue(MyPlaylistData(null,response.message(), response.code()))
                        }
                    }

                    override fun onFailure(call: Call<MyPlaylistData>, t: Throwable) {
                        mutableLiveData.postValue(MyPlaylistData(null,t.message, 400))
                    }
                })
        }


        return mutableLiveData
    }


    fun checkContentIsPresentInLike(token: String?, assertID: Int): MutableLiveData<CheckContentIsLike> {
        val mutableLiveData: MutableLiveData<CheckContentIsLike> = MutableLiveData()

        RequestConfig.getMonetizationBaseURL(token).create(ApiInterface::class.java)
            .checkContentPresentLike(assertID).enqueue(object : Callback<CheckContentIsLike> {
                override fun onResponse(
                    call: Call<CheckContentIsLike>, response: Response<CheckContentIsLike>
                ) {
                    if (response.code() == 200) {
                        mutableLiveData.postValue(response.body())
                    }
                }

                override fun onFailure(call: Call<CheckContentIsLike>, t: Throwable) {
                    mutableLiveData.postValue(CheckContentIsLike(null,t.message, 400))
                }
            })

        return mutableLiveData
    }


    fun renamePlaylist(token: String?, id : String, title: String?): LiveData<MyPlaylistData?> {
        val playlistDataMutableLiveData = MutableLiveData<MyPlaylistData?>()

        RequestConfig.getMonetizationBaseURL(token).create(ApiInterface::class.java).renamePlaylistName(ApiRequestForRenamePlaylist(id,title)).enqueue(
            object : Callback<MyPlaylistData>{
                override fun onResponse(call: Call<MyPlaylistData>, response: Response<MyPlaylistData>) {
                    if (response.body() != null && response.code() == 200){
                        playlistDataMutableLiveData.postValue(response.body())
                    }else{
                        playlistDataMutableLiveData.postValue(null)
                    }
                }

                override fun onFailure(call: Call<MyPlaylistData>, t: Throwable) {
                    playlistDataMutableLiveData.postValue(MyPlaylistData(null,t.message, 400))
                }

            }
        )
        return playlistDataMutableLiveData
    }

    data class ApiRequestForAddContent(
        val mediaContentIds: List<Int>,
//        val playlistIds: List<String>,
        val addToTop: Boolean,
        val type: String
    )

    data class ApiRequestAddContent(
        val mediaContentIds: List<Int>,
        val playlistIds: List<String>,
        val addToTop: Boolean
    )

    data class ApiRequestForAddContentOne(
        val mediaContentIds: Int,
        val playlistIds: List<String>,
        val addToTop: Boolean,
        val type: String
    )

    data class ApiRequestForRemoveContent(
        val playlistIds: String,
        val mediaContentIds: List<Int>,
        val type: String?= null
    )
        data class ApiRequestForRenamePlaylist(
        val id : String,
        val title: String?
    )



}