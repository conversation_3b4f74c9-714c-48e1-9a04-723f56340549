package com.enveu.activities.multiplePlaylist

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.enveu.bean_model_v2_0.MusicPlaylist.AddSongPlaylist
import com.enveu.bean_model_v2_0.MusicPlaylist.CheckContentIsLike
import com.enveu.bean_model_v2_0.MusicPlaylist.CreatePlaylist
import com.enveu.bean_model_v2_0.MusicPlaylist.DeleteMusicPlaylist
import com.enveu.bean_model_v2_0.MusicPlaylist.MyPlaylistData
import com.enveu.bean_model_v2_0.MusicPlaylist.PlaylistDetails

class MultiplePlaylistViewModel : ViewModel() {


    fun createMusicPlaylist(token: String?, title: String?): LiveData<CreatePlaylist?>? {
        return MultiplePlaylistRepo.createPlaylist(token, title)
    }
    fun deleteMusicPlaylist(token: String?, playlistID: String?): LiveData<DeleteMusicPlaylist?>? {
        return MultiplePlaylistRepo.deleteMusicPlaylist(token, playlistID)
    }
    fun getPlayListAll(token: String?): LiveData<MyPlaylistData?> {
        return MultiplePlaylistRepo.getPlaylists(token)
    }
    fun playlistDetail(token: String? , playlistID: String?): LiveData<PlaylistDetails?>? {
        return MultiplePlaylistRepo.getDetailPlaylistData(token, playlistID)
    }
    fun addContentPlaylist(
        token: String,
        mediaContentIds: Int,
        type: String,
        playlistID: String?,
        isPlayList: Boolean
    ): LiveData<AddSongPlaylist> {
        return MultiplePlaylistRepo.addContentToPlaylist(token,mediaContentIds, type,playlistID,isPlayList)
    }

    fun checkContentIsPresentInLike(token: String?,assertID : Int) : LiveData<CheckContentIsLike>{
     return MultiplePlaylistRepo.checkContentIsPresentInLike(token,assertID)
    }

    fun removeContentPlaylist(
        token: String,
        mediaContentIds: Int,
        type: String,
        playlistID: String,
        from: Boolean
    ): LiveData<MyPlaylistData> {
        return MultiplePlaylistRepo.removeContentToPlaylist(token,mediaContentIds, type,playlistID,from)
    }

    fun updatePlaylistName(token: String , id : String, title: String?) : LiveData<MyPlaylistData?> {
     return MultiplePlaylistRepo.renamePlaylist(token,id , title)
    }
}