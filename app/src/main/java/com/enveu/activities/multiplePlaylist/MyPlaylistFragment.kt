package com.enveu.activities.multiplePlaylist

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.watchList.ui.WatchListActivity
import com.enveu.adapters.CommonShimmerAdapter
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.bean_model_v2_0.MusicPlaylist.MyPlaylistData
import com.enveu.bean_model_v2_0.MusicPlaylist.PlaylistDetails
import com.enveu.databinding.ActivityMyPlaylistActviityBinding
import com.enveu.databinding.CustomPopupBinding
import com.enveu.fragments.more.ui.MoreFragment
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.android.material.bottomsheet.BottomSheetDialog

class MyPlaylistFragment : BaseBindingFragment<ActivityMyPlaylistActviityBinding>(),
    PlaylistOptionsBottomSheet.RemoveSongFromPlaylist {
    private var playlistData: MyPlaylistData? = null
    private var playlistViewModel: MultiplePlaylistViewModel? = null
    private var preferenceKeys: KsPreferenceKeys? = null
    private var adapter: MultiplePlaylistAdapter? = null
    private var playListData: PlaylistDetails? = null
    private var adapterPurchase: CommonShimmerAdapter? = null
    private var dialog: BottomSheetDialog? = null
    private var PlaylistID: String? = null
    private var adapterPosition: Int? = -1
    private var dialogTitle: EditText? = null
    private var dialogButton: Button? = null
    private var dialogText: TextView? = null
    private lateinit var playListApiCallListener: PlayListApiCallListener
    private var errorMsgText: TextView? = null
    private var featureModel: FeatureFlagModel? = null
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    private var from = " "
    private var queued = ""
    private var liked = ""
    private var songID = 0
    private var mListener: MoreFragment.OnMyListInteractionListener? = null

    interface OnMyListInteractionListener {
        fun onFragmentInteraction()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
       /* if (activity != null && activity is HomeActivity)
            (activity as HomeActivity).toolFrame(0)
        (activity as HomeActivity).detailFrameVisibility()*/
        if (arguments != null && arguments?.getInt("SongID") != null) {
            arguments?.getString("from").let {
                from = it.toString()
                binding?.newPlaylist?.visibility = View.VISIBLE
            }


            arguments?.getInt("songID").let {
                songID = it!!.toInt()
            }
        }

        dialog = BottomSheetDialog(requireContext())
        val dialogView = LayoutInflater.from(requireContext()).inflate(R.layout.cutom_new_playlist_ui, null)
        dialog?.setContentView(dialogView)
        dialogTitle = dialogView.findViewById(R.id.playlist_title)
        dialogButton = dialogView.findViewById(R.id.create_btn)
        dialogText = dialogView.findViewById(R.id.dialog_title)
        errorMsgText = dialogView.findViewById(R.id.errorMsgText)
        init()
        setClicks()
    }

    @SuppressLint("MissingInflatedId")
    private fun showCustomDialog() {
        // Access the custom layout elements
        dialogText?.text = getString(R.string.create_playlist_title_text)
        dialogButton?.text = getString(R.string.create_playlist_button_title_text)
        dialogTitle?.setText("")
        dialogButton?.setOnClickListener {
            val input = dialogTitle?.text.toString()
            when {
                input.isNotEmpty() -> {
                    dialog?.dismiss()
                    createPlaylist(dialogTitle?.text.toString())
                }
                input.length > 15 -> {
                    errorMsgText?.text = getString(R.string.enter_playlist_name_limit_msg)
                }
                else -> {
                    errorMsgText?.text = getString(R.string.enter_playlist_name_blank_msg)
                }
            }
        }
        dialog?.show()
        checkPlaylistTitleCondition()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).toolFrame(1)
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).detailFrameVisibility()

        mListener = if (context is MoreFragment.OnMyListInteractionListener) {
            context
        } else {
            throw RuntimeException(
                context.toString()
                        + " must implement OnFragmentInteractionListener"
            )
        }
    }

    private fun callShimmer(context: Context, recyclerView: RecyclerView) {
        adapterPurchase = CommonShimmerAdapter()
        val mLayoutManager: RecyclerView.LayoutManager = LinearLayoutManager(context)
        recyclerView.layoutManager = mLayoutManager
        recyclerView.itemAnimator = DefaultItemAnimator()
        recyclerView.adapter = adapterPurchase
    }

    private fun init() {
        preferenceKeys = KsPreferenceKeys.getInstance()
        playlistViewModel = ViewModelProvider(this)[MultiplePlaylistViewModel::class.java]
        binding?.newPlaylist?.setOnClickListener {
            showCustomDialog()
        }
        binding?.toolbar?.logoMain2?.visibility = View.GONE
        if (from == "Home"){
            binding?.toolbar?.backLayout?.visibility = View.GONE
        }
        binding?.toolbar?.searchIcon?.visibility = View.GONE
        binding?.toolbar?.titleMid?.visibility = View.VISIBLE
        binding?.toolbar?.titleMid?.text = getString(R.string.header_my_playlist)
        binding?.toolbar?.backLayout?.setOnClickListener {
            requireActivity().onBackPressed()
        }
        dialogButton?.background?.alpha = 128
        callShimmer(requireContext(), binding.playlistRecyclerView)

        playListApiCallListener = object :PlayListApiCallListener{
            override fun onPlayListApiCallListener() {
                fetchData()
            }
        }
        fetchData()
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityMyPlaylistActviityBinding {
        return ActivityMyPlaylistActviityBinding.inflate(inflater)
    }

    private fun fetchData() {
        featureModel = AppConfigMethod.parseFeatureFlagList()
        if (featureModel!!.featureFlag.IS_PLAYLIST_QUEUED) {
            queued = "QUEUED"
        }
        if (featureModel!!.featureFlag.IS_PLAYLIST_LIKED) {
            liked = "LIKED"
        }

        playlistViewModel?.getPlayListAll(preferenceKeys?.appPrefAccessToken)?.observe(requireActivity()) {
                if (it?.data?.items != null && it.data.items.isNotEmpty()) {
                    binding?.pBar?.visibility = View.GONE
                    binding?.playlistRecyclerView?.visibility = View.VISIBLE
     //               binding?.noPlaylist?.visibility = View.GONE
//                    if (adapter != null) {
//                        adapter?.updateList(it.data.items)
//                    } else {
                        playlistData = it
                    val reversedList = it.data.items.reversed() as? List<MyPlaylistData.Data.Item?> ?: emptyList()
                        adapter = MultiplePlaylistAdapter(reversedList.toMutableList(), object : MultiplePlaylistAdapter.OnItemCLick {
                                override fun threeDotClick(position: String, type: String?, mediaContentID: Int?, adapterItemPosition:Int) {
                                    PlaylistID = position
                                    adapterPosition = adapterItemPosition
                                    val bottomSheet = PlaylistOptionsBottomSheet(position, interfaceClick = this@MyPlaylistFragment, playlistTitle = reversedList?.get(adapterItemPosition)?.title, contentCounter = reversedList?.get(adapterItemPosition)?.contentCount)
                                    bottomSheet.show(requireActivity().supportFragmentManager, "PlaylistOptions")
                                }

                                override fun itemCLick(
                                    playlistSlug: String,
                                    playlistID: String?,
                                    data: PlaylistDetails?,
                                    position: Int,
                                    orderCount: Int?,
                                    title: String?,
                                ) {
                                    if (from == "playlist_selection") {
                                        preferenceKeys?.appPrefAccessToken?.let { it1 ->
                                            playlistViewModel?.addContentPlaylist(
                                                it1,
                                                mediaContentIds = songID,
                                                type = "USER_DEFINED",
                                                playlistID = playlistID,
                                                true
                                            )?.observe(requireActivity(), Observer {
                                                if (it.data != null) {
                                                    showToastWithMessage("Song added to playlist")
                                                }else{
                                                    showToastWithMessage(getString(R.string.something_went_wrong))
                                                }
                                            })
                                            requireActivity().onBackPressed()
                                        }
                                    } else {
                                        if (orderCount != null && orderCount > 0) {
                                            val playlistDetailsPage = PlaylistDetailsPage()
                                            playlistDetailsPage.getPlaylistApiCallback(playListApiCallListener)
                                            val bundle = Bundle()
                                            bundle.putString("playlistSlug", playlistSlug)
                                            bundle.putString("playlistName", title)
                                            bundle.putString("playlistID", playlistID)
                                            bundle.putBoolean("playQueueItems", checkQueueAndPlayList(title))
                                            playlistDetailsPage.arguments = bundle
                                            requireActivity().supportFragmentManager.beginTransaction().add(R.id.content_frame, playlistDetailsPage,AppConstants.TAG_PLAYLIST_DETAIL_FRAGMENT).addToBackStack(null).commit()
                                        }
                                    }
                                }
                            })
                        binding?.playlistRecyclerView?.adapter = adapter
                  //  }
                } else {
                    binding?.pBar?.visibility = View.GONE
                    binding?.playlistRecyclerView?.visibility = View.GONE
                }
            }
    }
    private fun showToastWithMessage(msg:String) {
        context?.let {
            Toast.makeText(it, msg, Toast.LENGTH_SHORT).show()
        }
    }
    private fun checkQueueAndPlayList(title: String?): Boolean {
        return title != "My Queue"
    }

    private fun setClicks() {
        val bundle=Bundle()
        binding.watchHistoryLayout.setOnClickListener {
            if (featureModel?.featureFlag?.IS_MUSIC_APP == true){
                val playlistDetailsPage = PlaylistDetailsPage()
                bundle.putString(AppConstants.FROM_REDIRECTION,AppConstants.WATCH_HISTORY)
                playlistDetailsPage.arguments=bundle
                requireActivity().supportFragmentManager.beginTransaction()
                    .add(R.id.content_frame, playlistDetailsPage).addToBackStack(null).commit()
            }else {
                ActivityLauncher.getInstance()?.watchHistory(
                    requireActivity(),
                    WatchListActivity::class.java, "Watch History", true
                )
            }
        }
        binding.watchlistLayout.setOnClickListener {
            if (featureModel?.featureFlag?.IS_MUSIC_APP == true){
                val playlistDetailsPage = PlaylistDetailsPage()
                bundle.putString(AppConstants.FROM_REDIRECTION,AppConstants.WATCH_LIST)
                playlistDetailsPage.arguments=bundle
                requireActivity().supportFragmentManager.beginTransaction()
                    .add(R.id.content_frame, playlistDetailsPage).addToBackStack(null).commit()
            }else {
                mListener?.onFragmentInteraction()
            }
        }
    }

    private fun updatePlaylistApi(title: String) {
        preferenceKeys?.appPrefAccessToken?.let {
            PlaylistID?.let { it1 ->
                playlistViewModel?.updatePlaylistName(
                    it,
                    it1, title
                )?.observe(requireActivity()) {
                    fetchData()
                }
            }
        }
    }

    private fun createPlaylist(title: String) {
        binding?.pBar?.visibility = View.VISIBLE
        playlistViewModel?.createMusicPlaylist(preferenceKeys?.appPrefAccessToken, title)
            ?.observe(viewLifecycleOwner) {
                if (it != null) {
                    fetchData()
                }
            }
    }

    override fun onDetach() {
        super.onDetach()
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).toolFrame(0)
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).detailFrameVisibility()
    }

    override fun updatePlaylist(playlistTitle:String?) {
        dialog?.show()
        dialogText?.text = getString(R.string.updatePlaylist)
        dialogButton?.text = getString(R.string.update_playlist_button_title_text)
        dialogTitle?.setText(playlistTitle)
        dialogButton?.setOnClickListener {
            when {
                dialogTitle?.text?.isNotEmpty() == true -> {
                    binding?.pBar?.visibility = View.VISIBLE
                    updatePlaylistApi(dialogTitle?.text.toString())
                    dialog?.dismiss()
                }
                (dialogTitle?.text?.length?:0) > 15 -> {
                    errorMsgText?.text = getString(R.string.enter_playlist_name_limit_msg)
                }
                else -> {
                    errorMsgText?.text = getString(R.string.enter_playlist_name_blank_msg)
                }
            }
        }

        checkPlaylistTitleCondition()
    }

    private fun checkPlaylistTitleCondition(){
        dialogTitle?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun onTextChanged(s: CharSequence?, p1: Int, p2: Int, p3: Int) {
                val input = s.toString()
                if (input.isNotEmpty()){
                    errorMsgText?.visibility = View.GONE
                }
                else{
                    errorMsgText?.visibility = View.VISIBLE
                }
            }
            override fun afterTextChanged(s: Editable?) {
                val input = s.toString()
                if (input.isNotEmpty()){
                    errorMsgText?.visibility = View.GONE
                }
                else{
                    errorMsgText?.visibility = View.VISIBLE
                }
            }
        })
    }

    override fun removeSong(playlistID: String) {
//        adapter?.updateList(playlistData?.data?.items?.filter {
//            it?.id != playlistID
//        })
        adapter?.removeAdapterItems(adapterPosition)

    }
}