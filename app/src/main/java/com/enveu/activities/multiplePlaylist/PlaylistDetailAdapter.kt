package com.enveu.activities.multiplePlaylist

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.bean_model_v2_0.MusicPlaylist.PlaylistDetails
import com.enveu.databinding.MultipalPlaylistDetailItemBinding
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.helpers.ImageHelper

class PlaylistDetailAdapter(
    var data: List<PlaylistDetails.Data.OrderedContent?>,
    private var playlistDetails: PlaylistDetails,
    private val itemCLick: MultiplePlaylistAdapter.OnItemCLick
) : RecyclerView.Adapter<PlaylistDetailAdapter.PlaylistViewHolder>() {

    inner class PlaylistViewHolder(val binding : MultipalPlaylistDetailItemBinding) : RecyclerView.ViewHolder(binding.root) {
        var image = binding.image
        var dot = binding.dot
        var song = binding.textSongName
        var desc = binding.textArtistName
        var imageText = binding.imageText
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PlaylistViewHolder {
        return  PlaylistViewHolder(MultipalPlaylistDetailItemBinding.inflate(LayoutInflater.from(parent.context),parent,false))
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: PlaylistViewHolder, position: Int) {

        holder.song.text = data.get(position)?.content?.title ?: ""

        val customData = data[position]?.content?.customData
        val songsArtistIds = customData?.songsArtistIds
        val songDesc = songsArtistIds?.getOrNull(position)?.title

        holder.desc.text = songDesc ?: ""


        holder.itemView.setOnClickListener {
            itemCLick.itemCLick("", "" , playlistDetails, position)
        }

        holder.dot.setOnClickListener {
            itemCLick.threeDotClick(playlistDetails.data?.id.toString(), type =  playlistDetails.data?.type, mediaContentID = playlistDetails.data?.orderedContents!![position]?.content?.id, adapterItemPosition = position)
        }

        val songImg=AppCommonMethod.getSongsPosterImageUrl(data[position]?.content)
        val imageSrc = customData?.songsAlbumsId?.images?.getOrNull(position)?.src
        if (!songImg.isNullOrEmpty()){
            ImageHelper.getInstance(holder.image.context).loadListSQRImage(holder.binding.albumImage,songImg)
        }else if (imageSrc.isNullOrEmpty()){
//            holder.imageText.visibility = View.VISIBLE
//            holder.imageText.text = data[position]?.content?.title ?: ""
            holder.image.visibility = View.VISIBLE; // Ensure the ImageView is visible
          //  holder.image.setImageResource(R.drawable.); // Set placeholder
        }else{
            Glide.with(holder.image.context).load(imageSrc).into(holder.binding.albumImage)
            holder.imageText.visibility = View.GONE
        }
    }


    @SuppressLint("NotifyDataSetChanged")
    fun updateData(data: List<PlaylistDetails.Data.OrderedContent?>){
        this.data = data
        notifyDataSetChanged()
    }
}