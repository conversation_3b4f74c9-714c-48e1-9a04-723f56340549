package com.enveu.activities.multiplePlaylist

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.R
import com.enveu.activities.multiplePlaylist.adapter.EpisodeItemClick
import com.enveu.activities.multiplePlaylist.adapter.RecommendedAdapter
import com.enveu.activities.sponsorArtist.ArtistAndSponserActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.ActivitySelectSubscriptionPlan
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent
import com.enveu.bean_model_v2_0.MusicPlaylist.PlaylistDetails
import com.enveu.callbacks.player.callback.OnAudioItemClickInteraction
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.callbacks.player.callback.SongClick
import com.enveu.client.watchHistory.beans.ResponseWatchHistoryAssetList
import com.enveu.databinding.PlaylistLayoutBinding
import com.enveu.fragments.album.adapter.SongsAdapter
import com.enveu.fragments.album.adapter.SongsItemClick
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.player.ui.AudioInteractionFragment
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.utils.Logger
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.BottomDialogFragment
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.ToastHandler
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

class PlaylistDetailsPage : BaseBindingFragment<PlaylistLayoutBinding>(),CommonDialogFragment.EditDialogListener,
    OnAudioItemClickInteraction,EpisodeItemClick, SongsItemClick,SongClick {

    private var multiplePlaylistViewModel : MultiplePlaylistViewModel?= null
    private var preference : KsPreferenceKeys?= null
    private var bookmarkingViewModel: BookmarkingViewModel? = null
    private var playlistId : String?= null
    private var playlistSlug : String?= null
    private var playlistName : String?= null
    private var playQueueItems:Boolean = false
    private var adapter: PlaylistDetailAdapter? = null
    private var singlesAdapter: RecommendedAdapter? = null
    var songList = mutableListOf<DataItem>()
    var songTypeList= mutableListOf<DataItem>()
    private var onSongItemClick: OnSongItemClick? = null
    var railInjectionHelper: RailInjectionHelper? = null
    private var resEntitle : ResponseEntitle?= null
    private var isUserNotEntitle : Boolean?= null
    private val stringsHelper by lazy { StringsHelper }
    private var audioInteractionFragment: AudioInteractionFragment? = null
    private var thumbnailImageUrl :String?= null
    private var railCommonDataList: MutableList<EnveuVideoItemBean> = ArrayList()
    private var railCommonData: RailCommonData? = null
    private var featureFlag : FeatureFlagModel?= null
    private var token:String=""
    private var counter:Int=0
    private var redirection=""
    private var playListApiCallListener: PlayListApiCallListener?= null
    private var songAdapter:SongsAdapter?=null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        onSongItemClick = context as OnSongItemClick
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]

        callShimmer()
        multiplePlaylistViewModel =
            ViewModelProvider(requireActivity())[MultiplePlaylistViewModel::class.java]
        preference = KsPreferenceKeys.getInstance()
        setCLick()
        playlistId = arguments?.getString("playlistID")
       // playQueueItems = arguments?.getBoolean("playQueueItems")!!
        val redirectFrom=arguments?.getString(AppConstants.FROM_REDIRECTION)
        if (!redirectFrom.isNullOrEmpty()){
            redirection=redirectFrom
        }

        if (!binding.description.text.isNullOrEmpty() && !binding.genre.text.isNullOrEmpty()){
            binding.description.visibility = View.VISIBLE
            binding.genre.visibility = View.VISIBLE
        }else{
            binding.description.visibility = View.GONE
            binding.genre.visibility = View.GONE
        }
        if (!playlistId.isNullOrEmpty()) {
            playlistSlug = arguments?.getString("playlistSlug")
            playlistId = arguments?.getString("playlistID")
        }
        playlistName = arguments?.getString("playlistName")
        if (playlistName.equals("My Queue",ignoreCase = true)) {
            playQueueItems = true
        } else {
            playQueueItems = true
        }
        featureFlag = AppConfigMethod.parseFeatureFlagList()
        if (!playlistSlug.isNullOrEmpty()) {
            getPlayListData()
        } else {
            binding.metaLayout.background = AppCommonMethod.setGradientBackgroundColor(
                Color.parseColor(
                    AppCommonMethod.getDominantColor(railCommonData?.enveuVideoItemBeans?.get(0)?.imageContent)
                ), Color.parseColor("#00000000"), "TOP_TO_BOTTOM"
            )
            initModels()
            if (redirection.equals(AppConstants.WATCH_HISTORY)){
                getWatchHistoryData()
            }else if (redirection.equals(AppConstants.WATCH_LIST)){
                getWatchListData()
            }
        }
    }

    private fun initModels() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        bookmarkingViewModel = ViewModelProvider(this)[BookmarkingViewModel::class.java]
    }
    fun setWatchHistoryData(railCommonData: RailCommonData) {
        this.railCommonData = railCommonData
        Handler(Looper.getMainLooper()).postDelayed(Runnable {
            setWatchListData(railCommonData)
        },200)
    }
    private fun setMetaData() {
        binding?.contentTitle?.text = playlistName
    }
    private fun commonDialog(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = requireActivity().supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun callShimmer() {
        binding.seriesShimmer.visibility = View.VISIBLE
        binding.mShimmer.seriesShimmerScroll1.isEnabled = false
        binding.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding.mShimmer.sfShimmer1.startShimmer()
        binding.mShimmer.sfShimmer2.startShimmer()
        binding.mShimmer.sfShimmer3.startShimmer()
        binding.mShimmer.flBackIconImage.bringToFront()
    }

    private fun stopShimmer() {
        binding.seriesShimmer.visibility = View.GONE
        binding.mShimmer.sfShimmer1.startShimmer()
        binding.mShimmer.sfShimmer2.startShimmer()
        binding.mShimmer.sfShimmer3.startShimmer()
    }

    private fun setCLick(){
        binding?.goBack?.setOnClickListener {
            ArtistAndSponserActivity.AppState.isSkipped = false
            requireActivity().onBackPressed()
        }
        binding?.llPlay?.setOnClickListener {
            setSongTypeList()
            playThroughAllSong(playQueueItems, songTypeList)
//            onPlayClick(playQueueItems)
        }
        binding?.shufflell?.setOnClickListener{
            Shuffle()
        }
        binding!!.noResultFound.toolbar.backLayout.setOnClickListener { activity?.onBackPressed() }

    }
    private fun setSongTypeList() {
        songTypeList.clear()
        for (song in songList){
            song.contentType?.let {
                if (it.equals(AppConstants.SONGS,true)){
                    songTypeList.add(song)
                }
            }
        }
    }

    private fun getPlayListData() {
        multiplePlaylistViewModel?.playlistDetail(preference?.appPrefAccessToken, playlistSlug)?.observe(requireActivity()) { it ->
            if (it?.data?.orderedContents != null) {
                binding?.pBar?.visibility = View.GONE
                it.data.orderedContents.forEach {
                    it?.content?.let { it1 -> songList.add(it1) }
                }
                binding.headerTitleLayout.visibility = View.GONE
                binding.moreLikeThisTitle.visibility = View.GONE

                val imageContent = AppCommonMethod.getImageContent(it.data.orderedContents[0]?.content)
                thumbnailImageUrl = imageContent.src
                stopShimmer()

                binding?.imageView?.let { it1 ->
                    Glide.with(requireContext()).load(thumbnailImageUrl).into(
                        it1
                    )
//                    binding.metaLayout.background = AppCommonMethod.setGradientBackgroundColor(
//                        Color.parseColor(imageContent.dominantColor), Color.parseColor("#00000000"), "TOP_TO_BOTTOM")

                    binding.metaLayout.background = AppCommonMethod.setGradientBackgroundColor(
                        Color.parseColor(AppCommonMethod.getDominantColorForPlaylistDetail(imageContent)),
                        Color.parseColor("#0000006b"),
                        Color.parseColor("#00000000"),
                        "TOP_TO_BOTTOM")

                    binding.contentTitle.text = it.data.orderedContents[0]?.content?.title
                    if (it.data.orderedContents[0]?.content?.description !=null && it.data.orderedContents[0]?.content?.description!!.isNotEmpty()){
                        binding.description.text= it.data.orderedContents.get(0)?.content?.description
                    }else{
                        binding.description.visibility=View.GONE
                    }

                    var artistName = ""
                    it.data.orderedContents?.get(0)?.content?.customData?.songsArtistIds?.forEachIndexed { index, item ->
                        if (index == it.data.orderedContents?.get(0)?.content?.customData?.songsArtistIds?.size?.minus(1)) {
                            artistName += item.title
                        } else {
                            artistName += "${item.title}, "
                        }
                    }
//                    var metadetails=getMetaDetails(it.data.orderedContents?.get(0)?.content!!,artistName)

                    binding.genre.text = artistName
                    if (adapter != null) {
                        it.data.orderedContents.let { it1 -> adapter?.updateData(it1) }
                    } else {
                        adapter =
                            it.data.orderedContents.let { it1 ->
                                PlaylistDetailAdapter(
                                    it1,
                                    it,
                                    object : MultiplePlaylistAdapter.OnItemCLick {
                                        override fun threeDotClick(position: String, type: String?, mediaContentID: Int?, adapterItemPosition: Int) {
                                            val bottomSheetDialog = BottomDialogFragment.getInstance(it1.get(adapterItemPosition)?.content, AppConstants.HIDE_TO_QUEUE)
                                            thumbnailImageUrl?.let {
                                                bottomSheetDialog.setDefaultImgOfTopPoster(it)
                                            }
                                           position.let{
                                               bottomSheetDialog.setPlaylistID(it)
                                            }
                                            bottomSheetDialog.RemoveSongListener(
                                                object : RemoveSongListener {
                                                    override fun removeSong(id: Int) {
                                                        binding?.pBar?.visibility = View.VISIBLE
                                                        getPlayListData()
                                                    }

                                                }
                                            )
                                            bottomSheetDialog.show(activity?.supportFragmentManager!!, "Bottom Sheet Dialog Fragment")

                        //                                            val bottomSheet = PlaylistOptionsBottomSheet(position, object : PlaylistOptionsBottomSheet.RemoveSongFromPlaylist {
                        //                                                    override fun updatePlaylist(playlistTitle:String?) {}
                        //
                        //                                                    override fun removeSong(playlistID: String) {
                        //                                                        binding?.pBar?.visibility = View.VISIBLE
                        //                                                        getPlayListData()
                        //                                                    }
                        //
                        //                                                }, from = "DetailPage", type = type!!, mediaContentIds = mediaContentID!! , playlistTitle = it1[apdapterItemAosition]?.content?.title, contentCounter = 0)
                        //                                            bottomSheet.show(requireActivity().supportFragmentManager, "PlaylistOptions")
                                        }

                                        override fun itemCLick(
                                            playlistSlug: String,
                                            playlistID: String?,
                                            data: PlaylistDetails?,
                                            position: Int,
                                            orderCount: Int?,
                                            title: String?
                                        ) {
                                            val content =
                                                data?.data?.orderedContents?.get(position)?.content
                                            if (content != null) {
                                                try {
                                                    if (featureFlag?.featureFlag?.IS_SPONSOR==true) {
                                                        if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()) {
                                                            if (!ArtistAndSponserActivity.AppState.isSkipped) {
                                                                ActivityLauncher.getInstance().artistAndSponserActivity(requireActivity(), ArtistAndSponserActivity::class.java)
                                                            } else {
                                                                preference?.setShuffleEnable(false)
                                                                initiatePlaybackAndCheckPos(false,position)
                                                                ArtistAndSponserActivity.AppState.isSkipped = false
                                                            }
                                                        } else {
                                                            preference?.setShuffleEnable(false)
                                                            ArtistAndSponserActivity.AppState.isSkipped = false
                                                            initiatePlaybackAndCheckPos(false,position)
                                                        }
                                                    }else{
                                                        preference?.setShuffleEnable(false)
                                                        initiatePlaybackAndCheckPos(false,position)
                                                    }

                                                } catch (e: Exception) {
                                                    Logger.e("error", e.toString())
                                                }

//                                                try {
//                                                    if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()  && !ArtistAndSponserActivity.AppState.isSkipped) {
//                                                        ActivityLauncher.getInstance().artistAndSponserActivity(
//                                                            requireActivity(),
//                                                            ArtistAndSponserActivity::class.java
//                                                        )
//                                                    }else{
//                                                        if (content.isPremium) {
//                                                            checkEntitlement(token, content, AppCommonMethod.getImageContent(content), false)
//                                                        } else {
//                                                            preference?.setShuffleEnable(false)
//                                                            onSongItemClick?.songItemClick(
//                                                                songList,
//                                                                content,
//                                                                content.externalRefId,
//                                                                if (content.customData?.songsAlbumsId?.images != null) AppCommonMethod.getImageContent(content)
//                                                                else null,
//                                                                AppCommonMethod.getSongsPosterImageUrl(content), playQueueItems)
//                                                        }
//                                                    }
//                                                    ArtistAndSponserActivity.AppState.isSkipped = false
//
//
//                                                } catch (e: Exception) {
//                                                    Logger.e("error", e.toString())
//                                                }

                                            }
                                        }
                                    })
                            }
                        binding.myRecycleView.adapter = adapter
                    }
                }
            } else {
                binding?.pBar?.visibility = View.GONE
                setMetaData()
                getRecommendedSongs()
            }

        }

    }
    private fun initiatePlaybackAndCheckPos(playQueueItems: Boolean?,songPosition:Int){
        var pos=songPosition
        if (songPosition>=songList.size){ pos=songList.size-1 }
        if (songList.size>0) {
            initiatePlayback(playQueueItems, pos)
        }
    }
    private fun initiatePlayback(playQueueItems: Boolean?,songPosition:Int) {
        if (songList[songPosition].contentType.equals(AppConstants.CUSTOM)) {
            AppCommonMethod.setAlbumFragment(requireContext(), songList[songPosition].contentSlug,AppConstants.ALBUM,songList[songPosition].id.toString())
        }else {
            if (songList[songPosition].isPremium) {
                checkEntitlement(
                    preference?.appPrefAccessToken,
                    songTypeList,
                    songList[songPosition],
                    AppCommonMethod.getImageContent(songList[songPosition]),
                    playQueueItems
                )
            } else {
                if (onSongItemClick != null) {
                    onSongItemClick!!.songItemClick(
                        songList,
                        songList[songPosition],
                        songList[songPosition].externalRefId,
                        AppCommonMethod.getImageContent(songList[songPosition]),
                        AppCommonMethod.getSongsPosterImageUrl(songList[songPosition]),
                        playQueueItems
                    )
                }
            }
        }
    }
    private fun playThroughAllSong(playQueueItems: Boolean?,songTypeList:List<DataItem>){
        if (songTypeList[0].isPremium) {
            // onSongItemClick!!.songItemClick(songList,"Y4SfmXMB",songList[0].id)
            checkEntitlement(preference?.appPrefAccessToken,songTypeList, songTypeList[0], AppCommonMethod.getImageContent(songTypeList[0]), playQueueItems)
        } else {
            if (onSongItemClick != null) {
                onSongItemClick!!.songItemClick(
                    songTypeList,
                    songTypeList[0],
                    songTypeList[0].externalRefId,
                    AppCommonMethod.getImageContent(songTypeList[0]),
                    AppCommonMethod.getSongsPosterImageUrl(songTypeList[0]),
                    playQueueItems
                )
            }
        }
    }

    private fun getWatchHistoryData() {
        try {
            token = preference!!.appPrefAccessToken
            bookmarkingViewModel!!.getWatchHistory(token, counter, AppConstants.PAGE_SIZE).observe(requireActivity()) { responseWatchHistoryAssetList: ResponseWatchHistoryAssetList? ->
                if (responseWatchHistoryAssetList!!.isStatus) {
                    if (responseWatchHistoryAssetList.data != null) {
                        var videoIds = ""
                        if (responseWatchHistoryAssetList.data != null) {
                            val watchHistoryList = responseWatchHistoryAssetList.data.items
                            for (historyItem: com.enveu.client.watchHistory.beans.ItemsItem in watchHistoryList) {
                                videoIds = videoIds + historyItem.assetId.toString() + ","
                            }
                            railInjectionHelper!!.getWatchHistoryAssets(watchHistoryList, videoIds,featureFlag?.featureFlag?.IS_MUSIC_APP).observe(
                                requireActivity()
                            ) { railCommonData ->
                                if (railCommonData != null) {
                                    binding!!.pBar.visibility = View.GONE
                                    binding!!.clNoData.visibility = View.GONE
                                    binding!!.noResultLayout.visibility+View.GONE
                                    setWatchListData(railCommonData)
                                    val gson = Gson()
                                    val railCommon = gson.toJson(railCommonData)
                                } else {
                                    hideRecyclerViewData()
                                }
                            }
                        } else {
                            hideRecyclerViewData()
                        }
                    }
                } else {
                    hideRecyclerViewData()
                }
            }
        } catch (e: Exception) {
            hideRecyclerViewData()
            stopShimmer()
            ToastHandler.getInstance().show(requireActivity(),"WatchHistory Error")
        }
    }
    private fun getWatchListData() {
        try {
            token = KsPreferenceKeys.getInstance()!!.appPrefAccessToken
            bookmarkingViewModel!!.getMywatchListData(token, counter, AppConstants.PAGE_SIZE).observe(requireActivity()) { responseWatchHistoryAssetList: ResponseWatchHistoryAssetList ->
                if (responseWatchHistoryAssetList.isStatus) {
                    if (responseWatchHistoryAssetList.data != null) {
                        var videoIds = ""
                        if (responseWatchHistoryAssetList.data != null) {
                            val watchHistoryList = responseWatchHistoryAssetList.data.items
                            for (historyItem in watchHistoryList) {
                                videoIds = videoIds + historyItem.assetId.toString() + ","
                            }
                            railInjectionHelper!!.getWatchHistoryAssets(watchHistoryList, videoIds,featureFlag?.featureFlag?.IS_MUSIC_APP).observe(requireActivity()) { railCommonData: RailCommonData? ->
                                if (railCommonData != null) {
                                    binding!!.pBar.visibility = View.GONE
                                    binding!!.clNoData.visibility = View.GONE
                                    binding!!.noResultLayout.visibility+View.GONE
                                    setWatchListData(railCommonData)
                                    val gson = Gson()
                                    val railCommon = gson.toJson(railCommonData)
                                } else {
                                    hideRecyclerViewData()
                                }
                            }
                        } else {
                            hideRecyclerViewData()
                        }
                    } else {
                        hideRecyclerViewData()
                    }
                } else {
                    hideRecyclerViewData()
                }
            }
        } catch (e: Exception) {
            Logger.w(e)
            hideRecyclerViewData()
            stopShimmer()
            ToastHandler.getInstance().show(requireActivity(),"WatchList Error")
        }
    }

    private var enveuVideoItemBeans: List<EnveuVideoItemBean>? = null
    private fun setWatchListData(railCommonData: RailCommonData) {
        this.railCommonData = railCommonData
        enveuVideoItemBeans = railCommonData.enveuVideoItemBeans
        val gson = Gson()
        val json = gson.toJson(enveuVideoItemBeans)
        val type = object : TypeToken<MutableList<DataItem>>() {}.type
        songList = gson.fromJson(json, type)
        railCommonData.enveuVideoItemBeans[0]?.let {
            binding.contentTitle.text = it?.title
            if (!it?.description.isNullOrEmpty()){
                binding.description.text=it?.description
            }else{
                binding.description.visibility=View.GONE
            }
            var artistName = ""
            it?.albumArtistId?.forEachIndexed { index, item ->
                if (index == it?.albumArtistId?.size?.minus(1)) {
                    artistName += item.title
                } else {
                    artistName += "${item.title}, "
                }
            }

            var metadetails=getMetaDetails(it!!,artistName)

            if (metadetails?.isNotEmpty() == true && metadetails?.isNotBlank() == true){
                binding.genre.text = metadetails
            }else{
                binding.genre.visibility=View.GONE
            }
            if (it.posterURL.isNotEmpty()) {
                thumbnailImageUrl=it.posterURL
                ImageHelper.getInstance(requireActivity())
                    .loadListImage(binding.imageView, it.posterURL!!)
            }else{
                binding!!.imageView.setImageResource(R.drawable.music_note)
            }
        }
        stopShimmer()
        binding!!.myRecycleView.visibility = View.VISIBLE
        songAdapter=SongsAdapter(railCommonData.enveuVideoItemBeans,this,AppConstants.PLAYLISTDETAIL)
        binding!!.myRecycleView.adapter=songAdapter
    }
    private fun getMetaDetails(videoDetails: EnveuVideoItemBean,artistName:String): CharSequence? {
        var metaDetail=StringBuilder()
        metaDetail.appendLine(artistName)
        var geners=""
        videoDetails.genres?.forEachIndexed { index, genre ->
            if (index == videoDetails?.customData?.genres?.size?.minus(1)) {
                geners += genre.title
            }else {
                geners += "${genre.title}, "
            }
        }
        videoDetails.sub_genres?.forEachIndexed{ index,sub_gener->
            if (index == 0) {
                geners += "${sub_gener.title}"
            } else {
                geners += "${sub_gener.title}, "
            }
        }
        if (geners.isNotEmpty()){
            metaDetail.appendLine(geners)
        }
        if (!videoDetails.year.isNullOrEmpty() && !videoDetails.year.equals("0")){
            metaDetail.appendLine(videoDetails.year)
        }
        return metaDetail
    }
    private fun hideRecyclerViewData() {
        if (redirection.equals(AppConstants.WATCH_HISTORY)) {
            binding!!.noResultFound.noDataTittle.text=getString(R.string.nothing_your_watchHistory_tittle)
            binding!!.noResultFound.noDataDescription.text=getString(R.string.nothing_your_watchHistory_description)
        }
        binding!!.noResultLayout.visibility = View.VISIBLE
        binding!!.noResultFound.toolbar.backLayout.visibility = View.VISIBLE
        binding!!.noResultFound.toolbar.logoMain2.visibility = View.GONE
        binding!!.noResultFound.toolbar.searchIcon.visibility = View.GONE
       binding!!.metaLayout.visibility=View.GONE
       binding!!.myRecycleView.visibility=View.GONE
        stopShimmer()
    }

    private fun getRecommendedSongs() {
        val activity = requireActivity()
        if (railInjectionHelper == null) {
            railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        }
        railInjectionHelper?.getRecommendedSongs(0,50,"AUDIO","PUBLISH_DATE")
            ?.observe(activity) { assetResponse: ResponseModel<*>? ->
                if (assetResponse != null) {
                    if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {

                    } else if (assetResponse.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
                        stopShimmer()
                        railCommonData = enveuCommonResponse
                        railCommonDataList = enveuCommonResponse.enveuVideoItemBeans
                        setRecommendedAdapter()
                    } else if (assetResponse.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                        stopShimmer()
                    } else {
                        stopShimmer()
                    }
                }
            }
    }

    private fun setRecommendedAdapter() {
        if (railCommonDataList.size>0) {
            binding.headerTitleLayout.visibility = View.VISIBLE
            binding.moreLikeThisTitle.setText(R.string.suggestions)
        } else {
            binding.headerTitleLayout.visibility = View.GONE
            binding.moreLikeThisTitle.visibility = View.GONE
        }
        singlesAdapter = featureFlag?.let { RecommendedAdapter(requireActivity(),railCommonDataList,this, featureFlag = it) }
        binding.myRecycleView.adapter = singlesAdapter
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): PlaylistLayoutBinding {
        return  PlaylistLayoutBinding.inflate(inflater)
    }

    private fun playClick(playQueueItems:Boolean?) {
        KsPreferenceKeys.getInstance().setShuffleEnable(false)
        KsPreferenceKeys.getInstance().setRepeatEnable(false)
        if (!songList.isNullOrEmpty()) {
            if (featureFlag?.featureFlag?.IS_SPONSOR==true) {
                if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()) {
                    if (!ArtistAndSponserActivity.AppState.isSkipped) {
                        ActivityLauncher.getInstance().artistAndSponserActivity(requireActivity(), ArtistAndSponserActivity::class.java)
                    } else {
                        initiatePlayback(playQueueItems,0)
                        ArtistAndSponserActivity.AppState.isSkipped = false
                    }
                } else {
                    ArtistAndSponserActivity.AppState.isSkipped = false
                    initiatePlayback(playQueueItems,0)
                }
            }else{
                initiatePlayback(playQueueItems,0)
            }
        }
    }

    override fun onPlayClick(playQueueItems: Boolean?) {
        playClick(playQueueItems)
    }

    override fun onShuffle() {
        Shuffle()
    }

    private fun checkEntitlement(token: String?,songTypeList: List<DataItem>, song: DataItem, imageContent: ImageContent?, playQueueItems: Boolean?) {
        binding?.pBar?.visibility = View.VISIBLE
        railInjectionHelper?.hitApiEntitlement(token, song.sku)?.observe(viewLifecycleOwner) { responseEntitle ->
                binding!!.pBar.visibility = View.GONE
                if (responseEntitle != null && responseEntitle.data != null) {
                    resEntitle = responseEntitle
//                    preference?.planMaxAllowedConcurrency = responseEntitle.data.maxAllowedConcurrency
                    if (responseEntitle.data.entitled) {
                        railInjectionHelper?.externalRefID(responseEntitle.data?.accessToken, responseEntitle.data?.sku)?.observe(viewLifecycleOwner) { drm ->
                            if (onSongItemClick != null) {
                                onSongItemClick!!.songItemClick(songTypeList, song, drm.data?.externalRefId!!, imageContent,AppCommonMethod.getSongsPosterImageUrl(song), playQueueItems)
                            }
                        }
                    } else {
                        isUserNotEntitle = true
                        binding!!.pBar.visibility = View.GONE
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                                getString(R.string.popup_notEntitled)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_select_plan.toString(),
                                getString(R.string.popup_select_plan)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_purchase.toString(),
                                getString(R.string.popup_purchase)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                                getString(R.string.popup_cancel)
                            )
                        )
                    }
                } else {
                    binding!!.pBar.visibility = View.GONE
                    if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                        //  clearCredientials(preference)
                        ActivityLauncher.getInstance().loginActivity(
                            requireActivity(), ActivityLogin::class.java, ""
                        )
                    } else {
                        binding!!.pBar.visibility = View.GONE
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            ), ""
                        )
                    }
                }
            }

    }


     private fun Shuffle() {
        KsPreferenceKeys.getInstance().setShuffleEnable(true)
        KsPreferenceKeys.getInstance().setRepeatEnable(false)
        if (!songList.isNullOrEmpty()) {
            if (songList[0].isPremium) {
                // onSongItemClick!!.songItemClick(songList,"Y4SfmXMB",songList[0].id)
                checkEntitlement(preference?.appPrefAccessToken,songTypeList, songList[0], AppCommonMethod.getImageContent(songList[0]), false)
            } else {
                if (onSongItemClick != null) {
                    onSongItemClick!!.songItemClick(
                        songList,
                        songList[0],
                        songList[0].externalRefId,
                        AppCommonMethod.getImageContent(songList[0]),
                        AppCommonMethod.getSongsPosterImageUrl(songList[0]),
                        false
                    )
                }
            }
        }
    }

    override fun onActionBtnClicked() {
        ActivityLauncher.getInstance().goToPlanScreen(requireActivity(), ActivitySelectSubscriptionPlan::class.java, "")
    }

    override fun onCancelBtnClicked() {
    }

    override fun onItemClick(enveuVideoItemBean: EnveuVideoItemBean?, isPremium: Boolean, position: Int) {
        AppCommonMethod.redirectionLogic(
            requireActivity(),
            railCommonData!!,
            position,"","",""
        )
    }

    override fun onPlusIconClick(enveuVideoItemBean: EnveuVideoItemBean?, assetId: Int, position: Int) {
        hitApiAddToPlayList(assetId)
    }

    private fun hitApiAddToPlayList(assetId: Int) {
        multiplePlaylistViewModel!!.addContentPlaylist(
            KsPreferenceKeys.getInstance().appPrefAccessToken, assetId, "", playlistId, false).observe(requireActivity()
        ) { addSongPlaylist ->
            if (addSongPlaylist != null) {
                if (addSongPlaylist.responseCode != null) {
                    if (addSongPlaylist.responseCode == 2000) {
                        Toast.makeText(requireContext(), getString(R.string.song_added_on_playlist), Toast.LENGTH_LONG).show()
                        playListApiCallListener?.onPlayListApiCallListener()
                        if (!playlistId.isNullOrEmpty()){
                            getPlayListData()
                            playListApiCallListener?.onPlayListApiCallListener()
                        }
                    } else if (addSongPlaylist.responseCode == 409) {
                        if (!playlistId.isNullOrEmpty()){
                            getPlayListData()
                            playListApiCallListener?.onPlayListApiCallListener()
                        }
                    } else if (addSongPlaylist.responseCode == 404) {
                        Toast.makeText(
                            requireActivity(),
                            getString(R.string.something_went_wrong),
                            Toast.LENGTH_LONG
                        ).show()
                    }
                }
            } else {
                Toast.makeText(
                    requireActivity(),
                    getString(R.string.something_went_wrong),
                    Toast.LENGTH_LONG
                ).show()
            }
        }

    }

    override fun onDetach() {
        super.onDetach()
        onSongItemClick=null
    }


    private fun convertModle(itemDetail: EnveuVideoItemBean): DataItem {
        val gson=Gson()
        val json=gson.toJson(itemDetail)
        val songDetail:DataItem=gson.fromJson(json,DataItem::class.java)
        return songDetail
    }
    override fun onMoreClick(itemDetail: EnveuVideoItemBean) {
        super.onMoreClick(itemDetail)
        Log.d(AppConstants.PLAYLISTDETAIL,"more clicked id: "+itemDetail.id)
        val song=convertModle(itemDetail)
        val bottomSheetDialog = BottomDialogFragment.getInstance(song, AppConstants.HIDE_TO_QUEUE)
        thumbnailImageUrl?.let {
            bottomSheetDialog.setDefaultImgOfTopPoster(it)
        }
        bottomSheetDialog.show(activity?.supportFragmentManager!!, "Bottom Sheet Dialog Fragment")
    }
    override fun onSongClick(song: DataItem) {
        preference?.setShuffleEnable(false)
        KsPreferenceKeys.getInstance().setRepeatEnable(false)
        if (featureFlag?.featureFlag?.IS_SPONSOR==true) {
            if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()) {
                if (!ArtistAndSponserActivity.AppState.isSkipped) {
                    ActivityLauncher.getInstance().artistAndSponserActivity(requireActivity(), ArtistAndSponserActivity::class.java)
                } else {
                    initiateSingleItemPlayback(song)
                    ArtistAndSponserActivity.AppState.isSkipped = false
                }
            } else {
                ArtistAndSponserActivity.AppState.isSkipped = false
                initiateSingleItemPlayback(song)
            }
        }else{
            initiateSingleItemPlayback(song)
        }
    }

    private fun initiateSingleItemPlayback(song: DataItem) {
        if (song.isPremium) {
            // onSongItemClick!!.songItemClick(songList,"Y4SfmXMB",songList[0].id)
            checkEntitlement(token,songList, song, AppCommonMethod.getImageContent(song), false)
        } else {
            if (onSongItemClick != null) {
                onSongItemClick!!.songItemClick(
                    songList,
                    song,
                    song.externalRefId,
                    AppCommonMethod.getImageContent(song),
                    AppCommonMethod.getSongsPosterImageUrl(song),
                    false
                )
            }
        }
    }

    override fun onThreeDotClick(song: DataItem) {
    }

    override fun onDeleteItemClick(song: DataItem) {

    }

    override fun onSongsItemClick(assetId: EnveuVideoItemBean?, isPremium: Boolean, position: Int) {
        AppCommonMethod.redirectionLogic(
            requireActivity(),
            railCommonData!!,
            position,"","",""
        )
    }

    fun getPlaylistApiCallback(playListApiCallListener: PlayListApiCallListener) {
        this.playListApiCallListener = playListApiCallListener
    }

}

    interface RemoveSongListener {
        fun removeSong(id: Int)
    }
