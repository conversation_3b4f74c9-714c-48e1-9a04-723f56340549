package com.enveu.activities.multiplePlaylist

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.R
import com.enveu.beanModel.emptyResponse.ResponseEmpty
import com.enveu.databinding.PlaylistMoreOptionsBinding
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import java.util.Objects

class PlaylistOptionsBottomSheet(private val playlistID: String,private val interfaceClick : RemoveSongFromPlaylist?= null ,private val from: String?= null, private val type : String?= null, private val mediaContentIds : Int?= null, private val playlistTitle:String?= null, private val contentCounter:Int? = null) : BottomSheetDialogFragment(){

    private var binding : PlaylistMoreOptionsBinding?= null
    private var playlistViewModel : MultiplePlaylistViewModel?= null
    private var preferenceKeys : KsPreferenceKeys?= null
    private var myPlaylistActivity : MyPlaylistFragment?= null
    private var bookmarkingViewModel: BookmarkingViewModel? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
       return PlaylistMoreOptionsBinding.inflate(inflater,container, false).run {
           binding= this
           root
       }
    }


    @SuppressLint("SetTextI18n")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        preferenceKeys = KsPreferenceKeys.getInstance()
        playlistViewModel = ViewModelProvider(this)[MultiplePlaylistViewModel::class.java]
        bookmarkingViewModel = ViewModelProvider(requireActivity())[BookmarkingViewModel::class.java]
        myPlaylistActivity = MyPlaylistFragment()

        if (!playlistTitle.isNullOrEmpty()){
            binding?.songsTitleText?.text = playlistTitle
        }
        binding?.trackCountText?.text = "$contentCounter Tracks"

        if (from.equals("DetailPage")){
            binding?.rename?.visibility = View.GONE
            binding?.delete?.text = getString(R.string.delete_song_from_your_playlist)
        }

        binding?.rename?.setOnClickListener {
            interfaceClick?.updatePlaylist(playlistTitle)
            dismiss()
        }

        binding?.delete?.setOnClickListener {
            if (from.equals("DetailPage")){
                deleteSongPlaylist()
            }else{
                deletePlaylist(playlistID)
            }
        }
    }

    private fun deleteSongPlaylist(){
        hitApiRemoveLike()
    }

    private fun hitApiRemoveLike() {
        bookmarkingViewModel!!.hitApiDeleteLike(KsPreferenceKeys.getInstance().appPrefAccessToken, mediaContentIds!!).observe(
            this
        ) { value ->
            if (Objects.requireNonNull<ResponseEmpty>(value).isStatus) {
                deleteSongFromPlayList()
            } else {
                //  customProgressBar.setVisibility(View.GONE)
                if (value?.responseCode == 4302) {
                    //    isLoggedOut = true
                    // showDialog(getString(R.string.logged_out), resources.getString(R.string.you_are_logged_out))
                } else if (value?.responseCode == 500) {
                    //showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                }
            }
        }
    }

    private fun deleteSongFromPlayList() {
        preferenceKeys?.appPrefAccessToken?.let {
            playlistViewModel?.removeContentPlaylist(it, mediaContentIds!!, type!!, playlistID,false)?.observe(requireActivity()){
                interfaceClick?.removeSong(playlistID)
            }
        }
        dismiss()
    }

    private fun deletePlaylist(playlistID : String) {
        playlistViewModel?.deleteMusicPlaylist(preferenceKeys?.appPrefAccessToken, playlistID)
            ?.observe(requireActivity()) {
                interfaceClick?.removeSong(playlistID)
            }
        dismiss()
    }

    interface RemoveSongFromPlaylist{
        fun updatePlaylist(updatePlaylist:String?)
        fun removeSong(playlistID: String)
    }
}