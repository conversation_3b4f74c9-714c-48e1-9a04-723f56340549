package com.enveu.activities.multiplePlaylist.adapter

import android.annotation.SuppressLint
import android.app.Activity
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.databinding.RecommendedSongsLayoutItemBinding
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys

class RecommendedAdapter() : RecyclerView.Adapter<SongListViewHolder>() {
    private var singleList: MutableList<EnveuVideoItemBean>? = null
    private var listner: EpisodeItemClick? = null
    private var featureFlag : FeatureFlagModel?= null
    private var preference : KsPreferenceKeys?= null
    private var context : Activity?= null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SongListViewHolder {
        return SongListViewHolder(RecommendedSongsLayoutItemBinding.inflate(LayoutInflater.from(parent.context),parent,false))
    }

    constructor(context: Activity,singleList: MutableList<EnveuVideoItemBean>, listner: EpisodeItemClick,featureFlag : FeatureFlagModel) : this() {
        this.context = context
        this.singleList = singleList
        this.listner = listner
        this.featureFlag = featureFlag
    }

    override fun getItemCount(): Int {
        return singleList!!.size
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addItems(newItems: MutableList<EnveuVideoItemBean>?) {
        val startPos = singleList?.size
        singleList?.addAll(newItems!!)
        notifyItemRangeInserted(startPos!!, newItems?.size!!)
        notifyDataSetChanged()
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: SongListViewHolder, position: Int) {
        holder.binding.textSongName.text = singleList!![position].title
     //   holder.binding.textRowNumber.text = singleList!![position].trackNumber
        holder.binding.textArtistName.text = singleList!![position].title


        holder.binding.linearLayoutView.setOnClickListener {
            listner?.onItemClick(
                singleList!![position],
                singleList!![position].isPremium,
                position
            )
        }

        holder.binding.plusIcon.setOnClickListener{
            listner?.onPlusIconClick( singleList!![position],
                singleList!![position].id,
                position)
        }

        if (singleList != null && singleList!![position].songsArtistIdsItems != null) {
            val songsArtistIdsItems = singleList!![position].songsArtistIdsItems
            if (songsArtistIdsItems.isNotEmpty()) {
                val images = songsArtistIdsItems[0]?.images
                if (!images.isNullOrEmpty()) {
                    val posterUrl: String = images[0].src
                    ImageHelper.getInstance(holder.binding.albumImage.context)
                        .loadListSQRImageForAlbumList(holder.binding.albumImage, posterUrl)
                }
            }
        }

    }
}


interface EpisodeItemClick {
    fun onItemClick(assetId: EnveuVideoItemBean?, isPremium: Boolean, position: Int)
    fun onPlusIconClick(assetId: EnveuVideoItemBean?, id: Int, position: Int)
}

class SongListViewHolder(val binding: RecommendedSongsLayoutItemBinding) :
    RecyclerView.ViewHolder(binding.root) {

}
