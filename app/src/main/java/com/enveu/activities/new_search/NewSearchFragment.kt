package com.enveu.activities.new_search

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.appcompat.app.AppCompatActivity.RESULT_OK
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.follow_follower.FollowFollowingProfileActivity
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.listing.listui.ListActivity
import com.enveu.activities.listing.ui.GridActivity
import com.enveu.activities.new_search.adapter.PlaylistParentAdapter
import com.enveu.activities.new_search.adapter.SearchHistoryAdapter
import com.enveu.activities.new_search.adapter.SearchTabAdapter
import com.enveu.activities.new_search.call_back.SearchHistoryClickListener
import com.enveu.activities.new_search.call_back.SearchItemListener
import com.enveu.activities.new_search.call_back.SearchTabListener
import com.enveu.activities.new_search.model.SearchTabModel
import com.enveu.baseModels.BaseFragment
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.PlaylistApiSuccessListener
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory
import com.enveu.client.enums.ListingLayoutType
import com.enveu.databinding.FragmentNewSearchBinding
import com.enveu.listener.PlayerPlayPauseCallBack
import com.enveu.networking.response.ReelCreatorId
import com.enveu.networking.response.ReelsContentItem
import com.enveu.networking.response.searchModel.PlaylistModelList
import com.enveu.ugc.UgcFragment
import com.enveu.utils.Constants
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.config.LanguageLayer
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show
import com.enveu.view_model.SearchViewModel
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import androidx.core.text.layoutDirection


class NewSearchFragment : BaseFragment() {
    private lateinit var binding:FragmentNewSearchBinding
    private lateinit var viewModel: SearchViewModel
    private var railsCounter: Int = 0
    private var playlistParentAdapter: PlaylistParentAdapter? = null
    private var playlistDataList:ArrayList<PlaylistModelList>? = ArrayList()
    private var preference:KsPreferenceKeys? = null
    private var searchHistoryAdapter: SearchHistoryAdapter? = null
    private var searchHistoryList:ArrayList<String?>? = ArrayList()
    private var keyword: String? = null
    private val fragmentList: MutableMap<Int?, Fragment>? = mutableMapOf()
    private var searchResultFragment:SearchResultFragment? = null
    private var activeFragment:Fragment? = null
    private var searchTabModel:SearchTabModel? = null
    private var playerPlayPauseCallBack: PlayerPlayPauseCallBack? = null
    private var viaSearchUgc:Boolean? = false
    private var tabSelectedPosition:Int = 0


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_new_search,  container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (activity as HomeActivity).hideSubMenu()
        viewModel = ViewModelProvider(this)[SearchViewModel::class.java]
        viaSearchUgc = arguments?.getBoolean(Constants.VIA_SEARCH_UGC)
        initUI()
       // playerPlayPauseCallBack?.onPlayPauseListener(false)
    }

    private fun initUI() {
        preference = KsPreferenceKeys.getInstance()
        searchHistoryList = preference?.searchHistoryList
        if ((searchHistoryList?.size?:0) > 1) {
            searchHistoryList?.reverse()
        }
        val searchEditText = view?.findViewById<EditText>(R.id.searchEditText)
        val isRtl = Locale.getDefault().layoutDirection == View.LAYOUT_DIRECTION_RTL
        if (isRtl) {
            searchEditText?.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_search_icon, 0, 0, 0)
        } else {
            searchEditText?.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_search_icon, 0)
        }

        searchHistoryUI()
        rotateImageLocaleWise(binding.backBtn)
        binding.backBtn.setOnClickListener {
            requireActivity().supportFragmentManager.setFragmentResult("SEARCH_BACK_RESULT", Bundle())
            requireActivity().supportFragmentManager.popBackStack()
            playerPlayPauseCallBack?.onPlayPauseListener(true)
            playerPlayPauseCallBack?.onPlayPauseAtPosition(true)
        }
        searchTabModel =  if (viaSearchUgc == true){
            tabSelectedPosition = 2
            SearchTabModel(2, getString(R.string.shorts_tab_text) , CONTENT_TYPE to VIDEO, VIDEO_TYPE to REEL)
        }
        else{
            tabSelectedPosition = 0
            SearchTabModel(0, getString(R.string.movies_tab_text) , CONTENT_TYPE to VIDEO, VIDEO_TYPE to MOVIES, keyWord = keyword)
        }

        viewModel.callScreenIdApi(preference?.searchScreenIdentifier)
        binding.progressBar.show()
        setObserver(viewModel)
        setSearchHistoryAdapter()
        setResultTab()

        binding.searchEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(text: Editable?) {
                keyword = text.toString().trim()
            }
        })

        binding.searchEditText.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                actionId == EditorInfo.IME_ACTION_GO ||
                actionId == EditorInfo.IME_ACTION_SEND ||
                actionId == EditorInfo.IME_ACTION_DONE ||
                actionId == EditorInfo.IME_ACTION_NEXT) {
                if ((keyword?.length?:0) >= 2) {
                    hitSearchResult()
                }
            }
            true
        }
    }

    private fun hitSearchResult() {
        clearPreviousFragment()
        openSearchResultFragment(searchTabModel)
    }

    private fun setRecyclerViewHeightForItems(maxVisibleItems: Int = 5) {
        val adapterItemCount = binding.searchHistoryRecyclerView.adapter?.itemCount?:0
        binding.searchHistoryRecyclerView.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                val firstItem = binding.searchHistoryRecyclerView.getChildAt(0)
                if (firstItem != null) {
                    val itemHeight = firstItem.height
                    if (itemHeight > 0) {
                        val visibleItemCount = if (adapterItemCount >= maxVisibleItems) maxVisibleItems else adapterItemCount
                        val params = binding.searchHistoryRecyclerView.layoutParams
                        params.height = itemHeight * visibleItemCount
                        binding.searchHistoryRecyclerView.layoutParams = params
                        binding.searchHistoryRecyclerView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    }
                }
            }
        })
    }

    private val searchHistoryClickListener = object : SearchHistoryClickListener {
        override fun searchHistoryItemClickListener(keyword:String?) {
            <EMAIL> = keyword
            binding.searchEditText.setText(keyword)
            hitSearchResult()
        }
    }


    private fun searchHistoryUI() {
        if(searchHistoryList?.isNotEmpty() == true){
            binding.searchHistoryRecyclerView.show()
            binding.headingTitle.show()
        }
        else{
            binding.searchHistoryRecyclerView.hide()
            binding.headingTitle.hide()
        }
    }

    private fun setObserver(viewModel: SearchViewModel) {
         viewModel.screenIdApiResponse.observe(viewLifecycleOwner) { screenIdApiResponse ->
            if (screenIdApiResponse.data?.widgets?.isNotEmpty() == true){
                getPlaylistDetailsById()
            }
             else{
                binding.progressBar.hide()
                binding.noDataFound.show()
             }
         }
    }

    private fun getPlaylistDetailsById() {
        val widgets = viewModel.getScreenIdResponse()?.data?.widgets?:return
        val totalWidgets = widgets.size
        CoroutineScope(Dispatchers.Main).launch {
            val deferredResults = mutableListOf<Deferred<PlaylistModelList?>>()
            for (counter in railsCounter until totalWidgets) {
                val playlistId = widgets[counter]?.item?.playlist?.enveuPlaylistId?: continue
                deferredResults.add(async(Dispatchers.IO) {
                    getPlaylistDetailsById(playlistId.toString(), counter)
                })
            }
            val results = deferredResults.awaitAll()
            results.forEach { response ->
                response?.let {
                    playlistDataList?.add(it)
                    setPlaylistRailsAdapter()
                }
            }
        }
    }

    private suspend fun getPlaylistDetailsById(playlistId: String, counter: Int): PlaylistModelList? {
        return suspendCancellableCoroutine { continuation ->
            try {
                viewModel.getPlaylistDetailsById(playlistId, LanguageLayer.getCurrentLanguageCode(), 0, 20, getBaseCategory(counter), object : PlaylistApiSuccessListener {
                    override fun onSuccessListener(response: RailCommonData?) {
                        if (response != null) {
                            val playlistModelList = PlaylistModelList().apply {
                                widgets = viewModel.getScreenIdResponse()?.data?.widgets?.get(counter)
                                items = response.enveuVideoItemBeans
                            }
                            continuation.resume(playlistModelList)
                        } else {
                            continuation.resume(null)
                        }
                    }
                })
            } catch (e: Exception) {
                continuation.resumeWithException(e)
            }
        }
    }

   private val searchItemListener = object: SearchItemListener {
       override fun moreSearchItemClickListener(position:Int) {
           setUpBaseCategoryModel(position)
       }

       override fun onSearchItemClickListener(content: EnveuVideoItemBean?) {
           val assetType = content?.assetType
           var mediaType: String? = ""
           if (content?.assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
               mediaType = content?.videoDetails?.videoType
           } else  if (content?.assetType.equals(AppConstants.CUSTOM, ignoreCase = true)) {
               mediaType = content?.customType
           }else if (content?.assetType.equals(AppConstants.AUDIO, ignoreCase = true)){
               mediaType = content?.contentType
           }
            if (content?.assetType.equals(AppConstants.PERSON)) {
               val reelCreatorId = ReelCreatorId()
               reelCreatorId.id = content?.id?.toLong()
               reelCreatorId.externalIdentifier = content?.externalRefId
               reelCreatorId.title = content?.title
               reelCreatorId.posterUrl = content?.posterURL
               Intent(requireActivity(), FollowFollowingProfileActivity::class.java).also {
                   it.putExtra(Constants.CREATOR_CONTENT_BUNDLE, reelCreatorId)
                   startActivityForResult(it, Constants.REQUEST_CODE)
               }
           }
           else if (assetType.equals(AppConstants.VIDEO) && content?.videoDetails?.videoType?.equals(Constants.REEL) == true){
                openUgcFragment(content)
           }
           else{
               if (content != null) {
                   AppCommonMethod.launchDetailScreen(requireActivity(), assetType, content.id, content.sku, mediaType, content.title?:"",content.externalRefId?:"",content.posterURL?:"",0,content.contentSlug?:"", content)
               }
            }
       }
   }

    private fun setUpBaseCategoryModel(position:Int){
        val items = playlistDataList?.get(position)?.widgets
        val baseCategory = BaseCategory()
        baseCategory.contentImageType = items?.item?.imageType
        baseCategory.referenceName = items?.item?.playlist?.referenceName
        baseCategory.railCardType = items?.item?.railCardType
        baseCategory.contentListinglayout = items?.item?.listingLayout
        if (items?.item?.listingLayout == ListingLayoutType.LST.name){
            ActivityLauncher.getInstance().listActivity(requireActivity(), ListActivity::class.java, items.item.playlist?.enveuPlaylistId.toString(), items.name, 0, 0, baseCategory)
        }
        else{
            Intent(requireActivity(), GridActivity::class.java).also { intent ->
                intent.putExtra("playListId", items?.item?.playlist?.enveuPlaylistId.toString())
                intent.putExtra("title", items?.name)
                intent.putExtra("flag", 0)
                intent.putExtra("shimmerType", 0)
                intent.putExtra("baseCategory", Gson().toJson(baseCategory))
                intent.putExtra("isContinueWatching", false)
                startActivityForResult(intent, Constants.REQUEST_CODE)
            }
        }
    }


    private fun getBaseCategory(counter: Int): BaseCategory {
       val items = viewModel.getScreenIdResponse()?.data?.widgets?.get(counter)?.item
        val baseCategory = BaseCategory()
        baseCategory.contentImageType = items?.imageType
        baseCategory.referenceName = items?.playlist?.referenceName
        baseCategory.railCardType = items?.railCardType
        baseCategory.contentListinglayout = items?.railCardType
        return baseCategory
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setPlaylistRailsAdapter(){
        binding.progressBar.hide()
        if (playlistParentAdapter == null) {
            playlistParentAdapter = PlaylistParentAdapter(requireActivity(), playlistDataList, searchItemListener)
            binding.myRecyclerView.layoutManager = LinearLayoutManager(requireActivity(), RecyclerView.VERTICAL, false)
            binding.myRecyclerView.adapter = playlistParentAdapter
            binding.myRecyclerView.setHasFixedSize(true)
            binding.myRecyclerView.setItemViewCacheSize(20)
            binding.myRecyclerView.isNestedScrollingEnabled = false
        } else {
            playlistDataList?.let { playModelList ->
                synchronized(playModelList) {
                    playlistParentAdapter?.notifyDataSetChanged()
                }
            }
        }
    }

    private fun clearPreviousFragment(){
        val previousFragment = childFragmentManager.beginTransaction()
        fragmentList?.values?.forEach { fragment ->
            previousFragment.remove(fragment)
        }
        previousFragment.commitNow()
        fragmentList?.clear()
        activeFragment = null
    }

    private fun setSearchHistoryAdapter(){
        searchHistoryAdapter = SearchHistoryAdapter(searchHistoryList, searchHistoryClickListener)
        binding.searchHistoryRecyclerView.layoutManager = LinearLayoutManager(requireActivity(), RecyclerView.VERTICAL, false)
        binding.searchHistoryRecyclerView.adapter = searchHistoryAdapter
        setRecyclerViewHeightForItems()
    }

    override fun onDestroy() {
        super.onDestroy()
        (activity as HomeActivity).showSubMenu()
    }

    private val searchTabListener = object: SearchTabListener {
        override fun onSearchTabListener(searchTabModel: SearchTabModel?) {
            openSearchResultFragment(searchTabModel)
        }
    }

    private fun setResultTab(){
        val tabList = ArrayList<SearchTabModel>()
        tabList.add(SearchTabModel(0, getString(R.string.movies_tab_text), CONTENT_TYPE to VIDEO, VIDEO_TYPE to MOVIES))
        tabList.add(SearchTabModel(1, getString(R.string.tv_shows_tags) , CONTENT_TYPE to CUSTOM, CUSTOM_TYPE to ENT_SERIES))
        tabList.add(SearchTabModel(2, getString(R.string.shorts_tab_text) , CONTENT_TYPE to VIDEO, VIDEO_TYPE to REEL))
        tabList.add(SearchTabModel(3, getString(R.string.creators_tab_text), CONTENT_TYPE to PERSON, PERSON_TYPE to CREATOR))
        tabList.add(SearchTabModel(4, getString(R.string.search_game_tabs) ,CONTENT_TYPE to CUSTOM, CUSTOM_TYPE to GAME))
        val searchTabAdapter = SearchTabAdapter(tabList, searchTabListener, tabSelectedPosition)
        binding.tabRecyclerView.layoutManager = LinearLayoutManager(requireActivity(), RecyclerView.HORIZONTAL, false)
        binding.tabRecyclerView.adapter = searchTabAdapter
    }


    @SuppressLint("CommitTransaction")
    private fun openSearchResultFragment(searchTabModel: SearchTabModel?) {
        hideKeyboard(binding.searchEditText)
        binding.searchResultLayout.show()
        binding.popularSearchLayout.hide()
        binding.progressBar.hide()
        if (searchHistoryList?.contains(keyword) != true){
            searchHistoryList?.add(keyword)
            preference?.saveSearchHistoryList(searchHistoryList)
        }
        searchTabModel?.keyWord = keyword
        this.searchTabModel = searchTabModel
        if (fragmentList?.get(searchTabModel?.id) == null) {
            searchResultFragment = SearchResultFragment()
            searchResultFragment?.getContentMetaData(searchTabModel)
            childFragmentManager.beginTransaction().add(R.id.search_frame, searchResultFragment as SearchResultFragment).commitNow()
            activeFragment?.let { activity -> searchResultFragment?.let { homeFragment -> childFragmentManager.beginTransaction().hide(activity).show(homeFragment).commit() } }
            activeFragment = searchResultFragment
            fragmentList?.put(searchTabModel?.id, searchResultFragment!!)
        } else {
            fragmentList[searchTabModel?.id]?.let { activeFragment?.let { activeFragment -> childFragmentManager.beginTransaction().hide(activeFragment).show(it).commit() } }
            activeFragment = fragmentList[searchTabModel?.id]
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == Constants.REQUEST_CODE) {
            if (data?.getBooleanExtra(Constants.OPEN_SHORTS_FRAGMENT, false) == true) {
                val reelsContentItem = data.getSerializableExtra(Constants.SHORTS_REELS_BUNDLE) as ReelsContentItem
                (activity as HomeActivity).openShortsReelsFragment(reelsContentItem)
            }
        }
    }

    private fun hideKeyboard(view : EditText?) {
        val imm = requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view?.windowToken, 0)
    }

     fun openUgcFragment(content:EnveuVideoItemBean? = null, reelContentItem:ReelsContentItem? = null){
       val reelsContentItem =  if (content != null) {
               Gson().fromJson(Gson().toJson(content), ReelsContentItem::class.java)
         }
         else{
             reelContentItem
         }
        if (viaSearchUgc == true){
            parentFragmentManager.popBackStack(null, FragmentManager.POP_BACK_STACK_INCLUSIVE)
            val ugcFragment = UgcFragment().apply {
                arguments = Bundle().apply {
                    putSerializable(Constants.SHORTS_REELS_BUNDLE, reelsContentItem)
                }
            }
            parentFragmentManager.beginTransaction().replace(R.id.content_frame, ugcFragment).commit()
        }
         else{
            (activity as HomeActivity).openShortsReelsFragment(reelsContentItem)
         }
    }

    fun getPlayerListener(playerPlayPauseCallBack: PlayerPlayPauseCallBack?) {
       this.playerPlayPauseCallBack = playerPlayPauseCallBack
    }

    companion object {
        const val  VIDEO = "VIDEO"
        const val  CONTENT_TYPE = "contentType"
        const val  PERSON_TYPE = "personType"
        const val  VIDEO_TYPE = "videoType"
        const val  MOVIES = "MOVIES"
        const val  PERSON = "PERSON"
        const val  CREATOR = "CREATOR"
        const val  REEL = "REEL"
        const val  GAME = "GAME"
        const val  CUSTOM = "CUSTOM"
        const val  CUSTOM_TYPE = "customType"
        const val  ENT_SERIES = "ENT_SERIES"
        const val  CUSTOM_TYPE_VALUE = "VIDEO_SERIES,AUDIO_SERIES"
    }
}