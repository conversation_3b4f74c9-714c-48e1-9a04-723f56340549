package com.enveu.activities.new_search

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity.RESULT_OK
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.enveu.R
import com.enveu.activities.follow_follower.FollowFollowingProfileActivity
import com.enveu.activities.follow_follower.HashtagsActivity
import com.enveu.activities.new_search.adapter.HashTagsSearchAdapter
import com.enveu.activities.new_search.adapter.SearchResultAdapter
import com.enveu.activities.new_search.call_back.HashTagItemClickListener
import com.enveu.activities.new_search.call_back.SearchItemListener
import com.enveu.activities.new_search.model.SearchTabModel
import com.enveu.baseModels.BaseFragment
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.databinding.FragmentSearchResultBinding
import com.enveu.networking.response.HashTagsItems
import com.enveu.networking.response.ReelCreatorId
import com.enveu.networking.response.ReelsContentItem
import com.enveu.utils.Constants
import com.enveu.utils.PaginationGridScrollListener
import com.enveu.utils.PaginationScrollListener
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.config.LanguageLayer
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.hide
import com.enveu.utils.show
import com.enveu.view_model.SearchViewModel
import com.google.gson.Gson

class SearchResultFragment : BaseFragment() {
    private lateinit var binding:FragmentSearchResultBinding
    private var layoutType:Int = 0
    private lateinit var viewModel: SearchViewModel
    private var searchTabModel:SearchTabModel? = null
    private var offSet: Int = 0
    private var size: Int = 10
    private var totalPages : Int = 0
    private var searchResultList:ArrayList<EnveuVideoItemBean?> = ArrayList()
    private var hashTagsItemsList:ArrayList<HashTagsItems?>? = ArrayList()
    private var searchResultAdapter: SearchResultAdapter? = null
    private var hashTagsSearchAdapter: HashTagsSearchAdapter? = null
    private var hasSearchData:Boolean = false
    private var isTablet:Boolean = false



    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_search_result, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()
    }

    private fun initUI() {
        viewModel = ViewModelProvider(this)[SearchViewModel::class.java]
        isTablet = resources.getBoolean(R.bool.isTablet)
//        if (layoutType == 4){
//          getHashTagsSearch()
//        }
//        else{
            getSearchResult(searchTabModel?.contentType, searchTabModel?.videoType)
      //  }
        setObserver(viewModel)
        binding.progressBar.show()
    }

    private fun getHashTagsSearch(){
        viewModel.getHasTagsSearch(searchTabModel?.keyWord, offSet,size, true)
    }
    private fun getSearchResult(contentType:Pair<String, String>?, videoType:Pair<String, String>?){
        if (contentType != null && videoType != null) {
                viewModel.getSearchResult(searchTabModel?.keyWord, contentType, videoType, offSet, size, true, LanguageLayer.getCurrentLanguageCode())
            }
        }

    fun getContentMetaData(searchTabModel: SearchTabModel?){
        this.layoutType = searchTabModel?.id!!
        this.searchTabModel = searchTabModel
    }

    private fun setObserver(viewModel: SearchViewModel){
        viewModel.searchResultResponse.observe(viewLifecycleOwner) { searchResultRes ->
            binding.progressBar.hide()
            if (searchResultRes.data?.items?.isNotEmpty() == true) {
                totalPages = searchResultRes.data?.pageInfo?.total?:0
                offSet += 10
                hasSearchData = true
                searchResultRes.data?.items?.let { searchResultList.addAll(it) }
                openSearchResultAdapter()
                binding.searchResultRecycler.show()
                binding.noResultFound.hide()
            }
            else{
                if (!hasSearchData) {
                    binding.searchResultRecycler.hide()
                    binding.noResultFound.show()
                }
            }
        }
        viewModel.hashTagsSearchResponse.observe(viewLifecycleOwner) { hashTagsSearchResponse ->
            binding.progressBar.hide()
            if (hashTagsSearchResponse.data?.items?.isNotEmpty() == true){
                totalPages = hashTagsSearchResponse.data.pageInfo?.total?:0
                offSet += 10
                hashTagsSearchResponse.data.items.let { hashTagsItemsList?.addAll(it) }
                hasSearchData = true
                binding.searchResultRecycler.show()
                binding.noResultFound.hide()
                setHashTagsSearchAdapter()
            }
            else{
                if (!hasSearchData) {
                    binding.searchResultRecycler.hide()
                    binding.noResultFound.show()
                }
            }
        }
    }

    private val hashTagItemClickListener = object : HashTagItemClickListener {
        override fun onHashTagItemClickListener(position: Int) {
            Intent(requireActivity(), HashtagsActivity::class.java).also {
                it.putExtra(Constants.HASHTAGS, hashTagsItemsList?.get(position)?.name)
                startActivityForResult(it, Constants.REQUEST_CODE)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setHashTagsSearchAdapter(){
        if (hashTagsSearchAdapter == null) {
            RecyclerAnimator(requireActivity()).animate(binding.searchResultRecycler)
            hashTagsSearchAdapter = HashTagsSearchAdapter(hashTagsItemsList, hashTagItemClickListener)
            binding.searchResultRecycler.adapter = hashTagsSearchAdapter
            val layoutManager = LinearLayoutManager(requireActivity(), LinearLayoutManager.VERTICAL, false)
            binding.searchResultRecycler.layoutManager = layoutManager
            callHashTagsPagination(layoutManager)
        }
        else{
            hashTagsSearchAdapter?.notifyDataSetChanged()
        }
    }


    @SuppressLint("NotifyDataSetChanged")
    private fun openSearchResultAdapter() {
        if (searchResultAdapter == null) {
            RecyclerAnimator(requireActivity()).animate(binding.searchResultRecycler)
            searchResultAdapter = SearchResultAdapter(requireActivity(), searchResultList, layoutType, searchItemListener)
            binding.searchResultRecycler.adapter = searchResultAdapter
            if (layoutType == 3){
                val layoutManager = LinearLayoutManager(requireActivity(), LinearLayoutManager.VERTICAL, false)
                binding.searchResultRecycler.layoutManager = layoutManager
                callLinearPagination(layoutManager)
            }
            else{
                val calculateSpanCount = if (layoutType == 2){
                    getSpanCountShorts()
                }
                else{
                    getSpanCountMoviesAndPodcast()
                }
                val layoutManager = GridLayoutManager(requireActivity(), calculateSpanCount)
                binding.searchResultRecycler.layoutManager = layoutManager
                callGridPagination(layoutManager)
            }
        }
        else{
            searchResultAdapter?.notifyDataSetChanged()
        }
    }

    private val searchItemListener = object: SearchItemListener {
        override fun moreSearchItemClickListener(position:Int) {

        }

        override fun onSearchItemClickListener(content: EnveuVideoItemBean?) {
            val json = Gson().toJson(content)
            val itemValue = Gson().fromJson(json, EnveuVideoItemBean::class.java)
            val assetType = itemValue.assetType
            var mediaType: String? = ""
            if (itemValue.assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
                mediaType = itemValue.videoDetails.videoType
            } else  if (itemValue.assetType.equals(AppConstants.CUSTOM, ignoreCase = true)) {
                mediaType = itemValue.customType
            } else if (itemValue.assetType.equals(AppConstants.AUDIO, ignoreCase = true)){
                mediaType = itemValue.contentType
            }
            if (content?.assetType.equals(AppConstants.PERSON)) {
                val reelCreatorId = ReelCreatorId()
                reelCreatorId.id = content?.id?.toLong()
                reelCreatorId.externalIdentifier = content?.externalRefId
                reelCreatorId.title = content?.title
                reelCreatorId.posterUrl = content?.posterURL
                Intent(requireActivity(), FollowFollowingProfileActivity::class.java).also {
                    it.putExtra(Constants.CREATOR_CONTENT_BUNDLE, reelCreatorId)
                    startActivityForResult(it, Constants.REQUEST_CODE)
                }
            }
            else if (assetType.equals(AppConstants.VIDEO) && content?.videoDetails?.videoType?.equals(Constants.REEL) == true){
                (parentFragment as? NewSearchFragment)?.openUgcFragment(content)
            }
            else{
                AppCommonMethod.launchDetailScreen(requireActivity(), assetType, itemValue.id, itemValue.sku, mediaType, itemValue.title?:"",itemValue.externalRefId?:"",itemValue.posterURL?:"",0,itemValue.contentSlug?:"", itemValue)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == Constants.REQUEST_CODE) {
            if (data?.getBooleanExtra(Constants.OPEN_SHORTS_FRAGMENT, false) == true) {
                val reelsContentItem = data.getSerializableExtra(Constants.SHORTS_REELS_BUNDLE) as ReelsContentItem
                (parentFragment as? NewSearchFragment)?.openUgcFragment(reelContentItem = reelsContentItem)
            }
        }
    }

    private fun callGridPagination(layoutManager: GridLayoutManager) {
        binding.searchResultRecycler.addOnScrollListener(object : PaginationGridScrollListener(layoutManager) {
            override fun onLoadMore(page: Int, totalItemsCount: Int) {
                if (totalPages > page) {
                    getSearchResult(searchTabModel?.contentType, searchTabModel?.videoType)
                }
            }
        })
    }

    private fun callLinearPagination(layoutManager: LinearLayoutManager) {
        binding.searchResultRecycler.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun onLoadMore(page: Int) {
                if (totalPages > page) {
                    getSearchResult(searchTabModel?.contentType, searchTabModel?.videoType)
                }
            }
        })
    }

    private fun callHashTagsPagination(layoutManager: LinearLayoutManager) {
        binding.searchResultRecycler.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun onLoadMore(page: Int) {
                if (totalPages > page) {
                    getHashTagsSearch()
                }
            }
        })
    }


    private fun getSpanCountMoviesAndPodcast(): Int {
      val value = if (isTablet){
            4
        } else{
            2
        }
        return value
    }

    private fun getSpanCountShorts(): Int {
      val value = if (isTablet){
            6
        } else{
            3
        }
        return value
    }
}