package com.enveu.activities.new_search.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.new_search.call_back.HashTagItemClickListener
import com.enveu.databinding.ItemCircularCardLayoutBinding
import com.enveu.networking.response.HashTagsItems
import com.enveu.utils.hide
import com.enveu.utils.show
import com.enveu.utils.thirdparty.RippleView



class HashTagsSearchAdapter(
    private var hashTagsItemsList: ArrayList<HashTagsItems?>?,
    private var hashTagItemClickListener: HashTagItemClickListener,
) : RecyclerView.Adapter<HashTagsSearchAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemCircularCardLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun getItemCount():Int {
        return hashTagsItemsList?.size?:0
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val hashTagsItem = hashTagsItemsList?.get(position)
        holder.binding.apply {
            tvTitle.text = "#" +hashTagsItem?.name
            tvTitle.show()
            itemImage.hide()
            hashTagsImageView.show()
        }
    }


    inner class ViewHolder(val binding: ItemCircularCardLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.rippleView.setOnClickListener {
                hashTagItemClickListener.onHashTagItemClickListener(bindingAdapterPosition)
            }
        }
    }
}