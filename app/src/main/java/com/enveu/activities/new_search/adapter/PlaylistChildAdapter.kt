package com.enveu.activities.new_search.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.enveu.R
import com.enveu.activities.new_search.call_back.SearchItemListener
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.ImagesItem
import com.enveu.databinding.CircleItemLargeBinding
import com.enveu.databinding.ItemShortCardLayoutBinding
import com.enveu.databinding.LandscapeItemBinding
import com.enveu.utils.Constants
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.getImageUrl
import com.enveu.utils.hide
import com.enveu.utils.setImageWithGlide
import com.enveu.utils.show
import com.enveu.utils.thirdparty.RippleView

class PlaylistChildAdapter(
    private val playlistItemsList: List<EnveuVideoItemBean?>?,
    private val layoutType: Int,
    private val searchItemListener: SearchItemListener
) :RecyclerView.Adapter<PlaylistChildAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val binding: ViewBinding = when (viewType) {
            0 -> LandscapeItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            1 -> CircleItemLargeBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            2 -> ItemShortCardLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            else -> LandscapeItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        }
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val playlistItems = playlistItemsList?.get(position)
        if (playlistItems?.assetType.equals(AppConstants.VIDEO) && playlistItems?.videoDetails?.videoType?.equals(AppConstants.REEL) == true){
            holder.overlayLayout?.show()
            holder.imageCard?.radius = 24f
            holder.titleLayout?.hide()
        } else {
            holder.overlayLayout?.hide()
            holder.imageCard?.radius = 0f
            holder.titleLayout?.show()
        }
        holder.tvTitle?.text = playlistItems?.title
        holder.titleText?.text = playlistItems?.title
        holder.titleText?.show()
        val imageType = getForCircularView(getImageAspectRatio(),playlistItems?.images)
        holder.viewCount?.text = playlistItems?.playCount.toString()
        playlistItems?.posterURL = getImageUrl(imageType, playlistItems?.images, holder.itemImage?.context)
        holder.itemImage?.setImageWithGlide(holder.itemImage.context, playlistItems?.posterURL)
        if (playlistItems?.customDataV3?.isExclusive?.equals("true") == true){
            holder.exclusive?.show()
        } else {
            holder.exclusive?.hide()
        }
//         if(layoutType == 2){
//             holder.overlayLayout?.show()
//             holder.titleLayout?.hide()
//         }
//        else{
//             holder.overlayLayout?.hide()
//             holder.titleLayout?.show()
//         }
    }

    override fun getItemCount(): Int {
      return playlistItemsList?.size?:0
    }

    private fun getImageAspectRatio():String {
        var imageType = Constants.SIXTEEN_INTO_NINE
        when (layoutType) {
            0 -> {
                imageType = Constants.SIXTEEN_INTO_NINE
            }
            1 -> {
                imageType = Constants.TEN_INTO_TEN
            }
            2 -> {
                imageType = Constants.NINE_INTO_SIXTEEN
            }
        }
        return imageType
    }

    private fun getForCircularView(imageAspectRatio: String, images: MutableList<ImagesItem>?):String {
        return images?.find { it.imageContent.imageType.contains("10") }?.imageContent?.imageType ?: imageAspectRatio
    }

    override fun getItemViewType(position: Int): Int = layoutType

   inner class ViewHolder(binding: ViewBinding) :RecyclerView.ViewHolder(binding.root) {
      val titleText: TextView? = binding.root.findViewById(R.id.tvTitle)
      val titleLayout: RelativeLayout? = binding.root.findViewById(R.id.titleLayout)
      val viewCount: TextView? = binding.root.findViewById(R.id.viewCount)
      val itemImage: ImageView? = binding.root.findViewById(R.id.itemImage)
      val overlayLayout: LinearLayout? = binding.root.findViewById(R.id.overlayLayout)
      val rippleView: RippleView? = binding.root.findViewById(R.id.rippleView)
      val exclusive: TextView? = binding.root.findViewById(R.id.exclusive)
      val imageCard: CardView? = binding.root.findViewById(R.id.imageCard)
      val tvTitle: TextView? = binding.root.findViewById(R.id.tvTitle)

       init {
           rippleView?.setOnClickListener {
             searchItemListener.onSearchItemClickListener(playlistItemsList?.get(bindingAdapterPosition))
           }
       }
    }
}