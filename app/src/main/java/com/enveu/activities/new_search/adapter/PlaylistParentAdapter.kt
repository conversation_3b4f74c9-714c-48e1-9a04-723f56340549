package com.enveu.activities.new_search.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.new_search.call_back.SearchItemListener
import com.enveu.databinding.ItemPlaylistParentAdapterLayoutBinding
import com.enveu.networking.response.screen_id.WidgetsItem
import com.enveu.networking.response.searchModel.PlaylistModelList
import com.enveu.utils.Constants
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show

class PlaylistParentAdapter(
    private val context: Context,
    private val playlistModelList: ArrayList<PlaylistModelList>?,
    private val searchItemListener: SearchItemListener
) :RecyclerView.Adapter<PlaylistParentAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemPlaylistParentAdapterLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    @SuppressLint("SetTextI18n", "StringFormatMatches")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val singlePlaylist = playlistModelList?.get(position)
        holder.binding.apply {
            if (getLocaleWiseTitle(singlePlaylist?.widgets)?.isNotEmpty() == true || singlePlaylist?.items?.isNotEmpty() == true) {
                titleHeading.mainHeaderTitle.show()
                titleHeading.itemsCountText.show()
                singlePlaylist?.widgets?.name = getLocaleWiseTitle(singlePlaylist?.widgets)
                titleHeading.headingTitle.text = singlePlaylist?.widgets?.name
                titleHeading.itemsCountText.text = context.getString(R.string.results, singlePlaylist?.items?.size)
                val playlistChildAdapter = PlaylistChildAdapter(singlePlaylist?.items, getLayoutType(singlePlaylist?.widgets?.item?.imageType), searchItemListener)
                recyclerViewList.adapter = playlistChildAdapter
                recyclerViewList.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
                recyclerViewList.show()
                recyclerViewList.isNestedScrollingEnabled = false
            } else {
                recyclerViewList.hide()
                titleHeading.mainHeaderTitle.hide()
            }
        }
        rotateImageLocaleWise(holder.binding.titleHeading.moreText)
    }


    private fun getLocaleWiseTitle(widgetsItem: WidgetsItem?): String? {
        var title:String? = ""
        title = if (widgetsItem?.item?.enableMultilingualTitle == true){
            if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.LANGUAGE_ARABIC, ignoreCase = true)){
                widgetsItem?.item?.multilingualTitle?.arabic
            } else{
                widgetsItem?.item?.multilingualTitle?.english
            }
        } else{
            widgetsItem?.name
        }
      return title
    }

    private fun getLayoutType(imageType: String?): Int {
        var layoutType = 0
        when (imageType) {
            Constants.LDS -> {
                layoutType =  0
            }
            Constants.CIR -> {
                layoutType =  1
            }
            Constants.PR1 -> {
                layoutType =  2
            }
        }
        return  layoutType
    }


    override fun getItemCount(): Int {
        return playlistModelList?.size?:0
    }

   inner class ViewHolder(val binding: ItemPlaylistParentAdapterLayoutBinding):RecyclerView.ViewHolder(binding.root) {
       init {
           binding.titleHeading.mainHeaderTitle.setOnClickListener {
               searchItemListener.moreSearchItemClickListener(bindingAdapterPosition)
           }
       }
   }
}