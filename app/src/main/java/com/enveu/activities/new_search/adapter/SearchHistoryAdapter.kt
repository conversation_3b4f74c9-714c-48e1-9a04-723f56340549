package com.enveu.activities.new_search.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.new_search.call_back.SearchHistoryClickListener
import java.util.ArrayList

class SearchHistoryAdapter(
    private val searchHistoryList: ArrayList<String?>?,
    private val searchHistoryClickListener: SearchHistoryClickListener
) :RecyclerView.Adapter<SearchHistoryAdapter.ViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val rootLayout = LayoutInflater.from(parent.context).inflate(R.layout.item_search_history_layout, parent, false)
        return ViewHolder(rootLayout)
    }


    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        holder.titleHistoryText.text = searchHistoryList?.get(position)
    }

    override fun getItemCount(): Int {
       return if ((searchHistoryList?.size?:0) < 5){
           searchHistoryList?.size?:0
       }
        else{
           5
        }
    }

   inner class ViewHolder(view: View):RecyclerView.ViewHolder(view) {
         val titleHistoryText:TextView = view.findViewById(R.id.titleHistoryText)
         val parentLayout:ConstraintLayout = view.findViewById(R.id.parentLayout)

       init {
           parentLayout.setOnClickListener {
               searchHistoryClickListener.searchHistoryItemClickListener(searchHistoryList?.get(bindingAdapterPosition))
           }
       }
    }
}