package com.enveu.activities.new_search.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.enveu.R
import com.enveu.activities.new_search.call_back.SearchItemListener
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.ImagesItem
import com.enveu.databinding.ItemCircularCardLayoutBinding
import com.enveu.databinding.ItemGridLandscapeLayoutBinding
import com.enveu.databinding.ItemShortLayoutBinding
import com.enveu.utils.Constants
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.getImageUrl
import com.enveu.utils.hide
import com.enveu.utils.setImageWithGlide
import com.enveu.utils.show
import com.enveu.utils.thirdparty.RippleView

class SearchResultAdapter(
    private var context: Context,
    private var searchResultList: ArrayList<EnveuVideoItemBean?>?,
    private val layoutType: Int,
    private val searchItemListener: SearchItemListener,
) : RecyclerView.Adapter<SearchResultAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding: ViewBinding = when (viewType) {
            0 -> ItemGridLandscapeLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            1 -> ItemGridLandscapeLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            2 -> ItemShortLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            3 -> ItemCircularCardLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            4 -> ItemGridLandscapeLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            else -> ItemGridLandscapeLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        }
        return ViewHolder(binding)
    }

    override fun getItemViewType(position: Int): Int = layoutType

    override fun getItemCount():Int {
        return searchResultList?.size?:0
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val playlistItems = searchResultList?.get(position)
        if (playlistItems?.assetType.equals(AppConstants.VIDEO) && playlistItems?.videoDetails?.videoType?.equals(AppConstants.REEL) == true){
            holder.overlayLayout?.show()
        } else {
            holder.overlayLayout?.hide()
        }
        holder.tvTitle?.text = playlistItems?.title
        holder.tvTitle?.show()
        val imageType = getForCircularView(getImageAspectRatio(),playlistItems?.images)
        searchResultList?.get(position)?.posterURL = getImageUrl(imageType, playlistItems?.images, holder.itemImage?.context)
        holder.itemImage?.setImageWithGlide(holder.itemImage.context, searchResultList?.get(position)?.posterURL)
        if (playlistItems?.customDataV3?.isExclusive?.equals("true") == true){
            holder.exclusive?.show()
        } else {
            holder.exclusive?.hide()
        }
        if (searchResultList?.get(position)?.posterURL?.endsWith("null", false) == true){
            if (layoutType == 0 || layoutType == 3){
                holder.imageTitle?.show()
                holder.imageTitle?.text = playlistItems?.title
            }
        }

        if (playlistItems?.customContent?.customType?.equals(AppConstants.VIDEO_SERIES) == true){
            holder.contentTypeText?.text = context.getString(R.string.type_video)
        } else {
            holder.contentTypeText?.text = context.getString(R.string.type_audio)
        }
        if (layoutType == 3){
            holder.contentTypeText?.show()
        }
        else{
            holder.contentTypeText?.hide()
        }
        holder.viewCount?.text = playlistItems?.playCount.toString()
        val playCount: Int? = playlistItems?.playCount
        if (playCount != null) {
            if(playCount>0){
                holder.viewCount?.show()
            } else {
                holder.viewCount?.hide()
            }
        }
    }

    private fun getImageAspectRatio():String {
        var imageType = Constants.SIXTEEN_INTO_NINE
        when (layoutType) {
            0,1,4 -> {
                imageType = Constants.SIXTEEN_INTO_NINE
            }
            3 -> {
                imageType = Constants.TEN_INTO_TEN
            }
            2 -> {
                imageType = Constants.NINE_INTO_SIXTEEN
            }
        }
        return imageType
    }

    private fun getForCircularView(imageAspectRatio: String?, images: MutableList<ImagesItem>?): String {
        val imageType = images
            ?.find { it.imageContent?.imageType?.contains("10") == true }
            ?.imageContent?.imageType
            ?: imageAspectRatio
            ?: "16*9"
        return imageType
    }

   inner class ViewHolder(binding: ViewBinding) : RecyclerView.ViewHolder(binding.root) {
        val tvTitle: TextView? = binding.root.findViewById(R.id.tvTitle)
        val rippleView: RippleView? = binding.root.findViewById(R.id.rippleView)
        val viewCount: TextView? = binding.root.findViewById(R.id.viewCount)
        val itemImage: ImageView? = binding.root.findViewById(R.id.itemImage)
        val contentTypeText: TextView? = binding.root.findViewById(R.id.contentTypeText)
        val exclusive: TextView? = binding.root.findViewById(R.id.exclusive)
        val overlayLayout: LinearLayout? = binding.root.findViewById(R.id.overlayLayout)
       val imageCard: CardView? = binding.root.findViewById(R.id.imageCard)
       val titleLayout: LinearLayout? = binding.root.findViewById(R.id.titleLayout)
       val imageTitle: TextView? = binding.root.findViewById(R.id.imageTitle)

       init {
           rippleView?.setOnClickListener {
               searchItemListener.onSearchItemClickListener(searchResultList?.get(bindingAdapterPosition))
           }
        }
    }
}
