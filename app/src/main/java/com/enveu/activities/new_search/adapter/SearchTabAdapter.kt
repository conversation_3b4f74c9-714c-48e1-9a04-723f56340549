package com.enveu.activities.new_search.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.new_search.call_back.SearchTabListener
import com.enveu.activities.new_search.model.SearchTabModel
import com.enveu.utils.hide
import com.enveu.utils.show
import java.util.ArrayList


class SearchTabAdapter(
    private val searchTabModelList: ArrayList<SearchTabModel>?,
    private val searchTabListener: SearchTabListener,
    private var tabSelectedPosition: Int
) : RecyclerView.Adapter<SearchTabAdapter.ViewHolder>() {



    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val rootLayout = LayoutInflater.from(parent.context).inflate(R.layout.item_search_tab_layout, parent, false)
        return ViewHolder(rootLayout)
    }


    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.tabTitleText.text = searchTabModelList?.get(position)?.displayName
        if (position == tabSelectedPosition){
            holder.tabSelectorView.show()
            holder.tabTitleText.typeface  = ResourcesCompat.getFont(holder.tabTitleText.context, R.font.poppins_bold)
            holder.tabTitleText.setTextColor(ContextCompat.getColor(holder.tabTitleText.context, R.color.tab_selected_color))
        }
        else{
            holder.tabSelectorView.hide()
            holder.tabTitleText.typeface  = ResourcesCompat.getFont(holder.tabTitleText.context, R.font.poppins_regular)
            holder.tabTitleText.setTextColor(ContextCompat.getColor(holder.tabTitleText.context, R.color.tab_unselected_color))
        }
    }

    override fun getItemCount(): Int {
        return searchTabModelList?.size?:0
    }

  @SuppressLint("NotifyDataSetChanged")
  inner class ViewHolder(view: View): RecyclerView.ViewHolder(view) {
        val tabTitleText: TextView = view.findViewById(R.id.tabTitleText)
        val tabSelectorView: View = view.findViewById(R.id.tabSelectorView)
        val parentLayout: ConstraintLayout = view.findViewById(R.id.parentLayout)

      init {
          parentLayout.setOnClickListener {
              searchTabListener.onSearchTabListener(searchTabModelList?.get(bindingAdapterPosition))
              tabSelectedPosition = bindingAdapterPosition
              notifyDataSetChanged()
          }
      }
    }
}