package com.enveu.activities.profile.activate_device

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.ViewModelProvider
import com.google.gson.JsonObject
import com.enveu.R
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.databinding.ActivityActivateDeviceBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.enveu.view.PinView

/*
*
Business Logic for Device Activation:
* as a end user i can open my app on a smart tv device. Depending on the customer work flow the app should ask for user to login.
* Depending on a platform support the user either see a QR code/Pin/OTP/user-id password input as per the customer work flow.
*
*
* Currently we are going to support user's ability to input the 4 digit pin in the pin box and they verify it
* the user can open the ActivateTV feature in the menu they click on it and ActivateTv Section is open
* If user enters the wrong input then a pop up is shown and in the pop up we show the error msg (Activation code is not matched) this error msg is provided by client
* if the entered input is correct then show success message :
* for this feature user should be logged in to the device.
* If user is logged out then they are taken to the login screen
* if user not entered any input and then they click on verify button then nothing will happen
*
*
*  QUERIES :
* what will be impact of Device Management/Session Management on this feature
*
*
*
*

PIN/OTP Entry: Obtain the PIN or OTP entered by the user to activate the device.
Activation Verification:

ActivationModel Creation: Create an ActivateDeviceModel object to encapsulate the entered PIN/OTP.
Verification Request: Send a verification request to the server through the viewModel.getTvVerify() method.
Server Response Handling:

Response Observation: Observe the response from the server.
Response Interpretation: Check the response code to determine the success or failure of device activation.
Actions Based on Verification:

Successful Activation (Response Code 2000): Log the verification status or take further actions as needed.
Failed Activation: Log the error/debug message or handle the failure scenario appropriately.

* */




// Activity responsible for activating a device
class ActivateDeviceActivity() : BaseBindingActivity<ActivityActivateDeviceBinding?>(), CommonDialogFragment.EditDialogListener {

    lateinit var viewModel: RegistrationLoginViewModel
    private var pinOtp: String = ""
    private val stringsHelper by lazy { StringsHelper }

    // Inflating the binding layout for the activity
    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityActivateDeviceBinding? {
        return ActivityActivateDeviceBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Initializing ViewModel
        viewModel = ViewModelProvider(this)[RegistrationLoginViewModel::class.java]

        // Setup UI elements
        setupUI()

        // Set up click listeners
        setupClickListeners()
    }

    // Method to configure UI elements
    private fun setupUI() {
        // Configure toolbar UI elements
        binding?.toolbar?.apply {
            logoMain2.visibility = View.GONE
            searchIcon.visibility = View.GONE
            backLayout.visibility = View.VISIBLE
            titleMid.visibility = View.VISIBLE
            titleMid.text = getText(R.string.more_activate_device)
            titleMid.setBackgroundResource(0)
            backLayout.setOnClickListener { onBackPressed() }
        }
    }

    // Method to set up click listeners
    private fun setupClickListeners() {
        // Set click listener for continue button
        binding?.continueBtn?.setOnClickListener {
            pinOtp = binding?.pinViewOtp?.text.toString()
            // Verify PIN/OTP if it's not empty
            if (pinOtp.isNotEmpty()) {
                hideKeyboard(binding?.pinViewOtp)
                val jsonObject = JsonObject()
                jsonObject.addProperty("activationCode", pinOtp)
                verifyDeviceActivation(jsonObject)
            }
        }
    }

    // Method to verify device activation
    private fun verifyDeviceActivation(pin : JsonObject) {
        // Create ActivateDeviceModel with entered PIN/OTP
        // Call ViewModel method to verify device activation
        viewModel.getTvVerify(
            pin,
            KsPreferenceKeys.getInstance().appPrefAccessToken
        ).observe(this) { response ->
            // Handle response from server
            if (response?.responseCode == 2000) {
                // Log verification status
                commonDialog(
                    "", getString(R.string.profile_registered_successfully), stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                        getString(R.string.popup_continue)
                    )
                )
                Log.d("checkResponse3", response.data?.isVerified.toString())
            } else {
                // Log error message if verification fails
                commonDialog(
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ), getString(R.string.activatioin_code_not_found), stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                        getString(R.string.popup_continue)
                    )
                )
                Log.d("checkResponse4", (response?.debugMessage ?: "Unknown error").toString())
            }
        }
    }

    // Method to show a common dialog
    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm: FragmentManager = supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    // Callback when action button is clicked in the dialog
    override fun onActionBtnClicked() {
        binding?.pinViewOtp?.text?.clear()
    }

    override fun onCancelBtnClicked() {

    }

    private fun hideKeyboard(view : PinView?) {
        val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view?.windowToken, 0)
    }
}
