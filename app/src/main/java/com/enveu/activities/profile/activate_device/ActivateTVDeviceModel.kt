package com.enveu.activities.profile.activate_device


import com.google.gson.annotations.SerializedName

data class ActivateTVDeviceModel(
    @SerializedName("data")
    val `data`: Data?= null,
    @SerializedName("debugMessage")
    val debugMessage: Any?, // null
    @SerializedName("responseCode")
    val responseCode: Int // 2000
) {
    data class Data(
        @SerializedName("isVerified")
        val isVerified: Boolean ?= false// true
    )
}