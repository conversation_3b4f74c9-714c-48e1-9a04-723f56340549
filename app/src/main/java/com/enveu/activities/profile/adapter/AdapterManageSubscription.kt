package com.enveu.activities.profile.adapter

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.res.ResourcesCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.R.layout
import com.enveu.R.string
import com.enveu.activities.profile.adapter.AdapterManageSubscription.ItemHolder
import com.enveu.activities.purchase.purchase_model.PurchaseModel
import com.enveu.activities.usermanagment.EnumClasses.SubscriptionPipe
import com.enveu.activities.usermanagment.adapter.AdapterPlan
import com.enveu.databinding.ManageSubscriptionItemBinding
import com.enveu.utils.Logger
import com.enveu.utils.commonMethods.AppCommonMethod.Companion.expiryDate
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import java.util.Objects

class AdapterManageSubscription(
    var items: List<PurchaseModel>,
    var mListener: OnPurchaseItemClick,
    var context: Context
) : RecyclerView.Adapter<ItemHolder>() {
    var paymentMode: String? = null

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ItemHolder {
        val manageSubscriptionItemBinding = DataBindingUtil.inflate<ManageSubscriptionItemBinding>(
            LayoutInflater.from(
                context
            ), layout.manage_subscription_item, viewGroup, false
        )
        return ItemHolder(manageSubscriptionItemBinding)
    }

    override fun onBindViewHolder(holder: ItemHolder, position: Int) {
        val model = items[position]


        if (KsPreferenceKeys.getInstance().appLanguage == "spanish"){
            if ((model.description_nl != null) && !model.description_nl.equals(
                    "",
                    ignoreCase = true
                ) && !model.description_nl.equals("null", ignoreCase = true)
            ) {
                holder.binding.annuallyId.text = model.description_nl
            }else{
                if ((model.description != null) && !model.description.equals(
                        "",
                        ignoreCase = true
                    ) && !model.description.equals("null", ignoreCase = true)
                ) {
                    holder.binding.annuallyId.text = model.description
                }
            }
        }else if (KsPreferenceKeys.getInstance().appLanguage == "English") {
            if ((model.description_en != null) && !model.description_en.equals(
                    "",
                    ignoreCase = true
                ) && !model.description_en.equals("null", ignoreCase = true)
            ) {
                holder.binding.annuallyId.text = model.description_en
            } else {
                if ((model.description != null) && !model.description.equals(
                        "",
                        ignoreCase = true
                    ) && !model.description.equals("null", ignoreCase = true)
                ) {
                    holder.binding.annuallyId.text = model.description
                }
            }
        }

        if ((model.title != null) && !model.title.equals(
                "",
                ignoreCase = true
            ) && !model.title.equals("null", ignoreCase = true)
        ) {
            holder.binding.premiumId.text = model.title
        }

        if (model.customData.isPurchaseViaCMS.equals("True",ignoreCase = true)){
            holder.binding.statusId.text = context.getString(string.expire_on)
            holder.binding.gracePeriodDate.visibility = View.GONE
            holder.binding.cancellationDate.visibility = View.GONE
            holder.binding.info.visibility = View.GONE
            val date = expiryDate(model.expiryDate)
            holder.binding.expireDate.text = date
            holder.binding.expireDate.setTextColor(holder.binding.activeId.context.getColor(R.color.resend_otp))
            holder.binding.paymentMode.visibility = View.GONE
            holder.binding.paymentModeId.visibility = View.GONE
            holder.binding.paymentId.visibility = View.INVISIBLE
            holder.binding.autoRenewal.visibility = View.GONE
            holder.binding.activatedBussiness.visibility = View.VISIBLE
            holder.binding.activatedBussiness.text =  context.getString(string.activated_by_business)

            holder.binding.autoRenewal.setTextColor(holder.binding.activeId.context.getColor(R.color.white))
            holder.binding.subscriptionType.visibility = View.GONE
            if (model.entitlementState || model.isOnHold) {
                holder.binding.activeId.visibility = View.VISIBLE
                holder.binding.activeId.setText(string.active_text)
            } else {
                holder.binding.activeId.text = ""
            }
        }else if (model.onGrace){
            holder.binding.info.visibility = View.VISIBLE
            holder.binding.info.setOnClickListener {
                    mListener.onCancelSubscription(1, "")
                }
            holder.binding.statusId.setText(string.grace_period_start_date)
            if (model.graceStartDate != null && !model.graceStartDate.equals("")){
                val date = expiryDate(model.graceStartDate)
                holder.binding.expireDate.text = date
                holder.binding.expireDate.setTextColor(holder.binding.activeId.context.getColor(R.color.resend_otp))
            }
            if (model.graceEndDate != null && !model.graceEndDate.equals("")){
                val date2 = expiryDate(model.graceEndDate)
                holder.binding.gracePeriodDateText.text = date2
                holder.binding.gracePeriodDateText.setTextColor(holder.binding.activeId.context.getColor(R.color.resend_otp))
            }
            if (model.entitlementState) {
                holder.binding.activeId.visibility = View.VISIBLE
                holder.binding.activeId.setText(string.active_text)
            } else {
                holder.binding.activeId.text = ""
            }
            if ((model.paymentProvider != null) && !model.paymentProvider.equals(
                    "",
                    ignoreCase = true
                ) && !model.paymentProvider.equals("null", ignoreCase = true)
            ) {
                if (model.paymentProvider.equals("GOOGLE_IAP", ignoreCase = true)) {
                    if (!model.isCancelled) {
                        holder.binding.cancelSubscriptionId.visibility = View.VISIBLE
                    }
                }
                setImageView(model.paymentProvider, holder)
            } else {
                paymentMode = "NA"
            }

            if (model.subscriptionType != null) {
                if (model.subscriptionType != null) holder.binding.subscriptionType.text =
                    String.format(
                        Objects.requireNonNull(SubscriptionPipe.fromCode(model.subscriptionType))?.displayText!!
                    )
            }
        }else if (model.isOnHold){
            holder.binding.info.visibility = View.VISIBLE
            holder.binding.info.setOnClickListener {
                mListener.onCancelSubscription(2, "")
            }
            val holdDate = expiryDate(model.holdStartDate)
            holder.binding.expireDate.text = holdDate
            holder.binding.statusId.setText(string.hold_period_start_date)
            holder.binding.gracePeriodDate.visibility = View.GONE
            holder.binding.gracePeriodDateText.visibility = View.GONE
            if (model.entitlementState) {
                holder.binding.activeId.visibility = View.VISIBLE
                holder.binding.activeId.setText(string.active_text)
            } else {
                holder.binding.activeId.text = ""
            }
            if ((model.paymentProvider != null) && !model.paymentProvider.equals(
                    "",
                    ignoreCase = true
                ) && !model.paymentProvider.equals("null", ignoreCase = true)
            ) {
                if (model.paymentProvider.equals("GOOGLE_IAP", ignoreCase = true)) {
                    if (!model.isCancelled) {
                        holder.binding.cancelSubscriptionId.visibility = View.VISIBLE
                    }
                }
                setImageView(model.paymentProvider, holder)
            } else {
                paymentMode = "NA"
            }
            if ((model.title != null) && !model.title.equals(
                    "",
                    ignoreCase = true
                ) && !model.title.equals("null", ignoreCase = true)
            ) {
                holder.binding.premiumId.text = model.title
            }

            if (model.subscriptionType != null) {
                if (model.subscriptionType != null) holder.binding.subscriptionType.text =
                    String.format(
                        Objects.requireNonNull(SubscriptionPipe.fromCode(model.subscriptionType))?.displayText!!
                    )
            }
        }
        else {
            holder.binding.info.visibility = View.GONE
            holder.binding.statusId.text = context.getString(string.renewes_on)
            val date = expiryDate(model.expiryDate)
            holder.binding.expireDate.text = date
            holder.binding.gracePeriodDateText.visibility = View.GONE

            /*if (((model.price != null) && !model.price.equals(
                    "",
                    ignoreCase = true
                ) && !model.price.equals("null", ignoreCase = true)
                        && (model.currency != null) && !model.currency.equals(
                    "",
                    ignoreCase = true
                ) && !model.currency.equals("null", ignoreCase = true))
            ) {
                val currencyCode = CurrencyCode.fromCode(model.currency)
                val displayTextCurrencyCode = currencyCode?.displayTextCurrencyCode ?: ""
                holder.binding.paymentId.text = String.format("%s %s", displayTextCurrencyCode, model.price)
            }*/

            if (model.subscriptionType != null) {
                if (model.subscriptionType != null) holder.binding.subscriptionType.text =
                    String.format(
                        Objects.requireNonNull(SubscriptionPipe.fromCode(model.subscriptionType))?.displayText!!
                    )
            }

            if (model.entitlementState) {
                holder.binding.activeId.visibility = View.VISIBLE
                holder.binding.activeId.setText(string.active_text)
            } else {
                holder.binding.activeId.text = ""
            }

            holder.binding.gracePeriodDate.visibility = View.GONE

            try {
                if (model.isCancelled) {
                    holder.binding.cancelSubscriptionId.visibility = View.GONE
                    holder.binding.llCancellationDate.visibility = View.VISIBLE
                    val date = expiryDate(model.expiryDate)
                    holder.binding.cancellationText.text = date
                    holder.binding.gracePeriodDateText.setTextColor(holder.binding.activeId.context.getColor(R.color.resend_otp))
                } else {
                    holder.binding.llCancellationDate.visibility = View.GONE
                }
            } catch (e: Exception) {
                Logger.e(e)
            }

            try {
                if (model.customData.orderIdentifier == null) {
                    holder.binding.llCancellationDate.visibility = View.VISIBLE
                    val date = expiryDate(model.expiryDate)
                    holder.binding.cancellationText.text = date
                }
            } catch (e: Exception) {
            }

            if ((model.paymentProvider != null) && !model.paymentProvider.equals(
                    "",
                    ignoreCase = true
                ) && !model.paymentProvider.equals("null", ignoreCase = true)
            ) {
                if (model.paymentProvider.equals("GOOGLE_IAP", ignoreCase = true)) {
                    if (!model.isCancelled) {
                        holder.binding.cancelSubscriptionId.visibility = View.VISIBLE
                    }
                }
                setImageView(model.paymentProvider, holder)
            } else {
                paymentMode = "NA"
            }
        }


        holder.binding.cancelSubscriptionId.setOnClickListener(View.OnClickListener {

            if (model.customData.isPurchaseViaCMS.equals("True", ignoreCase = true)) {
                mListener.onCancelSubscription(3, "")
            } else if (model.paymentProvider.equals("Web", ignoreCase = true)) {
                mListener.onCancelSubscription(4, "Web")

            } else if (model.paymentProvider.equals("APPLE_IAP", ignoreCase = true)) {
                mListener.onCancelSubscription(5, "APPLE_IAP")
            }
          else {
                val appPackageName = context.packageName
                AnalyticsUtils.logSubscriptionEvent(
                    context,
                    AppConstants.CANCEL_SUBSCRIPTION,
                    model.title,
                    model.price,
                    model.currency,
                    AppConstants.ANDROID
                )
                try {
                    context.startActivity(
                        Intent(
                            Intent.ACTION_VIEW,
                            Uri.parse("https://play.google.com/store/account/subscriptions?package=$appPackageName")
                        )
                    )
                } catch (anfe: ActivityNotFoundException) {
                    context.startActivity(
                        Intent(
                            Intent.ACTION_VIEW,
                            Uri.parse("https://play.google.com/store/apps/details?id=$appPackageName")
                        )
                    )
                }
            }
        })
    }

    private fun setImageView(paymentType: String?, holder: ItemHolder) {
        if (paymentType != null) {
            when (paymentType) {
                "STRIPE" -> holder.binding.paymentMode.setImageResource(R.drawable.stripe)
                "XENDIT" -> holder.binding.paymentMode.setImageResource(R.drawable.xendit)
                "ROKU_IAP" -> holder.binding.paymentMode.setImageResource(R.drawable.roku)
                "RAZORPAY" -> holder.binding.paymentMode.setImageResource(R.drawable.razorpay)
                "PAYPAL" -> holder.binding.paymentMode.setImageResource(R.drawable.paypal)
                "GOOGLE_IAP" -> holder.binding.paymentMode.setImageResource(R.drawable.google_play)
                "DCB" -> holder.binding.paymentMode.setImageResource(R.drawable.ais)
                "APPLE_IAP" -> holder.binding.paymentMode.setImageResource(R.drawable.apple)
                "AMAZON_IAP" -> holder.binding.paymentMode.setImageResource(R.drawable.amazon)
                "TWO_C_TWO_P" -> holder.binding.paymentMode.setImageResource(R.drawable.two_c_2_p)
                else -> {}
            }
        }
    }

    override fun getItemCount(): Int {
        return items.size
    }

    class ItemHolder(val binding: ManageSubscriptionItemBinding) : RecyclerView.ViewHolder(
        binding.root
    )


    interface OnPurchaseItemClick {
        fun onPurchaseCardClick(click: Boolean, planName: PurchaseModel?)
        fun setDescription(description: String?, planName: PurchaseModel?)
        fun onCancelSubscription(i : Int, provider : String)
    }
}
