package com.enveu.activities.profile.order_history.adapter

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.graphics.drawable.DrawableCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.profile.order_history.model.TransactionHistoryModel
import com.enveu.activities.purchase.purchase_model.PurchaseModel
import com.enveu.databinding.OrderHistoryItemBinding
import com.enveu.utils.colorsJson.converter.AppColors
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.stringsJson.converter.StringsHelper
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class OrderHistoryAdapter(
    private var list: MutableList<PurchaseModel>,
    private val currentLanguage: String,
    private val transactionHistoryModel: TransactionHistoryModel
) : RecyclerView.Adapter<OrderHistoryAdapter.ViewHolder>() {
    private var context: Activity? = null
    private val colorsHelper by lazy { ColorsHelper }
    private var paymentMode: String? = null
    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context as Activity
        val itemOrderHistoryBinding = DataBindingUtil.inflate<OrderHistoryItemBinding>(
            LayoutInflater.from(context),
            R.layout.order_history_item,
            viewGroup,
            false
        )
        itemOrderHistoryBinding.colorsData = colorsHelper
        itemOrderHistoryBinding.stringData = StringsHelper
        return ViewHolder(itemOrderHistoryBinding)
    }
    private fun getCurrencySymbol(currencyCode: String): String {
        return when (currencyCode) {
            "USD" -> "$"
            "INR" -> "₹"
            "MXN" -> "Mex$"
            "ARS", "CLP", "COP" -> "$"
            "CRC" -> "₡"
            "BRL" -> "R$"
            "BOB" -> "Bs."
            "GTQ" -> "Q"
            "DOP" -> "RD$"
            "PYG" -> "₲"
            "PEN" -> "S/."
            "UYU" -> "\$U"
            "Euro" -> "€"
            "AUD" -> "A$"
            "GBP" -> "£"
            "EUR" -> "€"
            else -> ""
        }
    }


    @SuppressLint("SetTextI18n", "ResourceType")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.binding.llLayout.background =
            colorsHelper.strokeBgDrawable(AppColors.tphBgColor(), AppColors.detailPageTabItemSelectedTxtColor(), 10f)
        val model = list[position]

        model.subscriptionType?.let {
//            when(it){
//                "ONE_TIME" ->  holder?.binding?.orderPurchaseType?.text = "Subscription"
//                "RECURRING_SUBSCRIPTION" -> holder?.binding?.orderPurchaseType?.text = "Subscription"
//                "PERPETUAL" ->  holder?.binding?.orderPurchaseType?.text = "Perpetual"
//                "RENTAL" -> holder.binding.orderPurchaseType.text =  "Rental"
//                else -> {}
//            }
            holder.binding.orderPurchaseType.text = it
        }

        model.currency?.let { currencyCode ->
            val currencySymbol = getCurrencySymbol(currencyCode)
            holder.binding.packageAmount.text = "${currencySymbol}${model.price}"
        } ?: run {
            holder.binding.packageAmount.text = ""
        }

        if (model.isOnTrial == true){
            holder?.binding?.activeExpired?.setBackgroundColor(Color.parseColor("#f6caca"))
            holder?.binding?.activeExpired?.text =  holder?.binding?.activeExpired?.context?.getString(R.string.on_trail) +
                    " ${model.trialDuration} ${model.trialType}"
            holder?.binding?.activeExpired?.setTextColor(
                Color.parseColor("#eb5757") // Hexadecimal color code
            )

        }else {
            model.transactionEntitlementState?.let {
                if (it.equals("CANCELLED", ignoreCase = true)) {
                    holder?.binding?.activeExpired?.text = "CANCELLED"

                    val unwrappedDrawable = holder.binding.activeExpired.background
                    val wrappedDrawable = DrawableCompat.wrap(unwrappedDrawable)
                    holder?.binding?.activeExpired?.setTextColor(
                        Color.parseColor("#eb5757") // Hexadecimal color code
                    )
                    DrawableCompat.setTint(wrappedDrawable, Color.rgb(246, 202, 202))
                } else if (it.equals("ACTIVE", ignoreCase = true)) {
                    val unwrappedDrawable = holder.binding.activeExpired.background
                    val wrappedDrawable = DrawableCompat.wrap(unwrappedDrawable)
                    DrawableCompat.setTint(wrappedDrawable, Color.rgb(205, 253, 223))
                    holder?.binding?.activeExpired?.text = "Active"
                    holder?.binding?.activeExpired?.setTextColor(
                        Color.parseColor("#479a5a") // Hexadecimal color code
                    )
                } else {

                }
            } ?: run {
                holder?.binding?.activeExpired?.text = ""
            }
        }

        /*   model.orderStatus?.let {
               if (it.equals("", true)){
                   holder?.binding?.activeExpired?.text = it
                   val unwrappedDrawable = holder.binding.activeExpired.background
                   val wrappedDrawable = DrawableCompat.wrap(unwrappedDrawable)
                   DrawableCompat.setTint(wrappedDrawable, Color.parseColor("#EC1C24"))
               } else if (it.equals("EXPIRED",true)){
                   holder?.binding?.activeExpired?.text = it
                   val unwrappedDrawable = holder.binding.activeExpired.background
                   val wrappedDrawable = DrawableCompat.wrap(unwrappedDrawable)
                   DrawableCompat.setTint(wrappedDrawable, Color.rgb(205, 253, 223))
                   holder?.binding?.activeExpired?.text = "ACTIVE"
                   holder?.binding?.activeExpired?.setTextColor(
                       Color.rgb(
                           151 / 255,
                           37 / 255,
                           31 / 255
                       )
                   )
               } else if (it.equals("onTrial", ignoreCase = true)) {
                   val unwrappedDrawable = holder.binding.activeExpired.background
                   val wrappedDrawable = DrawableCompat.wrap(unwrappedDrawable)
                   DrawableCompat.setTint(wrappedDrawable, context?.getColor(R.color.orange)!!)
                   holder?.binding?.activeExpired?.text = "On Trial For"
               } else {

               }
           } ?: run {
               holder?.binding?.activeExpired?.text = ""
           }*/

        model.transactionID?.let {
            holder.binding.transactionId.text = it
        } ?: run {
            holder.binding.transactionId.text = ""
        }

        model.createdDate?.let { createDate ->
            holder.binding.paymentDate.text = getTimeOn(createDate)
        } ?: run {
            holder.binding.paymentDate.text = ""
        }


//        holder.binding.paymentModeImage.setImageDrawable(context?.let { ContextCompat.getDrawable(it,R.drawable.stripe)
//            })
//        Log.d("setImagePaymentMode", "true")


        model.paymentProvider?.let {
            when(it){
                "STRIPE" -> holder.binding?.paymentModeImage?.setImageResource(R.drawable.stripe)
                "XENDIT" -> holder.binding?.paymentModeImage?.setImageResource(R.drawable.xendit)
                "ROKU_IAP" -> holder.binding?.paymentModeImage?.setImageResource(R.drawable.roku)
                "RAZORPAY" -> holder.binding?.paymentModeImage?.setImageResource(R.drawable.razorpay)
                "PAYPAL" -> holder.binding?.paymentModeImage?.setImageResource(R.drawable.paypal)
                "GOOGLE_IAP" -> holder.binding?.paymentModeImage?.setImageResource(R.drawable.google_play)
                "DCB" -> holder.binding?.paymentModeImage?.setImageResource(R.drawable.ais)
                "APPLE_IAP" -> holder.binding?.paymentModeImage?.setImageResource(R.drawable.apple)
                "AMAZON_IAP" -> holder.binding?.paymentModeImage?.setImageResource(R.drawable.amazon)
                "TWO_C_TWO_P" -> holder.binding?.paymentModeImage?.setImageResource(R.drawable.two_c_2_p)
                else -> {}
            }
        }


        model.rentalExpiryDate?.let { expireDate ->
            model.createdDate?.let { createdDate ->
                holder?.binding?.paymentStatus?.text =
                    getTimeOn(createdDate) + " to " + getExpireTimeOn(expireDate)
            }
        }

        model.title?.let {
            holder.binding.textView.text = it
        } ?: run {
            holder.binding.textView.text = ""
        }
    }

    fun updateData(list: List<PurchaseModel>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

    fun getTimeOn(timestamp: Long): String {
        val sdf = SimpleDateFormat("d MMM, yyyy", Locale.ENGLISH)
        val date = Date(timestamp)
        return sdf.format(date)
    }

    fun getExpireTimeOn(timestamp: Long): String {
        val sdf = SimpleDateFormat("MMM d, yyyy", Locale.ENGLISH)
        val date = Date(timestamp)
        return sdf.format(date)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    class ViewHolder(val binding: OrderHistoryItemBinding) : RecyclerView.ViewHolder(binding.root)
}