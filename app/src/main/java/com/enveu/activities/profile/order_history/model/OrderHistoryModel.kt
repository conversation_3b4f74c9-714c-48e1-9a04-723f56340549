package com.enveu.activities.profile.order_history.model

import com.google.gson.annotations.SerializedName

data class OrderHistoryModel(
    var data: Data = Data(),
    var isSuccessful: Boolean = false,
    var responseCode: Int = 0,
    var debugMessage: String? = null  // Change to String? to handle null values
)

data class Data(
    var items: List<Item> = emptyList(),
    var pageSize: Int = 0,
    var totalElements: Int = 0,
    var pageNumber: Int = 0,
    var totalPages: Int = 0
)

data class Item(
    @SerializedName("id") var id: Int? = null,
    @SerializedName("offerIdentifier") var offerIdentifier: String? = null,
    @SerializedName("offerTitle") var offerTitle: String? = null,
    @SerializedName("orderId") var orderId: String? = null,
    @SerializedName("userId") var userId: String? = null,
    @SerializedName("orderStatus") var orderStatus: String? = null,
    @SerializedName("orderAmount") var orderAmount: Float? = null,
    @SerializedName("paidAmount") var paidAmount: Float? = null,
    @SerializedName("orderCurrency") var orderCurrency: String? = null,
    @SerializedName("voDOfferType") var voDOfferType: String? = null,
    @SerializedName("subscriptionOfferType") var subscriptionOfferType: String? = null,
    @SerializedName("rentalExpiryDate") var rentalExpiryDate: Long? = null,
    @SerializedName("createdDate") var createdDate: Long? = null,
    @SerializedName("subscriptionExpiryDate") var subscriptionExpiryDate: String? = null,
    @SerializedName("renewalDate") var renewalDate: String? = null,
    @SerializedName("contentDetails") var contentDetails: ArrayList<Any>? = null,  // Adjusted type to handle null values
    @SerializedName("paymentProvider") var paymentProvider: String? = null,
    @SerializedName("paymentId") var paymentId: String? = null,
    @SerializedName("entitlementState") var entitlementState: String? = null,
    @SerializedName("contentSKU") var contentSKU: String? = null,
    @SerializedName("paymentStatus") var paymentStatus: String? = null,
    @SerializedName("nextChargeDate") var nextChargeDate: Long? = null,
    @SerializedName("currentExpiry") var currentExpiry: Long? = null,
    @SerializedName("onTrial") var onTrial: Boolean? = null,
    @SerializedName("offerDetails") var offerDetails: OfferDetails? = null,

    // New fields from JSON
    @SerializedName("lastOrderId") var lastOrderId: String? = null,
    @SerializedName("carrierMode") var carrierMode: String? = null,
    @SerializedName("activationMode") var activationMode: String? = null,
    @SerializedName("cancellationInitiatedBy") var cancellationInitiatedBy: String? = null,
    @SerializedName("cancellationRequestedDate") var cancellationRequestedDate: String? = null,
    @SerializedName("subscriptionHoldStartDate") var subscriptionHoldStartDate: String? = null,
    @SerializedName("subscriptionHoldEndDate") var subscriptionHoldEndDate: String? = null,
    @SerializedName("subscriptionRecoverDate") var subscriptionRecoverDate: String? = null,
    @SerializedName("subscriptionRestoreDate") var subscriptionRestoreDate: String? = null,
    @SerializedName("gracePeriodStartDate") var gracePeriodStartDate: String? = null,
    @SerializedName("gracePeriodEndDate") var gracePeriodEndDate: String? = null,
    @SerializedName("previousOrderStatus") var previousOrderStatus: String? = null,
    @SerializedName("notes") var notes: Notes? = null,
    @SerializedName("onGracePeriod") var onGracePeriod: Boolean? = null,
    @SerializedName("onHoldPeriod") var onHoldPeriod: Boolean? = null,
    @SerializedName("restored") var restored: Boolean? = null,
    @SerializedName("recovered") var recovered: Boolean? = null
)

data class ContentDetails(
    var id: String? = null,
    var title: String? = null,
    var contentType: String? = null
)

data class OfferDetails(
    var offerPeriod: String? = null,
    var trialPeriod: TrialPeriod? = null
)

data class TrialPeriod(
    var trialType: String? = null,
    var trialDuration: Int? = null
)

// New data class for the 'notes' field
data class Notes(
    @SerializedName("nextChargeDate") var nextChargeDate: String? = null,
    @SerializedName("enveuSMSPlanName") var enveuSMSPlanName: String? = null,
    @SerializedName("trialType") var trialType: String? = null,
    @SerializedName("purchaseId") var purchaseId: String? = null,
    @SerializedName("enveuSMSSubscriptionOfferType") var enveuSMSSubscriptionOfferType: String? = null,
    @SerializedName("offerPurchaseOption") var offerPurchaseOption: String? = null,
    @SerializedName("entitlementStatus") var entitlementStatus: String? = null,
    @SerializedName("trialDuration") var trialDuration: String? = null,
    @SerializedName("isTrialAllowed") var isTrialAllowed: String? = null,
    @SerializedName("paymentProvider") var paymentProvider: String? = null,
    @SerializedName("enveuSMSPurchaseCurrency") var enveuSMSPurchaseCurrency: String? = null,
    @SerializedName("isMultiOrder") var isMultiOrder: String? = null,
    @SerializedName("paidAmount") var paidAmount: String? = null,
    @SerializedName("enveuSMSPlanTitle") var enveuSMSPlanTitle: String? = null
)
