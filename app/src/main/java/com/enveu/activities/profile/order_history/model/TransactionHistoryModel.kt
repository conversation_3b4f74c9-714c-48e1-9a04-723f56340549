package com.enveu.activities.profile.order_history.model


import com.google.gson.annotations.SerializedName

data class TransactionHistoryModel(
    @SerializedName("data")
    val `data`: Data?,
    @SerializedName("debugMessage")
    val debugMessage: Any?, // null
    @SerializedName("responseCode")
    val responseCode: Int? // 2000
) {
    data class Data(
        @SerializedName("items")
        val items: List<Item?>?,
        @SerializedName("pageNumber")
        val pageNumber: Int?, // 0
        @SerializedName("pageSize")
        val pageSize: Int?, // 20
        @SerializedName("totalElements")
        val totalElements: Int?, // 1
        @SerializedName("totalPages")
        val totalPages: Int? // 1
    ) {
        data class Item(
            @SerializedName("appUser")
            val appUser: Any?, // null
            @SerializedName("cancellationRequestedDate")
            val cancellationRequestedDate: Any?, // null
            @SerializedName("contentDetails")
            val contentDetails: Any?, // null
            @SerializedName("contentSKU")
            val contentSKU: Any?, // null
            @SerializedName("dunningExpiryDate")
            val dunningExpiryDate: Any?, // null
            @SerializedName("entitlementState")
            val entitlementState: String?, // ACTIVE
            @SerializedName("id")
            val id: Int?, // 84
            @SerializedName("lastOrderIdentifier")
            val lastOrderIdentifier: Any?, // null
            @SerializedName("offerIdentifier")
            val offerIdentifier: String?, // svod_multicurrency_plan
            @SerializedName("offerTitle")
            val offerTitle: String?, // MultiCurrency Plan 
            @SerializedName("orderIdentifier")
            val orderIdentifier: Any?, // null
            @SerializedName("purchaseDate")
            val purchaseDate: Long?, // 1713430953138
            @SerializedName("purchaseExpiryDate")
            val purchaseExpiryDate: Any? // null
        )
    }
}