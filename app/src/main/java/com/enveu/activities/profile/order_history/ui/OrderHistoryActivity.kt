package com.enveu.activities.profile.order_history.ui


import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.enveu.R
import com.enveu.activities.profile.order_history.adapter.OrderHistoryAdapter
import com.enveu.activities.profile.order_history.model.OrderHistoryModel
import com.enveu.activities.profile.order_history.model.TransactionHistoryModel
import com.enveu.activities.purchase.purchase_model.PurchaseModel
import com.enveu.activities.purchase.ui.VodOfferType
import com.enveu.activities.watchList.viewModel.WatchListViewModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.databinding.OrderHistoryActivityBinding
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.enveu.utils.stringsJson.converter.StringsHelper.stringParse

class OrderHistoryActivity : BaseBindingActivity<OrderHistoryActivityBinding?>() {
    private var token = ""
    private var viewModel: WatchListViewModel? = null
    private var visibleItemCount = 0
    private var totalItemCount = 0
    private var pastVisibleItems = 0
    private var mIsLoading = false
    private var counter = 0
    private var transactionHistoryData : TransactionHistoryModel?= null
    private var mScrollY = 0
    private var purchaseFinalList: MutableList<PurchaseModel> = ArrayList()
    var adapter: OrderHistoryAdapter? = null
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    override fun inflateBindingLayout(inflater: LayoutInflater): OrderHistoryActivityBinding {
        return OrderHistoryActivityBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parseColor()
        setUi()
        connectionValidation(NetworkConnectivity.isOnline(this))
    }

    private fun parseColor() {
        binding?.colorsData = colorsHelper
        binding?.stringData = stringsHelper
        binding?.toolbar?.colorsData = colorsHelper
        binding?.toolbar?.stringData = stringsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            AnalyticsUtils.trackScreenView(this, AppConstants.ORDER_HISTORY)
            binding!!.noConnectionLayout.visibility = View.GONE
            binding!!.orderHistoryRecyclerView.visibility = View.VISIBLE
            binding!!.progressBar.visibility = View.VISIBLE
            initialization()
            RecyclerAnimator(this@OrderHistoryActivity)
                .animate(binding!!.orderHistoryRecyclerView)
            (binding!!.orderHistoryRecyclerView.itemAnimator as SimpleItemAnimator?)!!.supportsChangeAnimations = false
            hitOrderHistoryAPI()
            setUpRecyclerView()
        } else {
            binding!!.noConnectionLayout.visibility = View.VISIBLE
            binding!!.orderHistoryRecyclerView.visibility = View.GONE
            binding!!.connection.retryTxt.setOnClickListener { connectionValidation(
                NetworkConnectivity.isOnline(this@OrderHistoryActivity)) }
        }
        setUi()
    }

    private fun setUi() {
        binding?.toolbar?.titleText?.visibility = View.VISIBLE
        binding?.toolbar?.logoMain2?.visibility = View.GONE
        binding!!.toolbar.backLayout.setOnClickListener { onBackPressed() }
        binding?.toolbar?.backArrow?.let { rotateImageLocaleWise(it) }
        binding!!.toolbar.titleSkip.visibility = View.GONE
        binding!!.toolbar.llSearchIcon.visibility = View.GONE
        binding!!.toolbar.screenText.text = stringParse(
            stringsHelper.instance()?.data?.config?.order_history_title.toString(),
            getString(R.string.order_history_title)
        )
    }

    private fun initialization() {
        val preference = KsPreferenceKeys.getInstance()
        token = preference.appPrefAccessToken
        viewModel = ViewModelProvider(this@OrderHistoryActivity)[WatchListViewModel::class.java]
    }

    private fun setUpRecyclerView() {
        binding?.orderHistoryRecyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val layoutManager = recyclerView.layoutManager as LinearLayoutManager?
                if (dy > 0) {
                    assert(layoutManager != null)
                    visibleItemCount = layoutManager!!.childCount
                    totalItemCount = layoutManager.itemCount
                    pastVisibleItems = layoutManager.findFirstVisibleItemPosition()
                    if (mIsLoading) {
                        if (visibleItemCount + pastVisibleItems >= totalItemCount) {
                            val adapterSize = binding!!.orderHistoryRecyclerView.adapter?.itemCount
                            if (adapterSize != null) {
                                if (adapterSize > 8) {
                                    mIsLoading = false
                                    counter++
                                    mScrollY += dy
                                    hitOrderHistoryAPI()
                                }
                            }
                        }
                    }
                }
            }
        })
    }

    private fun setRail(orderHistoryModel: OrderHistoryModel?) {
        binding?.progressBar?.visibility = View.GONE
        orderHistoryModel?.let { model ->
            if (model.data.items.isNotEmpty()) {
                mIsLoading = true
                val updatedPurchaseList = mutableListOf<PurchaseModel>()
                model.data.items.forEach { item ->
                    transactionHistoryData?.data?.items?.forEach { transactionItem ->
                        if (transactionItem?.orderIdentifier == item.orderId) {
                            val purchaseModel = PurchaseModel().apply {
                                title = item.offerTitle ?: ""
                                price = (item.orderAmount ?: "").toString()
                                trialType = ""
                                trialDuration = 0
                                subscriptionType = item.subscriptionOfferType.takeIf { !it.isNullOrEmpty() }
                                createdDate = item.createdDate
                                isOnTrial = item.onTrial
                                isSelected = item.entitlementState.equals("ACTIVE", ignoreCase = true)
                                transactionEntitlementState = transactionItem?.entitlementState
                                purchaseOptions = VodOfferType.RECURRING_SUBSCRIPTION.name
                                offerPeriod = VodOfferType.WEEKLY.name
                                identifier = item.offerIdentifier ?: ""
                                paymentProvider = item.paymentProvider ?: ""
                                transactionID = item.orderId ?: ""
                                orderStatus = item.orderStatus ?: ""
                                rentalExpiryDate = item.rentalExpiryDate?:0L
                                currency = item.orderCurrency ?: ""
                                currentExpiryDate = item.currentExpiry ?: 0
                                nextChargeDate = item.nextChargeDate ?: 0
                                offerPeriod = item.offerDetails?.offerPeriod ?: ""
                                item.offerDetails?.trialPeriod?.let { trial ->
                                    trialType = trial.trialType ?: ""
                                    trialDuration = trial.trialDuration ?: 0
                                }
                            }
                            updatedPurchaseList.add(purchaseModel)
                        }
                    }
                }
                Log.d("updatedPurchaseList", "setRail: "+updatedPurchaseList.size)
                if (updatedPurchaseList.size>0) {
                    runOnUiThread {
                        binding!!.noDataTittle.visibility = View.GONE
                        binding!!.noDataDescription.visibility = View.GONE
                    }
                    if (adapter == null) {
                        adapter = transactionHistoryData?.let {
                            OrderHistoryAdapter(updatedPurchaseList, currentLanguage,
                                it
                            )
                        }
                        binding?.orderHistoryRecyclerView?.adapter = adapter
                    } else {
                        adapter?.updateData(updatedPurchaseList)
                    }
                } else {
                    noDataFound()
                }
                binding?.orderHistoryRecyclerView?.scrollToPosition(mScrollY)
            } else {
                noDataFound()
            }
        }
    }
    private fun hitOrderHistoryAPI() {
        viewModel?.getOrderHistory(token, counter.toString(), "100")?.observe(this) { orderHistoryModel: OrderHistoryModel? ->
            Log.d( "hitOrderHistoryAPI ","Response: ${orderHistoryModel}")
            if (!orderHistoryModel?.data?.items.isNullOrEmpty()) {
                Log.d( "hitOrderHistoryAPI: ","${orderHistoryModel}")
                getTransactionDetails(token, counter.toString(), AppConstants.PAGE_SIZE.toString(),orderHistoryModel)
            } else {
                noDataFound()
            }
        }
    }

    private fun noDataFound() {
        runOnUiThread {
            binding!!.progressBar.visibility = View.GONE
            binding!!.noDataTittle.visibility = View.VISIBLE
            binding!!.noDataDescription.visibility = View.VISIBLE
        }
    }


    private fun getTransactionDetails(token: String, page: String, size: String,orderHistoryModel: OrderHistoryModel?) {
        viewModel?.getTransactionHistory(token, page, "100")?.observe(this) { transactionHistoryModel: TransactionHistoryModel? ->
            if (transactionHistoryModel?.data != null) {
                this.transactionHistoryData = transactionHistoryModel
                setRail(orderHistoryModel)
            } else {
                noDataFound()
            }
        }
    }
}