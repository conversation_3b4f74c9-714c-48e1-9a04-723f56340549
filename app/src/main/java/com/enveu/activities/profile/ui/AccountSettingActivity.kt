package com.enveu.activities.profile.ui


import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.purchase.plans_layer.GetPlansLayer
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.ChangePasswordActivity
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.AppUserModel
import com.enveu.callbacks.commonCallbacks.MoreItemClickListener
import com.enveu.databinding.AccountSettingActivityBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstance
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstanceWithCancelBtn
import com.enveu.fragments.more.adapter.AccountListAdapter
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.StringUtils
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import java.util.Locale

class AccountSettingActivity : BaseBindingActivity<AccountSettingActivityBinding?>(), CommonDialogFragment.EditDialogListener,
    MoreItemClickListener {
    private var mPreference: KsPreferenceKeys? = null
    private var isLogin: String? = null
    private var mListLogin: MutableList<String>? = null
    private var token = ""
    private var userState = ""
    private var hasEntitlement = false
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    private var editProfile= ""
    private var changePassword = ""
    private var logout= ""
    private var featureModel: FeatureFlagModel? = null
    private var stringList: ArrayList<String>? = null
    override fun inflateBindingLayout(inflater: LayoutInflater): AccountSettingActivityBinding {
        return AccountSettingActivityBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AnalyticsUtils.trackScreenView(this,AppConstants.SETTINGS)
        binding!!.colorsData = ColorsHelper
        binding!!.stringData = stringsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
        binding!!.toolbar.colorsData = colorsHelper
        getAppLevelJsonData()
        setToolbar()
        userState = KsPreferenceKeys.getInstance().appPrefLoginStatus
        Logger.e("userState", userState)
        mPreference = KsPreferenceKeys.getInstance()
        setUI()
        callGetPlans()
    }

    private fun getAppLevelJsonData() {
        featureModel = AppConfigMethod.parseFeatureFlagList()
    }

    private fun setToolbar() {
        binding!!.toolbar.logoMain2.visibility = View.GONE
        binding!!.toolbar.searchIcon.visibility = View.GONE
        binding!!.toolbar.backLayout.visibility = View.VISIBLE
        binding!!.toolbar.titleMid.visibility = View.VISIBLE
        val account = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_account.toString(),
            getString(R.string.more_account)
        )
        binding!!.toolbar.titleMid.text = account
        binding!!.toolbar.titleMid.setBackgroundResource(0)
        binding?.toolbar?.backArrow?.let { rotateImageLocaleWise(it) }
        binding!!.toolbar.backLayout.setOnClickListener { onBackPressed() }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        userState = KsPreferenceKeys.getInstance().appPrefLoginStatus
        Logger.e("userState", userState)
        mPreference = KsPreferenceKeys.getInstance()
        setUI()
        callGetPlans()
    }

    private fun setUI() {
        token = KsPreferenceKeys.getInstance().appPrefAccessToken
        isLogin = mPreference!!.appPrefLoginStatus
        if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            val tempResponse = KsPreferenceKeys.getInstance().appPrefUser
            if (!StringUtils.isNullOrEmptyOrZero(tempResponse)) {
                setVerify()
            } else {
                val tempResponseApi = mPreference!!.appPrefProfile
                setVerifyApi(tempResponseApi)
            }
        }
    }

    private fun callGetPlans() {
        binding!!.progressBar.visibility = View.VISIBLE
        GetPlansLayer.getInstance().getEntitlementStatus(
            KsPreferenceKeys.getInstance(), token) { entitlementStatus: Boolean, apiStatus: Boolean, _:String, onHold : Boolean,responseCode: Int ->
            hasEntitlement = if (apiStatus) {
                entitlementStatus
            } else {
                if (responseCode == 403) {
                    hitApiLogout(this, token)
                    mPreference?.let { clearCredientials(it) }
                    ActivityLauncher.getInstance().homeActivity(this, HomeActivity::class.java)
                }
                false
            }
            modelCall(hasEntitlement)
        }
    }

    private fun modelCall(hasEntitlement: Boolean) {
        binding!!.progressBar.visibility = View.GONE
//        val label1 = this.resources.getStringArray(R.array.account_option)

         editProfile = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.account_edit_profile.toString(),
            getString(R.string.account_edit_profile)
        )
        changePassword = stringsHelper.stringParse(
             stringsHelper.instance()?.data?.config?.account_change_password.toString(),
             getString(R.string.account_change_password)
         )
         logout =stringsHelper.stringParse(
             stringsHelper.instance()?.data?.config?.account_logout.toString(),
             getString(R.string.account_logout)
         )

        if (stringList == null) {
            stringList = ArrayList()
        }

        val label1 = arrayOf(editProfile, changePassword, logout)
        stringList!!.addAll(label1)

        if (!featureModel!!.featureFlag.EDIT_PROFILE) {
            stringList!!.remove(editProfile)
        }
        if (!featureModel!!.featureFlag.CHANGE_PASSWORD) {
            stringList!!.remove(changePassword)
        }

        if (!featureModel!!.featureFlag.USER_LOGOUT) {
            stringList!!.remove(logout)
        }

        val accountOptions: List<String> = ArrayList(stringList!!)
        mListLogin = ArrayList()
        mListLogin?.addAll(listOf(*label1))
        mPreference = KsPreferenceKeys.getInstance()
        setUIComponets(accountOptions, false)
    }

    private fun setVerifyApi(tempResponse: String?) {
        if (!StringUtils.isNullOrEmptyOrZero(tempResponse)) {
            binding!!.usernameTv.visibility = View.VISIBLE
            val userName = mPreference!!.appPrefUserName
            if (userName != null && !userName.equals("", ignoreCase = true) && !userName.equals("null", ignoreCase = true)) {
                setNameOrEmail(userName)
            }
        }
    }

    private fun setVerify() {
        val tempResponse = mPreference!!.appPrefUser
        if (!StringUtils.isNullOrEmptyOrZero(tempResponse)) {
            val dataModel = Gson().fromJson(tempResponse, AppUserModel::class.java)
            if (dataModel != null) {
                val userName = dataModel.name
                if (userName != null && !userName.equals("", ignoreCase = true) && !userName.equals("null", ignoreCase = true)) {
                    setNameOrEmail(userName)
                }
            }
        }
    }

    private fun setNameOrEmail(userName: String?) {
        binding!!.usernameTv.text = userName
        var value = ""
        try {
            if (userName != null) {
                if (userName != "") {
                    value = userName.trim { it <= ' ' }.replace("\\s+".toRegex(), " ")
                    if (value.contains(" ")) {
                        val words = value.split(" ".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                        if (words.isNotEmpty()) {
                            val firstWord = words[0][0].toString().uppercase(Locale.getDefault())
                            if (words.size == 1) {
                                value = firstWord
                                binding!!.profileText.text = value
                            } else {
                                val secondWord = words[1][0].toString().uppercase(Locale.getDefault())
                                value = firstWord + secondWord
                                binding!!.profileText.text = value
                            }
                        }
                    } else {
                        value = userName[0].toString().uppercase(Locale.getDefault()) + "" + userName[1].toString().uppercase(Locale.getDefault())
                        binding!!.profileText.text = value
                    }
                }
            }
        } catch (e: Exception) {
            binding!!.profileText.text = value
        }
    }

    private fun setUIComponets(mList: List<String>, isLogin: Boolean) {
        val adapter = AccountListAdapter(this, mList, this, isLogin)
        binding!!.moreFragmentsRv.hasFixedSize()
        binding!!.moreFragmentsRv.isNestedScrollingEnabled = false
        binding!!.moreFragmentsRv.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding!!.moreFragmentsRv.adapter = adapter
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }
    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancel : String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstanceWithCancelBtn(title, description, actionBtn, cancel)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun onActionBtnClicked() {
        if (CheckInternetConnection.isOnline(this)) {
            hitApiLogout(this, token)
            clearCredientials(mPreference)
            ActivityLauncher.getInstance().homeScreen(this, HomeActivity::class.java,false,"","", 0)
        }
    }

    override fun onCancelBtnClicked() {

    }

    override fun onClick(caption: String) {
        val loginStatus = KsPreferenceKeys.getInstance().appPrefLoginStatus.equals(
            AppConstants.UserStatus.Login.toString(), ignoreCase = true)
        if (caption == editProfile) {
            if (loginStatus) {
                ActivityLauncher.getInstance().ProfileActivityNew(this, ProfileActivityNew::class.java)
            } else {
                ActivityLauncher.getInstance().loginActivity(this, ActivityLogin::class.java,"")
            }
        } else if (caption == changePassword) {
            if (loginStatus) {
                ActivityLauncher.getInstance().changePassword(this, ChangePasswordActivity::class.java)
            } else {
                ActivityLauncher.getInstance().loginActivity(this, ActivityLogin::class.java,"")
            }
        } else if (caption == logout) {
            commonDialogWithCancel(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_logout.toString(),
                    getString(R.string.logout_confirmation)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_logout_you_want_to_logout.toString(),
                    getString(R.string.popup_logout_you_want_to_logout)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_cancel.toString(),getString(R.string.popup_cancel)
                )
            )
        }
    }
}