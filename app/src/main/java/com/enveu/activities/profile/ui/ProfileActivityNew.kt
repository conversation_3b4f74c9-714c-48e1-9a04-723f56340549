package com.enveu.activities.profile.ui

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.app.DatePickerDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.text.format.DateFormat
import android.util.Patterns
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.amazonaws.ClientConfiguration
import com.amazonaws.auth.CognitoCachingCredentialsProvider
import com.amazonaws.mobileconnectors.s3.transferutility.TransferListener
import com.amazonaws.mobileconnectors.s3.transferutility.TransferObserver
import com.amazonaws.mobileconnectors.s3.transferutility.TransferState
import com.amazonaws.mobileconnectors.s3.transferutility.TransferUtility
import com.amazonaws.regions.Region
import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3Client
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.profile.CountryListActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.AppUserModel
import com.enveu.beanModel.userProfile.UserProfileResponse
import com.enveu.databinding.ProfileActivityNewBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.EditDialogListener
import com.enveu.utils.Constants
import com.enveu.utils.FileUtil
import com.enveu.utils.Logger
import com.enveu.utils.ObjectHelper
import com.enveu.utils.colorsJson.converter.AppColors
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.StringUtils
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.moengage.core.internal.utils.showToast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.text.DecimalFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.Objects


class ProfileActivityNew : BaseBindingActivity<ProfileActivityNewBinding?>(), EditDialogListener {
    private var s3: AmazonS3Client? = null
    private var imageUrlId = ""
    private var countryName = ""
    private var mPreference: KsPreferenceKeys? = null
    private var via = "Gallery"
    private var contentPreference = ""
    private var preference: KsPreferenceKeys? = null
    private var dateMilliseconds = ""
    private val encodePin = ""
    private var parentalPin = ""
    private var parentalPinEnabled:Boolean = false
    private var isLogin: String? = null
    private var isloggedout = false
    private var transferUtility: TransferUtility? = null
    private var isDeletionRequired = false
    private var isInvalidResponseCode = false
    private var isUpdateRecord: Boolean = false
    private var featureModel: FeatureFlagModel? = null
    private var viewModel: RegistrationLoginViewModel? = null
    private var saved: List<String>? = null
    private val regex: Regex = Regex("[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}")
    private val SECOND_ACTIVITY_REQUEST_CODE = 0
    private val colorsHelper by lazy { ColorsHelper }
    private val stringsHelper by lazy { StringsHelper }
    private var selectedArtistId=""
    private var isEditable : Boolean = false
    private var appUserInterestList:ArrayList<Int>?= ArrayList()
    companion object {
        private val REQUEST_CODE_PICK_IMAGE = 1001
        private val REQUEST_CODE_PERMISSION = 2001
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): ProfileActivityNewBinding {
        return ProfileActivityNewBinding.inflate(inflater)
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mPreference = KsPreferenceKeys.getInstance()
        binding?.colorsData = colorsHelper
        binding?.stringData = stringsHelper
        binding?.toolbar?.colorsData = colorsHelper
        binding?.toolbar?.stringData = stringsHelper
        binding?.connection?.colorsData = ColorsHelper
        binding?.connection?.stringData = stringsHelper
        binding?.toolbar?.backArrow?.let { rotateImageLocaleWise(it) }
        getAppLevelJsonData()
        credentialsProvider()
        setTransferUtility()
        inItView()
        connectionObserver()
        isLogin = KsPreferenceKeys.getInstance().appPrefLoginStatus
        modelcall()
        binding?.ivProfilePic?.setOnClickListener {
            openGallery()
        }

        val fullImageUrl = preference?.isActiveUserProfileData?.profilePicURL
//        binding?.ivProfilePic?.setUserImageWithGlide(this, fullImageUrl)
        binding?.let {
            Glide.with(this)
                .load(fullImageUrl)
                .placeholder(R.drawable.profile_avtar_logo)
                .error(R.drawable.profile_avtar_logo)
                .into(it.ivProfilePic)
        }
    }

    private fun credentialsProvider() {
        val credentialsProvider = CognitoCachingCredentialsProvider(applicationContext, SDKConfig.IDENTITY_POOL_ID, SDKConfig.REGION)
        setAmazonS3Client(credentialsProvider)
    }
    private fun setAmazonS3Client(credentialsProvider: CognitoCachingCredentialsProvider) {
        val clientConfiguration = ClientConfiguration()
        clientConfiguration.maxErrorRetry = 10
        clientConfiguration.connectionTimeout = 50000 // default is 10 secs
        clientConfiguration.socketTimeout = 50000
        clientConfiguration.maxConnections = 500
        s3 = AmazonS3Client(credentialsProvider, clientConfiguration)
        s3?.setRegion(Region.getRegion(SDKConfig.REGION))
    }


    private fun getAppLevelJsonData() {
        featureModel = AppConfigMethod.parseFeatureFlagList()

//        if (featureModel!!.featureFlag.DELETE_ACCOUNT) {
//            binding?.tvDeleteAccount!!.visibility = View.VISIBLE
//        } else {
//            binding?.tvDeleteAccount!!.visibility = View.INVISIBLE
//        }

        isEditable = featureModel!!.featureFlag.EDIT_PROFILE_EMAIL && mPreference?.isPrimaryAccountUser == true

        binding!!.userEMail.isEnabled = false
        binding!!.userEMail.visibility = if (mPreference?.isPrimaryAccountUser == false) View.GONE else View.VISIBLE
        binding!!.uniqueUsername.isEnabled = featureModel!!.featureFlag.EDIT_PROFILE_UNIQUE_USER_NAME
        binding!!.userName.isEnabled = featureModel!!.featureFlag.EDIT_PROFILE_USERNAME
        binding!!.EtCity.isEnabled = featureModel!!.featureFlag.EDIT_CITY_NAME
        binding!!.userMobile.isEnabled = featureModel!!.featureFlag.EDIT_PHONE_NUMBER

        if (featureModel!!.featureFlag.IS_EDIT_COUNTRY) {
            binding!!.countryId.visibility = View.VISIBLE
        } else {
            binding!!.countryId.visibility = View.GONE
        }

        if (featureModel?.featureFlag?.LAST_NAME == true){
            binding?.lastName?.visibility = View.VISIBLE
        }else{
            binding?.lastName?.visibility = View.GONE
        }

        if (featureModel!!.featureFlag.EDIT_DOB) {
            binding!!.dOB.visibility = View.VISIBLE
        } else {
            binding!!.dOB.visibility = View.GONE
        }

        if (featureModel!!.featureFlag.IS_EDIT_CITY) {
            binding!!.EtCity.visibility = View.VISIBLE
        } else {
            binding!!.EtCity.visibility = View.GONE
        }

        if (featureModel!!.featureFlag.IS_EDIT_PHONE_NUMBER) {
            binding!!.userMobile.visibility = View.VISIBLE
        } else {
            binding!!.userMobile.visibility = View.GONE
        }

    }

    private fun parseColor() {
       // binding?.titleLayout?.background = colorsHelper.strokeBgDrawable(AppColors.appBgColor(), AppColors.profileImageBorderColor(), 200f)
        binding?.userName?.background = ColorsHelper.strokeBgDrawable(AppColors.tphBgColor(), AppColors.editTextBorderColor(), 30f)
        binding?.userEMail?.background = ColorsHelper.strokeBgDrawable(AppColors.tphBgColor(), AppColors.editTextBorderColor(), 30f)
        binding?.countryId?.background = ColorsHelper.strokeBgDrawable(AppColors.tphBgColor(), AppColors.editTextBorderColor(), 30f)
        binding?.userMobile?.background = ColorsHelper.strokeBgDrawable(AppColors.tphBgColor(), AppColors.editTextBorderColor(), 30f)
        binding?.EtCity?.background = ColorsHelper.strokeBgDrawable(AppColors.tphBgColor(), AppColors.editTextBorderColor(), 30f)
        binding?.dOB?.background = ColorsHelper.strokeBgDrawable(AppColors.tphBgColor(), AppColors.editTextBorderColor(), 30f)
        ColorsHelper.textViewDrawableColor(binding!!.dOB, AppColors.dobIconColor())
        ColorsHelper.textViewDrawableColor(binding!!.countryId, AppColors.dropDownIconColor())
    }

    private fun modelcall() {
        if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            val tempResponse = KsPreferenceKeys.getInstance().appPrefUser
            setNameOrEmail(KsPreferenceKeys.getInstance().userProfileData)
            if (!StringUtils.isNullOrEmptyOrZero(tempResponse)) {
                setVerify()
            } else {
                val tempResponseApi: String = KsPreferenceKeys.getInstance().appPrefProfile
                setVerifyApi(tempResponseApi)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun inItView() {

        binding?.editImage?.setOnClickListener {
            openGallery()
        }

        binding?.tvDeleteAccount?.setOnClickListener {
            if (KsPreferenceKeys.getInstance().deleteAccountRequestStatus) {
                isDeletionRequired = false
                commonDialog(
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_under_review.toString(),
                        getString(R.string.popup_under_review))
                    ,
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_your_previous_account_already_review.toString(),
                        getString(R.string.popup_your_previous_account_already_review))
                    ,
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                        getString(R.string.popup_continue)))
            } else {
                isDeletionRequired = true
                commonDialogWithCancel(
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_delete_account.toString(),
                        getString(R.string.popup_delete_account)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_want_to_delete_account.toString(),
                        getString(R.string.popup_want_to_delete_account)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                        getString(R.string.popup_continue)),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                        getString(R.string.popup_cancel)))
            }
        }

        binding?.toolbar?.titleSkip?.visibility = View.GONE
        binding?.toolbar?.titleMid?.visibility = View.VISIBLE
        binding?.toolbar?.logoMain2?.visibility = View.GONE
        binding?.toolbar?.llSearchIcon?.visibility = View.GONE

        val myProfile = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.profile_my_profile.toString(),
            getString(R.string.profile_my_profile)
        )
        binding?.toolbar?.titleMid?.text = myProfile
        binding?.toolbar?.backLayout?.visibility = View.VISIBLE
        binding?.toolbar?.backLayout?.setOnClickListener { view -> onBackPressed() }
        binding?.llLogin?.setOnClickListener{
            hideSoftKeyboard(binding?.llLogin)
            if (CheckInternetConnection.isOnline(this@ProfileActivityNew)) {
                if(emptyEmail() && validateEmail() && validateFirstName() && validateLastName() && validateMobileNumber() && validateDate()){
                    callUpdateApi()
                }
            }else{
                commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                        getString(R.string.popup_no_internet_connection_found)
                    ), "", stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
            }
        }

        binding?.gender?.setOnClickListener {
            val genderOptions = if (KsPreferenceKeys.getInstance().appLanguage == AppConstants.LANGUAGE_ARABIC){
                arrayOf("ذكر", "أنثى", "بدلا من ذلك لا أقول")
            }else{
                arrayOf("Male", "Female", "Rather Not Say")
            }
            val builder = AlertDialog.Builder(this)
            builder.setTitle(R.string.select_gender)
            builder.setItems(genderOptions) { _, which ->
                binding?.gender?.text = genderOptions[which]
            }
            builder.show()
        }

        binding?.dOB?.setOnClickListener(View.OnClickListener {
            val mcurrentDate = Calendar.getInstance()
            val mYear = mcurrentDate[Calendar.YEAR]
            val mMonth = mcurrentDate[Calendar.MONTH]
            val mDay = mcurrentDate[Calendar.DAY_OF_MONTH]
            val maxDate = Calendar.getInstance()
            maxDate.add(Calendar.YEAR,-12)

            val mDatePicker = DatePickerDialog(
                this@ProfileActivityNew,
                { datepicker, selectedyear, selectedmonth, selectedday ->
                    mcurrentDate[Calendar.YEAR] = selectedyear
                    mcurrentDate[Calendar.MONTH] = selectedmonth
                    mcurrentDate[Calendar.DAY_OF_MONTH] = selectedday
                    val difference = mYear - selectedyear
                    if (difference >= 12) {
                        val sdf = SimpleDateFormat("dd-MM-yyyy ", Locale.getDefault())
                        binding?.dOB?.text = sdf.format(mcurrentDate.time)
                        try {
                            val d = sdf.parse(binding?.dOB?.text.toString())
                            dateMilliseconds = d.time.toString()
                        } catch (e: ParseException) {
                            Logger.w(e)
                        }
                    } else {
                        binding?.dOB?.text = ""
                        dateMilliseconds = ""
                        Toast.makeText(this, stringsHelper.instance()?.data?.config?.popup_date_difference, Toast.LENGTH_SHORT).show()
                    }
                }, mYear, mMonth, mDay
            )
            mDatePicker.datePicker.maxDate = maxDate.timeInMillis
            mDatePicker.updateDate(maxDate.get(Calendar.YEAR), maxDate.get(Calendar.MONTH), maxDate.get(Calendar.DAY_OF_MONTH))
            mDatePicker.show()
        })


        binding?.countryId?.setOnClickListener{
            val intent = Intent(this, CountryListActivity::class.java)
            startActivityForResult(intent, SECOND_ACTIVITY_REQUEST_CODE)
        }

    }

//    private fun openGallery() {
////        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
////        intent.type = "image/*"
////        startActivityForResult(intent, PICK_IMAGE_REQUEST)
//
//        ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE), 1)
//
//        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
//        startActivityForResult(intent, PICK_IMAGE_REQUEST)
//    }


    @SuppressLint("IntentReset")
   private fun openGallery() {
        val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }
        if (ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED) {
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            intent.type = "image/*"
            startActivityForResult(intent, REQUEST_CODE_PICK_IMAGE)
        } else {
            ActivityCompat.requestPermissions(this, arrayOf(permission), REQUEST_CODE_PERMISSION)
        }
    }

    private fun deleteAccount(){
        if (NetworkConnectivity.isOnline(this@ProfileActivityNew)) {
            showLoading(binding!!.progressBar, true)
            val token = preference!!.appPrefAccessToken
            viewModel = ViewModelProvider(this@ProfileActivityNew).get(RegistrationLoginViewModel::class.java)
            viewModel!!.deleteAccount(token)
                .observe(this@ProfileActivityNew) { deleteAccountResponse ->
                    dismissLoading(binding!!.progressBar)
                    binding!!.progressBar.visibility = View.GONE
                    if (deleteAccountResponse != null) {
                        if (deleteAccountResponse.responseCode == 2001) {
                            Toast.makeText(this@ProfileActivityNew, getString(R.string.account_deletion_success_msg), Toast.LENGTH_SHORT).show()
                            KsPreferenceKeys.getInstance().deleteAccountRequestStatus = true
                            connectionValidation(true)
                        } else {
                            isInvalidResponseCode = true;
                            if (deleteAccountResponse.responseCode == 4906) {
                                commonDialog(
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_delete_account.toString(),
                                        getString(R.string.popup_delete_account)
                                    ),
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_your_previous_account_already_review.toString(),
                                        getString(R.string.popup_your_previous_account_already_review))
                                    ,
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue))
                                )
                            } else {
                                commonDialog(
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                        getString(R.string.popup_error)
                                    ), stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                        getString(R.string.something_went_wrong)
                                    ), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue))
                                )
                            }
                        }
                    }
                }
        } else {
            Toast.makeText(
                this@ProfileActivityNew,
                getString(R.string.no_internet_connection),
                Toast.LENGTH_SHORT
            ).show()
        }
    }


    private fun setVerifyApi(tempResponseApi: String?) {
        if (!StringUtils.isNullOrEmptyOrZero(tempResponseApi)) {
//            binding?.usernameTv?.visibility = View.VISIBLE
            val userName = KsPreferenceKeys.getInstance().appPrefUserName
            if (userName != null && !userName.equals("", ignoreCase = true) && !userName.equals("null", ignoreCase = true)) {
                setNameOrEmail(userName)
            }
        }


    }

    private fun setNameOrEmail(userName: String) {
        binding?.usernameTv?.text = userName
       // binding?.profileText != null

        var value = ""
        try {
            if (userName != null) {
                if (userName != "") {
                    value = userName.trim { it <= ' ' }.replace("\\s+".toRegex(), " ")
                    if (value.contains(" ")) {
                        val words: Array<String> = value.split(" ").toTypedArray()
                        if (words.isNotEmpty()) {
                            val firstWord = words[0][0].toString().uppercase(Locale.getDefault())
                            if (words.size == 1) {
                                value = firstWord
                                binding?.profileText?.text = value
                            } else {
                                val secondWord = words[1][0].toString().uppercase(Locale.getDefault())
                                value = firstWord + secondWord
                                binding?.profileText?.text = value

                            }
                        }
                    } else {
                        value = userName[0].toString().uppercase(Locale.getDefault()) + "" + userName[1].toString().uppercase(Locale.getDefault())
                        binding?.profileText?.text = value

                    }
                }
            }
        } catch (e: java.lang.Exception) {
            binding?.profileText?.text = value
        }
    }

    private fun setVerify() {
        val tempResponse = mPreference?.appPrefUser
        if (!StringUtils.isNullOrEmptyOrZero(tempResponse)) {
            val dataModel = Gson().fromJson(tempResponse, AppUserModel::class.java)
            if (dataModel != null) {
                val userName = dataModel.name
                if (userName != null && !userName.equals("", ignoreCase = true) && !userName.equals("null", ignoreCase = true)) {
                    setNameOrEmail(userName)
                }
            }
        }

    }
    private fun callUpdateApi() {
        showLoading(binding?.progressBar, true)
        val token = KsPreferenceKeys.getInstance().appPrefAccessToken
        val customParam = JsonObject()
        val dataParam = JsonObject()

        if (preference?.isPrimaryAccountUser == true){
            dataParam.addProperty("email", binding?.userEMail?.text.toString())
        }
        dataParam.addProperty("name", binding?.userName?.text.toString())
        dataParam.addProperty("userName", binding?.uniqueUsername?.text.toString())
        dataParam.addProperty("dateOfBirth", dateMilliseconds)
        dataParam.addProperty("gender", binding?.gender?.text?.toString()?:"")
        dataParam.addProperty("bio", binding?.addBio?.text.toString())
        dataParam.addProperty("profilePicURL", imageUrlId)
        val interestArray = com.google.gson.JsonArray()
        appUserInterestList?.forEach {
            interestArray.add(it)
        }
        dataParam.add("appUserInterest", interestArray)
        customParam.addProperty("parentalPin", parentalPinEnabled)
        dataParam.add("customData", customParam)

        viewModel?.hitUpdateProfileApi(this, token, dataParam)?.observe(this) {
            if (it != null) {
                if (it.status) {
                    dismissLoading(binding?.progressBar)
                    val gson = Gson()
                    val userProfileData = gson.toJson(it)
                    KsPreferenceKeys.getInstance().userProfileData = userProfileData
                    isUpdateRecord=true
                    Logger.e("userdata",userProfileData)
                    Toast.makeText(this, "Update SuccessFully", Toast.LENGTH_SHORT).show()
                    updateUI(it)
                    val resultIntent = Intent().apply {
                        putExtra(AppConstants.UPDATE_NAME, it.data.name)
                        putExtra(AppConstants.UPDATE_PROFILE, imageUrlId)
                        putExtra(AppConstants.UPDATE_BIO, it.data.bio)
                    }
                    setResult(Activity.RESULT_OK, resultIntent)
                    finish()
                }else{
                    if(it.responseCode==4302){
                        isloggedout = true
                        dismissLoading(binding?.progressBar)
                        logoutCall()
                        ActivityLauncher.getInstance().goToLogin(this@ProfileActivityNew, ActivityLogin::class.java)
                        try {
                            runOnUiThread { onBackPressed() }
                        } catch (e: java.lang.Exception) {
                            Logger.d(e.toString())
                        }
                    }else if(it.responseCode==4019){
                        commonDialog(it.debugMessage.toString(),"",stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                    }else{
                        if (it.debugMessage!=null){
                            commonDialog(it.debugMessage.toString(),"",stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                        }else{
                            Toast.makeText(this, "Something went wrong", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            }else{
                commonDialog(stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_error.toString(), getString(R.string.popup_error)
                ), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(), getString(R.string.something_went_wrong)
                ),stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))

            }

        }
    }

    private var newObject: UserProfileResponse? = null
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray,
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }


    override fun onStart() {
        super.onStart()
        AppCommonMethod.Url = ""
        AppCommonMethod.UriId = ""
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }
    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstanceWithCancelBtn(title, description,actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }
    override fun onActionBtnClicked() {
        if (isDeletionRequired){
            deleteAccount()
        }
        if(isUpdateRecord){
//            ActivityLauncher.getInstance().homeScreen(this, HomeActivity::class.java,false,"","",0)
            onBackPressed()
        }
    }

    override fun onCancelBtnClicked() {

    }

    private fun logoutCall() {
        if (CheckInternetConnection.isOnline(Objects.requireNonNull(this))) {
            clearCredientials(preference)
            hitApiLogout(this, KsPreferenceKeys.getInstance().appPrefAccessToken)
        }else{
            commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                        getString(R.string.popup_no_internet_connection_found)
                    ), "", stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))

        }
    }

    private fun updateUI(it: UserProfileResponse) {
        try {
            var emailId = ""
            if (ObjectHelper.isNotEmpty(it.data.email)) {
                emailId = it.data.email as String
            }
            var phoneNumber = ""
            if (ObjectHelper.isNotEmpty(it.data.phoneNumber)) {
                phoneNumber = it.data.phoneNumber as String
            }
            var dob = 0.0
            if (ObjectHelper.isNotEmpty(it.data.dateOfBirth)) {
                dob = it.data.dateOfBirth as Double
            }
            imageUrlId = it?.data?.profilePicURL.toString()
            parentalPin = it?.data?.customData?.parentalPin.toString()
            parentalPinEnabled = it?.data?.customData?.parentalPinEnabled.toBoolean()
            AnalyticsUtils.trackUserAttributes(this, it?.data?.id.toString(), it?.data?.name, it?.data?.email.toString())
//            setUserProperties(applicationContext, it.data.id, it.data.name, emailId, phoneNumber,dob.toLong())
            binding?.userEMail?.setText(it?.data?.email.toString())
            binding?.userName?.setText(it?.data?.name)
            binding?.uniqueUsername?.setText(it?.data?.userName)
            if (it.data.dateOfBirth !=null && it.data.dateOfBirth != 0.0) {
                val df = DecimalFormat("#")
                df.maximumFractionDigits = 0
                val l = df.format(it.data.dateOfBirth).toLong()
                dateMilliseconds = l.toString()
                val dateString = DateFormat.format("dd-MM-yyyy", Date(l)).toString()
                binding?.dOB?.text = dateString
            }
            if (it.data.gender != null){
                binding?.gender?.text = it.data.gender.toString()
            }
            binding?.addBio?.setText(it.data.bio.toString())
            KsPreferenceKeys.getInstance().appPrefUserName = (it?.data?.name?.toString())

            if (featureModel?.featureFlag?.LAST_NAME == true){
                binding?.lastName?.setText(it.data?.customData?.lastName?: preference?.appPrefUserLastName)
            }
            if (it.data.customData.country !=null) {
                binding?.countryId?.text = it.data.customData.country
            }

            if (it.data.customData.mobileNumber != null) {
                binding?.userMobile?.setText(
                    it.data.customData.mobileNumber as String
                )
            }
            if(!it.data.lastName.isNullOrEmpty()) {
                binding?.lastName?.setText(it.data?.lastName.toString())
            }
            binding?.userName?.text?.length?.let { it -> binding?.userName?.setSelection(it) }
            binding?.userEMail?.isEnabled=false
            binding?.userEMail?.isFocusable = false
            if (it.data.customData.mobileNumber != null) {
                binding?.userMobile?.setText(it.data.customData.mobileNumber.toString() + "")
                binding?.userMobile?.isEnabled = true
                binding?.userMobile?.isFocusable = true
            } else {
                binding?.userMobile?.isEnabled = true
                binding?.userMobile?.isFocusable = true
            }

            if (it.data.customData != null) {
                if (it.data.customData.city != null
                ) binding?.EtCity?.setText(
                    it.data.customData.city
                )
            }
            KsPreferenceKeys.getInstance().appPrefUserProfilePic = (it.data?.profilePicURL.toString())
            KsPreferenceKeys.getInstance().appPrefUserLastName = (it.data?.customData?.lastName)
            KsPreferenceKeys.getInstance().appPrefMobileNumber = (it.data.customData.mobileNumber.toString())

        } catch (e: java.lang.Exception) {
            Logger.w(e)
        }
    }

    private fun connectionObserver() {
        if (NetworkConnectivity.isOnline(this@ProfileActivityNew)) {
            AnalyticsUtils.trackScreenView(this,AppConstants.VIEW_PROFILE)
            preference = KsPreferenceKeys.getInstance()
            binding?.connection?.noConnectionLayout?.visibility = View.GONE
            binding?.updateProfileLayout?.visibility = View.VISIBLE
            connectionValidation(true)
        } else {
            noConnectionLayout()
            connectionValidation(false)
        }
    }

    private fun connectionValidation(connected : Boolean) {
        if (connected) {
            viewModel = ViewModelProvider(this@ProfileActivityNew)[RegistrationLoginViewModel::class.java]
            val preference = KsPreferenceKeys.getInstance()
            val authToken = preference.appPrefAccessToken
            showLoading(binding?.progressBar,true)
            viewModel?.hitUserProfile(this@ProfileActivityNew,authToken)?.observe(this) {
                if (it != null) {
                    if (it.status) {
                        Logger.e("profileRes", it.toString())
                        if (it.data.deletionRequestStatus != null) {
                            val deletionRequestStatus: String = it.data.deletionRequestStatus
                            if (deletionRequestStatus.equals("UNDER_REVIEW")) {
                                KsPreferenceKeys.getInstance().deleteAccountRequestStatus = true
                            } else {
                                KsPreferenceKeys.getInstance().deleteAccountRequestStatus = false
                            }
                        }
                        selectedArtistId= it.data.customData.sponsoredArtist?:""
                        if (it.data.appUserInterest != null && it.data.appUserInterest.isNotEmpty()) {
                            appUserInterestList = it.data.appUserInterest
                        }
                        KsPreferenceKeys.getInstance().sponsorArtistId = selectedArtistId
                        val gson = Gson()
                        val userProfileData = gson.toJson(it)
                        Logger.e("userdata1",userProfileData)
                        updateUI(it)
                        dismissLoading(binding?.progressBar)
                    }
                    if (it.responseCode == 4302) {
                        isloggedout = true
                        logoutCall()
                        ActivityLauncher.getInstance().goToLogin(this@ProfileActivityNew, ActivityLogin::class.java)

                        try {
                            runOnUiThread { onBackPressed() }
                        } catch (_: java.lang.Exception) {
                        }
                    } else if (it.responseCode == 4019) {
                        commonDialog(stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ), it.debugMessage.toString(), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                    } else if (it.responseCode == 4901) {
                        commonDialog(stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ), it.debugMessage.toString(), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                    }
                }else{
                    Toast.makeText(this, stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(), Toast.LENGTH_SHORT).show()
                }
            }

        }else{
            commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                        getString(R.string.popup_no_internet_connection_found)
                    ), "", stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
        }
    }

    fun noConnectionLayout() {
        binding?.updateProfileLayout?.visibility = View.GONE
        binding?.connection?.noConnectionLayout?.visibility = View.VISIBLE
        binding?.connection?.retryTxt?.setOnClickListener { view: View? -> connectionObserver() }
    }


    private fun emptyEmail(): Boolean {
        var check = false
        if (preference?.isPrimaryAccountUser == true){
            if (StringUtils.isNullOrEmptyOrZero(binding?.userEMail?.text.toString().trim())) {
                Toast.makeText(this, stringsHelper.instance()?.data?.config?.popup_empty_email_subtitle.toString(), Toast.LENGTH_SHORT).show()
            } else {
                check = true
            }
        } else {
            check = true
        }
        return check
    }

    private fun validateFirstName(): Boolean {
        var check = false
        if (featureModel?.featureFlag!!.IS_FIRST_NAME_MANDATORY) {
            if (StringUtils.isNullOrEmptyOrZero(binding?.userName?.text.toString().trim())) {
                showToast(this, resources.getString(R.string.popup_empty_first_name_subtitle))
//                Toast.makeText(this, stringsHelper.instance()?.data?.config?.popup_empty_first_name_subtitle.toString(), Toast.LENGTH_SHORT).show()
            } else {
                check = true
            }
        } else {
            check = true
        }

        return check
    }

    private fun validateLastName(): Boolean {
        var check = false
        if (featureModel?.featureFlag!!.IS_LAST_NAME_MANDATORY) {
            if (StringUtils.isNullOrEmptyOrZero(binding?.lastName?.text.toString().trim())) {
                Toast.makeText(this, stringsHelper.instance()?.data?.config?.popup_empty_last_name_subtitle.toString(), Toast.LENGTH_SHORT).show()
            } else {
                check = true
            }
        } else {
            check = true
        }

        return check
    }

    private fun validateDate(): Boolean {
        return if (featureModel!!.featureFlag.EDIT_DOB) {
            var check = false
            if (StringUtils.isNullOrEmptyOrZero(binding?.dOB?.text.toString().trim())) {
                Toast.makeText(this, applicationContext.resources.getString(R.string.please_enter_a_valid_date), Toast.LENGTH_SHORT).show()
            } else {
                check = true
            }
            check
        } else {
            true
        }

    }
    private fun validateEmail(): Boolean {
        var check = false
        if (preference?.isPrimaryAccountUser == true){
            if (binding?.userEMail?.text.toString().trim().matches(regex)) {
                check = true
            } else {
                Toast.makeText(this, stringsHelper.instance()?.data?.config?.popup_empty_email_subtitle.toString(), Toast.LENGTH_SHORT).show()
            }
        } else {
            check = true
        }
        return check
    }

    private fun validateMobileNumber():Boolean {
        var check = false
        if (binding?.userMobile?.text!!.isEmpty()) {
            // return true because number is optional
             check = true
        } else {
            // Check if the mobile number matches a valid pattern
            if (!Patterns.PHONE.matcher(binding?.userMobile?.text.toString().trim()).matches()) {
                // Show an error message or handle the invalid input as desired
                Toast.makeText(this, stringsHelper.instance()?.data?.config?.popup_invalid_mobile_subtitle.toString(), Toast.LENGTH_SHORT).show()
            } else {
                check = true
            }
        }
     return check
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == SECOND_ACTIVITY_REQUEST_CODE) {
            if (resultCode == RESULT_OK) {
                countryName = data?.getStringExtra("countryName").toString()
                binding?.countryId?.text = countryName
            }
        }
        if (requestCode == REQUEST_CODE_PICK_IMAGE  && resultCode == RESULT_OK && data != null) {
            val imageUri: Uri? = data.data
            if (imageUri != null) {
                Glide.with(this)
                    .load(imageUri)
                    .into(binding?.ivProfilePic!!)
                    try {
                        val filePathFromURI: String = FileUtil.getFilePathFromImageURI(this, imageUri)
                        val imageFilePath = File(filePathFromURI)
                        showLoading(binding!!.progressBar, true)
                        lifecycleScope.launch(Dispatchers.IO) {
                            try {
                                val compressedFile = FileUtil.compressImageFile(imageFilePath, Constants.COMPRESS_IMAGE_SIZE, Constants.COMPRESS_IMAGE_WIDTH, Constants.COMPRESS_IMAGE_HEIGHT)
                                withContext(Dispatchers.Main) {
                                    setFileToUpload(compressedFile)
                                }
                            } catch (e: Exception) {
                                withContext(Dispatchers.Main) {
                                    showLoading(binding!!.progressBar, false)
                                }
                                Logger.w(e)
                            }
                        }

                    } catch (ex: Exception) {
                        showLoading(binding!!.progressBar, false)
                        Logger.w(ex)
                    }
                } else {
                    Logger.w("unable to get the image")
                }
        }
    }

    private fun transferObserverListener(transferObserver: TransferObserver) {
        transferObserver.setTransferListener(object : TransferListener {
            override fun onStateChanged(id: Int, state: TransferState) {
                if (state == TransferState.COMPLETED) {
                    dismissLoading(binding!!.progressBar)
                }
                else if(state == TransferState.FAILED){
                    dismissLoading(binding!!.progressBar)
                }
            }

            override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {
                Logger.e("error", "Progress - ${bytesCurrent},${bytesTotal}");
            }

            override fun onError(id: Int, ex: java.lang.Exception) {
                 Logger.e("error", "error");
            }
        })
    }

    private fun setTransferUtility() {
        transferUtility = TransferUtility.builder().s3Client(s3).context(applicationContext).build()
    }

    private fun getCurrentTimeStamp(): Long {
        return System.currentTimeMillis() / 1000
    }

    private fun setFileToUpload(fileToUpload: File) {
        val imageToUpload = "Thumbnail_" + getCurrentTimeStamp() + "_Android" + ".jpg"
        imageUrlId = AppCommonMethod.getSdkConfigUrl() + imageToUpload
        via = "Gallery"
        KsPreferenceKeys.getInstance().saveVia(via)
        val transferObserver = transferUtility?.upload(
            SDKConfig.BUCKET_ID, "${SDKConfig.CLOUD_PATH_IMAGE_URL}${imageToUpload}",
            fileToUpload
        )

        if (transferObserver != null) {
            transferObserverListener(transferObserver)
        }
        showLoading(binding!!.progressBar, false)
    }

}