package com.enveu.activities.search.adapter


import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.search.adapter.RowSearchAdapter.RowSearchListener
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.SearchClickCallbacks
import com.enveu.databinding.RowSearchCategoryBinding
import com.enveu.fragments.search.adapter.SearchAdapter
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod.Companion.launchDetailScreen
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.stringsJson.converter.StringsHelper


class CategoriedSearchAdapter(
    private val context: Context,
    private val list: MutableList<RailCommonData>?,
    private val featureList: FeatureFlagModel?,
    private val listener: SearchClickCallbacks,
    private val searchString: String
) : RecyclerView.Adapter<RecyclerView.ViewHolder>(),
    RowSearchListener {
    private val stringsHelper by lazy { StringsHelper }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val binding: RowSearchCategoryBinding = DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.row_search_category, parent, false
        )
        Logger.d("ViewType :$viewType")
        return VideoTypeViewHolder(binding)
    }

    override fun onBindViewHolder(viewHolder: RecyclerView.ViewHolder, pos: Int) {
        val singleSectionItems = list?.get(pos)?.enveuVideoItemBeans
        val totalCount = list?.get(pos)?.totalCount
        val itemListDataAdapter1 = RowSearchAdapter(
                context,
                singleSectionItems,
                true,
                this,
                totalCount!!,
                featureList
            )
        val videoTypeViewHolder = viewHolder as VideoTypeViewHolder
        setRecyclerProperties(videoTypeViewHolder.binding.recyclerView)
        viewHolder.binding.tvTitle.setTextColor(context.getColor(R.color.series_detail_description_text_color))
        viewHolder.binding.colorsData = ColorsHelper
        viewHolder.binding.stringData = StringsHelper

        val seeAll = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.search_see_all.toString(),
            context.getString(R.string.search_see_all)
        )
        val searchResults = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.search_results.toString(),
            context.getString(R.string.search_results)
        )
        videoTypeViewHolder.binding.showAll.text = seeAll
        videoTypeViewHolder.binding.recyclerView.adapter = itemListDataAdapter1
        var header = ""
        val movies = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.search_result.toString(),
            context.getString(R.string.search_result)
        )
        header = movies

        videoTypeViewHolder.binding.tvTitle.text = "$header $totalCount"

        if (list?.get(pos)?.totalCount!! < 4) {
            videoTypeViewHolder.binding.showAllSearch.visibility = View.GONE
        }
        val finalHeader = header
        videoTypeViewHolder.binding.showAllSearch.setOnClickListener { view: View? ->
            callResultActivity(
                list[pos],
                finalHeader
            )
        }
    }

    private fun callResultActivity(model: RailCommonData, header: String) {
        listener.onShowAllItemClicked(model, header)
    }

    override fun getItemViewType(position: Int): Int {
        return list?.get(position)?.layoutType!!
    }

    private fun setRecyclerProperties(recyclerView: RecyclerView) {
        val gridItem = if (context.resources.getBoolean(R.bool.isTablet)) {
            4
        } else {
            if (featureList?.featureFlag?.IS_SQUARE_DEFAULT == true) {
                3
            } else {
                2
            }
        }
        recyclerView.isNestedScrollingEnabled = false
        recyclerView.setHasFixedSize(true)
        recyclerView.layoutManager = GridLayoutManager(context, gridItem)
    }

    override fun getItemCount(): Int {
        return list?.size!!
    }

    internal inner class VideoTypeViewHolder(val binding: RowSearchCategoryBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onRowItemClicked(itemValue: EnveuVideoItemBean, position: Int) {
        AnalyticsUtils.logSearchContentSelectEvent(context,itemValue.id.toString(),itemValue.title,itemValue.contentType,searchString)
        val assetType = itemValue.assetType
        var mediType: String? = ""
        if (itemValue.assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
            mediType = itemValue.videoDetails.videoType
        } else if (itemValue.assetType.equals(AppConstants.CUSTOM, ignoreCase = true)) {
            mediType = itemValue.customType
        } else if (itemValue.assetType.equals(
                AppConstants.AUDIO,
                ignoreCase = true
            ) || itemValue.assetType.equals(AppConstants.PERSON, ignoreCase = true)
        ) {
            mediType = itemValue.contentType
        }
        assetType?.let {
            launchDetailScreen(
                context,
                it,
                itemValue.id,
                itemValue.sku,
                mediType,
                itemValue.title ?: "",
                itemValue.externalRefId ?: "",
                itemValue.posterURL ?: "",
                0,
                itemValue.contentSlug ?: "",
                itemValue
            )

        }
    }
}