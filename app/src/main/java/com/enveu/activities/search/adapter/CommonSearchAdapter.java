package com.enveu.activities.search.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.enveu.R;
import com.enveu.appLevelModel.FeatureFlagModel;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModel.popularSearch.ItemsItem;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.callbacks.commonCallbacks.SearchClickCallbacks;
import com.enveu.databinding.CommonSearchAdapterBinding;
import com.enveu.databinding.CommonSearchSquareAdapterBinding;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.helpers.ImageHelper;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;

import java.util.List;

public class CommonSearchAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private final Context context;
    private final RailCommonData jsonObject;
    private final KsPreferenceKeys preference;
    private final SearchClickCallbacks listener;

    private final FeatureFlagModel featureList;


    public CommonSearchAdapter(Context context, RailCommonData jsonObject, SearchClickCallbacks listener,FeatureFlagModel featureList) {
        this.context = context;
        this.jsonObject = jsonObject;
        preference = KsPreferenceKeys.getInstance();
        this.listener = listener;
        this.featureList = featureList;
    }


    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        if (featureList.getFeatureFlag().getIS_SQUARE_DEFAULT()){
            CommonSearchSquareAdapterBinding itemBinding;
            itemBinding = DataBindingUtil.inflate(
                    LayoutInflater.from(viewGroup.getContext()),
                    R.layout.common_search_square_adapter, viewGroup, false);
            itemBinding.setColorsData(ColorsHelper.INSTANCE);

            return new SquareItemRowHolder(itemBinding);
        }else {
            CommonSearchAdapterBinding itemBinding;
            itemBinding = DataBindingUtil.inflate(
                    LayoutInflater.from(viewGroup.getContext()),
                    R.layout.common_search_adapter, viewGroup, false);
            itemBinding.setColorsData(ColorsHelper.INSTANCE);

            return new SingleItemRowHolder(itemBinding);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, final int position) {
        if (viewHolder instanceof SingleItemRowHolder) {
            setLandscapeData(((SingleItemRowHolder) viewHolder), position);
        } else if (viewHolder instanceof SquareItemRowHolder) {
            setSquareData(((SquareItemRowHolder) viewHolder), position);
        }


    }

    private void setLandscapeData(SingleItemRowHolder viewHolder, int position) {

        List<EnveuVideoItemBean> itemList = jsonObject.getEnveuVideoItemBeans();
        viewHolder.searchItemBinding.tvTitle.setTextColor(context.getColor(R.color.series_detail_all_episode_txt_color));
        viewHolder.searchItemBinding.tvDescription.setTextColor(context.getColor(R.color.unselected_indicator_color));

        try {
            if (itemList.get(position).getPosterURL().equalsIgnoreCase("")) {
                viewHolder.searchItemBinding.imageTitle.setVisibility(View.VISIBLE);
                viewHolder.searchItemBinding.imageTitle.setText(itemList.get(position).getTitle());
                viewHolder.searchItemBinding.imageTitle.bringToFront();
            } else {
                viewHolder.searchItemBinding.imageTitle.setVisibility(View.GONE);
            }
        } catch (Exception ignored) {

        }

        if (itemList.size() > 0) {
            viewHolder.searchItemBinding.setPlaylistItem(itemList.get(position));
            if (featureList.getFeatureFlag().getSEARCH_TITTLE() && itemList.get(position).getTitle() != null && !itemList.get(position).getTitle().equalsIgnoreCase("")) {
                viewHolder.searchItemBinding.tvTitle.setText(itemList.get(position).getTitle());
            } else {
                viewHolder.searchItemBinding.tvTitle.setText("");
                viewHolder.searchItemBinding.tvTitle.setVisibility(View.INVISIBLE);
            }

            if (featureList.getFeatureFlag().getSEARCH_DESCRIPTION() && itemList.get(position).getDescription() != null && !itemList.get(position).getDescription().equalsIgnoreCase("")) {
                viewHolder.searchItemBinding.tvDescription.setText(itemList.get(position).getDescription());
            } else {
                viewHolder.searchItemBinding.tvDescription.setText("");
                viewHolder.searchItemBinding.tvDescription.setVisibility(View.INVISIBLE);
            }
            if (itemList.get(position).getPosterURL()!=null && !itemList.get(position).getPosterURL().equalsIgnoreCase("")){
                ImageHelper.getInstance(context).loadListImage(viewHolder.searchItemBinding.itemImage, AppCommonMethod.getListLDSImage(itemList.get(position).getPosterURL(),context));
            }
            viewHolder.searchItemBinding.rippleView.setOnClickListener(view -> listener.onEnveuItemClicked(itemList.get(position)));
        }
    }

    private void setSquareData(SquareItemRowHolder viewHolder, int position) {
        List<EnveuVideoItemBean> itemList = jsonObject.getEnveuVideoItemBeans();
        viewHolder.searchItemBinding.tvTitle.setTextColor(context.getColor(R.color.series_detail_all_episode_txt_color));
        viewHolder.searchItemBinding.tvDescription.setTextColor(context.getColor(R.color.unselected_indicator_color));

        try {
            if (itemList.get(position).getPosterURL().equalsIgnoreCase("")) {
                viewHolder.searchItemBinding.imageTitle.setVisibility(View.VISIBLE);
                viewHolder.searchItemBinding.imageTitle.setText(itemList.get(position).getTitle());
                viewHolder.searchItemBinding.imageTitle.bringToFront();
            } else {
                viewHolder.searchItemBinding.imageTitle.setVisibility(View.GONE);
            }
        } catch (Exception ignored) {

        }

        if (itemList.size() > 0) {
            viewHolder.searchItemBinding.setPlaylistItem(itemList.get(position));
            if (featureList.getFeatureFlag().getSEARCH_TITTLE() && itemList.get(position).getTitle() != null && !itemList.get(position).getTitle().equalsIgnoreCase("")) {
                viewHolder.searchItemBinding.tvTitle.setText(itemList.get(position).getTitle());
            } else {
                viewHolder.searchItemBinding.tvTitle.setText("");
                viewHolder.searchItemBinding.tvTitle.setVisibility(View.INVISIBLE);
            }

            if (featureList.getFeatureFlag().getSEARCH_DESCRIPTION() && itemList.get(position).getDescription() != null && !itemList.get(position).getDescription().equalsIgnoreCase("")) {
                viewHolder.searchItemBinding.tvDescription.setText(itemList.get(position).getDescription());
            } else {
                viewHolder.searchItemBinding.tvDescription.setText("");
                viewHolder.searchItemBinding.tvDescription.setVisibility(View.INVISIBLE);
            }
            if (itemList.get(position).getPosterURL()!=null && !itemList.get(position).getPosterURL().equalsIgnoreCase("")){
                ImageHelper.getInstance(context).loadListImage(viewHolder.searchItemBinding.itemImage, itemList.get(position).getPosterURL());
            }
            viewHolder.searchItemBinding.rippleView.setOnClickListener(view -> listener.onEnveuItemClicked(itemList.get(position)));
        }
    }

    @Override
    public int getItemCount() {
        return jsonObject.getEnveuVideoItemBeans().size();
    }

    public interface CommonSearchListener {
        void onItemClicked(ItemsItem itemValue);

        default void onEnveuItemClick(EnveuVideoItemBean itemValue) {

        }
    }

    public class SingleItemRowHolder extends RecyclerView.ViewHolder {

        final CommonSearchAdapterBinding searchItemBinding;

        SingleItemRowHolder(CommonSearchAdapterBinding binding) {
            super(binding.getRoot());
            this.searchItemBinding = binding;
        }

    }

    public class SquareItemRowHolder extends RecyclerView.ViewHolder {

        final CommonSearchSquareAdapterBinding searchItemBinding;

        public SquareItemRowHolder(CommonSearchSquareAdapterBinding binding) {
            super(binding.getRoot());
            this.searchItemBinding = binding;
        }
    }

}

