package com.enveu.activities.search.adapter;

import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.enveu.R;
import com.enveu.beanModel.KeywordList;
import com.enveu.beanModelV3.searchHistory.SearchHistory;
import com.enveu.databinding.ItemNewRecentSearchBinding;
import com.enveu.databinding.KeywordItemBinding;
import com.enveu.utils.Logger;
import com.enveu.utils.colorsJson.converter.ColorsHelper;

import java.util.List;

;

@SuppressWarnings("ALL")
public class RecentListAdapter extends RecyclerView.Adapter<RecentListAdapter.KeywordItemHolder> {
    private final KeywordItemHolderListener listener;
    private List<SearchHistory.Data.Item> list;

    private List<KeywordList> keyword;
    private int viewType=0;
    int listLimit = 5;
    private GradientDrawable itemBg;


    public RecentListAdapter(Context context,List<SearchHistory.Data.Item> list,List keyword, KeywordItemHolderListener listener) {

        this.list = list;
        this.listener = listener;
        this.keyword = keyword;
    }
    public RecentListAdapter(Context context,List<SearchHistory.Data.Item> list,List keyword, KeywordItemHolderListener listener,int viewType,GradientDrawable itemBg) {
        this.list = list;
        this.listener = listener;
        this.keyword = keyword;
        this.viewType=viewType;
        this.itemBg=itemBg;
    }

    public void notifyKeywordAdapter(List searchedKeywords) {
        this.list = searchedKeywords;
        notifyDataSetChanged();
        // Logger.d( "here is list" + this.list);
    }
    @Override
    public int getItemViewType(int position) {
        return viewType;
    }

    @NonNull
    @Override
    public KeywordItemHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        if (i == 1) {
            ItemNewRecentSearchBinding itemNewRecentSearchBinding=DataBindingUtil.inflate(
                    LayoutInflater.from(viewGroup.getContext()),
                    R.layout.item_new_recent_search,viewGroup,false);
            itemNewRecentSearchBinding.setColorsData(ColorsHelper.INSTANCE);

            return new KeywordItemHolder(null,itemNewRecentSearchBinding);
        }else {
            KeywordItemBinding keywordItemBinding = DataBindingUtil.inflate(
                    LayoutInflater.from(viewGroup.getContext()),
                    R.layout.keyword_item, viewGroup, false);
            keywordItemBinding.setColorsData(ColorsHelper.INSTANCE);

            return new KeywordItemHolder(keywordItemBinding);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull KeywordItemHolder viewHolder, int i) {
        try {
            final int pos = i;
            if (list != null){
                if (viewHolder.keywordItemBinding != null) {
                    viewHolder.keywordItemBinding.setRowItem(list.get(i));
                    viewHolder.keywordItemBinding.rootView.setOnClickListener(view -> listener.onItemClicked(list.get(pos)));
                } else if (viewHolder.itemNewRecentSearchBinding !=null && viewType==1) {
                    viewHolder.itemNewRecentSearchBinding.rootView.setBackground(itemBg);
                    viewHolder.itemNewRecentSearchBinding.setRowItem(list.get(i));
                    viewHolder.itemNewRecentSearchBinding.rootView.setOnClickListener(view -> listener.onItemClicked(list.get(pos)));
                }
            }else{
                if (keyword != null){
                    if (viewHolder.keywordItemBinding != null) {
                        viewHolder.keywordItemBinding.setRowItemLocal(keyword.get(i));
                        viewHolder.keywordItemBinding.rootView.setOnClickListener(view -> listener.onItemClickedByLocal(keyword.get(pos)));
                    } else if (viewHolder.itemNewRecentSearchBinding !=null && viewType==1) {
                        viewHolder.itemNewRecentSearchBinding.rootView.setBackground(itemBg);
                        viewHolder.itemNewRecentSearchBinding.setRowItemLocal(keyword.get(i));
                        viewHolder.itemNewRecentSearchBinding.rootView.setOnClickListener(view -> listener.onItemClickedByLocal(keyword.get(pos)));
                    }
                }
            }

            //Logger.d( "SearchValue" + list.get(i).getKeywords());
        } catch (Exception ex) {
            Logger.e("RecentListAdpater", "" + ex.toString());
        }

    }

    @Override
    public int getItemCount() {
        if (list != null && list.size() < listLimit)
            return list.size();
        else if (keyword != null && keyword.size() < listLimit) {
            return keyword.size();
        } else return listLimit;
    }
    public void setListLimit(int listLimit) {
        this.listLimit = listLimit;
    }

    public interface KeywordItemHolderListener {
        void onItemClicked(SearchHistory.Data.Item itemValue);
        void onItemClickedByLocal(KeywordList itemValue);

    }

    public class KeywordItemHolder extends RecyclerView.ViewHolder {

        final KeywordItemBinding keywordItemBinding ;

        ItemNewRecentSearchBinding itemNewRecentSearchBinding;

        public KeywordItemHolder(@NonNull KeywordItemBinding binding) {
            super(binding.getRoot());
            this.keywordItemBinding = binding;
        }
        public KeywordItemHolder(KeywordItemBinding keywordItemBinding, @NonNull ItemNewRecentSearchBinding binding) {
            super(binding.getRoot());
            this.keywordItemBinding = keywordItemBinding;
            this.itemNewRecentSearchBinding = binding;
        }

    }
}

