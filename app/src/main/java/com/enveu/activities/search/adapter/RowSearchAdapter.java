package com.enveu.activities.search.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.enveu.R;
import com.enveu.activities.listing.listadapter.ListAdapter;
import com.enveu.appLevelModel.FeatureFlagModel;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.databinding.CommonSearchAdapterBinding;
import com.enveu.databinding.CommonSearchSquareAdapterBinding;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.helpers.ImageHelper;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;

import java.util.List;

public class RowSearchAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private static final int ITEM = 0;
    private static final int LOADING = 1;
    private final Context context;
    private final KsPreferenceKeys preference;
    private final List<EnveuVideoItemBean> list;
    private final RowSearchListener listener;
    private final int limitView = 5;
    private int totalCount = 0;
    private FeatureFlagModel featureList;


    public RowSearchAdapter(Context context, List<EnveuVideoItemBean> list, boolean isLimit, RowSearchListener listener, int totalCount, FeatureFlagModel featureList) {
        this.context = context;
        this.list = list;
        this.totalCount = totalCount;
        this.listener = listener;
        this.featureList = featureList;
        preference = KsPreferenceKeys.getInstance();
    }

    @Override
    public int getItemViewType(int position) {
        boolean isLoadingAdded = false;
        return (position == list.size() - 1 && isLoadingAdded) ? LOADING : ITEM;
    }

    public void notifyAdapter(List<EnveuVideoItemBean> list) {
        this.list.addAll(list);
        notifyDataSetChanged();
    }



    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {

        if (featureList.getFeatureFlag().getIS_SQUARE_DEFAULT()){
            CommonSearchSquareAdapterBinding itemBinding;
            itemBinding = DataBindingUtil.inflate(
                    LayoutInflater.from(viewGroup.getContext()),
                    R.layout.common_search_square_adapter, viewGroup, false);
            itemBinding.setColorsData(ColorsHelper.INSTANCE);

            return new SquareItemRowHolder(itemBinding);
        }else {
            CommonSearchAdapterBinding itemBinding;
            itemBinding = DataBindingUtil.inflate(
                    LayoutInflater.from(viewGroup.getContext()),
                    R.layout.common_search_adapter, viewGroup, false);
            itemBinding.setColorsData(ColorsHelper.INSTANCE);

            return new SingleItemRowHolder(itemBinding);
        }

    }



    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, final int position) {
       if (viewHolder instanceof SingleItemRowHolder) {
           setLandscapeData(((SingleItemRowHolder) viewHolder), position);
       } else if (viewHolder instanceof SquareItemRowHolder) {
           setSquareData(((SquareItemRowHolder) viewHolder), position);
       }


    }

    private void setSquareData(SquareItemRowHolder viewHolder, int position) {
        if (featureList.getFeatureFlag().getSEARCH_TITTLE() && list.get(position).getTitle() != null && !list.get(position).getTitle().equalsIgnoreCase("")) {
            viewHolder.itemBinding.tvTitle.setText(list.get(position).getTitle());
        } else {
            viewHolder.itemBinding.tvTitle.setText("");
            viewHolder.itemBinding.tvTitle.setVisibility(View.INVISIBLE);
        }

        if (featureList.getFeatureFlag().getSEARCH_DESCRIPTION() && list.get(position).getDescription() != null && !list.get(position).getDescription().equalsIgnoreCase("")) {
            viewHolder.itemBinding.tvDescription.setText(list.get(position).getDescription());
        } else {
            viewHolder.itemBinding.tvDescription.setText("");
            viewHolder.itemBinding.tvDescription.setVisibility(View.INVISIBLE);
        }
        viewHolder.itemBinding.rippleView.setOnClickListener(view -> listener.onRowItemClicked(list.get(position), position));

        if (list.get(position) != null) {
            viewHolder.itemBinding.setPlaylistItem(list.get(position));
        }
        if (list.get(position).getPosterURL()!=null) {
            ImageHelper.getInstance(context).loadListImage(viewHolder.itemBinding.itemImage, AppCommonMethod.getListLDSImage(list.get(position).getPosterURL(), context));
        }
    }

    private void setLandscapeData(SingleItemRowHolder viewHolder, int position) {

        if (featureList.getFeatureFlag().getSEARCH_TITTLE() && list.get(position).getTitle() != null && !list.get(position).getTitle().equalsIgnoreCase("")) {
            viewHolder.itemBinding.tvTitle.setText(list.get(position).getTitle());
        } else {
            viewHolder.itemBinding.tvTitle.setText("");
            viewHolder.itemBinding.tvTitle.setVisibility(View.INVISIBLE);
        }

        if (featureList.getFeatureFlag().getSEARCH_DESCRIPTION() && list.get(position).getDescription() != null && !list.get(position).getDescription().equalsIgnoreCase("")) {
            viewHolder.itemBinding.tvDescription.setText(list.get(position).getDescription());
        } else {
            viewHolder.itemBinding.tvDescription.setText("");
            viewHolder.itemBinding.tvDescription.setVisibility(View.INVISIBLE);
        }
        viewHolder.itemBinding.rippleView.setOnClickListener(view -> listener.onRowItemClicked(list.get(position), position));

        if (list.get(position) != null) {
            viewHolder.itemBinding.setPlaylistItem(list.get(position));
        }
        if (list.get(position).getPosterURL()!=null) {
            ImageHelper.getInstance(context).loadListImage(viewHolder.itemBinding.itemImage, AppCommonMethod.getListLDSImage(list.get(position).getPosterURL(), context));
        }
    }


    @Override
    public int getItemCount() {
        return list.size();
    }

    public interface RowSearchListener {
        void onRowItemClicked(EnveuVideoItemBean itemValue, int position);
    }

    public class SingleItemRowHolder extends RecyclerView.ViewHolder {

        final CommonSearchAdapterBinding itemBinding;

        public SingleItemRowHolder(CommonSearchAdapterBinding binding) {
            super(binding.getRoot());
            this.itemBinding = binding;
        }
    }

    public class SquareItemRowHolder extends RecyclerView.ViewHolder {

        final CommonSearchSquareAdapterBinding itemBinding;

        public SquareItemRowHolder(CommonSearchSquareAdapterBinding binding) {
            super(binding.getRoot());
            this.itemBinding = binding;
        }
    }

}


