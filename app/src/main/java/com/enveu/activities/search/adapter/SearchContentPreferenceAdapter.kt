package com.enveu.activities.search.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.enveu.R
import com.enveu.appLevelModel.MediaInfo
import com.enveu.databinding.ItemGenreBinding

class SearchContentPreferenceAdapter(private var checkboxList: MutableList<MediaInfo?>? = null , private var listener: SortedByFilterListener) : RecyclerView.Adapter<SearchContentPreferenceAdapter.GenreViewHolder>() {
    private var selectedPosition = 0
    var mediaMapingHash:HashMap<String,String>?=null
    class GenreViewHolder(val binding: ItemGenreBinding) : ViewHolder(binding.root) {
        val text = binding.text
    }
    init {
        if (checkboxList?.get(0)?.mediaType.equals("All", ignoreCase = true)) {
            checkboxList?.removeAt(0)
        }
        checkboxList?.add(0, MediaInfo("All", "All"))
    }
    constructor(checkboxList: MutableList<MediaInfo?>?, mediaMapingHash:HashMap<String,String>,listener: SortedByFilterListener) : this(checkboxList,listener) {
        this.mediaMapingHash=mediaMapingHash
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GenreViewHolder {
        return GenreViewHolder(ItemGenreBinding.inflate(LayoutInflater.from(parent.context),parent,false))
    }

    override fun getItemCount(): Int {
        return checkboxList?.size!!
    }

    override fun onBindViewHolder(holder: GenreViewHolder, @SuppressLint("RecyclerView") position: Int) {
        val mediaInfo = checkboxList?.get(position)
        if (mediaInfo?.mediaType.equals("All")){
            holder.text.text = mediaInfo?.mediaType?.toLowerCase()?.capitalize()
        }else {
            holder.text.text = mediaMapingHash?.get(mediaInfo?.mediaType)
        }

        if (position == selectedPosition) {
            holder.text.setTextColor(holder.itemView.context.resources.getColor(R.color.white))
            holder.text.setBackgroundResource(R.drawable.rounded_background_search_selected)
        }else{
            holder.text.setTextColor(holder.itemView.context.resources.getColor(R.color.white))
            holder.text.setBackgroundResource(R.drawable.rounded_background_search)
        }

        holder.itemView.setOnClickListener {
            val previousPosition = selectedPosition
            selectedPosition = position
            notifyItemChanged(previousPosition)
            notifyItemChanged(selectedPosition)
            listener.filterSort(mediaInfo?.mediaType!!)
        }
    }

    fun setFilter(mediaInfo: String){
        checkboxList?.forEach {
            if (it?.mediaType.equals(mediaInfo,true)){
                selectedPosition = checkboxList?.indexOf(it)!!
            }
        }
        notifyDataSetChanged()
    }
}

interface SortedByFilterListener {
     fun filterSort(filter: String)
}