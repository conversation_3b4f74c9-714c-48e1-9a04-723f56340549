package com.enveu.activities.search.ui


import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.detail.ui.DetailActivity
import com.enveu.activities.search.adapter.CommonSearchAdapter.CommonSearchListener
import com.enveu.activities.search.adapter.RowSearchAdapter
import com.enveu.activities.search.adapter.RowSearchAdapter.RowSearchListener
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.adapters.CommonShimmerAdapter
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModel.popularSearch.ItemsItem
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.databinding.ActivityResultBinding
import com.enveu.utils.Logger
import com.enveu.utils.MediaTypeConstants
import com.enveu.utils.ObjectHelper
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.stringsJson.converter.StringsHelper

class ActivityResults : BaseBindingActivity<ActivityResultBinding?>(), CommonSearchListener, RowSearchListener {
    private var searchKeyword: String? = null
    private var searchType: String? = null
    private var customContentType: String? = null
    private var videoType: String? = null
    private var header: String? = null
    private var mLayoutManager: LinearLayoutManager? = null
    private var loading = true
    private var singleSectionItems: List<EnveuVideoItemBean>? = null
    private var itemListDataAdapter1: RowSearchAdapter? = null
    private var counter = 0
    private var isLastPage = false
    private var totalCount = 0
    private var mLastClickTime: Long = 0
    private var firstVisiblePosition = 0
    private var pastVisiblesItems = 0
    private var visibleItemCount = 0
    private var totalItemCount = 0
    private var railInjectionHelper: RailInjectionHelper? = null
    private var applyFilter = false
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding?.toolbar?.colorsData = colorsHelper
        binding?.toolbar?.colorsData = colorsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
        bundleValue
        modelCall()
    }

    private fun modelCall() {
        connectionObserver()
    }

    private val bundleValue: Unit
        private get() {
            mLayoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
            if (intent.hasExtra("SearchResult")) {
                var extras = intent.extras
                if (extras != null) {
                    try {
                        extras = extras.getBundle("SearchResult")
                        searchType = extras?.getString("assetType")
                        customContentType = extras?.getString("customContentType")
                        header = extras?.getString("header")
                        Log.d("main", "getBundleValue: $searchType $customContentType")
                        videoType = extras?.getString("videoType")
                        totalCount = extras!!.getInt("Total_Result")
                        applyFilter = extras.getBoolean("apply_filter")
                        searchKeyword = extras.getString("Search_Key")
                    } catch (e: NullPointerException) {
                        Logger.w(e)
                    }
                }
            } else {
                throw IllegalArgumentException("Activity cannot find  extras " + "Search_Show_All")
            }
            setHeader()
        }

    private fun setHeader() {
        if (searchType.equals(AppConstants.VIDEO, ignoreCase = true) || searchType.equals(
                AppConstants.CUSTOM, ignoreCase = true) ) {
            if (header.equals(getString(R.string.search_result), ignoreCase = true)) {
                binding!!.toolbar.titleMid.text = getString(R.string.search_result) + "-" + totalCount + " " + getString(R.string.search_results)
            }
        }
    }

    private fun connectionObserver() {
        connectionValidation(NetworkConnectivity.isOnline(this))
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            uiInitialisation()
        } else {
            noConnectionLayout()
        }
    }

    private fun uiInitialisation() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        binding!!.noConnectionLayout.visibility = View.GONE
        binding!!.root.visibility = View.VISIBLE
        binding!!.toolbar.logoMain2.visibility =View.GONE
        binding!!.toolbar.logoMain2.visibility =View.GONE
        binding!!.toolbar.titleMid.visibility =View.VISIBLE
        binding!!.toolbar.llSearchIcon.visibility =View.GONE
        binding!!.progressBar.visibility = View.VISIBLE
        counter = 0
        loading = true
        singleSectionItems = ArrayList()
        itemListDataAdapter1 =
            RowSearchAdapter(
                this@ActivityResults,
                singleSectionItems,
                false,
                this,
                totalCount,
                null
            )
        setRecyclerProperties(binding!!.resultRecycler)
        callShimmer(binding!!.resultRecycler)
        hitApiSearchKeyword(searchKeyword, searchType, applyFilter, customContentType, videoType, header)
        recyclerViewScroll()
        localizeHeader(searchType)
        binding!!.toolbar.backLayout.setOnClickListener { onBackPressed() }
    }

    private fun localizeHeader(searchType: String?) {
        binding!!.toolbar.titleMid.isAllCaps = true
        if (searchType.equals(MediaTypeConstants.getInstance().episode, ignoreCase = true)) {
            binding!!.toolbar.titleMid.text =
                stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.detail_page_episodes.toString(), getString(R.string.detail_page_episodes))
        } else if (ObjectHelper.isSame(searchType, AppConstants.SEARCH_TYPE_PROGRAM)) {
            binding!!.toolbar.titleMid.text = getString(R.string.heading_program)
        }
    }

    private fun recyclerViewScroll() {
        binding!!.resultRecycler.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                try {
                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager?
                    firstVisiblePosition = layoutManager?.findFirstVisibleItemPosition()!!
                    if (dy > 0) {
                        visibleItemCount = layoutManager.childCount
                        totalItemCount = layoutManager.itemCount
                        pastVisiblesItems = layoutManager.findFirstVisibleItemPosition()
                        if (loading) {
                            if (!isLastPage) {
                                loading = false
                                counter += AppConstants.PAGE_SIZE
                                binding!!.progressBar.visibility = View.VISIBLE
                                hitApiSearchKeyword(searchKeyword, searchType, applyFilter, customContentType, videoType, header)
                            }
                        }
                    }
                } catch (e: Exception) {
                    Logger.e("ListActivity", "" + e)
                }
            }
        })
    }

    private fun setRecyclerProperties(recyclerView: RecyclerView) {
        recyclerView.isNestedScrollingEnabled = false
        recyclerView.setHasFixedSize(true)
        mLayoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        recyclerView.layoutManager = mLayoutManager
    }

    private var railCommonData: RailCommonData? = null
    private fun hitApiSearchKeyword(keyword: String?, type: String?, applyFilter: Boolean, customContentType: String?, videoType: String?, header: String?) {
        railInjectionHelper?.getSearchSingleCategory(
            keyword, type, AppConstants.PAGE_SIZE,
            counter, applyFilter, customContentType, videoType, header
        )?.observe(this@ActivityResults) { data: RailCommonData? ->
            if (data != null) {
                if (counter == 0) {
                    RecyclerAnimator(this).animate(binding!!.resultRecycler)
                    binding!!.resultRecycler.adapter = itemListDataAdapter1
                }
                railCommonData = data
                singleSectionItems = data.enveuVideoItemBeans
                itemListDataAdapter1!!.notifyAdapter(singleSectionItems)
                loading = true
                if (itemListDataAdapter1!!.itemCount == totalCount) isLastPage = true
            }
            binding!!.progressBar.visibility = View.GONE
        }
    }

    private fun callShimmer(recyclerView: RecyclerView) {
        val adapterPurchase = CommonShimmerAdapter()
        val mLayoutManager: RecyclerView.LayoutManager = LinearLayoutManager(applicationContext)
        recyclerView.layoutManager = mLayoutManager
        recyclerView.itemAnimator = DefaultItemAnimator()
        recyclerView.adapter = adapterPurchase
    }

    private fun noConnectionLayout() {
        binding!!.root.visibility = View.GONE
        binding!!.noConnectionLayout.visibility = View.VISIBLE
        binding!!.connection.retryTxt.setOnClickListener { connectionObserver() }
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityResultBinding {
        return ActivityResultBinding.inflate(inflater)
    }

    override fun onItemClicked(itemValue: ItemsItem) {
        if (itemValue.type.equals(MediaTypeConstants.VIDEO, ignoreCase = true)) {
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
                return
            }
            mLastClickTime = SystemClock.elapsedRealtime()
            ActivityLauncher.getInstance().seriesDetailScreen(this@ActivityResults, SeriesDetailActivity::class.java, itemValue.id)
        } else if (itemValue.type.equals(AppConstants.CUSTOM, ignoreCase = true)) {
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
                return
            }
            mLastClickTime = SystemClock.elapsedRealtime()
            ActivityLauncher.getInstance().seriesDetailScreen(this@ActivityResults, SeriesDetailActivity::class.java, itemValue.id)
        } else {
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
                return
            }
            mLastClickTime = SystemClock.elapsedRealtime()
            ActivityLauncher.getInstance().detailScreen(this@ActivityResults, DetailActivity::class.java, itemValue.id, "0", false)
        }
    }

    override fun onRowItemClicked(itemValue: EnveuVideoItemBean, position: Int) {
        val assetType = itemValue.assetType
        var mediType: String? = ""
        if (itemValue.assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
            mediType = itemValue.videoDetails.videoType
        } else  if (itemValue.assetType.equals(AppConstants.CUSTOM, ignoreCase = true)) {
            mediType = itemValue.customType
        }else if (itemValue.assetType.equals(AppConstants.AUDIO, ignoreCase = true)||itemValue.assetType.equals(AppConstants.PERSON, ignoreCase = true)){
            mediType=itemValue.contentType
        }

        AppCommonMethod.launchDetailScreen(this, assetType, itemValue.id,itemValue.sku, mediType, itemValue.title?:"", itemValue.externalRefId?:"", itemValue.posterURL?:"", 0, itemValue.contentSlug?:"", itemValue)
    }


}