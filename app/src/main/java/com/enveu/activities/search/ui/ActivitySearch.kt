package com.enveu.activities.search.ui


import android.annotation.SuppressLint
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.InsetDrawable
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.SearchView
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.homeactivity.viewmodel.HomeViewModel
import com.enveu.activities.search.adapter.CategoriedSearchAdapter
import com.enveu.activities.search.adapter.CommonSearchAdapter
import com.enveu.activities.search.adapter.RecentListAdapter
import com.enveu.activities.search.adapter.RecentListAdapter.KeywordItemHolderListener
import com.enveu.activities.search.adapter.SearchContentPreferenceAdapter
import com.enveu.activities.search.adapter.SortedByFilterListener
import com.enveu.adapters.CommonShimmerAdapter
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaInfo
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.KeywordList
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModel.popularSearch.ItemsItem
import com.enveu.beanModel.search.SearchRequestModel
import com.enveu.beanModelV3.searchHistory.SearchHistory
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.SearchClickCallbacks
import com.enveu.databinding.ActivitySearchBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstance
import com.enveu.menuManager.MenuCommonFunction
import com.enveu.utils.BindingUtils.FontUtil
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.AppColors
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.AppPreference
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.SpacingItemDecoration
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.Collections.reverse

class ActivitySearch : BaseBindingActivity<ActivitySearchBinding?>(),
    SearchClickCallbacks, KeywordItemHolderListener, SearchView.OnQueryTextListener, CommonDialogFragment.EditDialogListener {
    private var islogin: Boolean?= false
    private var searchAdapter: CategoriedSearchAdapter? = null
    private var model: MutableList<RailCommonData>? = null
    private var mLastClickTime: Long = 0
    private var isShimmer = false
    private var railInjectionHelper: RailInjectionHelper? = null
    private val FILTER_REQUEST_CODE = 2000
    private var searchText: String? = ""
    private var applyFilter = false
    private var requestModel: SearchRequestModel? = null
    private val stringsHelper by lazy { StringsHelper }
    private var prefrences = KsPreferenceKeys.getInstance()
    private val colorsHelper by lazy { ColorsHelper }
    private var featureList: FeatureFlagModel? = null
    private var adapter: SearchContentPreferenceAdapter? = null
    private var viewmodel: HomeViewModel?= null
    private var isNewRecentSearch=false
    private var recentItemBg:GradientDrawable?=null


    @SuppressLint("SuspiciousIndentation")
    @RequiresApi(api = Build.VERSION_CODES.Q)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parseColor()
        AnalyticsUtils.trackScreenView(this, AppConstants.SEARCH)

        featureList = AppConfigMethod.parseFeatureFlagList()
        if(featureList?.featureFlag?.IS_RECENT_SEARCHES==true){
            binding?.recentSearchTab?.visibility=View.VISIBLE
            binding?.recentSearchRecycler?.visibility=View.VISIBLE
        }else{
            binding?.recentSearchTab?.visibility=View.GONE
            binding?.recentSearchRecycler?.visibility=View.GONE
        }
        if (featureList?.featureFlag?.IS_NEW_RECENT_SEARCH==true){
            isNewRecentSearch=true
            recentItemBg=getGradientDrawable(colorsHelper.instance()?.data?.config?.app_secondary_color)
        }
        val font = FontUtil.getNormal(this)
        islogin = prefrences.appPrefLoginStatus?.equals(
            AppConstants.UserStatus.Login.toString(), ignoreCase = true
        ) == true

        val searchText = binding!!.toolbar.searchView.findViewById<TextView>(androidx.appcompat.R.id.search_src_text)
        val imageView = binding!!.toolbar.searchView.findViewById<ImageView>(androidx.appcompat.R.id.search_close_btn)
        try {
            searchText.setTextColor(AppColors.searchKeywordTextColor())
            searchText.setHintTextColor(AppColors.searchKeywordHintColor())
            searchText.isCursorVisible = true
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                if (searchText.textCursorDrawable is InsetDrawable) {
                    val insetDrawable = searchText.textCursorDrawable as InsetDrawable?
                    insetDrawable!!.setColorFilter(Color.WHITE, PorterDuff.Mode.SRC_ATOP)
                    searchText.textCursorDrawable = insetDrawable
                }
                if (searchText.textSelectHandle is BitmapDrawable) {
                    val insetDrawable = searchText.textSelectHandle as BitmapDrawable?
                    insetDrawable!!.setColorFilter(Color.WHITE, PorterDuff.Mode.SRC_ATOP)
                    searchText.setTextSelectHandle(insetDrawable)
                }
                if (searchText.textSelectHandleRight is BitmapDrawable) {
                    val insetDrawable = searchText.textSelectHandleRight as BitmapDrawable?
                    insetDrawable!!.setColorFilter(Color.WHITE, PorterDuff.Mode.SRC_ATOP)
                    searchText.setTextSelectHandleRight(insetDrawable)
                }
                if (searchText.textSelectHandleLeft is BitmapDrawable) {
                    val insetDrawable = searchText.textSelectHandleLeft as BitmapDrawable?
                    insetDrawable!!.setColorFilter(Color.WHITE, PorterDuff.Mode.SRC_ATOP)
                    searchText.setTextSelectHandleLeft(insetDrawable)
                }
            }
            imageView.setColorFilter(ContextCompat.getColor(this, R.color.series_detail_now_playing_title_color), PorterDuff.Mode.MULTIPLY)
        } catch (ex: Exception) {
            Logger.w(ex)
        }
        searchText.typeface = font
        viewmodel = ViewModelProvider(this)[HomeViewModel::class.java]
        connectionObserver()
        binding!!.toolbar.searchView.setOnQueryTextListener(this)
        binding!!.toolbar.searchView.setIconifiedByDefault(false) // Make sure the SearchView is not iconified (collapsed)
        // Set the hint for the SearchView
        binding!!.toolbar.searchView.queryHint = getString(R.string.search_hint)
        //showSoftKeyboard(binding!!.toolbar.searchView)

    }

    private fun parseColor() {
        binding?.stringData = stringsHelper
        binding?.colorsData = colorsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
        binding?.toolbar?.colorsData = colorsHelper
        binding?.toolbar?.stringData = stringsHelper
    }

    private var searchResult = true
    private fun clickListener() {
        binding!!.noResult.visibility = View.GONE
        hitApiPopularSearch()
        if (isNewRecentSearch){  setRecyclerPropertiesForNewUI(binding?.recentSearchRecycler!!) }else {
            setRecyclerProperties(binding?.recentSearchRecycler!!)
        }
        setSearchHistoryResult()
        binding!!.toolbar.backButton.setOnClickListener { onBackPressed() }
        binding!!.toolbar.clearText.setOnClickListener { onBackPressed() }
        binding?.deleteKeywords?.setOnClickListener {
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.recent_searches.toString(),
                    getString(R.string.popup_recent_search)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_delete_search_history.toString(),
                    getString(R.string.popup_delete_search_history)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_search_yes.toString(),
                    getString(R.string.popup_search_yes)
                )
            )
        }
    }

    override fun onResume() {
        super.onResume()
        if (prefrences?.isComeWithFilterScreen == true){
            binding!!.toolbar.searchView.setQuery(prefrences?.filterSelectedItems?.trim { it <= ' ' }, true)
            binding!!.toolbar.searchView.setQuery("", false)
            binding!!.toolbar.searchView.tooltipText = ""
            prefrences?.filterSelectedItems = ""
        }
    }

    private fun setSearchHistoryResult() {
        if (islogin == true) {
            viewmodel?.getSearchHistory(prefrences.appPrefAccessToken)?.observe(this) {
                if (it.data?.items != null && it.data.items.isNotEmpty()) {
                    binding!!.llRecentSearchLayout.visibility = View.VISIBLE
                    val recentListAdapter = if (isNewRecentSearch){
                        RecentListAdapter(this, it.data.items, null,this@ActivitySearch,1,recentItemBg)
                    }else{
                        RecentListAdapter(this, it.data.items, null,this@ActivitySearch) }
                    binding?.recentSearchRecycler?.adapter = recentListAdapter
                }
            }
        }else{
            setRecentSearchAdapter()
        }
    }

    private fun setRecentSearchAdapter() {
        val gson = Gson()
        val json = AppPreference.getInstance(this).recentSearchList
        if (json.isEmpty()) {
            binding!!.llRecentSearchLayout.visibility = View.GONE
        } else {
            binding!!.llRecentSearchLayout.visibility = View.VISIBLE
            val type = object : TypeToken<List<KeywordList?>?>() {}.type
            val arrPackageData = gson.fromJson<List<KeywordList?>>(json, type)
            reverse(arrPackageData)
            if (arrPackageData.isNotEmpty()) {
                val recentListAdapter = if (isNewRecentSearch){
                    RecentListAdapter(this,null, arrPackageData, this@ActivitySearch, 1,recentItemBg)  }
                else{
                    RecentListAdapter(this,null, arrPackageData, this@ActivitySearch) }
                binding!!.recentSearchRecycler.adapter = recentListAdapter
            }
        }
    }

    private fun confirmDeletion() {
        val builder = AlertDialog.Builder(this, R.style.AlertDialogStyle)
        builder.setMessage(
            stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_delete_search_history.toString(),
                getString(R.string.popup_delete_search_history)
            )
        ).setCancelable(true).setPositiveButton(this.resources.getString(R.string.delete)) { dialog: DialogInterface, _: Int ->
            AppPreference.getInstance(this).recentSearchList = ""
            binding!!.llRecentSearchLayout.visibility = View.GONE
            dialog.cancel()
        }
            .setNegativeButton(this.resources.getString(R.string.cancel)) { dialog: DialogInterface, _: Int -> dialog.cancel() }
        val alert = builder.create()
        alert.show()
        val bn = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
        bn.setTextColor(ContextCompat.getColor(this, R.color.series_detail_now_playing_title_color))
        val bp = alert.getButton(DialogInterface.BUTTON_POSITIVE)
        bp.setTextColor(ContextCompat.getColor(this, R.color.series_detail_episode_unselected_btn_txt_color))
    }

    @SuppressLint("SetTextI18n")
    private fun hitApiSearchKeyword(searchKeyword: String) {
        model = ArrayList()
        callShimmer(binding!!.searchResultRecycler)
        setRecyclerProperties(binding!!.searchResultRecycler)
        binding!!.rootView.visibility = View.GONE
        binding!!.noResult.visibility = View.GONE
        binding!!.ivNoResult.visibility=View.GONE
        binding!!.tvNoResultDesc.visibility=View.GONE
        binding!!.llSearchResultLayout.visibility = View.VISIBLE
        railInjectionHelper?.getSearch(searchKeyword, 4, 0,true,false)?.observe(this@ActivitySearch) { data: List<RailCommonData> ->
            searchResult = true
            if (data.isNotEmpty()) {
                if (featureList?.featureFlag?.IS_SEARCH_FILTER == true){
                    setRecycleViewContentPreferences()
                }
//                this.searchKeyword
                AnalyticsUtils.logSearchEvent(this,searchKeyword)

                try {
                    binding!!.noResult.visibility = View.GONE
                    binding!!.rootView.visibility = View.GONE
                    for (i in data.indices) {
                        val railCommonData = data[i]
                        if (railCommonData.pageTotal > 0) {
                            if (railCommonData.status) {
                                val temp =
                                    RailCommonData()
                                temp.enveuVideoItemBeans = railCommonData.enveuVideoItemBeans
                                if (railCommonData.enveuVideoItemBeans.size > 0) {
                                    val enveuVideoItemBean = railCommonData.enveuVideoItemBeans[0]
                                    temp.assetType = enveuVideoItemBean.assetType
                                    temp.status = true
                                    temp.searchKey = searchKeyword
                                    temp.totalCount = railCommonData.pageTotal
                                    (model as ArrayList<RailCommonData>).add(temp)
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    binding!!.llNoresult.visibility=View.VISIBLE
                    binding!!.llRecentSearchLayout.visibility=View.GONE
                    Logger.w(e)
                }
                if ((model as ArrayList<RailCommonData>).size > 0) {
                    RecyclerAnimator(
                       this
                    ).animate(binding!!.searchResultRecycler)
//                    searchAdapter = CategoriedSearchAdapter(this, model, featureList,this@ActivitySearch)
                    searchAdapter = CategoriedSearchAdapter(this, model, featureList,this@ActivitySearch,searchKeyword)
                    binding!!.searchResultRecycler.adapter = searchAdapter
                } else {
                    binding!!.llNoresult.visibility=View.VISIBLE
                    binding!!.llRecentSearchLayout.visibility=View.GONE
                    if (prefrences?.isComeWithFilterScreen == false){
                        binding!!.noResult.text="Couldn't find \"$searchKeyword\" "
                    }
                    binding!!.noResult.visibility = View.VISIBLE
//                    binding!!.rootView.visibility = View.VISIBLE
                    binding!!.llSearchResultLayout.visibility = View.GONE
                    binding!!.ivNoResult.visibility=View.VISIBLE
                    binding!!.tvNoResultDesc.visibility=View.VISIBLE
                }
            } else {
                binding!!.llNoresult.visibility=View.VISIBLE
                if (prefrences?.isComeWithFilterScreen == false){
                    binding!!.noResult.text="Couldn't find \"$searchKeyword\" "
                }
                binding!!.noResult.visibility = View.VISIBLE
//                binding!!.rootView.visibility = View.VISIBLE
                binding!!.llSearchResultLayout.visibility = View.GONE
                binding!!.llRecentSearchLayout.visibility=View.GONE
                binding!!.ivNoResult.visibility=View.VISIBLE
                binding!!.tvNoResultDesc.visibility=View.VISIBLE
            }
            if (prefrences?.isComeWithFilterScreen == false){
                createRecentSearch(searchKeyword)
            }else{
                prefrences?.isComeWithFilterScreen = false
            }
            binding!!.progressBar.visibility = View.GONE
        }
    }

    private fun createRecentSearch(searchKeyword: String) {
        if (searchKeyword.equals("", ignoreCase = true)) {
            return
        }
        val keywordList = KeywordList()
        keywordList.keywords = searchKeyword
        keywordList.timeStamp = AppCommonMethod.currentTimeStamp
        if (AppPreference.getInstance(this).recentSearchList.equals("", ignoreCase = true)) {
            val list: MutableList<KeywordList> = ArrayList()
            list.add(keywordList)
            val gson = Gson()
            val json = gson.toJson(list)
            AppPreference.getInstance(this).recentSearchList = json
            setRecentSearchAdapter()
        } else {
            val gson = Gson()
            val json = AppPreference.getInstance(this).recentSearchList
            if (json.isEmpty()) {
            } else {
                val type = object : TypeToken<List<KeywordList?>?>() {}.type
                val arrPackageData = gson.fromJson<List<KeywordList?>>(json, type)
                if (json.contains(searchKeyword)) {
                    return
                }
                val newL: MutableList<KeywordList?> = ArrayList(arrPackageData)
                if (newL.size < 5) {
                    newL.add(keywordList)
                } else {
                    newL.removeAt(0)
                    newL.add(keywordList)
                }
                val jsons = gson.toJson(newL)
                AppPreference.getInstance(this).recentSearchList = jsons
                newL.reverse()
                if (newL.size > 0) {
                    val recentListAdapter = if (isNewRecentSearch){
                        RecentListAdapter(this,null, newL, this@ActivitySearch,1,recentItemBg) }
                    else{
                        RecentListAdapter(this,null, newL, this@ActivitySearch) }
                    binding!!.recentSearchRecycler.adapter = recentListAdapter
                }
            }
        }
    }


    private fun hitApiPopularSearch() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        if (!SDKConfig.getInstance().popularSearchId.equals("", ignoreCase = true)) {
            railInjectionHelper!!.getPlayListDetailsWithPagination(SDKConfig.getInstance().popularSearchId, prefrences?.appPrefAccessToken, 0, 8, null).observe(this) { playlistRailData: RailCommonData ->
                setUiComponents(playlistRailData)
            }
        }
    }

    private fun setUiComponents(jsonObject: RailCommonData) {
        binding!!.popularSearchRecycler.addItemDecoration(SpacingItemDecoration(5, SpacingItemDecoration.HORIZONTAL))
        val gridItem = if (resources.getBoolean(R.bool.isTablet)) {
            3
        } else {
            if (featureList?.featureFlag?.IS_SQUARE_DEFAULT == true) {
                3
            }else{
                2
            }
        }
        isShimmer = false
        if (jsonObject.status) {
            binding!!.tvPopularSearch.visibility = View.VISIBLE
        } else {
            binding!!.popularSearchRecycler.visibility = View.GONE
        }
        binding!!.popularSearchRecycler.layoutManager = GridLayoutManager(this, gridItem)
        (binding!!.popularSearchRecycler.itemAnimator as SimpleItemAnimator?)!!.supportsChangeAnimations = false
        binding!!.popularSearchRecycler.adapter =
            CommonSearchAdapter(
                this,
                jsonObject,
                this,
                featureList
            )
    }

    private fun setRecyclerProperties(recyclerView: RecyclerView) {
        recyclerView.hasFixedSize()
        recyclerView.isNestedScrollingEnabled = false
        recyclerView.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
    }
    private fun setRecyclerPropertiesForNewUI(recyclerView: RecyclerView) {
        recyclerView.hasFixedSize()
        recyclerView.isNestedScrollingEnabled = false
        recyclerView.layoutManager = LinearLayoutManager(this, RecyclerView.HORIZONTAL, false)
    }
    private fun connectionObserver() {
        connectionValidation(NetworkConnectivity.isOnline(this))
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            uiInitialisation()
        } else {
            noConnectionLayout()
        }
    }

    private fun uiInitialisation() {
        binding!!.searchLayout.visibility = View.VISIBLE
        binding!!.noConnectionLayout.visibility = View.GONE
        binding!!.tvPopularSearch.visibility = View.GONE
        binding!!.toolbar.searchView.requestFocus()
        callShimmer(binding!!.popularSearchRecycler)
        clickListener()
    }

    private fun callShimmer(recyclerView: RecyclerView) {
        isShimmer = true
        val adapterPurchase = CommonShimmerAdapter(true)
        val mLayoutManager: RecyclerView.LayoutManager = LinearLayoutManager(this)
        recyclerView.layoutManager = mLayoutManager
        recyclerView.itemAnimator = DefaultItemAnimator()
        recyclerView.adapter = adapterPurchase
    }

    private fun noConnectionLayout() {
        binding!!.searchLayout.visibility = View.GONE
        binding!!.progressBar.visibility = View.GONE
        binding!!.noConnectionLayout.visibility = View.VISIBLE
        binding!!.connection.retryTxt.setOnClickListener { connectionObserver() }
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivitySearchBinding {
        return ActivitySearchBinding.inflate(inflater)
    }

    override fun onEnveuItemClicked(itemValue: EnveuVideoItemBean) {
        val assetType = itemValue.assetType
        var mediType: String? = ""
        if (itemValue.assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
            mediType = itemValue.videoDetails.videoType
        } else  if (itemValue.assetType.equals(AppConstants.CUSTOM, ignoreCase = true)) {
            mediType = itemValue.customType
        }else if (itemValue.assetType.equals(AppConstants.AUDIO, ignoreCase = true)||itemValue.assetType.equals(AppConstants.PERSON, ignoreCase = true)){
            mediType=itemValue.contentType
        }

        AppCommonMethod.launchDetailScreen(this, assetType, itemValue.id, itemValue.sku,mediType, itemValue.title?:"", itemValue.externalRefId?:"", itemValue.posterURL?:"", 0, itemValue.contentSlug?:"", itemValue)
    }

    override fun onShowAllItemClicked(itemValue: RailCommonData, header: String) {
        val customContentType: String = ""
        var videoType: String? = ""
        val assetType = itemValue.assetType

        if (assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
            videoType = itemValue.enveuVideoItemBeans[0].videoDetails.videoType
        }
        if (itemValue.status) {
            applyFilter = java.lang.Boolean.parseBoolean(prefrences.filterApply)
            ActivityLauncher.getInstance().resultActivityBundle(
               this, ActivityResults::class.java,
                assetType, itemValue.searchKey, itemValue.totalCount, applyFilter, customContentType, videoType, header
            )
        }
    }

    override fun onShowAllProgramClicked(itemValue: RailCommonData) {
        if (itemValue.status) {
            applyFilter = java.lang.Boolean.parseBoolean(prefrences.filterApply)
        }
    }

    override fun onPopularSearchItemClicked(itemValue: ItemsItem?) {
    }


    override fun onItemClicked(itemValue: SearchHistory.Data.Item) {
        if (NetworkConnectivity.isOnline(this)) {
          hideSoftKeyboard(binding!!.toolbar.searchView)
            if (searchResult) {
                searchResult = false
                binding!!.toolbar.searchView.setQuery(itemValue.keyword?.trim { it <= ' ' }, true)
            }
        } else {
            commonDialog(
                "",
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                    getString(R.string.popup_no_internet_connection_found)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
    }



    override fun onItemClickedByLocal(itemValue: KeywordList?) {
        if (NetworkConnectivity.isOnline(this)) {
            hideSoftKeyboard(binding!!.toolbar.searchView)
            if (searchResult) {
                searchResult = false
                binding!!.toolbar.searchView.setQuery(itemValue?.keywords?.trim { it <= ' ' }, true)
            }
        } else {
            commonDialog(
                "",
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                    getString(R.string.popup_no_internet_connection_found)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
    }

    override fun onQueryTextSubmit(query: String): Boolean {
        if (NetworkConnectivity.isOnline(this)) {
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                return true
            }
            mLastClickTime = SystemClock.elapsedRealtime()
            hideSoftKeyboard(binding!!.toolbar.searchView)
            if (query.trim { it <= ' ' }.length > 2) {
                applyFilter = java.lang.Boolean.parseBoolean(prefrences.filterApply)
                searchText = query.trim { it <= ' ' }
                Logger.d("SEARCH TEXT $searchText")
                hitApiSearchKeyword(query.trim { it <= ' ' })
            }
        } else {
            commonDialog(
                "",
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                    getString(R.string.popup_no_internet_connection_found)
                ), stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
        return false
    }

    override fun onQueryTextChange(newText: String): Boolean {
        return false
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == FILTER_REQUEST_CODE) {
            if (data != null && data.hasExtra("selected_filter")) {
                requestModel = data.getParcelableExtra("selected_filter")
            }
           /* if (resultCode == RESULT_OK) {
                Logger.d("RETURN WITH DATA")
                if (searchText != null && searchText!!.trim { it <= ' ' }.length > 2) {
                    hitApiSearchKeyword(searchText!!.trim { it <= ' ' })
                }
            } else {
                hitApiSearchKeyword(searchText!!.trim { it <= ' ' })
            }*/
        }
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun onActionBtnClicked() {
        AppPreference.getInstance(this).recentSearchList = ""
        binding!!.llRecentSearchLayout.visibility = View.GONE
        if (islogin == true){
            viewmodel?.deleteSearchHistory(prefrences.appPrefAccessToken, "", "",true)
        }
    }

    private fun setRecycleViewContentPreferences(){
        val getMediaTypeList: List<MediaInfo?>? = MenuCommonFunction.getParseSearchFilter(this)
        if (getMediaTypeList != null) {
            binding?.rvContentPreference?.setHasFixedSize(true)
            adapter = SearchContentPreferenceAdapter(getMediaTypeList as MutableList<MediaInfo?>? , object  :
                SortedByFilterListener {
                override fun filterSort(filter: String) {

                    searchAdapter?.notifyDataSetChanged()
                }
            })
            binding?.rvContentPreference?.visibility = View.VISIBLE
            binding?.rvContentPreference?.adapter = adapter
        }
    }
    private fun getGradientDrawable(appSecondaryColor: String?): GradientDrawable {
        val drawable = GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            cornerRadius = resources.getDimension(R.dimen.NontonSize15)
            appSecondaryColor?.let {
                setColor(Color.parseColor(appSecondaryColor))
            } ?: setColor(Color.parseColor("#273b44"))
        }
        return drawable
    }
    override fun onCancelBtnClicked() {

    }
}