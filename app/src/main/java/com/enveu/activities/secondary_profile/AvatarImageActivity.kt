package com.enveu.activities.secondary_profile

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.homeactivity.viewmodel.HomeViewModel
import com.enveu.baseModels.BaseActivity
import com.enveu.beanModelV3.searchGenres.SearchGenres
import com.enveu.databinding.ActivityAvatarImageBinding
import com.enveu.utils.constants.AppConstants

class AvatarImageActivity : BaseActivity() {
    private lateinit var binding:ActivityAvatarImageBinding
    private lateinit var onAvatarImageClickListener: OnAvatarImageClickListener
    private var homeViewModel: HomeViewModel? = null
    private var avatarImageUrlData:String? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this,R.layout.activity_avatar_image)
        initUI()
    }

    private fun initUI() {
        homeViewModel = ViewModelProvider(this)[HomeViewModel::class.java]
        getGenreListApi()
        binding.toolbar.backLayout.setOnClickListener { finish() }
        binding.toolbar.searchIcon.visibility = View.GONE

        onAvatarImageClickListener = object :OnAvatarImageClickListener {
            override fun onAvatarImageClickListener(avatarImageUrl: String?) {
                 avatarImageUrlData = avatarImageUrl
                 finishCurrentActivity()
            }
        }
    }

    private fun finishCurrentActivity() {
        val intent = Intent().apply {
            putExtra(AppConstants.GET_IMAGE_URL, avatarImageUrlData)
        }
        setResult(RESULT_OK, intent)
        finish()
    }

    private fun getGenreListApi() {
        binding.pBar.visibility = View.VISIBLE
        homeViewModel?.getSearchGenres(AVATAR, CUSTOM)?.observe(this) { genreListData ->
            binding.pBar.visibility = View.GONE
            if (genreListData != null) {
                if (genreListData.data?.items?.isNotEmpty() == true) {
                     setAvatarImageAdapter(genreListData.data.items)
                }
            }
        }
    }

    private fun setAvatarImageAdapter(avatarImageItems: List<SearchGenres.Data.Item?>) {
        val avatarImageAdapter = AvatarImageAdapter(avatarImageItems, onAvatarImageClickListener)
        val layoutManager = object : GridLayoutManager(this, 3) {
            override fun checkLayoutParams(layoutParams: RecyclerView.LayoutParams) : Boolean {
                layoutParams.width = width / spanCount
                return true
            }
        }
        binding.rvAvatarImage.layoutManager = layoutManager
        binding.rvAvatarImage.adapter = avatarImageAdapter
    }

    companion object {
        const val AVATAR = "AVATAR"
        const val CUSTOM = "CUSTOM"
    }
}