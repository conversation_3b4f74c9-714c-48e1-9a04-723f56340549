package com.enveu.activities.secondary_profile

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.beanModelV3.searchGenres.SearchGenres
import com.enveu.utils.commonMethods.AppCommonMethod

class AvatarImageAdapter(
    private val avatarImageItems: List<SearchGenres.Data.Item?>?,
    private val onAvatarImageClickListener: OnAvatarImageClickListener
) :RecyclerView.Adapter<AvatarImageAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val rootLayout = LayoutInflater.from(parent.context).inflate(R.layout.items_avatar_image_layout, parent, false)
        return ViewHolder(rootLayout)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val avatarImageItem = avatarImageItems?.get(position)
        holder.apply {
            if (!avatarImageItem?.images.isNullOrEmpty()) {
                Glide.with(avatarImageView.context).asBitmap().load(getImageUrl(position)).placeholder(R.drawable.account_circle_icons).apply(AppCommonMethod.optionsSearch).into(avatarImageView)
            }
        }
    }

    override fun getItemCount(): Int {
        return avatarImageItems?.size?:0
    }

    private fun getImageUrl(position: Int):String? {
        var imageUrl:String? = null
        if (!avatarImageItems?.get(position)?.images.isNullOrEmpty()){
            avatarImageItems?.get(position)?.images?.forEachIndexed { _, image ->
                if (!image?.imageContent?.src.isNullOrEmpty()){
                    imageUrl = image?.imageContent?.src
                }
            }
        }
        return imageUrl
    }

   inner class ViewHolder(view: View):RecyclerView.ViewHolder(view) {
       val avatarImageView:ImageView = view.findViewById(R.id.avatarImageView)

       init {
           avatarImageView.setOnClickListener {
               onAvatarImageClickListener.onAvatarImageClickListener(getImageUrl(bindingAdapterPosition))
           }
       }
    }
}