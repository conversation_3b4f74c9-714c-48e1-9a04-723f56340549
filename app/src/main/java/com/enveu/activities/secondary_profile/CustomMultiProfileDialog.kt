package com.enveu.activities.secondary_profile

import android.app.Dialog
import android.content.Context
import android.os.CountDownTimer
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Base64
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.enveu.R
import com.enveu.databinding.MultipleProfileDisableParentalLockLayoutBinding
import com.enveu.databinding.MultipleProfileEnterPinLayoutBinding
import com.enveu.utils.showToast
import java.util.Locale

object CustomMultiProfileDialog {

    private const val mTimeLeftInMillis: Long = 30000
    private var mCountDownTimer: CountDownTimer? = null
    private var otpSendCount = 0
    private var dialog:Dialog? = null

    fun createPinDialog(context: Context, createOtpCallbackListener: VerifyOtpCallbackListener?){
        val dialog = Dialog(context)
        val binding = MultipleProfileEnterPinLayoutBinding.inflate(LayoutInflater.from(context))
        dialog.setContentView(binding.root)
        dialog.window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT)
        dialog.setCanceledOnTouchOutside(false)

        val pinViewTextWatcher = object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                binding.pinViewOtp.setItemBackground(ContextCompat.getDrawable(context, R.drawable.pin_view_bg))
                val pin = binding.pinViewOtp.text?.toString() ?: ""
                val confirmPin = binding.confirmPinViewOtp.text?.toString() ?: ""

                binding.setPinBtn.isEnabled = pin.length == 4 && confirmPin.length == 4 && pin == confirmPin
                binding.pinViewOtp.isCursorVisible = pin.length < 4
                binding.confirmPinViewOtp.isCursorVisible = confirmPin.length < 4

            }
            override fun afterTextChanged(p0: Editable?) {}
        }

        binding.pinViewOtp.addTextChangedListener(pinViewTextWatcher)
        binding.confirmPinViewOtp.addTextChangedListener(pinViewTextWatcher)

        binding.setPinBtn.setOnClickListener {
            if (binding.pinViewOtp.text.toString().trim().isEmpty()) {
                showToast(context, context.getString(R.string.please_enter_pin_text))
            } else if (binding.confirmPinViewOtp.text.toString().trim().isEmpty()) {
                showToast(context, context.getString(R.string.please_enter_confirm_pin))
            } else if (binding.confirmPinViewOtp.text.toString() != binding.pinViewOtp.text.toString()) {
                showToast(context, context.getString(R.string.your_pin_and_confirm_pin_do_not_match))
            } else {
                dialog.dismiss()
                createOtpCallbackListener?.onSetPinClickListener(encodeString(binding.pinViewOtp.text.toString()))
            }
        }
        dialog.show()
    }

    fun disableParentLockPin(context: Context, userEmail: String?, verifyOtpCallbackListener: VerifyOtpCallbackListener?) {
        dialog = Dialog(context)
        val binding = MultipleProfileDisableParentalLockLayoutBinding.inflate(LayoutInflater.from(context))
        dialog?.setContentView(binding.root)
        dialog?.window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT)
        dialog?.setCanceledOnTouchOutside(false)
        binding.resendOtpText.text = context.getString(R.string.resend_otp_text)
        binding.parentLockHeadingText.text = context.getString(R.string.enter_otp_sent_to, userEmail)
        startCounter(context, binding.resendOtpText)
        binding.verifyOtpBtn.setOnClickListener {
            if (binding.pinViewOtp.text.toString().isNotEmpty()) {
                verifyOtpCallbackListener?.onSuccessListener(binding.pinViewOtp.text.toString())
            }
        }
        binding.resendOtpText.setOnClickListener {
            if (otpSendCount < 3) {
                otpSendCount++
                binding.pinViewOtp.setText("")
                verifyOtpCallbackListener?.callAgainVerifyOtpApi()
                startCounter(context, binding.resendOtpText)
            }
        }
        dialog?.show()
    }

    private fun startCounter(context: Context, textView: TextView) {
        if (mCountDownTimer != null) {
            mCountDownTimer?.cancel()
        }
        textView.isEnabled = false
        textView.setTextColor(context.getColor(R.color.tph_hint_txt_color))
        textView.visibility = View.VISIBLE
        mCountDownTimer = object : CountDownTimer(mTimeLeftInMillis, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                updateCountDownText(millisUntilFinished, context, textView)
            }

            override fun onFinish() {
                textView.setText(R.string.resend_otp_btn_text)
                textView.visibility = View.INVISIBLE
                if (otpSendCount == 3) {
                    textView.isEnabled = false
                    textView.setTextColor(context.getColor(R.color.tph_hint_txt_color))
                } else {
                    textView.isEnabled = true
                    textView.visibility=View.VISIBLE
                    textView.setTextColor(context.getColor(R.color.main_btn_selected_bg_color))
                }
            }
        }.start()
    }


    private fun updateCountDownText(millisUntilFinished: Long, context: Context, textView: TextView) {
        val minutes = millisUntilFinished / 1000 / 60
        val seconds = millisUntilFinished / 1000 % 60
        val timeLeftFormatted = String.format(Locale("en", "US"), "%02d:%02d", minutes, seconds)
        textView.text = String.format("%s %s%s", context.getString(R.string.resend_otp_text), timeLeftFormatted," "+context.getString(R.string.sec))

        if (millisUntilFinished.equals(0) && (otpSendCount < 3)) {
            textView.isEnabled = true
            textView.visibility= View.VISIBLE
            textView.setTextColor(context.getColor(R.color.white))
        }
    }

     private fun encodeString(originalString: String?): String {
        val encodedString = Base64.encodeToString(originalString?.toByteArray(), Base64.NO_WRAP)
        return encodedString
    }

     fun dialogDismiss(){
        dialog?.dismiss()
    }

    fun otpInValidErrorMsg(){

    }
}