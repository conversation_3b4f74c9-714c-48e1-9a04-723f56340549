package com.enveu.activities.secondary_profile

import android.Manifest
import android.annotation.SuppressLint
import android.app.AlertDialog
import android.app.DatePickerDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.text.Editable
import android.text.TextWatcher
import android.text.format.DateFormat
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.amazonaws.ClientConfiguration
import com.amazonaws.auth.CognitoCachingCredentialsProvider
import com.amazonaws.mobileconnectors.s3.transferutility.TransferListener
import com.amazonaws.mobileconnectors.s3.transferutility.TransferObserver
import com.amazonaws.mobileconnectors.s3.transferutility.TransferState
import com.amazonaws.mobileconnectors.s3.transferutility.TransferUtility
import com.amazonaws.regions.Region
import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3Client
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.homeactivity.viewmodel.HomeViewModel
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseActivity
import com.enveu.beanModel.userProfile.UserProfileResponse
import com.enveu.beanModelV3.mutli_profile_response.AddProfileRequestBody
import com.enveu.beanModelV3.mutli_profile_response.CustomDatas
import com.enveu.beanModelV3.mutli_profile_response.PreferenceSettings
import com.enveu.beanModelV3.mutli_profile_response.SecondaryProfileData
import com.enveu.beanModelV3.searchGenres.SearchGenres
import com.enveu.databinding.ActivityEditSecondaryProfileBinding
import com.enveu.databinding.ItemGenresBottomSheetLayoutBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.utils.Constants
import com.enveu.utils.FileUtil
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.StringUtils
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.setUserImageWithGlide
import com.enveu.utils.show
import com.enveu.utils.showToast
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.gson.Gson
import com.google.gson.JsonObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.text.DecimalFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale


class EditSecondaryProfileActivity : BaseActivity(), CommonDialogFragment.EditDialogListener {
    private lateinit var binding: ActivityEditSecondaryProfileBinding
    private var secondaryProfileData: SecondaryProfileData? = null
    private var registrationLoginViewModel: RegistrationLoginViewModel? = null
    private val colorsHelper by lazy { ColorsHelper }
    private val stringsHelper by lazy { StringsHelper }
    private var homeViewModel: HomeViewModel? = null
    private lateinit var verifyOtpCallbackListener:VerifyOtpCallbackListener
    private lateinit var updateGenresTitleListener:UpdateGenresTitleListener
    private var byDefaultAllGenresSelect:Boolean = false
    private var genresListAdapter: GenresListAdapter? = null
    private var bottomSheetDialog: BottomSheetDialog? = null
    private var allGenresList: List<SearchGenres.Data.Item?>? = null
    private val REQUEST_CODE: Int = 1003
    private var dateMilliseconds = ""
    private var parentalLockEnable:Boolean? = true
    private var addTimeGenresList:ArrayList<Int?> = ArrayList()
    private var selectedGenresTitle:HashSet<String?>?= HashSet()
    private var userImageUrl:String? = null
    private var s3: AmazonS3Client? = null
    private var featureModel: FeatureFlagModel? = null
    private var imageUrlId = ""
    private var transferUtility: TransferUtility? = null
    private var via = "Gallery"
    private var profileUserName : String = ""


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_edit_secondary_profile)
        initUI()
    }

    private fun initUI() {
        homeViewModel = ViewModelProvider(this)[HomeViewModel::class.java]
        registrationLoginViewModel = ViewModelProvider(this)[RegistrationLoginViewModel::class.java]
        credentialsProvider()
        setTransferUtility()
        binding.stringData = stringsHelper
        binding.colorsData = colorsHelper
        rotateImageLocaleWise(binding.toolbar.backArrow)
        binding.toolbar.backArrow.setOnClickListener { finish() }
        binding.backArrowIv.setOnClickListener { finish() }
        binding.toolbar.searchIcon.visibility = View.GONE
        featureModel = AppConfigMethod.parseFeatureFlagList()

        if (intent.hasExtra(AppConstants.BUNDLE_EDIT_PROFILE)) {
            binding.toolbar.mainLay.visibility=  View.GONE
            binding.backArrowIv.visibility= View.VISIBLE
            binding.backArrowIv.let { it?.let { it1 -> rotateImageLocaleWise(it1) } }
            secondaryProfileData = intent.getSerializableExtra(AppConstants.BUNDLE_EDIT_PROFILE) as SecondaryProfileData
            setImageWithGlide(secondaryProfileData?.profilePicURL)
            binding.userName.setText(secondaryProfileData?.name)
            userImageUrl = secondaryProfileData?.profilePicURL
            binding.profileUserName.setText(secondaryProfileData?.userName)
            if (secondaryProfileData?.gender != null) {
                binding.gender.text = secondaryProfileData?.gender.toString()
            }
            binding.addBio.setText(secondaryProfileData?.bio)
            binding!!.profileUserName.isEnabled = featureModel!!.featureFlag.EDIT_PROFILE_UNIQUE_USER_NAME
            if (secondaryProfileData?.dateOfBirth != null) {
                val df = DecimalFormat("#")
                df.maximumFractionDigits = 0
                val l = df.format(secondaryProfileData?.dateOfBirth).toLong()
                dateMilliseconds = l.toString()
                val dateString = DateFormat.format("dd-MM-yyyy", Date(l)).toString()
                binding.dOB.text = dateString
            }
            binding.deleteProfileBtn.visibility = View.VISIBLE
            binding.updateProfileBtn.text = getString(R.string.save_changes)
            binding.headingTitleText.text = getString(R.string.edit_profile)
        } else {
            binding!!.profileUserName.isEnabled = true
            binding.updateProfileBtn.text = getString(R.string.countinue)
            binding.headingTitleText.text = getString(R.string.create_profile_text)
            binding.profileUserName.addTextChangedListener(userNameTextWatcher)
        }
        binding.profileUserName.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val username = binding.profileUserName.text.toString()
                if (username.isNotBlank()) {
                    uniqueUserNameApi(false)
                }
            }
        }
        val currentlyPrimaryAccount = intent.getBooleanExtra(AppConstants.CURRENTLY_PRIMARY_ACCOUNT, false)
        val parentPrimaryAccount =  intent.getBooleanExtra(AppConstants.PRIMARY_ACCOUNT, false)
        if (parentPrimaryAccount || currentlyPrimaryAccount) {
            binding.genreBottomSheetBtn.visibility = View.GONE
            binding.deleteProfileBtn.visibility = View.GONE
        } else {
//            binding.genreBottomSheetBtn.visibility = View.VISIBLE
            binding.deleteProfileBtn.visibility = View.VISIBLE
            getGenreListApi()
        }
        if (!parentPrimaryAccount && currentlyPrimaryAccount) {
//            binding.genreBottomSheetBtn.visibility = View.VISIBLE
            getGenreListApi()
        }
        if (intent.getBooleanExtra(AppConstants.ADD_SECONDARY_ACCOUNT, false)) {
//            binding.genreBottomSheetBtn.visibility = View.VISIBLE
            binding.deleteProfileBtn.visibility = View.GONE
            byDefaultAllGenresSelect = true
            getGenreListApi()
        }
        binding.updateProfileBtn.setOnClickListener {
            if (binding.userName.text.toString().trim().isNotEmpty()) {
                callUpdateProfileApi(binding.userName.text.toString())
            }
            else{
                showToast(this, getString(R.string.please_enter_name))
            }
        }

        binding.deleteProfileBtn.setOnClickListener {
            commonDialogWithCancel(getString(R.string.are_you_sure_text),
                getString(R.string.delete_acc_desc),
                getString(R.string.delete_profile_text), getString(R.string.cancel_text))
        }

        binding.genreBottomSheetBtn.setOnClickListener {
            bottomSheetDialog?.show()
        }

        binding.gender.setOnClickListener {
            val genderOptions = if (KsPreferenceKeys.getInstance().appLanguage == AppConstants.LANGUAGE_ARABIC){
                arrayOf("ذكر", "أنثى", "بدلا من ذلك لا أقول")
            }else{
                arrayOf("Male", "Female", "Rather Not Say")
            }
            val builder = AlertDialog.Builder(this)
            builder.setTitle(R.string.select_gender)
            builder.setItems(genderOptions) { _, which ->
                binding.gender.text = genderOptions[which]
            }
            builder.show()
        }

        binding?.dOB?.setOnClickListener {
            val mcurrentDate = Calendar.getInstance()
            val mYear = mcurrentDate[Calendar.YEAR]
            val mMonth = mcurrentDate[Calendar.MONTH]
            val mDay = mcurrentDate[Calendar.DAY_OF_MONTH]
            val maxDate = Calendar.getInstance()
            maxDate.add(Calendar.YEAR, -12)

            val mDatePicker = DatePickerDialog(
                this@EditSecondaryProfileActivity,
                { datepicker, selectedyear, selectedmonth, selectedday ->
                    mcurrentDate[Calendar.YEAR] = selectedyear
                    mcurrentDate[Calendar.MONTH] = selectedmonth
                    mcurrentDate[Calendar.DAY_OF_MONTH] = selectedday
                    val difference = mYear - selectedyear
                    if (difference >= 12) {
                        val sdf = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
                        binding?.dOB?.text = sdf.format(mcurrentDate.time)
                        try {
                            val d = sdf.parse(binding?.dOB?.text.toString())
                            dateMilliseconds = d.time.toString()
                        } catch (e: ParseException) {
                            Logger.w(e)
                        }
                    } else {
                        binding?.dOB?.text = ""
                        dateMilliseconds = ""
                        Toast.makeText(this, stringsHelper.instance()?.data?.config?.popup_date_difference, Toast.LENGTH_SHORT).show()
                    }
                }, mYear, mMonth, mDay
            )
            mDatePicker.datePicker.maxDate = maxDate.timeInMillis
            mDatePicker.updateDate(maxDate.get(Calendar.YEAR), maxDate.get(Calendar.MONTH), maxDate.get(Calendar.DAY_OF_MONTH))
            mDatePicker.show()
        }


        updateGenresTitleListener = object : UpdateGenresTitleListener {
            override fun onUpdateGenresTitleListener(genresItems: String?) {
                binding.genreBottomSheetBtn.text = genresItems
            }
        }

        binding.checkedImageView.setOnClickListener {
            openGallery()
        }

        binding.parentLockSwitch.setOnCheckedChangeListener { buttonView, isChecked ->
            if (buttonView.isPressed){
                parentalLockEnable = isChecked
            }
        }

        if (!parentPrimaryAccount) {
            if (getPrimaryUserData()?.data?.customData?.parentalPinEnabled != TRUE_VALUE) {
                binding.parentalLockParentView.visibility = View.VISIBLE
            }
        }

        verifyOtpCallbackListener = object : VerifyOtpCallbackListener {
            override fun onSuccessListener(otpText: String?) {}
            override fun callAgainVerifyOtpApi() {}
            override fun onSetPinClickListener(encodedOtpText: String) {
                updateParentProfile(encodedOtpText)
            }
        }

        binding.userProfileImage.setOnClickListener {
            openGallery()
        }
    }

    private val userNameTextWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        override fun afterTextChanged(s: Editable?) {
            when {
                binding.profileUserName.hasFocus() -> validateEmptyUserName() && validateUniqueUserName()
            }
        }
    }

    private fun validateEmptyUserName(): Boolean {
        var check = false
        profileUserName = binding.profileUserName.text.toString().trim()
        if (StringUtils.isNullOrEmptyOrZero(profileUserName)) {
            binding.errorUserName?.show()
            binding.errorUserName?.text = resources.getString(R.string.popup_empty_username)
            binding.profileUserName.setBackgroundResource(R.drawable.error_input_background)
        } else {
            check = true
            binding.errorUserName?.hide()
            binding.profileUserName.setBackgroundResource(R.drawable.signup_input_gradient)
        }
        return check
    }

    private fun validateUniqueUserName(): Boolean {
        var check = false
        profileUserName = binding.profileUserName.text.toString().trim()
        if (!StringUtils.isNullOrEmptyOrZero(profileUserName)) {
            check = true
        }
        return check
    }

    private fun callUpdateProfileApi(userName: String) {
        if (intent.hasExtra(AppConstants.BUNDLE_EDIT_PROFILE)) {
            if (intent.getBooleanExtra(AppConstants.PRIMARY_ACCOUNT, true)) {
                updatePrimaryAccount(userName)
            } else {
                updateSecondaryProfile(userName)
            }
        } else {
             if (parentalLockEnable == true){
                 if (getPrimaryUserData()?.data?.customData?.parentalPin?.isNotEmpty() == true) {
//                     addSecondaryProfile(userName)
                     uniqueUserNameApi(true)
                 }
                 else {
                     CustomMultiProfileDialog.createPinDialog(this, verifyOtpCallbackListener)
                 }
             } else {
//                addSecondaryProfile(userName)
                 uniqueUserNameApi(true)
           }
        }
    }

    private fun updatePrimaryAccount(userName: String) {
        binding.pBar.visibility = View.VISIBLE
          val updatedPrimaryData = secondaryProfileData?.copy(
              name = userName,
              dateOfBirth = dateMilliseconds,
              profilePicURL = userImageUrl,
              userName = binding.profileUserName.text.toString(),
              gender = binding.gender.text.toString().uppercase(),
              bio = binding.addBio.text.toString().uppercase()
          )
         val authToken = KsPreferenceKeys.getInstance().appPrefAccessToken
        registrationLoginViewModel?.updateParentProfile(updatedPrimaryData, authToken)?.observe(this){ primaryAccountResponse ->
            binding.pBar.visibility = View.GONE
            if (primaryAccountResponse != null) {
                Log.d("primaryAccountResponse", Gson().toJson(primaryAccountResponse))
                finishCurrentActivity()
            }
        }
    }

    private fun getGenreListApi() {
        binding.pBar.visibility = View.VISIBLE
        homeViewModel?.getSearchGenres(GENRES, CUSTOM)?.observe(this) { genreListData ->
            binding.pBar.visibility = View.GONE
            if (genreListData != null) {
                if (genreListData.data?.items?.isNotEmpty() == true) {
                    allGenresList = genreListData.data.items
                    if (intent.getBooleanExtra(AppConstants.ADD_SECONDARY_ACCOUNT, false)) {
                        binding.genreBottomSheetBtn.text = getAllGenresItems()
                    }
                    else{
                       binding.genreBottomSheetBtn.text = showPreviousSelectedGenres()
                    }
                    openGenresBottomSheet()
                } else {
                    binding.genreBottomSheetBtn.visibility = View.GONE
                }
            }
        }
    }

    private fun updateSecondaryProfile(updatedName: String) {
        binding.pBar.visibility = View.VISIBLE
        secondaryProfileData?.name = updatedName
        secondaryProfileData?.profilePicURL = userImageUrl
        secondaryProfileData?.gender = binding.gender.text.toString().uppercase()
        secondaryProfileData?.userName = binding.profileUserName.text.toString()
        secondaryProfileData?.bio = binding.addBio.text.toString()
        secondaryProfileData?.dateOfBirth = dateMilliseconds
        secondaryProfileData?.preferenceSettings?.genresIds = getEditGenresList()
        secondaryProfileData?.customData?.parentalPinEnabledAccount = parentalLockEnable.toString()
        registrationLoginViewModel?.updateSecondaryProfile(secondaryProfileData)?.observe(this) { updateSecondaryProfileResponse ->
                binding.pBar.visibility = View.GONE
                if (updateSecondaryProfileResponse != null) {
                    finishCurrentActivity()
                }
            }
    }


    private fun getPreviousSelectedGenres(): HashSet<String?>? {
        if (selectedGenresTitle?.isEmpty() == true) {
            if (allGenresList?.isNotEmpty() == true) {
                allGenresList?.forEachIndexed { _, genresItem ->
                    if (secondaryProfileData?.preferenceSettings?.genresIds?.isNotEmpty() == true) {
                        secondaryProfileData?.preferenceSettings?.genresIds?.forEachIndexed { _, id ->
                            if (genresItem?.id == id) {
                                selectedGenresTitle?.add(genresItem?.title)
                            }
                        }
                    }
                }
            }
        }
        return selectedGenresTitle
    }

    @SuppressLint("SuspiciousIndentation")
    private fun showPreviousSelectedGenres(): String {
        var genresSubString:String? = null
            if (getPreviousSelectedGenres()?.isNotEmpty() == true) {
                val genresBuilder = StringBuilder()
                getPreviousSelectedGenres()?.forEachIndexed { index, genresTitle ->
                    if (index > 0) {
                        genresBuilder.append(", ")
                    }
                    genresBuilder.append(genresTitle)
                }
                genresSubString = genresBuilder.toString()
            }
            else{
                genresSubString = ""
            }
        return genresSubString
    }


    private fun getEditGenresList(): List<Int?>? {
        return if (genresListAdapter?.getLoadedGenresList() == true) {
            genresListAdapter?.getAllSelectedGenresIds()
        } else {
            secondaryProfileData?.preferenceSettings?.genresIds
        }
    }

    private fun getAddGenresList(): List<Int?>? {
        return if (genresListAdapter?.getLoadedGenresList() == true) {
            genresListAdapter?.getAllSelectedGenresIds()
        } else {
            getAllGenresIds()
        }
    }

   private fun getAllGenresIds(): ArrayList<Int?> {
       allGenresList?.forEachIndexed { _, item ->
           addTimeGenresList.add(item?.id)
       }
       return addTimeGenresList
   }

    private fun finishCurrentActivity() {
        val intent = Intent().apply {
            putExtra(AppConstants.UPDATE_PRIMARY_PROFILE, true)
        }
        setResult(RESULT_OK, intent)
        finish()
    }

    private fun deleteSecondaryProfileApi() {
        binding.pBar.visibility = View.VISIBLE
        registrationLoginViewModel?.deleteSecondaryProfile(secondaryProfileData?.accountId)
            ?.observe(this) { deleteProfileResponse ->
                binding.pBar.visibility = View.GONE
                if (deleteProfileResponse != null) {
                    Log.d("deleteProfileResponse", Gson().toJson(deleteProfileResponse))
                    finishCurrentActivity()
                }
            }
    }

    private fun addSecondaryProfile(userName: String?) {
        binding.pBar.visibility = View.VISIBLE
        val addProfileRequestBody = AddProfileRequestBody()
        addProfileRequestBody.preferenceSettings = PreferenceSettings()
        addProfileRequestBody.customData = CustomDatas()
        addProfileRequestBody.dateOfBirth = dateMilliseconds
        addProfileRequestBody.name = userName
        addProfileRequestBody.userName = binding.profileUserName.text.toString()
        addProfileRequestBody.bio = binding.addBio.text.toString()
        addProfileRequestBody.gender = binding.gender.text.toString().uppercase()
        addProfileRequestBody.customData?.parentalPinEnabledAccount = parentalLockEnable
        addProfileRequestBody.profilePicURL = userImageUrl
        addProfileRequestBody.preferenceSettings?.genresIds = getAddGenresList()
        registrationLoginViewModel?.addSecondaryProfile(addProfileRequestBody)?.observe(this) { addSecondaryProfileResponse ->
                binding.pBar.visibility = View.GONE
            when (addSecondaryProfileResponse.responseCode) {
                2000 -> if (addSecondaryProfileResponse != null) {
                    Log.d("addSecondaryProfile", Gson().toJson(addSecondaryProfileResponse))
                    finishCurrentActivity()
                }
                4901 -> showToast(this, addSecondaryProfileResponse.debugMessage.toString())
            }

            }
    }

    private fun openGenresBottomSheet() {
        bottomSheetDialog = BottomSheetDialog(this)
        val binding = ItemGenresBottomSheetLayoutBinding.inflate(LayoutInflater.from(this))
        bottomSheetDialog?.setContentView(binding.root)
        val genresIdsList = secondaryProfileData?.preferenceSettings?.genresIds
        if (byDefaultAllGenresSelect)
            binding.allSelectGenresBtn.isChecked =  byDefaultAllGenresSelect
        else
            binding.allSelectGenresBtn.isChecked = allGenresList?.size == genresIdsList?.size
        binding.allSelectGenresBtn.text = getString(R.string.select_genre_text)
        genresListAdapter = GenresListAdapter(allGenresList, genresIdsList, byDefaultAllGenresSelect, updateGenresTitleListener)
        binding.genresListRecyclerView.adapter = genresListAdapter
        binding.allSelectGenresBtn.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                genresListAdapter?.allGenresSelectedItems(true)
            } else {
                genresListAdapter?.allGenresSelectedItems(false)
            }
        }
    }

    private fun uniqueUserNameApi(callCreateSecondaryProfile : Boolean){
        val jsonObject = JsonObject()
        jsonObject.addProperty("userName", profileUserName)
        registrationLoginViewModel?.uniqueUserNameApi(jsonObject)?.observe(this) {
            Logger.d("DataUniqueUser", Gson().toJson(it))
            if (it != null) {
                if (it.data?.exists == true) {
                    binding.pBar.hide()
                    binding.errorUserName?.show()
                    binding.errorUserName?.text = resources.getString(R.string.error_unique_user_name)
                    binding.profileUserName.setBackgroundResource(R.drawable.error_input_background)
//                } else if(binding?.userName?.text.toString().trim().matches(userNameRegex)){
                } else if(it.responseCode == 400){
                    binding.pBar.hide()
                    binding.errorUserName?.show()
                    binding.errorUserName?.text = resources.getString(R.string.error_username_cannot_contain_special_char)
                    binding.profileUserName.setBackgroundResource(R.drawable.error_input_background)
                }else {
                    if (callCreateSecondaryProfile){
                        addSecondaryProfile(profileUserName)
                    }
                    binding.errorUserName?.hide()
                    binding.profileUserName.setBackgroundResource(R.drawable.signup_input_gradient)
                }
            }
        }
    }

    @SuppressLint("SuspiciousIndentation")
    private fun setImageWithGlide(imageUrl:String?){
         if (!imageUrl.isNullOrEmpty()) {
             binding.userProfileImage.setUserImageWithGlide(this, imageUrl)
         } else {
             binding.userProfileImage.setImageResource(R.drawable.profile_avtar_logo)
         }
    }

    private fun convertMillisecond(dateOfBirth: Any?): String {
        return if (dateOfBirth != null) {
            val df = DecimalFormat("#")
            df.maximumFractionDigits = 0
            val l = df.format(dateOfBirth).toLong()
             l.toString()
        }
        else{
            ""
        }
    }

    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val commonDialogFragment = CommonDialogFragment.newInstanceWithCancelBtn(title, description,actionBtn, cancelBtn, true)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(supportFragmentManager, AppConstants.MESSAGE)
    }

    companion object {
        const val GENRES = "GENRES"
        const val CUSTOM = "CUSTOM"
        const val TRUE_VALUE = "true"
        private val REQUEST_CODE_PICK_IMAGE = 1001
        private val REQUEST_CODE_PERMISSION = 2001
    }

    override fun onActionBtnClicked() {
        deleteSecondaryProfileApi()
    }

    override fun onCancelBtnClicked() {

    }

    private fun getPrimaryUserData(): UserProfileResponse? {
        return KsPreferenceKeys.getInstance()?.getUserProfile()
    }

    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == REQUEST_CODE) {
            if (data?.hasExtra(AppConstants.GET_IMAGE_URL) == true){
                userImageUrl = data.getStringExtra(AppConstants.GET_IMAGE_URL)
                setImageWithGlide(userImageUrl)
            }
        }

        if (requestCode == REQUEST_CODE_PICK_IMAGE && resultCode == RESULT_OK && data != null) {
            val imageUri: Uri? = data.data
            binding.userProfileImage.setImageURI(imageUri)
            if (imageUri != null) {
                try {
                    val filePathFromURI: String = FileUtil.getFilePathFromImageURI(this, imageUri)
                    val imageFilePath = File(filePathFromURI)
                    showLoading(binding.pBar, true)
                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val compressedFile = FileUtil.compressImageFile(imageFilePath, Constants.COMPRESS_IMAGE_SIZE, Constants.COMPRESS_IMAGE_WIDTH, Constants.COMPRESS_IMAGE_HEIGHT)
                            withContext(Dispatchers.Main) {
                                setFileToUpload(compressedFile)
                            }
                        } catch (e: Exception) {
                            withContext(Dispatchers.Main) {
                                showLoading(binding.pBar, false)
                            }
                            Logger.w(e)
                        }
                    }

                } catch (ex: Exception) {
                    showLoading(binding.pBar, false)
                    Logger.w(ex)
                }
            } else {
                Logger.w("unable to get the image")
            }
        }
    }

    private fun updateParentProfile(parentalPin:String?) {
        val userProfileData = Gson().fromJson(Gson().toJson(getPrimaryUserData()?.data), SecondaryProfileData::class.java)
        userProfileData?.customData?.parentalPin = parentalPin
        val authToken = KsPreferenceKeys.getInstance().appPrefAccessToken
        registrationLoginViewModel?.updateParentProfile(userProfileData, authToken)?.observe(this) { parentProfileResponse ->
            if (parentProfileResponse != null) {
                addSecondaryProfile(binding.userName.text.toString())
            }
            showToast(this, getString(R.string.set_parental_pin_successfully))
        }
    }

    private fun getAllGenresItems():String {
        val genresSubString:String?
        if (allGenresList?.isNotEmpty() == true) {
            val genresBuilder = StringBuilder()
            allGenresList?.forEachIndexed { index, genresTitle ->
                if (index > 0) {
                    genresBuilder.append(", ")
                }
                genresBuilder.append(genresTitle?.title)
            }
            genresSubString = genresBuilder.toString()
        }
        else{
            genresSubString = ""
        }
        return genresSubString
    }

    @SuppressLint("IntentReset")
    private fun openGallery() {
        val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }
        if (ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED) {
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            intent.type = "image/*"
            startActivityForResult(intent, REQUEST_CODE_PICK_IMAGE)
        } else {
            ActivityCompat.requestPermissions(this, arrayOf(permission), REQUEST_CODE_PERMISSION)
        }
    }

    private fun credentialsProvider() {
        val credentialsProvider = CognitoCachingCredentialsProvider(applicationContext, SDKConfig.IDENTITY_POOL_ID, SDKConfig.REGION)
        setAmazonS3Client(credentialsProvider)
    }
    private fun setAmazonS3Client(credentialsProvider: CognitoCachingCredentialsProvider) {
        val clientConfiguration = ClientConfiguration()
        clientConfiguration.maxErrorRetry = 10
        clientConfiguration.connectionTimeout = 50000 // default is 10 secs
        clientConfiguration.socketTimeout = 50000
        clientConfiguration.maxConnections = 500
        s3 = AmazonS3Client(credentialsProvider, clientConfiguration)
        s3?.setRegion(Region.getRegion(SDKConfig.REGION))
    }

    private fun transferObserverListener(transferObserver: TransferObserver) {
        transferObserver.setTransferListener(object : TransferListener {
            override fun onStateChanged(id: Int, state: TransferState) {
                if (state == TransferState.COMPLETED) {
                    dismissLoading(binding.pBar)
                }
            }

            override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {
                Logger.e("error", "Progress - ${bytesCurrent},${bytesTotal}");
            }

            override fun onError(id: Int, ex: java.lang.Exception) {
                Logger.e("error", "error");
            }
        })
    }

    private fun setTransferUtility() {
        transferUtility = TransferUtility.builder().s3Client(s3).context(applicationContext).build()
    }

    private fun getCurrentTimeStamp(): Long {
        return System.currentTimeMillis() / 1000
    }

    private fun setFileToUpload(fileToUpload: File) {
        val imageToUpload = "Thumbnail_" + getCurrentTimeStamp() + "_Android" + ".jpg"
        userImageUrl = AppCommonMethod.getSdkConfigUrl() + imageToUpload
        via = "Gallery"
        KsPreferenceKeys.getInstance().saveVia(via)
        val transferObserver = transferUtility?.upload(
            SDKConfig.BUCKET_ID, "${SDKConfig.CLOUD_PATH_IMAGE_URL}${imageToUpload}",
            fileToUpload
        )

        if (transferObserver != null) {
            transferObserverListener(transferObserver)
        }
        showLoading(binding.pBar, false)
    }
}