package com.enveu.activities.secondary_profile

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.beanModelV3.searchGenres.SearchGenres

class GenresListAdapter(
    private val genresItems: List<SearchGenres.Data.Item?>?,
    private val genresIds: List<Int?>?,
    private var byDefaultAllGenresSelect:Boolean?,
    private val updateGenresTitleListener: UpdateGenresTitleListener
):RecyclerView.Adapter<GenresListAdapter.ViewHolder>(){

    private var isCheckedGenres:Boolean = false
    private var selectedGenresIdsList:HashSet<Int?>?= HashSet()
    private var selectedGenresTitle:HashSet<String?>?= HashSet()
    private var enableAllSelectedGenres:Boolean = false
    private var loadedGenresList:Boolean = false

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val rootLayout = LayoutInflater.from(parent.context).inflate(R.layout.items_genres_layout, parent, false)
        return ViewHolder(rootLayout)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val genresItemData = genresItems?.get(position)
        holder.apply {
            genresTitleText.text = genresItemData?.title
             if (byDefaultAllGenresSelect == true){
                 genresTitleText.isChecked = true
                 selectedGenresIdsList?.add(genresItemData?.id)
                 selectedGenresTitle?.add(genresItemData?.title)
             }
            if (enableAllSelectedGenres){
                if (isCheckedGenres){
                    genresTitleText.isChecked = isCheckedGenres
                    selectedGenresIdsList?.add(genresItemData?.id)
                    selectedGenresTitle?.add(genresItemData?.title)
                    updateGenresTitleListener.onUpdateGenresTitleListener(getGenresItemsTitle())
                } else {
                    genresTitleText.isChecked = isCheckedGenres
                    selectedGenresIdsList?.clear()
                    selectedGenresTitle?.clear()
                    updateGenresTitleListener.onUpdateGenresTitleListener(getGenresItemsTitle())
                }
            } else {
                if (genresIds?.isNotEmpty() == true){
                    for (genres in genresIds) {
                        if (genres == genresItemData?.id) {
                            genresTitleText.isChecked = true
                            selectedGenresIdsList?.add(genres)
                            selectedGenresTitle?.add(genresItemData?.title)
                        }
                    }
                }
            }
            loadedGenresList = true
        }
    }

    override fun getItemCount(): Int {
       return genresItems?.size?:0
    }

    fun getLoadedGenresList():Boolean{
        return loadedGenresList
    }

   inner class ViewHolder(view: View):RecyclerView.ViewHolder(view) {
        val genresTitleText:CheckBox = view.findViewById(R.id.genresTitleText)
       init {
           genresTitleText.setOnCheckedChangeListener { buttonView, isChecked ->
               if (buttonView.isPressed) {
                   if (isChecked){
                       selectedGenresIdsList?.add(genresItems?.get(bindingAdapterPosition)?.id)
                       selectedGenresTitle?.add(genresItems?.get(bindingAdapterPosition)?.title)
                   }
                   else{
                       selectedGenresIdsList?.remove(genresItems?.get(bindingAdapterPosition)?.id)
                       selectedGenresTitle?.remove(genresItems?.get(bindingAdapterPosition)?.title)
                   }
                   updateGenresTitleListener.onUpdateGenresTitleListener(getGenresItemsTitle())
               }
           }
       }
    }
    
    fun getGenresItemsTitle(): String {
        val genresSubString:String?
        if (selectedGenresTitle?.isNotEmpty() == true) {
            val genresBuilder = StringBuilder()
            selectedGenresTitle?.forEachIndexed { index, genresTitle ->
                if (index > 0) {
                    genresBuilder.append(", ")
                }
                genresBuilder.append(genresTitle)
            }
            genresSubString = genresBuilder.toString()
        }
        else{
            genresSubString = ""
        }
       return genresSubString
    }

      fun getAllSelectedGenresIds(): ArrayList<Int?> {
          val genresItems:ArrayList<Int?> = ArrayList()
          selectedGenresIdsList?.let { genresItems.addAll(it) }
         return genresItems
     }

    @SuppressLint("NotifyDataSetChanged")
    fun allGenresSelectedItems(isChecked: Boolean){
        isCheckedGenres = isChecked
        enableAllSelectedGenres = true
        byDefaultAllGenresSelect = false
        notifyDataSetChanged()
    }
}