package com.enveu.activities.secondary_profile

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import com.enveu.R
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.baseModels.BaseActivity
import com.enveu.beanModelV3.mutli_profile_response.SecondaryProfileData
import com.enveu.databinding.ActivityParentalControlBinding
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show
import com.enveu.utils.showToast
import com.google.gson.Gson

class ParentalControlActivity : BaseActivity() {
    private var viewModel: RegistrationLoginViewModel? = null
    private var userProfileData: SecondaryProfileData? = null
    private lateinit var verifyOtpCallbackListener: VerifyOtpCallbackListener
    private var generatedOtpToken: String? = null
    private var authToken: String? = null
    private var callSetPinEnable:Boolean = false
    private lateinit var binding: ActivityParentalControlBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_parental_control)
        initUI()
    }

    private fun initUI() {
        viewModel = ViewModelProvider(this)[RegistrationLoginViewModel::class.java]
        binding.toolbar.backLayout.setOnClickListener { finish() }
        rotateImageLocaleWise(binding.toolbar.backArrow)
        binding.toolbar.searchIcon.visibility = View.GONE
        val preference = KsPreferenceKeys.getInstance()
        authToken = preference.appPrefAccessToken
        hitUserProfileApi(authToken)

        verifyOtpCallbackListener = object : VerifyOtpCallbackListener {
            override fun onSuccessListener(otpText: String?) {
                validateParentalLockOtp(otpText)
            }

            override fun onSetPinClickListener(encodedOtpText: String) {
                updateParentProfile(userProfileData?.customData?.parentalPinEnabled, encodedOtpText, false)
            }

            override fun callAgainVerifyOtpApi() {
                generateOtpParentalPin(false)
            }
        }

        binding.parentLockSwitch.setOnCheckedChangeListener { buttonView, isChecked ->
            if (buttonView.isPressed) {
                if (isChecked){
                    if (userProfileData?.customData?.parentalPinEnabled  == false && userProfileData?.customData?.parentalPin?.isEmpty() == true) {
                        CustomMultiProfileDialog.createPinDialog(this, verifyOtpCallbackListener)
                    }
                   else {
                        updateParentProfile(true, userProfileData?.customData?.parentalPin, true)
                    }
                } else {
                    generateOtpParentalPin(true)
                }
            }
        }
        binding.setPinBtn.setOnClickListener {
            CustomMultiProfileDialog.createPinDialog(this, verifyOtpCallbackListener)
        }
    }

    private fun hitUserProfileApi(authToken: String?) {
         binding.pBar.show()
         viewModel?.hitUserProfile(this, authToken)?.observe(this) { profileResponse ->
            binding.pBar.hide()
            if (profileResponse != null) {
                userProfileData = Gson().fromJson(Gson().toJson(profileResponse?.data), SecondaryProfileData::class.java)
                binding.parentLockSwitch.isChecked = userProfileData?.customData?.parentalPinEnabled == true
                if (userProfileData?.customData?.parentalPin?.isNotEmpty() == true) {
                    binding.setPinBtn.text = getString(R.string.update_pin)
                    callSetPinEnable = true
                }
                else{
                    binding.setPinBtn.text = getString(R.string.set_pin)
                    callSetPinEnable = false
                }
            }
        }
    }

    private fun updateParentProfile(isCheckedParentalLock: Boolean?, parentalPin:String?, setParentLock:Boolean){
        binding.pBar.show()
        userProfileData?.customData?.parentalPinEnabled = isCheckedParentalLock
        userProfileData?.customData?.parentalPin = parentalPin
        viewModel?.updateParentProfile(userProfileData, authToken)?.observe(this) { parentProfileResponse ->
            binding.pBar.hide()
            if (parentProfileResponse != null) {
                userProfileData = Gson().fromJson(Gson().toJson(parentProfileResponse.data), SecondaryProfileData::class.java)
                if (isCheckedParentalLock != null) {
                    binding.parentLockSwitch.isChecked = isCheckedParentalLock
                }
                  showToastMessage(setParentLock, isCheckedParentalLock)
            }
        }
    }

    private fun showToastMessage(setParentLock: Boolean, isCheckedParentalLock: Boolean?) {
        if (setParentLock){
            if (isCheckedParentalLock == true)
                showToast(this, getString(R.string.parental_lock_enable))
            else
                showToast(this, getString(R.string.parental_lock_disable))
        }
        else {
            if (callSetPinEnable) {
                showToast(this, getString(R.string.update_parental_pin_successfully))
            }
            else {
                binding.setPinBtn.text = getString(R.string.update_pin)
                showToast(this, getString(R.string.set_parental_pin_successfully))
            }
        }
    }

    private fun generateOtpParentalPin(openBottomSheet:Boolean){
        binding.pBar.show()
        binding.parentLockSwitch.isChecked = true
        viewModel?.generateOtpParentalPin(userProfileData?.email.toString())?.observe(this) { generateOtpParentalResponse ->
            binding.pBar.hide()
                if (generateOtpParentalResponse != null) {
                    generatedOtpToken = generateOtpParentalResponse.data?.token
                    if (openBottomSheet){
                        CustomMultiProfileDialog.disableParentLockPin(this, userProfileData?.email.toString(), verifyOtpCallbackListener)
                    }
                }
         }
    }

    private fun validateParentalLockOtp(otpText: String?) {
        binding.pBar.show()
        inValidOtpData()
        viewModel?.validateParentalLockOtp(generatedOtpToken, otpText)?.observe(this) { validateParentalOtpResponse ->
            binding.pBar.hide()
                if (validateParentalOtpResponse != null) {
                    CustomMultiProfileDialog.dialogDismiss()
                    Log.d("updateParentProfileRes", Gson().toJson(validateParentalOtpResponse))
                    updateParentProfile(false, userProfileData?.customData?.parentalPin, true)
                }
        }
    }

    private fun inValidOtpData(){
        viewModel?.inValidOtpData()?.observe(this){ inValidOtpResponse->
            if (inValidOtpResponse.equals("IN_Valid_Otp")){
               showToast(this, getString(R.string.popup_enter_valid_otp))
            }
        }
    }
}