package com.enveu.activities.secondary_profile

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Base64
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.settings.UserInterestActivity
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.baseModels.BaseActivity
import com.enveu.beanModel.userProfile.CustomData
import com.enveu.beanModel.userProfile.UserProfileResponse
import com.enveu.beanModelV3.mutli_profile_response.SecondaryProfileData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.databinding.ActivitySecondaryProfileBinding
import com.enveu.databinding.MultipleProfileConfirmationPinLayoutBinding
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show
import com.enveu.utils.showToast
import com.google.gson.Gson


class SecondaryProfileActivity : BaseActivity() {
    private var registrationLoginViewModel: RegistrationLoginViewModel? = null
    private var profileItemsList: ArrayList<SecondaryProfileData?>? = ArrayList()
    private val REQUEST_CODE: Int = 1002
    private var authToken: String? = null
    private lateinit var binding: ActivitySecondaryProfileBinding
    private var preference: KsPreferenceKeys? = null
    private var enableEditProfile: Boolean = true
    private lateinit var verifyOtpCallbackListener: VerifyOtpCallbackListener
    private lateinit var createPinCallListener: VerifyOtpCallbackListener
    private lateinit var secondaryProfileListener: SecondaryProfileListener
    private var hideAddProfileSection: Boolean? = true
    private var generatedOtpToken: String? = null
    private var profileSwitchingPosition = -1
    private var mainProfileAuthToken: String? = null
    private var secondaryProfileAdapter: SecondaryProfileAdapter? = null
    private var primaryAccountProfileData: SecondaryProfileData? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_secondary_profile)
        initUI()
    }

    private fun initUI() {
        registrationLoginViewModel = ViewModelProvider(this)[RegistrationLoginViewModel::class.java]
        preference = KsPreferenceKeys.getInstance()
        hideAddProfileSection = intent.hasExtra(AppConstants.HIDE_BACK_BTN)
        authToken = preference?.appPrefAccessToken
        val isLoggedInUser = preference?.appPrefLoginStatus
        if (isLoggedInUser.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            getSecondaryProfileData(preference?.userProfile, false)
        }
        if (intent.hasExtra(AppConstants.HIDE_BACK_BTN)) {
            hideAddProfileSection = true
            binding.toolbar.backLayout.visibility = View.GONE
            binding.manageProfileBtn.visibility = View.GONE
        } else {
            binding.toolbar.backLayout.visibility = View.VISIBLE
            binding.manageProfileBtn.visibility = View.VISIBLE
            rotateImageLocaleWise(binding.toolbar.backArrow)
        }
        binding.toolbar.backLayout.setOnClickListener {
            finishCurrentActivity()
        }
        binding.toolbar.searchIcon.visibility = View.GONE

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (intent.hasExtra(AppConstants.HIDE_BACK_BTN)){
                    ActivityLauncher.getInstance().homeActivity(this@SecondaryProfileActivity, HomeActivity::class.java)
                }
                else {
                    finishCurrentActivity()
                }
            }
        })

        secondaryProfileListener = object : SecondaryProfileListener {
            override fun onSelectProfileClickListener(position: Int) {
                if (profileItemsList?.get(position)?.id != preference?.userId){
                    if (getPrimaryUserData()?.parentalPinEnabled == TRUE_VALUE || getCurrentlyUserData()?.customData?.parentalPinEnabledAccount == TRUE_VALUE)
                        openVerifyPinDialog(position)
                    else
                        switchAnotherProfile(position, authToken, false)
                } else {
                    navigateHomeScreen(position)
                }
            }

            override fun onAddProfileClickListener() {
                addSecondaryProfileScreen()
            }

            override fun onEditProfileClickListener(position: Int) {
                editSecondaryProfileScreen(position)
            }
        }

        binding.manageProfileBtn.setOnClickListener {
            if (enableEditProfile) {
                secondaryProfileAdapter?.editProfileNotify(enableEditProfile)
                binding.manageProfileBtn.text = getString(R.string.done_btn_text)
                enableEditProfile = false
            } else {
                secondaryProfileAdapter?.editProfileNotify(enableEditProfile)
                binding.manageProfileBtn.text = getString(R.string.manage_profile_sub_heading)
                enableEditProfile = true
            }
        }

        createPinCallListener = object : VerifyOtpCallbackListener {
            override fun onSuccessListener(otpText: String?) {}
            override fun onSetPinClickListener(encodedOtpText: String) {
                primaryAccountProfileData?.customData?.parentalPin = encodedOtpText
                val newAuthToken: String? = if (preference?.isPrimaryAccountUser != true){
                    if (profileSwitchingPosition == 0){
                        switchAnotherProfile(profileSwitchingPosition, authToken, true)
                        BLACK_STRING
                    }
                    else{
                        mainProfileAuthToken
                    }
                } else {
                    authToken
                }
                if (!newAuthToken.isNullOrEmpty()){
                    updateParentProfile(newAuthToken, primaryAccountProfileData)
                }
            }

            override fun callAgainVerifyOtpApi() {

            }
        }

        verifyOtpCallbackListener = object : VerifyOtpCallbackListener {
            override fun onSuccessListener(otpText: String?) {
                validateParentalLockOtp(otpText)
            }

            override fun onSetPinClickListener(encodedOtpText: String) {

            }

            override fun callAgainVerifyOtpApi() {
                generateOtpParentalPin(false)
            }
        }
    }

    private fun finishCurrentActivity(){
        val intent = Intent().apply {
            putExtra(AppConstants.UPDATE_MULTI_PROFILE, true)
        }
        setResult(RESULT_OK, intent)
        finish()
    }


    private fun updateParentProfile(authToken: String?, secondaryProfileData: SecondaryProfileData?) {
        registrationLoginViewModel?.updateParentProfile(secondaryProfileData, authToken)?.observe(this) { userProfileData ->
                if (userProfileData != null) {
                    switchAnotherProfile(profileSwitchingPosition, authToken, false)
                }
            }
    }

    private fun editSecondaryProfileScreen(position: Int) {
        val primaryAccount = position == 0
        val currentlyPrimaryAccount = profileItemsList?.get(position)?.id == preference?.userId
        Intent(this, EditSecondaryProfileActivity::class.java).also {
            it.putExtra(AppConstants.BUNDLE_EDIT_PROFILE, profileItemsList?.get(position))
            it.putExtra(AppConstants.PRIMARY_ACCOUNT, primaryAccount)
            it.putExtra(AppConstants.CURRENTLY_PRIMARY_ACCOUNT, currentlyPrimaryAccount)
            startActivityForResult(it, REQUEST_CODE)
        }
    }

    private fun addSecondaryProfileScreen() {
        Intent(this, EditSecondaryProfileActivity::class.java).also {
            it.putExtra(AppConstants.ADD_SECONDARY_ACCOUNT, true)
            startActivityForResult(it, REQUEST_CODE)
        }
    }

    private fun getSecondaryProfileData(userProfileData: UserProfileResponse?, updateCurrentProfile:Boolean) {
        binding.pBar.visibility = View.VISIBLE
        profileItemsList?.clear()
        primaryAccountProfileData = Gson().fromJson(Gson().toJson(userProfileData?.data), SecondaryProfileData::class.java)
        primaryAccountProfileData?.isParentAccount = true
        profileItemsList?.add(primaryAccountProfileData)
        Log.d("checkParentUserLog", Gson().toJson(profileItemsList))
        registrationLoginViewModel?.allSecondaryProfileList?.observe(this) { secondaryProfileList ->
            binding.pBar.visibility = View.GONE
            if (secondaryProfileList != null) {
                setSecondaryProfileAdapter(secondaryProfileList.data, updateCurrentProfile)
            }
        }
    }

    private fun switchAnotherProfile(position: Int, authToken: String?, viaForgotPin:Boolean){
        binding.pBar.visibility = View.VISIBLE
        registrationLoginViewModel?.switchAnotherProfile(authToken, profileItemsList?.get(position))?.observe(this) { switchProfileResponse ->
                if (switchProfileResponse != null){
                    if (!viaForgotPin){
                        navigateHomeScreen(position)
                    }
                    else{
                        registrationLoginViewModel?.updateParentProfile(primaryAccountProfileData, preference?.appPrefAccessToken)?.observe(this){ updateParentProfile ->
                            binding.pBar.visibility = View.GONE
                            if (updateParentProfile != null){
                                navigateHomeScreen(position)
                            }
                        }
                    }
                }
            else{
                binding.pBar.visibility = View.GONE
            }
        }
    }

    private fun navigateHomeScreen(position: Int){
        val isPrimaryUser = position == 0
        KsPreferenceKeys.getInstance().isPrimaryAccountUser = isPrimaryUser
        if (hideAddProfileSection == false){
            hitUserProfileApi()
        } else{
            binding.pBar.visibility = View.GONE
            ActivityLauncher.getInstance().homeActivity(this, HomeActivity::class.java)
        }
    }

    private fun hitUserProfileApi() {
        registrationLoginViewModel?.hitUserProfile(this, preference?.appPrefAccessToken)?.observe(this) { profileResponse ->
            binding.pBar.visibility = View.GONE
            if (profileResponse != null) {
                if (profileResponse.data.appUserInterest.isNullOrEmpty()) {
                    ActivityLauncher.getInstance().navigateUserInterestActivity(this@SecondaryProfileActivity, UserInterestActivity::class.java)
                }
                else{
                    ActivityLauncher.getInstance().homeActivity(this, HomeActivity::class.java)
                }
            }
        }
    }

    private fun setSecondaryProfileAdapter(profileDataList: List<SecondaryProfileData?>?, updateCurrentProfile: Boolean) {
        binding.manageProfileBtn.isEnabled = true
        binding.manageProfileBtn.isClickable = true
        profileDataList.let { if (it != null) { profileItemsList?.addAll(it) } }
        profileItemsList?.add(SecondaryProfileData(name = getString(R.string.add_profile_text)))
        secondaryProfileAdapter = SecondaryProfileAdapter(profileItemsList, preference?.userId, hideAddProfileSection, secondaryProfileListener)
        val layoutManager = object : GridLayoutManager(this, getSpanCount(profileItemsList?.size)) {
            override fun checkLayoutParams(layoutParams: RecyclerView.LayoutParams): Boolean {
                layoutParams.width = width / spanCount
                return true
            }
        }
        binding.rvSecondaryProfile.layoutManager = layoutManager
        binding.rvSecondaryProfile.adapter = secondaryProfileAdapter

        if (updateCurrentProfile){
            profileItemsList?.forEachIndexed { _, secondaryProfileData ->
                if (secondaryProfileData?.id == preference?.userId){
                    preference?.saveActiveProfileData(secondaryProfileData)
                }
            }
        }
    }

    private fun getSpanCount(listSize: Int?): Int {
        val getSpanCount = if (hideAddProfileSection == true && listSize == 2){
            1
        }
        else{
            2
        }
        return getSpanCount
    }

    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == REQUEST_CODE) {
            if (data?.hasExtra(AppConstants.UPDATE_PRIMARY_PROFILE) == true) {
                binding.manageProfileBtn.text = getString(R.string.manage_profile_sub_heading)
                enableEditProfile = true
                getSecondaryProfileData(preference?.userProfile, true)
            }
        }
    }

    private fun openVerifyPinDialog(position: Int) {
        val dialog = Dialog(this)
        val binding = MultipleProfileConfirmationPinLayoutBinding.inflate(LayoutInflater.from(this))
        dialog.setContentView(binding.root)
        dialog.window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT)
        setImageWithGlide(binding.userImage, profileItemsList?.get(position)?.profilePicURL)
        binding.crossIconBtn.setOnClickListener { dialog.dismiss() }
        rotateImageLocaleWise(binding.backArrow)
        binding.backLayout.setOnClickListener{
            dialog.dismiss()
        }
        binding.pinViewOtp.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}

                override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                    binding.pinViewOtp.setItemBackground(resources.getDrawable(R.drawable.pin_view_bg))
                    if (!TextUtils.isEmpty(binding.pinViewOtp.text)) {
                        if (binding.pinViewOtp.selectionEnd == 4) {
                            binding.verifyBtn.isEnabled = true
                            binding.pinViewOtp.isCursorVisible=false
                        } else {
                            binding.verifyBtn.isEnabled = false
                            binding.pinViewOtp.isCursorVisible=true
                        }
                    } else {
                        binding.verifyBtn.isEnabled = false
                        binding.pinViewOtp.isCursorVisible=true
                    }
                }
                override fun afterTextChanged(p0: Editable?) {}
            })

        binding.verifyBtn.setOnClickListener {
            if (binding.pinViewOtp.text.toString().isEmpty()) {
                showToast(this, getString(R.string.please_enter_pin))
            } else if (binding.pinViewOtp.text.toString().length < 4)
                showToast(this, getString(R.string.please_enter_pin))
            else {
                if (getPrimaryUserData()?.parentalPin?.equals(encodeString(binding.pinViewOtp.text.toString())) == true) {
                    dialog.dismiss()
                    switchAnotherProfile(position, authToken, false)
                } else {
                    showToast(this, getString(R.string.invalid_pin))
                }
            }
        }
        binding.forgotPinBtn.setOnClickListener {
            profileSwitchingPosition = position
            generateOtpParentalPin(true)
            dialog.dismiss()
        }
        dialog.show()
    }

    private fun generateOtpParentalPin(openBottomSheet: Boolean) {
        val parentUerEmail = primaryAccountProfileData?.email
        registrationLoginViewModel?.generateOtpParentalPin(parentUerEmail.toString())?.observe(this) { generateOtpParentalResponse ->
                if (generateOtpParentalResponse != null) {
                    generatedOtpToken = generateOtpParentalResponse.data?.token
                    if (openBottomSheet){
                        CustomMultiProfileDialog.disableParentLockPin(this, parentUerEmail.toString(), verifyOtpCallbackListener)
                    }
                }
            }
    }

    @SuppressLint("SuspiciousIndentation")
    private fun setImageWithGlide(userImage: ImageView, imageUrl: String?) {
        if (!imageUrl.isNullOrEmpty())
            Glide.with(userImage.context).asBitmap().load(imageUrl)
                .placeholder(R.drawable.profile_avtar_logo).apply(AppCommonMethod.optionsSearch)
                .into(userImage)
        else
            userImage.setImageResource(R.drawable.profile_avtar_logo)
    }

    private fun getPrimaryUserData(): CustomData? {
        return preference?.getUserProfile()?.data?.customData
    }

    private fun getCurrentlyUserData(): SecondaryProfileData? {
        return preference?.isActiveUserProfileData
    }

    private fun encodeString(originalString: String?): String {
        val encodedString = Base64.encodeToString(originalString?.toByteArray(), Base64.NO_WRAP)
        return encodedString
    }

    private fun validateParentalLockOtp(otpText: String?) {
        binding.pBar.show()
        inValidOtpData()
        registrationLoginViewModel?.validateParentalLockOtp(generatedOtpToken, otpText)?.observe(this){ validateParentalOtpResponse ->
                binding.pBar.hide()
                if (validateParentalOtpResponse != null) {
                     CustomMultiProfileDialog.dialogDismiss()
                    if (preference?.isPrimaryAccountUser != true && profileSwitchingPosition != 0) {
                        switchMainProfile()
                    }
                    CustomMultiProfileDialog.createPinDialog(this@SecondaryProfileActivity, createPinCallListener)
                }
            }
    }

    private fun switchMainProfile() {
        registrationLoginViewModel?.switchMainProfile(primaryAccountProfileData?.accountId)?.observe(this){ mainProfileAuthToken ->
                if (mainProfileAuthToken.isNotEmpty()) {
                    this.mainProfileAuthToken = mainProfileAuthToken
                }
        }
    }

    private fun inValidOtpData(){
        registrationLoginViewModel?.inValidOtpData()?.observe(this){ inValidOtpResponse->
            if (inValidOtpResponse.equals(IN_VALID_OTP)){
                showToast(this, getString(R.string.popup_enter_valid_otp))
            }
        }
    }

    companion object {
        const val TRUE_VALUE = "true"
        const val BLACK_STRING = ""
        const val IN_VALID_OTP = "IN_Valid_Otp"
    }
}