package com.enveu.activities.secondary_profile

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.beanModelV3.mutli_profile_response.SecondaryProfileData
import com.enveu.epg.context
import com.enveu.utils.Constants
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.getTenIntoTenImageUrl
import com.enveu.utils.setUserImageWithGlide

class SecondaryProfileAdapter(
    private val profileDataList: List<SecondaryProfileData?>?,
    private val userId: Int?,
    private val hideAddProfileSection: Boolean?,
    private val secondaryProfileListener: SecondaryProfileListener,
):RecyclerView.Adapter<SecondaryProfileAdapter.ViewHolder>() {

    private var enableEditProfile:Boolean = false

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val rootLayout = LayoutInflater.from(parent.context).inflate(R.layout.item_secondary_profile_layout, parent, false)
        return ViewHolder(rootLayout)
    }

    @SuppressLint("SuspiciousIndentation")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val profileData = profileDataList?.get(position)
        holder.apply {
            userNameTitle.text = profileData?.name
            addProfileTitle.text = profileData?.name
                if (!profileData?.profilePicURL.isNullOrEmpty())
                  Glide.with(context).load(profileData?.profilePicURL)
                      .placeholder(R.drawable.profile_avtar_logo)
                      .fallback(R.drawable.profile_avtar_logo)
                      .into(userImage)
                else userImage.setImageResource(R.drawable.profile_avtar_logo)
              if(getPositionCounter(position) == profileDataList?.size){
                if (profileDataList[0]?.id == userId){
                   if (hideAddProfileSection != true && profileDataList.size < 7) {
                        addProfileLayout.visibility = View.VISIBLE
                        mainProfileLayout.visibility = View.GONE
                   }
                    else{
                       addProfileLayout.visibility = View.GONE
                       mainProfileLayout.visibility = View.GONE
                    }
                }
                else{
                    addProfileLayout.visibility = View.GONE
                    mainProfileLayout.visibility = View.GONE
                }
            }
            else{
                addProfileLayout.visibility = View.GONE
                mainProfileLayout.visibility = View.VISIBLE
            }

            if (enableEditProfile){
                if (profileDataList?.get(0)?.id == userId) {
                    editProfileImage.visibility = View.VISIBLE
                }
                else{
                    if (profileData?.id == userId){
                        editProfileImage.visibility = View.VISIBLE
                        enableEditProfile = false
                    }
                }
            }
            else{
                editProfileImage.visibility = View.GONE
            }
            if (profileData?.id == userId){
                checkedImageView.visibility = View.VISIBLE
                checkedImageParentView.visibility = View.VISIBLE
            }
            else{
                checkedImageView.visibility = View.GONE
                checkedImageParentView.visibility = View.GONE
            }
        }
    }

    override fun getItemCount(): Int {
        return profileDataList?.size?:0
    }

    private fun getPositionCounter(position: Int):Int {
       return position+1
    }


    @SuppressLint("NotifyDataSetChanged")
    fun editProfileNotify(isChecked:Boolean){
        enableEditProfile = isChecked
        notifyDataSetChanged()
    }

   inner class ViewHolder(view:View):RecyclerView.ViewHolder(view) {
       val userNameTitle: TextView = view.findViewById(R.id.userNameTitle)
       val addProfileTitle: TextView = view.findViewById(R.id.addProfileTitle)
       val userImage: ImageView = view.findViewById(R.id.userImage)
       val editProfileImage: ImageView = view.findViewById(R.id.editProfileImage)
       val checkedImageView: ImageView = view.findViewById(R.id.checkedImageView)
       val mainProfileLayout: ConstraintLayout = view.findViewById(R.id.mainProfileLayout)
       val addProfileLayout: ConstraintLayout = view.findViewById(R.id.addProfileLayout)
       val checkedImageParentView: CardView = view.findViewById(R.id.checkedImageParentView)

       init {
           addProfileLayout.setOnClickListener {
               secondaryProfileListener.onAddProfileClickListener()
           }
           mainProfileLayout.setOnClickListener {
               secondaryProfileListener.onSelectProfileClickListener(bindingAdapterPosition)
           }
           editProfileImage.setOnClickListener {
               secondaryProfileListener.onEditProfileClickListener(bindingAdapterPosition)
           }
       }
   }
}