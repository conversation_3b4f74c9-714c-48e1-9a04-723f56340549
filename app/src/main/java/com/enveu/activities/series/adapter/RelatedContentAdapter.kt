package com.enveu.activities.series.adapter


import android.annotation.SuppressLint
import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.databinding.RelatedEpisodeListBinding
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.helpers.ImageHelper

class RelatedContentAdapter(val context: Activity,
    private val videoItemBeans: MutableList<EnveuVideoItemBean>,
    private val listner: EpisodeItemClick,
) : RecyclerView.Adapter<RelatedContentAdapter.SeasonViewHolder>() {
    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): SeasonViewHolder {
        val itemBinding: RelatedEpisodeListBinding = DataBindingUtil.inflate(
            LayoutInflater.from(viewGroup.context),
            R.layout.related_episode_list, viewGroup, false
        )
        itemBinding.colorsData = ColorsHelper
        return SeasonViewHolder(itemBinding)
    }

    override fun getItemCount(): Int {
        return videoItemBeans.size
    }

    override fun onBindViewHolder(holder: SeasonViewHolder, @SuppressLint("RecyclerView") position: Int) {
        holder.itemBinding.playlistItem = videoItemBeans[position]
        try {
            if (videoItemBeans[position].posterURL.equals("", ignoreCase = true)) {
                holder.itemBinding.imageTitle.visibility = View.VISIBLE
                holder.itemBinding.imageTitle.text = videoItemBeans[position].title
                holder.itemBinding.imageTitle.bringToFront()
            } else {
                holder.itemBinding.imageTitle.visibility = View.GONE
            }
        } catch (ignored: Exception) {

        }

        ImageHelper.getInstance(context).loadListImage(holder.itemBinding.itemImage, videoItemBeans[position].posterURL)
        holder.itemBinding.itemImage.setOnClickListener {
            listner.onItemClick(
                videoItemBeans[position],
                videoItemBeans[position].isPremium,
                position
            )
        }
    }

    interface EpisodeItemClick {
        fun onItemClick(assetId: EnveuVideoItemBean?, isPremium: Boolean, position: Int)
    }

    inner class SeasonViewHolder internal constructor(val itemBinding: RelatedEpisodeListBinding) : RecyclerView.ViewHolder(itemBinding.root)
}