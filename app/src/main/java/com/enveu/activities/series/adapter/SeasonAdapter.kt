package com.enveu.activities.series.adapter


import android.annotation.SuppressLint
import android.app.Activity
import android.content.res.Configuration
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.FirstEpisodeItem
import com.enveu.databinding.RowEpisodeListBinding
import com.enveu.fragments.newseriesui.NewSeriesEpisodesFragment
import com.enveu.utils.Constants
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.getImageUrl
import com.enveu.utils.getNineSixteenImageUrl
import com.enveu.utils.getSixteenNineImageUrl
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.downloads.OnDownloadClickInteraction
import com.enveu.utils.stringsJson.converter.StringsHelper

class SeasonAdapter(
    private val context: Activity,
    private val videoItemBeans: MutableList<EnveuVideoItemBean>,
    private var id: Int,
    private var currentAssetId: Int,
    private val listner: EpisodeItemClick,
    private val firstEpisodeItem: FirstEpisodeItem
) : RecyclerView.Adapter<SeasonAdapter.SeasonViewHolder>() {
    private val indexMap: HashMap<String, Int> = HashMap()
    private var onDownloadClickInteraction: OnDownloadClickInteraction? = null
    private var isFirstItemAdded = true
    private var isFromNewELF=false
    private val stringsHelper by lazy { StringsHelper }
    private fun buildIndexMap() {
        indexMap.clear()
        if (videoItemBeans.size > 0) {
            for ((index, videoItemBean) in videoItemBeans.withIndex()) {
                if (!videoItemBean.brightcoveVideoId.isNullOrEmpty()) {
                    indexMap[videoItemBean.brightcoveVideoId] = index
                }
            }
            notifyDataSetChanged()
        }
    }



    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): SeasonViewHolder {
        val itemBinding: RowEpisodeListBinding = DataBindingUtil.inflate(
            LayoutInflater.from(viewGroup.context),
            R.layout.row_episode_list, viewGroup, false
        )
        itemBinding.colorsData = ColorsHelper
        itemBinding.stringData = StringsHelper
        return SeasonViewHolder(itemBinding)
    }

    var clickBinding: RowEpisodeListBinding? = null

    init {
        if (context is OnDownloadClickInteraction) {
            onDownloadClickInteraction = context
        } else {
            Logger.w("$context does not implement OnDownloadClickInteraction")
        }
        buildIndexMap()
    }

    override fun onBindViewHolder(holder: SeasonViewHolder, @SuppressLint("RecyclerView") position: Int) {
        holder.itemBinding.playlistItem = videoItemBeans[position]
        if (isFirstItemAdded) {
            if (context is SeriesDetailActivity) {
                context.getFirstItem(videoItemBeans[0])
            }
            isFirstItemAdded = false
        }

        holder.itemBinding.titleWithSerialNo.text = videoItemBeans[position].title
//        ImageHelper.getInstance(context).loadListImage(holder.itemBinding.episodeImage, videoItemBeans[position].posterURL)
        ImageHelper.getInstance(context).loadListImage(holder.itemBinding.episodeImage, getSixteenNineImageUrl(videoItemBeans.get(position).posterURL,holder.itemBinding.episodeImage.context))

        if(isFromNewELF){
            when {
                isScreenWidth600dp() -> {
                    holder.itemBinding.description.visibility=View.VISIBLE
                }
                else -> {
                    if(NewSeriesEpisodesFragment.isShowMore){
                        holder.itemBinding.description.visibility=View.VISIBLE
                    }else{
                        holder.itemBinding.description.visibility=View.GONE
                    }
                }
            }
        }

        if (videoItemBeans[position].description.isNotBlank()) {
            holder.itemBinding.description.visibility = View.VISIBLE
            holder.itemBinding.description.text = videoItemBeans[position].description
        } else {
            holder.itemBinding.description.visibility = View.GONE
        }
        if (videoItemBeans[position].episodeNo != null) {
            val episodeNo = videoItemBeans[position].episodeNo
            val episodeNum = if (episodeNo is Number) {
                episodeNo.toInt()
            } else {
                episodeNo.toString()
            }
            if (videoItemBeans[position].duration.toString() != "0" && videoItemBeans[position].duration.toString() != "") {
                val d = videoItemBeans[position].duration.toDouble()
                val x = d.toLong() // x = 1234
               // val duration = AppCommonMethod.stringForTime(x) + " " + stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_minutes.toString(), context.getString(R.string.popup_minutes))
                val duration = AppCommonMethod.stringForTime(x)
                holder.itemBinding.durationAndEpisodesCountText.text = "E$episodeNum · $duration"
            } else {
                if (videoItemBeans[position].episodeNo != null){
                    holder.itemBinding.durationAndEpisodesCountText.visibility = View.VISIBLE
                    holder.itemBinding.durationAndEpisodesCountText.text = "E$episodeNum"
                }
                else{
                    holder.itemBinding.durationAndEpisodesCountText.visibility = View.GONE
                }
            }
        } else {
            if (videoItemBeans[position].duration.toString() != "0" && videoItemBeans[position].duration.toString() != "") {
                val d = videoItemBeans[position].duration.toDouble()
                val x = d.toLong() // x = 1234
             // val duration = AppCommonMethod.stringForTime(x) + " " + stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_minutes.toString(), context.getString(R.string.popup_minutes))
                val duration = AppCommonMethod.stringForTime(x)
                holder.itemBinding.durationAndEpisodesCountText.text = duration
            } else {
                holder.itemBinding.durationAndEpisodesCountText.visibility = View.GONE
            }
        }

//        if (videoItemBeans[position].id == currentAssetId) {
//            holder.itemBinding.nowPlaying.visibility = View.VISIBLE
//            holder.itemBinding.playIcon.visibility = View.GONE
//        } else {
//            holder.itemBinding.playIcon.visibility = View.VISIBLE
//            holder.itemBinding.nowPlaying.visibility = View.GONE
//        }

        holder.itemBinding.episodeImage.setOnClickListener(View.OnClickListener {
            if (videoItemBeans[position].id == currentAssetId) {
                return@OnClickListener
            }
            listner.onItemClick(videoItemBeans[position], videoItemBeans[position].isPremium, position)
        })
        holder.itemBinding.mainLay.setOnClickListener { view: View? ->
            Logger.d("positionIs" + videoItemBeans[position])
            id = videoItemBeans[position].id
            notifyDataSetChanged()
        }

    }

    override fun getItemCount(): Int {
        return videoItemBeans.size
    }


    fun setIsFromELF(elf:Boolean){
        this.isFromNewELF=elf
    }
    fun updateCurrentId(id: Int) {
        currentAssetId = id
    }

    private fun isScreenWidth600dp(): Boolean {
        val config: Configuration = context.resources.configuration
        val screenWidthDp = config.screenWidthDp
        return screenWidthDp >= 600
    }
    interface EpisodeItemClick {
        fun onItemClick(assetId: EnveuVideoItemBean?, isPremium: Boolean, position: Int)
    }

    inner class SeasonViewHolder internal constructor(val itemBinding: RowEpisodeListBinding) : RecyclerView.ViewHolder(itemBinding.root)
}