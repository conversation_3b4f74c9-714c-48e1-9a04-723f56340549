package com.enveu.activities.series.ui


import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.graphics.Typeface
import android.os.Bundle
import android.util.DisplayMetrics
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.ImageView
import android.widget.ScrollView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.customservices.EntBackgroundAudioActivity
import com.enveu.activities.detail.ContentMetaData
import com.enveu.activities.detail.adapter.CommonDetailAdapter
import com.enveu.activities.detail.viewModel.DetailViewModel
import com.enveu.activities.listing.listui.ListActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.ActivitySelectSubscriptionPlan
import com.enveu.activities.usermanagment.ui.EnterOTPActivity
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaConfig
import com.enveu.appLevelModel.MediaInfo
import com.enveu.appLevelModel.TabConfig
import com.enveu.appLevelModel.TabsConfig
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModel.selectedSeason.SelectedSeasonModel
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.bean_model_v2_0.listAll.AudioTrackListItem
import com.enveu.bean_model_v2_0.listAll.SeriesCustomData
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner
import com.enveu.callbacks.commonCallbacks.FirstEpisodeItem
import com.enveu.callbacks.commonCallbacks.MoreClickListner
import com.enveu.databinding.ActivitySeriesDetailBinding
import com.enveu.fragments.detailPageTabFragment.EnveuTabFragment
import com.enveu.fragments.dialog.AlertDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstance
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstanceWithCancelBtn
import com.enveu.fragments.newseriesui.NewSeriesEpisodesFragment
import com.enveu.fragments.newseriesui.NewSeriesEpisodesViewModel
import com.enveu.fragments.player.ui.UserInteractionFragment
import com.enveu.jwplayer.player.PlayerActivity
import com.enveu.jwplayer.player.PlayerFragment
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.response.ReelsContentItem
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.utils.Constants
import com.enveu.utils.Logger
import com.enveu.utils.ObjectHelper
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.moengage.core.internal.utils.isTablet
import kotlinx.coroutines.launch
import java.io.Serializable

class SeriesDetailActivity : BaseBindingActivity<ActivitySeriesDetailBinding?>(),
    AlertDialogFragment.AlertDialogListener,
    FirstEpisodeItem, CommonRailtItemClickListner, MoreClickListner,
    CommonDialogFragment.EditDialogListener {
    private var seriesId = 0
    private var seasonNumber = 0
    private var preference: KsPreferenceKeys? = null
    private var shimmerCounter = 0
    private var seriesDetailBean: EnveuVideoItemBean? = null
    private var enveuTabFragment : EnveuTabFragment?=null
    private var alertDialog: AlertDialog? = null
    private var isGeoBlocking = false
    private var playerFragment : PlayerFragment?= null
    private var audioTrackList: List<AudioTrackListItem>? = null
    private var newIntentCall = false
    private var seriesTittle = ""
    private var playerCallback : PlayerCallback?= null
    @JvmField
    var isSeasonData = false
    private var userInteractionFragment: UserInteractionFragment? = null
    private var keyword: String? = ""
    private var isUserVerified: String? = null
    private var isUserNotVerify = false
    private var isUserNotEntitle = false
    private var isLoggedIn = false
    private var playbackUrl: String? = null
    private var viewModel: DetailViewModel? = null
    private var token: String? = null
    private var isPremium = false
    private var refId: String? = ""
    private var currentEpisodeId = 0
    private var tittle = ""
    private var posterUrl = ""
    private var skipIntroStartTime = ""
    private var skipIntroEndTime = ""
    private var assetType = ""
    private var sku = ""
    private var mediaType = ""
    private var isSecondUiInit=false
    private var trailerUrl: String? = null
    private var trailerExternalRefId: String? = null
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    private var newSeriesEpisodesFragment:NewSeriesEpisodesFragment?=null
    private var nseriesepfrag:NewSeriesEpisodesFragment?=null
    private var tabsConfigList: ArrayList<TabsConfig>? = null
    private var tabConfig: ArrayList<TabConfig>? = null
    private var railCommonDataList: MutableList<RailCommonData> = ArrayList()
    private var commonDetailAdapter: CommonDetailAdapter? = null
    private var newSeriesEpisodesViewModel: NewSeriesEpisodesViewModel? = null
    private var displayHeight:Int = 0
    lateinit var customData: SeriesCustomData
    private var signedUrl = ""
    private var drmBaseUrl = ""
    private var drmToken = ""
    private var isFirstCallToGetFirstItem = true;
    companion object{
        var isEpisodes = false
        var seriesSeasons = hashMapOf<String,RailCommonData>()
        var seasonEpisodes = hashMapOf<String,List<EnveuVideoItemBean>>()
    }
    override fun inflateBindingLayout(inflater: LayoutInflater): ActivitySeriesDetailBinding {
        return ActivitySeriesDetailBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parserColor()
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        binding?.backImage?.let { rotateImageLocaleWise(it) }
        window.setBackgroundDrawableResource(R.color.app_bg_color)
        shimmerCounter = 0
        preference = KsPreferenceKeys.getInstance()
        if (preference?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
        }
        isUserVerified = preference?.isVerified
        setupUI(binding!!.llParent)
        seriesId = intent.getIntExtra("seriesId", 0)
        contentSlug = intent.getStringExtra(AppConstants.BUNDLE_CONTENT_SLUG)?:""
        onSeriesCreate()
        setOrientation()
        val displayMetrics = DisplayMetrics()
        val windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        displayHeight = displayMetrics.heightPixels
        binding!!.rvNewSeasonUi.visibility = View.GONE
    }

    private fun parserColor() {
        binding!!.stringData = stringsHelper
        binding!!.colorsData = colorsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
        binding!!.metaDetails.colorsData = colorsHelper
        binding!!.metaDetails.stringData = stringsHelper
    }

    override fun onPause() {
        dismissLoading(binding?.progressBar)
        super.onPause()
    }

    private fun onSeriesCreate() {
        if (shimmerCounter == 0) {
            callShimmer()
        }
        connectionObserver()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        shimmerCounter = 0
        setupUI(binding!!.llParent)
        seasonEpisodes.clear()
        seriesSeasons.clear()
        railCommonDataList.clear()
        seriesId = intent.getIntExtra("seriesId", 0)
        intent.getStringExtra("assetType")?.let {
            isEpisodes = it == AppConstants.EPISODES
        }
        newIntentCall = true
        if (playerFragment!= null){
            playerCallback?.stopPlayer()
        }
        newSeriesEpisodesFragment?.onDestroy()
        nseriesepfrag?.onDestroy()
        newSeriesEpisodesFragment=null
        nseriesepfrag=null
        onSeriesCreate()
    }


    private fun checkGeoBlocking() {
        try {
            viewModel!!.getGeoBlocking(currentEpisodeId.toString()).observe(this@SeriesDetailActivity) { response ->
                    if (response != null && response.data != null) {
                        if (response.data.isIsBlocked) {
                            isGeoBlocking = true
                        }
                    } else {
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
                }
        } catch (_: Exception) {

        }

    }


    private fun callShimmer() {
        shimmerCounter = 1
        binding!!.seriesShimmer.visibility = View.VISIBLE
        binding!!.mShimmer.colorsData = colorsHelper
        binding!!.mShimmer.seriesShimmerScroll1.isEnabled = false
        binding!!.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding!!.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding!!.noConnectionLayout.visibility = View.GONE
        lifecycleScope.launch {
            binding!!.mShimmer.sfShimmer1.startShimmer()
            binding!!.mShimmer.sfShimmer2.startShimmer()
            binding!!.mShimmer.sfShimmer3.startShimmer()
        }
        binding!!.mShimmer.flBackIconImage.bringToFront()
        binding!!.mShimmer.flBackIconImage.setOnClickListener { onBackPressed() }
    }

    fun stopShimmer() {
        if (isSeasonData) {
            isSeasonData = false
            // isRailData = false;
            binding!!.seriesShimmer.visibility = View.GONE
            binding!!.llParent.visibility = View.VISIBLE
            binding!!.newUiContainer.visibility=View.VISIBLE
            binding!!.newSeasonDetailRecyclerView.visibility=View.VISIBLE
            binding!!.noConnectionLayout.visibility = View.GONE
            lifecycleScope.launch {
                binding!!.mShimmer.sfShimmer1.startShimmer()
                binding!!.mShimmer.sfShimmer2.startShimmer()
                binding!!.mShimmer.sfShimmer3.startShimmer()
            }

        }
    }

    private fun modelCall() {
        binding!!.noConnectionLayout.visibility = View.GONE
        binding!!.player.visibility = View.VISIBLE
        binding!!.playerFooter.visibility = View.VISIBLE
        binding!!.flBackIconImage.visibility = View.VISIBLE
        binding!!.backImage.bringToFront()
        binding!!.flBackIconImage.bringToFront()
    }

    private fun connectionObserver() {
        connectionValidation(NetworkConnectivity.isOnline(this))
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (java.lang.Boolean.TRUE == aBoolean) {

            if (contentSlug.isNotEmpty()) {
                assetDetails
            } else if (seriesId != 0) {
                seriesDetail
            } else {
                commonDialog(
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                        getString(R.string.popup_this_content_not_available)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                        getString(R.string.popup_continue)
                    ),
                )
            }
        } else {
            noConnectionLayout()
        }
    }

    fun removeTab(position: Int) {
        enveuTabFragment?.removeTab(position)
    }

    override fun onResume() {
        super.onResume()
        dismissLoading(binding!!.progressBar)
        AppCommonMethod.isSeriesPage = true
        if (NetworkConnectivity.isOnline(this)) {
            Logger.d("isOnline")
            setUserInteractionFragment(seriesId)
        } else {
            noConnectionLayout()
        }
        if (preference != null && userInteractionFragment != null) {
            AppCommonMethod.callSocialAction(preference!!, userInteractionFragment)
        }
    }

    var railInjectionHelper: RailInjectionHelper? = null
    private val seriesDetail: Unit
        get() {
            modelCall()
            newSeriesEpisodesViewModel=ViewModelProvider(this)[NewSeriesEpisodesViewModel::class.java]
            railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
            railInjectionHelper!!.getSeriesDetailsV2(seriesId.toString(), true)
                .observe(this@SeriesDetailActivity) { response: ResponseModel<*>? ->
                    if (response != null) {
                        if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {
                        } else if (response.status.equals(
                                APIStatus.SUCCESS.name,
                                ignoreCase = true
                            )
                        ) {
                            if (response.baseCategory != null) {
                                val enveuCommonResponse = response.baseCategory as RailCommonData
                                parseSeriesData(enveuCommonResponse)
                            }
                        } else if (response.status.equals(
                                APIStatus.ERROR.name,
                                ignoreCase = true
                            )
                        ) {
                            if (response.errorModel.errorCode != 0) {
                                if (response.errorModel.errorCode == AppConstants.RESPONSE_CODE_LOGOUT) {
                                    if (isLoggedIn) {
                                        hitApiLogout()
                                    }
                                } else {
                                    commonDialog(
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                            getString(R.string.popup_error)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                            getString(R.string.popup_something_went_wrong)
                                        ),
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                            getString(R.string.popup_continue)
                                        )
                                    )
                                }
                            }
                        } else if (response.status.equals(
                                APIStatus.FAILURE.name,
                                ignoreCase = true
                            )
                        ) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.popup_something_went_wrong)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        }
                    }
                }
            binding!!.flBackIconImage.setOnClickListener { onBackPressed() }
        }


    private val assetDetails: Unit
        get() {
            railInjectionHelper?.getAssetDetailsbySlug(contentSlug)
                ?.observe(this) { assetResponse: ResponseModel<*>? ->
                    if (assetResponse != null) {
                        if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                        } else if (assetResponse.status.equals(
                                APIStatus.SUCCESS.name, ignoreCase = true
                            )
                        ) {
                            val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
                            parseSeriesData(enveuCommonResponse)
                        } else if (assetResponse.status.equals(
                                APIStatus.ERROR.name, ignoreCase = true
                            )
                        ) {
                            if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {

                            }
                        } else if (assetResponse.status.equals(
                                APIStatus.FAILURE.name, ignoreCase = true
                            )
                        ) {

                        }
                    }
                }
        }

    private fun parseSeriesData(enveuCommonResponse: RailCommonData?) {
        try {
            if (enveuCommonResponse != null && enveuCommonResponse.enveuVideoItemBeans.isNotEmpty()) {
                keyword = enveuCommonResponse.enveuVideoItemBeans[0].display_tags
                seriesDetailBean = enveuCommonResponse.enveuVideoItemBeans[0]
                seriesDetailBean?.seriesCustomData.toString()

                AnalyticsUtils.trackScreenView(this,AppConstants.CONTENT_DETAIL + " - " + seriesDetailBean?.title )

                if (isEpisodes){
                    customData = seriesDetailBean?.seriesCustomData!!
                    customData.episode_series_id?.id?.let {
                        seriesId=it
                    }
                }else{
                    seriesId = seriesDetailBean?.id!!
                }
                if (seriesDetailBean!!.assetType.equals(AppConstants.CUSTOM)) {
                    getAppLevelJsonData(seriesDetailBean!!.customType)
                } else {
                    getAppLevelJsonData(seriesDetailBean!!.videoDetails.videoType)
                }
                setUiComponents(seriesDetailBean)

                if (featureList?.featureFlag?.New_Detail_Page_UI == true){
                    binding!!.rvNewSeasonUi.visibility=View.VISIBLE
                    setNewSeriesUI()
                    if (featureList?.featureFlag?.SERIES_MORE_LIKE_THIS == true) {
                        prepareEnabledTabs()
                    }
                }else{
                    binding!!.rvNewSeasonUi.visibility=View.GONE
                    setEnveuTabFragment()
                }

            } else {
                if (enveuCommonResponse != null && ObjectHelper.isNotEmpty(enveuCommonResponse.enveuVideoItemBeans)
                    && (enveuCommonResponse.enveuVideoItemBeans[0].responseCode
                            == AppConstants.RESPONSE_CODE_LOGOUT)
                ) {
                    if (isLoggedIn) {
                        hitApiLogout()
                    }
                } else {
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )
                }
            }
        } catch (e: Exception) {
            Logger.w(e)
        }
    }

    private fun setEnveuTabFragment() {
         enveuTabFragment = EnveuTabFragment()
        val args = Bundle().apply {
            putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
            putInt(AppConstants.BUNDLE_SERIES_ID, seriesId)
            putInt(AppConstants.BUNDLE_ASSET_ID, seriesId)
            putInt(AppConstants.BUNDLE_SEASON_NUMBER, seasonNumber)
        }
        enveuTabFragment?.arguments=args
        supportFragmentManager.beginTransaction().replace(R.id.enveu_tab_fragment,enveuTabFragment!!).addToBackStack("null").commit()
    }

    private fun setNewSeriesUI(){
        val args = Bundle().apply {
            putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
            putInt(AppConstants.BUNDLE_SERIES_ID, seriesId)
            putInt(AppConstants.BUNDLE_ASSET_ID, seriesId)
            putInt(AppConstants.BUNDLE_SEASON_NUMBER, seasonNumber)
            putSerializable(AppConstants.BUNDLE_SERIES_DETAIL, seriesDetailBean)
        }
        newSeriesEpisodesFragment = NewSeriesEpisodesFragment()
        newSeriesEpisodesFragment?.arguments=args
        if (isEpisodes) {
        newSeriesEpisodesFragment!!.customData = customData
        }
        supportFragmentManager.beginTransaction()
            .replace(R.id.container_new_season_ui, newSeriesEpisodesFragment!!)
            .addToBackStack("null").commit()

        val bundle=Bundle().apply {
            putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
            putInt(AppConstants.BUNDLE_SERIES_ID, seriesId)
            putInt(AppConstants.BUNDLE_ASSET_ID, seriesId)
            putInt(AppConstants.BUNDLE_SEASON_NUMBER, seasonNumber)
            putSerializable(AppConstants.BUNDLE_SERIES_DETAIL, seriesDetailBean)
            putString(AppConstants.FROM_REDIRECTION,AppConstants.SECOND_CONTAINER)
        }
        nseriesepfrag = NewSeriesEpisodesFragment()
        nseriesepfrag?.arguments =bundle

        if (isEpisodes) {
            nseriesepfrag?.customData = customData
        }
            supportFragmentManager.beginTransaction()
                .replace(R.id.second_container_new_sean_ui, nseriesepfrag!!)
                .addToBackStack("null").commit()
    }
    private fun setUserInteractionFragment(id: Int) {
        val transaction = supportFragmentManager.beginTransaction()
        val args = Bundle()
        args.putInt(AppConstants.BUNDLE_ASSET_ID, id)
        args.putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
        args.putSerializable(AppConstants.BUNDLE_SERIES_DETAIL, seriesDetailBean)
        userInteractionFragment = UserInteractionFragment()
        userInteractionFragment!!.arguments = args
        transaction.replace(R.id.fragment_user_interaction, userInteractionFragment!!)
        transaction.addToBackStack(null)
        transaction.commit()
    }

    private var mediaConfig: MediaConfig? = null
    private var featureList: FeatureFlagModel? = null

    private fun getAppLevelJsonData(mediaType: String) {
        this.mediaType = mediaType
        Log.d("mediaType", "getAppLevelJsonData: $mediaType")
        val mediaConfigInfo: MediaInfo? = AppConfigMethod.getMediaMappingByType(mediaType)
        mediaConfig = AppConfigMethod.getMediaConfigByType(mediaConfigInfo?.pageType.toString())
        featureList = AppConfigMethod.parseFeatureFlagList()

    }



    fun seriesLoader() {
        showLoading(binding!!.progressBar, true)
    }


    private var seasonEpisodesList: ArrayList<EnveuVideoItemBean>? = null
    fun episodesList(seasonEpisodes: List<EnveuVideoItemBean>?) {
        if (!seasonEpisodes.isNullOrEmpty()) {
            if (seasonEpisodesList ==null) {
                seasonEpisodesList = ArrayList()
            }
            seasonEpisodesList?.addAll(seasonEpisodes)
            if (mediaConfig!!.detailPage.features.watchNowEnabled) {
                binding!!.metaDetails.playButton.visibility = View.VISIBLE
            } else {
                binding!!.metaDetails.playButton.visibility = View.GONE
            }
        } else {
            binding!!.metaDetails.playButton.visibility = View.GONE
        }
    }

    override fun onStop() {
        super.onStop()
        AppCommonMethod.isSeriesPage = false
    }

    override fun onDestroy() {
        try {
            super.onDestroy()
            AppCommonMethod.seasonId = -1
            preference!!.appPrefAssetId = 0
            preference!!.appPrefJumpTo = ""
            preference!!.appPrefBranchIo = false
            seasonEpisodes.clear()
            seriesSeasons.clear()
        } catch (e: Exception) {
            Logger.w(e)
        }
    }

    private fun noConnectionLayout() {
        stopShimmer()
        binding!!.llParent.visibility = View.GONE
        binding!!.noConnectionLayout.visibility = View.VISIBLE
        binding!!.connection.retryTxt.setOnClickListener {
            callShimmer()
            connectionObserver()
        }
    }


    override fun onBackPressed() {
        super.onBackPressed()
        if (preference!!.appPrefJumpBack) {
            preference!!.appPrefJumpBackId = 0
            preference!!.appPrefJumpBack = false
        }
        AppCommonMethod.seasonId = -1
        AppCommonMethod.isSeasonCount = false
        isEpisodes=false
        finish()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            setupLandscapeView()
        } else {
            setupPortraitView()
        }
    }
    override fun onFinishDialog() {
        if (isLoggedIn) {
            hitApiLogout()
        }
        finish()
    }

    fun hitApiLogout() {
        if (isLoggedIn) {
            hitApiLogout(this@SeriesDetailActivity, preference!!.appPrefAccessToken)
        }
    }

    fun openLogin() {
        preference!!.appPrefJumpTo = resources.getString(R.string.series)
        preference!!.appPrefJumpBack = true
        preference!!.appPrefJumpBackId = seriesId
        ActivityLauncher.getInstance().loginActivity(
            this@SeriesDetailActivity,
            ActivityLogin::class.java, ""
        )
    }

    fun showSeasonList(list: ArrayList<SelectedSeasonModel>) {
        binding!!.transparentLayout.visibility = View.VISIBLE
        val listAdapter = SeasonListAdapter(list)
        val builder = AlertDialog.Builder(this@SeriesDetailActivity)
        val inflater = LayoutInflater.from(this@SeriesDetailActivity)
        val content = inflater.inflate(R.layout.season_custom_dialog, null)
        builder.setView(content)
        val mRecyclerView = content.findViewById<RecyclerView>(R.id.my_recycler_view)
        val imageView = content.findViewById<ImageView>(R.id.close)
        imageView.setOnClickListener {
            alertDialog!!.cancel()
            binding!!.transparentLayout.visibility = View.GONE
        }

        mRecyclerView.layoutManager = LinearLayoutManager(this@SeriesDetailActivity)
        mRecyclerView.adapter = listAdapter
        alertDialog = builder.create()
        alertDialog?.window!!.setBackgroundDrawable(
            ActivityCompat.getDrawable(
                this@SeriesDetailActivity,
                R.color.transparent
            )
        )
        alertDialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        if (alertDialog?.window != null) alertDialog?.window!!.attributes.windowAnimations =
            R.style.SlidingDialogAnimation
        alertDialog?.show()
        val lWindowParams = WindowManager.LayoutParams()
        lWindowParams.copyFrom(alertDialog?.window!!.attributes)
        lWindowParams.width = ViewGroup.LayoutParams.MATCH_PARENT // this is where the magic happens
        lWindowParams.height = ViewGroup.LayoutParams.MATCH_PARENT
        alertDialog?.window!!.attributes = lWindowParams
    }

    private fun prepareEnabledTabs(){
        // Initialize the ArrayList if it's null
        if (tabsConfigList == null) {
            tabsConfigList = ArrayList()
        } else {
            tabsConfigList!!.clear()
        }

        // Initialize the ArrayList if it's null
        if (tabConfig == null) {
            tabConfig = ArrayList()
        } else {
            tabConfig!!.clear()
        }


        // Add the mediaConfig to the list
        tabsConfigList?.add(mediaConfig!!.detailPage.tabs)
        for (i in tabsConfigList!!.indices) {
            if (tabsConfigList!![i].moreLikeThis.enabled) {
                tabConfig!!.add(tabsConfigList!![i].moreLikeThis)
            }
            if (tabsConfigList!![i].trailersAndMore.enabled) {
                tabConfig!!.add(tabsConfigList!![i].trailersAndMore)
            }
            if (tabsConfigList!![i].clipAndMore.enabled) {
                tabConfig!!.add(tabsConfigList!![i].clipAndMore)
            }
        }
        setTabs()
    }

    private fun setTabs() {
        index=0
        processTabConfigRecursive(index)
    }

    private var index=0

    private fun processTabConfigRecursive(index: Int) {
        // Base case: check if the index is within the bounds of the list
        if (index < tabConfig!!.size) {
            // Make API call or perform processing for tabConfig[index]
            when (tabConfig!![index].displayLabel ) {
                mediaConfig!!.detailPage.tabs.moreLikeThis.displayLabel -> {
                    callMoreLikeThisTab()
                }
                mediaConfig!!.detailPage.tabs.trailersAndMore.displayLabel -> {
                    getLinkedTrailer()
                }
                mediaConfig!!.detailPage.tabs.clipAndMore.displayLabel -> {
                    getClips()
                }
            }
            // Continue the recursion with the next index
        }
    }

    private fun callMoreLikeThisTab() {
        railInjectionHelper!!.getRelatedContent(0, 20, seriesDetailBean!!.assetType, seriesId).observe(this) { response ->
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {

                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    if (response.baseCategory != null) {
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                        enveuCommonResponse.railType = AppConstants.MORE_LIKE_THIS_TAB
                        enveuCommonResponse.tabTittle = resources.getString(R.string.detail_tab_related)
//                        enveuCommonResponse.tabTittle = mediaConfig!!.detailPage.tabs.moreLikeThis.displayLabel
                        railCommonDataList.add(enveuCommonResponse)
                        setCommonAdapter()
                        binding!!.progressBar.visibility = View.GONE
                    }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        binding!!.progressBar.visibility = View.GONE
                    }
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    binding!!.progressBar.visibility = View.GONE
                }
            }
            index ++
            processTabConfigRecursive(index)
        }
    }

    private fun setCommonAdapter() {
        binding!!.newSeasonDetailRecyclerView.setHasFixedSize(true)
        binding!!.newSeasonDetailRecyclerView.isNestedScrollingEnabled = false
        binding!!.newSeasonDetailRecyclerView.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)

        if (commonDetailAdapter == null) {
            RecyclerAnimator(this).animate(binding!!.newSeasonDetailRecyclerView)
            commonDetailAdapter =
                CommonDetailAdapter(
                    railCommonDataList,
                    this,
                    this
                )
            binding!!.newSeasonDetailRecyclerView.adapter = commonDetailAdapter
        } else {
            /**/
            commonDetailAdapter?.notifyDataSetChanged()

        }
    }

    private fun getLinkedTrailer() {
        railInjectionHelper!!.getCommonListAll(0, 20, seriesId.toString(),
            AppConstants.TRAILER_CUSTOM_DATA).observe(this) { response ->
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {

                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    if (response.baseCategory != null) {
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                        enveuCommonResponse.railType = AppConstants.TRAILER_AND_MORE
                        enveuCommonResponse.tabTittle = mediaConfig!!.detailPage.tabs.trailersAndMore.displayLabel
                        railCommonDataList.add(enveuCommonResponse)
                        binding!!.progressBar.visibility = View.GONE
                        setCommonAdapter()
                    }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        binding!!.progressBar.visibility = View.GONE
                    }
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    binding!!.progressBar.visibility = View.GONE
                }
            }
            index++
            processTabConfigRecursive(index)
        }
    }
    private fun getClips() {
        railInjectionHelper!!.getCommonListAll(
            0, 20, seriesId.toString(),
            AppConstants.CLIPS_AND_MORE_CUSTOM_DATA
        ).observe(this) { response ->
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {

                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    if (response.baseCategory != null) {
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                        enveuCommonResponse.railType = AppConstants.TRAILER_AND_MORE
                        enveuCommonResponse.tabTittle =
                            mediaConfig!!.detailPage.tabs.clipAndMore.displayLabel
                        railCommonDataList.add(enveuCommonResponse)
                        binding!!.progressBar.visibility = View.GONE
                        setCommonAdapter()
                    }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        binding!!.progressBar.visibility = View.GONE
                    }
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    binding!!.progressBar.visibility = View.GONE
                }
            }
            index++
            processTabConfigRecursive(index)
        }
    }

    internal inner class SeasonListAdapter(private val list: ArrayList<SelectedSeasonModel>) : RecyclerView.Adapter<SeasonListAdapter.ViewHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.all_season_listing, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.season.text = resources.getString(R.string.season) + " " + list[position].list.seriesCustomData.season_number
            if (list[position].isSelected) {
                holder.season.setTextColor(
                    ContextCompat.getColor(
                        holder.season.context,
                        R.color.selected_indicator_color
                    )
                )
                val boldTypeface = Typeface.defaultFromStyle(Typeface.BOLD)
                holder.season.typeface = boldTypeface
            } else {
                holder.season.setTextColor(
                    ContextCompat.getColor(
                        holder.season.context,
                        R.color.series_detail_description_text_color
                    )
                )
                val boldTypeface = Typeface.defaultFromStyle(Typeface.NORMAL)
                holder.season.typeface = boldTypeface
            }
            holder.season.setOnClickListener {
                alertDialog!!.cancel()
                binding!!.transparentLayout.visibility = View.GONE
               enveuTabFragment?.seasonEpisodeNotify(list,position)
            }
        }

        override fun getItemCount(): Int {
            return list.size
        }

        internal inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            var season: TextView

            init {
                season = itemView.findViewById(R.id.season_name)
            }
        }
    }

    private var singleItem: EnveuVideoItemBean? = null
    override fun getFirstItem(itemValue: EnveuVideoItemBean) {
        preference = KsPreferenceKeys.getInstance()
        if (preference?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
        }
        if (itemValue != null && isFirstCallToGetFirstItem) {
            singleItem = itemValue
            setAllDataFromItemBean(itemValue)
            if (featureList?.featureFlag?.AUTO_PLAY == true){
                if (this.mediaType == AppConstants.MOVIES_AUDIO || this.mediaType == AppConstants.SHOW_AUDIO || this.mediaType.equals(AppConstants.TRAILER_AUDIO)) {
                    //stay at detail page and user can  play audio on click of watch now
                } else {
                    isFirstCallToGetFirstItem=false
                    playContent()
                }
            }

        }
    }
    override fun episodeClicked(itemBean: EnveuVideoItemBean?) {
        if (KsPreferenceKeys.getInstance()?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
        }
        if (itemBean != null){
            singleItem = itemBean
            setAllDataFromItemBean(itemBean)
            if (featureList?.featureFlag?.ALLOW_ANONYMOUS_USER_PLAY_CONTENT == true){
                if (isGeoBlocking) {
                    checkGeoBlocking()
                }else{
                    if (!isPremium){
                        checkRefAndCallPlayerProcess()
                    }else{
                        callPlayerForPremiumContent()
                    }
                }
            }else {
                playContent()
            }
        }
    }
    private fun setAllDataFromItemBean(itemValue: EnveuVideoItemBean) {
        isPremium = itemValue.isPremium
        currentEpisodeId = itemValue.id
        tittle = itemValue.title
        assetType = itemValue.assetType

        if (itemValue.externalRefId != null) {
            refId = itemValue.externalRefId
        }
        if (itemValue.sku != null) {
            sku = itemValue.sku
        }
        if (itemValue.skipintro_startTime != null) {
            skipIntroStartTime = itemValue.skipintro_startTime
        }

        if (itemValue.skipintro_endTime != null) {
            skipIntroEndTime = itemValue.skipintro_endTime
        }
        if (itemValue.audioTrackList != null) {
            audioTrackList = itemValue.audioTrackList
        }
        checkGeoBlocking()
    }

    @SuppressLint("SetTextI18n")
    private fun setUiComponents(seriesResponse: EnveuVideoItemBean?) {
        if (seriesResponse != null) {
            binding!!.metaDetails.duration.visibility = View.GONE

          /*  if (seriesResponse.seriesCustomData != null && seriesResponse.seriesCustomData.trailer_reference_id != null) {
                seriesResponse.seriesCustomData.trailer_reference_id?.let { getTrailer(it) }
            }*/
            getTrailer(seriesId.toString())
            setUserInteractionFragment(seriesId)
            binding!!.playlistItem = seriesResponse
            posterUrl = seriesResponse.posterURL

            ImageHelper.getInstance(this).loadListImage(binding!!.sliderImage, seriesResponse.posterURL)
            binding!!.responseApi = seriesResponse.description.trim { it <= ' ' }
            if (seriesResponse.title != null) {
                seriesTittle = seriesResponse.title
                binding!!.metaDetails.tvTitle.text = seriesResponse.title
            } else {
                binding!!.metaDetails.tvTitle.visibility = View.GONE
            }
            if (seriesResponse.description != null) {
                binding!!.metaDetails.descriptionText.text = seriesResponse.description
            } else {
                binding!!.metaDetails.descriptionText.visibility = View.GONE
            }

            if (!seriesResponse?.seriesCustomData?.showTitle.isNullOrEmpty()) {
                if (seriesResponse?.seriesCustomData?.showTitle.equals("true")) {
                    binding?.metaDetails?.tvTitle?.show()
                } else {
                    binding?.metaDetails?.tvTitle?.hide()
                }
            }
            else{
                binding?.metaDetails?.tvTitle?.hide()
            }

            if (seriesResponse.seriesCustomData?.director?.firstOrNull()?.title != null) {
                binding!!.metaDetails.directorTextView.visibility = View.VISIBLE
                binding!!.metaDetails.directorTextView.text = getString(R.string.director_text, seriesResponse.seriesCustomData?.director?.firstOrNull()?.title)
            } else {
                binding!!.metaDetails.directorTextView.visibility = View.GONE
            }

            if (seriesResponse.seriesCustomData?.actorData?.isNotEmpty() == true) {
                val titles = seriesResponse.seriesCustomData?.actorData?.map { it.title }
                binding!!.metaDetails.actorsTextView.visibility = View.VISIBLE
                binding!!.metaDetails.actorsTextView.text = getString(R.string.staring, titles?.joinToString(", "))
            } else {
                binding!!.metaDetails.actorsTextView.visibility = View.GONE
            }

            setClicks()
        }
    }

    private fun playContent(){
        if (isLoggedIn) {
            if (isGeoBlocking) {
                showGeoBlockDailog()
            } else {
                if (!isPremium) {
                    if (isUserVerified.equals("true", ignoreCase = true)) {
                       // checkRefAndCallPlayerProcess()
                        startPlayer(refId)
                    } else {
                        isUserNotVerify = true
                        commonDialogWithCancel(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                                getString(R.string.popup_notEntitled)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(),
                                getString(R.string.popup_user_not_verify)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_verify.toString(),
                                getString(R.string.popup_verify)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                                getString(R.string.popup_cancel)
                            )
                        )
                    }
                } else {
                    binding!!.progressBar.visibility = View.VISIBLE
                    callPlayerForPremiumContent()
                }
            }

        } else {
            ActivityLauncher.getInstance().loginActivity(this@SeriesDetailActivity, ActivityLogin::class.java,"")
        }
    }
    private fun callPlayerForPremiumContent() {
        viewModel!!.hitApiEntitlement(token, sku).observe(this@SeriesDetailActivity) { responseEntitle ->
                binding!!.progressBar.visibility = View.GONE
                if (responseEntitle != null && responseEntitle.data != null) {
                    resEntitle = responseEntitle
                    if (responseEntitle.data.entitled) {
                        if (isUserVerified.equals("true", ignoreCase = true)) {
                            viewModel?.externalRefID(
                                responseEntitle.data.accessToken,
                                responseEntitle.data.sku
                            )?.observe(this) { it ->

                                if (singleItem!!.drmDisabled == false){
                                    checkDrmAndCallPlayerProcess()
                                }else {
                                    playbackUrl = it?.data?.externalRefId
                                    if (this.mediaType == AppConstants.MOVIES_AUDIO || this.mediaType == AppConstants.SHOW_AUDIO || this.mediaType.equals(AppConstants.TRAILER_AUDIO)) {
                                        startAudioPlayer(refId!!,tittle,currentEpisodeId)
                                    } else {
                                        startPlayer(playbackUrl)
                                    }
                                }
                            }
                        } else {
                            isUserNotVerify = true
                            commonDialog(
                                "",
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(),
                                    getString(R.string.popup_user_not_verify)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_verify.toString(),
                                    getString(R.string.popup_verify)
                                )
                            )
                        }
                    } else {
                        isUserNotEntitle = true
                        commonDialogWithCancel(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                                getString(R.string.popup_notEntitled)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_select_plan.toString(),
                                getString(R.string.popup_select_plan)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_purchase.toString(),
                                getString(R.string.popup_purchase)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                                getString(R.string.popup_cancel)
                            )
                        )
                    }
                } else {
                    if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                        clearCredientials(preference)
                        ActivityLauncher.getInstance().loginActivity(
                            this@SeriesDetailActivity,
                            ActivityLogin::class.java, ""
                        )
                    } else {
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
                }
            }
    }

    private fun checkRefAndCallPlayerProcess() {
        if (null != refId && !refId.equals("", ignoreCase = true)) {
            if (singleItem!!.drmDisabled == false) {
               // checkDrmAndCallPlayerProcess()
                startPlayer(refId)
            }else{
                playbackUrl = refId
                if (this.mediaType == AppConstants.MOVIES_AUDIO || this.mediaType == AppConstants.SHOW_AUDIO || this.mediaType == AppConstants.TRAILER_AUDIO) {
                    startAudioPlayer(refId!!,tittle,currentEpisodeId)
                } else {
                    startPlayer(playbackUrl)
                }
            }
        }
    }
    private fun checkDrmAndCallPlayerProcess() {
            viewModel?.getLicenseUrl("",sku,AppConstants.JW_DRM_KEY)?.observe(this){
                if (it != null){
                    signedUrl = it.toString()
                    drmBaseUrl = signedUrl.substringBefore("?")
                    drmToken = signedUrl.substringAfter("token=")
                    callJwApiToGetWidevineUrl(drmBaseUrl,drmToken)
                }else{
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )
                }
            }
    }

    private fun showGeoBlockDailog() {
        commonDialog(
            stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.geo_blocking_title.toString(),
                getString(R.string.geo_blocking_title)
            ),
            "",
            stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                getString(R.string.ok)
            )
        )
    }

    private var widevineUrl = ""
    private var fileUrl = ""
    private fun callJwApiToGetWidevineUrl(signedUrl: String, drmToken: String) {
        viewModel?.getWidevineUrl(signedUrl,drmToken)?.observe(this){
            widevineUrl = it?.playlist?.get(0)?.sources?.get(0)?.drm?.widevine?.url.toString()
            fileUrl = it?.playlist?.get(0)?.sources?.get(0)?.file.toString()
            if (widevineUrl != "" && fileUrl != "") {
                startPlayer(playbackUrl)
                Log.d("widevineUrl", widevineUrl)
                Log.d("widevineUrl>>", fileUrl)
            }


        }
    }

    private fun startAudioPlayer(
        externalRefId: String,
        title: String,
        id: Int
    ) {
        ActivityLauncher.getInstance().podcastPlayer(this, EntBackgroundAudioActivity::class.java,externalRefId,seasonEpisodesList,title,id,false)
    }

    private var resEntitle: ResponseEntitle? = null
    private fun setClicks() {
        token = preference!!.appPrefAccessToken
        viewModel = ViewModelProvider(this)[DetailViewModel::class.java]
        binding!!.metaDetails.trailerButton.setOnClickListener {
            if (this.mediaType == AppConstants.MOVIES_AUDIO || this.mediaType == AppConstants.SHOW_AUDIO || this.mediaType.equals(AppConstants.TRAILER_AUDIO)) {
                startAudioPlayer(seriesDetailBean!!.externalRefId,seriesDetailBean!!.title,seriesDetailBean!!.id)
            } else {
                startPlayer(
                    trailerUrl
                )
            }

        }
        try {
            binding?.metaDetails?.playButton?.setOnClickListener {
              playContent()
            }
        } catch (e: Exception) {
            Logger.e(e)
        }
    }
    private val detailsPlayerCallBack = object : PlayerFragment.DetailsPlayerCallBack {
        override fun onPlayerError() {
            binding?.sliderImage?.visibility = View.VISIBLE
            binding?.flBackIconImage?.visibility = View.VISIBLE
        }

        override fun onCompleted() {
            binding?.sliderImage?.visibility = View.VISIBLE
            binding?.flBackIconImage?.visibility = View.VISIBLE
        }
    }
    private fun startPlayer(playbackUrl: String?) {
        if (featureList?.featureFlag?.PORTRAIT_MODE == true){
            if (playerFragment != null) {
                if (playbackUrl != null) {
                    currentEpisodeId.let {
                        playerFragment?.reloadPlayer(
                            playbackUrl, title.toString(), AppConstants.episodes, false,
                            it, assetType, true,
                            singleItem!!.drmDisabled,
                            widevineUrl = widevineUrl,
                            fileUrl = fileUrl
                        )
                    }
                }
                binding?.flBackIconImage?.visibility = View.GONE
            }else {
                playerFragment = PlayerFragment()
                detailsPlayerCallBack.let { playerFragment?.setDetailsPlayerCallBack(it) }
                val bundle = Bundle()
                bundle.putString("contentUrl", playbackUrl)
                bundle.putString("mediaType", AppConstants.episodes)
                bundle.putString("contentTitle", tittle)
                bundle.putInt("contentID",currentEpisodeId)
                bundle.putString("contentType", assetType)
                bundle.putBoolean("isDrmDisabled",singleItem!!.drmDisabled)
                bundle.putString("widevineUrl", widevineUrl)
                bundle.putString("fileUrl", fileUrl)
                bundle.putBoolean("IsLive", false)
                bundle.putSerializable("episodeList", seasonEpisodesList as Serializable?)
                playerFragment?.arguments = bundle
                supportFragmentManager.beginTransaction()
                    .add(R.id.player_frame, playerFragment!!, "PlayerFragment").commit()
                binding?.flBackIconImage?.visibility = View.GONE
            }
        }else{
            val contentMetaData = ContentMetaData()
            contentMetaData.playBackUrl = playbackUrl
            contentMetaData.contentTitle = tittle
            contentMetaData.isLive = false
            contentMetaData.contentType = assetType
            contentMetaData.mediaType = AppConstants.episodes
            contentMetaData.contentId = currentEpisodeId
            contentMetaData.isDrmDisabled = singleItem?.drmDisabled == true
            contentMetaData.widevineUrl = widevineUrl
            contentMetaData.fileUrl = fileUrl
            val seasonEpisodes  = SeasonEpisodes()
            val seasonEpisodeList = SeasonEpisodesList()
            seasonEpisodeList.seasonEpisodesList = seasonEpisodesList
            seasonEpisodes.seasonEpisodesJson = Gson().toJson(seasonEpisodeList)
            val intent = Intent(this, PlayerActivity::class.java)
            intent.putExtra(AppConstants.CONTENT_META_DATA, contentMetaData)
            intent.putExtra(Constants.ALL_EPISODES_LIST, seasonEpisodes)
            intent.putExtra(Constants.SINGLE_CONTENT_BUNDLE, Gson().toJson(singleItem))
            startActivity(intent)
        }
    }
    private fun setupPortraitView() {
        val params = binding!!.playerFrame.layoutParams as ConstraintLayout.LayoutParams
        params.width = ConstraintLayout.LayoutParams.MATCH_CONSTRAINT
        params.height = ConstraintLayout.LayoutParams.MATCH_CONSTRAINT
        binding!!.playerFrame.layoutParams = params
        val set = ConstraintSet()
        set.clone(binding!!.llParent)
        set.connect(
            R.id.player_frame,
            ConstraintSet.END,
            ConstraintSet.PARENT_ID,
            ConstraintSet.END
        )
        set.connect(
            R.id.player_frame,
            ConstraintSet.START,
            ConstraintSet.PARENT_ID,
            ConstraintSet.START
        )
        set.connect(
            R.id.player_frame,
            ConstraintSet.TOP,
            ConstraintSet.PARENT_ID,
            ConstraintSet.TOP
        )
        set.setDimensionRatio(R.id.player_frame, "H,16:9")
        set.applyTo(binding!!.llParent)
        binding!!.rootScroll.visibility = View.VISIBLE
    }


    private fun setOrientation() {
        window.setBackgroundDrawableResource(R.color.app_bg_color)
        val orientation = this.resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            setupPortraitView()
        } else {
            setupLandscapeView()
        }
    }
    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_IMMERSIVE
                        or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_LOW_PROFILE
                        or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                )
    }
    private fun setupLandscapeView() {
        supportActionBar?.hide()
        hideSystemUI()
        window?.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        //endregion
        //region To hide the navigation button
        window?.decorView?.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
        val params = binding?.playerFrame?.layoutParams as ConstraintLayout.LayoutParams
        val displayMetrics = resources.displayMetrics
        params.width = displayMetrics.widthPixels
        params.height = displayMetrics.heightPixels
        binding?.playerFrame?.layoutParams = params
        binding?.rootScroll?.visibility = View.GONE
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun onActionBtnClicked() {
        if (isUserNotVerify) {
            ActivityLauncher.getInstance()
                .goToEnterOTP(this, EnterOTPActivity::class.java, "DetailPage")
        }
        if (isUserNotEntitle) {
            ActivityLauncher.getInstance().goToPlanScreen(
                this@SeriesDetailActivity,
                ActivitySelectSubscriptionPlan::class.java,
                ""
            )
        }else{
            onBackPressed()
        }
    }

    override fun onCancelBtnClicked() {

    }
    private fun getTrailer(trailerReferenceId: String) {
        railInjectionHelper?.getAssetDetailsV2(trailerReferenceId, this@SeriesDetailActivity)
            ?.observe(this@SeriesDetailActivity) { assetResponse: ResponseModel<*>? ->
                if (assetResponse != null) {

                    if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {

                    } else if (assetResponse.status.equals(
                            APIStatus.SUCCESS.name,
                            ignoreCase = true
                        )
                    ) {

                        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
                        if (enveuCommonResponse.enveuVideoItemBeans.size > 0) {
                            if (!enveuCommonResponse.enveuVideoItemBeans[0].externalRefId.equals(
                                    "",
                                    ignoreCase = true
                                ) && !enveuCommonResponse.enveuVideoItemBeans[0].externalRefId.equals(
                                    null,
                                    ignoreCase = true
                                )
                            ) {
                                trailerExternalRefId =
                                    enveuCommonResponse.enveuVideoItemBeans[0].externalRefId
                                trailerUrl = enveuCommonResponse.enveuVideoItemBeans[0].externalRefId
                                if (mediaConfig!!.detailPage.features.isTrailerEnabled && !trailerExternalRefId.equals(
                                        ""
                                    )
                                ) {
                                    binding!!.metaDetails.trailerButton.visibility = View.VISIBLE
                                } else {
                                    binding!!.metaDetails.trailerButton.visibility = View.GONE
                                }
                            }
                        }

                    } else if (assetResponse.status.equals(
                            APIStatus.ERROR.name,
                            ignoreCase = true
                        )
                    ) {

                    } else if (assetResponse.status.equals(
                            APIStatus.FAILURE.name,
                            ignoreCase = true
                        )
                    ) {

                    }
                }
            }
    }

    override fun railItemClick(item: RailCommonData, position: Int) {
        AppCommonMethod.redirectionLogic(this, item, position,"","","")
    }

    override fun moreRailClick(data: RailCommonData, position: Int, multilingualTitle: String?) {
        if (data.screenWidget != null && data.screenWidget.contentID != null) {
            val playListId = data.screenWidget.contentID
            if (data.screenWidget.name != null) {
                ActivityLauncher.getInstance().listActivity(
                    this@SeriesDetailActivity,
                    ListActivity::class.java,
                    playListId,
                    data.screenWidget.name.toString(),
                    0,
                    0,
                    data.screenWidget
                )
            } else {
                ActivityLauncher.getInstance().listActivity(
                    this@SeriesDetailActivity,
                    ListActivity::class.java,
                    playListId,
                    "",
                    0,
                    0,
                    data.screenWidget
                )
            }
        }
    }

    fun setFullscreenVisiblity(isMoreShow:Boolean) {
        newSeriesEpisodesFragment?.setUpForDetail()
        nseriesepfrag?.setUpForDetail()
        if (isMoreShow) {
           // binding!!.mainNestedScroll.visibility=View.GONE
//            binding!!.mainNestedScroll.visibility=View.GONE
            binding!!.secondContainerNewSeanUi.visibility=View.VISIBLE
        }else{
          //  binding!!.mainNestedScroll.visibility=View.VISIBLE
//            binding!!.mainNestedScroll.visibility=View.VISIBLE
            binding!!.secondContainerNewSeanUi.visibility=View.GONE
        }
    }

    fun setSeasonUIVisiblity(flag:Boolean) {
        if (flag){
            binding!!.containerNewSeasonUi.visibility=View.VISIBLE
        }else {
            binding!!.containerNewSeasonUi.visibility = View.GONE
            isSeasonData = true
            stopShimmer()
            dismissLoading((this).binding!!.progressBar)
        }
    }

    fun setSeasonList(seriesId: Int, railCommonData: RailCommonData) {
        seriesSeasons[seriesId.toString()] = railCommonData
    }

    fun setEpisodes(id: String, allEpiosdes: List<EnveuVideoItemBean>) {
        seasonEpisodes[id] = allEpiosdes
    }

    fun hideProgressBar() {
        isSeasonData = true
        stopShimmer()
        dismissLoading(binding!!.progressBar)
    }

    fun playerCallbackInstance(playerCallback: PlayerCallback) {
        this.playerCallback = playerCallback
    }

    interface PlayerCallback {
        fun stopPlayer()
    }
}

data class SeasonEpisodes(
    var seasonEpisodesJson:String? = null
):Serializable

data class SeasonEpisodesList(
    @field:SerializedName("seasonEpisodesList")
    var seasonEpisodesList: ArrayList<EnveuVideoItemBean>? = null,
):Serializable
