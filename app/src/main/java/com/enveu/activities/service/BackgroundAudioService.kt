package com.enveu.activities.service

import android.Manifest
import android.app.Activity
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ServiceInfo
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.support.v4.media.MediaMetadataCompat
import android.support.v4.media.session.MediaSessionCompat
import android.support.v4.media.session.PlaybackStateCompat
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.enveu.OttApplication
import com.enveu.R
import com.enveu.utils.Logger
import com.jwplayer.pub.api.JWPlayer
import com.jwplayer.pub.api.PlayerState
import com.jwplayer.pub.api.configuration.PlayerConfig
import com.jwplayer.pub.api.configuration.UiConfig
import com.jwplayer.pub.api.events.BufferEvent
import com.jwplayer.pub.api.events.CompleteEvent
import com.jwplayer.pub.api.events.ErrorEvent
import com.jwplayer.pub.api.events.EventType
import com.jwplayer.pub.api.events.PauseEvent
import com.jwplayer.pub.api.events.PlayEvent
import com.jwplayer.pub.api.events.ReadyEvent
import com.jwplayer.pub.api.events.TimeEvent
import com.jwplayer.pub.api.events.listeners.VideoPlayerEvents
import com.jwplayer.pub.api.events.listeners.VideoPlayerEvents.OnErrorListener
import com.jwplayer.pub.view.JWPlayerView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException
import java.net.HttpURLConnection
import java.net.URL


class BackgroundAudioService : Service() {
    private var mPlayer: JWPlayer? = null
    private var mMediaSessionCompat: MediaSessionCompat? = null
    private val eventHandler = PlayerEventHandler()
    private val mPlaybackStateBuilder = PlaybackStateCompat.Builder()
    private val mBinder = ServiceBinder()
    private var mPlayerView: JWPlayerView? = null
    private var callback: PlayerTimeCallBack? = null
    private var onVideoComplete: OnVideoComplete? = null
    private var src: String? = null
    private var title: String? = ""



    /**
     * When the service is created, create and prepare a MediaSession
     */
    override fun onCreate() {
        mMediaSessionCompat = MediaSessionCompat(this, javaClass.simpleName)
        mMediaSessionCompat!!.setCallback(MediaSessionCallback())
        mMediaSessionCompat!!.isActive = true

    }






    /**
     * This method gets called each time a call to this service is made
     * we use the {@param intent} to determine the action to be performed.
     *
     * When the service is first bound, an intent with ACTION_START is launched, and the
     * startForeground method gets called, this creates the notification and also promotes the
     * service from background to foreground and prevents the system from killing it.
     *
     * Please note that startForeground() should be called within 5 seconds of calling
     * startForegroundService() in the activity, otherwise the service will be killed and the app
     * will crash with an error.
     *
     * When an ACTION intent is received, we call the TransportControls method to tell the media
     * session an event occurred and invoke it's callback that will forward the event to the
     * player. We also set the PlaybackState to keep the mediaSession and the JWPlayerView in
     * sync.
     *
     * Finally, the notification gets updated every time the Service receives an Intent.
     */
    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        mPlayer?.allowBackgroundAudio(true)
        val action = intent.getStringExtra(ACTION)
        Log.d("ClickNextIcon", "onStartCommand: "+action.toString())
        val transportControls = mMediaSessionCompat!!.controller.transportControls
       // val item = mPlayer!!.playlistItem
        when (action) {
            ACTION_START -> {
                val builder = NotificationCompat.Builder(
                     this,
                    OttApplication.CHANNEL_ID
                )
                    // .setContentTitle(item.title)
                    // .setContentText(item.description)
                    .setSmallIcon(R.drawable.ic_bell)
                // .setContentText(item.description)
                if (Build.VERSION.SDK_INT >= 34) {
                    startForeground(
                        NOTIFICATION_ID,
                        builder.build(),
                        ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE);
                }else {
                    startForeground(
                        NOTIFICATION_ID,
                        builder.build());
                }
                setPlaybackState(PlaybackStateCompat.STATE_PAUSED)
            }

            ACTION_PLAY -> {
                transportControls.play()
                  mPlayer!!.allowBackgroundAudio(true)
                setPlaybackState(PlaybackStateCompat.STATE_PLAYING)
            }

            ACTION_PAUSE -> {
                transportControls.pause()
                setPlaybackState(PlaybackStateCompat.STATE_PAUSED)
            }

            ACTION_STOP -> {
                transportControls.stop()
                setPlaybackState(PlaybackStateCompat.STATE_STOPPED)
            }
            ACTION_NEXT -> {
                transportControls.skipToNext()
                setPlaybackState(PlaybackStateCompat.STATE_SKIPPING_TO_NEXT)
            }
            ACTION_PREVIOUS -> {
                transportControls.skipToPrevious()
                setPlaybackState(PlaybackStateCompat.STATE_SKIPPING_TO_PREVIOUS)
            }
        }
        showNotification()
        return START_NOT_STICKY
    }

    /**
     * When an Activity bind to this service, an instance of the Binder interface is returned to
     * allow interaction.
     */
    override fun onBind(intent: Intent): IBinder? {
        return mBinder
    }

    /**
     * Free resources when the service is destroyed.
     * Clear the service's notification.
     * Stop the service.
     */
    override fun onDestroy() {
        super.onDestroy()
//        mPlayer!!.removeListener(EventType.PLAY, eventHandler)
//        mPlayer!!.removeListener(EventType.PAUSE, eventHandler)
//        mPlayer!!.stop()
//        mMediaSessionCompat!!.release()
//       // NotificationManagerCompat.from(this).cancel(OttApplication.CHANNEL_ID.toInt())
//       NotificationManagerCompat.from(this).cancel(NOTIFICATION_ID)
//        stopSelf()
    }







    fun setonVideoCompleteback(onVideoComplete: OnVideoComplete) {
        this.onVideoComplete = onVideoComplete
    }

    /**
     * Stop the service when the user swipes away the app from the recent app view
     */
    override fun onTaskRemoved(rootIntent: Intent) {
        super.onTaskRemoved(rootIntent)
        stopSelf()
    }

    /**
     * Updates the current payback state
     */
    private fun setPlaybackState(state: Int) {
        mPlaybackStateBuilder.setState(state,
            mPlayer?.position?.toLong()?.times(1000) ?: 0, PLAYBACK_SPEED)
        setActions(state)
        mMediaSessionCompat!!.setPlaybackState(mPlaybackStateBuilder.build())
    }

    /**
     * Sets the available actions for the current state.
     */
    private fun setActions(state: Int) {
        when (state) {
            PlaybackStateCompat.STATE_PLAYING -> {
                mPlaybackStateBuilder.setActions(PlaybackStateCompat.ACTION_PAUSE)
            }

            PlaybackStateCompat.STATE_PAUSED -> {
                mPlaybackStateBuilder.setActions(PlaybackStateCompat.ACTION_PLAY)
            }

            PlaybackStateCompat.STATE_SKIPPING_TO_PREVIOUS -> {
                mPlaybackStateBuilder.setActions(PlaybackStateCompat.ACTION_SKIP_TO_PREVIOUS)
            }

            PlaybackStateCompat.STATE_SKIPPING_TO_NEXT -> {
                mPlaybackStateBuilder.setActions(PlaybackStateCompat.ACTION_SKIP_TO_NEXT)
            }
        }
    }

    /**
     * Updates the notification to be shown. We use a notification builder to create a whole new
     * notification but we use the notify() method and the same notification id to update it. It
     * is important to use the same id so the system knows that the foreground service still has
     * a notification (created in startForeground).
     */
    var pendingIntent: PendingIntent? = null

    private fun showNotification() {
        CoroutineScope(Dispatchers.Main).launch {
            // Fetch the bitmap in the IO context
            val largeIconBitmap = withContext(Dispatchers.IO) {
                getBitmapFromURL(src)
            }

            // Create an instance of NotificationCompat.Builder
            val mNotificationBuilder = NotificationCompat.Builder(this@BackgroundAudioService, OttApplication.CHANNEL_ID)
                .setContentTitle(title)
                .setSmallIcon(R.drawable.ic_bell) // Ensure you have this icon in your drawable resources
                .setLargeIcon(largeIconBitmap)

            // Set other notification parameters
            MediaStyleHelper.prepareNotification(
                mNotificationBuilder,
                mPlayerView?.context,
                mPlayer?.playlistItem
            )

            val metaDataBuilder = MediaMetadataCompat.Builder()
            metaDataBuilder.putLong(
                MediaMetadataCompat.METADATA_KEY_DURATION, Math.round(
                    mPlayer!!.duration
                ) * 1000
            )
            mMediaSessionCompat!!.setMetadata(metaDataBuilder.build())

            // Initialize the action buttons
            val actions = mutableListOf<NotificationCompat.Action>()

            // Add previous action
            val previousIntent = Intent(this@BackgroundAudioService, BackgroundAudioService::class.java).apply {
                putExtra(ACTION, ACTION_PREVIOUS)
            }
            val previousPendingIntent = PendingIntent.getService(
                this@BackgroundAudioService, 5, previousIntent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_IMMUTABLE else PendingIntent.FLAG_UPDATE_CURRENT
            )
            actions.add(NotificationCompat.Action(com.npaw.youbora.lib6.jwplayer.R.drawable.cast_ic_expanded_controller_skip_previous, "Previous", previousPendingIntent))

            // Add play/pause action
            if (mMediaSessionCompat!!.controller.playbackState.state == PlaybackStateCompat.STATE_PLAYING) {
                val pauseIntent = Intent(this@BackgroundAudioService, BackgroundAudioService::class.java).apply {
                    putExtra(ACTION, ACTION_PAUSE)
                }
                val pausePendingIntent = PendingIntent.getService(
                    this@BackgroundAudioService, 2, pauseIntent,
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_IMMUTABLE else PendingIntent.FLAG_UPDATE_CURRENT
                )
                actions.add(NotificationCompat.Action(com.npaw.youbora.lib6.jwplayer.R.drawable.ic_jw_pause, "Pause", pausePendingIntent))
            } else {
                val playIntent = Intent(this@BackgroundAudioService, BackgroundAudioService::class.java).apply {
                    putExtra(ACTION, ACTION_PLAY)
                }
                val playPendingIntent = PendingIntent.getService(
                    this@BackgroundAudioService, 3, playIntent,
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_IMMUTABLE else PendingIntent.FLAG_UPDATE_CURRENT
                )
                actions.add(NotificationCompat.Action(com.npaw.youbora.lib6.jwplayer.R.drawable.ic_jw_play, "Play", playPendingIntent))
            }

            // Add next action
            val nextIntent = Intent(this@BackgroundAudioService, BackgroundAudioService::class.java).apply {
                putExtra(ACTION, ACTION_NEXT)
            }
            val playPendingIntent = PendingIntent.getService(
                this@BackgroundAudioService, 4, nextIntent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_IMMUTABLE else PendingIntent.FLAG_UPDATE_CURRENT
            )
            actions.add(NotificationCompat.Action(com.npaw.youbora.lib6.jwplayer.R.drawable.ic_jw_next, "Next", playPendingIntent))

            // Add actions to the notification builder
            actions.forEach { action ->
                mNotificationBuilder.addAction(action)
            }

            mNotificationBuilder.setStyle(
                androidx.media.app.NotificationCompat.MediaStyle()
                    .setMediaSession(mMediaSessionCompat!!.sessionToken)
                    .setShowActionsInCompactView(0, 1, 2) // Indices of the actions in the actions list
            )

            // Check for notification permission
            if (ActivityCompat.checkSelfPermission(
                    this@BackgroundAudioService,
                    Manifest.permission.POST_NOTIFICATIONS
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                // TODO: Request the missing permissions
                return@launch
            }
            NotificationManagerCompat.from(this@BackgroundAudioService).notify(NOTIFICATION_ID, mNotificationBuilder.build())
        }
    }


    // Helper method to fetch a Bitmap image from a URL
    private suspend fun getBitmapFromURL(imageUrl: String?): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val url = URL(imageUrl)
                val connection: HttpURLConnection = url.openConnection() as HttpURLConnection
                connection.doInput = true
                connection.connect()
                val input = connection.inputStream
                BitmapFactory.decodeStream(input)
            } catch (e: IOException) {
                e.printStackTrace()
                null
            }
        }
    }


    /**
     * This Binder interface provides interaction between the service and the binding activity by
     * returning an instance of this Binder and allowing the activity to call its methods.
     *
     * Other custom methods can be defined here.
     *
     * Instead of defining several custom methods, the Binder interface can return an instance of
     * this service to allow the activity to call the service methods directly, but this approach
     * is more dangerous.
     */
    internal inner class ServiceBinder : Binder() {
        private var mContect: Context? = null
        fun createPlayer(context: Context?, isPauseMiniPLayer: Boolean,externalRefId: String?, callback: PlayerTimeCallBack, src: String?, title: String) {
            this.mContect = context
            setNotificationValues(src,title)
            if (context is Activity) {
                setCallback(callback)
                if (callback != null) {
                    callback!!.onMetaDataUpdate()
                }
              //  setonVideoCompleteback(onVideoComplete)
                val hideJwControlUiConfig = UiConfig.Builder()
                    .hideAllControls()
                    .build()
                val playerView: JWPlayerView? = mPlayerView
                playerView?.player.also { mPlayer = it }
                val config = PlayerConfig.Builder()
                    .playlistUrl("https://cdn.jwplayer.com/v2/media/$externalRefId")
                    .uiConfig(hideJwControlUiConfig)
                    .build()
                mPlayerView = JWPlayerView(mContect, null)

                mPlayer = mPlayerView!!.player
                mPlayer!!.addListener(EventType.PLAY, eventHandler)
                mPlayer!!.addListener(EventType.PAUSE, eventHandler)
                mPlayer?.addListener(EventType.TIME, eventHandler)
                mPlayer?.addListener(EventType.COMPLETE, eventHandler)
                mPlayer?.addListener(EventType.BUFFER, eventHandler)
                mPlayer?.addListener(EventType.READY, eventHandler)
                mPlayer?.addListener(EventType.ERROR, eventHandler)
                mPlayer!!.setup(config)
                if (mPlayer != null && callback != null) {
                    callback.setJwPlayerAdapter(mPlayer)
                }
                if (isPauseMiniPLayer) {
                    if (mPlayer != null && callback != null) {
                        callback.setMiniPlayerState(mPlayer)
                    }
                } else {
                    mPlayer!!.play()
                }

                mPlayer!!.play()
                mPlayer!!.allowBackgroundAudio(true)
            }


        }

        fun clearNotification(){
            try {
                mPlayer!!.removeListener(EventType.PLAY, eventHandler)
                mPlayer!!.removeListener(EventType.PAUSE, eventHandler)
                mPlayer!!.stop()
                mMediaSessionCompat?.release()
                NotificationManagerCompat.from(mContect!!).cancel(OttApplication.CHANNEL_ID.toInt())
                stopSelf()
            } catch (e:Exception) {
                Log.d("Exception", "clearNotification: $e")
            }

        }


        fun getPlayerView(): JWPlayerView? {
            return mPlayerView
        }

        fun getPlayer(): JWPlayer? {
            return mPlayer
        }

        fun removeListener() {
            mPlayer!!.removeListener(EventType.PLAY, eventHandler)
            mPlayer!!.removeListener(EventType.PAUSE, eventHandler)
            mPlayer!!.removeListener(EventType.TIME, eventHandler)
            mPlayer!!.removeListener(EventType.COMPLETE, eventHandler)
        }

        fun getPlayPauseState(): Boolean {
            var isPlaying: Boolean = if (mPlayer != null && mPlayer?.state == PlayerState.PLAYING) {
                mPlayer?.pause()
                false
            }else{
                mPlayer?.play()
                true
            }
            return isPlaying
        }
        fun setCallback(callback: PlayerTimeCallBack){
            setPlayerCallBack(callback)
        }
        fun setPlayState():Boolean{
            mPlayer?.play()
            return true
        }
        fun setPauseState():Boolean{
            mPlayer?.pause()
            return false
        }
    }

    private fun setNotificationValues(src: String?, title: String) {
        this.src = src
        this.title = title
    }

    private fun setPlayerCallBack(callback: PlayerTimeCallBack) {
        this.callback = callback
    }


    /**
     * This class must override all the methods of the actions supported by each
     * application.
     * As minimum, it should override onPlay() and onPause().
     * If the application supports skipping tracks, this class should also implement onSkipToNext
     * () and onSkipToPrevious().
     *
     * Every method should forward the behavior to the JWPlayerView instance to keep sync between
     * itself and the mediaSession.
     */
    private inner class MediaSessionCallback : MediaSessionCompat.Callback() {
        override fun onPlay() {
            mPlayer!!.play()
            super.onPlay()
            if (callback != null) {
                callback?.updateIconThroughNotification(true)
            }
        }

        override fun onPause() {
            mPlayer!!.pause()
            super.onPause()
            if (callback != null) {
                callback?.updateIconThroughNotification(false)
            }
        }

        override fun onSkipToNext() {
            super.onSkipToNext()
            if (callback != null) {
                callback?.onSkipNext()
            }
        }

        override fun onSkipToPrevious() {
            super.onSkipToPrevious()
            if (callback != null) {
                callback?.onSkipPrevious()
            }
        }
    }

    /**
     * This class must implement all the player event listeners needed for each application.
     * As minimum, it should implement onPLayListener and onPauseListener and update the
     * playbackState and the notification, to keep sync between the JWPlayerView instance and the
     * mediaSession.
     */
    private inner class PlayerEventHandler : VideoPlayerEvents.OnPlayListener, VideoPlayerEvents.OnPauseListener, VideoPlayerEvents.OnTimeListener, VideoPlayerEvents.OnCompleteListener, VideoPlayerEvents.OnBufferListener,VideoPlayerEvents.OnReadyListener, OnErrorListener {
        override fun onPause(pauseEvent: PauseEvent) {
            if (mPlayer != null && callback != null) {
                callback!!.onPause(mPlayer!!.position,mPlayer!!.duration)
            }
            setPlaybackState(PlaybackStateCompat.STATE_PAUSED)
            showNotification()
        }

        override fun onPlay(playEvent: PlayEvent) {
            if (mPlayer != null && callback != null) {
                callback!!.onPlay(true,mPlayer!!.position,mPlayer!!.duration)
            }
            setPlaybackState(PlaybackStateCompat.STATE_PLAYING)
            showNotification()
        }

        override fun onTime(p0: TimeEvent?) {
            if (mPlayer != null && callback != null) {
                callback!!.onDataReceived(mPlayer!!.position, mPlayer!!.duration,p0!!.position)
            }
        }

        override fun onComplete(p0: CompleteEvent?) {
            Logger.d("songCompleted","True")
            if (mPlayer != null && callback != null) {
                callback!!.onVideoComplete(mPlayer!!.position,mPlayer!!.duration)
            }
        }

        override fun onBuffer(p0: BufferEvent?) {
            if (mPlayer != null && callback != null) {
                callback!!.onBuffer(true)
            }
        }
        override fun onReady(p0: ReadyEvent?) {
//            if (callback != null) {
//                callback!!.setJwPlayerAdapter(mPlayer)
//            }
        }

        override fun onError(p0: ErrorEvent?) {
            Log.d("PLAYER ERROR", "onError: ")
        }


    }

    interface PlayerTimeCallBack {
        fun onDataReceived(position: Double, duration: Double,playerProgressTime:Double)
        fun onBuffer(isBuffering: Boolean)
        fun onPlay(isBuffering: Boolean, position: Double, duration: Double)
        fun onPause(position: Double, duration: Double)

        fun onVideoComplete(position: Double, duration: Double)

        fun updateIconThroughNotification(isBuffering: Boolean)

        fun onMetaDataUpdate()

        fun setJwPlayerAdapter(mPlayer: JWPlayer?)

        fun setMiniPlayerState(mPlayer: JWPlayer?)


        fun onSkipPrevious()

        fun onSkipNext()
    // Add more callback methods as needed
    }


    interface OnVideoComplete {
        fun onVideoCompletion()
        // Add more callback methods as needed
    }


    companion object {
        private const val PLAYBACK_SPEED = 1.0f
        const val ACTION = "ACTION"
        const val ACTION_START = "ACTION_START"
        const val ACTION_PLAY = "ACTION_PLAY"
        const val ACTION_PAUSE = "ACTION_PAUSE"
        const val ACTION_PREVIOUS = "ACTION_NEXT"
        const val ACTION_NEXT = "com.google.android.exoplayer.next"
        const val ACTION_STOP = "ACTION_STOP"
        const val NOTIFICATION_ID = 1
    }

}