package com.enveu.activities.settings


import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.CompoundButton
import com.enveu.BuildConfig
import com.enveu.R
import com.enveu.activities.contentpreferences.SettingContentPreferences
import com.enveu.activities.secondary_profile.SecondaryProfileActivity
import com.enveu.activities.settings.downloadsettings.DownloadSettings
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.videoquality.ui.ChangeLanguageActivity
import com.enveu.activities.videoquality.ui.VideoQualityActivity
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.databinding.SettingsActivityBinding
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper

class ActivitySettings : BaseBindingActivity<SettingsActivityBinding?>(), View.OnClickListener {
    private var devInfoCounter = 0
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    private var featureModel: FeatureFlagModel? = null
    private var isLoggedIn:Boolean? = false
    override fun inflateBindingLayout(inflater: LayoutInflater): SettingsActivityBinding {
        return SettingsActivityBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AnalyticsUtils.trackScreenView(this,AppConstants.SETTINGS)
        isLoggedIn = KsPreferenceKeys.getInstance().appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)
        featureModel = AppConfigMethod.parseFeatureFlagList()
        binding!!.stringData = stringsHelper
        binding!!.colorsData = colorsHelper
        binding!!.toolbar.colorsData = colorsHelper
        binding!!.toolbar.stringData = stringsHelper
        setToolBar()
        if (KsPreferenceKeys.getInstance().currentTheme.equals(
                AppConstants.LIGHT_THEME, ignoreCase = true)) {
            setTheme(R.style.MyMaterialTheme_Base_Light)
            binding!!.switchTheme.isChecked = false
        } else {
            setTheme(R.style.MyMaterialTheme_Base_Dark)
            binding!!.switchTheme.isChecked = true
        }
        try {
            val isTablet = <EMAIL>(R.bool.isTablet)
            if (!isTablet) {
                val number: String = "v"+BuildConfig.VERSION_NAME
                binding!!.buildNumber.text = number
            }
        } catch (ignored: Exception) {
        }
        setSwitchForBingeWatch()
        checkLanguage()
        if (isLoggedIn == true) {
            binding?.contentPrefParentLayout?.visibility = View.VISIBLE
        } else {
            binding!!.downloadLayout.visibility = View.GONE
            binding?.contentPrefParentLayout?.visibility = View.GONE
            binding?.viewLine1?.visibility = View.GONE
        }
        binding!!.rlKidsModePin.visibility = View.GONE
        binding!!.downloadLayout.setOnClickListener(this)
        binding!!.parentLayout.setOnClickListener(this)
        binding!!.videoQuality.setOnClickListener(this)
        binding!!.contentPrefParentLayout.setOnClickListener(this)

//        binding!!.parentLayout.setOnClickListener {
//            val intent = Intent(this@ActivitySettings, ChangeLanguageActivity::class.java)
//            startActivity(intent)
//        }
//        binding!!.videoQuality.setOnClickListener {
//            val intent = Intent(this@ActivitySettings, VideoQualityActivity::class.java)
//            startActivity(intent)
//        }

        binding!!.switchTheme.setOnCheckedChangeListener { _: CompoundButton?, _: Boolean ->
            if (binding!!.switchTheme.isChecked) {
                KsPreferenceKeys.getInstance().currentTheme = AppConstants.DARK_THEME
            } else {
                KsPreferenceKeys.getInstance().currentTheme = AppConstants.LIGHT_THEME
            }
            recreate()
        }
        binding!!.bingeSetting.setOnCheckedChangeListener { _: CompoundButton?, isChecked: Boolean ->
            if (isChecked) {
                KsPreferenceKeys.getInstance().bingeWatchEnable = true
                setSwitchForBingeWatch()
            } else {
                KsPreferenceKeys.getInstance().bingeWatchEnable = false
                setSwitchForBingeWatch()
            }
        }
        binding!!.switchTheme.setOnClickListener { }
        binding!!.buildNumber.setOnClickListener {
            if (KsPreferenceKeys.getInstance().appPrefLoginStatus.equals(
                    AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
                devInfoCounter++
                if (devInfoCounter == 3) {
                    binding!!.rlDeveloperInfo.visibility = View.VISIBLE
                    devInfoCounter = 0
                }
            }
        }
        binding!!.rlDeveloperInfo.setOnClickListener {
            DeveloperInfoDialogFragment().show(
                supportFragmentManager,
                DeveloperInfoDialogFragment.TAG
            )
        }
        if (featureModel?.featureFlag?.CONTENT_PREFERENCE_ENABLE == true){
            binding?.contentPrefParentLayout?.visibility = View.VISIBLE
        }else{
            binding?.contentPrefParentLayout?.visibility = View.GONE
        }
    }

    private fun setToolBar() {
        binding!!.toolbar.logoMain2.visibility = View.GONE
        binding!!.toolbar.backLayout.visibility = View.VISIBLE
        binding!!.toolbar.rlToolBar.visibility = View.VISIBLE
        binding!!.toolbar.titleText.visibility = View.VISIBLE
        val settingsTile = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.settings_title.toString(),
            getString(R.string.settings_title)
        )
        binding!!.toolbar.screenText.text = settingsTile
        binding!!.toolbar.searchIcon.visibility = View.GONE
        binding!!.toolbar.clNotification.visibility = View.GONE
        binding!!.toolbar.screenText.setBackgroundResource(0)
        binding!!.toolbar.backArrow?.let { rotateImageLocaleWise(it) }
        binding!!.toolbar.backLayout.setOnClickListener { onBackPressed() }
    }

    private fun setSwitchForBingeWatch() {
        binding!!.bingeSetting.isChecked = KsPreferenceKeys.getInstance().bingeWatchEnable
    }

//    private fun checkLanguage() {
//        val preference = KsPreferenceKeys.getInstance()
//        val currentLanguage = preference.appLanguage
//        if (currentLanguage.isEmpty()) {
//            binding!!.languageText.text = "English"
//        } else {
//            if (currentLanguage.equals("English", ignoreCase = true)) {
//                binding!!.languageText.text = currentLanguage
//            } else {
//            }
//        }
//        val qualityName = preference.qualityName
//        setQualityText(qualityName)
//    }

    private fun checkLanguage() {
        val preference = KsPreferenceKeys.getInstance()
        val currentLanguage = if (preference.appLanguage == AppConstants.LANGUAGE_ARABIC) R.string.lang_arabic else R.string.lang_english

        binding?.languageText?.text = when {
            currentLanguage.equals("") -> R.string.lang_english.toString()
            else -> currentLanguage.toString()
        }

        setQualityText(preference.qualityName)
    }

    private fun setQualityText(qualityName: String) {
        if (qualityName.isEmpty()) {
            binding!!.qualityText.text = getString(R.string.ep_video_auto)
        } else {
            if (qualityName.equals(resources.getString(R.string.ep_video_auto), ignoreCase = true)) {
                binding!!.qualityText.text = getString(R.string.ep_video_auto)
            } else if (qualityName.equals(resources.getString(R.string.streaming_sd), ignoreCase = true)) {
                binding!!.qualityText.text = getString(R.string.streaming_sd)
            } else if (qualityName.equals(resources.getString(R.string.streaming_hd), ignoreCase = true)) {
                binding!!.qualityText.text = getString(R.string.streaming_hd)
            } else if (qualityName.equals(resources.getString(R.string.streaming_full_hd), ignoreCase = true)) {
                binding!!.qualityText.text = getString(R.string.streaming_full_hd)
            }
        }
    }

    override fun onStart() {
        super.onStart()
        if (binding != null) {
            setQualityText(KsPreferenceKeys.getInstance().qualityName)
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.downloadLayout -> {
                val intent = Intent(this@ActivitySettings, DownloadSettings::class.java)
                startActivity(intent)
            }
            R.id.video_quality -> {
                val intent = Intent(this@ActivitySettings, VideoQualityActivity::class.java)
                startActivity(intent)
            }
            R.id.parent_layout -> {
                val intent = Intent(this@ActivitySettings, ChangeLanguageActivity::class.java)
                startActivity(intent)
            }
            R.id.content_pref_parent_layout -> {
                 if (featureModel?.featureFlag?.APP_USER_INTEREST == true){
                     if (isLoggedIn == true) {
                         val intent = Intent(this@ActivitySettings, UserInterestActivity::class.java)
                         startActivity(intent)
                     }
                     else{
                         ActivityLauncher.getInstance().loginActivity(this, ActivityLogin::class.java, "")
                     }
                 }
                else{
                     val intent = Intent(this@ActivitySettings, SettingContentPreferences::class.java)
                     startActivity(intent)
                }
            }
        }
    }
}