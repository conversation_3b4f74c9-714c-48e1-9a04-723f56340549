package com.enveu.activities.settings

import android.annotation.SuppressLint
import android.os.Bundle
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.settings.adapter.UserInterestAdapter
import com.enveu.activities.settings.listener.UserInterestListener
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.baseModels.BaseActivity
import com.enveu.beanModelV3.mutli_profile_response.SecondaryProfileData
import com.enveu.databinding.ActivityUserInterestBinding
import com.enveu.networking.response.InterestItems
import com.enveu.utils.Constants
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.show
import com.google.gson.Gson

class UserInterestActivity : BaseActivity() {
    private lateinit var binding: ActivityUserInterestBinding
    private lateinit var viewModel: RegistrationLoginViewModel
    private var userInterestAdapter: UserInterestAdapter? = null
    private var authToken: String? = null
    private var userInterestList: ArrayList<InterestItems?>? = ArrayList()
    private var userProfileData: SecondaryProfileData? = null
    private var viaLogin:Boolean? = false
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_user_interest)
        initUi()
    }

    private fun initUi() {
        binding.backImage.let { rotateImageLocaleWise(it) }
        viewModel = ViewModelProvider(this)[RegistrationLoginViewModel::class.java]
        authToken = KsPreferenceKeys.getInstance().appPrefAccessToken
        viaLogin = intent.getBooleanExtra(AppConstants.VIA_LOGIN, false)
        hitUserProfileApi(authToken)

        if (viaLogin != true)
            binding.backBtn.show()
        else
            binding.backBtn.hide()

        binding.btnSave.setOnClickListener {
            updateProfile()
        }
        binding.backBtn.setOnClickListener { finish() }
    }

    private fun updateProfile() {
        binding.progressBar.show()
        val selectedInterest = userInterestAdapter?.getInterestItems()
        userProfileData?.appUserInterest = selectedInterest
        viewModel.updateParentProfile(userProfileData, authToken).observe(this){ parentProfileResponse ->
            binding.progressBar.hide()
            if (parentProfileResponse != null){
               // Toast.makeText(this, "User Interest Save Successfully", Toast.LENGTH_LONG).show()
                if (viaLogin == true){
                    ActivityLauncher.getInstance().homeActivity(this, HomeActivity::class.java)
                }
                else{
                    finish()
                }
            }
        }
    }

    private val userInterestListener = object : UserInterestListener {
        override fun onUserInterestClickListener(count: Int) = if (count == 0) {
            binding.btnSave.background = ContextCompat.getDrawable(this@UserInterestActivity, R.drawable.default_button_background)
            binding.btnSave.text = getString(R.string.select_at_least_one_interest)
            binding.btnSave.isEnabled = false
            binding.btnSave.isClickable = false
        } else {
            binding.btnSave.background = ContextCompat.getDrawable(this@UserInterestActivity, R.drawable.signup_btn_gradient)
            binding.btnSave.text = getString(R.string.save_text)
            binding.btnSave.isEnabled = true
            binding.btnSave.isClickable = true
        }
    }

    private fun hitUserProfileApi(authToken: String?) {
        binding.progressBar.show()
        viewModel.hitUserProfile(this, authToken)?.observe(this) { profileResponse ->
            if (profileResponse != null) {
                userProfileData = Gson().fromJson(Gson().toJson(profileResponse.data), SecondaryProfileData::class.java)
                getUserInterestList()
            }
            else{
                binding.progressBar.hide()
            }
        }
    }

    private fun getUserInterestList() {
        viewModel.getUserInterestList(INTEREST, 0, 20).observe(this) { userInterestResponse ->
            binding.progressBar.hide()
            if (userInterestResponse.data?.items?.isNotEmpty() == true) {
                val idsArrays = userProfileData?.appUserInterest?.map { item -> item }
                val finalUserInterestList:ArrayList<InterestItems?> = ArrayList(
                    userInterestResponse.data.items.map { selectedItem ->
                        selectedItem?.copy(isSelected = idsArrays?.contains(selectedItem.id) == true)
                    }
                )
                userInterestList?.addAll(finalUserInterestList)
                setUserInterestAdapter()
            }
        }
    }

    override fun onBackPressed() {
        if (viaLogin == true) {
            super.onBackPressed()
        }
        else{
            finish()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setUserInterestAdapter() {
        if (userInterestAdapter == null) {
            userInterestAdapter = UserInterestAdapter(userInterestList, userInterestListener)
            binding.rvCircleImages.layoutManager = GridLayoutManager(this, 2)
            binding.rvCircleImages.adapter = userInterestAdapter
        } else {
            userInterestAdapter?.notifyDataSetChanged()
        }
    }

    companion object {
        const val INTEREST = "INTEREST"
    }
}