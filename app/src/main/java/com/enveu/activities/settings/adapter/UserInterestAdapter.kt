package com.enveu.activities.settings.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.settings.listener.UserInterestListener
import com.enveu.networking.response.InterestItems
import com.enveu.utils.Constants
import com.enveu.utils.getImageUrl
import com.enveu.utils.hide
import com.enveu.utils.setImageWithGlide
import com.enveu.utils.show


class UserInterestAdapter(
    private val userInterestList: ArrayList<InterestItems?>?,
   private val userInterestListener: UserInterestListener
) : RecyclerView.Adapter<UserInterestAdapter.ViewHolder>() {

    private val selectedUserInterestList:ArrayList<Int?> = ArrayList()

    private var enableButton:Boolean = true

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val rootLayout = LayoutInflater.from(parent.context).inflate(R.layout.item_select_preferences, parent, false)
        return ViewHolder(rootLayout)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val userInterest = userInterestList?.get(position)
        holder.genreTv?.text = userInterest?.title
        holder.apply {
            genreIv?.setImageWithGlide(genreIv.context, getImageUrl(Constants.ONE_INTO_ONE, userInterest?.images, genreIv.context))
        }

        if (userInterest?.isSelected == true){
            addInterestItems(holder.parentLayout, holder.checkedImg, position)
            if (enableButton){
                userInterestListener.onUserInterestClickListener(selectedUserInterestList.size)
                enableButton = false
            }
        }
        else{
            holder.parentLayout?.background = holder.parentLayout?.context?.let { ContextCompat.getDrawable(it, R.drawable.un_selected_interest_bg) }
            holder.checkedImg?.hide()
        }
    }

    override fun getItemCount():Int {
        return userInterestList?.size?:0
    }

    fun getInterestItems(): ArrayList<Int?> {
        return selectedUserInterestList
    }

   @SuppressLint("NotifyDataSetChanged")
   inner class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val genreTv:TextView? = view.findViewById(R.id.genreTv)
        val genreIv:ImageView? = view.findViewById(R.id.genreIv)
        val parentLayout:ConstraintLayout? = view.findViewById(R.id.parentLayout)
        val checkedImg:ImageView? = view.findViewById(R.id.checkedImg)

       init {
           parentLayout?.setOnClickListener {
               if (selectedUserInterestList.contains(userInterestList?.get(bindingAdapterPosition)?.id)){
                   selectedUserInterestList.remove(userInterestList?.get(bindingAdapterPosition)?.id)
                   parentLayout.background = ContextCompat.getDrawable(parentLayout.context, R.drawable.un_selected_interest_bg)
                   checkedImg?.hide()
               }
               else{
                  if (selectedUserInterestList.size < 5){
                   addInterestItems(parentLayout, checkedImg, bindingAdapterPosition)
                  }
               }
               userInterestListener.onUserInterestClickListener(selectedUserInterestList.size)
           }
       }
    }

    private fun addInterestItems(parentLayout:ConstraintLayout?, checkedImg:ImageView?, position: Int){
        selectedUserInterestList.add(userInterestList?.get(position)?.id)
        parentLayout?.background = parentLayout?.context?.let { ContextCompat.getDrawable(it, R.drawable.selected_interest_bg) }
        checkedImg?.show()
    }
}

