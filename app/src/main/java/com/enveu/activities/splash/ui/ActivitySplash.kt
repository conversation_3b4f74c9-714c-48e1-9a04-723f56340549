package com.enveu.activities.splash.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.media.MediaPlayer
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.Base64
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AppCompatDelegate
import androidx.lifecycle.ViewModelProvider
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.SkuDetails
import com.enveu.OttApplication
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.purchase.in_app_billing.BillingProcessor
import com.enveu.activities.purchase.in_app_billing.InAppProcessListener
import com.enveu.activities.purchase.in_app_billing.RestoreSubscriptionCallback
import com.enveu.activities.purchase.plans_layer.GetPlansLayer
import com.enveu.activities.settings.UserInterestActivity
import com.enveu.activities.splash.dialog.ConfigFailDialog
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.callbacks.apicallback.ApiResponseModel
import com.enveu.callbacks.commonCallbacks.DialogInterface
import com.enveu.callbacks.commonCallbacks.VersionUpdateCallBack
import com.enveu.databinding.ActivitySplashBinding
import com.enveu.fragments.dialog.AlertDialogFragment
import com.enveu.menuManager.viewmodel.MenuViewModel
import com.enveu.networking.errormodel.ApiErrorModel
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.config.ConfigManager
import com.enveu.utils.config.LanguageLayer
import com.enveu.utils.config.bean.dmsResponse.ConfigBean
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.AnalyticsController
import com.enveu.utils.helpers.ForceUpdateHandler
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.SharedPrefHelper
import com.enveu.utils.helpers.StringUtils.isNullOrEmpty
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.android.gms.common.GooglePlayServicesNotAvailableException
import com.google.android.gms.common.GooglePlayServicesRepairableException
import com.google.android.gms.common.GooglePlayServicesUtil
import com.google.android.gms.security.ProviderInstaller
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.google.firebase.dynamiclinks.PendingDynamicLinkData
import com.google.gson.Gson
import com.moengage.core.analytics.MoEAnalyticsHelper
import com.moengage.core.model.AppStatus
import org.json.JSONObject
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

class ActivitySplash : BaseBindingActivity<ActivitySplashBinding?>(), AlertDialogFragment.AlertDialogListener,
    InAppProcessListener {
    private var menuViewModel: MenuViewModel?= null
    private val TAG = this.javaClass.simpleName
    private var forceUpdateHandler: ForceUpdateHandler? = null
    private var session: KsPreferenceKeys? = null
    private var viaIntent = false
    private var configBean: ConfigBean? = null
    private var configCall = 1
    private var selectedArtistId=""
    var isLoggedIn = false
    private var viewModel : RegistrationLoginViewModel? = null
    var clapanimation = 1
    private var notid: String? = ""
    private var notAssetType: String? = ""
    private var notificationAssetId = 0
    private var deepLinkObject: JSONObject? = null
    private val colorsHelper by lazy { ColorsHelper }
    private val stringsHelper by lazy { StringsHelper }
    override fun inflateBindingLayout(inflater: LayoutInflater): ActivitySplashBinding {
        return ActivitySplashBinding.inflate(inflater)
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AppCompatDelegate.setCompatVectorFromResourcesEnabled(true)
        connectionObserver()
        binding?.connection?.retryTxt?.setOnClickListener { connectionObserver() }
    }

    private fun initView() {
        if (TextUtils.isEmpty(KsPreferenceKeys.getInstance().qualityName)) {
            KsPreferenceKeys.getInstance().qualityName = "Auto"
            KsPreferenceKeys.getInstance().qualityPosition = 0
        }
        SharedPrefHelper.getInstance().setColorJson(ColorsHelper.loadDataFromJson())
        SharedPrefHelper.getInstance().setStringJson(StringsHelper.loadDataFromJson())
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
        binding?.colorsData = colorsHelper
        session = KsPreferenceKeys.getInstance()
        initBilling()
        AppCommonMethod.getPushToken(this)
        updateAndroidSecurityProvider(this@ActivitySplash)
        AnalyticsController(this@ActivitySplash).callAnalytics("splash_screen", "Action", "Launch")
        currentLanguage = KsPreferenceKeys.getInstance().appLanguage
        loadAnimations()
        // binding.noConnectionLayout.btnMyDownloads.setOnClickListener(view -> ActivityLauncher.getInstance().launchMyDownloads(ActivitySplash.this));
        Logger.d("IntentData: " + this.intent.data)
        printKeyHash()
       // dynamicLink
    }



    var bp: BillingProcessor? = null
    private fun initBilling() {
        bp = BillingProcessor(this@ActivitySplash, this)
        bp?.initializeBillingProcessor()
        val serviceIntent = Intent("com.android.vending.billing.InAppBillingService.BIND")
        serviceIntent.setPackage("com.android.vending")
    }

    private var firebaseDynamicLink: JSONObject? = null


    private val dynamicLink: Unit
        private get() {
            FirebaseDynamicLinks.getInstance()
                .getDynamicLink(intent)
                .addOnSuccessListener(this) { pendingDynamicLinkData: PendingDynamicLinkData? ->
                    try {
                        val deepLink: Uri?
                        if (pendingDynamicLinkData != null) {
                            deepLink = pendingDynamicLinkData.link
                            if (deepLink != null) {
                                val uri = Uri.parse(deepLink.toString())
                                var id: String? = null
                                var mediaType: String? = null
                                var contentSlug: String? = null
                                try {
                                    id = uri.getQueryParameter("id")
                                    mediaType = uri.getQueryParameter(MEDIA_TYPE)
                                    contentSlug = uri.getQueryParameter(CONTENT_SLUG)
                                    if (contentSlug != null) {
                                        KsPreferenceKeys.getInstance().contentSlug = contentSlug
                                    }
                                } catch (e: Exception) {
                                    homeRedirection()
                                }
                                try {
                                    if (mediaType != null) {
                                        if (!mediaType.equals("", ignoreCase = true) && !id.equals("", ignoreCase = true)) {
                                            KsPreferenceKeys.getInstance().appPrefJumpTo = mediaType
                                            KsPreferenceKeys.getInstance().appPrefBranchIo = true
                                            KsPreferenceKeys.getInstance().appPrefJumpBackId =
                                                id!!.toInt()
                                            deepLinkObject = AppCommonMethod.createDynamicLinkObject(id, mediaType,contentSlug)
                                            firebaseDynamicLink = AppCommonMethod.createDynamicLinkObject(id, mediaType, contentSlug)
                                            callConfig(deepLinkObject, "firebase")

                                        }
                                    } else {
                                        homeRedirection()
                                    }
                                } catch (e: Exception) {
                                    homeRedirection()
                                }
                            } else {
                                homeRedirection()
                            }
                        } else {
                            onNewIntent(intent)
                        }
                    } catch (e: Exception) {
                        Logger.e("Catch", e.toString())
                    }
                }
                .addOnFailureListener(this) { e: Exception? ->
                    Logger.w(e)
                    Log.w(TAG, "getDynamicLink:onFailure", e)
                }
        }

    private fun printKeyHash(): String? {
        val packageInfo: PackageInfo
        var key: String? = null
        try {
            //getting application package name, as defined in manifest
            val packageName: String = this.packageName

            //Retrieving package info
            packageInfo = this.packageManager.getPackageInfo(
                packageName,
                PackageManager.GET_SIGNATURES
            )
            Log.e("Package Name=", this.applicationContext.packageName)
            for (signature in packageInfo.signatures!!) {
                val md = MessageDigest.getInstance("SHA")
                md.update(signature.toByteArray())
                key = String(Base64.encode(md.digest(), 0))

                // String key = new String(Base64.encodeBytes(md.digest()));
                Log.e("Key Hash=", key)
            }
        } catch (e1: PackageManager.NameNotFoundException) {
            Log.e("Name not found", e1.toString())
        } catch (e: NoSuchAlgorithmException) {
            Log.e("No such an algorithm", e.toString())
        } catch (e: Exception) {
            Log.e("Exception", e.toString())
        }
        return key
    }
    private fun getMenu(jsonObject: JSONObject?, updateType: String?, isTablet: Boolean) {
        menuViewModel = ViewModelProvider(this@ActivitySplash)[MenuViewModel::class.java]
        menuViewModel?.getMenuManager(LanguageLayer.getCurrentLanguageCode(), this@ActivitySplash)?.observe(this@ActivitySplash){
            if (it?.data?.orderedMenuItems != null){
                KsPreferenceKeys.getInstance().saveDataMenuKeyValue(it.data.orderedMenuItems)
                startClapAnimation(jsonObject, updateType, isTablet)
            }
        }
    }

    private fun callGeoApi() {
        viewModel?.geoInform?.observe(this) {
            if (it != null) {
                KsPreferenceKeys.getInstance().countryCode = it.data
                Log.d("checkCountryCode", it?.data!!)
            }
        }
    }

    private fun callConfig(jsonObject: JSONObject?, updateType: String?) {
        ConfigManager.getInstance().getConfig(this@ActivitySplash, object : ApiResponseModel<Any> {
            override fun onStart() {}
            override fun onSuccess(response: Any?) {
                val isTablet: Boolean = resources.getBoolean(R.bool.isTablet)
                configBean = AppCommonMethod.configResponse
                val gson = Gson()
                val json: String = gson.toJson(configBean)
                Logger.d("configResponseLog: $json")
                Logger.d("configResponse: $response")
                KsPreferenceKeys.getInstance().saveConfigLocaleData(configBean?.data?.appConfig?.languageCodes)
                KsPreferenceKeys.getInstance().saveSearchScreenIdentifier(configBean?.data?.appConfig?.searchScreenIdentifier)
                AppCommonMethod.setConfigConstant(configBean, isTablet)
                AppConfigMethod.setMediaTypeJson(applicationContext)
                setupBaseClient()
                updateLanguage(configBean?.data?.appConfig?.primaryLanguage?:AppConstants.ENGLISH_LAN_CODE)
                KsPreferenceKeys.getInstance().ovpbaseurl = SDKConfig.getInstance().ovP_BASE_URL
                KsPreferenceKeys.getInstance().updatePlaybackSessionTime = SDKConfig.getInstance().updatePlaybackSessionTime
                if (configBean != null) {
                    getMenu(jsonObject,updateType,isTablet)
                } else {
                    getMenu(jsonObject, updateType, isTablet)
                    configFailPopup()
                }
                callGeoApi()
            }

            override fun onError(httpError: ApiErrorModel) {
                configFailPopup()
            }

            override fun onFailure(httpError: ApiErrorModel) {
                configFailPopup()
            }
        })
    }

    private fun startClapAnimation(
        jsonObject: JSONObject?,
        updateType: String?,
        isTablet: Boolean,
    ) {
        val isUserVerified: String
        val preference: KsPreferenceKeys = KsPreferenceKeys.getInstance()
        if (preference.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) { isLoggedIn = true }
        isUserVerified = preference.isVerified

        if (!isNullOrEmpty(KsPreferenceKeys.getInstance().sponsorArtistId) && preference.isPrimaryAccountUser) {
            getProfile()
        }
        Logger.d("branchRedirectors onAnimationEnd1")
        if (jsonObject != null) {
            if (updateType != null && updateType.equals(
                    ForceUpdateHandler.RECOMMENDED,
                    ignoreCase = true
                )
            ) {
                branchRedirections(jsonObject)
            } else {
                val updateValue = getForceUpdateValue(jsonObject, 1)
                if (!updateValue) {
                    branchRedirections(jsonObject)
                }
            }
        } else {
            if (updateType != null && updateType.equals(
                    ForceUpdateHandler.RECOMMENDED,
                    ignoreCase = true
                )
            ) {
                Handler(Looper.getMainLooper()).postDelayed({
                    redirectionInApp()
                }, 1)
            } else {
                val updateValue = getForceUpdateValue(null, 3)
                if (!updateValue) {
                    Handler().postDelayed({
                        redirectionInApp()
                    }, 1)
                }
            }
        }
    }

    @SuppressLint("SuspiciousIndentation")
    private fun redirectionInApp(){
      val featureList = AppConfigMethod.parseFeatureFlagList()
        KsPreferenceKeys.getInstance().concurrencyEnable = featureList.featureFlag.IS_CONCURRENCY_ENABLE
        val userProfileData = KsPreferenceKeys.getInstance().isActiveUserProfileData

        when {
            featureList.featureFlag.ALLOW_ANONYMOUS_USER_ACCESS_CONTENT || isLoggedIn -> {
                if (isLoggedIn && userProfileData?.appUserInterest.isNullOrEmpty()) {
                    ActivityLauncher.getInstance().navigateUserInterestActivity(this@ActivitySplash, UserInterestActivity::class.java)
                } else {
                    ActivityLauncher.getInstance().homeActivity(this, HomeActivity::class.java)
                }
            }
            featureList.featureFlag.LOGIN_WITH_EMAIL -> {
                ActivityLauncher.getInstance().loginActivity(this@ActivitySplash, ActivityLogin::class.java, "")
            }
        }
//        if (featureList.featureFlag.ALLOW_ANONYMOUS_USER_ACCESS_CONTENT || isLoggedIn){
//            if (isLoggedIn) {
//                // get userProfile Data and check appUserIntrests size
//                val userProfileData = KsPreferenceKeys.getInstance().userProfile
//                if (userProfileData?.data?.appUserInterest.isNullOrEmpty()) {
//                    ActivityLauncher.getInstance().navigateUserInterestActivity(this@ActivitySplash, UserInterestActivity::class.java)
//                } else {
//                    ActivityLauncher.getInstance().homeActivity(this, HomeActivity::class.java)
//                }
//            }else{
//                ActivityLauncher.getInstance().homeActivity(this, HomeActivity::class.java)
//            }
//        }else if(featureList.featureFlag.LOGIN_WITH_EMAIL){
//            ActivityLauncher.getInstance().loginActivity(this@ActivitySplash,ActivityLogin::class.java, "")
//        }
        finish()
    }

    private fun notificationCheck() {
        Logger.w("notificationCheck", "in")
        if (intent != null) {
            Logger.w("notificationCheck", "notnull")
            if (intent.extras != null) {
                Logger.w("notificationCheck", "extra")
                val bundle: Bundle? = intent.extras
                if (bundle != null) {
                    notid = bundle.getString("id")
                    if (notid != null && !notid.equals("", ignoreCase = true)) {
                        notAssetType = bundle.getString("mediaType")
                        if (notAssetType != null && !notAssetType.equals("", ignoreCase = true)) {
                            parseNotification(notid = notid,assetType = notAssetType)
                        } else {
                            onNewIntent(intent)
                        }
                    } else {
                        Logger.d("myApplication--->>>$intent")
                        onNewIntent(intent)
                    }
                } else {
                    onNewIntent(intent)
                }
            } else {
                Logger.w("notificationCheck", "nonextra")
                onNewIntent(intent)
            }
        } else {
            Logger.w("notificationCheck", "null")
        }
    }


    private fun loadAnimations() {
        var video: Uri? = null
        video = Uri.parse("android.resource://$packageName" + "/" + R.raw.splash_vid)
        binding!!.videoView.setVideoURI(video)
        binding!!.videoView.requestFocus()
        binding!!.videoView.start()
        binding!!.videoView.setOnCompletionListener {
//            notificationCheck()
        }
        binding!!.videoView.setOnPreparedListener { mediaPlayer ->
            mediaPlayer.setVolume(0f, 0f)
        }
        binding!!.videoView.setOnErrorListener { mp, what, extra -> false }

        Handler(Looper.getMainLooper()).postDelayed({
            notificationCheck()
        }, 4000)
    }

    private fun callNextForRedirection() {
        if (viaIntent) {
            val notiVAlues: String = KsPreferenceKeys.getInstance().getNotificationPayload(notificationAssetId.toString() + "")
            try {
                Logger.e("Animation End", "Config Call")
                val jsonObject = JSONObject(notiVAlues)
                redirections(jsonObject)
            } catch (e: Exception) {
                if (notificationObject != null) {
                    redirections(notificationObject)
                } else {
                    redirections(null)
                }
            }
        } else {
            Logger.w("callNext-forRedirection", "else")
        }
    }

    private fun updateAndroidSecurityProvider(callingActivity: Activity) {
        try {
            ProviderInstaller.installIfNeeded(this)
        } catch (e: GooglePlayServicesRepairableException) {
            // Thrown when Google Play Services is not installed, up-to-date, or enabled
            // Show dialog to allow users to install, update, or otherwise enable Google Play services.
            GooglePlayServicesUtil.getErrorDialog(e.connectionStatusCode, callingActivity, 0)
        } catch (e: GooglePlayServicesNotAvailableException) {
            Logger.e("SecurityException", "Google Play Services not available.")
        }
    }

    override fun onStart() {
        super.onStart()
        if (KsPreferenceKeys.getInstance().fromOnboard) {
            KsPreferenceKeys.getInstance().fromOnboard = false
            finish()
        }
    }


    private fun homeRedirection() {
        Logger.w("branchRedirectors $configCall")
        if (configCall == 1) {
            Logger.d("branchRedirectors $configCall")
            configCall = 2
            callConfig(null, null)
        }
    }

    var configRetry = false
    private fun configFailPopup() {
        if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.DUTCH, ignoreCase = true)) {
            AppCommonMethod.updateLanguage(AppConstants.DUTCH_LAN_CODE, OttApplication.instance!!)
        } else if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.ENGLISH, ignoreCase = true)) {
            AppCommonMethod.updateLanguage(AppConstants.ENGLISH_LAN_CODE, OttApplication.instance!!)
        }
        ConfigFailDialog(this@ActivitySplash)
            .showDialog(object :
                DialogInterface {
            override fun positiveAction() {
                Handler(Looper.getMainLooper()).postDelayed({
                    binding?.progressBar?.visibility = View.VISIBLE
                    configRetry = true
                    callConfig(null, null)
                }, 200)
            }

            override fun negativeAction() {
                binding?.progressBar?.visibility = View.GONE
                finish()
            }
        })
    }

    private fun redirections(jsonObject: JSONObject?) {
        try {
            callConfig(jsonObject, null)
        } catch (e: Exception) {
        }
    }

    private fun connectionObserver() {
        if (NetworkConnectivity.isOnline(this)) {
            connectionValidation(true)
        } else {
            connectionValidation(false)
        }
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            binding?.noConnectionLayout?.visibility = View.GONE
            initView()
        } else {
            binding?.noConnectionLayout?.visibility = View.VISIBLE
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        this.intent = intent
        if (intent.data != null) {
            try {
                if (intent.data?.path != null) {
                    val uri = intent?.data
                    val cleanedUriPath = uri?.path?.split('?')?.get(0) // removes any query params

                    cleanedUriPath?.let {
                        val pathComponents = it.split("/").filter { it.isNotBlank() }

                         contentType = pathComponents.getOrNull(1).toString() // "video"
                         contentSlug = pathComponents.getOrNull(2).toString().toString() // "movie-1011"

                        Log.d("DEEPLINK", "contentType = $contentType, contentSlug = $contentSlug")
                    }
                } else {
                    viaIntent = true
                    notid = intent.getStringExtra("id")
                    notAssetType = intent.getStringExtra("mediaType")
                }
                parseNotification(contentType, contentSlug, notid, notAssetType)
            } catch (e: Exception) {
                Logger.e(e)
            }
        } else {
            homeRedirection()
        }
    }
    private var notificationObject: JSONObject? = null
    private fun parseNotification(contentTYpe: String?= null, contentSlug: String?= null, notid : String? = null , assetType : String? = null) {
        if (notid == null && assetType.isNullOrEmpty()) {
            if (contentTYpe != null && contentSlug != null){
                deepLinkObject = AppCommonMethod.createDynamicLinkObject("", contentTYpe, contentSlug)
                firebaseDynamicLink =
                    AppCommonMethod.createDynamicLinkObject("", contentTYpe, contentSlug)
                Logger.d("mediaTypeFrom", contentTYpe.toString())
                if (contentTYpe.equals(AppConstants.SONGS, ignoreCase = true)) {
                    if (contentSlug != null) {
                        KsPreferenceKeys.getInstance().contentSlug = contentSlug
                    }
                }
                callConfig(deepLinkObject, "firebase")
            }else{
                homeRedirection()
            }
        }
        else if (notid != null && !assetType.equals("", ignoreCase = true)) {
            notificationAssetId = notid.toInt()
            if (notificationAssetId > 0 && assetType != null && !assetType.equals(
                    "",
                    ignoreCase = true
                )
            ) {
                Logger.w("FCM_Payload_final --", notificationAssetId.toString() + "")
                Logger.w("FCM_Payload_final --", assetType + "")
                notificationObject = AppCommonMethod.createNotificationObject(notid, assetType)
                viaIntent = true
                callConfig(notificationObject, "firebase")
            }
        } else {
            homeRedirection()
        }
    }


    override fun onFinishDialog() {
        connectionObserver()
    }

    protected override fun onPause() {
        super.onPause()
    }

    var forceUpdate = false
    private fun getForceUpdateValue(jsonObject: JSONObject?, type: Int): Boolean {
        if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.DUTCH, ignoreCase = true)) {
            AppCommonMethod.updateLanguage(AppConstants.DUTCH_LAN_CODE, OttApplication.instance!!)
        } else if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.ENGLISH, ignoreCase = true)) {
            AppCommonMethod.updateLanguage(AppConstants.ENGLISH_LAN_CODE, OttApplication.instance!!)
        }
        forceUpdateHandler =
            ForceUpdateHandler(
                this@ActivitySplash,
                configBean
            )
        forceUpdateHandler!!.checkCurrentVersion { status, _, _, updateType ->
            if (status) {
                // For Existing user who has updated the app
                MoEAnalyticsHelper.setAppStatus(application, AppStatus.UPDATE)
                forceUpdate = true
                forceUpdateHandler!!.typeHandle(updateType,
                    VersionUpdateCallBack { selection: Boolean ->
                        if (updateType == ForceUpdateHandler.RECOMMENDED) {
                            if (!selection) {
                                binding?.progressBar?.visibility = View.VISIBLE
                                forceUpdateHandler!!.hideDialog()
                                clapanimation = 1
                                callConfig(null, updateType)
                            }
                        }
                    })
            } else {
                forceUpdate = false
            }
        }
        return forceUpdate
    }

    override fun onBillingInitialized() {
        Log.w("identifiers", "in onBillingInitialized")
        restorePurchase()
    }

    private fun restorePurchase() {
        if (KsPreferenceKeys.getInstance().appPrefLoginStatus.equals(
                AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            if (bp != null) {
                if (bp!!.isReady) {
                    bp!!.queryPurchases(object :
                        RestoreSubscriptionCallback {
                        override fun subscriptionStatus(status: Boolean, message: String) {}
                        override fun subscriptionHistory(status: Boolean, purchases: List<Purchase>) {
                            if (status) {
                                GetPlansLayer.getInstance().getEntitlementStatus(
                                    KsPreferenceKeys.getInstance(),
                                    KsPreferenceKeys.getInstance().appPrefAccessToken
                                ) { entitlementStatus: Boolean, apiStatus: Boolean, offerStatus, onHold : Boolean,responseCode: Int ->
                                    if (apiStatus) {
                                        if (entitlementStatus) {
                                        } else {
                                            if (responseCode == 100) {
                                                bp!!.checkPurchase(purchases)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }, 1)
                }
            }
        }
    }

    override fun onPurchasesUpdated(billingResult: BillingResult, purchases: List<Purchase>?) {}
    override fun onListOfSKUFetched(purchases: List<SkuDetails>?) {}
    override fun onBillingError(error: BillingResult?) {}

    companion object {
        private const val MEDIA_TYPE = "mediaType"
        private const val CONTENT_SLUG = "contentSlug"
    }

    private fun getProfile() {
        val authToken = KsPreferenceKeys.getInstance().appPrefAccessToken
        viewModel?.hitUserProfile(this@ActivitySplash,authToken)?.observe(this) {
            if (it != null) {
                if (it.status) {
                    Logger.e("profileRes", it.toString())
                    selectedArtistId = it.data.customData.sponsoredArtist?:""
                    KsPreferenceKeys.getInstance().sponsorArtistId = selectedArtistId

                }
            } }
    }

}