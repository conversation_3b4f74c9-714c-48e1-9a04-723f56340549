package com.enveu.activities.sponsorArtist

import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.appcompat.widget.SearchView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.adapters.ArtistRecyclerViewAdapter
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.SaveCheckedArtistClickListener
import com.enveu.databinding.ActivityMusicSearchBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.utils.Logger
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import java.util.Objects

class ArtistActivity : BaseBindingActivity<ActivityMusicSearchBinding?>(), SearchView.OnQueryTextListener,CommonDialogFragment.EditDialogListener{
  //  private lateinit var binding: ActivityMusicSearchBinding
    private var railInjectionHelper: RailInjectionHelper? = null
    private var viewModel: RegistrationLoginViewModel? = null

    private lateinit var saveCheckedArtistClickListener : SaveCheckedArtistClickListener
    var mLastClickTime : Long = 0
    private var selectedArtist:EnveuVideoItemBean?=null
    private var appUserInterest: List<Int> = ArrayList<Int>()
    private var firstVisiblePosition = 0
    private var pastVisiblesItems = 0
    private var visibleItemCount = 0
    private var mIsLoading = true
    private var selectedArtistId=""
    private var counter = 0
    private var isScrolling = false
    private var mScrollY = 0
    private var totalItemCount = 0
    private  var musicRecyclerViewAdapter:ArtistRecyclerViewAdapter? = null
    private var searchText: String? = ""
    private var applyFilter = false
    private val stringsHelper by lazy { StringsHelper }


    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityMusicSearchBinding {
        return ActivityMusicSearchBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        //binding = DataBindingUtil.setContentView(this, R.layout.activity_music_search)
        AnalyticsUtils.trackScreenView(this,AppConstants.ARTISTS)
        super.onCreate(savedInstanceState)
        connectionObserver()


    }

    private fun setViewModel() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        viewModel = ViewModelProvider(this)[RegistrationLoginViewModel::class.java]

    }

    private fun connectionObserver() {
        if (NetworkConnectivity.isOnline(this)) {
            connectionValidation(true)
        } else {
//            commonDialog(
//                stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
//                    getString(R.string.popup_no_internet_connection_found)
//                ), "", stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                    getString(R.string.popup_continue)
//                )
//            )
        }
    }

    private fun connectionValidation(isConnected: Boolean) {
        if (isConnected){
            AnalyticsUtils.trackScreenView(this, AppConstants.SPONSOR_ARTIST)
            saveCheckedArtistClickListener = object : SaveCheckedArtistClickListener{
                override fun onSaveClick(
                    save: Boolean,
                    enveuVideoItemBean: EnveuVideoItemBean,
                    position: Int
                ) {
                    binding?.btnSave?.visibility = View.VISIBLE
                    selectedArtist=enveuVideoItemBean
                }

                override fun onUserDetail(
                    title: String?,
                    src: String?,
                    dataList: EnveuVideoItemBean
                ) {
                }

            }
            binding?.searchToolbar?.searchView?.setOnQueryTextListener(this)
            binding?.searchToolbar?.searchView?.setIconifiedByDefault(false) // Make sure the SearchView is not iconified (collapsed)
            // Set the hint for the SearchView
            binding?.searchToolbar?.searchView?.queryHint = getString(R.string.search_hint)
            binding?.rvCircleImages?.layoutManager = GridLayoutManager(this, 3)
            toolbar()
            setViewModel()
            setClick()
            getProfile()
            setPaginationListener()
        }else{
           noConnectionLayout()
        }
    }

    private fun getProfile() {
        val authToken = KsPreferenceKeys.getInstance().appPrefAccessToken
        viewModel?.hitUserProfile(this@ArtistActivity,authToken)?.observe(this) {
            if (it != null) {
                if (it.status) {
                    Logger.e("profileRes", it.toString())
                    if (it.data.deletionRequestStatus != null) {
                        val deletionRequestStatus: String = it.data.deletionRequestStatus
                        if (deletionRequestStatus.equals("UNDER_REVIEW")) {
                            KsPreferenceKeys.getInstance().deleteAccountRequestStatus = true
                        } else {
                            KsPreferenceKeys.getInstance().deleteAccountRequestStatus = false
                        }
                    }

                    selectedArtistId= it.data.customData.sponsoredArtist?:""
                    KsPreferenceKeys.getInstance().sponsorArtistId = selectedArtistId

                 //   updateUI(it)
                    dismissLoading(binding?.progressBar)
                    getAllArtist(null,selectedArtistId )
                }
                if (it.responseCode == 4302) {
                 //   isloggedout = true
                    logoutCall()
                    ActivityLauncher.getInstance().goToLogin(this@ArtistActivity, ActivityLogin::class.java)

                    try {
                        runOnUiThread { onBackPressed() }
                    } catch (_: java.lang.Exception) {
                    }
                } else if (it.responseCode == 4019) {
                    commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ), it.debugMessage.toString(), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                } else if (it.responseCode == 4901) {
                    commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ), it.debugMessage.toString(), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                }
            }else{
                commonDialog(stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                    getString(R.string.popup_error)
                ),stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                    getString(R.string.something_went_wrong)
                ) , stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))

            }

        }
    }

    private fun setClick() {
        binding?.btnSave?.setOnClickListener {
            binding?.progressBar?.visibility=View.VISIBLE
          //  updateProfile()
        }
    }

//    private fun updateProfile() {
//        val token = KsPreferenceKeys.getInstance().appPrefAccessToken
//        viewModel?.updateSponsor(token,appUserInterest)?.observe(this
//        ) {
//            if (it != null) {
//                if (it.status) {
//                    val gson = Gson()
//                    val userProfileData = gson.toJson(it)
//                    KsPreferenceKeys.getInstance().userProfileData = userProfileData
//                    KsPreferenceKeys.getInstance().sponsorArtistId = selectedArtist?.id.toString()
//                    Logger.e("userdata",userProfileData)
//                    commonDialog(
//                        stringsHelper.stringParse(
//                            stringsHelper.instance()?.data?.config?.popup_update.toString(),
//                            getString(R.string.popup_update)),
//                        stringsHelper.stringParse(
//                            stringsHelper.instance()?.data?.config?.popup_update_successfully.toString(),
//                            getString(R.string.popup_update_successfully))
//                        , stringsHelper.stringParse(
//                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                            getString(R.string.popup_continue)))
//                }else{
//                    if(it.responseCode==4302){
//                        dismissLoading(binding?.progressBar)
//                        logoutCall()
//                        ActivityLauncher.getInstance().goToLogin(this@ArtistActivity, ActivityLogin::class.java)
//                        try {
//                            runOnUiThread { onBackPressed() }
//                        } catch (e: java.lang.Exception) {
//                            Logger.d(e.toString())
//                        }
//                    }else if(it.responseCode==4019){
//                        commonDialog(it.debugMessage.toString(),"",stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
//                    }else{
//                        if (it.debugMessage!=null){
//                            commonDialog(it.debugMessage.toString(),"",stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
//                        }else{
//                            commonDialog(stringsHelper.stringParse(
//                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
//                                getString(R.string.popup_error)
//                            ), stringsHelper.stringParse(
//                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
//                                getString(R.string.something_went_wrong)
//                            ),stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
//
//                        }
//                    }
//
//                }
//            }else{
//                commonDialog(stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
//                    getString(R.string.popup_error)
//                ), stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
//                    getString(R.string.something_went_wrong)
//                ),stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
//
//            }
//            binding?.progressBar?.visibility=View.GONE
//
//        }
//    }

    private fun logoutCall() {
        if (CheckInternetConnection.isOnline(Objects.requireNonNull(this))) {
            val preference = KsPreferenceKeys.getInstance()
            clearCredientials(preference)
            hitApiLogout(this, KsPreferenceKeys.getInstance().appPrefAccessToken)
        }else{
            commonDialog(stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                getString(R.string.popup_no_internet_connection_found)
            ), "", stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))

        }
    }
    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }
    private fun setPaginationListener() {
        binding!!.rvCircleImages.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                try {
                        val layoutManager = recyclerView.layoutManager as GridLayoutManager?
                        firstVisiblePosition = layoutManager?.findFirstVisibleItemPosition()!!
                        if (dy > 0) {
                            visibleItemCount = layoutManager.childCount
                            totalItemCount = layoutManager.itemCount
                            pastVisiblesItems = layoutManager.findFirstVisibleItemPosition()
                            if (mIsLoading) {
                                if (visibleItemCount + pastVisiblesItems >= totalItemCount) {
                                    // Logger.d("slidingValues"+getBinding().listRecyclerview.getAdapter().getItemCount()+" "+counter);
                                    val adapterSize = binding!!.rvCircleImages.adapter!!.itemCount
                                    if (adapterSize > 8) {
                                        mIsLoading = false
                                        counter++
                                        isScrolling = true
                                        mScrollY += dy
                                        getAllArtist(null,selectedArtistId)
                                    }
                                }
                            }
                        }

                } catch (e: Exception) {
                    Logger.e("ListingActivity", "" + e)
                }
            }
        })
    }

    private fun getAllArtist(keyword: String?,selectedId:String) {
        binding?.progressBar?.visibility = View.VISIBLE
        railInjectionHelper?.getAllArtist(keyword,21,counter)?.observe(this){
            binding?.progressBar?.visibility = View.GONE
            if (it != null && it.isNotEmpty()) {
                binding?.tvNoResultFound?.visibility = View.GONE
               binding?.rvCircleImages?.visibility = View.VISIBLE
                if (keyword == null) {
                    if (musicRecyclerViewAdapter==null) {
                        musicRecyclerViewAdapter =
                            ArtistRecyclerViewAdapter(this,selectedId, it[0].enveuVideoItemBeans, saveCheckedArtistClickListener)
                        binding?.rvCircleImages?.adapter = musicRecyclerViewAdapter
                    }else{
                        musicRecyclerViewAdapter?.notifyData(it[0].enveuVideoItemBeans)
                        mIsLoading = it[0].pageTotal != musicRecyclerViewAdapter!!.itemCount
                    }
                }
                else{
                    musicRecyclerViewAdapter?.updateData(it[0].enveuVideoItemBeans)
                }
            }
            else{
                if (musicRecyclerViewAdapter?.list?.isEmpty() == true){
                    binding?.tvNoResultFound?.visibility = View.VISIBLE
                    binding?.rvCircleImages?.visibility = View.GONE
                }else{
                    musicRecyclerViewAdapter?.updateData(it[0].enveuVideoItemBeans)
                }
                binding?.tvNoResultFound?.visibility = View.VISIBLE
                binding?.btnSave?.visibility = View.GONE
            }
        }
    }


    override fun onQueryTextSubmit(query: String): Boolean {
        if (NetworkConnectivity.isOnline(this)) {
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                return true
            }
            mLastClickTime = SystemClock.elapsedRealtime()
            binding?.searchToolbar?.searchView?.let { hideSoftKeyboard(it) }
            if (query.trim { it <= ' ' }.length > 2) {
                applyFilter = java.lang.Boolean.parseBoolean(KsPreferenceKeys.getInstance().filterApply)
                searchText = query.trim { it <= ' ' }
                Log.d("SEARCH_TEXT", searchText.toString())
                getAllArtist(searchText,selectedArtistId)
            }

        }
        return false
    }

    override fun onQueryTextChange(newText: String?): Boolean {
        return false
    }

    private fun noConnectionLayout() {
            binding?.searchToolbar?.toolbarSearch?.visibility = View.GONE
            binding?.connection?.noConnectionLayout?.visibility = View.VISIBLE
            binding?.connection?.retryTxt?.setOnClickListener { connectionObserver() }
        }
    
    private fun toolbar() {
        binding?.toolbar?.apply {
            logoMain2.visibility = View.GONE
            searchIcon.visibility = View.GONE
            backLayout.visibility = View.VISIBLE
            titleMid.visibility = View.VISIBLE
            titleMid.text = getText(R.string.sponsor_artist)
            titleMid.setBackgroundResource(0)
            backLayout.setOnClickListener { onBackPressed() }
        }
    }


    override fun onActionBtnClicked() {
        onBackPressed()
    }

    override fun onCancelBtnClicked() {
    }
}