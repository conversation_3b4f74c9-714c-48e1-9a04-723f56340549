package com.enveu.activities.sponsorArtist

import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.appcompat.widget.SearchView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.adapters.ContentPreferenceAdapter
import com.enveu.adapters.SponserRecyclerViewAdapter
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.userProfile.UserProfileResponse
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.ContentPreferenceCallback
import com.enveu.callbacks.commonCallbacks.SaveCheckedArtistClickListener
import com.enveu.databinding.ActivityArtistSponserBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.utils.Logger
import com.enveu.utils.ObjectHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.SpacingItemDecoration
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import com.google.gson.JsonObject
import java.util.Objects


class ArtistAndSponserActivity : BaseBindingActivity<ActivityArtistSponserBinding?>(), SearchView.OnQueryTextListener,CommonDialogFragment.EditDialogListener,
    ContentPreferenceCallback {
  //  private lateinit var binding: ActivityArtistSponserBinding
    private var railInjectionHelper: RailInjectionHelper? = null
    private var viewModel: RegistrationLoginViewModel? = null

    private lateinit var saveCheckedArtistClickListener : SaveCheckedArtistClickListener
    var mLastClickTime : Long = 0
    private var selectedArtist:EnveuVideoItemBean?=null
    private var featureList: FeatureFlagModel? = null
    private var selectedSponserId = ""
    private var genreList:ArrayList<Int>?= ArrayList()
    private var posterUrl :String?= null
    private var artistName :String?= null
    private var assetTypeOne :String?= null
    private var firstVisiblePosition = 0
    private var pastVisiblesItems = 0
    private var visibleItemCount = 0
    private var mIsLoading = true
    private var selectedArtistId=""
    private var counter = 0
    private var isScrolling = false
    private var mScrollY = 0
    private var totalItemCount = 0
    private  var musicRecyclerViewAdapter: SponserRecyclerViewAdapter? = null
    private var searchText: String? = ""
    private var applyFilter = false
    private  val VERTICAL_ITEM_SPACE: Int = 10
    private val HORIONTAL_ITEM_SPACE: Int = 20
    private var commaSeparatedString :String = ""
    private var adatperContentPreference: ContentPreferenceAdapter? = null
    private val stringsHelper by lazy { StringsHelper }
    private var gender :String = ""
    private var name :String = ""
    private var userId :String = ""
    private var country :String = ""
    private var city :String = ""
    private var lastName :String = ""
    private var mobileNumber :String = ""
    private var dob = 0.0


    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityArtistSponserBinding {
        return ActivityArtistSponserBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        //binding = DataBindingUtil.setContentView(this, R.layout.activity_music_search)
        AnalyticsUtils.trackScreenView(this,AppConstants.ARTISTS)
        super.onCreate(savedInstanceState)
        getAppLevelJsonData()
        connectionObserver()
    }
    private fun getAppLevelJsonData() {
        featureList = AppConfigMethod.parseFeatureFlagList()
    }
    private fun setViewModel() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        viewModel = ViewModelProvider(this)[RegistrationLoginViewModel::class.java]

    }

    private fun connectionObserver() {
        if (NetworkConnectivity.isOnline(this)) {
            connectionValidation(true)
            uiInitialisation()
           // setAdapter()

        } else {
//            commonDialog(
//                stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
//                    getString(R.string.popup_no_internet_connection_found)
//                ), "", stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                    getString(R.string.popup_continue)
//                )
//            )
        }
    }

    private fun setAdapter(){

    }


    private fun uiInitialisation() {
        binding?.recyclerViewSponser?.setHasFixedSize(true)
        //binding?.recyclerViewSponser?.hasFixedSize()
        binding?.recyclerViewSponser?.isNestedScrollingEnabled = true
        binding?.recyclerViewSponser?.setLayoutManager(
            LinearLayoutManager(
                this,
                RecyclerView.HORIZONTAL,
                false
            )
        )
        binding?.recyclerViewSponser?.addItemDecoration(VerticalSpaceItemDecoration(VERTICAL_ITEM_SPACE))
        binding?.recyclerViewSponser?.addItemDecoration(HorizontalSpaceItemDecoration(HORIONTAL_ITEM_SPACE))
//        val flowLayoutManager = FlowLayoutManager()
//        flowLayoutManager.isAutoMeasureEnabled = true
//        binding?.recyclerViewSponser?.setLayoutManager(flowLayoutManager)
    }

    private fun connectionValidation(isConnected: Boolean) {
        if (isConnected){
            AnalyticsUtils.trackScreenView(this, AppConstants.SPONSOR_ARTIST)
            saveCheckedArtistClickListener = object : SaveCheckedArtistClickListener{
                override fun onSaveClick(
                    save: Boolean,
                    enveuVideoItemBean: EnveuVideoItemBean,
                    position: Int
                ) {
                    binding?.btnSave?.visibility = View.VISIBLE
                    selectedArtist=enveuVideoItemBean
                    selectedSponserId = enveuVideoItemBean.id.toString()
                    posterUrl = enveuVideoItemBean.images?.get(0)?.imageContent?.src
                    artistName = enveuVideoItemBean.title
                    assetTypeOne = enveuVideoItemBean.assetType
                    contentSlug= enveuVideoItemBean.contentSlug
                }

                override fun onUserDetail(
                    title: String?,
                    src: String?,
                    dataList: EnveuVideoItemBean
                ) {
                    if (src != null && !src.isEmpty()){
                        binding?.bgLay?.mainCard?.visibility = View.VISIBLE
                        ImageHelper.getInstance(this@ArtistAndSponserActivity).loadCircleImageTo(binding?.bgLay?.artistImg,src)
                    }else{
                        binding?.bgLay?.artistImg?.setBackgroundResource(R.drawable.default_profile_pic)
                    }
                    if (title != null && !title.isEmpty()){
                        binding?.bgLay?.mainCard?.visibility = View.VISIBLE
                        binding?.bgLay?.titleArtist?.text = title
                    }
                    if (src.isNullOrEmpty() && title.isNullOrEmpty()){
                        binding?.bgLay?.mainCard?.visibility = View.GONE

                    }
                    if (dataList.id != null){
                        KsPreferenceKeys.getInstance().sponsorArtistId = dataList.id.toString()
                    }
                    if (dataList.assetType != null && dataList.assetType.isNotEmpty()){
                        assetTypeOne = dataList.assetType
                       KsPreferenceKeys.getInstance().assetType = assetTypeOne
                    }
                    if (dataList.contentSlug != null && dataList.contentSlug.isNotEmpty()){
                        contentSlug = dataList.contentSlug
                        KsPreferenceKeys.getInstance().contentSlugSponser = contentSlug
                    }


                }


            }

            toolbar()
            binding?.toolbar?.skip?.visibility = View.VISIBLE
            binding?.toolbar?.llSearchIcon?.setOnClickListener {
                binding?.toolbar?.skip?.visibility = View.GONE
                binding?.toolbar?.mainLay?.visibility = View.GONE
                binding?.searchToolbar?.toolbarSearch?.visibility = View.VISIBLE
                binding?.searchToolbar?.searchView?.setOnQueryTextListener(this)
                binding?.searchToolbar?.searchView?.requestFocus()
                binding?.searchToolbar?.searchView?.setOnQueryTextListener(this)
                binding?.searchToolbar?.searchView?.setIconifiedByDefault(false) // Make sure the SearchView is not iconified (collapsed)
//            // Set the hint for the SearchView
                binding?.searchToolbar?.searchView?.queryHint = getString(R.string.search_hint_sp)

            }
            binding?.searchToolbar?.cancel?.setOnClickListener {
                binding?.toolbar?.skip?.visibility = View.VISIBLE
                binding?.searchToolbar?.searchView?.setQuery("", false)
                binding?.searchToolbar?.toolbarSearch?.visibility = View.GONE
                binding?.toolbar?.mainLay?.visibility = View.VISIBLE

            }
            binding?.toolbar?.skip?.setOnClickListener {
                AppState.isSkipped = true
                finish()
            }


            binding?.rvCircleImages?.layoutManager = GridLayoutManager(this, 3)
            setViewModel()
            setClick()
            getProfile()
            getSponserGenre()
            setPaginationListener()
        }else{
           noConnectionLayout()
        }
        binding?.bgLay?.mainCard?.setOnClickListener {
            if (!KsPreferenceKeys.getInstance().contentSlugSponser.isNullOrEmpty() && !KsPreferenceKeys.getInstance().assetType.isNullOrEmpty() && !KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()){
                ActivityLauncher.getInstance().homeActivityFromSponser(this@ArtistAndSponserActivity,HomeActivity::class.java,KsPreferenceKeys.getInstance().assetType,KsPreferenceKeys.getInstance().contentSlugSponser, KsPreferenceKeys.getInstance().sponsorArtistId.toInt(),true)
            }

        }
    }


    private fun getSponserGenre(){
        binding?.progressBar?.visibility = View.VISIBLE
        if (featureList?.featureFlag?.IS_GENRES_FROM_API == true) {
            railInjectionHelper?.getSearchGenres("GENRES", "CUSTOM")?.observe(this) {
                binding?.progressBar?.visibility = View.GONE
                if (it != null && it.data?.items?.isNotEmpty() == true) {
                    binding?.tvNoResultFound?.visibility = View.GONE
                    binding?.rvCircleImages?.visibility = View.VISIBLE
                    val gson = Gson()
                    // Convert to JSON string
                    val jsonString = gson.toJson(it)
                    // Log the JSON string
                    Log.e("responseGenre", jsonString)//
                    adatperContentPreference = ContentPreferenceAdapter(
                        this@ArtistAndSponserActivity,
                        it.data.items,
                        this@ArtistAndSponserActivity
                    )
                    binding?.recyclerViewSponser?.setAdapter(adatperContentPreference)
                } else {
                    binding?.tvNoResultFound?.visibility = View.VISIBLE
                    binding?.btnSave?.visibility = View.GONE
                }
            }
        }else{
            val filterGenres=AppConfigMethod.getFilterGenres()
            adatperContentPreference = ContentPreferenceAdapter(
                this@ArtistAndSponserActivity,
                filterGenres,
                false,
                this@ArtistAndSponserActivity
            )
            binding?.recyclerViewSponser?.adapter = adatperContentPreference
        }
    }


  private fun callSponsorUserTracking() {
      val authToken = KsPreferenceKeys.getInstance().appPrefAccessToken
      if (featureList?.featureFlag?.IS_SPONSOR_USER_TRACKING_ENABLED == true) {
          // Create the JSON structure for the request
          val jsonObject = JsonObject()
          // Device Info
          val deviceInfoObject = JsonObject()
          val innerDeviceInfoObject = JsonObject()
          innerDeviceInfoObject.addProperty("deviceId", AppCommonMethod.getDeviceName(this))
          deviceInfoObject.add("deviceInfo", innerDeviceInfoObject)
          jsonObject.add("deviceInfo", deviceInfoObject)
          // Event Info
          val eventObject = JsonObject()
          eventObject.addProperty("name", "ArtistUpdated")
          eventObject.addProperty("identifier", "ArtistUpdated")

          // Event Attributes
          val attributeObject = JsonObject()
          attributeObject.addProperty("sponsoredArtistId", selectedSponserId)
          attributeObject.addProperty("selectionDateTime",System.currentTimeMillis().toString())
          attributeObject.addProperty("userId", userId)
          eventObject.add("attribute", attributeObject)

          jsonObject.add("event", eventObject)
          railInjectionHelper?.getSponsorUserTracking(jsonObject, authToken)?.observe(this) {
              binding?.progressBar?.visibility = View.GONE
              if (it?.data != null) {
                  Log.d("callSponsorUserTracking", "success: ")
              } else {
                  Log.d("callSponsorUserTracking", "failure: ")
              }
          }
      }
  }



    private fun getProfile() {
        val authToken = KsPreferenceKeys.getInstance().appPrefAccessToken
        viewModel?.hitUserProfile(this@ArtistAndSponserActivity,authToken)?.observe(this) {
            if (it != null) {
                if (it.status) {
                    Logger.e("profileRes", it.toString())
                    if (it.data.deletionRequestStatus != null) {
                        val deletionRequestStatus: String = it.data.deletionRequestStatus
                        if (deletionRequestStatus.equals("UNDER_REVIEW")) {
                            KsPreferenceKeys.getInstance().deleteAccountRequestStatus = true
                        } else {
                            KsPreferenceKeys.getInstance().deleteAccountRequestStatus = false
                        }
                    }

                    selectedArtistId= it.data.customData.sponsoredArtist?:""
                    if (it.data.appUserInterest != null && it.data.appUserInterest.isNotEmpty()) {
                        genreList = it.data.appUserInterest
                    }
                    KsPreferenceKeys.getInstance().sponsorArtistId = selectedArtistId

                    getProfileData(it)
                    dismissLoading(binding?.progressBar)
                    getAllArtist(null,selectedArtistId )
                }
                if (it.responseCode == 4302) {
                 //   isloggedout = true
                    logoutCall()
                    ActivityLauncher.getInstance().goToLogin(this@ArtistAndSponserActivity, ActivityLogin::class.java)
                    try {
                        runOnUiThread { onBackPressed() }
                    } catch (_: java.lang.Exception) {
                    }
                } else if (it.responseCode == 4019) {
                    commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ), it.debugMessage.toString(), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                } else if (it.responseCode == 4901) {
                    commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ), it.debugMessage.toString(), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                }
            }else{
                commonDialog(stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                    getString(R.string.popup_error)
                ),stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                    getString(R.string.something_went_wrong)
                ) , stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))

            }

        }
    }

    private fun getProfileData(it: UserProfileResponse) {
        try {
            name = it.data.name as String
            userId = it.data.id.toString()
            lastName = it.data?.customData?.lastName!!
            mobileNumber = it.data?.customData?.mobileNumber.toString()
            if (ObjectHelper.isNotEmpty(it.data.dateOfBirth)) {
                dob = it.data.dateOfBirth as Double
            }
            if (it.data.customData.country !=null) {
                country = it.data.customData.country
            }
            if (it.data.customData != null) {
                if (it.data.customData.city != null)
                    city =  it.data.customData.city
            }
            if (it.data.gender != null) {
                if (it.data.gender != null)
                    gender = it.data.gender.toString()
            }

            KsPreferenceKeys.getInstance().appPrefUserName = (it.data.name as String)
            KsPreferenceKeys.getInstance().appPrefUserLastName = (it.data?.customData?.lastName)
            KsPreferenceKeys.getInstance().appPrefMobileNumber = (it.data.customData.mobileNumber.toString())

        } catch (e: java.lang.Exception) {
            Logger.w(e)
        }
    }


    private fun setClick() {
        binding?.btnSave?.setOnClickListener {
            binding?.progressBar?.visibility=View.VISIBLE
            updateProfile()
        }
    }

    private fun updateProfile() {
     val token = KsPreferenceKeys.getInstance().appPrefAccessToken
        val countryCode = KsPreferenceKeys.getInstance().countryCode

        viewModel?.updateSponsor(token,genreList, selectedSponserId,name,lastName,countryCode,mobileNumber,dob.toString(),country,city,gender)?.observe(this) {
            if (it != null) {
                if (it.status) {
                    val gson = Gson()
                    val userProfileData = gson.toJson(it)
                    KsPreferenceKeys.getInstance().userProfileData = userProfileData
                    KsPreferenceKeys.getInstance().sponsorArtistId = selectedArtist?.id.toString()
                    if (assetTypeOne != null){
                        KsPreferenceKeys.getInstance().assetType = assetTypeOne
                    }
                    if (contentSlug != null){
                       KsPreferenceKeys.getInstance().contentSlugSponser = contentSlug
                    }
                    if (posterUrl != null){
                        binding?.bgLay?.mainCard?.visibility = View.VISIBLE
                        ImageHelper.getInstance(this).loadCircleImageTo(binding?.bgLay?.artistImg,posterUrl)
                    }else{
                        binding?.bgLay?.artistImg?.setBackgroundResource(R.drawable.default_profile_pic)
                    }
                    if (artistName != null){
                        binding?.bgLay?.mainCard?.visibility = View.VISIBLE
                        binding?.bgLay?.titleArtist?.text = artistName
                    }else{
                        binding?.bgLay?.titleArtist?.visibility = View.GONE
                    }
                    Logger.e("userdata",userProfileData)
                    callSponsorUserTracking()
                    val dialogTitle="${getString(R.string.sponser_dailog_title)} $artistName"
                    val dialogDesc="${getString(R.string.sponser_dailog_desc_start)} $artistName ${getString(R.string.sponser_dailog_desc_end)}"
                    val dialogActionBtn= getString(R.string.sponser_dailog_action_txt)
                    commonDialog(dialogTitle, dialogDesc, dialogActionBtn)
                }else{
                    if(it.responseCode==4302){
                        dismissLoading(binding?.progressBar)
                        logoutCall()
                        ActivityLauncher.getInstance().goToLogin(this@ArtistAndSponserActivity, ActivityLogin::class.java)
                        try {
                            runOnUiThread { onBackPressed() }
                        } catch (e: java.lang.Exception) {
                            Logger.d(e.toString())
                        }
                    }else if(it.responseCode==4019){
                        commonDialog(it.debugMessage.toString(),"",stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                    }else{
                        if (it.debugMessage!=null){
                            commonDialog(it.debugMessage.toString(),"",stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                        }else{
                            commonDialog(stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.something_went_wrong)
                            ),stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))

                        }
                    }

                }
            }else{
                commonDialog(stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                    getString(R.string.popup_error)
                ), stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                    getString(R.string.something_went_wrong)
                ),stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))

            }
            binding?.progressBar?.visibility=View.GONE

        }
    }

    private fun logoutCall() {
        if (CheckInternetConnection.isOnline(Objects.requireNonNull(this))) {
            val preference = KsPreferenceKeys.getInstance()
            clearCredientials(preference)
            hitApiLogout(this, KsPreferenceKeys.getInstance().appPrefAccessToken)
        }else{
            commonDialog(stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                getString(R.string.popup_no_internet_connection_found)
            ), "", stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))

        }
    }
    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }
    private fun setPaginationListener() {
        binding!!.rvCircleImages.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                try {
                        val layoutManager = recyclerView.layoutManager as GridLayoutManager?
                        firstVisiblePosition = layoutManager?.findFirstVisibleItemPosition()!!
                        if (dy > 0) {
                            visibleItemCount = layoutManager.childCount
                            totalItemCount = layoutManager.itemCount
                            pastVisiblesItems = layoutManager.findFirstVisibleItemPosition()
                            if (mIsLoading) {
                                if (visibleItemCount + pastVisiblesItems >= totalItemCount) {
                                    // Logger.d("slidingValues"+getBinding().listRecyclerview.getAdapter().getItemCount()+" "+counter);
                                    val adapterSize = binding!!.rvCircleImages.adapter!!.itemCount
                                    if (adapterSize > 8) {
                                        mIsLoading = false
                                        counter++
                                        Logger.e("counterSize",counter.toString())
                                        isScrolling = true
                                        mScrollY += dy
                                        getAllArtist(null,selectedArtistId)
                                    }
                                }
                            }
                        }

                } catch (e: Exception) {
                    Logger.e("ListingActivity", "" + e)
                }
            }
        })
    }



    private fun getAllArtist(keyword: String?,selectedId:String) {
        binding?.progressBar?.visibility = View.VISIBLE
        railInjectionHelper?.getAllArtist(keyword,21,counter)?.observe(this){
            binding?.progressBar?.visibility = View.GONE
            if (it != null && it.isNotEmpty()) {
                binding?.tvNoResultFound?.visibility = View.GONE
               binding?.rvCircleImages?.visibility = View.VISIBLE
               binding?.rvCircleImages?.addItemDecoration(SpacingItemDecoration(4, SpacingItemDecoration.HORIZONTAL));                if (keyword == null) {
                    if (musicRecyclerViewAdapter==null) {
                        musicRecyclerViewAdapter =
                            SponserRecyclerViewAdapter(this,selectedId, it.get(0).enveuVideoItemBeans, saveCheckedArtistClickListener)
                        binding?.rvCircleImages?.adapter = musicRecyclerViewAdapter
                    }else{
                        musicRecyclerViewAdapter?.notifyData(it.get(0).enveuVideoItemBeans)
                        mIsLoading = it.get(0).pageTotal != musicRecyclerViewAdapter!!.itemCount
                    }
                }
                else{
                    musicRecyclerViewAdapter?.updateData(it.get(0).enveuVideoItemBeans)
                    if (it.get(0).enveuVideoItemBeans.size==0){
                        binding?.tvNoResultFound?.visibility = View.VISIBLE
                        binding?.rvCircleImages?.visibility = View.GONE
                    }
                }
            }
            else{
                if (musicRecyclerViewAdapter?.list?.isEmpty() == true){
                    binding?.tvNoResultFound?.visibility = View.VISIBLE
                    binding?.rvCircleImages?.visibility = View.GONE
                }else{
                    musicRecyclerViewAdapter?.updateData(it.get(0).enveuVideoItemBeans)
                }
                binding?.tvNoResultFound?.visibility = View.VISIBLE
                binding?.btnSave?.visibility = View.GONE
            }
        }
    }


    override fun onQueryTextSubmit(query: String): Boolean {
        if (NetworkConnectivity.isOnline(this)) {
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                return true
            }
            mLastClickTime = SystemClock.elapsedRealtime()
           binding?.searchToolbar?.searchView?.let { hideSoftKeyboard(it) }
            if (query.trim { it <= ' ' }.length > 2) {
                applyFilter = java.lang.Boolean.parseBoolean(KsPreferenceKeys.getInstance().filterApply)
                searchText = query.trim { it <= ' ' }
                Log.d("SEARCH_TEXT", searchText.toString())
             //   getAllArtist(searchText,selectedArtistId)
                callGenreFilerAPI(commaSeparatedString,searchText.toString())
            }

        }
        return false
    }

    override fun onQueryTextChange(newText: String?): Boolean {
        return false
    }

    private fun noConnectionLayout() {
         //   binding?.searchToolbar?.toolbarSearch?.visibility = View.GONE
            binding?.connection?.noConnectionLayout?.visibility = View.VISIBLE
            binding?.connection?.retryTxt?.setOnClickListener { connectionObserver() }
        }
    
    private fun toolbar() {
        binding?.toolbar?.apply {
            logoMain2.visibility = View.GONE
            searchIcon.visibility = View.VISIBLE
            backLayout.visibility = View.VISIBLE
            titleMid.visibility = View.VISIBLE
            titleMid.text = getText(R.string.sponsor_artist)
            titleMid.setBackgroundResource(0)
            backLayout.setOnClickListener { onBackPressed() }
        }
    }



    override fun onActionBtnClicked() {
        onBackPressed()
    }

    override fun onCancelBtnClicked() {
    }

    override fun onClick(arrayList: List<Int?>, id: Int) {
        Logger.e("arrayListOnClick",arrayList.toString())
        commaSeparatedString = convertListToCommaSeparatedString(arrayList)
        Log.d("FilterGener", "FilterGener ids: " + commaSeparatedString)
         callGenreFilerAPI(commaSeparatedString, "")
    }


    fun convertListToCommaSeparatedString(arrayList: List<Int?>): String {
        return arrayList
            .mapNotNull { it?.toString() }  // Convert non-null integers to strings
            .joinToString(",")             // Join the strings with a comma
    }

    private fun callGenreFilerAPI(commaSeparatedString: String, keyword: String?) {
        binding?.progressBar?.visibility = View.VISIBLE
        railInjectionHelper?.hitGenreDataFilter(this@ArtistAndSponserActivity,"PERSON", "GENRE",commaSeparatedString, keyword, 0, 32, "true","ARTISTS", "en-US")?.observe(this){
            binding?.progressBar?.visibility = View.GONE
            if (it != null && it.isNotEmpty()) {
                binding?.tvNoResultFound?.visibility = View.GONE
                binding?.rvCircleImages?.visibility = View.VISIBLE
                val gson = Gson()
                // Convert to JSON string
                val jsonString = gson.toJson(it)
                // Log the JSON string
                Log.e("responseGenre", jsonString)//
                if (keyword ==null){
                    if (musicRecyclerViewAdapter==null) {
                        musicRecyclerViewAdapter =
                            SponserRecyclerViewAdapter(this,"", it[0].enveuVideoItemBeans, saveCheckedArtistClickListener)
                        binding?.rvCircleImages?.adapter = musicRecyclerViewAdapter
                    }else{
                        musicRecyclerViewAdapter?.notifyData(it[0].enveuVideoItemBeans)
                        mIsLoading = it[0].pageTotal != musicRecyclerViewAdapter!!.itemCount
                    }
                }else{
                    musicRecyclerViewAdapter?.updateData(it.get(0).enveuVideoItemBeans)
                    if (it.get(0).enveuVideoItemBeans.size== 0){
                        binding?.tvNoResultFound?.visibility = View.VISIBLE
                        binding?.rvCircleImages?.visibility = View.GONE
                    }
                }
            }else{
                if (musicRecyclerViewAdapter?.list?.isEmpty() == true){
                    binding?.tvNoResultFound?.visibility = View.VISIBLE
                    binding?.rvCircleImages?.visibility = View.GONE
                }else{
                    musicRecyclerViewAdapter?.updateData(it.get(0).enveuVideoItemBeans)
                }
                binding?.tvNoResultFound?.visibility = View.VISIBLE
                binding?.btnSave?.visibility = View.GONE
            }


        }

    }
    object AppState {
        var isSkipped: Boolean = false
    }
}
