package com.enveu.activities.sponsorArtist;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

 class HorizontalSpaceItemDecoration extends RecyclerView.ItemDecoration {
    private final int horizontalItemSpace;

    public HorizontalSpaceItemDecoration(int horiontalItemSpace) {
        this.horizontalItemSpace = horiontalItemSpace;
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent,
                               @NonNull RecyclerView.State state) {
        outRect.left = horizontalItemSpace;
    }
}
