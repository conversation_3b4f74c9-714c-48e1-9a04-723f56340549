package com.enveu.activities.usermanagment


class EnumClasses {

    enum class CurrencyCode(val code: String) {
        USD("USD"),
        INR("INR"),
        MXN("MXN"),
        ARS("ARS"),
        CLP("CLP"),
        COP("COP"),
        CRC("CRC"),
        BRL("R$"),
        BOB("BOB"),
        GTQ("GTQ"),
        DOP("DOP"),
        PYG("PYG"),
        PEN("PEN"),
        UYU("UYU"),
        EURO("Euro"),
        AUD("AUD"),
        GBP("GBP");

        companion object {
            fun fromCode(code: String): CurrencyCode? {
                return values().find { it.code == code }
            }
        }

//        val displayTextCurrencyCode: String
//            get() = when (this) {
//                USD -> "$"
//                INR -> "₹"
//                MXN -> "Mex$"
//                ARS, CLP, COP -> "$"
//                CRC -> "₡"
//                BRL -> "R$"
//                BOB -> "Bs."
//                GTQ -> "Q"
//                DOP -> "RD$"
//                PYG -> "₲"
//                PEN -> "S/."
//              //  UYU -> "$U"
//                EURO -> "€"
//                AUD -> "A$"
//                GBP -> "£"
//            }
    }

    enum class SubscriptionPipe(val code: String) {
        ONE_TIME("ONE_TIME"),
        RECURRING_SUBSCRIPTION("RECURRING_SUBSCRIPTION"),
        PERPETUAL("PERPETUAL"),
        RENTAL("RENTAL");

        val displayText: String
            get() = when (this) {
                ONE_TIME -> "Subscription"
                RECURRING_SUBSCRIPTION -> "Subscription"
                PERPETUAL -> "Perpetual"
                RENTAL -> "Rental"
            }

        companion object {
            @JvmStatic
            fun fromCode(subscriptionType: String): SubscriptionPipe? {
                return SubscriptionPipe.values().find { it.code == subscriptionType }
            }
        }
    }
}
