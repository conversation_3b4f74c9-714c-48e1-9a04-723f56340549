package com.enveu.activities.usermanagment.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.enveu.R;
import com.enveu.activities.purchase.purchase_model.PurchaseModel;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class AdapterPlan extends RecyclerView.Adapter<AdapterPlan.ViewHolder>{
    List<PurchaseModel> items;
    OnPurchaseItemClick mListener;
    Context context;
    boolean isDescriptionVisible = false;

    public AdapterPlan(List<PurchaseModel> items1, OnPurchaseItemClick mListener, Context context) {
        this.items = items1;
        Collections.sort(items, new Comparator<PurchaseModel>(){
            public int compare(PurchaseModel o1, PurchaseModel o2){
                return Long.compare(o1.getSubscriptionOrder(), o2.getSubscriptionOrder());
            }
        });

        if (items != null && items.size() > 0) {
            items.get(0).setSelected(true);
        }
        this.mListener = mListener;
        this.context=context;
    }
    @NonNull
    @Override
    public AdapterPlan.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View inflate = LayoutInflater.from(parent.getContext()).inflate(R.layout.plan_subscription, null);
        return new AdapterPlan.ViewHolder(inflate);
    }

    public interface OnPurchaseItemClick {
        void onPurchaseCardClick(boolean click, PurchaseModel planName);
        void setDescription(String description,PurchaseModel planName);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, @SuppressLint("RecyclerView") int position) {
        if (KsPreferenceKeys.getInstance().getAppLanguage().equals("spanish")) {
            holder.text_price.setText(items.get(position).getPrice() + "  " + items.get(position).getOfferPeriod());
            if (items.get(position).getDescription_es() !=null || items.get(position).getDescription() !=null ) {
                holder.text_description.setVisibility(View.VISIBLE);
                holder.text_description.setText(items.get(position).getDescription_es() != null ? items.get(position).getDescription_es() : items.get(position).getDescription());
            } else {
                holder.text_description.setVisibility(View.GONE);
            }
            holder.text_subscription_type.setText(items.get(position).getTitle_es() != null ? items.get(position).getTitle_es() : items.get(position).getTitle());

        } else if (KsPreferenceKeys.getInstance().getAppLanguage().equals("English")) {
            holder.text_price.setText(items.get(position).getPrice() + "  " + items.get(position).getOfferPeriod());
            if (items.get(position).getDescription_en() !=null || items.get(position).getDescription() !=null ) {
                isDescriptionVisible = true;
                holder.text_description.setVisibility(View.VISIBLE);
                holder.text_description.setText(items.get(position).getDescription_en() != null ? items.get(position).getDescription_en() : items.get(position).getDescription());
            } else {
                holder.text_description.setVisibility(View.GONE);
            }
            holder.text_subscription_type.setText(items.get(position).getTitle_en() != null ? items.get(position).getTitle_en() : items.get(position).getTitle());

        }

        Drawable drawable;
        if (items.get(position).isSelected()) {
            if (KsPreferenceKeys.getInstance().getAppLanguage().equals("spanish")) {
                mListener.setDescription(items.get(position).getDescription_es(),items.get(position));
            } else if (KsPreferenceKeys.getInstance().getAppLanguage().equals("English")) {
                mListener.setDescription(items.get(position).getDescription_en(),items.get(position));
            }
            drawable=selected(context,holder);
        } else {
            drawable=unselected(context,holder);
        }
        ViewCompat.setBackground(holder.mainLay, drawable);

        holder.mainLay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                resetList(items);
                items.get(position).setSelected(true);
                mListener.onPurchaseCardClick(true, items.get(position));
            }
        });
    }

    private Drawable unselected(Context context, ViewHolder holder) {
        holder.text_description.setVisibility(View.GONE);
        return ResourcesCompat.getDrawable(context.getResources(), R.drawable.border_plan, null);
    }

    private Drawable selected(Context context, ViewHolder holder) {
        if (isDescriptionVisible) {
            holder.text_description.setVisibility(View.VISIBLE);
        }
        return ResourcesCompat.getDrawable(context.getResources(), R.drawable.border_plan, null);
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        public TextView text_subscription_type,text_published,text_price,text_description;
        LinearLayout mainLay;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            text_subscription_type = (TextView) itemView.findViewById(R.id.text_subscription_type);
            text_published = (TextView) itemView.findViewById(R.id.text_published);
            text_price = (TextView) itemView.findViewById(R.id.text_price);
            text_description = (TextView) itemView.findViewById(R.id.text_description);

            mainLay=(LinearLayout)itemView.findViewById(R.id.llmainLay);

        }
    }

    private void resetList(List<PurchaseModel> list) {
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSelected(false);
        }
    }
}
