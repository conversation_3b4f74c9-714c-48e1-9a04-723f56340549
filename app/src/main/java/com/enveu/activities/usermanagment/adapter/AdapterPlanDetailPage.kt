package com.enveu.activities.usermanagment.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.purchase.purchase_model.PurchaseModel
import com.enveu.player.utils.Logger
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys

class AdapterPlanDetailPage(
    var items: List<PurchaseModel>?,
    mListener: OnPurchaseItemClick,
    context: Context
) :
    RecyclerView.Adapter<AdapterPlanDetailPage.ViewHolder>() {
    var mListener: OnPurchaseItemClick
    var context: Context
    var isDescriptionVisible: Boolean = false

    init {
        if (items != null && items!!.size > 0) {
            items!![0].isSelected = true
        }
        this.mListener = mListener
        this.context = context
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.plan_subscription_detail, parent, false)
        return ViewHolder(view)
    }

    interface OnPurchaseItemClick {
        fun onPurchaseCardClick(click: Boolean, planName: PurchaseModel?)
        fun setDescription(description: String?, planName: PurchaseModel?)
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, @SuppressLint("RecyclerView") position: Int) {
            holder.text_price.text = items?.get(position)?.price + "  " + items?.get(position)?.currency
            if (items?.get(position)?.description_en != null || items?.get(position)?.description != null) {
                isDescriptionVisible = true
                holder.text_description.visibility = View.VISIBLE
                holder.text_description.text =
                    if (items?.get(position)?.description_en != null) items?.get(position)?.description_en else items?.get(position)?.description
            } else {
                holder.text_description.visibility = View.GONE
            }
            holder.text_subscription_type.text = if (items?.get(position)?.title_en != null) items?.get(position)?.title_en else items?.get(position)?.title

            holder.offerDurationText.text = "${items?.get(position)?.rentalPeriod?.periodLength} ${items?.get(position)?.rentalPeriod?.periodType}"

        val drawable: Drawable?
        if (items!![position].isSelected) {
            mListener.setDescription(items?.get(position)?.description_en, items?.get(position))
            drawable = selected(context, holder)
        } else {
            drawable = unselected(context, holder)
        }
        ViewCompat.setBackground(holder.mainLay, drawable)

        holder.mainLay.setOnClickListener {
            resetList(items!!)
            items!![position].isSelected = true
            mListener.onPurchaseCardClick(true, items!![position])
        }
    }

    private fun unselected(context: Context, holder: ViewHolder): Drawable? {
        holder.text_description.visibility = View.VISIBLE
        return ResourcesCompat.getDrawable(context.resources, R.drawable.rounded_all_bg, null)
    }

    private fun selected(context: Context, holder: ViewHolder): Drawable? {
        if (isDescriptionVisible) {
            holder.text_description.visibility = View.VISIBLE
        }
        return ResourcesCompat.getDrawable(context.resources, R.drawable.border_plan, null)
    }

    override fun getItemCount(): Int {
        return items!!.size
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var text_subscription_type: TextView = itemView.findViewById<View>(R.id.text_subscription_type) as TextView
        var text_published: TextView = itemView.findViewById<View>(R.id.text_published) as TextView
        var text_price: TextView = itemView.findViewById<View>(R.id.text_price) as TextView
        var text_description: TextView = itemView.findViewById<View>(R.id.rental_description) as TextView
        var mainLay: ConstraintLayout = itemView.findViewById<View>(R.id.llmainLay) as ConstraintLayout
        var offerDurationText: TextView = itemView.findViewById<View>(R.id.offerDurationText) as TextView
    }

    private fun resetList(list: List<PurchaseModel>) {
        for (i in list.indices) {
            list[i].isSelected = false
        }
    }
}
