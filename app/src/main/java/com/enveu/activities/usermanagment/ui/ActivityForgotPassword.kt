package com.enveu.activities.usermanagment.ui


import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.responseModels.LoginResponse.Data
import com.enveu.databinding.ActivityForgotPasswordBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.AppColors
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.colorsJson.converter.ColorsHelper.strokeBgDrawable
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.StringUtils
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import java.util.Objects

class ActivityForgotPassword : BaseBindingActivity<ActivityForgotPasswordBinding?>(),
    CommonDialogFragment.EditDialogListener {
    private val regex: Regex = Regex("[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,100}")
    private var viewModel: RegistrationLoginViewModel? = null
    private var errorDialog = false
    private lateinit var email:String
    private var preference: KsPreferenceKeys? = null
    private var name: String? = null
    private var fbId: String? = null
    private var accessToken: String? = null
    private var forceLogin: Boolean? = null
    private var isRequestSent: Boolean? = null
    private var modelLogin: Data? = null
    private val colorsHelper by lazy { ColorsHelper }
    private val stringsHelper by lazy { StringsHelper }

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityForgotPasswordBinding {
        return ActivityForgotPasswordBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parseColor()
        viewModel = ViewModelProvider(this@ActivityForgotPassword)[RegistrationLoginViewModel::class.java]
        callBinding()
        setClicks()
    }

    private fun parseColor() {
        binding?.colorsData = colorsHelper
        binding?.stringData = stringsHelper
        binding?.toolbar?.colorsData = colorsHelper
        binding?.toolbar?.stringData = stringsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
//        binding?.llnumber?.background = strokeBgDrawable(AppColors.popupBgColor(), AppColors.editTextBorderColor(), 30f)
//        binding?.etPasswordRecoveryEmail?.background = strokeBgDrawable(AppColors.tphBgColor(), AppColors.editTextBorderColor(), 30f)
    }

    private fun callBinding() {
        if (CheckInternetConnection.isOnline(this@ActivityForgotPassword)) {
            AnalyticsUtils.trackScreenView(this, AppConstants.FORGOT_PASSWORD)
            val extra = intent.extras
            if (extra != null) {
                getBundleValue()
            }

        } else {
            commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                        getString(R.string.popup_no_internet_connection_found)
                    ), "", stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
        }
    }

    private fun getBundleValue() {
        if (intent.hasExtra(AppConstants.EXTRA_REGISTER_USER)) {
            var bundle: Bundle? = intent.extras
            if (bundle != null) {
                bundle = bundle.getBundle(AppConstants.EXTRA_REGISTER_USER)
                name = bundle?.getString("fbName")
                fbId = bundle?.getString("fbId")
                accessToken = bundle?.getString("fbToken")
                forceLogin = bundle?.getBoolean("forceLogin")

            }

        }

    }


    private fun setClicks() {
        binding?.toolbar?.logoMain2?.visibility =View.VISIBLE
        binding?.toolbar?.searchIcon?.visibility =View.GONE
        binding?.toolbar?.titleSkip?.visibility = View.GONE
        binding?.toolbar?.backLayout?.setOnClickListener { onBackPressed() }
//        setTextWatcher()
        binding?.continueBtnOne?.setOnClickListener{
            hideSoftKeyboard(binding?.continueBtnOne)
            if (CheckInternetConnection.isOnline(this@ActivityForgotPassword)) {
                if (validateEmptyEmail()  && validateEmail()){
                    if(forceLogin == true)
                        callForceFbApi()
                    else
                        callForgotApi()
                }
            }else{
                commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                        getString(R.string.popup_no_internet_connection_found)
                    ), "", stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)))
            }

        }
    }


    private fun callForceFbApi() {
        if (CheckInternetConnection.isOnline(this@ActivityForgotPassword)) {
            showLoading(binding?.progressBar, true)
            forceLogin?.let {
                viewModel?.hitApiForceFbLogin(this@ActivityForgotPassword, binding?.etPasswordRecoveryEmail?.text.toString().trim(),accessToken, name, fbId, "", it
                )?.observe(this@ActivityForgotPassword) { loginResponseModelResponse ->
                    if (Objects.requireNonNull(loginResponseModelResponse).responseCode == 2000) {
                        val gson = Gson()
                        modelLogin = loginResponseModelResponse.data
                        val stringJson = gson.toJson(loginResponseModelResponse.data)
                        Log.d("stringJson", stringJson)
                        saveUserDetails(stringJson,  false)
                        Log.d("stringJson", loginResponseModelResponse.data.isVerified.toString())
                        if (loginResponseModelResponse.data.isVerified){
                            ActivityLauncher.getInstance()
                                .homeScreen(this@ActivityForgotPassword, HomeActivity::class.java,false,"","",0)
                        }else{
                            ActivityLauncher.getInstance().goToEnterOTP(this@ActivityForgotPassword,
                                EnterOTPActivity::class.java,"ForgotPassword")
                        }

                    } else if (loginResponseModelResponse.responseCode == 403) {
                        dismissLoading(binding?.progressBar)
                        commonDialog(stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),loginResponseModelResponse.debugMessage.toString(),
                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_ok.toString(), getString(R.string.popup_ok)))
                    } else {
                        dismissLoading(binding?.progressBar)
                        commonDialog(stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),loginResponseModelResponse.debugMessage.toString(),
                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_ok.toString(), getString(R.string.popup_ok)))
                    }
                }
            }
        } else {
            commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                        getString(R.string.popup_no_internet_connection_found)
                    ), "", stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
        }
    }


    private fun callForgotApi() {
        showLoading(binding?.progressBar,true)
        viewModel?.hitForgotPasswordApi(email)?.observe(this, Observer {
            if (it!=null){
                dismissLoading(binding?.progressBar)
                if (it.code==200){
                    errorDialog = false
                    isRequestSent = true;
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_request_sent.toString(),
                            getString(R.string.popup_request_sent))
                        ,applicationContext.resources.getString(R.string.forgot_password_response),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_check_your_email.toString(),
                            getString(R.string.popup_check_your_email)))
                    clearEditView()
                }else{
                    if (it.debugMessage != null && !it.debugMessage.equals("", ignoreCase = true)) {
                        errorDialog = true
                        commonDialog(it.debugMessage,"",
                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString()
                                , getString(R.string.popup_continue)))
                    } else {
                        errorDialog = false
                    }
                }

            }else{
                commonDialog(stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.something_went_wrong)
                            ),
                    stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                        getString(R.string.popup_continue)))
            }
        })
    }

    private fun clearEditView() {
        binding?.etPasswordRecoveryEmail?.setText("")
    }

    private fun validateEmail(): Boolean {
        var check = false
        if (binding?.etPasswordRecoveryEmail?.text.toString().trim().matches(regex)) {
            check = true
        } else {
            commonDialog(stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_invalid_email_tittle.toString(), getString(R.string.popup_invalid_email_tittle)),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_invalid_email_subtitle.toString(),
                    getString(R.string.popup_invalid_email_subtitle)
                ), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue))
            )
        }
        return check
    }

    private fun validateEmptyEmail(): Boolean {
        var check = false
        email=binding?.etPasswordRecoveryEmail?.text.toString().trim()
        if (StringUtils.isNullOrEmptyOrZero(email)) {
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_empty_email_tittle.toString(),
                    getString(R.string.popup_empty_email_tittle)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_empty_email_subtitle.toString(),
                    getString(R.string.popup_empty_email_subtitle)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        } else {
            check = true
        }
        return check
    }


    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm: FragmentManager = supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }
    override fun onActionBtnClicked()
    {
        if (isRequestSent == true) {
            onBackPressed()

        }
    }

    override fun onCancelBtnClicked() {

    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            binding?.forgotLayout?.visibility = View.VISIBLE
            binding?.noConnectionLayout?.visibility = View.GONE
        } else {
            commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                        getString(R.string.popup_no_internet_connection_found)
                    ), "", stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                getString(R.string.popup_continue)))
        }
    }

    private fun noConnectionLayout() {
        binding?.forgotLayout?.visibility = View.GONE
        binding?.noConnectionLayout?.visibility = View.VISIBLE
        binding?.connection?.retryTxt?.setOnClickListener { view -> connectionObserver() }
    }
    private fun connectionObserver() {
        connectionValidation(NetworkConnectivity.isOnline(this@ActivityForgotPassword))
    }


    private fun saveUserDetails(stringJson: String?, b: Boolean) {
        try {
            val fbLoginData: Data = Gson().fromJson(stringJson, Data::class.java)
            val gson = Gson()
            val stringJson = gson.toJson(fbLoginData)
            if (b) {
                preference?.appPrefLoginType = AppConstants.UserLoginType.Manual.toString()
            } else {
                preference?.appPrefLoginType = AppConstants.UserLoginType.FbLogin.toString()
            }
            AppConstants.UserLoginType.FbLogin.toString()
            preference?.appPrefProfile = stringJson
            preference?.appPrefLoginStatus = AppConstants.UserStatus.Login.toString()
            preference?.appPrefUserId =fbLoginData.id.toString()
            preference?.appPrefUserName = fbLoginData.name.toString()
            preference?.appPrefUserEmail = fbLoginData.email.toString()
            preference?.expirytime = fbLoginData.expiryTime.toString()
            preference?.setIsVerified(fbLoginData.isVerified.toString())
        } catch (e: Exception) {
            Logger.w(e)
        }

    }

}