package com.enveu.activities.usermanagment.ui


import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Paint
import android.graphics.Typeface
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.Editable
import android.text.InputType
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.TextUtils
import android.text.TextWatcher
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.device_management.DeviceManagementViewModel
import com.enveu.activities.device_management.DeviceManagerActivity
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.purchase.plans_layer.GetPlansLayer
import com.enveu.activities.secondary_profile.SecondaryProfileActivity
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.responseModels.LoginResponse.Data
import com.enveu.callbacks.commonCallbacks.AppleSignInListener
import com.enveu.databinding.ActivityLoginBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.AppColors
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.colorsJson.converter.ColorsHelper.strokeBgDrawable
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.AppleSignInManager
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.SharedPrefHelper
import com.enveu.utils.helpers.StringUtils
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.facebook.CallbackManager
import com.facebook.CallbackManager.Factory.create
import com.facebook.FacebookAuthorizationException
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.FacebookSdk
import com.facebook.GraphRequest
import com.facebook.GraphResponse
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.google.gson.Gson
import org.json.JSONException
import org.json.JSONObject
import java.util.Objects


class ActivityLogin : BaseBindingActivity<ActivityLoginBinding?>(),
    CommonDialogFragment.EditDialogListener,
    AppleSignInListener {
    private val permissionNeeds = listOf("email", "public_profile")
    var font: Typeface? = null
    private var viewModel: RegistrationLoginViewModel? = null
    private var deviceManagementViewModel: DeviceManagementViewModel? = null
    private var email: String? = null
    private var modelLogin: Data? = null
    private var preference: KsPreferenceKeys? = null
    private lateinit var password: String
    private var accessTokenFB: String? = null
    private var selectedArtistId: String = ""
    private var callbackManager: CallbackManager? = null
    var hasFbEmail = false
    private var isFbLoginClick = false
    private var mLastClickTime: Long = 0
    private var featureList: FeatureFlagModel? = null
    private var loginFrom : String? = null
    private var name = ""
    private var id = ""
    private var jwtToken = ""
    private val regexPass: Regex = Regex("^.{8,16}$")
    private val regex: Regex = Regex("[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,100}")
    private val colorsHelper by lazy { ColorsHelper }
    private val stringsHelper by lazy { StringsHelper }
    lateinit var sbView:View
    lateinit var tvTitle:TextView
    lateinit var tvMessage:TextView
    lateinit var ivSB : ImageView

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityLoginBinding {
        return ActivityLoginBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        loginFrom = intent?.getStringExtra("loginFrom").toString() ?: ""
        SharedPrefHelper.getInstance().setColorJson(ColorsHelper.loadDataFromJson())
        SharedPrefHelper.getInstance().setStringJson(StringsHelper.loadDataFromJson())
        binding?.stringData = stringsHelper
        binding?.colorsData = colorsHelper
        binding?.toolbar?.colorsData = colorsHelper
        binding?.toolbar?.stringData = stringsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
        featureList = AppConfigMethod.parseFeatureFlagList()
        parseColor()
        binding?.signIn?.paintFlags = binding!!.signIn.paintFlags or Paint.UNDERLINE_TEXT_FLAG
        setClicks()
        connectionObserver()
        viewModel = ViewModelProvider(this@ActivityLogin)[RegistrationLoginViewModel::class.java]
        deviceManagementViewModel = ViewModelProvider(this@ActivityLogin)[DeviceManagementViewModel::class.java]
        preference = KsPreferenceKeys.getInstance()
        sbView=LayoutInflater.from(this).inflate(R.layout.snackbar_layout,binding!!.root as ViewGroup)

        if (featureList?.featureFlag?.ALLOW_ANONYMOUS_USER_ACCESS_CONTENT == true) {
            binding?.toolbar?.backLayout?.visibility = View.GONE
            binding?.toolbar?.titleSkip?.visibility = View.VISIBLE
            binding?.toolbar?.titleSkip?.setOnClickListener {
                if (loginFrom == "more") {
                    val i = Intent(this@ActivityLogin, HomeActivity::class.java)
                    i.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    startActivity(i)
                    finish()
                } else {
                    onBackPressed()
                }
            }
        }

        tvTitle=sbView.findViewById<TextView>(R.id.tv_sbl_title)
        tvMessage=sbView.findViewById<TextView>(R.id.tv_sbl_msg)
        ivSB=sbView.findViewById<ImageView>(R.id.iv_sbl)

        if (KsPreferenceKeys.getInstance()?.appLanguage?.equals(AppConstants.LANGUAGE_ARABIC, true) == true){
            binding?.password?.gravity =  Gravity.END or Gravity.CENTER_VERTICAL
            binding?.header?.layoutDirection = View.LAYOUT_DIRECTION_RTL
            binding?.headerLetsStartedText?.gravity =  View.LAYOUT_DIRECTION_RTL
        }
    }

    private fun setClicks() {
        if (featureList?.featureFlag?.IS_MUSIC_APP==true){
            binding?.toolbar?.backLayout?.visibility=View.GONE
        }else{
            binding?.toolbar?.backLayout?.visibility=View.VISIBLE
        }
        if (  !loginFrom?.isNullOrEmpty()!! && loginFrom == "home"){
            binding?.toolbar?.backLayout?.visibility=View.VISIBLE
        }
        callbackManager = create()
        FacebookSdk.fullyInitialize();
        binding?.fbButton?.setReadPermissions(permissionNeeds);
        binding?.confirmPasswordEye?.isChecked = false
        binding?.toolbar?.logoMain2?.visibility = View.VISIBLE
        binding?.toolbar?.searchIcon?.visibility = View.GONE
        binding?.toolbar?.backLayout?.setOnClickListener {
            onBackPressed()
        }
        binding?.signIn?.setOnClickListener {
            val i = Intent(this@ActivityLogin, ActivitySignUp::class.java)
            startActivity(i)

        }
        if (featureList!!.featureFlag.FORGOT_PASSWORD) {
            binding?.forgetPassword?.visibility = View.VISIBLE
            binding?.forgetPassword?.setOnClickListener {
                val i = Intent(this@ActivityLogin, ActivityForgotPassword::class.java)
                startActivity(i)
            }
        }
        binding?.signUp?.setOnClickListener {
            Log.d("Melody", "setClicks: ")
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
                return@setOnClickListener
            }
            mLastClickTime = SystemClock.elapsedRealtime()
            hideSoftKeyboard(binding?.signUp)
            if (CheckInternetConnection.isOnline(this@ActivityLogin)) {
                if (validateEmptyEmail() && validateEmail() && validateEmptyPassword()) {
                    callLoginApi()

                }
            } else {
//                commonDialog(
//                    stringsHelper.stringParse(
//                        stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
//                        getString(R.string.popup_no_internet_connection_found)
//                    ), "", stringsHelper.stringParse(
//                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                        getString(R.string.popup_continue)
//                    )
//                )
                showToast("",resources.getString(R.string.popup_no_internet_connection_found))
            }
        }

        binding?.appleImg?.setOnClickListener {
            showLoading(binding?.progressBar, true)
            viewModel?.authResponse?.observe(this, Observer {
                if (it != null) {
                    dismissLoading(binding?.progressBar)
                    Log.d("LoginResponse", Gson().toJson(it))
                    try {
                        if (it.responseCode == 2000 && it.data.authURL != null) {
                            AppleSignInManager.setContext(this)
                            AppleSignInManager.setUrl(it.data.authURL)
                            AppleSignInManager.setupAppleWebViewDialog()
                        } else if (it.debugMessage != null) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ), it.debugMessage.toString(), stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        } else {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ), stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.popup_something_went_wrong)
                                ), stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        }

                    } catch (e: Exception) {
                        Logger.w(e)
                    }
                } else {
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ), stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ), stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )

                }
            })
        }

        binding?.facebookImg?.setOnClickListener {
            hideSoftKeyboard(binding?.facebookImg)
            if (CheckInternetConnection.isOnline(this@ActivityLogin)) {
                if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
                    return@setOnClickListener
                }
                mLastClickTime = SystemClock.elapsedRealtime()
                clearEditView()
                isFbLoginClick = true
                binding?.fbButton?.performClick()

            } else {
                commonDialog(
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                        getString(R.string.popup_no_internet_connection_found)
                    ), "", stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                        getString(R.string.popup_continue)
                    )
                )

            }
        }

        binding?.fbButton?.registerCallback(
            callbackManager,
            object : FacebookCallback<LoginResult?> {
                override fun onCancel() {
                    println("onCancel")
                }

                override fun onError(error: FacebookException) {
                    if (error is FacebookAuthorizationException) LoginManager.getInstance().logOut()
                }

                override fun onSuccess(result: LoginResult?) {
                    println("onSuccess")
                    if (result != null) {
                        accessTokenFB = result.accessToken.token
                    }
                    val request = GraphRequest.newMeRequest(
                        result?.accessToken
                    ) { `object`: JSONObject?, response: GraphResponse? ->
                        Logger.i(
                            "LoginActivity", response.toString()
                        )
                        try {
                            id = `object`!!.getString("id")
                            name = `object`.getString("name")
                            if (`object`.has("email")) {
                                email = `object`.getString("email")
                                hasFbEmail = true
                            } else hasFbEmail = false
                            showHideProgress(getBinding()!!.progressBar)
                            hitApiFBLogin()
                        } catch (e: JSONException) {
                            e.printStackTrace()
                        }
                    }
                    val parameters = Bundle()
                    parameters.putString(
                        "fields",
                        "id,name,email"
                    )
                    request.parameters = parameters
                    request.executeAsync()
                }
            })
//        setTextWatcher()
//        setTextWatcherForEmail()
        binding?.confirmPasswordEye?.setOnCheckedChangeListener { compoundButton, b ->
            if (KsPreferenceKeys.getInstance()?.appLanguage?.equals(AppConstants.LANGUAGE_ARABIC, true) == true){
                binding?.password?.gravity =  Gravity.START or Gravity.CENTER_VERTICAL
            }
            if (!b) {
                binding?.password?.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
                binding?.password?.typeface = font
            } else {
                binding?.password?.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
                binding?.password?.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
                hideSoftKeyboard(binding?.confirmPasswordEye)
                binding?.password?.typeface = font
            }
            binding?.password?.text?.let { binding?.password?.setSelection(it.length) }
        }
    }

    private fun parseColor() {
        val textColorStates = ColorStateList(
            arrayOf(
                intArrayOf(-android.R.attr.state_checked),
                intArrayOf(android.R.attr.state_checked)
            ),
            intArrayOf(AppColors.pwdSelectedEyeColor(), AppColors.pwdUnselectedEyeColor())
        )
        binding?.confirmPasswordEye?.buttonDrawable?.setTintList(textColorStates)
//        binding?.name?.background =
//            strokeBgDrawable(AppColors.tphBgColor(), AppColors.editTextBorderColor(), 30f)
//        binding?.password?.background =
//            strokeBgDrawable(AppColors.tphBgColor(), AppColors.editTextBorderColor(), 30f)
//        binding?.forgetPassword?.background =
//            strokeBgDrawable(AppColors.clearColor(), AppColors.clearColor(), 10f)
    }

    private fun setTextWatcherForEmail() {
        binding?.name?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            }

            override fun afterTextChanged(editable: Editable) {
//                editTextEmptyCheck()
            }
        })
    }


    private fun callLoginApi() {
        showLoading(binding?.progressBar, true)
        preference?.appPrefAccessToken = ""
        viewModel?.hitLoginAPI(binding?.name?.text.toString(), binding?.password?.text.toString())?.observe(this, Observer {
                if (it != null) {
                    Log.d("LoginResponse", Gson().toJson(it))
                    AnalyticsUtils.logEvent(this,AppConstants.SIGN_IN_SUCCESS)
                    try {
                        if (it.responseCode == 2000) {
                            val gson = Gson()
                            val loginData: Data = it.data
                            modelLogin = it.data
                            val stringJson = gson.toJson(loginData)

                            // Example: Track User Attributes on Login Success
                            AnalyticsUtils.trackUserAttributes(this, it.data.id.toString(), it.data.name, it.data.email)

                            if (featureList?.featureFlag?.ACTIVE_DEVICE_CALL == true){
                                callLoginDevice(stringJson)
                            }else{
                                saveUserDetails(stringJson, true)
                            }
                        } else if (it.responseCode == 4089) {
                            dismissLoading(binding?.progressBar)
//                            commonDialog(
//                                stringsHelper.stringParse(
//                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
//                                    getString(R.string.popup_error)
//                                ),
//                                it.debugMessage.toString(),
//                                stringsHelper.stringParse(
//                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                                    getString(R.string.popup_continue)
//                                )
//                            )
                            showToast("",resources.getString(R.string.popup_error))
                        } else {
                            if (it.debugMessage != null) {
                                dismissLoading(binding?.progressBar)
//                                commonDialog(
//                                    stringsHelper.stringParse(
//                                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
//                                        getString(R.string.popup_error)
//                                    ), stringsHelper.stringParse(
//                                        stringsHelper.instance()?.data?.config?.popup_username_pwd_does_not_match.toString(),
//                                        getString(R.string.popup_username_pwd_does_not_match)
//                                    ), stringsHelper.stringParse(
//                                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                                        getString(R.string.popup_continue)
//                                    )
//                                )
                                showToast(resources.getString(R.string.popup_error),resources.getString(R.string.popup_username_pwd_does_not_match))
                            } else {
                                dismissLoading(binding?.progressBar)
//                                commonDialog(
//                                    stringsHelper.stringParse(
//                                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
//                                        getString(R.string.popup_error)
//                                    ), stringsHelper.stringParse(
//                                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
//                                        getString(R.string.something_went_wrong)
//                                    ), stringsHelper.stringParse(
//                                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                                        getString(R.string.popup_continue)
//                                    )
//                                )
                                showToast("",resources.getString(R.string.popup_something_went_wrong))
                            }
                        }

                    } catch (e: NullPointerException) {
                        Logger.w(e)
                    }
                } else {
                    dismissLoading(binding?.progressBar)
//                    commonDialog(
//                        stringsHelper.stringParse(
//                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
//                            getString(R.string.popup_error)
//                        ), stringsHelper.stringParse(
//                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
//                            getString(R.string.something_went_wrong)
//                        ), stringsHelper.stringParse(
//                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                            getString(R.string.popup_continue)
//                        )
//                    )
                    showToast("",resources.getString(R.string.popup_something_went_wrong))
                }
            })

    }

    fun hitApiFBLogin() {
        if (CheckInternetConnection.isOnline(this@ActivityLogin)) {
            showLoading(getBinding()?.progressBar, true)
            viewModel?.hitFbLogin(
                this@ActivityLogin, email, accessTokenFB, name, id, "", hasFbEmail
            )?.observe(this@ActivityLogin) { loginResponseModelResponse ->
                if (Objects.requireNonNull(loginResponseModelResponse).responseCode == 2000) {
                    val gson = Gson()
                    modelLogin = loginResponseModelResponse.data
                    val stringJson = gson.toJson(loginResponseModelResponse.data)
                    Log.d("stringJson", stringJson)
                    Log.d("stringJson", loginResponseModelResponse.data.isVerified.toString())
                    if (featureList?.featureFlag?.ACTIVE_DEVICE_CALL == true){
                        callLoginDevice(stringJson)
                    }else{
                        dismissLoading(binding?.progressBar)
                        saveUserDetails(stringJson, false)
                    }

                } else if (loginResponseModelResponse.responseCode === 403) {
                    ActivityLauncher.getInstance().forceLogin(
                        this@ActivityLogin,
                        ActivityForgotPassword::class.java,
                        accessTokenFB,
                        id,
                        name,
                        "",
                        true
                    )
                } else {
                    dismissLoading(getBinding()?.progressBar)
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ), loginResponseModelResponse.debugMessage.toString(),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                            getString(R.string.popup_ok)
                        )
                    )
                }
            }
        } else {
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                    getString(R.string.popup_no_internet_connection_found)
                ), "", stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        try {
            callbackManager!!.onActivityResult(requestCode, resultCode, data)
        } catch (e: java.lang.Exception) {
            Logger.e("LoginActivity", "" + e.toString())
        }
    }


    private fun saveUserDetails(stringJson: String?, b: Boolean) {
        val userLoginData: Data = Gson().fromJson(stringJson, Data::class.java)
        val gson = Gson()
        val stringJson = gson.toJson(userLoginData)

        // Example: Track User Attributes on Login Success
        AnalyticsUtils.trackUserAttributes(this, userLoginData.id.toString(), userLoginData.name, userLoginData.email)

        getProfile(userLoginData)

        preference?.setIsVerified(userLoginData.isVerified.toString())
        if (b) {
            preference?.appPrefLoginType = AppConstants.UserLoginType.Manual.toString()
        } else {
            preference?.appPrefLoginType = AppConstants.UserLoginType.FbLogin.toString()
        }
        AppConstants.UserLoginType.FbLogin.toString()
        preference?.appPrefProfile = stringJson
        preference?.appPrefLoginStatus = AppConstants.UserStatus.Login.toString()
        preference?.appPrefUserId = userLoginData.id.toString()
        preference?.appPrefUserEmail = userLoginData.email.toString()
        preference?.expirytime = userLoginData.expiryTime.toString()
        if (userLoginData.name != null) {
            preference?.appPrefUserName = userLoginData.name.toString()
        }
        //  checkPlansForUser()
    }


    private fun openSecondaryActivity(){
        Intent(this, SecondaryProfileActivity::class.java).also {
            it.putExtra(AppConstants.HIDE_BACK_BTN, true)
            startActivity(it)
            finish()
        }
    }


     private fun getProfile(userLoginData: Data) {
        val authToken = KsPreferenceKeys.getInstance().appPrefAccessToken
        viewModel?.hitUserProfile(this@ActivityLogin,authToken)?.observe(this) {
            dismissLoading(binding?.progressBar)
            if (it != null) {
                if (it.status) {
                    Logger.e("profileRes", it.toString())
                    if (it.data.deletionRequestStatus != null) {
                        val deletionRequestStatus: String = it.data.deletionRequestStatus
                        if (deletionRequestStatus.equals("UNDER_REVIEW")) {
                            KsPreferenceKeys.getInstance().deleteAccountRequestStatus = true
                        } else {
                            KsPreferenceKeys.getInstance().deleteAccountRequestStatus = false
                        }
                    }

//                    it.data.profilePicURL?.toString()?.let {
//                        setUpdatedProfileImageFullUrl(it)
//                    }
                    selectedArtistId= it.data.customData.sponsoredArtist?:""
                    KsPreferenceKeys.getInstance().sponsorArtistId = selectedArtistId

                    if (!StringUtils.isNullOrEmpty(it.data.customData.sponsoredArtist)) {
                        if (KsPreferenceKeys.getInstance().sponsorArtistId != it.data.customData.sponsoredArtist){
                            KsPreferenceKeys.getInstance().sponsorArtistId = ""
                            KsPreferenceKeys.getInstance().assetType = ""
                            KsPreferenceKeys.getInstance().contentSlugSponser = ""
                        }else{
                            KsPreferenceKeys.getInstance().sponsorArtistId = ""
                            KsPreferenceKeys.getInstance().sponsorArtistId = it.data.customData.sponsoredArtist ?: ""
                        }
                    }else{
                        KsPreferenceKeys.getInstance().sponsorArtistId = ""
                    }

                    if (featureList?.featureFlag?.IS_VERIFICATION_ALLOWED == true) {
                        if (userLoginData.isVerified){
                            if (featureList?.featureFlag?.MULTIPLE_PROFILE_ENABLE == true)
                                openSecondaryActivity()
                            else
                                ActivityLauncher.getInstance().homeActivity(this@ActivityLogin, HomeActivity::class.java)
                        } else {
                            ActivityLauncher.getInstance().goToEnterOTP(this@ActivityLogin, EnterOTPActivity::class.java, "login")
                        }
                    } else {
                        if (featureList?.featureFlag?.MULTIPLE_PROFILE_ENABLE == true)
                            openSecondaryActivity()
                        else
                            ActivityLauncher.getInstance().homeActivity(this@ActivityLogin, HomeActivity::class.java)
                    }
                }
                if (it.responseCode == 4302) {
                    //   isloggedout = true
                    hitApiLogout(this,authToken)
                    ActivityLauncher.getInstance().goToLogin(this@ActivityLogin, ActivityLogin::class.java)

                    try {
                        runOnUiThread { onBackPressed() }
                    } catch (_: java.lang.Exception) {
                    }
                } else if (it.responseCode == 4019) {
                    commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ), it.debugMessage.toString(), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                } else if (it.responseCode == 4901) {
                    commonDialog(stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ), it.debugMessage.toString(), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
                }
            }else{
                commonDialog(stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                    getString(R.string.popup_error)
                ),stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                    getString(R.string.something_went_wrong)
                ) , stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)))
            }
        }
    }


    private fun checkPlansForUser() {
        showLoading(binding?.progressBar, true)
        val token: String = KsPreferenceKeys.getInstance().appPrefAccessToken
        GetPlansLayer.getInstance().getEntitlementStatus(
            KsPreferenceKeys.getInstance(),
            token
        ) { entitlementStatus, apiStatus, _, _,_ ->
            dismissLoading(binding?.progressBar)
            if (apiStatus) {
                if (entitlementStatus) {
                    if (KsPreferenceKeys.getInstance().isVerified.equals(
                            "true",
                            ignoreCase = true
                        )
                    ) {
                        ActivityLauncher.getInstance()
                            .homeScreen(this@ActivityLogin, HomeActivity::class.java,false,"","", 0)
                    } else {
                        ActivityLauncher.getInstance().goToEnterOTP(
                            this,
                            EnterOTPActivity::class.java, "Login"
                        )
                    }
                } else {
                    ActivityLauncher.getInstance().goToPlanScreen(
                        this@ActivityLogin,
                        ActivitySelectSubscriptionPlan::class.java,
                        "Login"
                    )
                }
            } else {
                ActivityLauncher.getInstance()
                    .homeScreen(this@ActivityLogin, HomeActivity::class.java,false,"","", 0)
            }
        }
    }

    private fun validateEmptyEmail(): Boolean {
        var check = false
        email = binding?.name?.text.toString()
        if (StringUtils.isNullOrEmptyOrZero(email)) {
//            commonDialog(
//                stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_empty_email_tittle.toString(),
//                    getString(R.string.popup_empty_email_tittle)
//                ),
//                stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_empty_email_subtitle.toString(),
//                    getString(R.string.popup_empty_email_subtitle)
//                ),
//                stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                    getString(R.string.popup_continue)
//                )
//            )
//            var msg:String="${resources.getString(R.string.popup_empty_email_tittle)} ${resources.getString(R.string.popup_empty_email_tittle)}"
//            msg.trim()
            showToast(resources.getString(R.string.popup_empty_email_tittle).trim(),resources.getString(R.string.popup_empty_email_subtitle).trim())
        } else {
            check = true
        }
        return check
    }

    private fun showToast(title: String,message: String) {
        Toast.makeText(this@ActivityLogin,message,Toast.LENGTH_LONG).show()
//        val snackbar= Snackbar.make(binding!!.root,message,Snackbar.LENGTH_LONG).show()

//        Snackbar.make(this@ActivityLogin,binding!!.root,"",Snackbar.LENGTH_LONG)
//            .show()
    }

    private fun validateEmail(): Boolean {
        var check = false
        if (binding?.name?.text.toString().trim().matches(regex)) {
            check = true
        } else {
//            commonDialog(
//                stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_invalid_email_tittle.toString(),
//                    getString(R.string.popup_invalid_email_tittle)
//                ), stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_invalid_email_subtitle.toString(),
//                    getString(R.string.popup_invalid_email_subtitle)
//                ), stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                    getString(R.string.popup_continue)
//                )
//            )
            showToast(resources.getString(R.string.popup_invalid_email_tittle).trim(),resources.getString(R.string.popup_invalid_email_subtitle).trim())
        }
        return check
    }

    private fun validateEmptyPassword(): Boolean {
        var check = false
        if (binding?.password?.text.toString().trim() != "") {
            password = binding?.password?.text.toString().trim().matches(regexPass).toString()
            if ((binding?.password?.text.toString().trim().matches(regexPass))) {
                check = true
            } else {
//                commonDialog(
//                    stringsHelper.stringParse(
//                        stringsHelper.instance()?.data?.config?.popup_incorrect_pwd_tittle.toString(),
//                        getString(R.string.popup_incorrect_pwd_tittle)
//                    ), stringsHelper.stringParse(
//                        stringsHelper.instance()?.data?.config?.popup_pwd_must_be_8_to_16_char.toString(),
//                        getString(R.string.popup_pwd_must_be_8_to_16_char)
//                    ), stringsHelper.stringParse(
//                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                        getString(R.string.popup_continue)
//                    )
//                )
                showToast(resources.getString(R.string.popup_incorrect_pwd_tittle).trim(),resources.getString(R.string.popup_pwd_must_be_8_to_16_char).trim())
            }
        } else {
//            commonDialog(
//                stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_empty_Password.toString(),
//                    getString(R.string.popup_empty_Password)
//                ), stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_empty_message_Password.toString(),
//                    getString(R.string.popup_empty_message_Password)
//                ), stringsHelper.stringParse(
//                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
//                    getString(R.string.popup_continue)
//                )
//            )
            showToast(resources.getString(R.string.popup_empty_Password).trim(),resources.getString(R.string.popup_empty_message_Password).trim())
        }
        return check
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            AnalyticsUtils.trackScreenView(this, AppConstants.LOGIN)
            binding?.loginMainFrame?.visibility = View.VISIBLE
            binding?.toolbar?.rlToolBar?.visibility = View.VISIBLE
            binding?.connection?.noConnectionLayout?.visibility = View.GONE

            if (featureList?.featureFlag?.SIGN_UP_WITH_EMAIL == true) {
                binding?.newAccount?.visibility = View.VISIBLE
            }
            if (featureList?.featureFlag?.SOCIAL_FACEBOOK_LOGIN == true) {
                binding?.socialLogin?.visibility = View.VISIBLE
                binding?.facebookImg?.visibility = View.VISIBLE
            }
            if (featureList?.featureFlag?.SOCIAL_APPLE_LOGIN == true) {
                binding?.socialLogin?.visibility = View.VISIBLE
                binding?.appleImg?.visibility = View.VISIBLE
            }
        } else {
            noConnectionLayout()
        }
    }

    private fun connectionObserver() {
        if (NetworkConnectivity.isOnline(this)) {
            connectionValidation(true)
        } else {
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                    getString(R.string.popup_no_internet_connection_found)
                ), "", stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
    }

    private fun noConnectionLayout() {
        binding?.toolbar?.rlToolBar?.visibility = View.GONE
        binding?.loginMainFrame?.visibility = View.GONE
        binding?.connection?.noConnectionLayout?.visibility = View.VISIBLE
        binding?.connection?.retryTxt?.setOnClickListener { view -> connectionObserver() }
    }


    private fun setTextWatcher() {
        binding?.password?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}

            @RequiresApi(api = Build.VERSION_CODES.M)
            override fun afterTextChanged(editable: Editable) {
//                editTextEmptyCheck()
            }
        })
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm: FragmentManager = supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun callLoginDevice(stringJson: String) {
        deviceManagementViewModel?.getLoginDevice(KsPreferenceKeys.getInstance().appPrefAccessToken)
            ?.observe(this) {
                if (it != null) {
                    when {
                        it.responseCode == 2000 -> {
                            dismissLoading(binding?.progressBar)
                            saveUserDetails(stringJson, true)
                        }
                        it.responseCode == 4500 -> {
                            dismissLoading(binding?.progressBar)
                            startActivity(Intent(this@ActivityLogin, DeviceManagerActivity::class.java))
                            Toast.makeText(this, "Device Limit reached", Toast.LENGTH_LONG).show()
                        }
                        it.debugMessage != null -> {
                            dismissLoading(binding?.progressBar)
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ), stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_username_pwd_does_not_match.toString(),
                                    getString(R.string.popup_username_pwd_does_not_match)
                                ), stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        }
                        else -> {
                            dismissLoading(binding?.progressBar)
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ), stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.something_went_wrong)
                                ), stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        }
                    }
                }
            }
    }

    override fun onActionBtnClicked() {
       /* val i = Intent(this@ActivityLogin, DeviceManagerActivity::class.java)
        startActivity(i)*/
    }

    override fun onCancelBtnClicked() {

    }

    private fun clearEditView() {
        binding?.name?.setText("")
        binding?.password?.setText("")
    }

    override fun onAppSignInSuccess(
        jwt_token: String
    ) {
        runOnUiThread {
            try {
                jwtToken = jwt_token
                val token: String = jwt_token
                val preference = KsPreferenceKeys.getInstance()
                preference.appPrefAccessToken = token
                var signInResponse = AppCommonMethod.getDecodedJwt(jwt_token)
                val separated: Array<String> = signInResponse.split("}").toTypedArray()
                var firstIndex = separated[0]
                var lastIndex = separated[1]
                if (lastIndex != null) {
                    saveUserDetails(lastIndex + "}", true)
                    //ActivityLauncher.getInstance().homeslider(this@ActivityLogin, HomeSliderActivity::class.java)
                }

            } catch (e: Exception) {
                Logger.w(e)
            }

        }
    }

    override fun onAppSignInError() {
        runOnUiThread {
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                    getString(R.string.popup_error)
                ), stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                    getString(R.string.something_went_wrong)
                ), stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
    }
}