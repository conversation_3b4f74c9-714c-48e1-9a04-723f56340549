package com.enveu.activities.usermanagment.ui

import android.Manifest
import android.annotation.SuppressLint
import android.app.AlertDialog
import android.app.DatePickerDialog
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.provider.MediaStore
import android.text.*
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.StyleSpan
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.CompoundButton
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.amazonaws.ClientConfiguration
import com.amazonaws.auth.CognitoCachingCredentialsProvider
import com.amazonaws.mobileconnectors.s3.transferutility.TransferListener
import com.amazonaws.mobileconnectors.s3.transferutility.TransferObserver
import com.amazonaws.mobileconnectors.s3.transferutility.TransferState
import com.amazonaws.mobileconnectors.s3.transferutility.TransferUtility
import com.amazonaws.regions.Region
import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3Client
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.device_management.DeviceManagementViewModel
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.profile.ui.ProfileActivityNew
import com.enveu.activities.profile.ui.ProfileActivityNew.Companion
import com.enveu.activities.purchase.plans_layer.GetPlansLayer
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.responseModels.LoginResponse.Data
import com.enveu.callbacks.commonCallbacks.AppleSignInListener
import com.enveu.databinding.ActivitySignupBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.utils.BindingUtils.FontUtil
import com.enveu.utils.Constants
import com.enveu.utils.FileUtil
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.AppColors
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod

import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.AppleSignInManager
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.SharedPrefHelper
import com.enveu.utils.helpers.StringUtils
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.show
import com.enveu.utils.showToast
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.facebook.*
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.google.gson.Gson
import com.google.gson.JsonObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

class ActivitySignUp : BaseBindingActivity<ActivitySignupBinding?>(),
    CommonDialogFragment.EditDialogListener,
    AppleSignInListener {
    var font: Typeface? = null
    private var s3: AmazonS3Client? = null
    private var imageUrlId = ""
    private var transferUtility: TransferUtility? = null
    private var viewModel: RegistrationLoginViewModel? = null
    private var deviceManagementViewModel: DeviceManagementViewModel? = null
    private val permissionNeeds = listOf("email", "public_profile")
    private var isNotificationEnable = false
    private var ifCheckboxIsChecked = true
    private var ifCheckboxChecked = false
    private lateinit var name: String
    private var via = "Gallery"
    private lateinit var userName: String
    private lateinit var gender: String
    private lateinit var featureList: FeatureFlagModel
    private lateinit var email: String
    private var selectedCountryCode: String? = null
    private lateinit var phoneNumber: String
    private var dob: String = ""
    private var dateMilliseconds = ""
    private var accessTokenFB: String? = null
    private var callbackManager: CallbackManager? = null
    var hasFbEmail = false
    var isFbLoginClick = false
    private var mLastClickTime: Long = 0
    private var modelLogin: Data? = null
    private var id = ""
    private var preference: KsPreferenceKeys? = null
    private lateinit var password: String
    private var jwtToken = ""
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    val regexPass: Regex = Regex("^.{8,16}$")
    val regex: Regex = Regex("[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,100}")

    companion object {
        private val REQUEST_CODE_PICK_IMAGE = 1001
        private val REQUEST_CODE_PERMISSION = 2001
    }

    private var imageUri: Uri? = null
    private var imageBitmap: String? = null

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivitySignupBinding {
        return ActivitySignupBinding.inflate(inflater)
    }

    @SuppressLint("RestrictedApi")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        featureList = AppConfigMethod.parseFeatureFlagList()
        SharedPrefHelper.getInstance().setColorJson(ColorsHelper.loadDataFromJson())
        SharedPrefHelper.getInstance().setStringJson(StringsHelper.loadDataFromJson())
        parseColor()
        binding?.signIn?.paintFlags = binding!!.signIn.paintFlags or Paint.UNDERLINE_TEXT_FLAG
        viewModel = ViewModelProvider(this@ActivitySignUp)[RegistrationLoginViewModel::class.java]
        deviceManagementViewModel =
            ViewModelProvider(this@ActivitySignUp)[DeviceManagementViewModel::class.java]
        preference = KsPreferenceKeys.getInstance()
        uiCall()
        setClicks()
        credentialsProvider()
        setTransferUtility()
        connectionObserver()
        // uniqueUserNameApi()
      //  binding?.name?.addTextChangedListener(commonTextWatcher)
        binding?.userName?.addTextChangedListener(commonTextWatcher)
        binding?.dOB?.addTextChangedListener(commonTextWatcher)
      //  binding?.Gender?.addTextChangedListener(commonTextWatcher)
      //  binding?.email?.addTextChangedListener(commonTextWatcher)
      //  binding?.password?.addTextChangedListener(commonTextWatcher)

        if (featureList?.featureFlag?.ALLOW_ANONYMOUS_USER_ACCESS_CONTENT == true) {
            binding?.toolbar?.backLayout?.visibility = View.GONE
            binding?.toolbar?.titleSkip?.visibility = View.VISIBLE
            binding?.toolbar?.titleSkip?.setOnClickListener {
                val i = Intent(this@ActivitySignUp, HomeActivity::class.java)
                startActivity(i)
            }
        }

        if (KsPreferenceKeys.getInstance()?.appLanguage?.equals(AppConstants.LANGUAGE_ARABIC, true) == true){
            binding?.password?.gravity =  Gravity.END or Gravity.CENTER_VERTICAL
            binding?.header?.layoutDirection = View.LAYOUT_DIRECTION_RTL
        }
        /* setTextWatcher()
         emailTextWatcher()
         passwordTextWatcher()*/

    }

    private fun parseColor() {
        binding?.colorsData = colorsHelper
        binding?.stringData = stringsHelper
        binding?.toolbar?.colorsData = colorsHelper
        binding?.toolbar?.stringData = stringsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper

        val textColorStates = ColorStateList(
            arrayOf(
                intArrayOf(-android.R.attr.state_checked),
                intArrayOf(android.R.attr.state_checked)
            ),
            intArrayOf(AppColors.pwdSelectedEyeColor(), AppColors.pwdUnselectedEyeColor())
        )
        binding?.radioPasswordEye?.buttonDrawable?.setTintList(textColorStates)
    }

    private fun passwordTextWatcher() {
        binding?.password?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            }

            override fun afterTextChanged(editable: Editable) {
                editTextEmptyCheck()
            }
        })
    }

    private fun emailTextWatcher() {
        binding?.email?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            }

            override fun afterTextChanged(editable: Editable) {
                editTextEmptyCheck()
            }
        })
    }


    private fun editTextEmptyCheck(): Boolean {
        val email = binding?.email?.text
        val pass = binding?.password?.text
        if (!email.isNullOrEmpty() && !pass.isNullOrEmpty() && binding?.checkBox?.isChecked == true) {
            binding?.signUp?.isEnabled = true
            binding?.signUp?.setBackgroundResource(R.drawable.roundedcornerforbtn)
            binding?.signUp?.setTextColor(getColor(R.color.main_btn_txt_color))
            return true
        } else {
            binding?.signUp?.isEnabled = false
            binding?.signUp?.setBackgroundResource(R.drawable.signup_btn_gradient)
            binding?.signUp?.setTextColor(getColor(R.color.main_btn_txt_color))
            return false
        }
    }


    private fun setClicks() {

        binding?.toolbar?.backLayout?.setOnClickListener { onBackPressed() }

        callbackManager = CallbackManager.Factory.create()
        FacebookSdk.fullyInitialize();
        binding?.fbButton?.setReadPermissions(permissionNeeds);

        if (featureList.featureFlag.SIGN_UP_WITH_PHONE_NUMBER) {
            binding?.phoneLayout?.visibility = View.VISIBLE
        }

        if (featureList.featureFlag.IS_DOB_ENABLE) {
            binding?.dOB?.visibility = View.VISIBLE
        }

        if (featureList.featureFlag.IS_USERNAME_ENABLE) {
            binding?.userName?.visibility = View.VISIBLE
        }

        if (featureList.featureFlag.IS_NAME_ENABLE) {
            binding?.name?.visibility = View.VISIBLE
        }

        if (featureList.featureFlag.IS_GENDER_ENABLE) {
            binding?.Gender?.visibility = View.VISIBLE
        }

        if (featureList.featureFlag.IS_CITY_ENABLE) {
            binding?.city?.visibility = View.VISIBLE
        }
        if (featureList.featureFlag.IS_STATE_ENABLE) {
            binding?.state?.visibility = View.VISIBLE
        }

        if (featureList.featureFlag.SOCIAL_FACEBOOK_SIGNUP) {
            binding?.facebookImg?.visibility = View.VISIBLE
        }

        if (featureList.featureFlag.SOCIAL_APPLE_SIGNUP) {
            binding?.appleImg?.visibility = View.VISIBLE
        }
        binding?.facebookImg?.setOnClickListener {
            hideSoftKeyboard(binding?.facebookImg)
            if (CheckInternetConnection.isOnline(this@ActivitySignUp)) {
                if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
                    return@setOnClickListener
                }
                mLastClickTime = SystemClock.elapsedRealtime()
                isFbLoginClick = true
                binding?.fbButton?.performClick()

            } else {
                showToast("", resources.getString(R.string.popup_no_internet_connection_found))
            }
        }

//        binding?.dOB?.setOnClickListener {
//            val mcurrentDate = Calendar.getInstance()
//            val mYear = mcurrentDate[Calendar.YEAR]
//            val mMonth = mcurrentDate[Calendar.MONTH]
//            val mDay = mcurrentDate[Calendar.DAY_OF_MONTH]
//            val mDatePicker = DatePickerDialog(
//                this@ActivitySignUp,
//                { datepicker, selectedyear, selectedmonth, selectedday ->
//                    mcurrentDate[Calendar.YEAR] = selectedyear
//                    mcurrentDate[Calendar.MONTH] = selectedmonth
//                    mcurrentDate[Calendar.DAY_OF_MONTH] = selectedday
//                    val difference = mYear - selectedyear
//                    if (difference >= 12) {
//                        val sdf = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
//                        binding?.dOB?.text = sdf.format(mcurrentDate.time)
//                        try {
//                            val d = sdf.parse(binding?.dOB?.text.toString())
//                            dateMilliseconds = d.time.toString()
//                        } catch (e: ParseException) {
//                            Logger.w(e)
//                        }
//                    } else {
//                        binding?.dOB?.text = ""
//                        dateMilliseconds = ""
//                        binding?.errorDob?.show()
//                        binding?.errorDob?.text =
//                            resources.getString(R.string.popup_date_difference)
////                        Toast.makeText(this, stringsHelper.instance()?.data?.config?.popup_date_difference, Toast.LENGTH_SHORT).show()
//                    }
//                }, mYear, mMonth, mDay
//            )
//            mDatePicker.show()
//        }

        binding?.dOB?.setOnClickListener {
            val mcurrentDate = Calendar.getInstance()
            val mYear = mcurrentDate[Calendar.YEAR]
            val mMonth = mcurrentDate[Calendar.MONTH]
            val mDay = mcurrentDate[Calendar.DAY_OF_MONTH]
            val maxDate = Calendar.getInstance()
            maxDate.add(Calendar.YEAR, -12)

            val mDatePicker = DatePickerDialog(
                this@ActivitySignUp,
                { datepicker, selectedyear, selectedmonth, selectedday ->
                    mcurrentDate[Calendar.YEAR] = selectedyear
                    mcurrentDate[Calendar.MONTH] = selectedmonth
                    mcurrentDate[Calendar.DAY_OF_MONTH] = selectedday
                    val difference = mYear - selectedyear
                    if (difference >= 12) {
                        val sdf = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
                        binding?.dOB?.text = sdf.format(mcurrentDate.time)
                        try {
                            val d = sdf.parse(binding?.dOB?.text.toString())
                            dateMilliseconds = d.time.toString()
                        } catch (e: ParseException) {
                            Logger.w(e)
                        }
                    } else {
                        binding?.dOB?.text = ""
                        dateMilliseconds = ""
                        binding?.errorDob?.show()
                        binding?.errorDob?.text =
                            resources.getString(R.string.popup_date_difference)
                    }
                }, mYear, mMonth, mDay
            )
            mDatePicker.datePicker.maxDate = maxDate.timeInMillis
            mDatePicker.updateDate(maxDate.get(Calendar.YEAR), maxDate.get(Calendar.MONTH), maxDate.get(Calendar.DAY_OF_MONTH))
            mDatePicker.show()
        }



        binding?.profileImage?.setOnClickListener {
            openGallery()
        }

        binding?.Gender?.setOnClickListener {
            val genderOptions = if (KsPreferenceKeys.getInstance().appLanguage == AppConstants.LANGUAGE_ARABIC){
                arrayOf("ذكر", "أنثى", "بدلا من ذلك لا أقول")
            }else{
                arrayOf("Male", "Female", "Rather Not Say")
            }
            val builder = AlertDialog.Builder(this)
            builder.setTitle(R.string.select_gender)
            builder.setItems(genderOptions) { _, which ->
                binding?.Gender?.text = genderOptions[which]
            }
            builder.show()
        }

        binding?.appleImg?.setOnClickListener {
            showLoading(binding?.progressBar, true)
            viewModel?.authResponse?.observe(this) {
                if (it != null) {
                    dismissLoading(binding?.progressBar)
                    Log.d("LoginResponse", Gson().toJson(it))
                    try {
                        if (it.responseCode === 2000 && it.data.authURL != null) {
                            AppleSignInManager.setContext(this)
                            AppleSignInManager.setUrl(it.data.authURL)
                            AppleSignInManager.setupAppleWebViewDialog()
                        } else if (it.debugMessage != null) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                it.debugMessage.toString(),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        } else {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.something_went_wrong)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        }

                    } catch (e: Exception) {
                        Logger.w(e)
                    }
                } else {
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )

                }
            }
        }

        binding?.fbButton?.registerCallback(callbackManager, object :
            FacebookCallback<LoginResult?> {
            override fun onCancel() {
                println("onCancel")
            }

            override fun onError(error: FacebookException) {
                if (error is FacebookAuthorizationException) LoginManager.getInstance().logOut()
            }

            override fun onSuccess(result: LoginResult?) {
                println("onSuccess")
                if (result != null) {
                    accessTokenFB = result.accessToken.token
                }
                val request = GraphRequest.newMeRequest(
                    result?.accessToken
                ) { `object`: JSONObject?, response: GraphResponse? ->
                    Logger.i(
                        "LoginActivity", response.toString()
                    )
                    try {
                        id = `object`!!.getString("id")
                        name = `object`.getString("name")
                        if (`object`.has("email")) {
                            email = `object`.getString("email")
                            hasFbEmail = true
                        } else hasFbEmail = false
                        showHideProgress(getBinding()!!.progressBar)
                        hitApiFBLogin()
                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                }
                val parameters = Bundle()
                parameters.putString(
                    "fields",
                    "id,name,email"
                )
                request.parameters = parameters
                request.executeAsync()
            }
        })


        // Get the selected country code
        selectedCountryCode = binding?.countryCodePicker?.selectedCountryCode

        // Get the text from the EditText
        phoneNumber = binding?.phoneNumber?.text.toString()
    }

    fun hitApiFBLogin() {
        if (CheckInternetConnection.isOnline(this@ActivitySignUp)) {
            showLoading(getBinding()?.progressBar, true)
            viewModel?.hitFbLogin(
                this@ActivitySignUp, email, accessTokenFB, name, id, "", hasFbEmail
            )?.observe(this@ActivitySignUp) { loginResponseModelResponse ->
                if (Objects.requireNonNull(loginResponseModelResponse).responseCode == 2000) {
                    val gson = Gson()
                    modelLogin = loginResponseModelResponse.data
                    val stringJson = gson.toJson(loginResponseModelResponse.data)
                    if (featureList?.featureFlag?.ACTIVE_DEVICE_CALL == true) {
                        callSignupDevice(stringJson)
                    } else {
                        dismissLoading(binding?.progressBar)
                        saveUserDetails(stringJson, true)
                    }
                    Log.d("stringJson", stringJson)
                    saveUserDetails(stringJson, false)
                    Log.d("stringJson", loginResponseModelResponse.data.isVerified.toString())
                    ActivityLauncher.getInstance().goToPlanScreen(
                        this@ActivitySignUp,
                        ActivitySelectSubscriptionPlan::class.java, "SignUp"
                    )
//                    if (loginResponseModelResponse.data.isVerified){
//                        ActivityLauncher.getInstance().homeslider(this@ActivitySignUp, HomeSliderActivity::class.java)
//                    }else{
//                        ActivityLauncher.getInstance().goToPlanScreen(this@ActivitySignUp,ActivitySelectSubscriptionPlan::class.java,true)
//                    }
                    // AppCommonMethod.trackFcmCustomEvent(applicationContext, AppConstants.SIGN_IN_SUCCESS, "", "", "", 0, " ", 0, "", 0, 0, "", "", loginResponseModelResponse.data.id.toString() + "", loginResponseModelResponse.data.name + ""

                } else if (loginResponseModelResponse.responseCode === 403) {
                    dismissLoading(binding?.progressBar)
                    ActivityLauncher.getInstance().forceLogin(
                        this@ActivitySignUp,
                        ActivityForgotPassword::class.java,
                        accessTokenFB,
                        id,
                        name,
                        "",
                        true
                    )
                } else {
                    dismissLoading(binding?.progressBar)
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ), loginResponseModelResponse.debugMessage.toString(),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                            getString(R.string.popup_ok)
                        )
                    )
                }
            }
        } else {
            dismissLoading(binding?.progressBar)
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                    getString(R.string.popup_no_internet_connection_found)
                ),
                "",
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
    }

    private fun uiCall() {
        binding?.userName?.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val username = binding?.userName?.text.toString()
                if (username.isNotBlank()) {
                    uniqueUserNameApi(false)
                }
            }
        }
        binding?.signIn?.setOnClickListener {
            ActivityLauncher.getInstance().goToLogin(this@ActivitySignUp, ActivityLogin::class.java)
        }

        binding?.toolbar?.titleSkip?.visibility = View.GONE

        val signupTermConditionText = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.signup_term_condition.toString(),
            getString(R.string.signup_term_condition)
        )

        setSpannableClickableLinks()

        binding?.checkBox?.setOnCheckedChangeListener { _: CompoundButton?, b: Boolean ->
            if (b) {
                binding?.checkBox?.setBackgroundResource(R.drawable.checked_state)
                isNotificationEnable = ifCheckboxIsChecked
                ifCheckboxChecked = ifCheckboxIsChecked
                binding?.checkBox?.background = strokeCheckBoxBgDrawable(
                    AppColors.radioBtnCheckedColor(),
                    AppColors.radioBtnUncheckedColor(),
                    5,
                    0f
                )
//                editTextEmptyCheck()

            } else {
                //binding?.checkBox?.setBackgroundResource(R.drawable.unchecked_state)
                isNotificationEnable = !ifCheckboxIsChecked
                ifCheckboxChecked = !ifCheckboxIsChecked
                binding?.checkBox?.background = strokeCheckBoxBgDrawable(
                    AppColors.appBgColor(),
                    AppColors.radioBtnUncheckedColor(),
                    5,
                    0f
                )
//                editTextEmptyCheck()
            }
            //   KsPreferenceKeys.getInstance().saveNotificationEnable(isNotificationEnable)
        }

        binding?.password?.isLongClickable = false
        binding?.password?.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
        font = FontUtil.getNormal(this)
        binding?.password?.typeface = font

        binding?.radioPasswordEye?.setOnCheckedChangeListener { compoundButton, b ->
            if (KsPreferenceKeys.getInstance()?.appLanguage?.equals(AppConstants.LANGUAGE_ARABIC, true) == true){
                binding?.password?.gravity =  Gravity.START or Gravity.CENTER_VERTICAL
            }
            if (!b) {
                binding?.password?.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
                binding?.password?.typeface = font
                if (KsPreferenceKeys.getInstance()?.appLanguage?.equals(AppConstants.LANGUAGE_ARABIC, true) == true){
                    binding?.password?.gravity =  Gravity.END or Gravity.CENTER_VERTICAL
                }
                } else {
                binding?.password?.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
                binding?.password?.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
                hideSoftKeyboard(binding?.radioPasswordEye)
                binding?.password?.typeface = font
            }
            binding?.password?.text?.let { binding?.password?.setSelection(it.length) }
        }
        binding?.signUp?.setOnClickListener {
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
                return@setOnClickListener
            }
            mLastClickTime = SystemClock.elapsedRealtime()
            hideSoftKeyboard(binding?.signUp)
            if (CheckInternetConnection.isOnline(this@ActivitySignUp)) {
                if (validateEmptyName() && validateEmptyUserName() && validateEmptyDob() && validateEmptyGender() && validateEmptyEmail() && validateEmail() && validateEmptyPassword() && validateCheckboxChecked()) {
                    uniqueUserNameApi(true)
                    binding?.progressBar?.show()
                }
            } else {
                showToast("", resources.getString(R.string.popup_no_internet_connection_found))
            }
        }
    }

    private fun showToast(title: String, message: String) {
        Toast.makeText(this@ActivitySignUp, title, Toast.LENGTH_LONG).show()
    }

    private fun checkCityIsNotEmpty(): Boolean {
        return if (featureList.featureFlag.IS_CITY_MANDATORY) {
            binding?.city?.text?.isNotEmpty() == true
        } else {
            true
        }
    }

    private fun checkStateIsNotEmpty(): Boolean {
        return if (featureList.featureFlag.IS_CITY_MANDATORY) {
            binding?.state?.text?.isNotEmpty() == true
        } else {
            true
        }
    }

    private fun strokeCheckBoxBgDrawable(
        tphBgColor: Int,
        tphBorderColor: Int,
        strokeWidth: Int,
        cornerRadius: Float
    ): GradientDrawable {
        val drawable = GradientDrawable()
        drawable.shape = GradientDrawable.RECTANGLE
        drawable.cornerRadius = cornerRadius
        drawable.setStroke(strokeWidth, tphBorderColor)
        drawable.setColor(tphBgColor)
        return drawable
    }

    private fun clearEditView() {
        binding?.name?.setText("")
        binding?.email?.setText("")
        binding?.dOB?.text = ""
        binding?.password?.setText("")
        binding?.Gender?.text = ""
        binding?.userName?.setText("")
    }


    private fun hitSignUpApi() {
        showLoading(binding?.progressBar, true)
        preference?.appPrefAccessToken = ""
        viewModel?.hitSignUpAPI(
            binding?.name?.text.toString(),
            binding?.userName?.text.toString(),
            binding?.email?.text.toString(),
            dateMilliseconds,
            binding?.password?.text.toString(),
            imageBitmap.toString(),
            true,
            "",
            binding?.Gender?.text.toString().uppercase(),
            imageUrlId
        )?.observe(this) {
            if (it != null) {
                AnalyticsUtils.logEvent(this, AppConstants.SIGN_UP_SUCCESS)
                Log.d("SIgnupResponse", Gson().toJson(it))
                try {
                    if (it.responseModel.responseCode == 200) {
                        val gson = Gson()
                        KsPreferenceKeys.getInstance().appPrefAccessToken = it.accessToken
                        val signUpData: Data = it.responseModel.data
                        val stringJson = gson.toJson(signUpData)
                        /*if (signUpData.profilePicURL != null) {
                            setUpdatedProfileImageFullUrl(signUpData.profilePicURL)
                        }*/
                        if (featureList?.featureFlag?.ACTIVE_DEVICE_CALL == true) {
                            callSignupDevice(stringJson)
                        } else {
                            dismissLoading(binding?.progressBar)
                            saveUserDetails(stringJson, true)
                        }
                    } else if (it.responseModel.responseCode == 4901) {
                        dismissLoading(binding?.progressBar)
                        commonDialog(
                            it.debugMessage.toString(),
                            "",
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    } else if (it.responseModel.responseCode == 400) {
                        dismissLoading(binding?.progressBar)
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            it.debugMessage.toString(), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    } else {
                        dismissLoading(binding?.progressBar)
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
                } catch (e: NullPointerException) {
                    Logger.w(e)
                }
            } else {
                dismissLoading(binding?.progressBar)
                commonDialog(
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                        getString(R.string.popup_error)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                        getString(R.string.something_went_wrong)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                        getString(R.string.popup_continue)
                    )
                )
            }
        }
    }

    private fun uniqueUserNameApi(callSignupApi : Boolean){
        val jsonObject = JsonObject()
        jsonObject.addProperty("userName", userName)
        viewModel?.uniqueUserNameApi(jsonObject)?.observe(this) {
            Logger.d("DataUniqueUser", Gson().toJson(it))
            if (it != null) {
                if (it.data?.exists == true) {
                    binding?.progressBar?.hide()
                    binding?.errorUserName?.show()
                    binding?.errorUserName?.text = resources.getString(R.string.error_unique_user_name)
                    binding?.userName?.setBackgroundResource(R.drawable.error_input_background)
//                } else if(binding?.userName?.text.toString().trim().matches(userNameRegex)){
                } else if(it.responseCode == 400){
                    binding?.progressBar?.hide()
                    binding?.errorUserName?.show()
                    binding?.errorUserName?.text = resources.getString(R.string.error_username_cannot_contain_special_char)
                    binding?.userName?.setBackgroundResource(R.drawable.error_input_background)
                }else {
                    if (callSignupApi){
                        hitSignUpApi()
                    }
                    binding?.errorUserName?.hide()
                    binding?.userName?.setBackgroundResource(R.drawable.signup_input_gradient)
                }
            }
        }
    }

    @SuppressLint("SuspiciousIndentation")
    private fun saveUserDetails(stringJson: String?, b: Boolean) {
        val fbLoginData: Data = Gson().fromJson(stringJson, Data::class.java)
        Logger.d("signupResponseIs", fbLoginData.accountId)
        Logger.d("signupResponseIs", fbLoginData.expiryTime)
        val gson = Gson()
        val stringJson = gson.toJson(fbLoginData)
        preference?.setfirstTimeUserForKidsPIn(true)
        preference?.setIsVerified(fbLoginData.isVerified.toString())

        getProfile()
        AnalyticsUtils.trackUserAttributes(this, fbLoginData.id.toString(), fbLoginData.name, fbLoginData.email)

        if (b) {
            preference?.appPrefLoginType = AppConstants.UserLoginType.Manual.toString()
        } else {
            preference?.appPrefLoginType = AppConstants.UserLoginType.FbLogin.toString()
        }
        preference?.appPrefProfile = stringJson
        preference?.appPrefLoginStatus = AppConstants.UserStatus.Login.toString()
        preference?.appPrefUserId = fbLoginData.id.toString()
        preference?.appPrefLoginType = AppConstants.UserStatus.Login.toString()
        Logger.e("loginvalue", preference?.appPrefLoginType.toString())
        preference?.appPrefUserName = fbLoginData.name.toString()
        preference?.appPrefUserEmail = fbLoginData.email.toString()
        preference?.expirytime = java.lang.String.valueOf(fbLoginData.expiryTime)
        ActivityLauncher.getInstance().goToEnterOTP(this@ActivitySignUp, EnterOTPActivity::class.java, "signUp")
        // checkPlansForUser()
    }

    private fun checkPlansForUser() {
        val token: String = KsPreferenceKeys.getInstance().appPrefAccessToken
        GetPlansLayer.getInstance().getEntitlementStatus(
            KsPreferenceKeys.getInstance(), token
        ) { entitlementStatus, apiStatus, _, _,_ ->
            if (apiStatus) {
                if (entitlementStatus) {
                    if (KsPreferenceKeys.getInstance().isVerified.equals("true", ignoreCase = true)) {
                        ActivityLauncher.getInstance().goToPlanScreen(this@ActivitySignUp, ActivitySelectSubscriptionPlan::class.java, "SignUp")
                    } else {
                        ActivityLauncher.getInstance().goToEnterOTP(this, EnterOTPActivity::class.java, "SignUp")
                    }
                } else {
                    ActivityLauncher.getInstance().goToPlanScreen(this@ActivitySignUp, ActivitySelectSubscriptionPlan::class.java, "SignUp")
                }
            } else {
                ActivityLauncher.getInstance().homeScreen(this@ActivitySignUp, HomeActivity::class.java,false,"","",0)
            }
        }
    }

    private fun getProfile() {
        val authToken = KsPreferenceKeys.getInstance().appPrefAccessToken
        viewModel?.hitUserProfile(this@ActivitySignUp,authToken)?.observe(this) { profileData ->
           if (profileData != null){

           }
        }
    }


    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            AnalyticsUtils.trackScreenView(this, AppConstants.REGISTER)
            binding?.loginMainFrame?.visibility = View.VISIBLE
            binding?.toolbar?.logoMain2?.visibility = View.VISIBLE
            binding?.toolbar?.searchIcon?.visibility = View.GONE
            binding?.connection?.noConnectionLayout?.visibility = View.GONE
        } else {
            noConnectionLayout()
        }
    }

    private fun connectionObserver() {
        if (NetworkConnectivity.isOnline(this)) {
            connectionValidation(true)
        } else {
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                    getString(R.string.popup_no_internet_connection_found)
                ),
                "",
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
    }

    private fun noConnectionLayout() {
        binding?.loginMainFrame?.visibility = View.GONE
        binding?.connection?.noConnectionLayout?.visibility = View.VISIBLE
        binding?.connection?.retryTxt?.setOnClickListener { view -> connectionObserver() }
    }

    private val commonTextWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        override fun afterTextChanged(s: Editable?) {
            when {
                binding?.name?.hasFocus() == true -> validateEmptyName()
                binding?.userName?.hasFocus() == true -> validateEmptyUserName() && validateUniqueUserName()
                binding?.dOB?.hasFocus() == true -> validateEmptyDob()
                binding?.Gender?.hasFocus() == true -> validateEmptyGender()
                binding?.email?.hasFocus() == true -> validateEmptyEmail() && validateEmail()
                binding?.password?.hasFocus() == true -> validateEmptyPassword()
            }
        }
    }

    private fun validateEmptyPassword(): Boolean {
        var check = false
        password = binding?.password?.text.toString().trim().matches(regexPass).toString()
        if ((binding?.password?.text.toString().trim().matches(regexPass))) {
            binding?.errorPassword?.hide()
            binding?.password?.setBackgroundResource(R.drawable.signup_input_gradient)
            check = true
        } else if (binding?.password?.text.toString().trim() == "") {
            binding?.errorPassword?.show()
            binding?.password?.setBackgroundResource(R.drawable.error_input_background)
            binding?.errorPassword?.text = resources.getString(R.string.popup_empty_message_Password)
        } else {
            binding?.errorPassword?.show()
            binding?.password?.setBackgroundResource(R.drawable.error_input_background)
            binding?.errorPassword?.text = resources.getString(R.string.popup_pwd_must_be_8_to_16_char)
        }
        return check
    }

    private fun validateEmptyEmail(): Boolean {
        var check = false
        email = binding?.email?.text.toString().trim()
        if (StringUtils.isNullOrEmptyOrZero(email)) {

            binding?.errorEmail?.show()
            binding?.errorEmail?.text = resources.getString(R.string.popup_empty_email_tittle)
            binding?.email?.setBackgroundResource(R.drawable.error_input_background)
        } else {
            check = true
            binding?.errorEmail?.hide()
            binding?.email?.setBackgroundResource(R.drawable.signup_input_gradient)
        }
        return check
    }

    private fun validateEmptyName(): Boolean {
        var check = false
        name = binding?.name?.text.toString().trim()
        if (StringUtils.isNullOrEmptyOrZero(name)) {
            binding?.errorFullName?.show()
            binding?.errorFullName?.text = resources.getString(R.string.empty_name)
            binding?.name?.setBackgroundResource(R.drawable.error_input_background)
        } else {
            check = true
            binding?.errorFullName?.hide()
            binding?.name?.setBackgroundResource(R.drawable.signup_input_gradient)
        }
        return check
    }

    private fun validateEmptyUserName(): Boolean {
        var check = false
        userName = binding?.userName?.text.toString().trim()
        if (StringUtils.isNullOrEmptyOrZero(userName)) {
            binding?.errorUserName?.show()
            binding?.errorUserName?.text = resources.getString(R.string.popup_empty_username)
            binding?.userName?.setBackgroundResource(R.drawable.error_input_background)
        } else {
            check = true
            binding?.errorUserName?.hide()
            binding?.userName?.setBackgroundResource(R.drawable.signup_input_gradient)
        }
        return check
    }

    private fun validateUniqueUserName(): Boolean {
        var check = false
        userName = binding?.userName?.text.toString().trim()
        if (!StringUtils.isNullOrEmptyOrZero(userName)) {
            check = true

        }
        return check
    }

    private fun validateEmptyDob(): Boolean {
        var check = false
        dob = binding?.dOB?.text.toString().trim()
        if (featureList.featureFlag.IS_DOB_MANDATORY) {
            if (!dob.isNullOrEmpty()) {
                binding?.errorDob?.hide()
                binding?.dOB?.setBackgroundResource(R.drawable.signup_input_gradient)
                return true
            } else {
                binding?.errorDob?.show()
                binding?.dOB?.setBackgroundResource(R.drawable.error_input_background)
                binding?.errorDob?.text = resources.getString(R.string.popup_empty_dob_subtitle)
                return false
            }
        } else {
            binding?.errorDob?.hide()
            check = true
        }
        return check
    }

    private fun validateEmptyGender(): Boolean {
        var check = false
        gender = binding?.userName?.text.toString().trim()
        if (!StringUtils.isNullOrEmpty(gender)) {
            binding?.errorGender?.hide()
            binding?.Gender?.setBackgroundResource(R.drawable.signup_input_gradient)
            check = true
        } else {
            binding?.errorGender?.show()
            binding?.Gender?.setBackgroundResource(R.drawable.error_input_background)
            binding?.errorGender?.text = resources.getString(R.string.invalid_email)
        }
        return check
    }

    private fun validateEmail(): Boolean {
        var check = false
        if (binding?.email?.text.toString().trim().matches(regex)) {
            binding?.errorEmail?.hide()
            binding?.email?.setBackgroundResource(R.drawable.signup_input_gradient)
            check = true
        } else {
            binding?.errorEmail?.show()
            binding?.email?.setBackgroundResource(R.drawable.error_input_background)
            binding?.errorEmail?.text = resources.getString(R.string.invalid_email)

        }
        return check
    }

    private fun validateCheckboxChecked(): Boolean {
        var check = false
        if (!ifCheckboxChecked){
            check = false
            showToast(this, resources.getString(R.string.please_accept_signup_term_condition))
        } else{
            check = true
        }
        return check
    }

    private fun setSpannableClickableLinks() {
        val fullText = getString(R.string.signup_term_condition)
        val spannable = SpannableString(fullText)

        val terms = if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.LANGUAGE_ARABIC,true))
            "الشروط والأحكام"
        else
            "Terms & Conditions"

        val privacy = if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.LANGUAGE_ARABIC,true))
            "سياسة الخصوصية"
        else
            "Privacy Policy"

        val termsIndex = fullText.indexOf(terms)
        val privacyIndex = fullText.indexOf(privacy)

        if (termsIndex != -1) {
            spannable.setSpan(object : ClickableSpan() {
                override fun onClick(widget: View) {
                    val url = SDKConfig.getInstance().termCondition_URL
                    startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
                }
                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = true
                    ds.color = Color.WHITE
                }
            }, termsIndex, termsIndex + terms.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            spannable.setSpan(StyleSpan(Typeface.BOLD), termsIndex, termsIndex + terms.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }

        if (privacyIndex != -1) {
            spannable.setSpan(object : ClickableSpan() {
                override fun onClick(widget: View) {
                    val url = SDKConfig.getInstance().privay_Policy_URL
                    startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
                }
                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = true
                    ds.color = Color.WHITE
                }
            }, privacyIndex, privacyIndex + privacy.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        spannable.setSpan(StyleSpan(Typeface.BOLD), privacyIndex, privacyIndex + privacy.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding?.termstext?.text = spannable
        binding?.termstext?.movementMethod = LinkMovementMethod.getInstance()
    }



    private fun commonDialog(title: String, description: String, actionBtn: String) {
//        val fm: FragmentManager = supportFragmentManager
//        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
//        commonDialogFragment.setEditDialogCallBack(this)
//        commonDialogFragment.show(fm, AppConstants.MESSAGE)
        showToast(title, description)
    }

    private fun callSignupDevice(stringJson: String) {
        deviceManagementViewModel?.getLoginDevice(KsPreferenceKeys.getInstance().appPrefAccessToken)
            ?.observe(this) {
                if (it.data != null && it != null) {
                    if (it.responseCode == 2000) {
                        dismissLoading(binding?.progressBar)
                        saveUserDetails(stringJson, true)
                    } else if (it.debugMessage != null) {
                        dismissLoading(binding?.progressBar)
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_username_pwd_does_not_match.toString(),
                                getString(R.string.popup_username_pwd_does_not_match)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    } else {
                        dismissLoading(binding?.progressBar)
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.something_went_wrong)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
                }


            }

    }


    override fun onActionBtnClicked() {
    }

    override fun onCancelBtnClicked() {

    }

    override fun onAppSignInSuccess(jwt_token: String) {
        runOnUiThread {
            try {
                jwtToken = jwt_token
                val token: String = jwt_token
                val preference = KsPreferenceKeys.getInstance()
                preference.appPrefAccessToken = token
                var signInResponse = AppCommonMethod.getDecodedJwt(jwt_token)
                val separated: Array<String> = signInResponse.split("}").toTypedArray()
                var firstIndex = separated[0]
                var lastIndex = separated[1]
                if (lastIndex != null) {
                    saveUserDetails(lastIndex + "}", true)
                    //ActivityLauncher.getInstance().homeslider(this@ActivitySignUp, HomeSliderActivity::class.java)
                    ActivityLauncher.getInstance().goToPlanScreen(
                        this@ActivitySignUp,
                        ActivitySelectSubscriptionPlan::class.java,
                        "SignUp"
                    )
                }

            } catch (e: Exception) {
                Logger.w(e)
            }

        }
    }

    override fun onAppSignInError() {
        runOnUiThread {
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                    getString(R.string.popup_error)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                    getString(R.string.something_went_wrong)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
    }

    @SuppressLint("IntentReset")
    private fun openGallery() {
        val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }
        if (ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED) {
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            intent.type = "image/*"
            startActivityForResult(intent, REQUEST_CODE_PICK_IMAGE)
        } else {
            ActivityCompat.requestPermissions(this, arrayOf(permission), REQUEST_CODE_PERMISSION)
        }
    }

    private fun credentialsProvider() {
        val credentialsProvider = CognitoCachingCredentialsProvider(applicationContext, SDKConfig.IDENTITY_POOL_ID, SDKConfig.REGION)
        setAmazonS3Client(credentialsProvider)
    }
    private fun setAmazonS3Client(credentialsProvider: CognitoCachingCredentialsProvider) {
        val clientConfiguration = ClientConfiguration()
        clientConfiguration.maxErrorRetry = 10
        clientConfiguration.connectionTimeout = 50000 // default is 10 secs
        clientConfiguration.socketTimeout = 50000
        clientConfiguration.maxConnections = 500
        s3 = AmazonS3Client(credentialsProvider, clientConfiguration)
        s3?.setRegion(Region.getRegion(Regions.US_EAST_1))
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_PICK_IMAGE && resultCode == RESULT_OK && data != null) {
            val imageUri: Uri? = data.data
            binding?.profileImage?.setImageURI(imageUri)
            if (imageUri != null) {
                try {
                    val filePathFromURI: String = FileUtil.getFilePathFromImageURI(this, imageUri)
                    val imageFilePath = File(filePathFromURI)
                    showLoading(binding!!.progressBar, true)
                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val compressedFile = FileUtil.compressImageFile(imageFilePath, Constants.COMPRESS_IMAGE_SIZE, Constants.COMPRESS_IMAGE_WIDTH, Constants.COMPRESS_IMAGE_HEIGHT)
                            withContext(Dispatchers.Main) {
                                setFileToUpload(compressedFile)
                            }
                        } catch (e: Exception) {
                            withContext(Dispatchers.Main) {
                                showLoading(binding!!.progressBar, false)
                            }
                            Logger.w(e)
                        }
                    }

                } catch (ex: Exception) {
                    showLoading(binding!!.progressBar, false)
                    Logger.w(ex)
                }
            } else {
                Logger.w("unable to get the image")
            }
        }
    }

    private fun transferObserverListener(transferObserver: TransferObserver) {
        transferObserver.setTransferListener(object : TransferListener {
            override fun onStateChanged(id: Int, state: TransferState) {
                if (state == TransferState.COMPLETED) {
                    dismissLoading(binding!!.progressBar)
                }
            }
            override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {
                Logger.e("error", "Progress - ${bytesCurrent},${bytesTotal}");
            }
            override fun onError(id: Int, ex: java.lang.Exception) {
                Logger.e("error", "error");
            }
        })
    }

    private fun setTransferUtility() {
        transferUtility = TransferUtility.builder().s3Client(s3).context(applicationContext).build()
    }

    private fun getCurrentTimeStamp(): Long {
        return System.currentTimeMillis() / 1000
    }

    private fun setFileToUpload(fileToUpload: File) {
        val imageToUpload = "Thumbnail_" + getCurrentTimeStamp() + "_Android" + ".jpg"
        imageUrlId = AppCommonMethod.getSdkConfigUrl()+ imageToUpload
        via = "Gallery"
        KsPreferenceKeys.getInstance().saveVia(via)
        val transferObserver = transferUtility?.upload(
            SDKConfig.BUCKET_ID, imageToUpload,
            fileToUpload
        )

        if (transferObserver != null) {
            transferObserverListener(transferObserver)
        }
    }

}