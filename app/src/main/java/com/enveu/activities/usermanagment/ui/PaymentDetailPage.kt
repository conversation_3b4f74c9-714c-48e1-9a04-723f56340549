package com.enveu.activities.usermanagment.ui

import android.content.Intent
import android.os.Bundle
import android.util.Base64
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ConsumeParams
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.SkuDetails
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.profile.adapter.AdapterManageSubscription
import com.enveu.activities.purchase.call_back.EntitlementStatus
import com.enveu.activities.purchase.in_app_billing.BillingProcessor
import com.enveu.activities.purchase.in_app_billing.InAppProcessListener
import com.enveu.activities.purchase.in_app_billing.PurchaseType
import com.enveu.activities.purchase.plans_layer.GetPlansLayer
import com.enveu.activities.purchase.purchase_model.PurchaseModel
import com.enveu.activities.purchase.purchase_model.PurchaseResponseModel
import com.enveu.activities.sponsorArtist.ArtistAndSponserActivity
import com.enveu.activities.usermanagment.adapter.AdapterPlanDetailPage
import com.enveu.activities.usermanagment.payment_layer.PaymentCallBack
import com.enveu.activities.usermanagment.payment_layer.PaymentCallsLayer
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.membershipAndPlan.ResponseMembershipAndPlan
import com.enveu.databinding.ActivityPaymentDetailPagePlanBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.jwplayer.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson

class PaymentDetailPage : BaseBindingActivity<ActivityPaymentDetailPagePlanBinding?>(),
    CommonDialogFragment.EditDialogListener,
    InAppProcessListener, AdapterPlanDetailPage.OnPurchaseItemClick , AdapterManageSubscription.OnPurchaseItemClick{
    private val stringsHelper by lazy { StringsHelper }
    lateinit var bp : BillingProcessor
    private var fromTo: Boolean? = false
    private var from: String? = ""
    private var response: ResponseEntitle? = null
    private val colorsHelper by lazy { ColorsHelper }
    private val stringHelper by lazy { StringsHelper }
    private var featureList: FeatureFlagModel? = null

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityPaymentDetailPagePlanBinding {
        return ActivityPaymentDetailPagePlanBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        featureList = AppConfigMethod.parseFeatureFlagList()
        binding?.stringData = stringHelper
        binding?.colorsData = colorsHelper
        binding?.toolbar?.colorsData = colorsHelper
        binding?.toolbar?.stringData = stringHelper
        val bundle = intent.extras
        fromTo = intent.getBooleanExtra("fromWhich",false)
        from = intent.getStringExtra("from")
        response = bundle?.getSerializable("responseEntitle") as ResponseEntitle

        binding?.toolbar!!.backLayout.visibility=View.GONE
        initBilling()
        setClicks()
        binding?.toolbar!!.logoMain2.visibility=View.VISIBLE
        if(from!=null && from!!.equals("settings",ignoreCase = true)) {
            binding?.toolbar!!.titleMid.text=resources.getString(R.string.manage_account)
            binding?.toolbar!!.titleSkip.visibility= View.GONE
            binding?.toolbar!!.llSearchIcon.visibility= View.GONE
            binding?.mainPaymentLayout!!.visibility = View.GONE
            binding?.mainManageSubscriptionLayout!!.visibility = View.VISIBLE
        }else{
            binding?.toolbar!!.titleSkip.visibility= View.VISIBLE
            binding?.toolbar!!.backLayout.visibility= View.VISIBLE
            binding?.toolbar?.backArrow?.let { rotateImageLocaleWise(it) }
            binding?.mainPaymentLayout!!.visibility = View.VISIBLE
            binding?.mainManageSubscriptionLayout!!.visibility = View.GONE
            binding?.toolbar!!.titleSkip.visibility=View.GONE
            binding?.toolbar!!.llSearchIcon.visibility= View.GONE
        }

        binding?.btnOk?.setOnClickListener {
            ActivityLauncher.getInstance().homeScreen(this@PaymentDetailPage, HomeActivity::class.java,false,"","",0)
            finish()
        }
        binding?.btnOkComplete?.setOnClickListener {
            ActivityLauncher.getInstance().homeScreen(this@PaymentDetailPage, HomeActivity::class.java,false,"","",0)
            finish()
        }
       // adapter = AdapterPlanDetailPage(listOfAllItems, this, this)
       // binding?.planRecycleView?.layoutManager = LinearLayoutManager(this)

    }

    private fun initBilling() {
        binding?.progressBar?.visibility= View.VISIBLE
        window.setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE, WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
        bp =
            BillingProcessor(
                this@PaymentDetailPage,
                this
            )
        bp.initializeBillingProcessor()
        val serviceIntent = Intent("com.android.vending.billing.InAppBillingService.BIND")
        serviceIntent.setPackage("com.android.vending")
    }

    var orderID: String? = null
    private fun setClicks() {
        /*binding?.btnBuy?.setOnClickListener {
            ActivityLauncher.getInstance().goToEnterOTP(this@ActivitySelectSubscriptionPlan, EnterOTPActivity::class.java)
        }*/
        binding?.toolbar?.backLayout?.setOnClickListener {
            checkBackCondition()
        }
        binding?.toolbar?.titleSkip?.setOnClickListener{
            ActivityLauncher.getInstance().homeScreen(this, HomeActivity::class.java,false,"","",0)
        }
        binding?.toolbar?.backLayout?.setOnClickListener { checkBackCondition() }
        binding?.btnBuy?.setOnClickListener {
            if (featureList?.featureFlag?.IS_SPONSOR==true) {
                if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()) {
                    if (!ArtistAndSponserActivity.AppState.isSkipped) {
                        ActivityLauncher.getInstance().artistAndSponserActivity(this, ArtistAndSponserActivity::class.java)
                    } else {
                        initiatePayment()
                        ArtistAndSponserActivity.AppState.isSkipped = false
                    }
                } else {
                    ArtistAndSponserActivity.AppState.isSkipped = false
                    initiatePayment()
                }
            }else{
                initiatePayment()
            }
        }
    binding?.planRecycleView?.layoutManager  = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)

    }

private fun initiatePayment() {
    clickedPlan?.let {
        Log.w("itemClick", clickedPlan!!.identifier.toString() + "")
        val token = KsPreferenceKeys.getInstance().appPrefAccessToken
        if (token != null && !token.equals("", ignoreCase = true)) {
            binding?.progressBar?.visibility=View.VISIBLE
            window.setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE, WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
            PaymentCallsLayer.getInstance().createOrder(token, clickedPlan, object :
                    PaymentCallBack {
                    override
                    fun createOrderResponse(
                        response: PurchaseResponseModel,
                        status: Boolean
                    ) {
                        if (status) {
                            orderID = response.data.orderId
                            KsPreferenceKeys.getInstance().paymentorderid = orderID
                            Log.w("orderIdOf", orderID.toString())
                            callInitiateOrder(response)
                        } else {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_payment_error.toString(),
                                    getString(R.string.popup_payment_error)
                                )  + " " + SUPPORT,
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                                    getString(R.string.popup_ok))
                            )
                        }
                    }
                })
        }
    }?: run{
        Log.w("cardSelectiom","no card selected")
    }
}


    var paymentId: String? = null
    private fun callInitiateOrder(response: PurchaseResponseModel) {
        val token = KsPreferenceKeys.getInstance().appPrefAccessToken
        PaymentCallsLayer.getInstance()
            .callInitiatePayment(token, response.data.orderId, object :
                PaymentCallBack {
                override fun initiateOrderResponse(
                    response: PurchaseResponseModel,
                    status: Boolean
                ) {
                    if (status) {
                        paymentId = response.data.paymentId.toString()
                        buySubscription(paymentId)
                        Log.w("orderIdOf", paymentId.toString())
                    } else {
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_payment_error.toString(),
                                getString(R.string.popup_payment_error)
                            )  + " " + SUPPORT,
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                                getString(R.string.popup_ok))
                        )
                    }
                }
            })
    }

    private fun buySubscription(paymentId: String?) {
        if (paymentId != null && !paymentId.equals("", ignoreCase = true)) {
            binding!!.progressBar.visibility = View.GONE
            window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
            bp.purchase(
                this@PaymentDetailPage,
                clickedPlan!!.customIdentifier,
                "DEVELOPER PAYLOAD",
                PurchaseType.SUBSCRIPTION.name
            )
        }
    }


    private fun commonDialog(title: String, description: String, actionBtn: String) {
        try {
            val fm: FragmentManager = supportFragmentManager
            val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
            commonDialogFragment.setEditDialogCallBack(this)
            commonDialogFragment.show(fm, AppConstants.MESSAGE)
        }catch (e :Exception){
            e.printStackTrace()
        }

    }

    override fun onActionBtnClicked() {
        ActivityLauncher.getInstance().homeScreen(this, HomeActivity::class.java,false,"","",0)
        finish()
    }

    override fun onCancelBtnClicked() {

    }

    override fun onDestroy() {
        super.onDestroy()
        // binding!!.planRecycleView.adapter =  AdapterPlan(imageArray,itemClickListener)
    }

    override fun onBillingInitialized() {
        try {
            <EMAIL> {
                callGetPlansApi()
            }
        }catch (e:Exception){
            Log.w("plansError", e.toString() + "")
        }

    }

    private val SUPPORT = "<EMAIL>"
    private val BILLING_RESULT = "BillingResult"
    private val PURCHASED_SKU = "purchasedSKU"
    override fun onPurchasesUpdated(
        billingResult: BillingResult,
        purchases: MutableList<Purchase>?) {
        binding?.progressBar?.visibility=View.GONE
        window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        val gson = Gson()
        val json = gson.toJson(billingResult)
        Log.w(BILLING_RESULT, json)
        Log.w(
            BILLING_RESULT,
            "new line"
        )
        val json2 = gson.toJson(purchases)
        Log.w(BILLING_RESULT, json2)

        binding!!.progressBar.visibility = View.VISIBLE
        window.setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE, WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)

        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && purchases != null) {
            if (purchases[0].purchaseToken != null) {
                processPurchase(purchases)
            } else {
                Log.d("checkPaymentStatus", "1")
                updatePayment("FAILED", "inapp:com.enveu.demo:android.test.purchased", paymentId.toString())
            }
        }
    }

    private var purchasedSKU = ""
    private fun handlePurchase(purchase: Purchase) {
        try {
            Log.w(PURCHASED_SKU, purchase.skus[0])
            purchasedSKU = Base64.encodeToString(purchase.skus[0].toByteArray(), Base64.NO_WRAP)
            Log.w(PURCHASED_SKU, purchasedSKU)
            Log.w(PURCHASED_SKU, purchase.purchaseToken)
        } catch (e: java.lang.Exception) {
            Logger.e(e)
        }
        try {
            bp.rentalOfferConsuming(purchase)
            Log.d("checkPaymentStatus", "2")
            updatePayment("PAYMENT_DONE", purchase.purchaseToken, paymentId.toString())
        } catch (e: java.lang.Exception) {
            Logger.e(e)
        }
    }

    private fun updatePayment(paymentStatus: String, purchaseToken: String, paymentId: String?) {
        Log.d("checkPaymentStatusApi", paymentStatus)
        Log.w("itemClick", clickedPlan!!.identifier.toString() + "")
        val token = KsPreferenceKeys.getInstance().appPrefAccessToken
        if (token != null && !token.equals("", ignoreCase = true)) {
            if (paymentId != null && orderID != null) {
                PaymentCallsLayer.getInstance().updatePurchase(
                    "",
                    paymentStatus,
                    token,
                    purchaseToken,
                    paymentId,
                    orderID,
                    clickedPlan,
                    purchasedSKU,
                    object :
                        PaymentCallBack {
                        override fun updateOrderResponse(
                            response: PurchaseResponseModel,
                            status: Boolean
                        ) {
                            binding!!.progressBar.visibility = View.GONE
                            window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
                            dismissLoading(binding!!.progressBar)
                            if (status) {
                                if (response.data.orderStatus != null) {
                                    if (response.data.orderStatus.equals("COMPLETED", ignoreCase = true)) {
                                        binding?.transactionCompleteLayout?.visibility = View.VISIBLE
                                    } else {
                                        binding?.transactionCompleteLayout?.visibility = View.GONE
                                        binding?.transactionFailedLayout?.visibility = View.VISIBLE
                                    }
                                }
                            } else {
                                binding?.transactionFailedLayout?.visibility = View.VISIBLE
                            }
                    } })
            }
        }
    }

    private fun processPurchase(purchases: List<Purchase>) {
        try {
            for (purchase in purchases) {
                Log.w(BILLING_RESULT,
                    "new line"
                )
                val gson = Gson()
                val json = gson.toJson(purchase)
                Log.w(BILLING_RESULT,
                    json
                )
                if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
                    handlePurchase(purchase)
                } else if (purchase.purchaseState == Purchase.PurchaseState.PENDING) {
                }
            }
        } catch (ignored: Exception) {
        }
    }

    private var hasFetchedSKUs = false
    private lateinit var purchaseFinalList: List<PurchaseModel>
    var adapter : AdapterPlanDetailPage? = null
    var manageAdapter : AdapterManageSubscription? = null
    override fun onListOfSKUFetched(purchases: MutableList<SkuDetails>?) {
        if (hasFetchedSKUs) {
            return
        }
        hasFetchedSKUs = true
        purchaseFinalList = java.util.ArrayList()
        assert(purchases != null)
        Log.w("onListOfSKUFetched", purchases!!.size.toString() + "")
        runOnUiThread {
            binding?.progressBar?.visibility= View.GONE
            window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
            if(from!=null && from?.equals("settings",ignoreCase = true) == true){
                purchaseFinalList = AppCommonMethod.createManagePurchaseList(purchaseModel, purchases,
                    purchaseFinalList as java.util.ArrayList<PurchaseModel?>,bp
                ) as List<PurchaseModel>
            }else{
                purchaseFinalList = AppCommonMethod.createPurchaseList(purchaseModel, purchases,
                    purchaseFinalList as java.util.ArrayList<PurchaseModel?>,bp
                ) as List<PurchaseModel>
            }

            if(from!=null && from!!.equals("settings",ignoreCase = true)){
                binding?.mainPaymentLayout!!.visibility= View.GONE;
                binding?.mainManageSubscriptionLayout!!.visibility= View.VISIBLE;
                manageAdapter= AdapterManageSubscription(
                        purchaseFinalList,
                        this@PaymentDetailPage,
                        this@PaymentDetailPage
                    )
                binding?.subscriptionRecycle?.adapter =  manageAdapter
            }else{
                if (!purchaseFinalList.isNullOrEmpty()) {
                    binding?.mainPaymentLayout?.visibility = View.VISIBLE
                    binding?.mainManageSubscriptionLayout?.visibility = View.GONE
                    clickedPlan = purchaseFinalList[0]
                    Log.d("checkPurchaseFinalList", Gson().toJson(purchaseFinalList))
                    adapter = AdapterPlanDetailPage(
                        purchaseFinalList,
                        this@PaymentDetailPage,
                        this@PaymentDetailPage
                    )
                    binding?.planRecycleView?.adapter = adapter
                }
            }
        }
    }

    override fun onBillingError(error: BillingResult?) {
        Log.d("checkPurchaseFinalList", Gson().toJson(purchaseFinalList))
    }

    var subSkuList: MutableList<String>? = null
    var productSkuList: MutableList<String>? = null
    var purchaseModel: List<PurchaseModel>? = null
    private fun callGetPlansApi() {
        subSkuList = java.util.ArrayList<String>()
        productSkuList = java.util.ArrayList<String>()
        val token: String = KsPreferenceKeys.getInstance().appPrefAccessToken
        if (response!=null && response!!.data!=null){
            if(response!!.data.purchaseAs!=null && response!!.data.purchaseAs.size>0){
                purchaseModel = AppCommonMethod.fetchEntitleRecSubscriptionModel(response!!, subSkuList as java.util.ArrayList<String>, productSkuList as java.util.ArrayList<String>)
                if (purchaseModel?.isNotEmpty() == true) {
                   bp.getAllSkuDetails(purchaseModel?.get(0)?.subscriptionList, purchaseModel?.get(0)?.productList)
                }
            }else{
                binding!!.progressBar.visibility = View.GONE
                window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
                binding!!.planRecycleView.visibility = View.GONE
                binding!!.bottomLay.visibility = View.INVISIBLE
                binding!!.noPlans.visibility=View.VISIBLE
            }

        }else{
            GetPlansLayer.getInstance().getPlansDetail(token, object : EntitlementStatus {
                override fun entitlementStatus(entitlementStatus: Boolean, apiStatus: Boolean,offerStatus:String, onHold : Boolean,responseCode:Int) {

                }
                override fun getPlans(plans: ResponseMembershipAndPlan?, apiStatus: Boolean) {
                    if (apiStatus) {
                        purchaseModel = AppCommonMethod.fetchRecSubscriptionModel("",plans!!, subSkuList as java.util.ArrayList<String>, productSkuList as java.util.ArrayList<String>)
                        if (purchaseModel!!.isNotEmpty()) {
                            bp.getAllSkuDetails(
                                purchaseModel!![0].subscriptionList,
                                purchaseModel!![0].productList
                            )
                        } else{
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_payment_error.toString(),
                                    getString(R.string.popup_payment_error)
                                ) + " " + SUPPORT,
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                                    getString(R.string.popup_ok))
                            )
                        }
                    } else{
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_payment_error.toString(),
                                getString(R.string.popup_payment_error)
                            ) + " " + SUPPORT,
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                                getString(R.string.popup_ok))
                        )
                    }

                }
            })

        }
    }

    /*override fun onBackPressed() {
        checkBackCondition()
        super.onBackPressed()
    }*/

    private fun checkBackCondition() {
        finish()
    }
    var clickedPlan: PurchaseModel? = null

    override fun onPurchaseCardClick(click: Boolean, planName: PurchaseModel?) {
        runOnUiThread {
            if (adapter != null) {
                clickedPlan = planName
                AppCommonMethod.activeBtn(binding,getColor(R.color.main_btn_txt_color));
                adapter?.notifyDataSetChanged()
            }
        }    }

    override fun setDescription(description: String?, planName: PurchaseModel?) {
        clickedPlan = planName
    }

    override fun onCancelSubscription(i : Int, provider : String) {
        when (i) {
            1 -> {
                commonDialog(
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.grace_period_alert.toString(),
                        getString(R.string.grace_period_alert)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.grace_period_alert_desc.toString(),
                        getString(R.string.grace_period_alert_desc)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                        getString(R.string.popup_ok)
                    )
                )
            }

            2 -> {
                commonDialog(
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.hold_period_alert.toString(),
                        getString(R.string.hold_period_alert)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.hold_period_alert_desc.toString(),
                        getString(R.string.hold_period_alert_desc)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                        getString(R.string.popup_ok)
                    )
                )
            }

            3 -> {
                commonDialog(
                    "",
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.this_subscibtion_was_activated_by_bussiness.toString(),
                        getString(R.string.this_subscibtion_was_activated_by_bussiness)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                        getString(R.string.popup_ok)
                    )
                )
            }

            4 -> {
                commonDialog(
                    "",
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.this_subscibtion_web.toString(),
                        getString(R.string.this_subscibtion_web)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                        getString(R.string.popup_ok)
                    )
                )
            }

            5 -> {
                commonDialog(
                    "",
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.this_subscibtion_ios.toString(),
                        getString(R.string.this_subscibtion_ios)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                        getString(R.string.popup_ok)
                    )
                )
            }
        }
    }
}