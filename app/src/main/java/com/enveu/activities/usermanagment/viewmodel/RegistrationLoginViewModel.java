package com.enveu.activities.usermanagment.viewmodel;

import android.app.Application;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.enveu.beanModel.responseModels.sharing.SharingModel;
import com.enveu.beanModelV3.mutli_profile_response.AddProfileRequestBody;
import com.enveu.beanModelV3.mutli_profile_response.AddSecondaryProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.GenerateOtpResponse;
import com.enveu.beanModelV3.mutli_profile_response.GetAllProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.ProfileSwitchResponse;
import com.enveu.beanModelV3.mutli_profile_response.RemoveSecondaryProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.SecondaryProfileData;
import com.enveu.beanModelV3.mutli_profile_response.UpdateSecondaryProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.ValidateOtpResponse;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.bean_model_v2_0.geo.GeoResponse;
import com.enveu.client.api_callback.NetworkResultCallback;
import com.enveu.client.deleteAccount.DeleteAccountResponse;
import com.enveu.client.joinContest.joinContestResponse.Response;
import com.enveu.client.networkRequestManager.RequestManager;
import com.enveu.networking.response.UniqueUserNameResponse;
import com.enveu.networking.response.UserInterestResponse;
import com.enveu.utils.config.LanguageLayer;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.enveu.activities.profile.activate_device.ActivateTVDeviceModel;
import com.enveu.activities.usermanagment.model.OtpResponse;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModel.forgotPassword.CommonResponse;
import com.enveu.beanModel.responseModels.LoginResponse.LoginResponseModel;
import com.enveu.beanModel.responseModels.SignUp.SignupResponseAccessToken;
import com.enveu.beanModel.responseModels.listAllAccounts.AllSecondaryAccountDetails;
import com.enveu.beanModel.responseModels.secondaryUserDetails.SecondaryUserDetailsJavaPojo;
import com.enveu.beanModel.userProfile.UserProfileResponse;
import com.enveu.bean_model_v2_0.authResponse.AuthResponse;
import com.enveu.repository.userManagement.RegistrationLoginRepository;

import java.util.HashSet;
import java.util.List;
import java.util.Set;


public class RegistrationLoginViewModel extends AndroidViewModel {

    final RegistrationLoginRepository loginRepository;

    public MutableLiveData<Set<String>> selectedIds = new MutableLiveData<>(new HashSet<>());

    public RegistrationLoginViewModel(@NonNull Application application) {
        super(application);
        loginRepository = RegistrationLoginRepository.getInstance();
    }

    public LiveData<LoginResponseModel> hitLoginAPI( String userName, String pwd) {
        return loginRepository.getLoginAPIResponse(userName, pwd);
    }

    public LiveData<ActivateTVDeviceModel> getTvVerify(JsonObject activateCode, String token) {
        return loginRepository.getLoginTV(token, activateCode);
    }
    public LiveData<AuthResponse> getAuthResponse() {
        return loginRepository.getAuthResponse();
    }
    public LiveData<AllSecondaryAccountDetails> hitAllSecondaryApi(Context context, String token) {
        return loginRepository.getSecondaryAPIResponse(context,token);
    }

    public LiveData<SecondaryUserDetailsJavaPojo> hitSecondaryUser(String token, String userName) {
        return loginRepository.getSecondaryUserAPIReponse(token,userName);
    }

    public LiveData<SignupResponseAccessToken> hitSignUpAPI(String name, String userName,String email, String dob, String pwd, String profilePicURL, boolean isNotificationEnable, String phoneNumber,String gender, String imageUrl) {
        return loginRepository.getSignupAPIResponse(name, userName,email,dob,pwd,profilePicURL,isNotificationEnable,phoneNumber,gender, imageUrl);
    }

    public LiveData<SharingModel> hitSharingApi(Context context, EnveuVideoItemBean enveuVideoItemBean) {
        return loginRepository.getDynamicLinkFromUserModel(context,enveuVideoItemBean);
    }
    public LiveData<CommonResponse> hitForgotPasswordApi(String email) {
        return loginRepository.getForgotPasswordAPIResponse(email);
    }

    public LiveData<LoginResponseModel> hitApiChangePwd(String email, String token, Context context) {
        return loginRepository.getChangePwdAPIResponse(email, token,context);
    }
    public LiveData<LoginResponseModel> hitFbLogin(Context context, String email, String fbToken, String name, String fbId,String pic, boolean isEmail) {
        return loginRepository.getFbLogin(context, email, fbToken, name, fbId,pic, isEmail);
    }

    public LiveData<LoginResponseModel> hitApiForceFbLogin(Context context, String email, String fbToken, String name, String fbId, String profilePic, boolean isEmail) {
        return loginRepository.getForceFbLogin(context, email, fbToken, name, fbId, profilePic, isEmail);
    }

    public LiveData<UserProfileResponse> hitUserProfile(Context context, String token) {
        return loginRepository.getUserProfile(context,token);
    }

    public LiveData<UserProfileResponse> hitUpdateProfile(Context context, String token, String name, String mobile, String spinnerValue, String dob, String address, String imageUrl, String via, String contentPreference, boolean isNotificationEnable, String parentalPin, String city, String country, String profile, String species, String type, String lastName, boolean parentalPinEnabled, List<Integer> appUserInterest, String selectedSponserId) {
        return loginRepository.getUpdateProfile(context,token,name,mobile,spinnerValue,dob,address,imageUrl,via,contentPreference,isNotificationEnable,parentalPin,city,country,profile,species,type, lastName, parentalPinEnabled, appUserInterest,selectedSponserId);
    }

    public LiveData<UserProfileResponse> hitUpdateProfileApi(Context context, String token, JsonObject jsonObject) {
        return loginRepository.getUpdateProfile(context,token, jsonObject);
    }

    public LiveData<UserProfileResponse> updateSponsor(String token, List<Integer> appUserInterest,String selectedSponserId,String name,String lastName,String countryCode,String mobileNumber,String dob,String country,String city,String gender) {
        return loginRepository.updateSponsor(token,appUserInterest,selectedSponserId,name,lastName,countryCode,mobileNumber, dob,country, city,gender);
    }

    public LiveData<RailCommonData> getEpgListing(Context context, String channelId, Long startDate , Long endDate, int pageNumber, int pageSize, String languageCode) {
        return loginRepository.getEpgListing(context,channelId,startDate,endDate,pageNumber,pageSize,languageCode);
    }

    public LiveData<OtpResponse> generateOTPCode(String token) {
        return loginRepository.getGenerateOTPResponse(token);

    }
    public LiveData<OtpResponse> otpVerify(int otp, String token) {
        return loginRepository.getInstance().getOTPVerify(otp, token);
    }
    public LiveData<GeoResponse> getGeoInform() {
        return loginRepository.getGeoInfo();
    }

    public LiveData<DeleteAccountResponse> deleteAccount(String token) {
        return loginRepository.getInstance().deleteAccountReq(token);
    }

    public LiveData<Response> checkUserContestApi(String token, int userId, int contestId, String languageCode) {
        return loginRepository.getInstance().checkUserContestApi(token,userId,contestId,languageCode);
    }

    public LiveData<Response> joinContestForUSer(String token, int userId, int contestId, String languageCode) {
        return loginRepository.joinContestForUser(token,userId,contestId,languageCode);
    }

    public LiveData<AddSecondaryProfileResponse> addSecondaryProfile(AddProfileRequestBody addProfileRequestBody) {
        return loginRepository.addSecondaryProfile(addProfileRequestBody);
    }

    public LiveData<GetAllProfileResponse> getAllSecondaryProfileList() {
        return loginRepository.getAllSecondaryProfileList();
    }

    public LiveData<UpdateSecondaryProfileResponse> updateSecondaryProfile(SecondaryProfileData secondaryProfileData) {
        return loginRepository.updateSecondaryProfile(secondaryProfileData);
    }

    public LiveData<RemoveSecondaryProfileResponse> deleteSecondaryProfile(String accountId) {
        return loginRepository.deleteSecondaryProfile(accountId);
    }

    public LiveData<ProfileSwitchResponse> switchAnotherProfile(String authToken, SecondaryProfileData secondaryProfileData) {
        return loginRepository.switchAnotherProfile(authToken, secondaryProfileData);
    }

    public LiveData<UserProfileResponse> updateParentProfile(SecondaryProfileData secondaryProfileData, String authToken) {
        return loginRepository.updateParentProfile(secondaryProfileData, authToken);
    }

    public LiveData<GenerateOtpResponse> generateOtpParentalPin(String userEmail) {
        return loginRepository.generateOtpParentalPin(userEmail);
    }

    public LiveData<ValidateOtpResponse> validateParentalLockOtp(String token, String otpText) {
        return loginRepository.validateParentalLockOtp(token, otpText);
    }

    public LiveData<String> switchMainProfile(String accountId) {
        return loginRepository.switchMainProfile(accountId);
    }

    public LiveData<String> inValidOtpData(){
        return loginRepository.inValidOtpData();
    }

    public MutableLiveData<UniqueUserNameResponse> uniqueUserNameApi(JsonObject jsonObject) {
        MutableLiveData<UniqueUserNameResponse> uniqueUserNameResponse = new MutableLiveData<>();
        loginRepository.uniqueUserNameApi(jsonObject, new NetworkResultCallback<JsonObject>() {
            @Override
            public void loading(boolean isLoading) {}

            @Override
            public void success(@Nullable Boolean status, @Nullable retrofit2.Response<JsonObject> response) {
                assert response != null;
                UniqueUserNameResponse responseModel = new Gson().fromJson(response.body(), UniqueUserNameResponse.class);
                uniqueUserNameResponse.setValue(responseModel);
            }

            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                UniqueUserNameResponse errorBlock = new UniqueUserNameResponse();
                errorBlock.setResponseCode(errorCode);
                uniqueUserNameResponse.setValue(errorBlock);
            }
        });
        return uniqueUserNameResponse;
    }


    public MutableLiveData<UserInterestResponse> getUserInterestList(String customType, int pageNumber, int pageSize) {
        MutableLiveData<UserInterestResponse> liveData = new MutableLiveData<>();
        RequestManager.Companion.getInstance().getUserInterestList(customType, pageNumber, pageSize, LanguageLayer.INSTANCE.getCurrentLanguageCode(), new NetworkResultCallback<JsonObject>() {
            @Override
            public void loading(boolean isLoading) {}
            @Override
            public void success(@Nullable Boolean status, @Nullable retrofit2.Response<JsonObject> response) {
                if (response != null && response.body() != null) {
                    UserInterestResponse responseModel = new Gson().fromJson(response.body(), UserInterestResponse.class);
                    liveData.setValue(responseModel);
                } else {
                    UserInterestResponse errorResponse = new UserInterestResponse();
                    errorResponse.setResponseCode(response != null ? response.code():0);
                    liveData.setValue(errorResponse);
                }
            }
            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                UserInterestResponse errorResponse = new UserInterestResponse();
                errorResponse.setResponseCode(errorCode);
                liveData.setValue(errorResponse);
            }
        });
        return liveData;
    }
}
