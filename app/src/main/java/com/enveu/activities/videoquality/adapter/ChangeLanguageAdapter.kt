package com.enveu.activities.videoquality.adapter

import android.annotation.SuppressLint
import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.res.ResourcesCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.videoquality.bean.LanguageItem
import com.enveu.activities.videoquality.callBack.ItemClick
import com.enveu.databinding.VideoQualityItemBinding
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import java.util.Objects


class ChangeLanguageAdapter(private val languageList: ArrayList<LanguageItem>?, private val itemClickListener: ItemClick) : RecyclerView.Adapter<ChangeLanguageAdapter.SingleItemRowHolder>() {
    
    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): SingleItemRowHolder {
        val itemBinding = DataBindingUtil.inflate<VideoQualityItemBinding>(LayoutInflater.from(viewGroup.context), R.layout.video_quality_item, viewGroup, false)
        itemBinding.colorsData = ColorsHelper
        itemBinding.stringData = StringsHelper
        return SingleItemRowHolder(itemBinding)
    }


    @SuppressLint("NotifyDataSetChanged")
    override fun onBindViewHolder(viewHolder: SingleItemRowHolder, @SuppressLint("RecyclerView") position: Int) {
       val languageItem = languageList?.get(position)
        if (languageItem?.languageId == languageItem?.defaultLanguageName) {
            viewHolder.notificationItemBinding.checkedState.visibility = View.VISIBLE
            viewHolder.notificationItemBinding.titleText.typeface = ResourcesCompat.getFont(viewHolder.notificationItemBinding.titleText.context, R.font.poppins_bold)
        } else {
            viewHolder.notificationItemBinding.checkedState.visibility = View.GONE
            viewHolder.notificationItemBinding.titleText.typeface = ResourcesCompat.getFont(viewHolder.notificationItemBinding.titleText.context, R.font.poppins_regular)
        }

        viewHolder.notificationItemBinding.titleText.text = languageItem?.languageName

        viewHolder.notificationItemBinding.parentLayout.setOnClickListener {
            itemClickListener.itemClicked(languageItem?.languageId)

        }
    }



    override fun getItemCount(): Int {
        return languageList?.size?:0
    }

    inner class SingleItemRowHolder internal constructor(val notificationItemBinding: VideoQualityItemBinding) : RecyclerView.ViewHolder(notificationItemBinding.root)
}
