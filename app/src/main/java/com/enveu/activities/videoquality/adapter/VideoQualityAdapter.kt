package com.enveu.activities.videoquality.adapter

import android.annotation.SuppressLint
import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.res.ResourcesCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.enveu.OttApplication
import com.enveu.R
import com.enveu.activities.videoquality.bean.TrackItem
import com.enveu.activities.videoquality.callBack.NotificationItemClickListner
import com.enveu.databinding.VideoQualityItemBinding
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper

class VideoQualityAdapter(activity: Activity?, private val inboxMessages: List<TrackItem>?, private val itemClickListener: NotificationItemClickListner) :
    RecyclerView.Adapter<VideoQualityAdapter.SingleItemRowHolder>() {
    private var pos: Int

    init {
        pos = KsPreferenceKeys.getInstance().qualityPosition
        if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.LANGUAGE_ARABIC, ignoreCase = true)) {
            AppCommonMethod.updateLanguage(AppConstants.LANGUAGE_ARABIC, OttApplication.instance!!)
        } else if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.ENGLISH_LAN_CODE, ignoreCase = true)) {
            AppCommonMethod.updateLanguage(AppConstants.ENGLISH_LAN_CODE, OttApplication.instance!!)
        }
    }

    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): SingleItemRowHolder {
        val itemBinding = DataBindingUtil.inflate<VideoQualityItemBinding>(
            LayoutInflater.from(viewGroup.context),
            R.layout.video_quality_item, viewGroup, false
        )
        //      ThemeHandler.getInstance().applyChangeLangeAdapter(viewGroup.getContext(),itemBinding);
        itemBinding.colorsData = ColorsHelper
        itemBinding.stringData = StringsHelper
        return SingleItemRowHolder(itemBinding)
    }

    override fun onBindViewHolder(viewHolder: SingleItemRowHolder, @SuppressLint("RecyclerView") position: Int) {
        if (pos == position) {
            viewHolder.notificationItemBinding.checkedState.visibility = View.VISIBLE
            viewHolder.notificationItemBinding.titleText.typeface = ResourcesCompat.getFont(
                viewHolder.notificationItemBinding.titleText.context, R.font.poppins_bold)
        } else {
            viewHolder.notificationItemBinding.checkedState.visibility = View.GONE
            viewHolder.notificationItemBinding.titleText.typeface = ResourcesCompat.getFont(
                viewHolder.notificationItemBinding.titleText.context, R.font.poppins_regular)
        }
        viewHolder.notificationItemBinding.titleText.text = inboxMessages?.get(position)?.trackName
        //  viewHolder.notificationItemBinding.secondTitleText.setText(inboxMessages.get(position).getDescription());
        viewHolder.notificationItemBinding.parentLayout.setOnClickListener { view: View? ->
            pos = position
            KsPreferenceKeys.getInstance().qualityPosition = pos
            KsPreferenceKeys.getInstance().qualityName = inboxMessages?.get(position)?.uniqueId
            itemClickListener.onClick("", "")
        }
    }

    override fun getItemCount(): Int {
        return inboxMessages?.size!!
    }

    inner class SingleItemRowHolder internal constructor(val notificationItemBinding: VideoQualityItemBinding) : RecyclerView.ViewHolder(
        notificationItemBinding.root
    )
}