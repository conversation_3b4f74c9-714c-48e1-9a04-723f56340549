package com.enveu.activities.videoquality.ui

import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.enveu.R


import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.videoquality.adapter.ChangeLanguageAdapter
import com.enveu.activities.videoquality.bean.LanguageItem
import com.enveu.activities.videoquality.callBack.ItemClick
import com.enveu.activities.videoquality.viewModel.VideoQualityViewModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.databinding.VideoQualityActivityBinding

import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstanceWithCancelBtn
import com.enveu.menuManager.viewmodel.MenuViewModel
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.config.LanguageLayer
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.constants.AppConstants.ENGLISH_LAN_CODE
import com.enveu.utils.constants.AppConstants.LANGUAGE_ARABIC
import com.enveu.utils.constants.AppConstants.LANGUAGE_ARABIC_CODE
import com.enveu.utils.constants.AppConstants.LANGUAGE_ENGLISH_CODE
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.SharedPrefHelper
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper

class ChangeLanguageActivity : BaseBindingActivity<VideoQualityActivityBinding?>(),
    ItemClick, CommonDialogFragment.EditDialogListener {
    private var viewModel: VideoQualityViewModel? = null
    private var notificationAdapter: ChangeLanguageAdapter? = null
    private var menuViewModel:MenuViewModel? = null
    var click = false
    private var lanName: String? = null
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    private var arrayList: ArrayList<LanguageItem>? = ArrayList()
    override fun inflateBindingLayout(inflater: LayoutInflater): VideoQualityActivityBinding {
        return VideoQualityActivityBinding.inflate(inflater)
    }

    private var preference: KsPreferenceKeys? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        menuViewModel = ViewModelProvider(this)[MenuViewModel::class.java]

        preference = KsPreferenceKeys.getInstance()
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
        binding!!.colorsData = colorsHelper
        binding!!.stringData = stringsHelper
        binding!!.toolbar.colorsData = colorsHelper
        binding!!.toolbar.stringData = stringsHelper
        val config = Configuration(resources.configuration)
        Logger.d("Locale: " + config.locale.displayLanguage)
        callModel()
        connectionObserver()
        setToolBar()
    }

    private fun setToolBar() {
        binding!!.toolbar.logoMain2.visibility = View.GONE
        binding!!.toolbar.backLayout.visibility = View.VISIBLE
        binding?.toolbar?.backArrow?.let { rotateImageLocaleWise(it) }
        binding!!.toolbar.searchIcon.visibility = View.GONE
        binding!!.toolbar.titleText.visibility = View.VISIBLE
        val changeLangTile = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.lang_change_lang.toString(),
            getString(R.string.lang_change_lang)
        )
        binding!!.toolbar.screenText.text = changeLangTile
        binding!!.toolbar.screenText.setBackgroundResource(0)
        binding!!.toolbar.backLayout.setOnClickListener { onBackPressed() }
    }

    private fun callModel() {
        viewModel = ViewModelProvider(this)[VideoQualityViewModel::class.java]
    }

    private fun connectionObserver() {
        connectionValidation(NetworkConnectivity.isOnline(this))
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            binding!!.noConnectionLayout.visibility = View.GONE
            val english = stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.lang_english.toString(),getString(R.string.lang_english))
            val arabic = getString(R.string.lang_arabic)
            arrayList?.add(LanguageItem(english, preference?.configLocaleData?.english, preference?.appLanguage))
            arrayList?.add(LanguageItem(arabic, preference?.configLocaleData?.arabic, preference?.appLanguage))
            uiInitialisation()
            setAdapter()
        } else {
            noConnectionLayout()
        }
    }

    private fun setAdapter() {
        notificationAdapter = ChangeLanguageAdapter(arrayList, this@ChangeLanguageActivity)
        binding!!.recyclerview.adapter = notificationAdapter
    }

    private fun uiInitialisation() {
        binding!!.recyclerview.hasFixedSize()
        binding!!.recyclerview.isNestedScrollingEnabled = false
        binding!!.recyclerview.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
    }

    private fun noConnectionLayout() {
        binding!!.noConnectionLayout.visibility = View.VISIBLE
        // getBinding().connection.closeButton.setOnClickListener(view -> onBackPressed());
        binding!!.connection.retryTxt.setOnClickListener { connectionObserver() }
    }

    private fun commonDialog(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun itemClicked(name: String?) {
        lanName = name
        val selectedLang = lanName
        val currentLang = preference?.appLanguage

        if (selectedLang.equals(currentLang, ignoreCase = true)) {
            ActivityLauncher.getInstance().homeScreen(this, HomeActivity::class.java, false, "", "", 0)
            return
        }
        preference?.appLanguage = selectedLang
        if (selectedLang.equals(LANGUAGE_ARABIC, ignoreCase = true)) {
            AppCommonMethod.updateLanguage(LANGUAGE_ARABIC_CODE, this@ChangeLanguageActivity)
        } else if (selectedLang.equals(ENGLISH_LAN_CODE, ignoreCase = true)) {
            AppCommonMethod.updateLanguage(LANGUAGE_ENGLISH_CODE, this@ChangeLanguageActivity)
        }
        SharedPrefHelper.getInstance().setStringJson(StringsHelper.loadDataFromJson())
        callMenuManger()

    }

    override fun onActionBtnClicked() {
//        val selectedLang = lanName
//        val currentLang = preference?.appLanguage
//
//        if (selectedLang.equals(currentLang,ignoreCase = true)){
//            ActivityLauncher.getInstance().homeScreen(this, HomeActivity::class.java,false,"","",0)
//            return
//        }
//        preference?.appLanguage = selectedLang
//        if (selectedLang.equals(LANGUAGE_ARABIC, ignoreCase = true)) {
//            AppCommonMethod.updateLanguage(LANGUAGE_ARABIC_CODE, this@ChangeLanguageActivity)
//        } else if (selectedLang.equals(ENGLISH_LAN_CODE, ignoreCase = true)) {
//            AppCommonMethod.updateLanguage(LANGUAGE_ENGLISH_CODE, this@ChangeLanguageActivity)
//        }
//        SharedPrefHelper.getInstance().setStringJson(StringsHelper.loadDataFromJson())
//        callMenuManger()
    }

     private fun callMenuManger(){
         menuViewModel?.getMenuManager(LanguageLayer.getCurrentLanguageCode(), this)?.observe(this){
             if (it?.data?.orderedMenuItems != null){
                 KsPreferenceKeys.getInstance().saveDataMenuKeyValue(it.data.orderedMenuItems)
                 ActivityLauncher.getInstance().homeScreen(this, HomeActivity::class.java,false,"","",0)
             }
         }
     }

    override fun onCancelBtnClicked() {}

}