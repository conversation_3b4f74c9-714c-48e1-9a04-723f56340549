package com.enveu.activities.videoquality.ui


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.enveu.R
import com.enveu.activities.videoquality.adapter.VideoQualityAdapter
import com.enveu.activities.videoquality.bean.TrackItem
import com.enveu.activities.videoquality.callBack.NotificationItemClickListner
import com.enveu.activities.videoquality.viewModel.VideoQualityViewModel
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.databinding.VideoQualityActivityBinding
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper

class VideoQualityActivity : BaseBindingActivity<VideoQualityActivityBinding?>(),
    NotificationItemClickListner {
    private var viewModel: VideoQualityViewModel? = null
    private var notificationAdapter: VideoQualityAdapter? = null
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    override fun inflateBindingLayout(inflater: LayoutInflater): VideoQualityActivityBinding {
        return VideoQualityActivityBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
        binding!!.colorsData = colorsHelper
        binding!!.stringData = stringsHelper
        binding!!.toolbar.colorsData = colorsHelper
        binding!!.toolbar.stringData = stringsHelper
        callModel()
        connectionObserver()
        setToolBar()
    }

    private fun setToolBar() {
        binding!!.toolbar.logoMain2.visibility = View.GONE
        binding!!.toolbar.backLayout.visibility = View.VISIBLE
        binding!!.toolbar.searchIcon.visibility = View.GONE
        binding!!.toolbar.titleText.visibility = View.VISIBLE
        val streamingTile = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.streaming_settings_title.toString(),
            getString(R.string.streaming_settings_title)
        )
        binding!!.toolbar.screenText.text = streamingTile
        binding!!.toolbar.screenText.setBackgroundResource(0)
        binding!!.toolbar.backArrow?.let { rotateImageLocaleWise(it) }
        binding!!.toolbar.backLayout.setOnClickListener { onBackPressed() }
    }

    private fun callModel() {
        viewModel = ViewModelProvider(this)[VideoQualityViewModel::class.java]
    }

    private fun connectionObserver() {
        connectionValidation(NetworkConnectivity.isOnline(this))
    }

    private var arrayList: ArrayList<TrackItem>? = null
    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            binding!!.noConnectionLayout.visibility = View.GONE
            arrayList = viewModel!!.getQualityList(resources)
            uiInitialisation()
            setAdapter()
        } else {
            noConnectionLayout()
        }
    }

    private fun setAdapter() {
        notificationAdapter = VideoQualityAdapter(this@VideoQualityActivity, arrayList, this@VideoQualityActivity)
        binding!!.recyclerview.adapter = notificationAdapter
    }

    private fun uiInitialisation() {
        binding!!.recyclerview.hasFixedSize()
        binding!!.recyclerview.isNestedScrollingEnabled = false
        binding!!.recyclerview.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
    }

    private fun noConnectionLayout() {
        binding!!.noConnectionLayout.visibility = View.VISIBLE
    }

    override fun onClick(id: String, status: String) {
        notificationAdapter!!.notifyDataSetChanged()
    }
}