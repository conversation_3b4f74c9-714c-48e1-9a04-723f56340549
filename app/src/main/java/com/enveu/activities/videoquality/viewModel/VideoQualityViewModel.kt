package com.enveu.activities.videoquality.viewModel

import android.app.Application
import android.content.res.Resources
import androidx.lifecycle.AndroidViewModel
import com.enveu.R
import com.enveu.activities.videoquality.bean.LanguageItem
import com.enveu.activities.videoquality.bean.TrackItem
import com.enveu.utils.Logger


import com.enveu.utils.stringsJson.converter.StringsHelper

class VideoQualityViewModel(application: Application) : AndroidViewModel(application) {

    private val stringsHelper by lazy { StringsHelper }

    val languageList: ArrayList<LanguageItem>
        get() {
            Logger.e("Locale", getApplication<Application>().getString(R.string.language_english))
            val trackItems = ArrayList<LanguageItem>()
            for (i in 0..2) {
                if (i == 0) {
                    val languageItem =
                        LanguageItem()
                    languageItem.languageName = getApplication<Application>().getString(R.string.language_english)
                    trackItems.add(languageItem)
                } else if (i == 1) {
                    val languageItem =
                        LanguageItem()
                    languageItem.languageName = getApplication<Application>().getString(R.string.language_spanish)
                    trackItems.add(languageItem)
                }
            }
            return trackItems
        }

    fun getQualityList(resources: Resources): ArrayList<TrackItem> {
        val trackItems = ArrayList<TrackItem>()
        for (i in 0..3) {
            when (i) {
                0 -> {
                    val auto = stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.streaming_auto.toString(),
                        "Auto"
                    )
                    trackItems.add(
                        TrackItem(
                            auto,
                            resources.getString(R.string.ep_video_auto),
                            resources.getString(R.string.ep_video_auto)
                        )
                    )
                }
                1 -> {
                    val fullHd = stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.streaming_full_hd.toString(),
                        "High"
                    )
                    trackItems.add(
                        TrackItem(
                            fullHd,
                            resources.getString(R.string.streaming_full_hd),
                            resources.getString(R.string.streaming_full_hd)
                        )
                    )
                }
                2 -> {
                    val hd = stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.streaming_hd.toString(),
                        "Medium"
                    )
                    trackItems.add(
                        TrackItem(
                            hd,
                            resources.getString(R.string.streaming_hd),
                            resources.getString(R.string.streaming_hd)
                        )
                    )
                }
                3 -> {
                    val sd = stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.streaming_sd.toString(),
                        "Low"
                    )
                    trackItems.add(
                        TrackItem(
                            sd,
                            resources.getString(R.string.streaming_sd),
                            resources.getString(R.string.streaming_sd)
                        )
                    )
                }
            }
        }
        return trackItems
    }
}