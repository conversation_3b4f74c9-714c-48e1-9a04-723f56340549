package com.enveu.activities.watchList.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.databinding.FragmentAlbumBinding
import com.enveu.fragments.dialog.CommonDialogFragment

class WatchListAndHistoryFragment: BaseBindingFragment<FragmentAlbumBinding>(),
    CommonDialogFragment.EditDialogListener {
    override fun inflateBindingLayout(inflater: LayoutInflater): FragmentAlbumBinding {
        return FragmentAlbumBinding.inflate(inflater)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

    }
    override fun onActionBtnClicked() {
    }
    override fun onCancelBtnClicked() {
    }
}