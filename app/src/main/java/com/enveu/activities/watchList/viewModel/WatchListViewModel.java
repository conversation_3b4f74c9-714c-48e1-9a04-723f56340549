package com.enveu.activities.watchList.viewModel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;

import com.enveu.activities.profile.order_history.model.TransactionHistoryModel;
import com.enveu.beanModel.AssetHistoryContinueWatching.ResponseAssetHistory;
import com.enveu.beanModel.emptyResponse.ResponseEmpty;
import com.enveu.repository.watchList.WatchListRepository;

import com.enveu.activities.profile.order_history.model.OrderHistoryModel;

public class WatchListViewModel extends AndroidViewModel {
    // private Context context;
    private final WatchListRepository watchListRepository;

    public WatchListViewModel(@NonNull Application application) {
        super(application);
        //  this.context = application;
        watchListRepository = WatchListRepository.getInstance();

    }
    public LiveData<ResponseEmpty> hitApiRemoveWatchList(String token, String data) {
        return watchListRepository.hitApiRemoveFromWatchList(token, data);
    }

    public LiveData<ResponseAssetHistory> getAssetHistory(String token, int page, int size) {
        return watchListRepository.getAssetHistory(token, page, size);
    }
    public LiveData<OrderHistoryModel> getOrderHistory(String token, String page, String size) {
        return watchListRepository.getOrderHistoryModel(token, page, size);
    }
    public LiveData<TransactionHistoryModel> getTransactionHistory(String token, String page, String size) {
        return watchListRepository.getTransactionDetails(token, page, size);
    }
    public void callCleared() {
        onCleared();
    }

    @Override
    protected void onCleared() {
        super.onCleared();
    }
}

