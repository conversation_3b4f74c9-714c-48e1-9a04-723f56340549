package com.enveu.activities.web_view

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.OnBackPressedCallback
import com.enveu.R
import com.enveu.baseModels.BaseBindingActivity
import com.enveu.databinding.ActivityWebViewBinding
import com.enveu.utils.Constants
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.helpers.ToolBarHandler
import com.enveu.utils.hide
import com.enveu.utils.show
import com.enveu.utils.stringsJson.converter.StringsHelper


class WebViewActivity : BaseBindingActivity<ActivityWebViewBinding?>() {
    override fun inflateBindingLayout(inflater: LayoutInflater): ActivityWebViewBinding {
        return ActivityWebViewBinding.inflate(inflater)
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ToolBarHandler.getInstance().setArticleAction(binding, <EMAIL>(R.string.help_center), this)
        setClick()
        initUI()
    }

    private fun setClick() {
        binding!!.toolbarFaq.titleSkip.visibility = View.GONE
        binding!!.toolbarFaq.llSearchIcon.visibility = View.GONE
        binding!!.toolbarFaq.logoMain2.visibility = View.GONE
        binding!!.toolbarFaq.colorsData = ColorsHelper
        binding!!.toolbarFaq.stringData = StringsHelper
        binding!!.toolbarFaq.backLayout.setOnClickListener { onBackPressedDispatcher.onBackPressed() }
    }

    private fun initUI() {
        webViewImplement()
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finish()
            }
        })
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun webViewImplement() {
        val webViewUrl = intent.getStringExtra(Constants.WEB_VIEW_URL)
        binding?.webView?.apply {
            settings.javaScriptEnabled = true
            settings.domStorageEnabled = true
            settings.allowFileAccess = true
            settings.allowContentAccess = true
            settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            settings.useWideViewPort = true
            settings.loadWithOverviewMode = true
            settings.builtInZoomControls = false
            settings.userAgentString = USER_AGENT_STRING
            val cookieManager = android.webkit.CookieManager.getInstance()
            cookieManager.setAcceptCookie(true)
            cookieManager.setAcceptThirdPartyCookies(this, true)
            webViewClient = InsideWebViewClient()
            webChromeClient = android.webkit.WebChromeClient()
            webViewUrl?.let { loadUrl(it) }
        }
    }

    private inner class InsideWebViewClient : WebViewClient() {
        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
            super.onPageStarted(view, url, favicon)
            binding?.webView?.hide()
            binding?.progressBar?.show()
        }

        @SuppressLint("ResourceType")
        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            binding?.webView?.show()
            binding?.progressBar?.hide()
        }
    }

    companion object {
      const val  USER_AGENT_STRING = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
}