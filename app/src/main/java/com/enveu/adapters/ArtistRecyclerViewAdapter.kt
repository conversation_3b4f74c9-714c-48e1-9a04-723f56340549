package com.enveu.adapters

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.SaveCheckedArtistClickListener
import com.enveu.databinding.CircularViewRowBinding
import com.enveu.utils.helpers.ImageHelper

class ArtistRecyclerViewAdapter(
    private val context: Context,private var selectedId: String,
    var list: MutableList<EnveuVideoItemBean>,
    private val saveCheckedArtistClickListener: SaveCheckedArtistClickListener
) : RecyclerView.Adapter<ArtistRecyclerViewAdapter.MyViewHolder>() {
    private var selectedItemPosition : Int = -1

    @SuppressLint("NotifyDataSetChanged")
    inner class MyViewHolder(val binding: CircularViewRowBinding) : RecyclerView.ViewHolder(binding.root){
        fun setDataList(dataList: EnveuVideoItemBean?) {
            ImageHelper.getInstance(binding.circleImages.context)
                .loadCircleImageTo(binding.circleImages, dataList?.posterURL)

            if (dataList?.id.toString() == selectedId||selectedItemPosition == bindingAdapterPosition){
                binding.checkedArtist.visibility = View.VISIBLE
                saveCheckedArtistClickListener.onSaveClick(true, dataList!!, position)
            }else{
                binding.checkedArtist.visibility = View.GONE
            }
            binding.parentLayout.setOnClickListener {
                selectedId = dataList?.id.toString()
                notifyDataSetChanged()
            }
        }


}

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
        val binding = CircularViewRowBinding.inflate(LayoutInflater.from(context), parent, false)
        return MyViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateData(list: MutableList<EnveuVideoItemBean>){
        this.list.clear()
        this.list = list
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun notifyData(artistList: MutableList<EnveuVideoItemBean>){
        list.addAll(artistList)
        notifyDataSetChanged()
    }


    override fun onBindViewHolder(holder: MyViewHolder, position: Int) {
        val dataList: EnveuVideoItemBean = list[position]
        holder.setDataList(dataList)
    }

}