package com.enveu.adapters;

import android.app.Activity;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.enveu.R;
import com.enveu.beanModelV3.searchGenres.SearchGenres;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.callbacks.commonCallbacks.ContentPreferenceCallback;
import com.enveu.databinding.ItemGenreSponserBinding;
import com.enveu.utils.config.bean.Genre;
import com.enveu.utils.config.bean.PreferenceBean;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;


import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ContentPreferenceAdapter extends RecyclerView.Adapter<ContentPreferenceAdapter.SingleItemRowHolder> {

    private final Activity activity;
    List<SearchGenres.Data.Item> list;
    List<String> genres;
    private Map<String,List<Integer>> filterGenres;
    private boolean isFromApi=true;
    private final ContentPreferenceCallback callback;
    private final Set<Integer> selectedIds = new HashSet<>(); // To track selected item IDs
    private final Set<String> selectedGenre = new HashSet<>(); // To track selected item IDs

    public ContentPreferenceAdapter(Activity ctx, List<SearchGenres.Data.Item> list, ContentPreferenceCallback callback) {
        this.activity = ctx;
        this.list = list;
        this.callback = callback;
    }

    public ContentPreferenceAdapter(Activity ctx, Map<String,List<Integer>> list,boolean isFromApi, ContentPreferenceCallback callback) {
        this.activity = ctx;
        this.filterGenres=list;
        this.isFromApi=isFromApi;
        this.callback = callback;
        genres=new ArrayList<>();
        for (Map.Entry<String, List<Integer>> entry : filterGenres.entrySet()) {
            genres.add(entry.getKey());
        }
    }
    @NonNull
    @Override
    public SingleItemRowHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        ItemGenreSponserBinding itemBinding = DataBindingUtil.inflate(
                LayoutInflater.from(viewGroup.getContext()),
                R.layout.item_genre_sponser, viewGroup, false);
        return new SingleItemRowHolder(itemBinding);
    }

    @Override
    public void onBindViewHolder(@NonNull final SingleItemRowHolder viewHolder, final int position) {
        if (isFromApi) {
            SearchGenres.Data.Item item = list.get(position);

            if (item.getTitle() != null) {
                viewHolder.genreItemBinding.titleText.setText(item.getTitle());
            }
            // Update the appearance based on selection state
            if (selectedIds.contains(item.getId())) {
                viewHolder.genreItemBinding.titleText.setBackgroundResource(R.drawable.rounded_selected_sponsers);
                viewHolder.genreItemBinding.titleText.setTextColor(activity.getResources().getColor(R.color.white));
            } else {
                viewHolder.genreItemBinding.titleText.setBackgroundResource(R.drawable.rounded_background_sponser); // Use a drawable for deselected state
                viewHolder.genreItemBinding.titleText.setTextColor(activity.getResources().getColor(R.color.secondary_color));
            }

            // Handle item click
            viewHolder.genreItemBinding.titleText.setOnClickListener(view -> {
                int itemId = item.getId(); // Assuming getId() returns the ID for the item

                if (selectedIds.contains(itemId)) {
                    selectedIds.remove(itemId);
                    List<Integer> selectedIdsList = new ArrayList<>(selectedIds);
                    callback.onClick(selectedIdsList, position);
                } else {
                    selectedIds.add(itemId);
                    List<Integer> selectedIdsList = new ArrayList<>(selectedIds);
                    callback.onClick(selectedIdsList, position);
                }

                // Update the view
                notifyItemChanged(position);

                // Prepare the list of selected item IDs

            });
        }else {
            setLocalFilterGenresData(viewHolder,position);
        }
    }

    private void setLocalFilterGenresData(SingleItemRowHolder viewHolder, int position) {
        viewHolder.genreItemBinding.titleText.setText(genres.get(position));
        if (selectedGenre.contains(genres.get(position))) {
            viewHolder.genreItemBinding.titleText.setBackgroundResource(R.drawable.rounded_selected_sponsers);
            viewHolder.genreItemBinding.titleText.setTextColor(activity.getResources().getColor(R.color.white));
        } else {
            viewHolder.genreItemBinding.titleText.setBackgroundResource(R.drawable.rounded_background_sponser); // Use a drawable for deselected state
            viewHolder.genreItemBinding.titleText.setTextColor(activity.getResources().getColor(R.color.secondary_color));
        }

        viewHolder.genreItemBinding.titleText.setOnClickListener(view -> {
            String genre = genres.get(position); // Assuming getId() returns the ID for the item

            if (selectedGenre.contains(genre)) {
                selectedGenre.remove(genre);
                List<Integer> selectedIdsList = getSelectedGenreIds(genre);
                Log.d("FilterGener","ids: "+selectedIdsList.toString());
                callback.onClick(selectedIdsList, position);
            } else {
                selectedGenre.add(genre);
                List<Integer> selectedIdsList = getSelectedGenreIds(genre);
                callback.onClick(selectedIdsList, position);
            }

            // Update the view
            notifyItemChanged(position);

            // Prepare the list of selected item IDs

        });
    }

    private List<Integer> getSelectedGenreIds(String genre) {
        List<Integer> genreIds=new ArrayList<>();
        for (String genre1:selectedGenre) {
            genreIds.addAll(filterGenres.get(genre1));
        }
        return genreIds;
    }

    @Override
    public int getItemCount() {
        if (isFromApi) {
            return list.size();
        }else {
            return genres.size();
        }
    }

    public class SingleItemRowHolder extends RecyclerView.ViewHolder {
        final ItemGenreSponserBinding genreItemBinding;

        private SingleItemRowHolder(ItemGenreSponserBinding binding) {
            super(binding.getRoot());
            this.genreItemBinding = binding;
        }
    }
}
