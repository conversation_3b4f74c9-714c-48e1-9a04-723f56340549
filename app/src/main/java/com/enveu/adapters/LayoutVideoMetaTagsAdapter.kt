package com.enveu.adapters

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.databinding.LayoutVideoMetaTagsItemBinding

class LayoutVideoMetaTagsAdapter(val context:Context, var tagList:MutableList<String>,val callback: LayoutVideoMetaTagsCallback):RecyclerView.Adapter<LayoutVideoMetaTagsAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: LayoutVideoMetaTagsItemBinding) :RecyclerView.ViewHolder(binding.root){

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding=LayoutVideoMetaTagsItemBinding.inflate(LayoutInflater.from(context),parent,false)
        return ViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return tagList.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.binding.tvLvmTag.text=tagList[position]

        if (tagList.size-1 == position){
            holder.binding.imLvmTagDivider.visibility=View.GONE
        }

        holder.binding.tvLvmTag.setOnClickListener{
            callback.tagClicked(position)
        }
    }
}

public interface LayoutVideoMetaTagsCallback{
    fun tagClicked(position: Int)
}