package com.enveu.adapters

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.fragments.newseriesui.NewEpisodeListFragment
import com.enveu.fragments.newseriesui.NewSeriesEpisodesViewModel
import com.enveu.utils.constants.AppConstants
import com.npaw.youbora.lib6.extensions.putInt

class SeasonEpisodesAdapter(val activity:FragmentActivity):FragmentStateAdapter(activity) {
    var totalSeason=0
    var seasonNumber=0
    var seriesId=0
    var currentAssetId=0
    var seasonId=0
    var redirection=""
    var seasonData:List<EnveuVideoItemBean>?=null
    constructor(activity: FragmentActivity, seasonNumber: Int, seriesId: Int, currentAssetId: Int, totalSeason:Int,seasonData:List<EnveuVideoItemBean>,redirection:String) : this(activity){
        this.totalSeason=totalSeason
        this.seasonNumber= seasonNumber
        this.seriesId= seriesId
        this.currentAssetId= currentAssetId
        this.seasonId
        this.seasonData=seasonData
        this.redirection=redirection
    }

    override fun getItemCount(): Int {
        return totalSeason
    }

    override fun createFragment(position: Int): Fragment {
        val newEpisodeListFragment = NewEpisodeListFragment()
        val bundle=Bundle()
        bundle.putInt("totalSeason",totalSeason)
        bundle.putInt("seasonNumber",seasonNumber)
        bundle.putInt("seriesId",seriesId)
        bundle.putInt("currentAssetId",currentAssetId)
        bundle.putInt("position",position)
        bundle.putString(AppConstants.FROM_REDIRECTION,redirection)
        newEpisodeListFragment.arguments=bundle
        newEpisodeListFragment.setSeasonData(seasonData)
        return newEpisodeListFragment
    }

}