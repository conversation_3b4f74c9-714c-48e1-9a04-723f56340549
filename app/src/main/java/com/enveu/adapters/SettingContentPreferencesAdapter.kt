package com.enveu.adapters

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.contentpreferences.SettingContentPreferences
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.beanModelV3.searchGenres.SearchGenres
import com.enveu.callbacks.commonCallbacks.SaveCheckedGenreClickListener
import com.enveu.databinding.CircularViewRowBinding
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ImageHelper
import com.moengage.core.internal.utils.isNullOrEmpty

class SettingContentPreferencesAdapter(
    private val context: Context, private var selectedId: String,
    var list: List<SearchGenres.Data.Item?>?,
    private val saveCheckedGenreClickListener: SaveCheckedGenreClickListener,
    private val selectedIds: MutableSet<String>?
) : RecyclerView.Adapter<SettingContentPreferencesAdapter.MyViewHolder>() {
    private var selectedItemPosition : Int = -1

    @SuppressLint("NotifyDataSetChanged")
    inner class MyViewHolder(val binding: CircularViewRowBinding) : RecyclerView.ViewHolder(binding.root){
        fun setDataList(dataList: SearchGenres.Data.Item?) {
            val image = dataList?.images?.get(0)?.imageContent?.src
            val dominantColorHex = dataList?.images?.get(0)?.imageContent
            if (!image.isNullOrEmpty()) {
                ImageHelper.getInstance(binding.circleImages.context)
                    .loadCircleImageTo(binding.circleImages, image)
            }else{
                val dominantColor = AppCommonMethod.getDominantColor(dominantColorHex)
                binding.circleImages.setImageDrawable(ColorDrawable(Color.parseColor(dominantColor)))
            }

            if (image.isNullOrEmpty()){
                 binding.textTitle.text = dataList?.title
            }else{
                binding.textTitle.text = ""
            }

            if (selectedIds?.contains(dataList?.id.toString()) == true) {
                binding.checkedArtist.visibility = View.VISIBLE
            } else {
                binding.checkedArtist.visibility = View.GONE
            }

            binding.parentLayout.setOnClickListener {
                val itemId = dataList?.id.toString()

                if (selectedIds?.contains(itemId) == true) {
                    selectedIds?.remove(itemId)
                    saveCheckedGenreClickListener.onSaveClick(false, selectedIds)
                } else {
                    if (selectedIds?.size!! < 5) {
                        selectedIds?.add(itemId)
                        saveCheckedGenreClickListener.onSaveClick(true, selectedIds)
                    } else {
                        //TODO Put this massage in string file
                        Toast.makeText(itemView.context, R.string.you_can_only_select_five_items, Toast.LENGTH_SHORT).show()
                    }
                }

                (itemView.context as? SettingContentPreferences)?.let { activity ->
                    ViewModelProvider(activity).get(RegistrationLoginViewModel::class.java).selectedIds.value = selectedIds
                }
//                (itemView.context as? SettingContentPreferences)?.viewModel?.selectedIds?.value = selectedIds

                notifyItemChanged(bindingAdapterPosition)
            }
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
        val binding = CircularViewRowBinding.inflate(LayoutInflater.from(context), parent, false)
        return MyViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return list?.size ?:0
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateData(list: List<SearchGenres.Data.Item?>?){
        this.list
        this.list = list
        notifyDataSetChanged()
    }

    fun saveSelectedIdsToStorage(context: Context, selectedIds: Set<String>) {
        val sharedPreferences = context.getSharedPreferences(AppConstants.PREFS_SELECTED_IDS, Context.MODE_PRIVATE)
        val editor = sharedPreferences.edit()
        editor.putStringSet(AppConstants.SELECTED_IDS, selectedIds)
        editor.apply()
    }

    fun selectedGenresList(): MutableSet<String>? {
        return selectedIds
    }

    override fun onBindViewHolder(holder: MyViewHolder, position: Int) {
        val dataList = list?.get(position)
        holder.setDataList(dataList)
    }

}