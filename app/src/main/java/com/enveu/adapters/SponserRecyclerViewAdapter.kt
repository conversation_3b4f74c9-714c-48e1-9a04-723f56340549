package com.enveu.adapters

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.SaveCheckedArtistClickListener
import com.enveu.databinding.CircularSponserRowBinding
import com.enveu.utils.helpers.ImageHelper

class SponserRecyclerViewAdapter(
    private val context: Context,
    private var selectedId: String ?, // ID for API calls
    var list: MutableList<EnveuVideoItemBean>,
    private val saveCheckedArtistClickListener: SaveCheckedArtistClickListener
) : RecyclerView.Adapter<SponserRecyclerViewAdapter.MyViewHolder>() {

    private var selectedItemPosition: Int = RecyclerView.NO_POSITION

    inner class MyViewHolder(val binding: CircularSponserRowBinding) : RecyclerView.ViewHolder(binding.root) {
        val image = binding.circleImages
        val checkedArtist = binding.checkedArtist
        val artistName = binding.artistName
        val circulerTxt = binding.circleTxt
        val parentLayout = binding.parentLayout
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
        val binding = CircularSponserRowBinding.inflate(LayoutInflater.from(context), parent, false)
        return MyViewHolder(binding)
    }

    override fun getItemCount(): Int = list.size

    fun updateData(newList: List<EnveuVideoItemBean>) {
        list.clear()
        list.addAll(newList)
        notifyDataSetChanged() // Notify RecyclerView that the whole data set has changed
    }

    fun notifyData(newList: MutableList<EnveuVideoItemBean>) {
        val startPosition = list.size
        list.addAll(newList)
        notifyItemRangeInserted(startPosition, newList.size)
    }

    override fun onBindViewHolder(holder: MyViewHolder, @SuppressLint("RecyclerView") position: Int) {
        val dataList = list[position]
        if (!dataList.images?.get(0)?.imageContent?.src.isNullOrEmpty()) {
            holder.circulerTxt.visibility = View.GONE
            ImageHelper.getInstance(holder.image.context)
                .loadCircleImageTo(holder.image, dataList.images?.get(0)?.imageContent?.src)
        } else {
            if (!dataList.title.isNullOrEmpty()) {
                ImageHelper.getInstance(holder.image.context)
                    .loadCircleImageTo(holder.image, "") // Load a placeholder or empty image if needed
                holder.circulerTxt.text = dataList.title
                holder.circulerTxt.visibility = View.VISIBLE
            }
        }




        holder.artistName.text = dataList.title
        holder.artistName.visibility = if (dataList.title != null) View.VISIBLE else View.GONE

        if (selectedId.isNullOrEmpty()){
            selectedId = "0"
        }

        val isSelected = selectedId?.toInt() == dataList.id
        val isCurrentPosition = position == selectedItemPosition

        holder.checkedArtist.setBackgroundResource(
            when {
                isSelected -> R.drawable.star
                isCurrentPosition -> R.drawable.rigth
                else -> 0 // Add a default drawable if needed
            }
        )
        if (isSelected) {
            val imageContent = dataList.imageContent
            if (imageContent != null && imageContent.src != null) {
                saveCheckedArtistClickListener.onUserDetail(dataList.title, imageContent!!.src,dataList)
            }else{
                saveCheckedArtistClickListener.onUserDetail(dataList.title, "",dataList)
            }
        }


        holder.parentLayout.setOnClickListener {
            val oldPosition = selectedItemPosition
            selectedItemPosition = position
            notifyItemChanged(oldPosition)
            notifyItemChanged(position)

            saveCheckedArtistClickListener.onSaveClick(true, dataList, position)
        }
    }
}
