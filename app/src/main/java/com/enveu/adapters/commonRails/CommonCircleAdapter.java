package com.enveu.adapters.commonRails;

import static com.enveu.utils.ViewExtensionKt.getImageUrl;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.enveu.R;
import com.enveu.activities.listing.callback.ItemClickListener;
import com.enveu.activities.series.ui.SeriesDetailActivity;
import com.enveu.beanModel.ContinueRailModel.CommonContinueRail;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.beanModelV3.videoDetailV3.list.ImagesItem;
import com.enveu.databinding.GridCircleItemBinding;
import com.enveu.utils.Constants;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.ImageHelper;
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

;

public class CommonCircleAdapter extends RecyclerView.Adapter<CommonCircleAdapter.SingleItemRowHolder> {

    private final String contentType;
    private final List<EnveuVideoItemBean> itemsList;
    private final ItemClickListener listener;
    private long mLastClickTime = 0;
    private final ArrayList<CommonContinueRail> continuelist;
    private boolean isContinueList;
    private final String isLogin;
    private final KsPreferenceKeys preference;
    private RailCommonData railCommonData;

    public CommonCircleAdapter(Activity context, List<EnveuVideoItemBean> itemsList, String contentType, ArrayList<CommonContinueRail> continuelist, ItemClickListener callback, RailCommonData playlistRailData) {
        this.itemsList = itemsList;
        this.contentType = contentType;
        this.continuelist = continuelist;
        this.railCommonData = playlistRailData;
        listener = callback;
        if (this.continuelist != null) {
            isContinueList = this.continuelist.size() > 0;
        }
        preference = KsPreferenceKeys.getInstance();
        isLogin = preference.getAppPrefLoginStatus();
        int num = 4;
        boolean tabletSize = context.getResources().getBoolean(R.bool.isTablet);
        if (tabletSize) {
            if (context.getResources().getConfiguration().orientation == 2)
                num = 6;
            else
                num = 5;
        }
    }

    public void notifydata(List<EnveuVideoItemBean> i) {

        this.itemsList.addAll(i);
        notifyDataSetChanged();

    }
    @NonNull
    @Override
    public SingleItemRowHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        GridCircleItemBinding binding = DataBindingUtil.inflate(
                LayoutInflater.from(parent.getContext()),
                R.layout.grid_circle_item, parent, false);
        binding.setColorsData(ColorsHelper.INSTANCE);
        return new SingleItemRowHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull SingleItemRowHolder holder, @SuppressLint("RecyclerView") int i) {
        if (itemsList.size() > 0) {
            EnveuVideoItemBean contentsItem = itemsList.get(i);
            if (contentsItem != null) {
                holder.circularItemBinding.llContinueProgress.setVisibility(View.GONE);
                holder.circularItemBinding.ivContinuePlay.setVisibility(View.GONE);
                holder.circularItemBinding.tvTitle.setText(itemsList.get(i).getTitle());

                try{
                    if (contentsItem.getPosterURL().equalsIgnoreCase("") && contentsItem.getPosterURL() == null) {
                        Objects.requireNonNull(holder.circularItemBinding.imageTitle).setVisibility(View.VISIBLE);
                        holder.circularItemBinding.imageTitle.setText(contentsItem.getTitle());
                    } else {
                        Objects.requireNonNull(holder.circularItemBinding.imageTitle).setVisibility(View.GONE);
                    }
                } catch (Exception ignored) {

                }

                if (contentType.equalsIgnoreCase(AppConstants.VOD)) {
                    try {
                        String imageUrl;
                        if (contentsItem.getAssetType().equalsIgnoreCase(AppConstants.PERSON)) {
                            imageUrl = getImageUrl(Constants.TEN_INTO_TEN, contentsItem.getImages(),holder.circularItemBinding.itemImage.getContext());
                        } else {
                            imageUrl = getImageUrl(Constants.ONE_INTO_ONE, contentsItem.getImages(),holder.circularItemBinding.itemImage.getContext());
                        }
                        contentsItem.setPosterURL(imageUrl);
                        ImageHelper.getInstance(holder.circularItemBinding.itemImage.getContext()).loadCIRImage2(holder.circularItemBinding.itemImage,
                                imageUrl, null);
                    }catch (Exception ignored){

                    }

//                    if (contentsItem.getPosterURL() != null && !contentsItem.getPosterURL().equalsIgnoreCase("")) {
//                        ImageHelper.getInstance(holder.circularItemBinding.itemImage.getContext())
//                                .loadCIRImage2(holder.circularItemBinding.itemImage, contentsItem.getPosterURL(),null);
//                    }

                    holder.circularItemBinding.itemImage.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            listener.onRowItemClicked(itemsList.get(i), i);
                        }
                    });
                } else if (contentType.equalsIgnoreCase(AppConstants.SERIES)) {
                    holder.circularItemBinding.itemImage.setOnClickListener(view -> {
                        if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
                            return;
                        }
                        mLastClickTime = SystemClock.elapsedRealtime();
                        ActivityLauncher.getInstance().seriesDetailScreen((Activity) holder.circularItemBinding.itemImage.getContext(), SeriesDetailActivity.class, itemsList.get(i).getId());

                    });
                    ImageHelper.getInstance(holder.circularItemBinding.itemImage.getContext())
                            .loadImageTo(holder.circularItemBinding.itemImage, getImageUrl(Constants.TEN_INTO_TEN,contentsItem.getImages(),holder.circularItemBinding.itemImage.getContext()));

                } else if (contentType.equalsIgnoreCase(AppConstants.CAST_AND_CREW)) {

                    ImageHelper.getInstance(holder.circularItemBinding.itemImage.getContext())
                            .loadImageTo(holder.circularItemBinding.itemImage, getImageUrl(Constants.TEN_INTO_TEN,contentsItem.getImages(),holder.circularItemBinding.itemImage.getContext()));
                } else if (contentType.equalsIgnoreCase(AppConstants.GENRE)) {

                    ImageHelper.getInstance(holder.circularItemBinding.itemImage.getContext())
                            .loadImageTo(holder.circularItemBinding.itemImage, getImageUrl(Constants.TEN_INTO_TEN,contentsItem.getImages(),holder.circularItemBinding.itemImage.getContext()));

                }


            }
        } else if (continuelist.size() > 0) {

            if (isLogin.equalsIgnoreCase(AppConstants.UserStatus.Login.toString())) {
                holder.circularItemBinding.llContinueProgress.setVisibility(View.VISIBLE);
                holder.circularItemBinding.ivContinuePlay.setVisibility(View.VISIBLE);
                holder.circularItemBinding.flNew.setVisibility(View.GONE);

                if (continuelist.get(i).getUserAssetDetail().isPremium()) {
                    holder.circularItemBinding.flExclusive.setVisibility(View.VISIBLE);
                } else {
                    holder.circularItemBinding.flExclusive.setVisibility(View.GONE);
                }
                ImageHelper.getInstance(holder.circularItemBinding.itemImage.getContext())
                        .loadImageTo(holder.circularItemBinding.itemImage, AppCommonMethod.getImageUrl(AppConstants.VOD, "CIRCLE") + continuelist.get(i).getUserAssetDetail().getPortraitImage());

                holder.circularItemBinding.itemImage.setOnClickListener(v -> {
                    if (isLogin.equalsIgnoreCase(AppConstants.UserStatus.Login.toString())) {
                        if (continuelist.size() > 0 && (continuelist.get(i).getUserAssetDetail().getAssetType()) != null) {
                            AppCommonMethod.redirectionLogic(holder.circularItemBinding.itemImage.getContext(),railCommonData,i ,"","","");
                          //  AppCommonMethod.launchDetailScreen(holder.circularItemBinding.itemImage.getContext(),0l, continuelist.get(i).getUserAssetDetail().getAssetType(), continuelist.get(i).getUserAssetDetail().getId(), String.valueOf(continuelist.get(i).getUserAssetStatus().getPosition()), continuelist.get(i).getUserAssetDetail().isPremium());
                        }
                    }
                });

            } else {
                holder.circularItemBinding.llContinueProgress.setVisibility(View.GONE);
                holder.circularItemBinding.ivContinuePlay.setVisibility(View.GONE);
            }
        }
    }

    private String getForCircularView(List<ImagesItem> images){
        String type = Constants.TEN_INTO_TEN;
        if (images.get(0).getImageContent().getImageType().contains("10")){
            type = images.get(0).getImageContent().getImageType();
        }
        return type;
    }

    @Override
    public int getItemCount() {

        if (isContinueList)
            return (null != continuelist ? continuelist.size() : 0);
        else
            return (null != itemsList ? itemsList.size() : 0);
    }

    public class SingleItemRowHolder extends RecyclerView.ViewHolder {

        final GridCircleItemBinding circularItemBinding;

        SingleItemRowHolder(GridCircleItemBinding circularItemBind) {
            super(circularItemBind.getRoot());
            circularItemBinding = circularItemBind;
        }

    }


}
