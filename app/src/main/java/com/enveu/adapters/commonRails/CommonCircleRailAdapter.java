package com.enveu.adapters.commonRails;

import static com.enveu.utils.ViewExtensionKt.getImageUrl;

import android.os.SystemClock;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.enveu.R;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.beanModelV3.videoDetailV3.list.ImagesItem;
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner;
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory;
import com.enveu.client.enums.RailCardSize;
import com.enveu.databinding.CircleItemBinding;
import com.enveu.databinding.CircleItemLargeBinding;
import com.enveu.databinding.CircleItemSmallBinding;
import com.enveu.utils.Constants;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.ImageHelper;

import java.util.ArrayList;
import java.util.List;

public class CommonCircleRailAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private long mLastClickTime = 0;
    private final RailCommonData railCommonData;
    private List<EnveuVideoItemBean> videos;
    private final CommonRailtItemClickListner listner;
    private final int pos;

    BaseCategory baseCategory;

    public CommonCircleRailAdapter(RailCommonData railCommonData, int position, CommonRailtItemClickListner listner, BaseCategory baseCat) {
        this.railCommonData = railCommonData;
        this.videos = new ArrayList<>();
        this.videos = railCommonData.getEnveuVideoItemBeans();
        this.listner = listner;
        this.pos = position;
        this.baseCategory = baseCat;

    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        if (baseCategory != null && baseCategory.getRailCardSize() != null) {
            if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.NORMAL.name())) {
                CircleItemBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.circle_item, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new NormalHolder(binding);
            } else if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.SMALL.name())) {
                CircleItemSmallBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.circle_item_small, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new SmallHolder(binding);
            } else if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.LARGE.name())) {
                CircleItemLargeBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.circle_item_large, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new LargeHolder(binding);
            } else {
                CircleItemBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.circle_item, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new NormalHolder(binding);
            }
        } else {
            CircleItemBinding binding = DataBindingUtil.inflate(
                    LayoutInflater.from(parent.getContext()),
                    R.layout.circle_item, parent, false);
            binding.setColorsData(ColorsHelper.INSTANCE);
            return new NormalHolder(binding);
        }

    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int i) {
        if (holder instanceof NormalHolder) {
            setNomalValues(((NormalHolder) holder).circularItemBinding, i);
        } else if (holder instanceof SmallHolder) {
            setSmallValues(((SmallHolder) holder).circularItemBinding, i);
        } else if (holder instanceof LargeHolder) {
            setLargeValues(((LargeHolder) holder).circularItemBinding, i);
        } else {
            setNomalValues(((NormalHolder) holder).circularItemBinding, i);
        }
    }

    private void setLargeValues(CircleItemLargeBinding itemBinding, int i) {
        itemBinding.setPlaylistItem(videos.get(i));
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i);

        });


        itemBinding.imageTitle.setVisibility(View.GONE);
        itemBinding.itemImage.setImageResource(R.color.placeholder_bg); // Use a placeholder image

        try {
            if (videos.get(i).getPosterURL().equalsIgnoreCase("")) {
                itemBinding.imageTitle.setVisibility(View.VISIBLE);
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                itemBinding.imageTitle.setVisibility(View.GONE);
            }
        } catch (Exception ignored) {
        }

        if (videos.get(i).getCustomDataV3() != null && videos.get(i).getCustomDataV3().getIsExclusive() != null) {
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            } else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        } else {
            itemBinding.exclusive.setVisibility(View.GONE);
        }

        try {
            String imageUrl;
            if (videos.get(i).getAssetType().equalsIgnoreCase(AppConstants.PERSON)) {
                imageUrl = getImageUrl(Constants.TEN_INTO_TEN, videos.get(i).getImages(), itemBinding.itemImage.getContext());
            } else {
                imageUrl = getImageUrl(Constants.ONE_INTO_ONE, videos.get(i).getImages(), itemBinding.itemImage.getContext());
            }
            videos.get(i).setPosterURL(imageUrl);
            ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadCircleImageTo(itemBinding.itemImage, imageUrl);
//            if (!videos.get(i).getPosterURL().equalsIgnoreCase("")) {
//                ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadCircleImageTo(itemBinding.itemImage, AppCommonMethod.getListCIRCLEImage(videos.get(i).getPosterURL(), itemBinding.itemImage.getContext()));
//            } else {
//                itemBinding.itemImage.setImageResource(R.color.placeholder_bg); // Use a placeholder image if there's no URL
//            }
        } catch (Exception ignored) {
        }


        try {
            AppCommonMethod.handleTitleDesc(itemBinding.titleLayout, itemBinding.tvTitle, itemBinding.tvDescription, baseCategory);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
        } catch (Exception ignored) {
            Log.d("setLargeValues", "setLargeValues: " + ignored);
        }

    }

    private void setSmallValues(CircleItemSmallBinding itemBinding, int i) {
        itemBinding.setPlaylistItem(videos.get(i));
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i);

        });

        if (videos.get(i).getCustomDataV3() != null && videos.get(i).getCustomDataV3().getIsExclusive() != null) {
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            } else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        } else {
            itemBinding.exclusive.setVisibility(View.GONE);
        }

        itemBinding.imageTitle.setVisibility(View.GONE);
        itemBinding.itemImage.setImageResource(R.color.placeholder_bg); // Use a placeholder image

        try {
            if (videos.get(i).getPosterURL().equalsIgnoreCase("")) {
                itemBinding.imageTitle.setVisibility(View.VISIBLE);
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                itemBinding.imageTitle.setVisibility(View.GONE);
            }
        } catch (Exception ignored) {
        }

        try {
            String imageUrl;
            if (videos.get(i).getAssetType().equalsIgnoreCase(AppConstants.PERSON)) {
                imageUrl = getImageUrl(Constants.TEN_INTO_TEN, videos.get(i).getImages(), itemBinding.itemImage.getContext());
            } else {
                imageUrl = getImageUrl(Constants.ONE_INTO_ONE, videos.get(i).getImages(), itemBinding.itemImage.getContext());
            }
            videos.get(i).setPosterURL(imageUrl);
            ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadCircleImageTo(itemBinding.itemImage, imageUrl);
//            if (!videos.get(i).getPosterURL().equalsIgnoreCase("")) {
//                ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadCircleImageTo(itemBinding.itemImage, AppCommonMethod.getListCIRCLEImage(videos.get(i).getPosterURL(), itemBinding.itemImage.getContext()));
//            } else {
//                itemBinding.itemImage.setImageResource(R.color.placeholder_bg); // Use a placeholder image if there's no URL
//            }
        } catch (Exception ignored) {
        }


        try {
            AppCommonMethod.handleTitleDesc(itemBinding.titleLayout, itemBinding.tvTitle, itemBinding.tvDescription, baseCategory);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
        } catch (Exception ignored) {
            Log.d("setLargeValues", "setLargeValues: " + ignored);
        }
    }

    private void setNomalValues(CircleItemBinding itemBinding, int i) {
        itemBinding.setPlaylistItem(videos.get(i));
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i);

        });

        if (videos.get(i).getCustomDataV3() != null && videos.get(i).getCustomDataV3().getIsExclusive() != null) {
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            } else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        } else {
            itemBinding.exclusive.setVisibility(View.GONE);
        }

        itemBinding.imageTitle.setVisibility(View.GONE);
        itemBinding.itemImage.setImageResource(R.color.placeholder_bg); // Use a placeholder image

        String imageUrl;
        if (videos.get(i).getAssetType().equalsIgnoreCase(AppConstants.PERSON)) {
            imageUrl = getImageUrl(Constants.TEN_INTO_TEN, videos.get(i).getImages(), itemBinding.itemImage.getContext());
        } else {
            imageUrl = getImageUrl(Constants.ONE_INTO_ONE, videos.get(i).getImages(), itemBinding.itemImage.getContext());
        }
        videos.get(i).setPosterURL(imageUrl);
        ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadCircleImageTo(itemBinding.itemImage, imageUrl);
//            if (!videos.get(i).getPosterURL().equalsIgnoreCase("")) {
//                ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadCircleImageTo(itemBinding.itemImage, AppCommonMethod.getListCIRCLEImage(videos.get(i).getPosterURL(), itemBinding.itemImage.getContext()));
//            } else {
//                itemBinding.itemImage.setImageResource(R.color.placeholder_bg); // Use a placeholder image if there's no URL
//            }
        try {
            assert imageUrl != null;
            if (imageUrl.equalsIgnoreCase("")) {
                itemBinding.imageTitle.setVisibility(View.VISIBLE);
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                itemBinding.imageTitle.setVisibility(View.GONE);
            }
        } catch (Exception ignored) {
        }


        try {
            AppCommonMethod.handleTitleDesc(itemBinding.titleLayout, itemBinding.tvTitle, itemBinding.tvDescription, baseCategory);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
        } catch (Exception ignored) {
            Log.d("setLargeValues", "setLargeValues: " + ignored);
        }


    }

    private String getForCircularView(String imageAspectRatio, List<ImagesItem> images) {
        String type = imageAspectRatio;
        if (images.get(0).getImageContent().getImageType().contains("10")) {
            type = images.get(0).getImageContent().getImageType();
        }
        return type;
    }

    public void itemClick(int position) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        listner.railItemClick(railCommonData, position);
    }

    @Override
    public int getItemCount() {

        return videos.size();
    }

    public class SingleItemRowHolder extends RecyclerView.ViewHolder {

        final CircleItemBinding circularItemBinding;

        SingleItemRowHolder(CircleItemBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;
        }

    }

    public class NormalHolder extends RecyclerView.ViewHolder {

        final CircleItemBinding circularItemBinding;

        NormalHolder(CircleItemBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;

        }

    }

    public class SmallHolder extends RecyclerView.ViewHolder {

        final CircleItemSmallBinding circularItemBinding;

        SmallHolder(CircleItemSmallBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;

        }

    }

    public class LargeHolder extends RecyclerView.ViewHolder {

        final CircleItemLargeBinding circularItemBinding;

        LargeHolder(CircleItemLargeBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;

        }

    }


}
