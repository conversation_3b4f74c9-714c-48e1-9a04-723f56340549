package com.enveu.adapters.commonRails

import android.annotation.SuppressLint
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner
import com.enveu.databinding.LayoutHeroCircularItemBinding
import com.enveu.databinding.LayoutHeroLandscapeItemBinding
import com.enveu.databinding.LayoutHeroPosterItemBinding
import com.enveu.databinding.LayoutHeroPotraitItemBinding
import com.enveu.databinding.LayoutHeroSquareItemBinding
import com.enveu.utils.constants.AppConstants

class CommonHeroRailAdapter(
    private val item: RailCommonData,
    private val listner: CommonRailtItemClickListner
) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private val viewType = item.railType
    private var mLastClickTime: Long = 0

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        when (viewType) {
            AppConstants.HERO_CIR_CIRCLE -> {
                val itemBinding = DataBindingUtil.inflate<LayoutHeroCircularItemBinding>(
                    LayoutInflater.from(parent.context),
                    R.layout.layout_hero_circular_item,
                    parent,
                    false
                )
                return CircularHeroHolder(itemBinding)
            }

            AppConstants
                .HERO_LDS_LANDSCAPE -> {
                val itemBinding1 = DataBindingUtil.inflate<LayoutHeroLandscapeItemBinding>(
                    LayoutInflater.from(parent.context),
                    R.layout.layout_hero_landscape_item,
                    parent,
                    false
                )
                return LandscapeHeroHolder(itemBinding1)
            }

            AppConstants
                .HERO_PR_POTRAIT -> {
                val itemBinding2 = DataBindingUtil.inflate<LayoutHeroPotraitItemBinding>(
                    LayoutInflater.from(parent.context),
                    R.layout.layout_hero_potrait_item,
                    parent,
                    false
                )
                return PotraitHeroHolder(itemBinding2)
            }

            AppConstants
                .HERO_PR_POSTER -> {
                val itemBinding6 = DataBindingUtil.inflate<LayoutHeroPosterItemBinding>(
                    LayoutInflater.from(parent.context),
                    R.layout.layout_hero_poster_item,
                    parent,
                    false
                )
                return PosterHeroHolder(itemBinding6)
            }

            else -> {
                val itemBinding7 = DataBindingUtil.inflate<LayoutHeroSquareItemBinding>(
                    LayoutInflater.from(parent.context),
                    R.layout.layout_hero_square_item,
                    parent,
                    false
                )
                return SquareHeroHolder(itemBinding7)
            }
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        @SuppressLint("RecyclerView") position: Int
    ) {
        if (holder is SquareHeroHolder) {
            holder.itemBinding.playlistItem = item.enveuVideoItemBeans[0]

            if (item.enveuVideoItemBeans[position].customData != null) {
                if (item.enveuVideoItemBeans[position].customData.isIs_exclusive) {
                    holder.itemBinding.exclusive!!.visibility =
                        View.VISIBLE
                }
            }

            holder.itemBinding.heroImage.setOnClickListener {
                itemClick(
                    position
                )
            }
        } else if (holder is PosterHeroHolder) {
            holder.itemBinding.playlistItem = item.enveuVideoItemBeans[0]

            if (item.enveuVideoItemBeans[position].customData != null) {
                if (item.enveuVideoItemBeans[position].customData.isIs_exclusive) {
                    holder.itemBinding.exclusive.visibility =
                        View.VISIBLE
                }
            }
            holder.itemBinding.heroImage.setOnClickListener {
                itemClick(
                    position
                )
            }
        } else if (holder is PotraitHeroHolder) {
            holder.itemBinding.playlistItem = item.enveuVideoItemBeans[0]

            if (item.enveuVideoItemBeans[position].customData.isIs_exclusive) {
                (holder as PosterHeroHolder).itemBinding.exclusive.visibility =
                    View.VISIBLE
            }
            holder.itemBinding.heroImage.setOnClickListener {
                itemClick(
                    position
                )
            }
        } else if (holder is CircularHeroHolder) {
            holder.itemBinding.playlistItem =
                item.enveuVideoItemBeans[0]

            if (item.enveuVideoItemBeans[position].customData.isIs_exclusive) {
                holder.itemBinding.exclusive.visibility =
                    View.VISIBLE
            }
            holder.itemBinding.heroImage.setOnClickListener {
                itemClick(
                    position
                )
            }
        } else if (holder is LandscapeHeroHolder) {
            holder.itemBinding.playlistItem =
                item.enveuVideoItemBeans[0]

            if (item.enveuVideoItemBeans[position].customData != null && item.enveuVideoItemBeans[position].customData.isIs_exclusive) {
                holder.itemBinding.exclusive.visibility =
                    View.VISIBLE
            }
            holder.itemBinding.heroImage.setOnClickListener {
                itemClick(
                    position
                )
            }
        }
    }


    fun itemClick(position: Int) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
            return
        }
        mLastClickTime = SystemClock.elapsedRealtime()
        listner.railItemClick(item, position)
    }

    override fun getItemViewType(position: Int): Int {
        return item.railType
    }


    override fun getItemCount(): Int {
        return 1
    }

    inner class PotraitHeroHolder internal constructor(var itemBinding: LayoutHeroPotraitItemBinding) :
        RecyclerView.ViewHolder(itemBinding.root)

    inner class LandscapeHeroHolder internal constructor(var itemBinding: LayoutHeroLandscapeItemBinding) :
        RecyclerView.ViewHolder(itemBinding.root)

    inner class CircularHeroHolder internal constructor(var itemBinding: LayoutHeroCircularItemBinding) :
        RecyclerView.ViewHolder(itemBinding.root)

    inner class SquareHeroHolder internal constructor(var itemBinding: LayoutHeroSquareItemBinding) :
        RecyclerView.ViewHolder(itemBinding.root)

    inner class PosterHeroHolder internal constructor(var itemBinding: LayoutHeroPosterItemBinding) :
        RecyclerView.ViewHolder(itemBinding.root)
}
