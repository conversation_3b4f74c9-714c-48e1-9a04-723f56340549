package com.enveu.adapters.commonRails;

import static com.enveu.utils.ViewExtensionKt.getImageUrl;

import android.os.SystemClock;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.enveu.R;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner;
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory;
import com.enveu.client.enums.RailCardSize;
import com.enveu.databinding.LandscapeItemBinding;
import com.enveu.databinding.LandscapeItemLargeBinding;
import com.enveu.databinding.LandscapeItemSmallBinding;
import com.enveu.utils.Constants;
import com.enveu.utils.Logger;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.helpers.ImageHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import io.reactivex.annotations.NonNull;


public class CommonLandscapeRailAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private long mLastClickTime = 0;
    private final RailCommonData railCommonData;
    private List<EnveuVideoItemBean> videos;
    private final CommonRailtItemClickListner listner;
    private final int pos;
    BaseCategory baseCategory;
    public CommonLandscapeRailAdapter(RailCommonData railCommonData, int position, CommonRailtItemClickListner listner, BaseCategory baseCat) {
        this.railCommonData = railCommonData;
        this.videos = new ArrayList<>();
        this.videos = railCommonData.getEnveuVideoItemBeans();
        this.listner = listner;
        this.pos=position;
        this.baseCategory=baseCat;
    }


    @androidx.annotation.NonNull
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@androidx.annotation.NonNull @NonNull ViewGroup parent, int i) {
        if (baseCategory!=null && baseCategory.getRailCardSize()!=null) {
            if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.NORMAL.name())) {
                LandscapeItemBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.landscape_item, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new NormalHolder(binding);
            }
            else  if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.SMALL.name())) {
                LandscapeItemSmallBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.landscape_item_small, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new SmallHolder(binding);
            }
            else  if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.LARGE.name())) {
                LandscapeItemLargeBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.landscape_item_large, parent, false);
                return new LargeHolder(binding);
            }else {
                LandscapeItemBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.landscape_item, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new NormalHolder(binding);
            }
        }else {
            LandscapeItemBinding binding = DataBindingUtil.inflate(
                    LayoutInflater.from(parent.getContext()),
                    R.layout.landscape_item, parent, false);
            binding.setColorsData(ColorsHelper.INSTANCE);
            return new NormalHolder(binding);
        }

    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int i) {
        Log.w("callinGonBind","in");
        if (holder instanceof NormalHolder) {
            setNomalValues((( NormalHolder) holder).circularItemBinding,i);
        }
        else if (holder instanceof  SmallHolder) {
            setSmallValues((( SmallHolder) holder).circularItemBinding,i);
        }
        else if (holder instanceof  LargeHolder) {
            setLargeValues((( LargeHolder) holder).circularItemBinding,i);
        }
        else {
            setNomalValues((( NormalHolder) holder).circularItemBinding,i);
        }

    }

    private void setLargeValues(LandscapeItemLargeBinding itemBinding, int i) {
        itemBinding.setPlaylistItem(videos.get(i));
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i);

        });

        if (videos.get(i).getCustomDataV3() != null  && videos.get(i).getCustomDataV3().getIsExclusive() != null){
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            }
            else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        }

        try{
            if (videos.get(i).getPosterURL().equalsIgnoreCase("") ) {
                Objects.requireNonNull(itemBinding.imageTitle).setVisibility(View.VISIBLE);
                itemBinding.imageTitle.bringToFront();
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                Objects.requireNonNull(itemBinding.imageTitle).setVisibility(View.GONE);
            }
        } catch (Exception ignored) {

        }


        try {
            AppCommonMethod.handleTitleDesc(itemBinding.titleLayout,itemBinding.tvTitle,itemBinding.tvDescription,baseCategory);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
        }catch (Exception ignored){

        }
        try {
            if (videos.get(i).getVideoPosition() > 0) {
                AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, true);
                int totalDuration = videos.get(i).getVideoDetails().getDuration() / 1000;
                int currentPosition = (int) videos.get(i).getVideoPosition();
                int percentagePlayed = (currentPosition*100/totalDuration);
                itemBinding.setProgress((int) percentagePlayed);
            } else {
                AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, false);
            }
        }catch (Exception e){
            Logger.w(e);
        }

        try {
            videos.get(i).setPosterURL(getImageUrl(Constants.SIXTEEN_INTO_NINE, videos.get(i).getImages(), itemBinding.itemImage.getContext()));
            ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadListImage(itemBinding.itemImage, getImageUrl(Constants.SIXTEEN_INTO_NINE, videos.get(i).getImages(), itemBinding.itemImage.getContext()));
        }catch (Exception ignored){

        }
    }

    private void setSmallValues(LandscapeItemSmallBinding itemBinding, int i) {
        itemBinding.setPlaylistItem(videos.get(i));
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i);

        });

        if (videos.get(i).getCustomDataV3() != null  && videos.get(i).getCustomDataV3().getIsExclusive() != null){
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            }
            else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        }
        else {
            itemBinding.exclusive.setVisibility(View.GONE);
        }

        try{
            if (videos.get(i).getPosterURL().equalsIgnoreCase("") ) {
                Objects.requireNonNull(itemBinding.imageTitle).setVisibility(View.VISIBLE);
                itemBinding.imageTitle.bringToFront();
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                Objects.requireNonNull(itemBinding.imageTitle).setVisibility(View.GONE);
            }
        } catch (Exception e) {

        }

        try {
            AppCommonMethod.handleTitleDesc(itemBinding.titleLayout,itemBinding.tvTitle,itemBinding.tvDescription,baseCategory);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
        }catch (Exception ignored){

        }


        try {
            if (videos.get(i).getVideoPosition() > 0) {
                AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, true);
                int totalDuration = videos.get(i).getVideoDetails().getDuration() / 1000;
                int currentPosition = (int) videos.get(i).getVideoPosition();
                int percentagePlayed = (currentPosition*100/totalDuration);
                itemBinding.setProgress((int) percentagePlayed);
            } else {
                AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, false);
            }
        }catch (Exception e){
            Logger.w(e);
        }

        try {
            videos.get(i).setPosterURL(getImageUrl(Constants.SIXTEEN_INTO_NINE, videos.get(i).getImages(), itemBinding.itemImage.getContext()));
            ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadListImage(itemBinding.itemImage, getImageUrl(Constants.SIXTEEN_INTO_NINE, videos.get(i).getImages(), itemBinding.itemImage.getContext()));
        }catch (Exception e){
            Logger.w(e);
        }
    }

    private void setNomalValues(LandscapeItemBinding itemBinding, int i) {
        itemBinding.setPlaylistItem(videos.get(i));
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i);

        });

        if (videos.get(i).getCustomDataV3() != null  && videos.get(i).getCustomDataV3().getIsExclusive() != null){
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            }
            else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        }
        else {
            itemBinding.exclusive.setVisibility(View.GONE);
        }

        try{
            if (videos.get(i).getPosterURL().equalsIgnoreCase("") ) {
                Objects.requireNonNull(itemBinding.imageTitle).setVisibility(View.GONE);
                itemBinding.imageTitle.bringToFront();
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                Objects.requireNonNull(itemBinding.imageTitle).setVisibility(View.GONE);
            }
        } catch (Exception ignored) {

        }

        try {
            AppCommonMethod.handleTitleDesc(itemBinding.titleLayout,itemBinding.tvTitle,itemBinding.tvDescription,baseCategory);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
        }catch (Exception ignored){

        }
        try {
        if (videos.get(i).getVideoPosition() > 0) {
            AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, true);
            int totalDuration = videos.get(i).getVideoDetails().getDuration() / 1000;
            int currentPosition = (int) videos.get(i).getVideoPosition();
            int percentagePlayed = (currentPosition*100/totalDuration);
            itemBinding.setProgress((int) percentagePlayed);
        } else {
            AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, false);
        }
        }catch (Exception e){
            Logger.w(e);
        }

        try {
            videos.get(i).setPosterURL(getImageUrl(Constants.SIXTEEN_INTO_NINE, videos.get(i).getImages(), itemBinding.itemImage.getContext()));
            ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadListImage(itemBinding.itemImage, getImageUrl(Constants.SIXTEEN_INTO_NINE, videos.get(i).getImages(), itemBinding.itemImage.getContext()));
        }catch (Exception e){
            Logger.w(e);
        }
    }

    public void itemClick(int position) {
        Logger.d("clickedfrom list " + position);

        if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        listner.railItemClick(railCommonData, position);
    }

    @Override
    public int getItemCount() {
        return videos.size();
    }

    public class NormalHolder extends RecyclerView.ViewHolder {

        final LandscapeItemBinding circularItemBinding;

        NormalHolder(LandscapeItemBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;

        }

    }

    public class SmallHolder extends RecyclerView.ViewHolder {

        final LandscapeItemSmallBinding circularItemBinding;

        SmallHolder(LandscapeItemSmallBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;

        }

    }

    public class LargeHolder extends RecyclerView.ViewHolder {

        final LandscapeItemLargeBinding circularItemBinding;

        LargeHolder(LandscapeItemLargeBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;

        }

    }


}
