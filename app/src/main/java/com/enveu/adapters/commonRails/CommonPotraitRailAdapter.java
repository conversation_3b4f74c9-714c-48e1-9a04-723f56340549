package com.enveu.adapters.commonRails;

import static com.enveu.utils.ViewExtensionKt.getImageUrl;

import android.content.Context;
import android.os.SystemClock;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.enveu.R;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner;
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory;
import com.enveu.client.enums.RailCardSize;
import com.enveu.databinding.PotraitItemBinding;
import com.enveu.databinding.PotraitItemLargeBinding;
import com.enveu.databinding.PotraitItemSmallBinding;
import com.enveu.utils.Constants;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.ImageHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import io.reactivex.annotations.NonNull;


public class CommonPotraitRailAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private long mLastClickTime = 0;
    private final RailCommonData railCommonData;
    private List<EnveuVideoItemBean> videos;
    private final CommonRailtItemClickListner listner;
    private final int pos;
    BaseCategory baseCategory;
    public CommonPotraitRailAdapter(RailCommonData railCommonData, int position, CommonRailtItemClickListner listner,BaseCategory baseCat) {
        this.railCommonData = railCommonData;
        this.videos = new ArrayList<>();
        this.videos = railCommonData.getEnveuVideoItemBeans();
        this.listner = listner;
        this.pos=position;
        this.baseCategory=baseCat;
    }
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        if (baseCategory!=null && baseCategory.getRailCardSize()!=null) {
            if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.NORMAL.name())) {
                PotraitItemBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.potrait_item, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new NormalHolder(binding);
            }
            else  if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.SMALL.name())) {
                PotraitItemSmallBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.potrait_item_small, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new SmallHolder(binding);
            }
            else  if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.LARGE.name())) {
                PotraitItemLargeBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.potrait_item_large, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new LargeHolder(binding);
            }
            else {
                PotraitItemBinding binding = DataBindingUtil.inflate(
                        LayoutInflater.from(parent.getContext()),
                        R.layout.potrait_item, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new NormalHolder(binding);
            }
        }else {
            PotraitItemBinding binding = DataBindingUtil.inflate(
                    LayoutInflater.from(parent.getContext()),
                    R.layout.potrait_item, parent, false);
            binding.setColorsData(ColorsHelper.INSTANCE);
            return new NormalHolder(binding);
        }

    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int i) {

        if (holder instanceof   NormalHolder) {
            setNomalValues(((  NormalHolder) holder).circularItemBinding,i);
        }
        else if (holder instanceof   SmallHolder) {
            setSmallValues(((  SmallHolder) holder).circularItemBinding,i);
        }
        else if (holder instanceof   LargeHolder) {
            setLargeValues(((LargeHolder) holder).circularItemBinding,i);
        }
        else {
            setNomalValues(((  NormalHolder) holder).circularItemBinding,i);
        }
    }

    private void setLargeValues(PotraitItemLargeBinding itemBinding, int i) {
        if (videos.get(i).getAssetType().equalsIgnoreCase(AppConstants.VIDEO) && videos.get(i).getVideoDetails().getVideoType().equalsIgnoreCase(AppConstants.REEL)){
            itemBinding.overlayLayout.setVisibility(View.VISIBLE);
            itemBinding.imageCard.setRadius(8f);
        } else {
            itemBinding.overlayLayout.setVisibility(View.GONE);
            itemBinding.imageCard.setRadius(0f);
            itemBinding.titleLayout.setVisibility(View.VISIBLE);
            itemBinding.tvTitle.setVisibility(View.VISIBLE);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());

//            AppCommonMethod.handleTitleDesc(itemBinding.titleLayout,itemBinding.tvTitle,itemBinding.tvDescription,baseCategory);
        }

        EnveuVideoItemBean enveuVideoItemBean=videos.get(i);
        itemBinding.setPlaylistItem(videos.get(i));
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i,itemBinding.rippleView.getContext());

        });


        try{
            if (videos.get(i).getPosterURL().equalsIgnoreCase("") ) {
                itemBinding.imageTitle.setVisibility(View.VISIBLE);
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                itemBinding.imageTitle.setVisibility(View.GONE);
            }
        } catch (Exception e) {

        }

        itemBinding.viewCount.setText(String.valueOf(videos.get(i).getPlayCount()));
        int playCount=  videos.get(i).getPlayCount();
        if (playCount>0){
            itemBinding.viewCount.setVisibility(View.VISIBLE);
        } else {
            itemBinding.viewCount.setVisibility(View.GONE);
        }

        if (videos.get(i).getCustomDataV3() != null  && videos.get(i).getCustomDataV3().getIsExclusive() != null){
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            }
            else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        }
        else {
            itemBinding.exclusive.setVisibility(View.GONE);
        }

        try {
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
        }catch (Exception ignored){

        }
        try {
            ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadPortraitImage(itemBinding.itemImage, getImageUrl(Constants.NINE_INTO_SIXTEEN, videos.get(i).getImages(), itemBinding.itemImage.getContext()));
//            if (enveuVideoItemBean.getPosterURL() != null && !enveuVideoItemBean.getPosterURL().equalsIgnoreCase("")) {
//                ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadPortraitImage(itemBinding.itemImage, AppCommonMethod.getListPRImage(enveuVideoItemBean.getPosterURL(), itemBinding.itemImage.getContext()));
//            }
        }catch (Exception ignored){

        }
    }

    private void setSmallValues(PotraitItemSmallBinding itemBinding, int i) {
        if (videos.get(i).getAssetType().equalsIgnoreCase(AppConstants.VIDEO) && videos.get(i).getVideoDetails().getVideoType().equalsIgnoreCase(AppConstants.REEL)){
            itemBinding.overlayLayout.setVisibility(View.VISIBLE);
            itemBinding.imageCard.setRadius(24f);
        } else {
            itemBinding.overlayLayout.setVisibility(View.GONE);
            itemBinding.imageCard.setRadius(0f);
            itemBinding.titleLayout.setVisibility(View.VISIBLE);
            itemBinding.tvTitle.setVisibility(View.VISIBLE);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
        }

        EnveuVideoItemBean enveuVideoItemBean=videos.get(i);
        itemBinding.setPlaylistItem(videos.get(i));
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i, itemBinding.rippleView.getContext());

        });

        try{
            if (videos.get(i).getPosterURL().equalsIgnoreCase("") ) {
                itemBinding.imageTitle.setVisibility(View.VISIBLE);
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                itemBinding.imageTitle.setVisibility(View.GONE);
            }
        } catch (Exception e) {

        }
        itemBinding.viewCount.setText(String.valueOf(videos.get(i).getPlayCount()));
        int playCount=  videos.get(i).getPlayCount();
        if (playCount>0){
            itemBinding.viewCount.setVisibility(View.VISIBLE);
        } else {
            itemBinding.viewCount.setVisibility(View.GONE);
        }

        if (videos.get(i).getCustomDataV3() != null  && videos.get(i).getCustomDataV3().getIsExclusive() != null){
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            }
            else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        }
        else {
            itemBinding.exclusive.setVisibility(View.GONE);
        }


        try {
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
        }catch (Exception ignored){

        }
        try {
            ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadPortraitImage(itemBinding.itemImage, getImageUrl(Constants.NINE_INTO_SIXTEEN, videos.get(i).getImages(), itemBinding.itemImage.getContext()));
//            if (enveuVideoItemBean.getPosterURL() != null && !enveuVideoItemBean.getPosterURL().equalsIgnoreCase("")) {
//                ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadPortraitImage(itemBinding.itemImage, AppCommonMethod.getListPRImage(enveuVideoItemBean.getPosterURL(), itemBinding.itemImage.getContext()));
//            }
        }catch (Exception ignored){

        }
    }

    private void setNomalValues(PotraitItemBinding itemBinding, int i) {
        if (videos.get(i).getAssetType().equalsIgnoreCase(AppConstants.VIDEO) && videos.get(i).getVideoDetails().getVideoType().equalsIgnoreCase(AppConstants.REEL)){
            itemBinding.overlayLayout.setVisibility(View.VISIBLE);
            if (itemBinding.imageCard != null) {
                itemBinding.imageCard.setRadius(24f);
            }
        } else {
            itemBinding.overlayLayout.setVisibility(View.GONE);
            if (itemBinding.imageCard != null) {
                itemBinding.imageCard.setRadius(0f);
            }
            itemBinding.titleLayout.setVisibility(View.VISIBLE);
            itemBinding.tvTitle.setVisibility(View.VISIBLE);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
        }

        EnveuVideoItemBean enveuVideoItemBean=videos.get(i);
        itemBinding.setPlaylistItem(videos.get(i));
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i, itemBinding.rippleView.getContext());

        });

        try{
            if (videos.get(i).getPosterURL().equalsIgnoreCase("") ) {
                Objects.requireNonNull(itemBinding.imageTitle).setVisibility(View.VISIBLE);
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                Objects.requireNonNull(itemBinding.imageTitle).setVisibility(View.GONE);
            }
        } catch (Exception ignored) {

        }

        if (itemBinding.exclusive != null) {
            if (videos.get(i).getCustomDataV3() != null && videos.get(i).getCustomDataV3().getIsExclusive() != null) {
                if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                    itemBinding.exclusive.setVisibility(View.VISIBLE);
                } else {
                    itemBinding.exclusive.setVisibility(View.GONE);
                }
            } else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        }
        itemBinding.viewCount.setText(String.valueOf(videos.get(i).getPlayCount()));
        int playCount=  videos.get(i).getPlayCount();
        if (playCount>0){
            itemBinding.viewCount.setVisibility(View.VISIBLE);
        } else {
            itemBinding.viewCount.setVisibility(View.GONE);
        }

        try {
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
        }catch (Exception ignored){

        }
        try {
            ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadPortraitImage(itemBinding.itemImage, getImageUrl(Constants.NINE_INTO_SIXTEEN, videos.get(i).getImages(), itemBinding.itemImage.getContext()));
//            if (enveuVideoItemBean.getPosterURL() != null && !enveuVideoItemBean.getPosterURL().equalsIgnoreCase("")) {
//                ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadPortraitImage(itemBinding.itemImage, AppCommonMethod.getListPRImage(enveuVideoItemBean.getPosterURL(), itemBinding.itemImage.getContext()));
//            }
        }catch (Exception ignored){

        }

    }

    public void itemClick(int position, Context context) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        listner.railItemClick(railCommonData, position);
    }

    @Override
    public int getItemCount() {
        return videos.size();
    }

    public static class NormalHolder extends RecyclerView.ViewHolder {

        final PotraitItemBinding circularItemBinding;

        NormalHolder(PotraitItemBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;

        }

    }

    public static class SmallHolder extends RecyclerView.ViewHolder {

        final PotraitItemSmallBinding circularItemBinding;

        SmallHolder(PotraitItemSmallBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;

        }

    }

    public static class LargeHolder extends RecyclerView.ViewHolder {

        final PotraitItemLargeBinding circularItemBinding;

        LargeHolder(PotraitItemLargeBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;

        }

    }


}
