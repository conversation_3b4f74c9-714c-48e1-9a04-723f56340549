package com.enveu.adapters.commonRails;

import android.os.SystemClock;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.enveu.R;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner;
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory;
import com.enveu.client.enums.RailCardSize;
import com.enveu.databinding.SquareItemBinding;
import com.enveu.databinding.SquareItemLargeBinding;
import com.enveu.databinding.SquareItemSmallBinding;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.helpers.ImageHelper;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.annotations.NonNull;


public class CommonSquareRailAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private long mLastClickTime = 0;
    private final RailCommonData railCommonData;
    private List<EnveuVideoItemBean> videos;
    private final CommonRailtItemClickListner listner;
    BaseCategory baseCategory;
    public CommonSquareRailAdapter(RailCommonData railCommonData, CommonRailtItemClickListner listner, BaseCategory baseCat) {
        this.railCommonData = railCommonData;
        this.videos = new ArrayList<>();
        this.videos = railCommonData.getEnveuVideoItemBeans();
        this.listner = listner;
        this.baseCategory=baseCat;
    }
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        if (baseCategory!=null && baseCategory.getRailCardSize()!=null) {
            if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.NORMAL.name())) {
                SquareItemBinding binding = DataBindingUtil.inflate(LayoutInflater.from(parent.getContext()), R.layout.square_item, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new NormalHolder(binding);
            }
            else if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.SMALL.name())) {
                SquareItemSmallBinding binding = DataBindingUtil.inflate(LayoutInflater.from(parent.getContext()), R.layout.square_item_small, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new SmallHolder(binding);
            }
            else  if (baseCategory.getRailCardSize().equalsIgnoreCase(RailCardSize.LARGE.name())) {
                SquareItemLargeBinding binding = DataBindingUtil.inflate(LayoutInflater.from(parent.getContext()), R.layout.square_item_large, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new LargeHolder(binding);
            }
            else {
                SquareItemBinding binding = DataBindingUtil.inflate(LayoutInflater.from(parent.getContext()), R.layout.square_item, parent, false);
                binding.setColorsData(ColorsHelper.INSTANCE);
                return new NormalHolder(binding);
            }
        }else {
            SquareItemBinding binding = DataBindingUtil.inflate(LayoutInflater.from(parent.getContext()), R.layout.square_item, parent, false);
            binding.setColorsData(ColorsHelper.INSTANCE);
            return new NormalHolder(binding);
        }

    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int i) {
        if (holder instanceof NormalHolder) {
            setNomalValues(((NormalHolder) holder).circularItemBinding,i);
        }
        else if (holder instanceof SmallHolder) {
            setSmallValues(((SmallHolder) holder).circularItemBinding,i);
        }
        else if (holder instanceof LargeHolder) {
            setLargeValues(((LargeHolder) holder).circularItemBinding,i);
        }
        else {
            setNomalValues(((NormalHolder) holder).circularItemBinding,i);
        }

    }

    private void setLargeValues(SquareItemLargeBinding itemBinding, int i) {
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i);
        });

        itemBinding.imageTitle.setVisibility(View.GONE);
        itemBinding.itemImage.setImageResource(R.color.placeholder_bg); // Use a placeholder image

        if (videos.get(i).getCustomDataV3() != null  && videos.get(i).getCustomDataV3().getIsExclusive() != null){
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            }
            else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        }
        else {
            itemBinding.exclusive.setVisibility(View.GONE);
        }

        try {
            if (videos.get(i).getPosterURL().equalsIgnoreCase("")) {
                itemBinding.imageTitle.setVisibility(View.VISIBLE);
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                itemBinding.imageTitle.setVisibility(View.GONE);
            }
        } catch (Exception ignored) {
        }

        try {
            if (!videos.get(i).getPosterURL().equalsIgnoreCase("")) {
                ImageHelper.getInstance(itemBinding.itemImage.getContext()).loadListSQRImage(itemBinding.itemImage, AppCommonMethod.getListSQRImage(videos.get(i).getPosterURL(), itemBinding.itemImage.getContext()));
            } else {
                itemBinding.itemImage.setImageResource(R.color.placeholder_bg); // Use a placeholder image if there's no URL
            }
        } catch (Exception ignored) {
        }

        try {
            AppCommonMethod.handleTitleDesc(itemBinding.titleLayout,itemBinding.tvTitle,itemBinding.tvDescription,baseCategory);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
            itemBinding.tvDescription.setVisibility(View.GONE);
        }catch (Exception ignored){
            Log.d("setLargeValues", "setLargeValues: "+ignored);
        }
    }

    private void setSmallValues(SquareItemSmallBinding itemBinding, int i) {
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i);
        });

        itemBinding.imageTitle.setVisibility(View.GONE);
        itemBinding.itemImage.setImageResource(R.color.placeholder_bg);
        // Use a placeholder image

        if (videos.get(i).getCustomDataV3() != null  && videos.get(i).getCustomDataV3().getIsExclusive() != null){
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            }
            else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        }
        else {
            itemBinding.exclusive.setVisibility(View.GONE);
        }

        try {
            if (videos.get(i).getPosterURL().equalsIgnoreCase("")) {
                itemBinding.imageTitle.setVisibility(View.VISIBLE);
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                itemBinding.imageTitle.setVisibility(View.GONE);
            }
        } catch (Exception ignored) {
        }

        try {
            if (!videos.get(i).getPosterURL().equalsIgnoreCase("")) {
                ImageHelper.getInstance(itemBinding.itemImage.getContext())
                        .loadListSQRImage(itemBinding.itemImage, AppCommonMethod.getListSQRImage(videos.get(i).getPosterURL(), itemBinding.itemImage.getContext()));
            } else {
                itemBinding.itemImage.setImageResource(R.color.placeholder_bg); // Use a placeholder image if there's no URL
            }
        } catch (Exception ignored) {
        }

        try {
            AppCommonMethod.handleTitleDesc(itemBinding.titleLayout,itemBinding.tvTitle,itemBinding.tvDescription,baseCategory);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
            itemBinding.tvDescription.setVisibility(View.GONE);
        }catch (Exception ignored){

        }
    }

    private void setNomalValues(SquareItemBinding itemBinding, int i) {
        itemBinding.rippleView.setOnClickListener(v -> {
            itemClick(i);
        });

        if (videos.get(i).getCustomDataV3() != null  && videos.get(i).getCustomDataV3().getIsExclusive() != null){
            if (videos.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                itemBinding.exclusive.setVisibility(View.VISIBLE);
            }
            else {
                itemBinding.exclusive.setVisibility(View.GONE);
            }
        }
        else {
            itemBinding.exclusive.setVisibility(View.GONE);
        }

        itemBinding.imageTitle.setVisibility(View.GONE);
        itemBinding.itemImage.setImageResource(R.color.placeholder_bg); // Use a placeholder image

        try {
            if (videos.get(i).getPosterURL().equalsIgnoreCase("")) {
                itemBinding.imageTitle.setVisibility(View.VISIBLE);
                itemBinding.imageTitle.setText(videos.get(i).getTitle());
            } else {
                itemBinding.imageTitle.setVisibility(View.GONE);
            }
        } catch (Exception ignored) {
        }

        try {
            if (!videos.get(i).getPosterURL().equalsIgnoreCase("")) {
                ImageHelper.getInstance(itemBinding.itemImage.getContext())
                        .loadListSQRImage(itemBinding.itemImage, AppCommonMethod.getListSQRImage(videos.get(i).getPosterURL(), itemBinding.itemImage.getContext()));
            } else {
                itemBinding.itemImage.setImageResource(R.color.placeholder_bg); // Use a placeholder image if there's no URL
            }
        } catch (Exception ignored) {
        }


        try {
            AppCommonMethod.handleTitleDesc(itemBinding.titleLayout,itemBinding.tvTitle,itemBinding.tvDescription,baseCategory);
            itemBinding.tvTitle.setText(videos.get(i).getTitle());
            itemBinding.tvDescription.setText(videos.get(i).getDescription());
            itemBinding.tvDescription.setVisibility(View.GONE);
        }catch (Exception ignored){

        }
    }

    public void itemClick(int position) {

        if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        listner.railItemClick(railCommonData, position);

    }

    @Override
    public int getItemCount() {
        return videos.size();
    }
    public class NormalHolder extends RecyclerView.ViewHolder {

        final SquareItemBinding circularItemBinding;

        NormalHolder(SquareItemBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;

        }

    }

    public class SmallHolder extends RecyclerView.ViewHolder {

        final SquareItemSmallBinding circularItemBinding;

        SmallHolder(SquareItemSmallBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;

        }

    }

    public class LargeHolder extends RecyclerView.ViewHolder {

        final SquareItemLargeBinding circularItemBinding;

        LargeHolder(SquareItemLargeBinding circularItemBind) {
            super(circularItemBind.getRoot());
            this.circularItemBinding = circularItemBind;
        }
    }
}
