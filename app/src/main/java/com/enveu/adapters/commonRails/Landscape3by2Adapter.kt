package com.enveu.adapters.commonRails

import android.graphics.Color
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory
import com.enveu.client.enums.RailCardSize
import com.enveu.databinding.Landscape3x2Binding
import com.enveu.databinding.Landscape3x2LargeBinding
import com.enveu.databinding.Landscape3x2SmallBinding
import com.enveu.utils.Logger
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppCommonMethod.Companion.getListLDSImage
import com.enveu.utils.helpers.ImageHelper

class Landscape3by2Adapter(
    private val railCommonData: RailCommonData,
    position: Int,
    listner: CommonRailtItemClickListner,
    baseCat: BaseCategory?
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private var mLastClickTime: Long = 0
    private var videos: List<EnveuVideoItemBean> = ArrayList()
    private val listner: CommonRailtItemClickListner
    val baseCategory: BaseCategory?
    var isCardTransperentBg=false

    init {
        videos = railCommonData.enveuVideoItemBeans
        this.listner = listner
        baseCategory = baseCat
    }

    override fun onCreateViewHolder(parent: ViewGroup, i: Int): RecyclerView.ViewHolder {
        return if (baseCategory != null && baseCategory.railCardSize != null) {
            if (baseCategory.railCardSize.equals(RailCardSize.NORMAL.name, ignoreCase = true)) {
                val binding = DataBindingUtil.inflate<Landscape3x2Binding>(
                    LayoutInflater.from(parent.context),
                    R.layout.landscape_3x2, parent, false
                )
                NormalHolder(binding)
            } else if (baseCategory.railCardSize.equals(
                    RailCardSize.SMALL.name,
                    ignoreCase = true
                )
            ) {
                val binding =
                    DataBindingUtil.inflate<Landscape3x2SmallBinding>(
                        LayoutInflater.from(parent.context),
                        R.layout.landscape_3x2_small, parent, false
                    )
                SmallHolder(binding)
            } else if (baseCategory.railCardSize.equals(
                    RailCardSize.LARGE.name,
                    ignoreCase = true
                )
            ) {
                val binding =
                    DataBindingUtil.inflate<Landscape3x2LargeBinding>(
                        LayoutInflater.from(parent.context),
                        R.layout.landscape_3x2_large, parent, false
                    )
                LargeHolder(binding)
            } else {
                val binding = DataBindingUtil.inflate<Landscape3x2Binding>(
                    LayoutInflater.from(parent.context),
                    R.layout.landscape_3x2, parent, false
                )
                NormalHolder(binding)
            }
        } else {
            val binding = DataBindingUtil.inflate<Landscape3x2Binding>(
                LayoutInflater.from(parent.context),
                R.layout.landscape_3x2, parent, false
            )
            NormalHolder(binding)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, i: Int) {
        baseCategory?.name?.let {
            if (it.toString().equals("Top Ten",true)){
                isCardTransperentBg=true
            }
        }
        if (holder is NormalHolder) {
            setNomalValues(holder.circularItemBinding, i)
        } else if (holder is SmallHolder) {
            setSmallValues(holder.circularItemBinding, i)
        } else if (holder is LargeHolder) {
            setLargeValues(holder.circularItemBinding, i)
        } else {
            setNomalValues((holder as NormalHolder).circularItemBinding, i)
        }
    }

    private fun setLargeValues(itemBinding: Landscape3x2LargeBinding, i: Int) {
        itemBinding.playlistItem = videos[i]
        if (isCardTransperentBg){
            itemBinding.cvLand3x2.setCardBackgroundColor(Color.parseColor("#00FFFFFF"))
        }
        if (videos[i].posterURL!= null && videos[i].posterURL.isNotEmpty()){
            ImageHelper.getInstance(itemBinding.itemImage.context).loadListImage(
                itemBinding.itemImage, AppCommonMethod.getListLDSImage(videos.get(i).getPosterURL(), itemBinding.itemImage.getContext())
            )
        }else{
            itemBinding.itemImage.setBackgroundResource(R.color.placeholder_bg)
        }
        itemBinding.rippleView.setOnClickListener { v: View? -> itemClick(i) }
        try {
            AppCommonMethod.handleTags(
                videos[i].isVIP,
                videos[i].isNewS,
                itemBinding.flExclusive,
                itemBinding.flNew,
                itemBinding.flEpisode,
                itemBinding.flNewMovie,
                videos[i].assetType
            )
        } catch (ignored: Exception) {
        }
        try {
            AppCommonMethod.handleTitleDesc(
                itemBinding.titleLayout,
                itemBinding.tvTitle,
                itemBinding.tvDescription,
                baseCategory
            )
            itemBinding.tvTitle.text = videos[i].title
            itemBinding.tvDescription.text = videos[i].description
        } catch (ignored: Exception) {
        }
        if (videos[i].videoPosition > 0) {
            AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, true)
            val totalDuration = videos[i].duration.toDouble()
            val currentPosition = videos[i].videoPosition * 1000.0
            val percentagePlayed = currentPosition / totalDuration * 100L
            itemBinding.progress = percentagePlayed.toInt()
        } else {
            AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, false)
        }
    }

    private fun setSmallValues(itemBinding: Landscape3x2SmallBinding, i: Int) {
        itemBinding.playlistItem = videos[i]
        if (isCardTransperentBg){
            itemBinding.cvLand3x2.setCardBackgroundColor(Color.parseColor("#00FFFFFF"))
        }
        if (videos[i].posterURL!= null && videos[i].posterURL.isNotEmpty()){
            ImageHelper.getInstance(itemBinding.itemImage.context).loadListImage(
                itemBinding.itemImage, videos[i].posterURL
            )
        }else{
            itemBinding.itemImage.setBackgroundResource(R.color.placeholder_bg)
        }
        itemBinding.rippleView.setOnClickListener { v: View? -> itemClick(i) }
        try {
            AppCommonMethod.handleTags(
                videos[i].isVIP,
                videos[i].isNewS,
                itemBinding.flExclusive,
                itemBinding.flNew,
                itemBinding.flEpisode,
                itemBinding.flNewMovie,
                videos[i].assetType
            )
        } catch (e: Exception) {
            Logger.w(e)
        }
        try {
            AppCommonMethod.handleTitleDesc(
                itemBinding.titleLayout,
                itemBinding.tvTitle,
                itemBinding.tvDescription,
                baseCategory
            )
            itemBinding.tvTitle.text = videos[i].title
            itemBinding.tvDescription.text = videos[i].description
        } catch (e: Exception) {
            Logger.w(e)
        }
        if (videos[i].videoPosition > 0) {
            AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, true)
            val totalDuration = videos[i].duration.toDouble()
            val currentPosition = videos[i].videoPosition * 1000.0
            val percentagePlayed = currentPosition / totalDuration * 100L
            itemBinding.progress = percentagePlayed.toInt()
        } else {
            AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, false)
        }
    }

    private fun setNomalValues(itemBinding: Landscape3x2Binding, i: Int) {
        itemBinding.playlistItem = videos[i]
        if (isCardTransperentBg){
            itemBinding.cvLand3x2.setCardBackgroundColor(Color.parseColor("#00FFFFFF"))
        }
        if (videos[i].posterURL!= null && videos[i].posterURL.isNotEmpty()){
            ImageHelper.getInstance(itemBinding.itemImage.context).loadListImage(
                itemBinding.itemImage, videos[i].posterURL
            )
        }
        itemBinding.rippleView.setOnClickListener { v: View? -> itemClick(i) }
        try {
            AppCommonMethod.handleTags(
                videos[i].isVIP,
                videos[i].isNewS,
                itemBinding.flExclusive,
                itemBinding.flNew,
                itemBinding.flEpisode,
                itemBinding.flNewMovie,
                videos[i].assetType
            )
        } catch (e: Exception) {
            Logger.w(e)
        }
        try {
            AppCommonMethod.handleTitleDesc(
                itemBinding.titleLayout,
                itemBinding.tvTitle,
                itemBinding.tvDescription,
                baseCategory
            )
            itemBinding.tvTitle.text = videos[i].title
            itemBinding.tvDescription.text = videos[i].description
        } catch (e: Exception) {
            Logger.w(e)
        }
        if (videos[i].videoPosition > 0) {
            AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, true)
            val totalDuration = videos[i].duration.toDouble()
            val currentPosition = videos[i].videoPosition * 1000.0
            val percentagePlayed = currentPosition / totalDuration * 100L
            itemBinding.progress = percentagePlayed.toInt()
        } else {
            AppCommonMethod.railBadgeVisibility(itemBinding.llContinueProgress, false)
        }
    }

    fun itemClick(position: Int) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
            return
        }
        mLastClickTime = SystemClock.elapsedRealtime()
        listner.railItemClick(railCommonData, position)
        Logger.d("clickedfrom list $position")
    }

    override fun getItemCount(): Int {
        return videos.size
    }

    class NormalHolder internal constructor(val circularItemBinding: Landscape3x2Binding) :
        RecyclerView.ViewHolder(
            circularItemBinding.root
        )

    class SmallHolder internal constructor(val circularItemBinding: Landscape3x2SmallBinding) :
        RecyclerView.ViewHolder(
            circularItemBinding.root
        )

    class LargeHolder internal constructor(val circularItemBinding: Landscape3x2LargeBinding) :
        RecyclerView.ViewHolder(
            circularItemBinding.root
        )
}