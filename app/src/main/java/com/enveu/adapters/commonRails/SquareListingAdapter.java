package com.enveu.adapters.commonRails;


import android.app.Activity;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.enveu.R;
import com.enveu.activities.listing.callback.ItemClickListener;
import com.enveu.activities.series.ui.SeriesDetailActivity;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory;
import com.enveu.databinding.SquareListingItemBinding;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.ImageHelper;
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher;

import java.util.List;


public class SquareListingAdapter extends RecyclerView.Adapter<SquareListingAdapter.SingleItemRowHolder> {

    private final String contentType;
    private final List<EnveuVideoItemBean> itemsList;
    ItemClickListener listener;
    private long mLastClickTime = 0;
    BaseCategory baseCategory;

    public SquareListingAdapter(List<EnveuVideoItemBean> itemsList, String contentType, BaseCategory baseCat,ItemClickListener callBack) {
        this.itemsList = itemsList;
        this.contentType = contentType;
        this.baseCategory = baseCat;
        listener = callBack;
    }

    public void notifydata(List<EnveuVideoItemBean> i) {
        this.itemsList.addAll(i);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public SingleItemRowHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        SquareListingItemBinding binding = DataBindingUtil.inflate(
                LayoutInflater.from(parent.getContext()),
                R.layout.square_listing_item, parent, false);
        binding.setColorsData(ColorsHelper.INSTANCE);
        return new SingleItemRowHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull SingleItemRowHolder holder, int i) {
        if (itemsList.size() > 0) {
            EnveuVideoItemBean contentsItem = itemsList.get(i);
            if (contentsItem != null) {
                holder.squareItemBinding.setPlaylistItem(contentsItem);

                try {
                    AppCommonMethod.handleTags(itemsList.get(i).getIsVIP(),itemsList.get(i).getIsNewS(),
                            holder.squareItemBinding.flExclusive,holder.squareItemBinding.flNew,holder.squareItemBinding.flEpisode,holder.squareItemBinding.flNewMovie,itemsList.get(i).getAssetType());

                }catch (Exception ignored){

                }
                if (contentType.equalsIgnoreCase(AppConstants.VOD)) {
                    holder.squareItemBinding.itemImage.setOnClickListener(view -> {
                        listener.onRowItemClicked(itemsList.get(i), i);
                    });

                    if (itemsList.get(i).getPosterURL() != null && !itemsList.get(i).getPosterURL().equalsIgnoreCase("")) {
                        ImageHelper.getInstance(holder.squareItemBinding.itemImage.getContext())
                                .loadListSQRImage(holder.squareItemBinding.itemImage, AppCommonMethod.getListSQRImage(itemsList.get(i).getPosterURL(), holder.squareItemBinding.itemImage.getContext()));
                      //  ImageHelper.getInstance(holder.squareItemBinding.itemImage.getContext()).loadImageTo(holder.squareItemBinding.itemImage,itemsList.get(i).getPosterURL());
                    }else{
                        holder.squareItemBinding.imageTitle.setText(itemsList.get(i).getTitle());
                    }
                } else if (contentType.equalsIgnoreCase(AppConstants.SERIES)) {
                    Glide.with(holder.squareItemBinding.itemImage.getContext()).load(AppCommonMethod.getImageUrl(AppConstants.SERIES, "SQUARE") + itemsList.get(i).getPosterURL()).into(holder.squareItemBinding.itemImage);
                    holder.squareItemBinding.itemImage.setOnClickListener(view -> {
                        if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
                            return;
                        }
                        mLastClickTime = SystemClock.elapsedRealtime();
                        ActivityLauncher.getInstance().seriesDetailScreen((Activity) holder.squareItemBinding.itemImage.getContext(), SeriesDetailActivity.class, itemsList.get(i).getId());

                    });

                } else if (contentType.equalsIgnoreCase(AppConstants.CAST_AND_CREW)) {
                    Glide.with(holder.squareItemBinding.itemImage.getContext()).load(AppCommonMethod.getImageUrl(AppConstants.CAST_AND_CREW, "SQUARE") + itemsList.get(i).getPosterURL()).into(holder.squareItemBinding.itemImage);

                } else if (contentType.equalsIgnoreCase(AppConstants.GENRE)) {
                    Glide.with(holder.squareItemBinding.itemImage.getContext()).load(AppCommonMethod.getImageUrl(AppConstants.GENRE, "SQUARE") + itemsList.get(i).getPosterURL()).into(holder.squareItemBinding.itemImage);
                }

                if (itemsList.get(i).getCustomDataV3() != null  && itemsList.get(i).getCustomDataV3().getIsExclusive() != null){
                    if (itemsList.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                        holder.squareItemBinding.exclusive.setVisibility(View.VISIBLE);
                    }
                    else {
                        holder.squareItemBinding.exclusive.setVisibility(View.GONE);
                    }
                }

                try {
                    AppCommonMethod.handleTitleDesc(holder.squareItemBinding.titleLayout,holder.squareItemBinding.tvTitle,holder.squareItemBinding.tvDescription,baseCategory);
                    holder.squareItemBinding.tvTitle.setText(itemsList.get(i).getTitle());
                    holder.squareItemBinding.tvDescription.setText(itemsList.get(i).getDescription());
                }catch (Exception ignored){

                }
            }
            if (itemsList.get(i).getCustomDataV3() != null  && itemsList.get(i).getCustomDataV3().getIsExclusive() != null){
                if (itemsList.get(i).getCustomDataV3().getIsExclusive().equals("true")) {
                    holder.squareItemBinding.exclusive.setVisibility(View.VISIBLE);
                }
                else {
                    holder.squareItemBinding.exclusive.setVisibility(View.GONE);
                }
            }
            else {
                holder.squareItemBinding.exclusive.setVisibility(View.GONE);
            }
        }
    }


    @Override
    public int getItemCount() {
        return (null != itemsList ? itemsList.size() : 0);
    }
    public class SingleItemRowHolder extends RecyclerView.ViewHolder {

        final SquareListingItemBinding squareItemBinding;

        SingleItemRowHolder(SquareListingItemBinding squareItemBind) {
            super(squareItemBind.getRoot());
            squareItemBinding = squareItemBind;

        }

    }


}

