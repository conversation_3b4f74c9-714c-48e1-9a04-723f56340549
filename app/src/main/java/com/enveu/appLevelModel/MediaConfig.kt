package com.enveu.appLevelModel

data class MediaConfig(
    val player: PlayerConfig,
    val bookmarkingEnabled: Boolean,
    val detailPage: DetailPageConfig
)

data class PlayerConfig(
    val name: String,
    val displaydescription: Boolean,
    val displaytitle: <PERSON><PERSON>an,
    val pipIcon: String,
    val allowFullscreen: Boolean,
    val controls: <PERSON><PERSON><PERSON>,
    val autostart: <PERSON>olean,
    val mute: <PERSON>olean,
    val playbackRateControls: <PERSON>olean,
    val backButton: Boolean,
    val width: String,
    val height: String
)

data class DetailPageConfig(
    val isDetailPage: Boolean,
    val features: FeaturesConfig,
    val trailerText: String,
    val tabs: TabsConfig
)

data class FeaturesConfig(
    val isLikeEnabled: Boolean,
    val isShareEnabled: Boolean,
    val isWatchlistEnabled: Boolean,
    val isFollowAllowed: <PERSON>olean,
    val isTrailerEnabled: <PERSON>olean,
    val watchNowEnabled: Boolean,
    val isBiographyEnabled:Boolean
)

data class TabsConfig(
    val episode: TabConfig,
    val moreLikeThis: TabConfig,
    val trailersAndMore: TabConfig,
    val clipAndMore: TabConfig,
    val similar: TabConfig,
    val extras: TabConfig,
    val overview: TabConfig,
    val fixtures: TabConfig,
    val highlights: TabConfig,
    val interviews: TabConfig,
    val replays: TabConfig
)

data class TabConfig(
    val enabled: Boolean,
    val displayLabel: String,
    val displayOrder: Int,
    val isDefault: Boolean

)




