package com.enveu.appLevelModel

import com.google.gson.annotations.SerializedName

data class MediaMappingInfo(

	@field:SerializedName("data")
	val data: List<MediaInfo?>? = null
)

data class MediaInfo(

	@field:SerializedName("pageType")
	val pageType: String? = null,

	@field:SerializedName("mediaType")
	val mediaType: String? = null,

	@field:SerializedName("isSearchable")
	val isSearchable: Boolean? = null
)
