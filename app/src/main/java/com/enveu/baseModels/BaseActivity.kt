package com.enveu.baseModels

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import com.enveu.BuildConfig
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.detail.ui.DetailActivity
import com.enveu.activities.detail.ui.EpisodeActivity
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.activities.splash.ui.ActivitySplash
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.client.baseClient.BaseClient
import com.enveu.client.baseClient.BaseConfiguration
import com.enveu.client.baseClient.BaseDeviceType
import com.enveu.client.baseClient.BaseGateway
import com.enveu.client.baseClient.BasePlatform
import com.enveu.client.baseCollection.baseCategoryServices.BaseCategoryServices
import com.enveu.client.userManagement.callBacks.LogoutCallBack
import com.enveu.client.utils.ClickHandler.disallowClick
import com.enveu.utils.BaseActivityAlertDialog
import com.enveu.utils.Logger
import com.enveu.utils.MediaTypeConstants
import com.enveu.utils.ObjectHelper
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppCommonMethod.Companion.getDeviceId
import com.enveu.utils.commonMethods.AppCommonMethod.Companion.getDeviceName
import com.enveu.utils.commonMethods.AppCommonMethod.Companion.updateLanguage
import com.enveu.utils.commonMethods.AppConfigMethod.setMediaTypeJson
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.SharedPrefHelper
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.inAppUpdate.ApplicationUpdateManager
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.facebook.login.LoginManager
import com.google.android.material.snackbar.Snackbar
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.install.model.InstallStatus
import com.google.gson.JsonObject
import org.json.JSONObject
import retrofit2.Response
import java.util.Objects

open class BaseActivity : AppCompatActivity(), BaseActivityAlertDialog.AlertDialogListener {
    var tabletSize: Boolean = false
    protected var currentLanguage: String = ""
    private var strCurrentTheme = ""
    open fun hideSoftKeyboard(view: View?) {
        val imm = view?.context?.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        Objects.requireNonNull(imm).hideSoftInputFromWindow(view?.windowToken, 0)
    }

    fun openSoftKeyboard(view: View?) {
        view?.requestFocus()
        view?.postDelayed({
            val keyboard = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
            keyboard.showSoftInput(view, 0)
        }, 200)
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    fun hitApiLogout(context: Context?, token: String?) {
        val isFacebook = KsPreferenceKeys.getInstance().appPrefLoginType

        if (isFacebook.equals(AppConstants.UserLoginType.FbLogin.toString(), ignoreCase = true)) {
            LoginManager.getInstance().logOut()
        }
        BaseCategoryServices.instance.logoutService(
            token!!, object : LogoutCallBack {
                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val jsonObject = JsonObject()
                    jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, 500)
                }

                override fun success(status: Boolean, response: Response<JsonObject>) {
                    if (status) {
                        try {
                            if (response.code() == 404) {
                                val jsonObject = JsonObject()
                                jsonObject.addProperty(
                                    AppConstants.API_RESPONSE_CODE,
                                    response.code()
                                )
                            }
                            if (response.code() == 403) {
                                val jsonObject = JsonObject()
                                jsonObject.addProperty(
                                    AppConstants.API_RESPONSE_CODE,
                                    response.code()
                                )
                            } else if (response.code() == 200) {
                                Objects.requireNonNull(response.body())?.addProperty(AppConstants.API_RESPONSE_CODE, response.code())
                            } else if (response.code() == 401) {
                                val jsonObject = JsonObject()
                                jsonObject.addProperty(
                                    AppConstants.API_RESPONSE_CODE,
                                    response.code()
                                )
                            } else if (response.code() == 500) {
                                val jsonObject = JsonObject()
                                jsonObject.addProperty(
                                    AppConstants.API_RESPONSE_CODE,
                                    response.code()
                                )
                            }
                        } catch (e: Exception) {
                            val jsonObject = JsonObject()
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code())
                        }
                    }
                }
            })
    }

    fun clearCredientials(preference: KsPreferenceKeys?) {
        try {
            val json = KsPreferenceKeys.getInstance().getString("DMS_Response", "")
            val isFacebook = preference?.appPrefLoginType
            if (isFacebook.equals(
                    AppConstants.UserLoginType.FbLogin.toString(),
                    ignoreCase = true
                )
            ) {
                LoginManager.getInstance().logOut()
            }
            val sponserID = KsPreferenceKeys.getInstance().sponsorArtistId
            val assetType = KsPreferenceKeys.getInstance().assetType
            val contentSlug = KsPreferenceKeys.getInstance().contentSlugSponser
            val strCurrentTheme = KsPreferenceKeys.getInstance().currentTheme
            val encrypt = preference?.encryptionUpdate
            val strCurrentLanguage = KsPreferenceKeys.getInstance().appLanguage
            val strSubscriptionURL = KsPreferenceKeys.getInstance().subscriptioN_BASE_URL
            val strPaymentURL = KsPreferenceKeys.getInstance().paymenT_BASE_URL
            val isBingeWatchEnable = KsPreferenceKeys.getInstance().bingeWatchEnable
            preference?.appPrefRegisterStatus = AppConstants.UserStatus.Logout.toString()
            val colorsModel = ColorsHelper.loadDataFromJson()
            val stringsHelper = StringsHelper.loadDataFromJson()
            val menuItem = preference?.dataMenuKeyValue
            preference?.clear()
            KsPreferenceKeys.getInstance().saveDataMenuKeyValue(menuItem)
            setMediaTypeJson(this)
            SharedPrefHelper.getInstance().setColorJson(colorsModel)
            SharedPrefHelper.getInstance().setStringJson(stringsHelper)
            if (encrypt != null) {
                preference?.encryptionUpdate = encrypt
            }
            KsPreferenceKeys.getInstance().setString("DMS_Response", json)
            KsPreferenceKeys.getInstance().setfirstTimeUserForKidsPIn(false)
            KsPreferenceKeys.getInstance().subscriptioN_BASE_URL = strSubscriptionURL
            KsPreferenceKeys.getInstance().paymenT_BASE_URL = strPaymentURL
            KsPreferenceKeys.getInstance().currentTheme = strCurrentTheme
            KsPreferenceKeys.getInstance().appLanguage = strCurrentLanguage
            updateLanguage(strCurrentLanguage, this)
            KsPreferenceKeys.getInstance().bingeWatchEnable = isBingeWatchEnable
            KsPreferenceKeys.getInstance().sponsorArtistId = ""
            KsPreferenceKeys.getInstance().assetType = assetType
            KsPreferenceKeys.getInstance().contentSlugSponser = contentSlug
        } catch (e: Exception) {
            Logger.w(e)
        }
    }

    fun showHideProgress(progressBar: ProgressBar) {
        showLoading(progressBar, false)
        val mHandler = Handler(Looper.getMainLooper())
        mHandler.postDelayed({ dismissLoading(progressBar) }, 3000)
    }

    protected fun showLoading(progressBar: ProgressBar?, `val`: Boolean) {
        if (`val`) {
            progressBar?.visibility = View.VISIBLE
            progressBar?.bringToFront()
        }
        window.setFlags(
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
        )
    }


    fun dismissLoading(progressBar: ProgressBar?) {
        if (progressBar != null) {
            progressBar.visibility = View.INVISIBLE
            window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
        }
    }


    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        tabletSize = resources.getBoolean(R.bool.isTablet)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            WindowCompat.setDecorFitsSystemWindows(window, true)
        }
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT

        strCurrentTheme = KsPreferenceKeys.getInstance().currentTheme
        if (KsPreferenceKeys.getInstance().currentTheme.equals(
                AppConstants.LIGHT_THEME,
                ignoreCase = true
            )
        ) {
            setTheme(R.style.MyMaterialTheme_Base_Light)
        } else {
            setTheme(R.style.MyMaterialTheme_Base_Dark)
        }
        currentLanguage = KsPreferenceKeys.getInstance().appLanguage
        if (currentLanguage.isNotEmpty()){
            updateLanguage(currentLanguage)
        }
    }


    var contentSlug: String = ""
    var contentType: String = ""
    fun branchRedirections(jsonObject: JSONObject?) {
        try {
            Logger.d("branchRedirections: $jsonObject")
            if (jsonObject != null && jsonObject.has("contentType")) {
                var assetId = 0
                contentType = jsonObject.getString("contentType")
                val id = jsonObject.optString("id")
                contentSlug = jsonObject.getString("contentSlug")
                if (!id.equals("", ignoreCase = true)) {
                    assetId = id.toInt()
                    if (contentType.equals(
                            MediaTypeConstants.getInstance().series,
                            ignoreCase = true
                        )
                    ) {
                        if (disallowClick()) {
                            return
                        }
                        ActivityLauncher.getInstance().homeScreen(
                            this,
                            HomeActivity::class.java,
                            false,
                            contentSlug,
                            contentType,
                            0
                        )
                        ActivityLauncher.getInstance().seriesDetailScreen(
                            this@BaseActivity,
                            SeriesDetailActivity::class.java,
                            assetId
                        )
                        finish()
                    } else if (contentType.equals(
                            AppConstants.ContentType.VIDEO.name,
                            ignoreCase = true
                        ) || contentType.equals(
                            MediaTypeConstants.getInstance().movie,
                            ignoreCase = true
                        )
                    ) {
                        if (disallowClick()) {
                            return
                        }
                        ActivityLauncher.getInstance().homeScreen(
                            this,
                            HomeActivity::class.java,
                            false,
                            contentSlug,
                            contentType,
                            0
                        )
                        ActivityLauncher.getInstance().detailScreen(
                            this@BaseActivity,
                            DetailActivity::class.java,
                            assetId,
                            "0",
                            false
                        )
                        finish()
                    } else if (contentType.equals(
                            MediaTypeConstants.getInstance().show, ignoreCase = true
                        )
                    ) {
                        if (disallowClick()) {
                            return
                        }
                        ActivityLauncher.getInstance().homeScreen(
                            this,
                            HomeActivity::class.java,
                            false,
                            contentSlug,
                            contentType,
                            0
                        )
                        ActivityLauncher.getInstance().detailScreen(
                            this@BaseActivity,
                            DetailActivity::class.java,
                            assetId,
                            "0",
                            false
                        )
                        finish()
                    } else if (contentType.equals(
                            MediaTypeConstants.getInstance().episode, ignoreCase = true
                        )
                    ) {
                        if (disallowClick()) {
                            return
                        }
                        ActivityLauncher.getInstance().homeScreen(
                            this,
                            HomeActivity::class.java,
                            false,
                            contentSlug,
                            contentType,
                            0
                        )
                        ActivityLauncher.getInstance().episodeScreen(
                            this@BaseActivity,
                            EpisodeActivity::class.java, assetId, "0", false
                        )
                        finish()
                    } else if (contentType.equals(
                            AppConstants.SONGS, ignoreCase = true
                        )
                    ) {
                        if (disallowClick()) {
                            return
                        }
                        ActivityLauncher.getInstance().homeScreen(
                            this,
                            HomeActivity::class.java,
                            false,
                            contentSlug,
                            contentType,
                            assetId
                        )
                        finish()
                    } else if (contentType.equals(
                            AppConstants.ARTISTS, ignoreCase = true
                        )
                    ) {
                        if (disallowClick()) {
                            return
                        }
                        ActivityLauncher.getInstance().homeScreen(
                            this,
                            HomeActivity::class.java,
                            true,
                            contentSlug,
                            contentType,
                            assetId
                        )
                        finish()
                    } else if (contentType.equals(
                            AppConstants.ALBUM, ignoreCase = true
                        )
                    ) {
                        if (disallowClick()) {
                            return
                        }
                        ActivityLauncher.getInstance().homeScreen(
                            this,
                            HomeActivity::class.java,
                            true,
                            contentSlug,
                            contentType,
                            assetId
                        )
                        finish()
                    }
                } else {
                    Log.d("uselessHomeRedirection", ": " + contentType + "2" + assetId)
                    ActivityLauncher.getInstance().homeScreen(
                        this,
                        HomeActivity::class.java,
                        true,
                        contentSlug,
                        contentType,
                        0
                    )
                    finish()
                }
            } else {
                try {
                    if (jsonObject != null && jsonObject.has("mediaType") && jsonObject.has("id")) {
                        var assetId = 0
                        contentType = jsonObject.getString("mediaType")
                        val id = jsonObject.optString("id")
                        contentSlug = jsonObject.getString("contentSlug")

                        Log.d("branchRedirections", "branchRedirections: " + contentType + "_" + id)

                        if (!id.equals("", ignoreCase = true)) {
                            assetId = id.toInt()
                            Log.d(
                                "branchRedirections",
                                "branchRedirections: " + contentType + "___" + assetId
                            )

                            if (contentType.equals(
                                    MediaTypeConstants.getInstance().series, ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    false,
                                    contentSlug,
                                    contentType,
                                    0
                                )
                                ActivityLauncher.getInstance().seriesDetailScreen(
                                    this@BaseActivity, SeriesDetailActivity::class.java, assetId
                                )
                                finish()
                            } else if (contentType.equals(
                                    AppConstants.ContentType.VIDEO.name,
                                    ignoreCase = true
                                ) || contentType.equals(
                                    MediaTypeConstants.getInstance().movie,
                                    ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    false,
                                    contentSlug,
                                    contentType,
                                    0
                                )
                                ActivityLauncher.getInstance().detailScreen(
                                    this@BaseActivity,
                                    DetailActivity::class.java, assetId, "0", false
                                )
                                finish()
                            } else if (contentType.equals(
                                    MediaTypeConstants.getInstance().show, ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    false,
                                    contentSlug,
                                    contentType,
                                    0
                                )
                                ActivityLauncher.getInstance().detailScreen(
                                    this@BaseActivity,
                                    DetailActivity::class.java, assetId, "0", false
                                )
                                finish()
                            } else if (contentType.equals(
                                    MediaTypeConstants.getInstance().episode, ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    false,
                                    contentSlug,
                                    contentType,
                                    0
                                )
                                ActivityLauncher.getInstance().episodeScreen(
                                    this@BaseActivity,
                                    EpisodeActivity::class.java, assetId, "0", false
                                )
                                finish()
                            } else if (contentType.equals(
                                    AppConstants.SONGS, ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    false,
                                    contentSlug,
                                    contentType,
                                    assetId
                                )
                                finish()
                            } else if (contentType.equals(
                                    AppConstants.ARTISTS, ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    true,
                                    contentSlug,
                                    contentType,
                                    assetId
                                )
                                finish()
                            } else if (contentType.equals(
                                    AppConstants.ALBUM, ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                Log.d(
                                    "branchRedirections",
                                    "branchRedirections: " + contentType + "___2" + assetId
                                )
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    true,
                                    contentSlug,
                                    contentType,
                                    assetId
                                )
                                finish()
                            }
                        } else {
                            if (contentType.equals(
                                    MediaTypeConstants.getInstance().series, ignoreCase = true
                                ) || contentType.equals(AppConstants.CUSTOM,ignoreCase = true)){
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    false,
                                    contentSlug,
                                    contentType,
                                    0
                                )
                                ActivityLauncher.getInstance().seriesDetailScreenDeepLink(
                                    this@BaseActivity, SeriesDetailActivity::class.java, assetId,contentSlug
                                )
                                finish()
                            } else if (contentType.equals(
                                    AppConstants.ContentType.VIDEO.name,
                                    ignoreCase = true
                                ) || contentType.equals(
                                    MediaTypeConstants.getInstance().movie,
                                    ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    false,
                                    contentSlug,
                                    contentType,
                                    0
                                )
                                ActivityLauncher.getInstance().detailScreenForDeepLink(
                                    this@BaseActivity,
                                    DetailActivity::class.java, assetId,contentSlug,"0", false
                                )
                                finish()
                            } else if (contentType.equals(
                                    MediaTypeConstants.getInstance().show, ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    false,
                                    contentSlug,
                                    contentType,
                                    0
                                )
                                ActivityLauncher.getInstance().detailScreenForDeepLink(
                                    this@BaseActivity,
                                    DetailActivity::class.java, assetId,contentSlug,"0", false
                                )
                                finish()
                            } else if (contentType.equals(
                                    MediaTypeConstants.getInstance().episode, ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    false,
                                    contentSlug,
                                    contentType,
                                    0
                                )
                                ActivityLauncher.getInstance().episodeScreen(
                                    this@BaseActivity,
                                    EpisodeActivity::class.java, assetId, "0", false
                                )
                                finish()
                            } else if (contentType.equals(
                                    AppConstants.SONGS, ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    false,
                                    contentSlug,
                                    contentType,
                                    assetId
                                )
                                finish()
                            } else if (contentType.equals(
                                    AppConstants.ARTISTS, ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    true,
                                    contentSlug,
                                    contentType,
                                    assetId
                                )
                                finish()
                            } else if (contentType.equals(
                                    AppConstants.ALBUM, ignoreCase = true
                                )
                            ) {
                                if (disallowClick()) {
                                    return
                                }
                                Log.d(
                                    "branchRedirections",
                                    "branchRedirections: " + contentType + "___2" + assetId
                                )
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    true,
                                    contentSlug,
                                    contentType,
                                    assetId
                                )
                                finish()
                            }else if (contentType.equals(AppConstants.Reel,ignoreCase = true)){
                                if (disallowClick()) {
                                    return
                                }
                                KsPreferenceKeys.getInstance().reel = true
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    true,
                                    contentSlug,
                                    contentType,
                                    0
                                )

                           } else if (contentType.equals(AppConstants.live,ignoreCase = true)){
                                if (disallowClick()) {
                                    return
                                }
                                KsPreferenceKeys.getInstance().live = true
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    true,
                                    contentSlug,contentType,0
                                )
                            }
                            else{
                                Log.d("uselessHomeRedirection", ": " + contentType + "1" + assetId)
                                ActivityLauncher.getInstance().homeScreen(
                                    this,
                                    HomeActivity::class.java,
                                    true,
                                    contentSlug,
                                    contentType,
                                    0
                                )
                                finish()
                            }

                        }
                    }
                } catch (e: Exception) {
                    Logger.w(e)
                }
            }
        } catch (e: Exception) {
            Logger.w(e)
        }
    }

//    public void updateLanguage(String currentLanguage) {
//        try {
//            if (Objects.equals(KsPreferenceKeys.getInstance().getAppLanguage(), "")) {
//                KsPreferenceKeys.getInstance().setAppLanguage(currentLanguage);
//                AppCommonMethod.updateLanguage(currentLanguage, this);
//            } else {
//                if (KsPreferenceKeys.getInstance().getAppLanguage().equalsIgnoreCase(AppConstants.LANGUAGE_ARABIC)) {
//                    AppCommonMethod.updateLanguage(AppConstants.LANGUAGE_ARABIC, this);
//                } else if (KsPreferenceKeys.getInstance().getAppLanguage().equalsIgnoreCase(AppConstants.ENGLISH_LAN_CODE)) {
//                    AppCommonMethod.updateLanguage(AppConstants.ENGLISH_LAN_CODE, this);
//                }
//            }
//
//        } catch (Exception e) {
//
//        }
//    }


    fun updateLanguage(currentLanguage: String?) {
        try {
            val preference = KsPreferenceKeys.getInstance()
            if (preference.appLanguage == "") {
                preference.appLanguage = currentLanguage
                updateLanguage(currentLanguage, this)
            } else {
                if (preference.appLanguage.equals(AppConstants.LANGUAGE_ARABIC, ignoreCase = true)) {
                    updateLanguage(AppConstants.LANGUAGE_ARABIC_CODE, this)
                } else if (preference.appLanguage.equals(AppConstants.ENGLISH_LAN_CODE, ignoreCase = true)) {
                    updateLanguage(AppConstants.LANGUAGE_ENGLISH_CODE, this)
                }
            }
        } catch (e: Exception) {
            // Handle exception
        }
    }




    protected fun checkPermissionGranted(permission: String): Boolean {
        val result = ContextCompat.checkSelfPermission(this, permission)
        return result == PackageManager.PERMISSION_GRANTED
    }

    private var onPermissionResult: OnPermissionResult? = null

    protected fun requestPermission(permission: String, onPermissionResult: OnPermissionResult?) {
        this.onPermissionResult = onPermissionResult
        if (ActivityCompat.shouldShowRequestPermissionRationale(this, permission)) {
            val intent = Intent(
                Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                Uri.parse("package:" + BuildConfig.APPLICATION_ID)
            )
            intent.addCategory(Intent.CATEGORY_DEFAULT)
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        } else {
            ActivityCompat.requestPermissions(this, arrayOf(permission), PERMISSION_REQUEST_CODE)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (onPermissionResult != null) {
            onPermissionResult!!.performAction(permissions, grantResults)
        }
    }

    interface OnPermissionResult {
        fun performAction(permissions: Array<String>, grantResults: IntArray)
    }

    protected fun setupUI(view: View) {
        if (view !is EditText) {
            view.setOnTouchListener { v: View?, event: MotionEvent? ->
                hideSoftKeyboard(view)
                false
            }
        }
        //If a layout container, iterate over children and seed recursion.
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                val innerView = view.getChildAt(i)
                setupUI(innerView)
            }
        }
    }

    override fun onDialogFinished() {
        val token = KsPreferenceKeys.getInstance().appPrefAccessToken
        hitApiLogout(applicationContext, token)
    }


    override fun onResume() {
        super.onResume()
        if (!strCurrentTheme.equals(
                KsPreferenceKeys.getInstance().currentTheme,
                ignoreCase = true
            )
        ) {
            recreate()
        }

        ApplicationUpdateManager.getInstance(applicationContext).appUpdateManager.appUpdateInfo.addOnSuccessListener { appUpdateInfo: AppUpdateInfo ->
            if (appUpdateInfo.installStatus() == InstallStatus.DOWNLOADED) {
                // If the update is downloaded but not installed,
                // notify the user to complete the update.
                popupSnackbarForCompleteUpdate()
            }
        }

        if (this !is ActivitySplash
            && BaseConfiguration.instance.clients == null
        ) {

            setupBaseClient()
        }
    }

    protected fun setupBaseClient() {
        val isTablet = resources.getBoolean(R.bool.isTablet)
        var API_KEY = ""
        var DEVICE_TYPE = ""
        if (isTablet) {
            API_KEY = SDKConfig.API_KEY_TAB
            DEVICE_TYPE = BaseDeviceType.tablet.name
        } else {
            API_KEY = SDKConfig.API_KEY_MOB
            DEVICE_TYPE = BaseDeviceType.mobile.name
        }
        val client = BaseClient(
            BaseGateway.ENVEU,
            SDKConfig.getInstance().basE_URL,
            SDKConfig.getInstance().ovP_BASE_URL,
            "",
            DEVICE_TYPE,
            BuildConfig.VERSION_NAME,
            API_KEY,
            BasePlatform.android.name,
            isTablet,
            getDeviceId(contentResolver),
            SDKConfig.getInstance().subscriptioN_BASE_URL,
            getDeviceId(contentResolver),
            getDeviceName(applicationContext)!!,
            SDKConfig.getInstance().searcH_BASE_URL,
            )
        BaseConfiguration.instance.clientSetup(client)
    }

    override fun onStart() {
        super.onStart()
    }

    /* Displays the snackbar notification and call to action. */
    private fun popupSnackbarForCompleteUpdate() {
        val snackbar =
            Snackbar.make(
                findViewById(android.R.id.content),
                resources.getString(R.string.update_has_downloaded),
                Snackbar.LENGTH_INDEFINITE
            )
        snackbar.setAction(resources.getString(R.string.restart)) { view: View? ->
            ApplicationUpdateManager.getInstance(
                applicationContext
            ).appUpdateManager.completeUpdate()
        }
        snackbar.setActionTextColor(resources.getColor(R.color.series_detail_episode_unselected_btn_txt_color))
        snackbar.show()
    }

    fun setTextOrHide(textView: TextView, data: String) {
        Logger.d(Logger.getTag(), " setTextOrHide called $textView $data")
        if (ObjectHelper.isEmpty(data)) {
            textView.visibility = View.GONE
        } else {
            textView.text = data
            textView.visibility = View.VISIBLE
        }
    }

    fun updateVisibility(view: View, value: String) {
        try {
            Logger.d(Logger.getTag(), "updateVisibility called $view $value")
            if (ObjectHelper.isNotEmpty(value)) {
                view.visibility = View.VISIBLE
            } else {
                view.visibility = View.GONE
            }
        } catch (ex: Exception) {
            Logger.w(ex)
        }
    }

    fun getFeatureCount(seriesDetailBean: EnveuVideoItemBean?): Int {
        var featureCount = 0
        if (seriesDetailBean == null) {
            return featureCount
        }
        if (ObjectHelper.isNotEmpty(seriesDetailBean.is4k)) {
            featureCount++
        }
        if (ObjectHelper.isNotEmpty(seriesDetailBean.signedLangEnabled)) {
            featureCount++
        }
        if (ObjectHelper.isNotEmpty(seriesDetailBean.isAudioDesc)) {
            featureCount++
        }
        if (ObjectHelper.isNotEmpty(seriesDetailBean.isClosedCaption)) {
            featureCount++
        }
        if (ObjectHelper.isNotEmpty(seriesDetailBean.isSoundTrack)) {
            featureCount++
        }
        return featureCount
    }

    companion object {
        private const val PERMISSION_REQUEST_CODE = 1
    }
}
