package com.enveu.baseModels;

import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.graphics.Insets;
import androidx.core.view.OnApplyWindowInsetsListener;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.databinding.ViewDataBinding;

import com.enveu.activities.homeactivity.ui.HomeActivity;
import com.enveu.utils.Logger;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;

public abstract class BaseBindingActivity<B extends ViewDataBinding> extends BaseActivity {

    private B mBinding;

    public abstract B inflateBindingLayout(@NonNull LayoutInflater inflater);


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = setupBinding(getLayoutInflater());
        setContentView(mBinding.getRoot());
        Logger.d("current activity: " + this);

//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
//            ViewCompat.setOnApplyWindowInsetsListener(mBinding.getRoot(), (view, insets) -> {
//                Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
//                view.setPadding(
//                        systemBars.left,
//                        systemBars.top * 2,
//                        systemBars.right,
//                        -systemBars.bottom
//                );
//                return WindowInsetsCompat.CONSUMED;
//            });
//        }

    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    public B getBinding() {
        return mBinding;
    }

    private B setupBinding(@NonNull LayoutInflater inflater) {
        return inflateBindingLayout(inflater);
    }
}
