package com.enveu.baseModels;


import static com.enveu.utils.commonMethods.AppCommonMethod.updateLanguage;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;

import com.enveu.OttApplication;
import com.enveu.utils.Logger;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;


public abstract class BaseBindingFragment<B extends ViewDataBinding> extends BaseFragment {
    private B mBinding;


    protected abstract B inflateBindingLayout(@NonNull LayoutInflater inflater);

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        mBinding = setupBinding(inflater);
        Logger.d("current fragment: " + this);
        return mBinding.getRoot();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (KsPreferenceKeys.getInstance().getAppLanguage().equalsIgnoreCase(AppConstants.LANGUAGE_ARABIC)){
            updateLanguage(AppConstants.LANGUAGE_ARABIC,  OttApplication.Companion.getContext());
        } else if (KsPreferenceKeys.getInstance().getAppLanguage().equalsIgnoreCase(AppConstants.ENGLISH)) {
            updateLanguage(AppConstants.ENGLISH,  OttApplication.Companion.getContext());
        }
    }

    public B getBinding() {
        return mBinding;
    }


    private B setupBinding(@NonNull LayoutInflater inflater) {
        return inflateBindingLayout(inflater);
    }
}
