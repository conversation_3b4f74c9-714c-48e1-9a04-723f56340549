package com.enveu.baseModels;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.ProgressBar;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.enveu.client.baseCollection.baseCategoryServices.BaseCategoryServices;
import com.enveu.client.userManagement.callBacks.LogoutCallBack;
import com.enveu.menuManager.model.MenuManagerModel;
import com.enveu.utils.commonMethods.AppConfigMethod;
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;
import com.facebook.login.LoginManager;
import com.google.gson.JsonObject;
import com.enveu.OttApplication;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.constants.AppConstants;


import java.util.List;
import java.util.Objects;

import retrofit2.Response;

public class BaseFragment extends Fragment {

    @Nullable
    protected BaseBindingActivity getBaseActivity() {
        return (BaseBindingActivity) getActivity();
    }

    protected void showLoading(ProgressBar progressBar, boolean val, Activity context) {
        if (val) {
            progressBar.setVisibility(View.VISIBLE);
            progressBar.bringToFront();
        }
        context.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
    }
    public void hideSoftKeyboard(View view) {
        InputMethodManager imm = (InputMethodManager) view.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        Objects.requireNonNull(imm).hideSoftInputFromWindow(view.getWindowToken(), 0);
    }

    public void showSoftKeyboard(View view) {
        // Show the soft keyboard
        InputMethodManager keyboard = (InputMethodManager) view.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        keyboard.showSoftInput(view, 0);
    }


    public void clearCredientials(KsPreferenceKeys preference) {
        try {
            String isFacebook = preference.getAppPrefLoginType();
            if (isFacebook.equalsIgnoreCase(AppConstants.UserLoginType.FbLogin.toString())) {
                LoginManager.getInstance().logOut();
            }
            String strCurrentTheme = KsPreferenceKeys.getInstance().getCurrentTheme();
            String strCurrentLanguage = KsPreferenceKeys.getInstance().getAppLanguage();
            String  sponserID = KsPreferenceKeys.getInstance().getSponsorArtistId();
            String assetType = KsPreferenceKeys.getInstance().getAssetType();
            String contentSlug = KsPreferenceKeys.getInstance().getContentSlugSponser();
            List<MenuManagerModel.Data.OrderedMenuItem> menuItem=preference.getDataMenuKeyValue();
            preference.clear();
            AppConfigMethod.INSTANCE.setMediaTypeJson(requireActivity());
            KsPreferenceKeys.getInstance().saveDataMenuKeyValue(menuItem);
            KsPreferenceKeys.getInstance().setCurrentTheme(strCurrentTheme);
            KsPreferenceKeys.getInstance().setAppLanguage(strCurrentLanguage);
            KsPreferenceKeys.getInstance().setSponsorArtistId(sponserID);
            KsPreferenceKeys.getInstance().setAssetType(assetType);
            KsPreferenceKeys.getInstance().setContentSlugSponser(contentSlug);
            if (KsPreferenceKeys.getInstance().getAppLanguage().equalsIgnoreCase(AppConstants.DUTCH)) {
                AppCommonMethod.updateLanguage(AppConstants.DUTCH_LAN_CODE, OttApplication.Companion.getContext());
            } else if (KsPreferenceKeys.getInstance().getAppLanguage().equals(AppConstants.ENGLISH)) {
                AppCommonMethod.updateLanguage(AppConstants.ENGLISH_LAN_CODE, OttApplication.Companion.getContext());
            }
            //AppCommonMethod.updateLanguage(strCurrentLanguage,getBaseActivity());
            // ActivityLauncher.getInstance()`.homeScreen(getBaseActivity(), HomeActivity.class);
        } catch (Exception e) {
            //ActivityLauncher.getInstance()`.homeScreen(getBaseActivity(), HomeActivity.class);

        }
    }

    public void hitApiLogout(Context context, String token) {

        String isFacebook = KsPreferenceKeys.getInstance().getAppPrefLoginType();

        if (isFacebook.equalsIgnoreCase(AppConstants.UserLoginType.FbLogin.toString())) {
            LoginManager.getInstance().logOut();
        }
        BaseCategoryServices.Companion.getInstance().logoutService(token, new LogoutCallBack() {
            @Override
            public void failure(boolean status, int errorCode, String message) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, 500);

            }

            @Override
            public void success(boolean status, Response<JsonObject> response) {
                if (status) {
                    AnalyticsUtils.logEvent(context,AppConstants.LOGOUT);
                    try {
                        if (response.code() == 404) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        }
                        if (response.code() == 403) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        } else if (response.code() == 200) {
                            Objects.requireNonNull(response.body()).addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        } else if (response.code() == 401) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        } else if (response.code() == 500) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        }
                    } catch (Exception e) {
                        JsonObject jsonObject = new JsonObject();
                        jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                    }

                }
            }
        });
    }

    protected void dismissLoading(ProgressBar progressBar, Activity context) {
        if (progressBar != null) {
            progressBar.setVisibility(View.INVISIBLE);
            context.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        }
    }
}
