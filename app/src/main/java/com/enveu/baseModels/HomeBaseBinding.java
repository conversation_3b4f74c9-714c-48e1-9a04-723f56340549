package com.enveu.baseModels;


import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.graphics.Insets;
import androidx.core.view.OnApplyWindowInsetsListener;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.databinding.ViewDataBinding;

import com.enveu.utils.Logger;

public abstract class HomeBaseBinding<B extends ViewDataBinding> extends BaseActivity {

    private B mBinding;

    public abstract B inflateBindingLayout(@NonNull LayoutInflater inflater);


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = setupBinding(getLayoutInflater());
        setContentView(mBinding.getRoot());
        Logger.d("current activity: " + this);

//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
//            ViewCompat.setOnApplyWindowInsetsListener(mBinding.getRoot(), new OnApplyWindowInsetsListener() {
//                @NonNull
//                @Override
//                public WindowInsetsCompat onApplyWindowInsets(@NonNull View view, @NonNull WindowInsetsCompat insets) {
//                    Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
//                    view.setPadding(systemBars.left, systemBars.top-5, systemBars.right, -systemBars.bottom);
//                    return insets;
//                }
//            });
//        }
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    public B getBinding() {
        return mBinding;
    }

    private B setupBinding(@NonNull LayoutInflater inflater) {
        return inflateBindingLayout(inflater);
    }
}
