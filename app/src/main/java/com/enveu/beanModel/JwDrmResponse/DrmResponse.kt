package com.enveu.beanModel.JwDrmResponse

data class DrmResponse(
	val playlist: List<PlaylistItem?>? = null,
	val feedInstanceId: String? = null,
	val kind: String? = null,
	val description: String? = null,
	val title: String? = null
)

data class Playready(
	val url: String? = null
)

data class Fairplay(
	val processSpcUrl: String? = null,
	val certificateUrl: String? = null
)

data class PlaylistItem(
	val image: String? = null,
	val images: List<ImagesItem?>? = null,
	val sources: List<SourcesItem?>? = null,
	val mediaContentId: String? = null,
	val link: String? = null,
	val description: String? = null,
	val title: String? = null,
	val mediaid: String? = null,
	val tracks: List<TracksItem?>? = null,
	val duration: Int? = null,
	val variations: Variations? = null,
	val tenantId: String? = null,
	val projectId: String? = null,
	val pubdate: Int? = null
)

data class Variations(
	val any: Any? = null
)

data class SourcesItem(
	val file: String? = null,
	val type: String? = null,
	val drm: Drm? = null
)

data class ImagesItem(
	val src: String? = null,
	val width: Int? = null,
	val type: String? = null
)

data class Widevine(
	val url: String? = null
)

data class Drm(
	val fairplay: Fairplay? = null,
	val playready: Playready? = null,
	val widevine: Widevine? = null
)

data class TracksItem(
	val file: String? = null,
	val kind: String? = null
)

