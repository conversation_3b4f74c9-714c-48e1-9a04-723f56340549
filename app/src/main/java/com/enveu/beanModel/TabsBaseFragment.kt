package com.enveu.beanModel


import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AppCompatActivity.RESULT_OK
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.follow_follower.FollowFollowingProfileActivity
import com.enveu.activities.homeactivity.TabIdInterface
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.listing.listui.ListActivity
import com.enveu.activities.listing.ui.GridActivity
import com.enveu.activities.privacypolicy.ui.WebViewActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.watchList.ui.WatchListActivity
import com.enveu.adapters.commonRails.CommonAdapterNew
import com.enveu.adapters.shimmer.ShimmerAdapter
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.baseModels.HomeBaseViewModel
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.CommonApiCallBack
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner
import com.enveu.callbacks.commonCallbacks.MoreClickListner
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory
import com.enveu.client.enums.LandingPageType
import com.enveu.client.enums.Layouts
import com.enveu.client.enums.ListingLayoutType
import com.enveu.client.enums.PDFTarget
import com.enveu.databinding.ActivityMainBinding
import com.enveu.databinding.FragmentHomeBinding
import com.enveu.fragments.listing.GridFragment
import com.enveu.fragments.listing.ListFragment
import com.enveu.jwplayer.player.LivePlayerActivity
import com.enveu.networking.response.ReelCreatorId
import com.enveu.networking.response.ReelsContentItem
import com.enveu.utils.Constants
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppCommonMethod.Companion.redirectionLogic
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.cropImage.helpers.ShimmerDataModel
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.ToastHandler
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson


open class TabsBaseFragment<T : HomeBaseViewModel?> : BaseBindingFragment<FragmentHomeBinding?>(), CommonRailtItemClickListner, MoreClickListner, TabIdInterface {
    private var viewModel: T? = null
    private var swipeToRefresh = 0
    private var featureList: FeatureFlagModel? = null
    private val mScrollY = 0
    private var preference: KsPreferenceKeys? = null
    private var tabId: String? = null
    private var railCommonDataList: MutableList<RailCommonData> = ArrayList()
    private var adapterNew: CommonAdapterNew? = null
    private var kidsMode = false
    private var intentFrom = ""
    private var homeBinding: ActivityMainBinding?= null
    private var railInjectionHelper:RailInjectionHelper? = null
    private var mActivity: Activity? = null
    private val tabsBaseBackgroundHideCallBack: TabsBaseBackgroundHideCallBack? = null
    protected fun setViewModel(viewModelClass: Class<out HomeBaseViewModel>, screenID: String, homeBinding: ActivityMainBinding?= null) {
        tabId = screenID
        viewModel = ViewModelProvider(this)[viewModelClass] as T
        this.homeBinding = homeBinding
    }

    public override fun inflateBindingLayout(inflater: LayoutInflater): FragmentHomeBinding {
        return FragmentHomeBinding.inflate(inflater)
    }

    private fun modelCall() {
        mActivity = activity
        if (activity != null) {
            preference = KsPreferenceKeys.getInstance()
        }
        connectionObserver()
    }

    private fun connectionObserver() {
        if (activity != null && NetworkConnectivity.isOnline(activity)) {
            binding!!.noConnectionLayout.visibility = View.GONE
            adapterNew = null
            getAppLevelJsonData()
            connectionValidation(true)
        } else {
            connectionValidation(false)
        }
    }

    override fun onResume() {
        super.onResume()
        if (activity != null && activity is HomeActivity)
            (activity as HomeActivity).showMainToolbar()
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
//            binding!!.swipeContainer.isRefreshing = true
            uiInitialisation()
            loadDataFromModel()
        } else {
            noConnectionLayout()
        }
    }

    private fun getAppLevelJsonData() {
        featureList = AppConfigMethod.parseFeatureFlagList()
    }


    private fun uiInitialisation() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        swipeToRefresh()
//        callShimmer()
        binding!!.noResultFound.colorsData = ColorsHelper
        binding!!.connection.colorsData = ColorsHelper
        binding!!.noResultFound.stringData = StringsHelper
        binding!!.connection.stringData = StringsHelper
        binding!!.colorsData = ColorsHelper
        binding!!.noResultFound.retryAgain.setOnClickListener { view: View? ->
            binding!!.noResultLayout.visibility = View.GONE
            binding!!.noConnectionLayout.visibility = View.GONE
            binding!!.myRecyclerView.visibility = View.VISIBLE
            swipeToRefresh = 2
            connectionObserver()
        }
        binding!!.myRecyclerView.setHasFixedSize(true)
        binding!!.myRecyclerView.setItemViewCacheSize(20)
        binding!!.myRecyclerView.isNestedScrollingEnabled = false
        binding!!.myRecyclerView.layoutManager = LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
//        binding!!.swipeContainer.isRefreshing = true
//        binding!!.swipeContainer.visibility = View.VISIBLE
        binding!!.noResultLayout.visibility = View.GONE
        binding!!.noConnectionLayout.visibility = View.GONE
        binding!!.swipeContainer.setOnRefreshListener {
            if (NetworkConnectivity.isOnline(activity) && swipeToRefresh == 1) {
                swipeToRefresh = 2
                binding!!.swipeContainer.isRefreshing = true
                binding!!.swipeContainer.visibility = View.VISIBLE
                binding!!.noResultLayout.visibility = View.GONE
                railCommonDataList.clear()
                baseCategories
            } else {
                swipeToRefreshCheck()
            }
        }
        baseCategories
    }

    private val baseCategories: Unit
        get() {
            railCommonDataList = ArrayList()
            adapterNew = null
            tabId?.let { Log.w("screenID", it) }
            railCommonDataList.clear()
            railInjectionHelper?.getScreenWidgets(activity, tabId, preference?.appPrefAccessToken, false, object : CommonApiCallBack {
                override fun onSuccess(item: Any) {
                        if (item is RailCommonData) {
                            Log.d("test1", "onSuccess: "+Gson().toJson(item))
                            railCommonDataList.add(item)
                            Log.d("test1", "onSuccess:railCommonDataList.size "+railCommonDataList.size)
                            binding!!.swipeContainer.isRefreshing = false
//                            if (adapterNew == null) {
////                            RecyclerAnimator(activity).animate(binding!!.myRecyclerView) // For fall down animation
//                                adapterNew = CommonAdapterNew(railCommonDataList, this@TabsBaseFragment, this@TabsBaseFragment)
//                                binding!!.myRecyclerView.adapter = adapterNew
//                              //  binding?.myRecyclerView?.addItemDecoration(FirstItemOverlapDecoration(60))
//                            } else {
//                                synchronized(railCommonDataList) {
//                                    adapterNew?.notifyItemChanged(railCommonDataList.size - 1)
//                                    binding!!.myRecyclerView.scrollToPosition(mScrollY + 500)
//                                }
//                            }
                        }
                    }

                override fun onDataLoaded(orderedData: MutableList<RailCommonData>?) {
                    for (i in orderedData!!.indices) {
                        binding?.rvPlaceholder?.visibility = View.GONE
                        homeBinding!!.toolbar.recLayoutSubMenu.visibility = View.VISIBLE
                        val item = orderedData[i]
                        if (i < railCommonDataList.size) {
                            val currentItem = railCommonDataList[i]
                            if (currentItem != item) {
                                railCommonDataList[i] = item
                                adapterNew?.notifyItemChanged(i)
                            }
                        } else {
                            railCommonDataList.add(item)
                            adapterNew?.notifyItemInserted(i)
                        }
                    }
                    binding!!.myRecyclerView.scrollToPosition(0)
                }

                    override fun onFailure(throwable: Throwable) {
                        Logger.w(throwable)
                        Log.d("test1", "onFailure: ")
                        if ("No Data".equals(throwable.message, ignoreCase = true)) {
                            binding!!.swipeContainer.isRefreshing = false
                            binding!!.myRecyclerView.visibility = View.GONE
                            binding!!.noResultLayout.visibility = View.VISIBLE
                        }
                    }

                    override fun onFinish() {
                        swipeToRefresh = 1
//                        if (railCommonDataList.isEmpty()) {
//                            binding!!.swipeContainer.isRefreshing = false
//                            binding!!.myRecyclerView.visibility = View.GONE
//                            binding!!.noResultLayout.visibility = View.VISIBLE
//                        }
                        binding?.rvPlaceholder?.visibility = View.GONE
                        adapterNew = CommonAdapterNew(railCommonDataList, this@TabsBaseFragment, this@TabsBaseFragment)
                        binding?.myRecyclerView?.adapter = adapterNew
                    }
                })
        }

        private fun recycleViewScroller() {
            binding?.myRecyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                    val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                    if (dy > 0) {
                        // Scrolling down: Hide submenu and make toolbar transparent
                        homeBinding?.toolbar?.mainLay?.visibility = View.GONE
                        homeBinding?.toolbar?.mainLay?.setBackgroundColor(Color.TRANSPARENT)
                    } else if (dy < 0 && firstVisibleItemPosition == 0) {
                        // Scrolling up: Show submenu and reset toolbar
                        homeBinding?.toolbar?.mainLay?.visibility = View.VISIBLE
                        homeBinding?.toolbar?.mainLay?.setBackgroundColor(Color.TRANSPARENT) // Keep transparent
                    }
                }
            })
        }

    private fun callShimmer() {
        val shimmerAdapter = ShimmerAdapter(
            activity,
            ShimmerDataModel(activity)
                .getList(0),
            ShimmerDataModel(
                activity
            ).slides
        )
        binding!!.myRecyclerView.adapter = shimmerAdapter
//        binding!!.myRecyclerView.apply {
//            edgeEffectFactory = BounceEdgeEffectFactory(BounceEdgeEffectFactory.VERTICAL_BOUNCE)
//        }
        binding!!.myRecyclerView.setOnTouchListener { _, _ ->
            false
        }
    }

    private fun noConnectionLayout() {
        binding!!.noConnectionLayout.visibility = View.VISIBLE
        binding!!.connection.retryTxt.setOnClickListener { view: View? -> connectionObserver() }
    }

    private fun loadDataFromModel() {
        if (activity != null && swipeToRefresh != 1) {
            viewModel?.allCategories?.observe(requireActivity()) { assetCommonBean: List<BaseCategory?>? ->
                if (assetCommonBean == null) {
                    binding!!.myRecyclerView.visibility = View.GONE
                    binding!!.noResultLayout.visibility = View.VISIBLE
                }
            }
        }
    }

    private fun swipeToRefresh() {
        binding!!.swipeContainer.setOnRefreshListener {
            if (NetworkConnectivity.isOnline(baseActivity) && swipeToRefresh == 1) {
                swipeToRefresh = 2
                railCommonDataList.clear()
                connectionObserver()
            }
            swipeToRefreshCheck()
        }
    }

    private fun swipeToRefreshCheck() {
        if (binding!!.swipeContainer.isRefreshing) {
            binding!!.swipeContainer.isRefreshing = false
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        modelCall()
        viewModel?.resetObject()
        recycleViewScroller()
    }

    override fun railItemClick(railCommonData: RailCommonData, position: Int) {
        val singleContent = railCommonData?.enveuVideoItemBeans?.get(position)
        if (railCommonData.screenWidget.type != null && Layouts.HRO.name == railCommonData.screenWidget.layout) {
            heroClickRedirection(railCommonData, position)
        }
        else if (singleContent?.assetType.equals("LIVE") && singleContent?.liveContent != null) {
            if (singleContent?.liveContent?.liveType?.equals(Constants.LINEAR_24_7) == true){
                Intent(requireActivity(), LivePlayerActivity::class.java).also {
                it.putExtra(Constants.PLAYER_URL, singleContent?.liveContent?.externalUrl)
                it.putExtra(Constants.EXTERNAL_REF_ID, singleContent?.externalRefId)
                it.putExtra(Constants.CONTENT_TITLE, singleContent?.title)
                it.putExtra(Constants.CONTENT_DESCRIPTION, singleContent?.description)
                startActivity(it)
            }
            }
        }
        else if (singleContent?.assetType.equals(AppConstants.PERSON)) {
            navigateCreatorProfilePage(singleContent)
        }
        else if(singleContent?.assetType?.equals(AppConstants.VIDEO) == true && singleContent?.videoDetails?.videoType?.equals(Constants.REEL) == true) {
            val json = Gson().toJson(singleContent)
            val reelsContentItem = Gson().fromJson(json, ReelsContentItem::class.java)
            (activity as HomeActivity).openShortsReelsFragment(reelsContentItem)
        }else if(railCommonData.screenWidget.layout.equals("COL",true)){
            moreRailClick(railCommonData, position, "")
        }
        else {
            redirectionLogic(
                requireActivity(),
                railCommonData,
                position,
                tabId,
                railCommonData.screenWidget.contentID.toString(),
                railCommonData.screenWidget.name.toString()
            )
        }
    }

    private fun navigateCreatorProfilePage(content: EnveuVideoItemBean?){
        val reelCreatorId = ReelCreatorId()
        reelCreatorId.id = content?.id?.toLong()
        reelCreatorId.externalIdentifier = content?.externalRefId
        reelCreatorId.title = content?.customDataV3?.userName
        reelCreatorId.posterUrl = content?.posterURL
        Intent(context, FollowFollowingProfileActivity::class.java).also {
            it.putExtra(Constants.CREATOR_CONTENT_BUNDLE, reelCreatorId)
            startActivityForResult(it, Constants.REQUEST_CODE)
        }
    }

    private fun openUgcFragment(content: EnveuVideoItemBean?){
        val json = Gson().toJson(content)
        val reelsContentItem = Gson().fromJson(json, ReelsContentItem::class.java)
        Intent(requireActivity(), HomeActivity::class.java).also {
            it.putExtra(Constants.OPEN_SHORTS_FRAGMENT, true)
            it.putExtra(Constants.SHORTS_REELS_BUNDLE, reelsContentItem)
            startActivity(it)
        }
    }

    private fun heroClickRedirection(railCommonData: RailCommonData, position: Int) {
        val landingPageType = railCommonData.screenWidget.landingPageType
        if (landingPageType != null) {
            if (landingPageType == LandingPageType.DEF.name || landingPageType == LandingPageType.AST.name) {
                AppCommonMethod.heroAssetRedirections(tabId, railCommonData, activity)
            } else if (landingPageType == LandingPageType.HTM.name) {
                val webViewIntent = Intent(activity, WebViewActivity::class.java)
                webViewIntent.putExtra(AppConstants.WEB_VIEW_HEADING, railCommonData.screenWidget.landingPageTitle)
                webViewIntent.putExtra(AppConstants.WEB_VIEW_URL, railCommonData.screenWidget.htmlLink)
                startActivity(webViewIntent)
            } else if (landingPageType == LandingPageType.PDF.name) {
                if (railCommonData.screenWidget.landingPagetarget != null) {
                    if (railCommonData.screenWidget.landingPagetarget == PDFTarget.LGN.name) {
                        if (preference != null && !preference?.appPrefLoginStatus.equals(
                                AppConstants.UserStatus.Login.toString(),
                                ignoreCase = true
                            )
                        ) {
                            ActivityLauncher.getInstance().loginActivity(activity, ActivityLogin::class.java, "")
                        }else{
                            redirectionLogic(
                                requireActivity(),
                                railCommonData,
                                position,
                                tabId,
                                railCommonData.screenWidget.contentID,
                                railCommonData.screenWidget.name.toString()
                            )
                        }
                    } else if (railCommonData.screenWidget.landingPagetarget == PDFTarget.SRH.name) {
                        if (activity is HomeActivity)
                            (activity as HomeActivity).openSearch()
                    }
                }
            } else if (landingPageType == LandingPageType.PLT.name) {
                Logger.e("MORE RAIL CLICK: $railCommonData")
                moreRailClick(railCommonData, 0, "")
            }
        }
    }

    override fun moreRailClick(data: RailCommonData, position: Int, multilingualTitle: String) {
        if (data.screenWidget != null) {
            var finalName : String? = null
            var playListId : String? = null
            var currentData = ""

            if(data.screenWidget.layout.equals("COL",true)){
                currentData = "COL"
                AnalyticsUtils.logWidgetMoreEvent(context,data.enveuVideoItemBeans.get(position).id.toString(),data.screenWidget.name.toString())
                playListId =
                    if (data.enveuVideoItemBeans.get(position).id != null) data.enveuVideoItemBeans.get(position).id.toString() else data.screenWidget.landingPagePlayListId
                if (playListId == null || playListId.equals("", ignoreCase = true)) {
                    ToastHandler.getInstance().show(
                        activity,
                        requireActivity().resources.getString(R.string.something_went_wrong_at_our_end_please_try_later)
                    )
                    return
                }
            }else{
                AnalyticsUtils.logWidgetMoreEvent(context,data.screenWidget.contentID.toString(),data.screenWidget.name.toString())
                playListId =
                    if (data.screenWidget.contentID != null) data.screenWidget.contentID else data.screenWidget.landingPagePlayListId
                if (playListId == null || playListId.equals("", ignoreCase = true)) {
                    ToastHandler.getInstance().show(
                        activity,
                        requireActivity().resources.getString(R.string.something_went_wrong_at_our_end_please_try_later)
                    )
                    return
                }
                finalName = checkNull(data.screenWidget, multilingualTitle)

            }

            if (data.screenWidget.name != null && data.screenWidget.referenceName != null && (data.screenWidget.referenceName.equals(
                    AppConstants.ContentType.CONTINUE_WATCHING.name, ignoreCase = true
                ) || data.screenWidget.referenceName.equals("special_playlist", ignoreCase = true))
            ) {
                if (featureList?.featureFlag?.IS_MUSIC_APP==true) {
                   /* if (activity != null && activity is HomeActivity)
                        (activity as HomeActivity).detailFrameVisibility(View.VISIBLE)*/
                    val gridFragment=GridFragment()
                    val bundle=Bundle().apply {
                        putString("playListId", playListId)
                        putString("currentData", currentData)
                        putString("title", finalName)
                        putInt("flag", 0)
                       putInt("shimmerType", 0)
                       putParcelable("baseCategory", data.screenWidget)
                       putBoolean("isContinueWatching",  data.isContinueWatching)
                    }
                    gridFragment.arguments=bundle
                    fragmentManager?.beginTransaction()
                        ?.add(R.id.content_frame,gridFragment, AppConstants.TAG_GRID_FRAGMENT)?.addToBackStack(null)
                        ?.commit()
                }else {
                    ActivityLauncher.getInstance().portraitListing(activity, GridActivity::class.java,currentData, playListId, finalName, 0, 0, data.screenWidget, data.isContinueWatching, Constants.REQUEST_CODE)
                }
            } else if (data.screenWidget.name != null && data.screenWidget.referenceName != null && data.screenWidget.referenceName.equals(AppConstants.ContentType.MY_WATCHLIST.name,
                    ignoreCase = true)) {
                ActivityLauncher.getInstance().watchHistory(activity, WatchListActivity::class.java, finalName, false)
            } else {
                if (data.screenWidget.contentListinglayout != null && !data.screenWidget.contentListinglayout.equals("", ignoreCase = true) && data.screenWidget.contentListinglayout.equals(ListingLayoutType.LST.name, ignoreCase = true)) {
                    if (featureList?.featureFlag?.IS_MUSIC_APP==true){
                        val gridFragment=ListFragment()
                        val bundle=Bundle().apply {
                            putString("playListId", playListId)
                            putString("title", finalName)
                            putInt("flag", 0)
                            putInt("shimmerType", 0)
                            putParcelable("baseCategory", data.screenWidget)
                            putBoolean("isContinueWatching",  data.isContinueWatching)
                        }
                        gridFragment.arguments=bundle
                       /* if (activity != null && activity is HomeActivity)
                            (activity as HomeActivity).detailFrameVisibility(View.VISIBLE)*/
                        fragmentManager?.beginTransaction()
                            ?.add(R.id.content_frame,gridFragment, AppConstants.TAG_GRID_FRAGMENT)?.addToBackStack(null)
                            ?.commit()
                    }else {
                        ActivityLauncher.getInstance().listActivity(activity, ListActivity::class.java, playListId, if (data.screenWidget?.name!=null )finalName else "", 0, 0, data.screenWidget)
                    }
                } else if (data.screenWidget.contentListinglayout != null && !data.screenWidget.contentListinglayout.equals("", ignoreCase = true) && data.screenWidget.contentListinglayout.equals(ListingLayoutType.GRD.name, ignoreCase = true)) {
                    Logger.e("getRailData", "GRD")

                    if (featureList?.featureFlag?.IS_MUSIC_APP==true){
                        val gridFragment=GridFragment()
                        val bundle=Bundle().apply {
                            putString("playListId", playListId)
                            putString("title", finalName)
                            putInt("flag", 0)
                            putInt("shimmerType", 0)
                            putParcelable("baseCategory", data.screenWidget)
                            putBoolean("isContinueWatching",  data.isContinueWatching)
                        }
                        gridFragment.arguments=bundle
                       /* if (activity != null && activity is HomeActivity)
                            (activity as HomeActivity).detailFrameVisibility(View.VISIBLE)*/
                        fragmentManager?.beginTransaction()
                            ?.add(R.id.content_frame,gridFragment, AppConstants.TAG_GRID_FRAGMENT)?.addToBackStack(null)
                            ?.commit()
                    }else {
                        Intent(requireActivity(), GridActivity::class.java).also { intent ->
                            intent.putExtra("playListId", playListId)
                            intent.putExtra("title", finalName)
                            intent.putExtra("currentData", currentData)
                            intent.putExtra("flag", 0)
                            intent.putExtra("shimmerType", 0)
                            intent.putExtra("baseCategory", Gson().toJson(data.screenWidget))
                            intent.putExtra("isContinueWatching", data.isContinueWatching)
                            startActivityForResult(intent, Constants.REQUEST_CODE)
                        }
                    }
                } else {
                    if (featureList?.featureFlag?.IS_MUSIC_APP==true){
                        val gridFragment=GridFragment()
                        val bundle=Bundle().apply {
                            putString("playListId", playListId)
                            putString("title", finalName)
                            putInt("flag", 0)
                            putInt("shimmerType", 0)
                            putParcelable("baseCategory", data.screenWidget)
                            putBoolean("isContinueWatching",  data.isContinueWatching)
                        }
                        gridFragment.arguments=bundle
                       /* if (activity != null && activity is HomeActivity)
                            (activity as HomeActivity).detailFrameVisibility(View.VISIBLE)*/
                        fragmentManager?.beginTransaction()
                            ?.add(R.id.content_frame,gridFragment, AppConstants.TAG_GRID_FRAGMENT)?.addToBackStack(null)
                            ?.commit()
                    }else {
                        ActivityLauncher.getInstance().portraitListing(activity, GridActivity::class.java,currentData, playListId, if (data.screenWidget.name!=null) finalName else "", 0, 0, data.screenWidget, data.isContinueWatching, Constants.REQUEST_CODE)
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == Constants.REQUEST_CODE) {
            if (data?.getBooleanExtra(Constants.OPEN_SHORTS_FRAGMENT, false) == true) {
                val reelsContentItem = data.getSerializableExtra(Constants.SHORTS_REELS_BUNDLE) as ReelsContentItem
                (activity as HomeActivity).openShortsReelsFragment(reelsContentItem)
            }
        }
    }

    private fun checkNull(screenWidget: BaseCategory?, multilingualTitle: String): String {
        var name = ""
        try {
            if (multilingualTitle.isEmpty()) {
                name = screenWidget?.heroTitle.toString()
            } else {
                name = multilingualTitle
            }
        } catch (ignored: Exception) {
        }
        return name
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (adapterNew != null) {
            binding!!.myRecyclerView.requestLayout()
            binding!!.myRecyclerView.adapter = null
            binding!!.myRecyclerView.layoutManager = null
            binding!!.myRecyclerView.adapter = adapterNew
            binding!!.myRecyclerView.layoutManager = LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
            adapterNew!!.notifyDataSetChanged()
        }
    }

    fun updateList() {
        if (preference != null && !preference!!.appPrefLoginStatus.equals(
                AppConstants.UserStatus.Login.toString(),
                ignoreCase = true
            )
        ) {
            for (i in railCommonDataList.indices) {
                if (railCommonDataList[i].isContinueWatching) {
                    railCommonDataList.removeAt(i)
                    adapterNew!!.notifyItemRemoved(i)
                }
            }
        }
    }

    fun updateAdList() {
        for (i in railCommonDataList.indices) {
            if (railCommonDataList[i].isAd) {
                Logger.w("isAdConfigurd$i  -->>", railCommonDataList[i].isAd.toString() + "")
                if (preference!!.entitlementStatus) {
                    railCommonDataList.removeAt(i)
                    adapterNew!!.notifyItemRemoved(i)
                }
            }
        }
    }

    override fun selectedTab(value: String) {
        intentFrom = value
        Log.d("intentFrom", "selectedTab: $intentFrom")
    }

    interface TabsBaseBackgroundHideCallBack {
        fun tabBackgroundHide(isBgHide: Boolean)
    }
}