package com.enveu.beanModel.responseModels.genre_response

import com.enveu.beanModelV3.searchV2.Data

data class GenreResponse(
	val dateCreated: Long,
	val lastUpdated: Long,
	val id: Int,
	val title: String,
	val description: String?,
	val longDescription: String?,
	val contentType: String,
	val keywords: List<String>,
	val targetingTags: List<String>,
	val premium: Boolean,
	val sku: String,
	val publishedDate: String?,
	val customData: CustomData,
	val parentalRating: String?,
	val parentContent: String?,
	val video: String?,
	val audioContent: String?,
	val externalRefId: String?,
	val analyticsId: String?,
	val contentSource: String,
	val customContent: CustomContent,
	val liveContent: String?,
	val personContent: String?,
	val articleContent: String?,
	val contentReviewRating: String?,
	val styleInfo: String?,
	val seoInfo: String?,
	val imageContent: String?,
	val images: List<Image>,
	val accessibility: String?,
	val contentSlug: String,
	val organizationId: Int,
	val playCount: Int
) {
	val data: Data = Data()
}

fun getData(): Data {
	return getData()
}
data class CustomData(
	val genresAlbumsIds: String,
	val genresEntSeriesIds: List<Int>,
	val genresTrailerIds: List<Int>,
	val genresSinglesIds: GenresSinglesIds,
	val genresClipIds: List<Int>,
	val genresLiveEventIds: List<Int>,
	val genresShortFilmsIds: List<Int>,
	val genresSongsIds: String,
	val genresArtistIds: String,
	val genresEntSeasonIds: List<Int>,
	val genresMoviesIds: List<Int>,
	val genresLiveLinearIds: List<Int>,
	val genresEpisodesIds: List<Int>
)

data class GenresSinglesIds(
	val id: Int,
	val externalIdentifier: String,
	val title: String,
	val contentSlug: String,
	val contentType: String,
	val mediaType: String,
	val description: String,
	val images: List<Image>
)

data class CustomContent(
	val dateCreated: Long,
	val lastUpdated: Long,
	val id: Int,
	val customType: String,
	val images: String?
)

data class Image(
	val dateCreated: Long,
	val lastUpdated: Long,
	val id: Int,
	val title: String,
	val description: String?,
	val longDescription: String?,
	val contentType: String,
	val keywords: List<String>,
	val targetingTags: List<String>,
	val premium: Boolean,
	val sku: String,
	val publishedDate: String?,
	val customData: String?,
	val parentalRating: String?,
	val parentContent: String?,
	val video: String?,
	val audioContent: String?,
	val externalRefId: String?,
	val analyticsId: String?,
	val contentSource: String,
	val imageContent: ImageContent,
	val liveContent: String?,
	val personContent: String?,
	val articleContent: String?,
	val contentReviewRating: String?,
	val styleInfo: String?,
	val seoInfo: String?,
	val accessibility: String?,
	val contentSlug: String,
	val organizationId: Int,
	val playCount: Int
)

data class ImageContent(
	val id: Int,
	val imageKey: String,
	val src: String,
	val height: Double,
	val width: Double,
	val colorPalette: List<String>,
	val dominantColor: String,
	val imageType: String,
	val status: String,
	val tag: String,
	val originalImageSizeInBytes: Long,
	val isDefault: Boolean,
	val showTitle: Boolean
)
