package com.enveu.beanModel.responseModels.sharing

import com.google.gson.annotations.SerializedName

data class SharingModel(

	@field:SerializedName("data")
	val data: Data? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	val responseCode: Int? = null
)

data class AndroidBehaviour(

	@field:SerializedName("openInApp")
	val openInApp: Boolean? = null,

	@field:SerializedName("intentScheme")
	val intentScheme: String? = null,

	@field:SerializedName("fallbackToStore")
	val fallbackToStore: Boolean? = null,

	@field:SerializedName("androidPackageName")
	val androidPackageName: String? = null,

	@field:SerializedName("deeplinkUrl")
	val deeplinkUrl: String? = null
)

data class UtmParams(

	@field:SerializedName("utmSource")
	val utmSource: String? = null,

	@field:SerializedName("utmTerm")
	val utmTerm: Any? = null,

	@field:SerializedName("utmContent")
	val utmContent: Any? = null,

	@field:SerializedName("utmMedium")
	val utmMedium: Any? = null,

	@field:SerializedName("utmCampaign")
	val utmCampaign: Any? = null
)

data class SocialMediaTags(

	@field:SerializedName("st")
	val st: String? = null,

	@field:SerializedName("sd")
	val sd: String? = null,

	@field:SerializedName("si")
	val si: String? = null
)

data class AppleBehaviour(

	@field:SerializedName("openInApp")
	val openInApp: Boolean? = null,

	@field:SerializedName("appleAppBundleIdentifier")
	val appleAppBundleIdentifier: Any? = null
)

data class Data(

	@field:SerializedName("appleBehaviour")
	val appleBehaviour: AppleBehaviour? = null,

	@field:SerializedName("favicon")
	val favicon: String? = null,

	@field:SerializedName("dynamicLinkName")
	val dynamicLinkName: String? = null,

	@field:SerializedName("androidBehaviour")
	val androidBehaviour: AndroidBehaviour? = null,

	@field:SerializedName("dynamicLink")
	val dynamicLink: String? = null,

	@field:SerializedName("contentId")
	val contentId: Any? = null,

	@field:SerializedName("description")
	val description: String? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("domainId")
	val domainId: String? = null,

	@field:SerializedName("utmParams")
	val utmParams: UtmParams? = null,

	@field:SerializedName("deepLink")
	val deepLink: String? = null,

	@field:SerializedName("displayImg")
	val displayImg: String? = null,

	@field:SerializedName("socialMediaTags")
	val socialMediaTags: SocialMediaTags? = null
)
