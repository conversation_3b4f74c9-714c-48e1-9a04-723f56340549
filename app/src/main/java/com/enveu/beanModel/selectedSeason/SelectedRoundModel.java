package com.enveu.beanModel.selectedSeason;

import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;

public class SelectedRoundModel {
    private EnveuVideoItemBean list;
    private int selectedId;
    private boolean selected;

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public SelectedRoundModel(EnveuVideoItemBean list, int selectedId, boolean isSelected) {
        this.list = list;
        this.selectedId = selectedId;
        this.selected=isSelected;
    }

    public EnveuVideoItemBean getList() {
        return list;
    }

    public void setList(EnveuVideoItemBean list) {
        this.list = list;
    }

    public int getSelectedId() {
        return selectedId;
    }

    public void setSelectedId(int selectedId) {
        this.selectedId = selectedId;
    }
}
