package com.enveu.beanModel.sponsorUserTracking

data class UserTrackingResponse(
    val data: Data?,
    val responseCode: Int,
    val debugMessage: String?
)

data class Data(
    val dateCreated: Long,
    val lastUpdated: Long,
    val id: String,
    val deviceInfo: DeviceInfo?,
    val customer: Customer?,
    val event: Event?,
    val owner: Int
)

data class DeviceInfo(
    val deviceInfo: DeviceDetails?
)

data class DeviceDetails(
    val test: String
)

data class Customer(
    val userName: String?,
    val phoneNumber: String?,
    val email: String?,
    val additionalInformation: String?
)

data class Event(
    val name: String,
    val identifier: String,
    val attribute: Attribute?
)

data class Attribute(
    val platform: String,
    val via: String,
    val deviceId: String
)
