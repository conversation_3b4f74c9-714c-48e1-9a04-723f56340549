package app.doxzilla.bean_model.active_device_management.startPLayBackSessionModel_AppLevel


import com.google.gson.annotations.SerializedName

data class StartSessionModel(
    @SerializedName("data")
    val `data`: Data?= null,
    @SerializedName("debugMessage")
    val debugMessage: Any?= null, // null
    @SerializedName("responseCode")
    val responseCode: Int?= 0 // 2000
) {
    data class Data(
        @SerializedName("contentId")
        val contentId: String?, // 1234
        @SerializedName("deviceId")
        val deviceId: String?, // abcd7
        @SerializedName("sessionId")
        val sessionId: String?, // 6c7ea942-4133-4c3c-9a39-cf59bdd2353e
        @SerializedName("startTime")
        val startTime: Long?, // 1703056912159
        @SerializedName("status")
        val status: String?, // ACTIVE
        @SerializedName("userId")
        val userId: String? // 1504
    )
}