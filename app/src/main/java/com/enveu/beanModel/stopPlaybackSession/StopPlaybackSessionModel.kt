package app.doxzilla.bean_model.active_device_management.stopPlaybackSession


import com.google.gson.annotations.SerializedName

data class StopPlaybackSessionModel(
    @SerializedName("data")
    val `data`: Any?, // null
    @SerializedName("debugMessage")
    val debugMessage: String?, // No session found with this session Id
    @SerializedName("responseCode")
    val responseCode: Int? // 4098
)