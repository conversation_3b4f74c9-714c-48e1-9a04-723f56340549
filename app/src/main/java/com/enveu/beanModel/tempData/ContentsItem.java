package com.enveu.beanModel.tempData;

import java.util.List;

public class ContentsItem {
    private Object vastTag;
    private Object transcodedFlavoursPlaylistLinks;
    private String description;
    private List<Object> assetKeywords;
    private int likeCount;
    private String title;
    private String transcodingJobId;
    private Object svod;
    private ContentProvider contentProvider;
    private int duration;
    private List<AssetCastItem> assetCast;
    private String landscapeImage;
    private boolean premium;
    private Object price;
    private List<AssetGenresItem> assetGenres;
    private Object season;
    private int id;
    private String portraitImage;
    private String sku;
    private Object thumbnailURL;
    private Object tvod;
    private String posterLandscapeImage;
    private Object episodeNo;
    private String assetLink;
    private boolean isNew;
    private Object transcodedMasterPlaylistLink;
    private String assetType;
    private int commentCount;
    private String posterPortraitImage;
    private Object series;
    private List<PlansItem> plans;
    private String contentUniquenessType;
    private long publishedDate;
    private String status;

    public Object getVastTag() {
        return vastTag;
    }

    public void setVastTag(Object vastTag) {
        this.vastTag = vastTag;
    }

    public Object getTranscodedFlavoursPlaylistLinks() {
        return transcodedFlavoursPlaylistLinks;
    }

    public void setTranscodedFlavoursPlaylistLinks(Object transcodedFlavoursPlaylistLinks) {
        this.transcodedFlavoursPlaylistLinks = transcodedFlavoursPlaylistLinks;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Object> getAssetKeywords() {
        return assetKeywords;
    }

    public void setAssetKeywords(List<Object> assetKeywords) {
        this.assetKeywords = assetKeywords;
    }

    public int getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(int likeCount) {
        this.likeCount = likeCount;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTranscodingJobId() {
        return transcodingJobId;
    }

    public void setTranscodingJobId(String transcodingJobId) {
        this.transcodingJobId = transcodingJobId;
    }

    public Object getSvod() {
        return svod;
    }

    public void setSvod(Object svod) {
        this.svod = svod;
    }

    public ContentProvider getContentProvider() {
        return contentProvider;
    }

    public void setContentProvider(ContentProvider contentProvider) {
        this.contentProvider = contentProvider;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public List<AssetCastItem> getAssetCast() {
        return assetCast;
    }

    public void setAssetCast(List<AssetCastItem> assetCast) {
        this.assetCast = assetCast;
    }

    public String getLandscapeImage() {
        return landscapeImage;
    }

    public void setLandscapeImage(String landscapeImage) {
        this.landscapeImage = landscapeImage;
    }

    public boolean isPremium() {
        return premium;
    }

    public void setPremium(boolean premium) {
        this.premium = premium;
    }

    public Object getPrice() {
        return price;
    }

    public void setPrice(Object price) {
        this.price = price;
    }

    public List<AssetGenresItem> getAssetGenres() {
        return assetGenres;
    }

    public void setAssetGenres(List<AssetGenresItem> assetGenres) {
        this.assetGenres = assetGenres;
    }

    public Object getSeason() {
        return season;
    }

    public void setSeason(Object season) {
        this.season = season;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPortraitImage() {
        return portraitImage;
    }

    public void setPortraitImage(String portraitImage) {
        this.portraitImage = portraitImage;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Object getThumbnailURL() {
        return thumbnailURL;
    }

    public void setThumbnailURL(Object thumbnailURL) {
        this.thumbnailURL = thumbnailURL;
    }

    public Object getTvod() {
        return tvod;
    }

    public void setTvod(Object tvod) {
        this.tvod = tvod;
    }

    public String getPosterLandscapeImage() {
        return posterLandscapeImage;
    }

    public void setPosterLandscapeImage(String posterLandscapeImage) {
        this.posterLandscapeImage = posterLandscapeImage;
    }

    public Object getEpisodeNo() {
        return episodeNo;
    }

    public void setEpisodeNo(Object episodeNo) {
        this.episodeNo = episodeNo;
    }

    public String getAssetLink() {
        return assetLink;
    }

    public void setAssetLink(String assetLink) {
        this.assetLink = assetLink;
    }

    public boolean isIsNew() {
        return isNew;
    }

    public void setIsNew(boolean isNew) {
        this.isNew = isNew;
    }

    public Object getTranscodedMasterPlaylistLink() {
        return transcodedMasterPlaylistLink;
    }

    public void setTranscodedMasterPlaylistLink(Object transcodedMasterPlaylistLink) {
        this.transcodedMasterPlaylistLink = transcodedMasterPlaylistLink;
    }

    public String getAssetType() {
        return assetType;
    }

    public void setAssetType(String assetType) {
        this.assetType = assetType;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public String getPosterPortraitImage() {
        return posterPortraitImage;
    }

    public void setPosterPortraitImage(String posterPortraitImage) {
        this.posterPortraitImage = posterPortraitImage;
    }

    public Object getSeries() {
        return series;
    }

    public void setSeries(Object series) {
        this.series = series;
    }

    public List<PlansItem> getPlans() {
        return plans;
    }

    public void setPlans(List<PlansItem> plans) {
        this.plans = plans;
    }

    public String getContentUniquenessType() {
        return contentUniquenessType;
    }

    public void setContentUniquenessType(String contentUniquenessType) {
        this.contentUniquenessType = contentUniquenessType;
    }

    public long getPublishedDate() {
        return publishedDate;
    }

    public void setPublishedDate(long publishedDate) {
        this.publishedDate = publishedDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return
                "ContentsItem{" +
                        "vastTag = '" + vastTag + '\'' +
                        ",transcodedFlavoursPlaylistLinks = '" + transcodedFlavoursPlaylistLinks + '\'' +
                        ",description = '" + description + '\'' +
                        ",assetKeywords = '" + assetKeywords + '\'' +
                        ",likeCount = '" + likeCount + '\'' +
                        ",title = '" + title + '\'' +
                        ",transcodingJobId = '" + transcodingJobId + '\'' +
                        ",svod = '" + svod + '\'' +
                        ",contentProvider = '" + contentProvider + '\'' +
                        ",duration = '" + duration + '\'' +
                        ",assetCast = '" + assetCast + '\'' +
                        ",landscapeImage = '" + landscapeImage + '\'' +
                        ",premium = '" + premium + '\'' +
                        ",price = '" + price + '\'' +
                        ",assetGenres = '" + assetGenres + '\'' +
                        ",season = '" + season + '\'' +
                        ",id = '" + id + '\'' +
                        ",portraitImage = '" + portraitImage + '\'' +
                        ",sku = '" + sku + '\'' +
                        ",thumbnailURL = '" + thumbnailURL + '\'' +
                        ",tvod = '" + tvod + '\'' +
                        ",posterLandscapeImage = '" + posterLandscapeImage + '\'' +
                        ",episodeNo = '" + episodeNo + '\'' +
                        ",assetLink = '" + assetLink + '\'' +
                        ",isNew = '" + isNew + '\'' +
                        ",transcodedMasterPlaylistLink = '" + transcodedMasterPlaylistLink + '\'' +
                        ",assetType = '" + assetType + '\'' +
                        ",commentCount = '" + commentCount + '\'' +
                        ",posterPortraitImage = '" + posterPortraitImage + '\'' +
                        ",series = '" + series + '\'' +
                        ",plans = '" + plans + '\'' +
                        ",contentUniquenessType = '" + contentUniquenessType + '\'' +
                        ",publishedDate = '" + publishedDate + '\'' +
                        ",status = '" + status + '\'' +
                        "}";
    }
}