package app.doxzilla.bean_model.active_device_management.updatePlayBackSession


import com.google.gson.annotations.SerializedName

data class UpdatePlaybackSessionModel(
    @SerializedName("data")
    val `data`: Any?= null, // null
    @SerializedName("debugMessage")
    val debugMessage: String?= null, // No session found with this session Id
    @SerializedName("responseCode")
    val responseCode: Int?= null // 4098
)