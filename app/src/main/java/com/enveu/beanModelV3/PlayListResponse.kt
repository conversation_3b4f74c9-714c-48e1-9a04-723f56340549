package com.enveu.beanModelV3

import com.enveu.beanModelV3.videoDetailV3.list.ImagesItem
import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class PlayListResponse(
    @SerializedName("data") val data: List<PlaylistItem>,
    @SerializedName("responseCode") val responseCode: Int,
    @SerializedName("debugMessage") val debugMessage: String?
) : Serializable

data class PlaylistItem(
    @SerializedName("id") val id: Int,
    @SerializedName("displayTitle") val displayTitle: String,
    @SerializedName("displayDescription") val displayDescription: String?,
    @SerializedName("images") val images: List<ImagesItem>,
    @SerializedName("contents") val contents: Any?
) : Serializable {

    data class ImageItem(
        @SerializedName("dateCreated") val dateCreated: Long?,
        @SerializedName("lastUpdated") val lastUpdated: Long?,
        @SerializedName("id") val id: Int?,
        @SerializedName("title") val title: String?,
        @SerializedName("description") val description: String?,
        @SerializedName("longDescription") val longDescription: String?,
        @SerializedName("contentType") val contentType: String?,
        @SerializedName("keywords") val keywords: Any?,
        @SerializedName("targetingTags") val targetingTags: Any?,
        @SerializedName("premium") val premium: Boolean?,
        @SerializedName("sku") val sku: String?,
        @SerializedName("publishedDate") val publishedDate: String?,
        @SerializedName("customData") val customData: String?,
        @SerializedName("parentalRating") val parentalRating: String?,
        @SerializedName("parentContent") val parentContent: String?,
        @SerializedName("video") val video: String?,
        @SerializedName("audioContent") val audioContent: String?,
        @SerializedName("externalRefId") val externalRefId: String?,
        @SerializedName("analyticsId") val analyticsId: String?,
        @SerializedName("contentSource") val contentSource: String?,
        @SerializedName("customContent") val customContent: String?,
        @SerializedName("liveContent") val liveContent: String?,
        @SerializedName("personContent") val personContent: String?,
        @SerializedName("articleContent") val articleContent: String?,
        @SerializedName("contentReviewRating") val contentReviewRating: String?,
        @SerializedName("styleInfo") val styleInfo: String?,
        @SerializedName("seoInfo") val seoInfo: String?,
        @SerializedName("imageContent") val imageContent: ImageContent?,
        @SerializedName("images") val images: String?,
        @SerializedName("accessibility") val accessibility: String?,
        @SerializedName("contentSlug") val contentSlug: String?,
        @SerializedName("organizationId") val organizationId: String?,
        @SerializedName("playCount") val playCount: Int?,
        @SerializedName("mediaType") val mediaType: String?,
        @SerializedName("likeCount") val likeCount: Int?,
        @SerializedName("shareCount") val shareCount: Int?,
        @SerializedName("commentCount") val commentCount: Int?
    ) : Serializable

    data class ImageContent(
        @SerializedName("id") val id: Long,
        @SerializedName("imageKey") val imageKey: String,
        @SerializedName("src") val src: String,
        @SerializedName("height") val height: Float,
        @SerializedName("width") val width: Float,
        @SerializedName("colorPalette") val colorPalette: List<String>,
        @SerializedName("dominantColor") val dominantColor: String,
        @SerializedName("imageType") val imageType: String,
        @SerializedName("status") val status: String?,
        @SerializedName("tag") val tag: String?,
        @SerializedName("originalImageSizeInBytes") val originalImageSizeInBytes: Int,
        @SerializedName("isDefault") val isDefault: Boolean?,
        @SerializedName("showTitle") val showTitle: Boolean? // corrected to Boolean?
    ) : Serializable
}
