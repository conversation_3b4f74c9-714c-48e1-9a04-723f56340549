package com.enveu.beanModelV3


import com.enveu.beanModelV3.playListModelV2.ArticleContent
import com.enveu.beanModelV3.playListModelV2.VideosItem
import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class RelatedRailsCommonData(
    @SerializedName("data")
    val `data`: Data?,
    @SerializedName("debugMessage")
    val debugMessage: Any?, // null
    @SerializedName("responseCode")
    val responseCode: Int? // 2000
) : Serializable{
    data class Data(
        @SerializedName("items")
        val items: List<Item?>?,
        @SerializedName("pageNumber")
        val pageNumber: Int?, // 0
        @SerializedName("pageSize")
        val pageSize: Int?, // 20
        @SerializedName("totalElements")
        val totalElements: Int?, // 39
        @SerializedName("totalPages")
        val totalPages: Int? // 2
    ) : Serializable {
        data class Item(
            @SerializedName("accessibility")
            val accessibility: Accessibility?,
            @SerializedName("analyticsId")
            val analyticsId: Any?, // null
            @SerializedName("articleContent")
            val articleContent: ArticleContent?, // null
            @SerializedName("audioContent")
            val audioContent: Any?, // null
            @SerializedName("contentReviewRating")
            val contentReviewRating: Any?, // null
            @SerializedName("contentSlug")
            val contentSlug: String?, // mahesh-bhatt
            @SerializedName("contentSource")
            val contentSource: String?, // ENVEU_OVP
            @SerializedName("contentType")
            val contentType: String?, // PERSON
            @SerializedName("customContent")
            val customContent: Any?, // null
            @SerializedName("customData")
            val customData: CustomData?,
            @SerializedName("dateCreated")
            val dateCreated: Long?, // 1718868053409
            @SerializedName("description")
            val description: String?, // Jennifer Lynn Affleck, also known by her nickname J.Lo, is an American actress, singer, dancer and businesswoman. Lopez is regarded as one of the most influential entertainers of her time
            @SerializedName("externalRefId")
            val externalRefId: Any?, // null
            @SerializedName("id")
            val id: Int?, // 217
            @SerializedName("imageContent")
            val imageContent: Any?, // null
            @SerializedName("images")
            val images: List<Image?>?,
            @SerializedName("keywords")
            val keywords: List<String?>?,
            @SerializedName("lastUpdated")
            val lastUpdated: Long?, // 1720591219380
            @SerializedName("liveContent")
            val liveContent: Any?, // null
            @SerializedName("longDescription")
            val longDescription: String?, // <p>bncxhgvjcxfvjvcxgfhkjhgfcfghmcxvbnmjhytfdrs</p>
            @SerializedName("organizationId")
            val organizationId: Int?, // 1
            @SerializedName("parentContent")
            val parentContent: Any?, // null
            @SerializedName("parentalRating")
            val parentalRating: Any?, // null
            @SerializedName("personContent")
            val personContent: PersonContent?,
            @SerializedName("playCount")
            val playCount: Int?, // 0
            @SerializedName("premium")
            val premium: Boolean?, // true
            @SerializedName("publishedDate")
            val publishedDate: Any?, // null
            @SerializedName("seoInfo")
            val seoInfo: SeoInfo?,
            @SerializedName("sku")
            val sku: String?, // MEDIA_1eb5f654-1920-4cd0-b931-6fbf23a019cb
            @SerializedName("styleInfo")
            val styleInfo: Any?, // null
            @SerializedName("targetingTags")
            val targetingTags: List<String?>?,
            @SerializedName("title")
            val title: String?, // Mahesh bhatt
            @SerializedName("video")
            val video: Object? // null
        ) : Serializable {
            data class Accessibility(
                @SerializedName("accessibilitySchedule")
                val accessibilitySchedule: Any?, // null
                @SerializedName("checkAccessibility")
                val checkAccessibility: Boolean? // false
            ) : Serializable

            data class CustomData(
                @SerializedName("artist-albums-ids")
                val artistAlbumsIds: List<ArtistAlbumsId?>?,
                @SerializedName("artist-podcasts-ids")
                val artistPodcastsIds: List<Any?>?,
                @SerializedName("artist-songs-ids")
                val artistSongsIds: List<ArtistSongsId?>?,
                @SerializedName("genres")
                val genres: List<Genre?>?,
                @SerializedName("is_exclusive")
                val isExclusive: String?, // false
                @SerializedName("rating")
                val rating: Any?, // null
                @SerializedName("sub-genres")
                val subGenres: List<SubGenre?>?
            ) : Serializable{
                data class ArtistAlbumsId(
                    @SerializedName("contentSlug")
                    val contentSlug: String?, // bryan-adams-getup
                    @SerializedName("contentType")
                    val contentType: String?, // CUSTOM
                    @SerializedName("description")
                    val description: String?,
                    @SerializedName("externalIdentifier")
                    val externalIdentifier: Any?, // null
                    @SerializedName("id")
                    val id: Int?, // 416
                    @SerializedName("images")
                    val images: List<Image?>?,
                    @SerializedName("mediaType")
                    val mediaType: String?, // ALBUMS
                    @SerializedName("title")
                    val title: String? // Bryan Adams - GetUp
                ) : Serializable{
                    data class Image(
                        @SerializedName("colorPalette")
                        val colorPalette: List<String?>?,
                        @SerializedName("dominantColor")
                        val dominantColor: String?, // #0c0c0c
                        @SerializedName("height")
                        val height: Double?, // 544.0
                        @SerializedName("id")
                        val id: Int?, // 135
                        @SerializedName("imageKey")
                        val imageKey: String?, // BR3_1720260393746.jpg
                        @SerializedName("imageType")
                        val imageType: String?, // 1x1
                        @SerializedName("isDefault")
                        val isDefault: Boolean?, // false
                        @SerializedName("originalImageSizeInBytes")
                        val originalImageSizeInBytes: Int?, // 67779
                        @SerializedName("showTitle")
                        val showTitle: Boolean?, // false
                        @SerializedName("src")
                        val src: String?, // https://d3ffi1kkogkk1a.cloudfront.net/imageStore/BR3_1720260393746.jpg
                        @SerializedName("status")
                        val status: String?, // PUBLISHED
                        @SerializedName("tag")
                        val tag: String?, // img1
                        @SerializedName("width")
                        val width: Double? // 544.0
                    ) : Serializable
                }

                data class ArtistSongsId(
                    @SerializedName("contentSlug")
                    val contentSlug: String?, // 8-psalm-51mp3
                    @SerializedName("contentType")
                    val contentType: String?, // AUDIO
                    @SerializedName("description")
                    val description: String?,
                    @SerializedName("externalIdentifier")
                    val externalIdentifier: String?, // v3GNsO6B
                    @SerializedName("id")
                    val id: Int?, // 87
                    @SerializedName("images")
                    val images: List<Image?>?,
                    @SerializedName("mediaType")
                    val mediaType: String?, // SONGS
                    @SerializedName("title")
                    val title: String? // 8. Psalm 51.mp3
                ) : Serializable {
                    data class Image(
                        @SerializedName("colorPalette")
                        val colorPalette: List<String?>?,
                        @SerializedName("dominantColor")
                        val dominantColor: String?, // #e0d5c8
                        @SerializedName("height")
                        val height: Double?, // 3000.0
                        @SerializedName("id")
                        val id: Int?, // 29
                        @SerializedName("imageKey")
                        val imageKey: String?, // cover_1718175501782.jpg
                        @SerializedName("imageType")
                        val imageType: String?, // 1x1
                        @SerializedName("isDefault")
                        val isDefault: Boolean?, // false
                        @SerializedName("originalImageSizeInBytes")
                        val originalImageSizeInBytes: Int?, // 7479006
                        @SerializedName("showTitle")
                        val showTitle: Boolean?, // false
                        @SerializedName("src")
                        val src: String?, // https://d3ffi1kkogkk1a.cloudfront.net/imageStore/cover_1718175501782.jpg
                        @SerializedName("status")
                        val status: String?, // PUBLISHED
                        @SerializedName("tag")
                        val tag: String?, // img1
                        @SerializedName("width")
                        val width: Double? // 3000.0
                    ) : Serializable
                }

                data class Genre(
                    @SerializedName("contentSlug")
                    val contentSlug: String?, // gospel
                    @SerializedName("contentType")
                    val contentType: String?, // CUSTOM
                    @SerializedName("description")
                    val description: String?,
                    @SerializedName("externalIdentifier")
                    val externalIdentifier: Any?, // null
                    @SerializedName("id")
                    val id: Int?, // 375
                    @SerializedName("images")
                    val images: List<Image?>?,
                    @SerializedName("mediaType")
                    val mediaType: String?, // GENRES
                    @SerializedName("title")
                    val title: String? // Gospel
                ) : Serializable{
                    data class Image(
                        @SerializedName("colorPalette")
                        val colorPalette: List<String?>?,
                        @SerializedName("dominantColor")
                        val dominantColor: String?, // #4f4339
                        @SerializedName("height")
                        val height: Double?, // 200.0
                        @SerializedName("id")
                        val id: Long?, // 139
                        @SerializedName("imageKey")
                        val imageKey: String?, // 04-52-39-720_200x200_1720425320368.jpg
                        @SerializedName("imageType")
                        val imageType: String?, // 1x1
                        @SerializedName("isDefault")
                        val isDefault: Boolean?, // false
                        @SerializedName("originalImageSizeInBytes")
                        val originalImageSizeInBytes: Int?, // 80499
                        @SerializedName("showTitle")
                        val showTitle: Boolean?, // false
                        @SerializedName("src")
                        val src: String?, // https://d3ffi1kkogkk1a.cloudfront.net/imageStore/04-52-39-720_200x200_1720425320368.jpg
                        @SerializedName("status")
                        val status: String?, // PUBLISHED
                        @SerializedName("tag")
                        val tag: String?, // img1
                        @SerializedName("width")
                        val width: Double? // 200.0
                    ) : Serializable
                }

                data class SubGenre(
                    @SerializedName("contentSlug")
                    val contentSlug: String?, // jazz
                    @SerializedName("contentType")
                    val contentType: String?, // CUSTOM
                    @SerializedName("description")
                    val description: String?, // test description
                    @SerializedName("externalIdentifier")
                    val externalIdentifier: Any?, // null
                    @SerializedName("id")
                    val id: Int?, // 441
                    @SerializedName("images")
                    val images: List<Any?>?,
                    @SerializedName("mediaType")
                    val mediaType: String?, // SUB_GENRES
                    @SerializedName("title")
                    val title: String? // Jazz
                ) : Serializable
            }

            data class Image(
                @SerializedName("accessibility")
                val accessibility: Accessibility?,
                @SerializedName("analyticsId")
                val analyticsId: Any?, // null
                @SerializedName("articleContent")
                val articleContent: Any?, // null
                @SerializedName("audioContent")
                val audioContent: Any?, // null
                @SerializedName("contentReviewRating")
                val contentReviewRating: Any?, // null
                @SerializedName("contentSlug")
                val contentSlug: String?, // ศิริพลศิรินินาทกุล-1jpg
                @SerializedName("contentSource")
                val contentSource: String?, // ENVEU_OVP
                @SerializedName("contentType")
                val contentType: String?, // IMAGE
                @SerializedName("customContent")
                val customContent: Any?, // null
                @SerializedName("customData")
                val customData: CustomData?,
                @SerializedName("dateCreated")
                val dateCreated: Long?, // 1718862617018
                @SerializedName("description")
                val description: String?,
                @SerializedName("externalRefId")
                val externalRefId: Any?, // null
                @SerializedName("id")
                val id: Int?, // 209
                @SerializedName("imageContent")
                val imageContent: ImageContent?,
                @SerializedName("images")
                val images: Any?, // null
                @SerializedName("keywords")
                val keywords: List<Any?>?,
                @SerializedName("lastUpdated")
                val lastUpdated: Long?, // 1718862652860
                @SerializedName("liveContent")
                val liveContent: Any?, // null
                @SerializedName("longDescription")
                val longDescription: Any?, // null
                @SerializedName("organizationId")
                val organizationId: Int?, // 1
                @SerializedName("parentContent")
                val parentContent: Any?, // null
                @SerializedName("parentalRating")
                val parentalRating: Any?, // null
                @SerializedName("personContent")
                val personContent: Any?, // null
                @SerializedName("playCount")
                val playCount: Int?, // 0
                @SerializedName("premium")
                val premium: Boolean?, // false
                @SerializedName("publishedDate")
                val publishedDate: Any?, // null
                @SerializedName("seoInfo")
                val seoInfo: Any?, // null
                @SerializedName("sku")
                val sku: String?, // MEDIA_068479c4-d6f5-4470-a5e8-ae1fe408d633
                @SerializedName("styleInfo")
                val styleInfo: Any?, // null
                @SerializedName("targetingTags")
                val targetingTags: List<Any?>?,
                @SerializedName("title")
                val title: String?, // ศิริพล+ศิรินินาทกุล (1).jpg
                @SerializedName("video")
                val video: Any? // null
            ) : Serializable{
                data class Accessibility(
                    @SerializedName("accessibilitySchedule")
                    val accessibilitySchedule: Any?, // null
                    @SerializedName("checkAccessibility")
                    val checkAccessibility: Boolean? // false
                ) : Serializable


                data class ImageContent(
                    @SerializedName("colorPalette")
                    val colorPalette: List<String?>?,
                    @SerializedName("dominantColor")
                    val dominantColor: String?, // #c0ada6
                    @SerializedName("height")
                    val height: Double?, // 900.0
                    @SerializedName("id")
                    val id: Long?, // 86
                    @SerializedName("imageKey")
                    val imageKey: String?, // ศิริพล+ศิรินินาทกุล (3)_1718862645933.jpg
                    @SerializedName("imageType")
                    val imageType: String?, // 1x1
                    @SerializedName("isDefault")
                    val isDefault: Boolean?, // false
                    @SerializedName("originalImageSizeInBytes")
                    val originalImageSizeInBytes: Int?, // 356044
                    @SerializedName("showTitle")
                    val showTitle: Boolean?, // false
                    @SerializedName("src")
                    val src: String?, // https://d3ffi1kkogkk1a.cloudfront.net/imageStore/ศิริพล+ศิรินินาทกุล (3)_1718862645933.jpg
                    @SerializedName("status")
                    val status: String?, // PUBLISHED
                    @SerializedName("tag")
                    val tag: String?, // img1
                    @SerializedName("width")
                    val width: Double? // 900.0
                )
            }

            data class PersonContent(
                @SerializedName("dateCreated")
                val dateCreated: Long?, // 1718868053413
                @SerializedName("id")
                val id: Int?, // 20
                @SerializedName("lastUpdated")
                val lastUpdated: Long?, // 1718868053413
                @SerializedName("personType")
                val personType: String?, // ARTISTS
                @SerializedName("personalInfo")
                val personalInfo: PersonalInfo?
            ) {
                data class PersonalInfo(
                    @SerializedName("dob")
                    val dob: Any?, // null
                    @SerializedName("gender")
                    val gender: Any?, // null
                    @SerializedName("height")
                    val height: Any?, // null
                    @SerializedName("weight")
                    val weight: Any? // null
                )
            }

            data class SeoInfo(
                @SerializedName("description")
                val description: String?,
                @SerializedName("seoMetaInfo")
                val seoMetaInfo: SeoMetaInfo?,
                @SerializedName("tags")
                val tags: List<Any?>?,
                @SerializedName("title")
                val title: String?
            ) {
                class SeoMetaInfo
            }
        }
    }
}