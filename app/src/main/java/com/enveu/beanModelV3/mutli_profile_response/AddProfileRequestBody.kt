package com.enveu.beanModelV3.mutli_profile_response

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class AddProfileRequestBody (

    @field:SerializedName("name")
    var name: String? = null,

    @field:SerializedName("userName")
    var userName: String? = null,

    @field:SerializedName("gender")
    var gender: String? = null,

    @field:SerializedName("bio")
    var bio: String? = null,

    @field:SerializedName("profilePicURL")
    var profilePicURL: String? = null,

    @field:SerializedName("dateOfBirth")
    var dateOfBirth: Any? = null,

    @field:SerializedName("accountId")
    val accountId: String? = null,

    @field:SerializedName("preferenceSettings")
    var preferenceSettings: PreferenceSettings? = null,

    @field:SerializedName("customData")
    var customData: CustomDatas? = null,
)

data class CustomDatas(
    @field:SerializedName("parentalPinEnabledAccount")
    var parentalPinEnabledAccount: Boolean? = null,
)

