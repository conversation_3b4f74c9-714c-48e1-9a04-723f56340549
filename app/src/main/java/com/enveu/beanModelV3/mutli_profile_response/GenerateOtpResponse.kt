package com.enveu.beanModelV3.mutli_profile_response

import com.google.gson.annotations.SerializedName

data class GenerateOtpResponse(

	@field:SerializedName("data")
	val data: GenerateOtpData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	val responseCode: Int? = null
)

data class GenerateOtpData(

	@field:SerializedName("otp")
	val otp: Any? = null,

	@field:SerializedName("msisdn")
	val msisdn: Any? = null,

	@field:SerializedName("email")
	val email: String? = null,

	@field:SerializedName("token")
	val token: String? = null
)
