package com.enveu.beanModelV3.mutli_profile_response

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class GetAllProfileResponse(

	@field:SerializedName("data")
	val data: List<SecondaryProfileData?>? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	val responseCode: Int? = null
):Serializable

data class PreferenceSettings(

	@field:SerializedName("genresIds")
	var genresIds: List<Int?>? = null,

	@field:SerializedName("parentalRatingId")
	val parentalRatingId: Int? = null,

	@field:SerializedName("isRestricedGenres")
	val isRestricedGenres: Boolean? = null
):Serializable

data class SecondaryProfileData(

	@field:SerializedName("lastLogin")
	val lastLogin: Any? = null,

	@field:SerializedName("profileStep")
	val profileStep: Any? = null,

	@field:SerializedName("isFbLinked")
	val isFbLinked: Boolean? = null,

	@field:SerializedName("subscriptions")
	val subscriptions: Any? = null,

	@field:SerializedName("customData")
	val customData: CustomData? = null,

	@field:SerializedName("firstTvodPurchase")
	val firstTvodPurchase: Any? = null,

	@field:SerializedName("accountVerificationDetails")
	val accountVerificationDetails: Any? = null,

	@field:SerializedName("householdId")
	val householdId: String? = null,

	@field:SerializedName("lastUpdated")
	val lastUpdated: Long? = null,

	@field:SerializedName("password")
	val password: Any? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("firstSubscriptionPurchase")
	val firstSubscriptionPurchase: Any? = null,

	@field:SerializedName("kidsAccount")
	val kidsAccount: Boolean? = null,

	@field:SerializedName("isPrimaryAccount")
	var isPrimaryAccount: Boolean? = null,

	@field:SerializedName("isParentAccount")
	var isParentAccount: Boolean? = null,

	@field:SerializedName("purchases")
	val purchases: Any? = null,

	@field:SerializedName("paddleCustomerId")
	val paddleCustomerId: Any? = null,

	@field:SerializedName("creatorContentId")
	val creatorContentId: Int? = null,

	@field:SerializedName("ageGroup")
	val ageGroup: Any? = null,

	@field:SerializedName("verificationDate")
	val verificationDate: Long? = null,

	@field:SerializedName("primaryAccount")
	val primaryAccount: Boolean? = null,

	@field:SerializedName("deletionRequestStatus")
	val deletionRequestStatus: Any? = null,

	@field:SerializedName("accountId")
	val accountId: String? = null,

	@field:SerializedName("bio")
	var bio: String? = null,

	@field:SerializedName("phoneNumber")
	val phoneNumber: Any? = null,

	@field:SerializedName("profilePicURL")
	var profilePicURL: String? = null,

	@field:SerializedName("primaryAccountRef")
	val primaryAccountRef: PrimaryAccountRef? = null,

	@field:SerializedName("name")
	var name: String? = null,

	@field:SerializedName("userName")
	var userName: String? = null,

	@field:SerializedName("countryName")
	val countryName: Any? = null,

	@field:SerializedName("userType")
	val userType: Any? = null,

	@field:SerializedName("status")
	val status: String? = null,

	@field:SerializedName("manualLinked")
	val manualLinked: Boolean? = null,

	@field:SerializedName("firstSubscriptionPurchaseDays")
	val firstSubscriptionPurchaseDays: Any? = null,

	@field:SerializedName("lastName")
	val lastName: String? = null,

	@field:SerializedName("gender")
    var gender: Any? = null,

	@field:SerializedName("secondaryAccounts")
	val secondaryAccounts: Any? = null,

	@field:SerializedName("hubSpotId")
	val hubSpotId: Any? = null,

	@field:SerializedName("appUserFavourites")
	val appUserFavourites: Any? = null,

	@field:SerializedName("referenceId")
	val referenceId: Any? = null,

	@field:SerializedName("expiryDate")
	val expiryDate: Any? = null,

	@field:SerializedName("userTier")
	val userTier: Any? = null,

	@field:SerializedName("emailValid")
	val emailValid: Any? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Long? = null,

	@field:SerializedName("cityName")
	val cityName: Any? = null,

	@field:SerializedName("isChurnedUser")
	val isChurnedUser: Any? = null,

	@field:SerializedName("appUserInterest")
	var appUserInterest: ArrayList<Int?>? = null,

	@field:SerializedName("countryCode")
	val countryCode: Any? = null,

	@field:SerializedName("firstPurchaseDays")
	val firstPurchaseDays: Any? = null,

	@field:SerializedName("firstTvodPurchaseDays")
	val firstTvodPurchaseDays: Any? = null,

	@field:SerializedName("marketingPreferences")
	val marketingPreferences: Any? = null,

	@field:SerializedName("email")
	val email: Any? = null,

	@field:SerializedName("verified")
	val verified: Boolean? = null,

	@field:SerializedName("appUserPlans")
	val appUserPlans: Any? = null,

	@field:SerializedName("dateOfBirth")
	var dateOfBirth: Any? = null,

	@field:SerializedName("userEconomics")
	val userEconomics: Any? = null,

	@field:SerializedName("fbLinked")
	val fbLinked: Boolean? = null,

	@field:SerializedName("preferenceSettings")
	val preferenceSettings: PreferenceSettings? = null,

	@field:SerializedName("activePlans")
	val activePlans: Any? = null,

	@field:SerializedName("deletionRequestIdentifier")
	val deletionRequestIdentifier: Any? = null,

	@field:SerializedName("marketingPreferencesDetails")
	val marketingPreferencesDetails: Any? = null,

	@field:SerializedName("allowedTrialNum")
	val allowedTrialNum: Any? = null,

	@field:SerializedName("firstPurchase")
	val firstPurchase: Any? = null,

	@field:SerializedName("gplusLinked")
	val gplusLinked: Boolean? = null
):Serializable

data class PrimaryAccountRef(

	@field:SerializedName("lastLogin")
	val lastLogin: Long? = null,

	@field:SerializedName("profileStep")
	val profileStep: Any? = null,

	@field:SerializedName("isFbLinked")
	val isFbLinked: Boolean? = null,

	@field:SerializedName("subscriptions")
	val subscriptions: Any? = null,

	@field:SerializedName("customData")
	val customData: Any? = null,

	@field:SerializedName("firstTvodPurchase")
	val firstTvodPurchase: Any? = null,

	@field:SerializedName("accountVerificationDetails")
	val accountVerificationDetails: Any? = null,

	@field:SerializedName("householdId")
	val householdId: String? = null,

	@field:SerializedName("lastUpdated")
	val lastUpdated: Long? = null,

	@field:SerializedName("password")
	val password: Any? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("firstSubscriptionPurchase")
	val firstSubscriptionPurchase: Any? = null,

	@field:SerializedName("kidsAccount")
	val kidsAccount: Boolean? = null,

	@field:SerializedName("purchases")
	val purchases: Any? = null,

	@field:SerializedName("paddleCustomerId")
	val paddleCustomerId: Any? = null,

	@field:SerializedName("ageGroup")
	val ageGroup: Any? = null,

	@field:SerializedName("verificationDate")
	val verificationDate: Any? = null,

	@field:SerializedName("primaryAccount")
	val primaryAccount: Boolean? = null,

	@field:SerializedName("deletionRequestStatus")
	val deletionRequestStatus: Any? = null,

	@field:SerializedName("accountId")
	val accountId: String? = null,

	@field:SerializedName("phoneNumber")
	val phoneNumber: Any? = null,

	@field:SerializedName("profilePicURL")
	val profilePicURL: Any? = null,

	@field:SerializedName("primaryAccountRef")
	val primaryAccountRef: Any? = null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("countryName")
	val countryName: Any? = null,

	@field:SerializedName("userType")
	val userType: Any? = null,

	@field:SerializedName("status")
	val status: String? = null,

	@field:SerializedName("manualLinked")
	val manualLinked: Boolean? = null,

	@field:SerializedName("firstSubscriptionPurchaseDays")
	val firstSubscriptionPurchaseDays: Any? = null,

	@field:SerializedName("lastName")
	val lastName: Any? = null,

	@field:SerializedName("gender")
	val gender: Any? = null,

	@field:SerializedName("secondaryAccounts")
	val secondaryAccounts: Any? = null,

	@field:SerializedName("hubSpotId")
	val hubSpotId: Any? = null,

	@field:SerializedName("appUserFavourites")
	val appUserFavourites: Any? = null,

	@field:SerializedName("referenceId")
	val referenceId: Any? = null,

	@field:SerializedName("expiryDate")
	val expiryDate: Any? = null,

	@field:SerializedName("userTier")
	val userTier: Any? = null,

	@field:SerializedName("emailValid")
	val emailValid: Boolean? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Long? = null,

	@field:SerializedName("cityName")
	val cityName: Any? = null,

	@field:SerializedName("isChurnedUser")
	val isChurnedUser: Any? = null,

	@field:SerializedName("appUserInterest")
	val appUserInterest: Any? = null,

	@field:SerializedName("countryCode")
	val countryCode: Any? = null,

	@field:SerializedName("firstPurchaseDays")
	val firstPurchaseDays: Any? = null,

	@field:SerializedName("firstTvodPurchaseDays")
	val firstTvodPurchaseDays: Any? = null,

	@field:SerializedName("marketingPreferences")
	val marketingPreferences: Boolean? = null,

	@field:SerializedName("email")
	val email: String? = null,

	@field:SerializedName("verified")
	val verified: Boolean? = null,

	@field:SerializedName("appUserPlans")
	val appUserPlans: Any? = null,

	@field:SerializedName("dateOfBirth")
	val dateOfBirth: Any? = null,

	@field:SerializedName("userEconomics")
	val userEconomics: Any? = null,

	@field:SerializedName("fbLinked")
	val fbLinked: Boolean? = null,

	@field:SerializedName("preferenceSettings")
	val preferenceSettings: Any? = null,

	@field:SerializedName("activePlans")
	val activePlans: Any? = null,

	@field:SerializedName("deletionRequestIdentifier")
	val deletionRequestIdentifier: Any? = null,

	@field:SerializedName("marketingPreferencesDetails")
	val marketingPreferencesDetails: Any? = null,

	@field:SerializedName("allowedTrialNum")
	val allowedTrialNum: Int? = null,

	@field:SerializedName("firstPurchase")
	val firstPurchase: Any? = null,

	@field:SerializedName("gplusLinked")
	val gplusLinked: Boolean? = null
):Serializable

data class CustomData(
	@field:SerializedName("address")
	val address: String? = null,
	@field:SerializedName("city")
	val city: String? = null,
	@field:SerializedName("country")
	val country: String? = null,
	@field:SerializedName("NotificationCheck")
	val notificationCheck: String? = null,
	@field:SerializedName("mobileNumber")
	val mobileNumber: String? = null,
	@field:SerializedName("parentalPin")
	var parentalPin: String? = null,
	@field:SerializedName("parentalPinEnabled")
	var parentalPinEnabled: Boolean? = null,
	@field:SerializedName("parentalPinEnabledAccount")
	var parentalPinEnabledAccount: String? = null,
):Serializable
