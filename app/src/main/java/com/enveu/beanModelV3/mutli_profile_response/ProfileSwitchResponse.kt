package com.enveu.beanModelV3.mutli_profile_response

import com.google.gson.annotations.SerializedName

data class ProfileSwitchResponse(

	@field:SerializedName("data")
	val data: ProfileSwitchData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	val responseCode: Int? = null
)

data class ProfileSwitchData(

	@field:SerializedName("accountId")
	val accountId: String? = null,

	@field:SerializedName("phoneNumber")
	val phoneNumber: Any? = null,

	@field:SerializedName("expiryTime")
	val expiryTime: Long? = null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("verified")
	val verified: Boolean? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("email")
	val email: Any? = null
)
