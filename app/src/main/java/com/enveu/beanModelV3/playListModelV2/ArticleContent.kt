package com.enveu.beanModelV3.playListModelV2

import com.enveu.bean_model_v2_0.videoDetailBean.Image
import com.google.gson.annotations.SerializedName

data class ArticleContent(
    @SerializedName("id"          ) var id          : Int?              = null,
    @SerializedName("articleType"  ) var articleType  : String?           = null,
    @SerializedName("articleBody"  ) var articleBody  : String?           = null,
    @SerializedName("images"      ) var images      : ArrayList<Image> = arrayListOf()


)