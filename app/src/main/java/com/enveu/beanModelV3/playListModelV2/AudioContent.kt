package com.enveu.beanModelV3.playListModelV2

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class AudioContent(

    @field:SerializedName("duration")
    val duration: Any? = null,

    @field:SerializedName("offlineEnabled")
    val offlineEnabled: Boolean? = null,

    @field:SerializedName("audioType")
    val audioType: String? = null,

    @field:SerializedName("audios")
    val audios: List<AudiosItem?>? = null,

    @field:SerializedName("hostingSource")
    val hostingSource: String? = null

) : Serializable

data class AudiosItem(

    @field:SerializedName("default")
    val jsonMemberDefault: Boolean? = null,

    @field:SerializedName("langCode")
    val langCode: String? = null,

    @field:SerializedName("encodingRates")
    val encodingRates: List<Any?>? = null,

    @field:SerializedName("name")
    val name: String? = null,

    @field:SerializedName("externalIdentifier")
    val externalIdentifier: String? = null,

    @field:SerializedName("language")
    val language: String? = null,

    @field:SerializedName("id")
    val id: Any? = null,

    @field:SerializedName("type")
    val type: String? = null,

    @field:SerializedName("status")
    val status: String? = null

) : Serializable
