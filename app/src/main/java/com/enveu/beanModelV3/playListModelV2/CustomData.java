package com.enveu.beanModelV3.playListModelV2;

import com.enveu.beanModelV3.videoDetailV3.SongsAlbumIdItem;
import com.enveu.client.playlist.beanv2_0.CommonCustomData;
import com.enveu.client.playlist.beanv2_0.ReelCreatorIdModel;
import com.google.gson.annotations.SerializedName;
import com.enveu.beanModelV3.videoDetailV3.ArtistAlbumIdItem;
import com.enveu.beanModelV3.videoDetailV3.ArtistSongIdItem;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class CustomData implements Serializable {

//	@SerializedName("episode-season-id")
//	private CommonCustomData episodeSeasonId;
//
//	@SerializedName("episode-series-id")
//	private CommonCustomData episodeSeriesId;


//	@SerializedName("episode-season-id")
//	private String episodeSeasonId;

	@SerializedName("episode-series-id")
	private String episodeSeriesId;

	@SerializedName("genres")
	private ArrayList<CommonCustomData> genres;

	@SerializedName("trailer-linked-with-id")
	private List<CommonCustomData> trailerLinkedWithId;

	@SerializedName("reel-creator-id")
	private ReelCreatorIdModel reelCreatorId;

	@SerializedName("season-number")
	private String seasonNumber;


	@SerializedName("ArtistAlbumId")
	private List<ArtistAlbumIdItem> artistAlbumId;

	@SerializedName("songs-albums-id")
	private SongsAlbumIdItem songs_albums_id;
	@SerializedName("songs-artist-ids")
	private List<SongsAlbumIdItem> songs_artist_ids;

	public List<ArtistAlbumIdItem> getArtistAlbumId(){
		return artistAlbumId;
	}

	@SerializedName("ArtistSongId")
	private List<ArtistSongIdItem> artistSongId;

	@SerializedName("trackNumber")
	private String trackNumber;

	@SerializedName("is_exclusive")
	private String isExclusive;

	@SerializedName("followers")
	private String followers;

	public String getFollowers() {
		return followers;
	}

	public void setFollowers(String followers) {
		this.followers = followers;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public ReelCreatorIdModel getReelCreatorId() {
		return reelCreatorId;
	}

	public void setReelCreatorId(ReelCreatorIdModel reelCreatorId) {
		this.reelCreatorId = reelCreatorId;
	}

	public String getFollowing() {
		return following;
	}

	public void setFollowing(String following) {
		this.following = following;
	}

	@SerializedName("following")
	private String following;

	@SerializedName("userName")
	private String userName;

	public String getIsExclusive() {
		return isExclusive;
	}

	public void setIsExclusive(String isExclusive) {
		this.isExclusive = isExclusive;
	}

	public String getTrackNumber() {
		return trackNumber;
	}

	public List<ArtistSongIdItem> getArtistSongId(){
		return artistSongId;
	}

	public SongsAlbumIdItem getSongs_albums_id() {
		return songs_albums_id;
	}
	public List<SongsAlbumIdItem> getSongs_artist_ids() {
		return songs_artist_ids;
	}

//	public CommonCustomData getEpisodeSeasonId() {
//		return episodeSeasonId;
//	}
//
//	public CommonCustomData getEpisodeSeriesId() {
//		return episodeSeriesId;
//	}

//	public String getEpisodeSeasonId() {
//		return episodeSeasonId;
//	}

	public String getEpisodeSeriesId() {
		return episodeSeriesId;
	}

	public ArrayList<CommonCustomData> getGenres() {
		return genres;
	}

	public List<CommonCustomData> getTrailerLinkedWithId() {
		return trailerLinkedWithId;
	}

	public String getSeasonNumber() {
		return seasonNumber;
	}
}