package com.enveu.beanModelV3.searchGenres


import com.google.gson.annotations.SerializedName
import org.json.JSONObject

data class SearchGenres(
    @SerializedName("data")
    val `data`: Data?,
    @SerializedName("debugMessage")
    val debugMessage: Any?, // null
    @SerializedName("responseCode")
    val responseCode: Int? // 2000
) {
    data class Data(
        @SerializedName("items")
        val items: List<Item?>?,
        @SerializedName("pageNumber")
        val pageNumber: Int?, // 0
        @SerializedName("pageSize")
        val pageSize: Int?, // 20
        @SerializedName("totalElements")
        val totalElements: Int?, // 13
        @SerializedName("totalPages")
        val totalPages: Int? // 1
    ) {
        data class Item(
            @SerializedName("accessibility")
            val accessibility: Accessibility?,
            @SerializedName("analyticsId")
            val analyticsId: Any?, // null
            @SerializedName("articleContent")
            val articleContent: Any?, // null
            @SerializedName("audioContent")
            val audioContent: Any?, // null
            @SerializedName("contentReviewRating")
            val contentReviewRating: Any?, // null
            @SerializedName("contentSlug")
            val contentSlug: String?, // fresh-releases
            @SerializedName("contentSource")
            val contentSource: String?, // ENVEU_OVP
            @SerializedName("contentType")
            val contentType: String?, // CUSTOM
            @SerializedName("customContent")
            val customContent: CustomContent?,
            @SerializedName("customData")
            val customData: CustomData?,
            @SerializedName("dateCreated")
            val dateCreated: Long?, // 1719997067850
            @SerializedName("description")
            val description: String?, // Rock Music
            @SerializedName("externalRefId")
            val externalRefId: Any?, // null
            @SerializedName("id")
            val id: Int?, // 345
            @SerializedName("isSelectedItems")
            var isSelectedItems: Boolean?, // 345
            @SerializedName("imageContent")
            val imageContent: Any?, // null
            @SerializedName("images")
            val images: List<Image?>?,
            @SerializedName("keywords")
            val keywords: List<Any?>?,
            @SerializedName("lastUpdated")
            val lastUpdated: Long?, // 1719997069473
            @SerializedName("liveContent")
            val liveContent: Any?, // null
            @SerializedName("longDescription")
            val longDescription: Any?, // null
            @SerializedName("organizationId")
            val organizationId: Int?, // 1
            @SerializedName("parentContent")
            val parentContent: Any?, // null
            @SerializedName("parentalRating")
            val parentalRating: Any?, // null
            @SerializedName("personContent")
            val personContent: Any?, // null
            @SerializedName("premium")
            val premium: Boolean?, // false
            @SerializedName("publishedDate")
            val publishedDate: Long?, // 1718610449294
            @SerializedName("seoInfo")
            val seoInfo: SeoInfo?,
            @SerializedName("sku")
            val sku: String?, // MEDIA_ee4766fd-e0f7-4476-a9f1-17200855b1a1
            @SerializedName("styleInfo")
            val styleInfo: Any?, // null
            @SerializedName("targetingTags")
            val targetingTags: List<Any?>?,
            @SerializedName("title")
            val title: String?, // Fresh Releases
            @SerializedName("video")
            val video: Any? // null
        ) {
            override fun toString(): String {
                return title.toString()
            }
            data class Accessibility(
                @SerializedName("accessibilitySchedule")
                val accessibilitySchedule: Any?, // null
                @SerializedName("checkAccessibility")
                val checkAccessibility: Boolean? // false
            )

            data class CustomContent(
                @SerializedName("customType")
                val customType: String?, // GENRES
                @SerializedName("dateCreated")
                val dateCreated: Long?, // 1719997067850
                @SerializedName("id")
                val id: Int?, // 166
                @SerializedName("images")
                val images: Any?, // null
                @SerializedName("lastUpdated")
                val lastUpdated: Long? // 1719997067850
            )

            data class CustomData(
                @SerializedName("genres-albums-ids")
                val genresAlbumsIds: Any, // Changed from String to List<String?>
                @SerializedName("genres-artist-ids")
                val genresArtistIds: Any, // Adjust if it's a List based on actual JSON
                @SerializedName("genres-clip-ids")
                val genresClipIds: List<GenresClipId?>?,
                @SerializedName("genres-ent-season-ids")
                val genresEntSeasonIds: List<GenresEntSeasonId?>?,
                @SerializedName("genres-ent-series-ids")
                val genresEntSeriesIds: List<GenresEntSeriesId?>?,
                @SerializedName("genres-episodes-ids")
                val genresEpisodesIds: List<Any?>?,
                @SerializedName("genres-live-event-ids")
                val genresLiveEventIds: List<Any?>?,
                @SerializedName("genres-live-linear-ids")
                val genresLiveLinearIds: List<Any?>?,
                @SerializedName("genres-movies-ids")
                val genresMoviesIds: List<GenresMoviesId?>?,
                @SerializedName("genres-short-films-ids")
                val genresShortFilmsIds: List<GenresShortFilmsId?>?,
                @SerializedName("genres-songs-ids")
                val genresSongsIds: Any, // Adjust if it's a List based on actual JSON
                @SerializedName("genres-trailer-ids")
                val genresTrailerIds: List<GenresTrailerId?>?
            )

            {
                data class GenresClipId(
                    @SerializedName("contentSlug")
                    val contentSlug: String?, // clip-1
                    @SerializedName("contentType")
                    val contentType: String?, // VIDEO
                    @SerializedName("description")
                    val description: String?,
                    @SerializedName("externalIdentifier")
                    val externalIdentifier: String?, // 7nr1a1Kd
                    @SerializedName("id")
                    val id: Int?, // 95
                    @SerializedName("images")
                    val images: List<Any?>?,
                    @SerializedName("mediaType")
                    val mediaType: String?, // CLIP
                    @SerializedName("title")
                    val title: String? // Clip 1
                )

                data class GenresEntSeasonId(
                    @SerializedName("contentSlug")
                    val contentSlug: String?, // cat-videos
                    @SerializedName("contentType")
                    val contentType: String?, // CUSTOM
                    @SerializedName("description")
                    val description: String?,
                    @SerializedName("externalIdentifier")
                    val externalIdentifier: Any?, // null
                    @SerializedName("id")
                    val id: Int?, // 26
                    @SerializedName("images")
                    val images: List<Image?>?,
                    @SerializedName("mediaType")
                    val mediaType: String?, // ENT_SEASON
                    @SerializedName("title")
                    val title: String? // Cat videos
                ) {
                    data class Image(
                        @SerializedName("colorPalette")
                        val colorPalette: List<String?>?,
                        @SerializedName("dominantColor")
                        val dominantColor: String?, // #374446
                        @SerializedName("height")
                        val height: Double?, // 200.0
                        @SerializedName("id")
                        val id: Int?, // 66
                        @SerializedName("imageKey")
                        val imageKey: String?, // 13-19-29-218_200x200_1718697575410.png
                        @SerializedName("imageType")
                        val imageType: String?, // 16x9
                        @SerializedName("isDefault")
                        val isDefault: Boolean?, // false
                        @SerializedName("originalImageSizeInBytes")
                        val originalImageSizeInBytes: Int?, // 19458
                        @SerializedName("showTitle")
                        val showTitle: Boolean?, // false
                        @SerializedName("src")
                        val src: String?, // https://d3ffi1kkogkk1a.cloudfront.net/imageStore/13-19-29-218_200x200_1718697575410.png
                        @SerializedName("status")
                        val status: String?, // PUBLISHED
                        @SerializedName("tag")
                        val tag: String?, // img0
                        @SerializedName("width")
                        val width: Double? // 200.0
                    )
                }

                data class GenresEntSeriesId(
                    @SerializedName("contentSlug")
                    val contentSlug: String?, // cat-series
                    @SerializedName("contentType")
                    val contentType: String?, // CUSTOM
                    @SerializedName("description")
                    val description: String?,
                    @SerializedName("externalIdentifier")
                    val externalIdentifier: Any?, // null
                    @SerializedName("id")
                    val id: Int?, // 28
                    @SerializedName("images")
                    val images: List<Image?>?,
                    @SerializedName("mediaType")
                    val mediaType: String?, // ENT_SERIES
                    @SerializedName("title")
                    val title: String? // Cat series
                ) {
                    data class Image(
                        @SerializedName("colorPalette")
                        val colorPalette: List<String?>?,
                        @SerializedName("dominantColor")
                        val dominantColor: String?, // #54676f
                        @SerializedName("height")
                        val height: Double?, // 200.0
                        @SerializedName("id")
                        val id: Int?, // 12
                        @SerializedName("imageKey")
                        val imageKey: String?, // 20-48-11-446_200x200_1717575731865.jpg
                        @SerializedName("imageType")
                        val imageType: String?, // 16x9
                        @SerializedName("isDefault")
                        val isDefault: Boolean?, // false
                        @SerializedName("originalImageSizeInBytes")
                        val originalImageSizeInBytes: Int?, // 12873
                        @SerializedName("showTitle")
                        val showTitle: Boolean?, // false
                        @SerializedName("src")
                        val src: String?, // https://d3ffi1kkogkk1a.cloudfront.net/imageStore/20-48-11-446_200x200_1717575731865.jpg
                        @SerializedName("status")
                        val status: String?, // PUBLISHED
                        @SerializedName("tag")
                        val tag: String?, // img0
                        @SerializedName("width")
                        val width: Double? // 200.0
                    )
                }

                data class GenresMoviesId(
                    @SerializedName("contentSlug")
                    val contentSlug: String?, // partner-movie
                    @SerializedName("contentType")
                    val contentType: String?, // VIDEO
                    @SerializedName("description")
                    val description: String?,
                    @SerializedName("externalIdentifier")
                    val externalIdentifier: String?, // Z2QYoUf1
                    @SerializedName("id")
                    val id: Int?, // 294
                    @SerializedName("images")
                    val images: List<Image?>?,
                    @SerializedName("mediaType")
                    val mediaType: String?, // MOVIES
                    @SerializedName("title")
                    val title: String? // partner movie
                ) {
                    data class Image(
                        @SerializedName("colorPalette")
                        val colorPalette: List<String?>?,
                        @SerializedName("dominantColor")
                        val dominantColor: String?, // #df9774
                        @SerializedName("height")
                        val height: Double?, // 168.0
                        @SerializedName("id")
                        val id: Int?, // 35
                        @SerializedName("imageKey")
                        val imageKey: String?, // avengers_1718188242042.jpg
                        @SerializedName("imageType")
                        val imageType: String?, // 16x9
                        @SerializedName("isDefault")
                        val isDefault: Boolean?, // false
                        @SerializedName("originalImageSizeInBytes")
                        val originalImageSizeInBytes: Int?, // 14539
                        @SerializedName("showTitle")
                        val showTitle: Boolean?, // false
                        @SerializedName("src")
                        val src: String?, // https://d3ffi1kkogkk1a.cloudfront.net/imageStore/avengers_1718188242042.jpg
                        @SerializedName("status")
                        val status: String?, // PUBLISHED
                        @SerializedName("tag")
                        val tag: String?, // img0
                        @SerializedName("width")
                        val width: Double? // 300.0
                    )
                }

                data class GenresShortFilmsId(
                    @SerializedName("contentSlug")
                    val contentSlug: String?, // video-2160pmp4-qhja
                    @SerializedName("contentType")
                    val contentType: String?, // VIDEO
                    @SerializedName("description")
                    val description: String?,
                    @SerializedName("externalIdentifier")
                    val externalIdentifier: String?, // C07ZhrxI
                    @SerializedName("id")
                    val id: Int?, // 296
                    @SerializedName("images")
                    val images: List<Any?>?,
                    @SerializedName("mediaType")
                    val mediaType: String?, // SHORT_FILMS
                    @SerializedName("title")
                    val title: String? // video (2160p).mp4
                )

                data class GenresTrailerId(
                    @SerializedName("contentSlug")
                    val contentSlug: String?, // avenger-trailer
                    @SerializedName("contentType")
                    val contentType: String?, // VIDEO
                    @SerializedName("description")
                    val description: String?,
                    @SerializedName("externalIdentifier")
                    val externalIdentifier: String?, // mfj1BSkb
                    @SerializedName("id")
                    val id: Int?, // 94
                    @SerializedName("images")
                    val images: List<Any?>?,
                    @SerializedName("mediaType")
                    val mediaType: String?, // TRAILER
                    @SerializedName("title")
                    val title: String? // Avenger Trailer
                )
            }

            data class Image(
                @SerializedName("accessibility")
                val accessibility: Accessibility?,
                @SerializedName("analyticsId")
                val analyticsId: Any?, // null
                @SerializedName("articleContent")
                val articleContent: Any?, // null
                @SerializedName("audioContent")
                val audioContent: Any?, // null
                @SerializedName("contentReviewRating")
                val contentReviewRating: Any?, // null
                @SerializedName("contentSlug")
                val contentSlug: String?, // 04-52-39-720_200x200jpg
                @SerializedName("contentSource")
                val contentSource: String?, // ENVEU_OVP
                @SerializedName("contentType")
                val contentType: String?, // IMAGE
                @SerializedName("customContent")
                val customContent: Any?, // null
                @SerializedName("customData")
                val customData: CustomData?,
                @SerializedName("dateCreated")
                val dateCreated: Long?, // 1720425325735
                @SerializedName("description")
                val description: String?,
                @SerializedName("externalRefId")
                val externalRefId: Any?, // null
                @SerializedName("id")
                val id: Int?, // 423
                @SerializedName("imageContent")
                val imageContent: ImageContent?,
                @SerializedName("images")
                val images: Any?, // null
                @SerializedName("keywords")
                val keywords: List<Any?>?,
                @SerializedName("lastUpdated")
                val lastUpdated: Long?, // 1720425325735
                @SerializedName("liveContent")
                val liveContent: Any?, // null
                @SerializedName("longDescription")
                val longDescription: Any?, // null
                @SerializedName("organizationId")
                val organizationId: Any?, // null
                @SerializedName("parentContent")
                val parentContent: Any?, // null
                @SerializedName("parentalRating")
                val parentalRating: Any?, // null
                @SerializedName("personContent")
                val personContent: Any?, // null
                @SerializedName("premium")
                val premium: Boolean?, // false
                @SerializedName("publishedDate")
                val publishedDate: Any?, // null
                @SerializedName("seoInfo")
                val seoInfo: Any?, // null
                @SerializedName("sku")
                val sku: String?, // MEDIA_2221284b-e8de-4f3b-974a-d942b40870ac
                @SerializedName("styleInfo")
                val styleInfo: Any?, // null
                @SerializedName("targetingTags")
                val targetingTags: List<Any?>?,
                @SerializedName("title")
                val title: String?, // 04-52-39-720_200x200.jpg
                @SerializedName("video")
                val video: Any? // null
            ) {
                data class Accessibility(
                    @SerializedName("accessibilitySchedule")
                    val accessibilitySchedule: Any?, // null
                    @SerializedName("checkAccessibility")
                    val checkAccessibility: Boolean? // false
                )

                class CustomData

                data class ImageContent(
                    @SerializedName("colorPalette")
                    val colorPalette: List<String?>?,
                    @SerializedName("dominantColor")
                    val dominantColor: String?, // #4f4339
                    @SerializedName("height")
                    val height: Double?, // 200.0
                    @SerializedName("id")
                    val id: Int?, // 139
                    @SerializedName("imageKey")
                    val imageKey: String?, // 04-52-39-720_200x200_1720425320368.jpg
                    @SerializedName("imageType")
                    val imageType: String?, // 1x1
                    @SerializedName("isDefault")
                    val isDefault: Boolean?, // false
                    @SerializedName("originalImageSizeInBytes")
                    val originalImageSizeInBytes: Int?, // 80499
                    @SerializedName("showTitle")
                    val showTitle: Boolean?, // false
                    @SerializedName("src")
                    val src: String?, // https://d3ffi1kkogkk1a.cloudfront.net/imageStore/04-52-39-720_200x200_1720425320368.jpg
                    @SerializedName("status")
                    val status: String?, // PUBLISHED
                    @SerializedName("tag")
                    val tag: String?, // img1
                    @SerializedName("width")
                    val width: Double? // 200.0
                )
            }

            data class SeoInfo(
                @SerializedName("description")
                val description: String?,
                @SerializedName("seoMetaInfo")
                val seoMetaInfo: SeoMetaInfo?,
                @SerializedName("tags")
                val tags: List<Any?>?,
                @SerializedName("title")
                val title: String?
            ) {
                class SeoMetaInfo
            }
        }
    }
}