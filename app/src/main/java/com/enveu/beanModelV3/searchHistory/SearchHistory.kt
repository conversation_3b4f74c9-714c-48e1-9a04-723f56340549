package com.enveu.beanModelV3.searchHistory


import com.google.gson.annotations.SerializedName

data class SearchHistory(
    @SerializedName("data")
    val `data`: Data?,
    @SerializedName("debugMessage")
    val debugMessage: String?, // string
    @SerializedName("responseCode")
    val responseCode: Int? // 0
) {
    data class Data(
        @SerializedName("items")
        val items: List<Item?>?,
        @SerializedName("pageNumber")
        val pageNumber: Int?, // 0
        @SerializedName("pageSize")
        val pageSize: Int?, // 0
        @SerializedName("totalElements")
        val totalElements: Int?, // 0
        @SerializedName("totalPages")
        val totalPages: Int? // 0
    ) {
        data class Item(
            @SerializedName("dateCreated")
            val dateCreated: String?, // 2019-08-24T14:15:22Z
            @SerializedName("keyword")
            val keyword: String?, // string
            @SerializedName("lastUpdated")
            val lastUpdated: String?, // 2019-08-24T14:15:22Z
            @SerializedName("locale")
            val locale: String? // string
        )
    }
}