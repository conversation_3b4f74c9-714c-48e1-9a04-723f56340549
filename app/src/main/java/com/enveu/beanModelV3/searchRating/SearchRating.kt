package com.enveu.beanModelV3.searchRating


import com.google.gson.annotations.SerializedName

data class SearchRating(
    @SerializedName("data")
    val `data`: Data?,
    @SerializedName("debugMessage")
    val debugMessage: Any?, // null
    @SerializedName("responseCode")
    val responseCode: Int? // 2000
) {
    data class Data(
        @SerializedName("items")
        val items: List<Item?>?,
        @SerializedName("pageNumber")
        val pageNumber: Int?, // 0
        @SerializedName("pageSize")
        val pageSize: Int?, // 20
        @SerializedName("totalElements")
        val totalElements: Int?, // 8
        @SerializedName("totalPages")
        val totalPages: Int? // 1
    ) {
        data class Item(
            @SerializedName("accessibility")
            val accessibility: Accessibility?,
            @SerializedName("analyticsId")
            val analyticsId: Any?, // null
            @SerializedName("articleContent")
            val articleContent: Any?, // null
            @SerializedName("audioContent")
            val audioContent: Any?, // null
            @SerializedName("contentReviewRating")
            val contentReviewRating: Any?, // null
            @SerializedName("contentSlug")
            val contentSlug: String?, // g
            @SerializedName("contentSource")
            val contentSource: String?, // ENVEU_OVP
            @SerializedName("contentType")
            val contentType: String?, // CUSTOM
            @SerializedName("customContent")
            val customContent: CustomContent?,
            @SerializedName("customData")
            val customData: CustomData?,
            @SerializedName("dateCreated")
            val dateCreated: Long?, // 1720161278932
            @SerializedName("description")
            val description: Any?, // null
            @SerializedName("externalRefId")
            val externalRefId: Any?, // null
            @SerializedName("id")
            val id: Int?, // 359
            @SerializedName("imageContent")
            val imageContent: Any?, // null
            @SerializedName("images")
            val images: Any?, // null
            @SerializedName("keywords")
            val keywords: List<Any?>?,
            @SerializedName("lastUpdated")
            val lastUpdated: Long?, // 1720161281051
            @SerializedName("liveContent")
            val liveContent: Any?, // null
            @SerializedName("longDescription")
            val longDescription: Any?, // null
            @SerializedName("organizationId")
            val organizationId: Any?, // null
            @SerializedName("parentContent")
            val parentContent: Any?, // null
            @SerializedName("parentalRating")
            val parentalRating: Any?, // null
            @SerializedName("personContent")
            val personContent: Any?, // null
            @SerializedName("premium")
            val premium: Boolean?, // false
            @SerializedName("publishedDate")
            val publishedDate: Any?, // null
            @SerializedName("seoInfo")
            val seoInfo: Any?, // null
            @SerializedName("sku")
            val sku: String?, // MEDIA_57a44048-3afd-418b-bf5d-7cd7031fbd18
            @SerializedName("styleInfo")
            val styleInfo: Any?, // null
            @SerializedName("targetingTags")
            val targetingTags: List<Any?>?,
            @SerializedName("title")
            val title: String?, // G
            @SerializedName("video")
            val video: Any? // null
        ) {
            data class Accessibility(
                @SerializedName("accessibilitySchedule")
                val accessibilitySchedule: Any?, // null
                @SerializedName("checkAccessibility")
                val checkAccessibility: Boolean? // false
            )

            data class CustomContent(
                @SerializedName("customType")
                val customType: String?, // RATING
                @SerializedName("dateCreated")
                val dateCreated: Long?, // 1720161278932
                @SerializedName("id")
                val id: Int?, // 185
                @SerializedName("images")
                val images: Any?, // null
                @SerializedName("lastUpdated")
                val lastUpdated: Long? // 1720161278932
            )

            data class CustomData(
                @SerializedName("score")
                val score: String? // 10
            )
        }
    }
}