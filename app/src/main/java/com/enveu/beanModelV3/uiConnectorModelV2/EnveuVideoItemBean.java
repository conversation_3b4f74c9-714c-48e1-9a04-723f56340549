package com.enveu.beanModelV3.uiConnectorModelV2;

import android.graphics.drawable.Drawable;
import android.util.Log;

import androidx.core.content.ContextCompat;

import com.enveu.OttApplication;
import com.enveu.R;
import com.enveu.beanModelV3.PlaylistItem;
import com.enveu.beanModelV3.RelatedRailsCommonData;
import com.enveu.beanModelV3.continueWatching.DataItem;
import com.enveu.beanModelV3.playListModelV2.ArticleContent;
import com.enveu.beanModelV3.playListModelV2.AudioContent;
import com.enveu.beanModelV3.playListModelV2.CustomContentData;
import com.enveu.beanModelV3.playListModelV2.PersonContent;
import com.enveu.beanModelV3.playListModelV2.VideosItem;
import com.enveu.beanModelV3.searchV2.ItemsItem;
import com.enveu.beanModelV3.videoDetailV3.ArtistAlbumIdItem;
import com.enveu.beanModelV3.videoDetailV3.ArtistSongIdItem;
import com.enveu.beanModelV3.videoDetailV3.MovieArtistsResponse;
import com.enveu.beanModelV3.videoDetailV3.SongsAlbumIdItem;
import com.enveu.beanModelV3.videoDetailV3.list.ImagesItem;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.AlbumArtistIdItem;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.SinglesArtistItem;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.SongsArtistIdsItem;
import com.enveu.beanModelV3.videoDetailsV2.EnveuVideoDetailsBean;
import com.enveu.beanModelV3.videoDetailsV2.VideoDetails;
import com.enveu.bean_model_v2_0.listAll.AudioTrackListItem;
import com.enveu.bean_model_v2_0.listAll.CustomData;
import com.enveu.bean_model_v2_0.listAll.Item;
import com.enveu.bean_model_v2_0.listAll.ParentContent;
import com.enveu.bean_model_v2_0.listAll.SeriesCustomData;
import com.enveu.bean_model_v2_0.listAll.VideoItem;
import com.enveu.bean_model_v2_0.videoDetailBean.Data;
import com.enveu.bean_model_v2_0.videoDetailBean.DetailVideo;
import com.enveu.client.epgListing.epgResponse.Program;
import com.enveu.client.playlist.beanv2_0.LiveContent;
import com.enveu.epg.models.EPGMediaContent;
import com.enveu.epg.models.EPGResponseModel;
import com.enveu.utils.Logger;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.config.ImageLayer;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class EnveuVideoItemBean implements Serializable {
    private Boolean isDrmDisabled = false;

    public Boolean getDrmDisabled() {
        return isDrmDisabled;
    }

    public void setDrmDisabled(Boolean drmDisabled) {
        isDrmDisabled = drmDisabled;
    }

    private ArrayList seasons;
    private String description;
    private String longDescription;
    private ImageContent imageContent;
    private List<String> assetKeywords;

    private int likeCount;
    private String contentSlug;
    private int playCount;
    private String title;
    private String playPage;
    private long startTime;
    private Object svod;


    @SerializedName("customData")
    private com.enveu.beanModelV3.videoDetailV3.CustomData customData;
    private com.enveu.beanModelV3.playListModelV2.CustomData customDataV3;
    private CustomData customDataForSong;

    public String getDisplay_date() {
        return display_date;
    }

    public void setDisplay_date(String display_date) {
        this.display_date = display_date;
    }

    private String parentalSeriesId;
    private String contentProvider;
    private List<String> assetCast;
    private List<Object> keywords;
    private List<Object> video;
    private Boolean premium;
    private String posterURL;


    public List<ImagesItem> getImagesItems() {
        return images;
    }

    public void setImagesItems(List<com.enveu.beanModelV3.videoDetailV3.list.ImagesItem> images) {
        this.images = images;
    }

    private List<com.enveu.beanModelV3.videoDetailV3.list.ImagesItem> images;
    private String profilePosterUrl;
    private Object price;
    private List<String> assetGenres;
    private String season;
    private String customDataLyric;
    private int id;
    private String externalRefId;
    private String sku;
    private boolean isNew;
    private Object tvod;
    private Object episodeNo;
    private String assetType;
    private int commentCount;
    private String uploadedAssetKey;
    private String brightcoveVideoId;
    private String series;
    private String seriesId;
    private String trackNumber;
    private Object plans;
    private long publishedDate;
    private String status;
    private int responseCode;
    private long duration;
    private String contentRelease;
    private String languages;
    private String certificate;
    private String name;
    private int vodCount;
    private int seasonCount;
    private String thumbnailImage;
    private long videoPosition;
    private int contentOrder;
    private String seasonNumber;
    private String imageType;
    private String parentalRating;
    private String signedLangEnabled;
    private String isPodcast;
    private String is4k;
    private String isClosedCaption;
    private String isSoundTrack;
    private String isAudioDesc;
    private String seasonName;
    private CustomContentData customContent;
    private SeriesCustomData seriesCustomData;
    private VideoDetails videoDetails;
    private com.enveu.beanModelV3.videoDetailV3.VideoDetails videoDetailsV3;
    private LiveContent liveContent;
    private com.enveu.epg.models.LiveContent epgLiveContent;

    public com.enveu.beanModelV3.videoDetailV3.VideoDetails getVideoDetailsV3() {
        return videoDetailsV3;
    }

    public void setVideoDetailsV3(com.enveu.beanModelV3.videoDetailV3.VideoDetails videoDetailsV3) {
        this.videoDetailsV3 = videoDetailsV3;
    }





    private DetailVideo detailVideo;
    private Program program;

    public com.enveu.epg.models.LiveContent getEpgLiveContent() {
        return epgLiveContent;
    }

    public void setEpgLiveContent(com.enveu.epg.models.LiveContent epgLiveContent) {
        this.epgLiveContent = epgLiveContent;
    }

    private String customType;
    private String contentType;
    private int SearchViewType;
    private PersonContent personContent;
    private AudioContent audioContent;
    private ArticleContent articleContent;

    public ArticleContent getArticleContent() {
        return articleContent;
    }

    public void setArticleContent(ArticleContent articleContent) {
        this.articleContent = articleContent;
    }

    private VideoItem videoDetailsEpisodes;
    private String epgChannelId;
    private boolean isSelected;
    private List<AudioTrackListItem> audioTrackList;

    private List<ArtistAlbumIdItem> artistAlbumId;
    private List<AlbumArtistIdItem> albumArtistId;
    private List<MovieArtistsResponse> movieArtistId;
    private List<MovieArtistsResponse> movieDirectorId;


    public List<ArtistAlbumIdItem> getArtistAlbumId(){
        return artistAlbumId;
    }

    public List<AlbumArtistIdItem> getAlbumArtistId() {
        return albumArtistId;
    }
    private  String lyrics;
    private List<ArtistSongIdItem> artistSongId;
    private SinglesArtistItem singlesArtistItem;
    private List<SongsArtistIdsItem> songsArtistIdsItems;

    public List<ArtistSongIdItem> getArtistSongId(){
        return artistSongId;
    }
    private com.enveu.bean_model_v2_0.videoDetailBean.ParentContent parentContent;
    private ParentContent parentContent2;

    private List<RelatedRailsCommonData.Data.Item.CustomData.Genre> genres;
    private List<RelatedRailsCommonData.Data.Item.CustomData.SubGenre> sub_genres;

    public List<RelatedRailsCommonData.Data.Item.CustomData.Genre> getGenres() {
        return genres;
    }

    public void setGenres(List<RelatedRailsCommonData.Data.Item.CustomData.Genre> genres) {
        this.genres = genres;
    }
    public List<RelatedRailsCommonData.Data.Item.CustomData.SubGenre> getSub_genres() {
        return sub_genres;
    }

    public String getPlayPage() {
        return playPage;
    }

    public void setPlayPage(String playPage) {
        this.playPage = playPage;
    }

    public List<AudioTrackListItem> getAudioTrackList() {
        return audioTrackList;
    }
    public void setAudioTrackList(List<AudioTrackListItem> audioTrackList) {
        this.audioTrackList = audioTrackList;
    }

    public ParentContent getParentContent2() {
        return parentContent2;
    }

    public void setParentContent2(ParentContent parentContent2) {
        this.parentContent2 = parentContent2;
    }

    public com.enveu.bean_model_v2_0.videoDetailBean.ParentContent getParentContent() {
        return parentContent;
    }

    public void setParentContent(com.enveu.bean_model_v2_0.videoDetailBean.ParentContent parentContent) {
        this.parentContent = parentContent;
    }

    public String getLyrics() {
        return lyrics;
    }

    public void setLyrics(String lyrics) {
        this.lyrics = lyrics;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public LiveContent getLiveContent() {
        return liveContent;
    }

    public void setLiveContent(LiveContent liveContent) {
        this.liveContent = liveContent;
    }

    public int getPlayCount() {
        return playCount;
    }

    public void setPlayCount(int playCount) {
        this.playCount = playCount;
    }

    public List<SongsArtistIdsItem> getSongsArtistIdsItems() {
        return songsArtistIdsItems;
    }

    public void setSongsArtistIdsItems(List<SongsArtistIdsItem> songsArtistIdsItems) {
        this.songsArtistIdsItems = songsArtistIdsItems;
    }

    public String getEpgChannelId() {
        return epgChannelId;
    }

    public void setEpgChannelId(String epgChannelId) {
        this.epgChannelId = epgChannelId;
    }

    public VideoDetails getVideoDetails() {
        return videoDetails;
    }

    public void setVideoDetails(VideoDetails videoDetails) {
        this.videoDetails = videoDetails;
    }

    public List<Object> getVideo() {
        return video;
    }

    public void setVideo(List<Object> video) {
        this.video = video;
    }

    public String getIs4k() {
        return is4k;
    }


    public void setIs4k(String is4k) {
        this.is4k = is4k;
    }

    public String getIsClosedCaption() {
        return isClosedCaption;
    }

    public void setIsClosedCaption(String isClosedCaption) {
        this.isClosedCaption = isClosedCaption;
    }

    public List<Object> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<Object> keywords) {
        this.keywords = keywords;
    }

    public String getIsSoundTrack() {
        return isSoundTrack;
    }

    public void setIsSoundTrack(String isSoundTrack) {
        this.isSoundTrack = isSoundTrack;
    }

    public String getTrackNumber() {
        return trackNumber;
    }

    public void setTrackNumber(String trackNumber) {
        this.trackNumber = trackNumber;
    }

    public String getIsAudioDesc() {
        return isAudioDesc;
    }

    public void setIsAudioDesc(String isAudioDesc) {
        this.isAudioDesc = isAudioDesc;
    }

    public String getSeasonName() {
        return seasonName;
    }

    public void setSeasonName(String seasonName) {
        this.seasonName = seasonName;
    }

    public String getIscomingsoon() {
        return iscomingsoon;
    }

    public void setIscomingsoon(String iscomingsoon) {
        this.iscomingsoon = iscomingsoon;
    }

    public String getIsPodcast() {
        return isPodcast;
    }

    public void setIsPodcast(String isPodcast) {
        this.isPodcast = isPodcast;
    }

    public String getSignedLangEnabled() {
        return signedLangEnabled;
    }

    public void setSignedLangEnabled(String signedLangEnabled) {
        this.signedLangEnabled = signedLangEnabled;
    }


    public String getSignedLangParentRefId() {
        return signedLangParentRefId;
    }

    public void setSignedLangParentRefId(String signedLangParentRefId) {
        this.signedLangParentRefId = signedLangParentRefId;
    }

    private String signedLangParentRefId;
    private String signedLangRefId;

    public String getSignedLangRefId() {
        return signedLangRefId;
    }

    public void setSignedLangRefId(String signedLangRefId) {
        this.signedLangRefId = signedLangRefId;
    }

    public String getParentalSeriesId() {
        return parentalSeriesId;
    }

    public void setParentalSeriesId(String parentalSeriesId) {
        this.parentalSeriesId = parentalSeriesId;
    }

    public String getExternalRefId() {
        return externalRefId;
    }

    public void setExternalRefId(String externalRefId) {
        this.externalRefId = externalRefId;
    }

    private String iscomingsoon;
    private String widevineLicence;
    private String producer;
    private String quality;
    private String skipintro_endTime;
    private String skipintro_startTime;
    private String sponsors;
    private String display_tags;
    private String description_two;
    private String description_three;
    private String display_date;
    private String getWidevineURL;
    private String country;
    private String company;
    private String year;
    private String releaseYear;
    private String isNewS;
    private String isVIP;
    private String vastTag;
    private String trailerReferenceId;
    private String islivedrm = "false";
    private boolean isCurrentlyPlaying;

    public String getContentSlug() {
        return contentSlug;
    }

    public void setSeasons(ArrayList seasons) {
        this.seasons = seasons;
    }

    public void setImageContent(ImageContent imageContent) {
        this.imageContent = imageContent;
    }

    public void setContentSlug(String contentSlug) {
        this.contentSlug = contentSlug;
    }

    public Boolean getPremium() {
        return premium;
    }

    public void setPremium(Boolean premium) {
        this.premium = premium;
    }

    public List<ImagesItem> getImages() {
        return images;
    }

    public void setImages(List<ImagesItem> images) {
        this.images = images;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public DetailVideo getDetailVideo() {
        return detailVideo;
    }

    public void setDetailVideo(DetailVideo detailVideo) {
        this.detailVideo = detailVideo;
    }

    public PersonContent getPersonContent() {
        return personContent;
    }

    public void setPersonContent(PersonContent personContent) {
        this.personContent = personContent;
    }

    public AudioContent getAudioContent() {
        return audioContent;
    }

    public void setAudioContent(AudioContent audioContent) {
        this.audioContent = audioContent;
    }

    public VideoItem getVideoDetailsEpisodes() {
        return videoDetailsEpisodes;
    }

    public void setVideoDetailsEpisodes(VideoItem videoDetailsEpisodes) {
        this.videoDetailsEpisodes = videoDetailsEpisodes;
    }

    public void setArtistAlbumId(List<ArtistAlbumIdItem> artistAlbumId) {
        this.artistAlbumId = artistAlbumId;
    }

    public void setAlbumArtistId(List<AlbumArtistIdItem> albumArtistId) {
        this.albumArtistId = albumArtistId;
    }

    public void setArtistSongId(List<ArtistSongIdItem> artistSongId) {
        this.artistSongId = artistSongId;
    }

    public void setSkipintro_endTime(String skipintro_endTime) {
        this.skipintro_endTime = skipintro_endTime;
    }

    public void setSkipintro_startTime(String skipintro_startTime) {
        this.skipintro_startTime = skipintro_startTime;
    }

    public boolean isCurrentlyPlaying() {
        return isCurrentlyPlaying;
    }

    public void setCurrentlyPlaying(boolean currentlyPlaying) {
        isCurrentlyPlaying = currentlyPlaying;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getIslivedrm() {
        return islivedrm;
    }

    public void setIslivedrm(String islivedrm) {
        this.islivedrm = islivedrm;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public EnveuVideoItemBean() {
    }

    public EnveuVideoItemBean(PlaylistItem details){
        try {
            this.title = details.getDisplayTitle() == null ? "" : details.getDisplayTitle();
            this.description = details.getDisplayDescription() == null ? "" : details.getDisplayDescription();
            this.id = details.getId();
            if(details.getImages().size()>0){
                this.posterURL = details.getImages().get(0).getImageContent().getSrc() == null ? "" : details.getImages().get(0).getImageContent().getSrc();
            }
            this.images = details.getImages();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    // Parsing EPG Details
    // Parsing EPG Details
    public EnveuVideoItemBean(com.enveu.client.epgListing.epgResponseNew.MediaContent itemsItem) {
        try {
            this.title = itemsItem.getTitle() == null ? "" : itemsItem.getTitle();
            this.description = itemsItem.getDescription() == null ? "" : itemsItem.getDescription();
            this.posterURL = ImageLayer.getInstance().getLivePosterImageUrl(itemsItem);
        } catch (Exception e) {
            Logger.w(e);
        }


    }


    public Program getProgram() {
        return program;
    }

    public void setProgram(Program program) {
        this.program = program;
    }

    //for asset details on episode page listing.......
    public EnveuVideoItemBean(Data details) {
        try {
            //Gson gson = new Gson();
            this.id = details.getId();
            this.title = details.getTitle() == null ? "" : details.getTitle();
            this.description = details.getDescription() == null ? "" : details.getDescription().trim();
            this.longDescription = details.getLongDescription() == null ? "" : details.getLongDescription().trim();
            this.keywords = details.getKeywords() == null ? new ArrayList<>() : details.getKeywords();
            this.contentSlug=details.getContentSlug()==null?"":details.getContentSlug();
            this.premium = details.getPremium();
            this.assetType = details.getContentType() == null ? "" : details.getContentType();

            if (details.getContentType().equalsIgnoreCase(AppConstants.VIDEO)) {
                if (details.getVideo() != null) {
                    this.videoDetails = details.getVideo();
                }
            } else {
                if (details.getContentType().equalsIgnoreCase(AppConstants.CUSTOM)) {
                    if (details.getCustomContent() != null) {
                        this.customType = details.getCustomContent().getCustomType();
                    }
                }
            }

            if (details.getCustomData()!=null) {
                this.seriesCustomData = details.getCustomData();
            }

            this.posterURL = ImageLayer.getInstance().getEpisodePosterImageUrl(details, AppConstants.LDS);

            if (details.getExternalRefId() != null && !details.getExternalRefId().equalsIgnoreCase("")) {
                this.externalRefId = details.getExternalRefId();
                this.setExternalRefId(externalRefId);
            }


            if (details.getContentType().equalsIgnoreCase(AppConstants.VIDEO)) {
                if (details.getVideo() != null) {
                    this.videoDetails = details.getVideo();
                }
            }

            this.season = "";
            this.sku = details.getSku() == null ? "" : details.getSku();
            this.assetType = details.getVideo().getVideoType() == null ? "" : details.getVideo().getVideoType();

            this.duration = (long) details.getVideo().getDuration();
            this.contentRelease= details.getVideo().getContentRelease() == null ? "" :details.getVideo().getContentRelease();
            this.languages= details.getVideo().getLanguages() == null ? "" :details.getVideo().getLanguages();
            this.certificate= details.getVideo().getCertificates() == null ? "" :details.getVideo().getCertificates();
        } catch (Exception e) {
            Log.w("EpisodesError->", e.toString());
        }
    }

    public EnveuVideoItemBean(Item details, String imageType, String imageIdentifier) {
        try {

            this.keywords = details.getKeywords() == null ? new ArrayList<>() : details.getKeywords();
            this.title = details.getTitle() == null ? "" : details.getTitle();
            this.externalRefId = details.getExternalRefId() == null ? "" : details.getExternalRefId().trim();
            this.assetType = details.getContentType() == null ? "" : details.getContentType();
            this.premium = details.getPremium();
            if (details.getContentType().equalsIgnoreCase(AppConstants.VIDEO)) {
                if (details.getVideo() != null) {
                    this.videoDetails = details.getVideo();
                    this.isDrmDisabled = details.getVideo().getDrmDisabled();
                    this.episodeNo = details.getVideo().getEpisodeNo() == null ? "" : details.getVideo().getEpisodeNo();
                }
            } else if (details.getContentType().equalsIgnoreCase(AppConstants.CUSTOM)) {
                if (details.getCustomContent() != null) {
                    this.customType = details.getCustomContent().getCustomType();
                }
            } else if (details.getContentType().equalsIgnoreCase(AppConstants.LIVE)) {
                if (details.getLiveContent() != null) {
                    this.liveContent = details.getLiveContent();
                    this.setLiveContent(liveContent);
                }
            }
            if (details.getCustomData()!=null) {
                this.seriesCustomData = details.getCustomData();
            }
            /*if (details.getCustomData()!=null&&details.getCustomData().getAlbumArtistId()!=null){
                this.albumArtistId=details.getCustomData().getAlbumArtistId();
            }
            if (details.getCustomData()!=null&&details.getCustomData().getArtistAlbumId()!=null){
                this.artistAlbumId=details.getCustomData().getArtistAlbumId();
            }
            if (details.getCustomData()!=null&&details.getCustomData().getArtistSongId()!=null){
                this.artistSongId=details.getCustomData().getArtistSongId();
            }*/

            if (details.getCustomData()!=null&&details.getCustomData().getSongs_artist_ids()!=null){
                this.songsArtistIdsItems = details.getCustomData().getSongs_artist_ids();
            }
            this.description = details.getDescription() == null ? "" : details.getDescription().trim();
            this.profilePosterUrl = ImageLayer.getInstance().getProfilePosterUrl(details);
            this.posterURL = ImageLayer.getInstance().getEpisodePosterImageUrl(details, AppConstants.LDS);
            this.id = details.getId();
            this.sku = details.getSku() == null ? "" : details.getSku();
            this.duration = (long) details.getVideo().getDuration();
            this.contentRelease= details.getVideo().getContentRelease() == null ? "" :details.getVideo().getContentRelease();
            this.languages= details.getVideo().getLanguages() == null ? "" :details.getVideo().getLanguages();
            this.certificate= details.getVideo().getCertificates() == null ? "" :details.getVideo().getCertificates();
        } catch (Exception e) {
            Log.w("EpisodesError->", e.toString());
        }
    }

    public SeriesCustomData getSeriesCustomData() {
        return seriesCustomData;
    }

    public void setSeriesCustomData(SeriesCustomData seriesCustomData) {
        this.seriesCustomData = seriesCustomData;
    }

    //description page - single asset parsing
    public EnveuVideoItemBean(EnveuVideoDetailsBean details ,boolean isMusicEnabled) {
        try {
            this.title = details.getData().getTitle() == null ? "" : details.getData().getTitle();
            this.assetType= details.getData().getContentType() == null ? "" : details.getData().getContentType();
            this.description = details.getData().getDescription() == null ? "" : details.getData().getDescription().trim();
            this.longDescription = details.getData().getLongDescription() == null ? "" : details.getData().getLongDescription().trim();
            this.externalRefId = details.getData().getExternalRefId() == null ? "" : details.getData().getExternalRefId().trim();
            this.assetGenres = details.getData().getGenres() == null ? new ArrayList<>() : details.getData().getGenres();
            this.assetCast = details.getData().getCast() == null ? new ArrayList<>() : details.getData().getCast();
            this.contentProvider = details.getData().getContentProvider() == null ? "" : details.getData().getContentProvider();
            this.contentSlug = details.getData().getContentSlug()== null ? "" : details.getData().getContentSlug();
            this.playCount = details.getData().getPlayCount()== 0 ? 0 : details.getData().getPlayCount();
            this.premium = details.getData().getPremium();
            this.keywords = details.getData().getKeywords() == null ? new ArrayList<>() : details.getData().getKeywords();

            if (details.getData().getContentType().equalsIgnoreCase(AppConstants.VIDEO)) {
                if (details.getData().getVideo() != null) {
                    this.videoDetails = details.getData().getVideo();
                    this.isDrmDisabled = details.getData().getVideo().getDrmDisabled();
                    this.posterURL = ImageLayer.getInstance().getPosterImageUrl(details.getData(),AppConstants.LDS);

                }
            } else if (details.getData().getContentType().equalsIgnoreCase(AppConstants.LIVE)) {
                if (details.getData().getLiveContent() != null) {
                    this.liveContent = details.getData().getLiveContent();
                    this.setLiveContent(liveContent);
                    this.posterURL = ImageLayer.getInstance().getLiveImageUrl(details.getData());
                }
            } else if (details.getData().getContentType().equalsIgnoreCase(AppConstants.AUDIO)) {
                if (details.getData().getAudioContent() != null) {
                    this.contentType = details.getData().getAudioContent().getAudioType();
                    this.audioContent = details.getData().getAudioContent();
                    if (isMusicEnabled) {
                        this.posterURL = ImageLayer.getInstance().getPosterImageUrl(details.getData(),AppConstants.SQR);
                    } else  {
                        this.posterURL = ImageLayer.getInstance().getPosterImageUrl(details.getData(),AppConstants.LDS);
                    }
                    if (details.getData()!=null&&details.getData().getCustomData()!=null&&details.getData().getCustomData().getSingles_artist_id()!=null){
                        this.singlesArtistItem = details.getData().getCustomData().getSingles_artist_id();
                    }
                }
            }
            else  if (details.getData().getContentType().equalsIgnoreCase(AppConstants.PERSON)) {
                if (details.getData().getPersonContent() != null) {
                    this.contentType = details.getData().getContentType();
                    this.personContent=details.getData().getPersonContent();
                    this.posterURL = ImageLayer.getInstance().getPosterImageUrl(details.getData(),AppConstants.IMG_0);

                }
            }
            else  {
                if (details.getData().getContentType().equalsIgnoreCase(AppConstants.CUSTOM)) {
                    if (details.getData().getCustomContent() != null) {
                        this.customType = details.getData().getCustomContent().getCustomType();
                        if (isMusicEnabled) {
                            this.posterURL = ImageLayer.getInstance().getPosterImageUrl(details.getData(),AppConstants.SQR);
                        } else {
                            this.posterURL = ImageLayer.getInstance().getPosterImageUrl(details.getData(),AppConstants.LDS);
                        }
                    }
                }
            }
//            if(details.getData().getImages()!=null && AppCommonMethod.getImageContentForRailItem(details.getData().getImages())!=null) {
//                this.imageContent = AppCommonMethod.getImageContentForRailItem(details.getData().getImages());
//            }
            this.imageContent = AppCommonMethod.getImageContent(details.getData());
            if (details.getData().getImages() != null) {
                this.images = details.getData().getImages();
            }
            this.season = "";
            if (details.getData().getLinkedContent() != null) {
                this.seriesId = String.valueOf(details.getData().getLinkedContent().getId());
            }
            this.sku = details.getData().getSku() == null ? "" : details.getData().getSku();
            this.id = details.getData().getId();
            this.isNew = false;
            this.episodeNo = details.getData().getEpisodeNumber() == null ? "" : details.getData().getEpisodeNumber();
            this.series = String.valueOf(details.getData().getId());
            this.status = details.getData().getStatus() == null ? "" : details.getData().getStatus();
            this.longDescription = details.getData().getLongDescription() == null ? "" : details.getData().getLongDescription().toString().trim();

            if (details.getData() !=null && details.getData().getVideo() !=null) {
                this.contentRelease = details.getData().getVideo().getContentRelease() == null ? "" : details.getData().getVideo().getContentRelease();
                this.languages = details.getData().getVideo().getLanguages() == null ? "" : details.getData().getVideo().getLanguages();
                this.certificate = details.getData().getVideo().getCertificates() == null ? "" : details.getData().getVideo().getCertificates();
                this.duration = details.getData().getVideo().getDuration() == null ? 0L : details.getData().getDuration();
            } else if (details.getData().getCustomData() !=null) {
                this.contentRelease= String.valueOf(details.getData().getCustomData().getRelease_year1());
            }
        } catch (Exception e) {
            Logger.e("parsing error", Objects.requireNonNull(e.getMessage()));
            Logger.w(e);
        }

    }



    public EnveuVideoItemBean(com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetailsBean details) {
        try {
            this.title = details.getData().getTitle() == null ? "" : details.getData().getTitle();
            this.id = details.getData().getId();
            this.assetType= details.getData().getContentType() == null ? "" : details.getData().getContentType();
            this.description = details.getData().getDescription() == null ? "" : details.getData().getDescription().trim();
            this.longDescription = details.getData().getLongDescription() == null ? "" : details.getData().getLongDescription().trim();
            this.externalRefId = details.getData().getExternalRefId() == null ? "" : details.getData().getExternalRefId().trim();
            this.contentSlug = details.getData().getContentSlug()== null ? "" : details.getData().getContentSlug();
            this.assetGenres = details.getData().getGenres() == null ? new ArrayList<>() : details.getData().getGenres();
            this.assetCast = details.getData().getCast() == null ? new ArrayList<>() : details.getData().getCast();
            this.contentProvider = details.getData().getContentProvider() == null ? "" : details.getData().getContentProvider();
            this.premium = details.getData().getPremium();
            this.playCount = details.getData().getPlayCount()== 0 ? 0 : details.getData().getPlayCount();
            this.keywords = details.getData().getKeywords() == null ? new ArrayList<>() : details.getData().getKeywords();
            this.releaseYear=String.valueOf(details.getData().getCustomData().getRelease_year1());
            this.epgLiveContent = details.getData().getLiveContent();

            if (details.getData().getCustomData().getGenres() != null){
                this.setGenres(details.getData().getCustomData().getGenres());
            }

            if (details.getData()!=null && details.getData().getCustomData()!=null && details.getData().getCustomData().getPlayPage()!=null){
                this.playPage = details.getData().getCustomData().getPlayPage();
            }

            if (details.getData() != null && details.getData().getVideo() != null && details.getData().getVideo().getVastTag() != null){
                vastTag = details.getData().getVideo().getVastTag();
            }

            if (details.getData().getCustomData().getLyrics() != null){
                this.lyrics = details.getData().getCustomData().getLyrics() == null ? "" : details.getData().getCustomData().getLyrics();
            }
            if (details.getData().getContentType().equalsIgnoreCase(AppConstants.VIDEO) || details.getData().getContentType().equalsIgnoreCase(AppConstants.CUSTOM)) {
                if (details.getData().getVideo() != null) {
                    this.videoDetailsV3 = details.getData().getVideo();
                }
                this.posterURL = ImageLayer.getInstance().getPosterImageUrl(details, AppConstants.LDS);

            } else  if (details.getData().getContentType().equalsIgnoreCase(AppConstants.PERSON)) {
                if (details.getData().getPersonContent() != null) {
                    this.contentType = details.getData().getPersonContent().getPersonType();
                    this.personContent=details.getData().getPersonContent();
                }
            } else  if (details.getData().getContentType().equalsIgnoreCase(AppConstants.AUDIO)) {
                if (details.getData().getAudioContent() != null) {
                    this.contentType = details.getData().getAudioContent().getAudioType();
                    this.audioContent=details.getData().getAudioContent();
                    this.posterURL = ImageLayer.getInstance().getSongsPosterImageUrl(details, AppConstants.SQR);

                }
                if (details.getData()!=null&&details.getData().getCustomData()!=null&&details.getData().getCustomData().getSingles_artist_id()!=null){
                    this.singlesArtistItem = details.getData().getCustomData().getSingles_artist_id();
                }
            }  else  {
                if (details.getData().getContentType().equalsIgnoreCase(AppConstants.CUSTOM)) {
                    if (details.getData().getCustomContent() != null) {
                        this.customType = details.getData().getCustomContent().getCustomType();
                    }
                }
            }

            if (details.getData()!=null&&details.getData().getCustomData()!=null){
                this.customData = details.getData().getCustomData();
            }

            if (details.getData()!=null&&details.getData().getParentalRating()!=null){
                this.parentalRating = details.getData().getParentalRating();
            }

            if (details.getData()!=null&&details.getData().getCustomData()!=null&&details.getData().getCustomData().getAlbumArtistId()!=null){
                this.albumArtistId=details.getData().getCustomData().getAlbumArtistId();
            }
            if (details.getData()!=null&&details.getData().getCustomData()!=null&&details.getData().getCustomData().getMovieArtistsIds()!=null){
                this.movieArtistId=details.getData().getCustomData().getMovieArtistsIds();
            }
            if (details.getData()!=null&&details.getData().getCustomData()!=null&&details.getData().getCustomData().getMovieDirectorId()!=null){
                this.movieDirectorId=details.getData().getCustomData().getMovieDirectorId();
            }
            if (details.getData()!=null&&details.getData().getCustomData()!=null&&details.getData().getCustomData().getArtistAlbumId()!=null){
                this.artistAlbumId=details.getData().getCustomData().getArtistAlbumId();
            }
            if (details.getData()!=null&&details.getData().getCustomData()!=null&&details.getData().getCustomData().getArtistSongId()!=null){
                this.artistSongId=details.getData().getCustomData().getArtistSongId();
            }

            if (details.getData()!=null&&details.getData().getCustomData()!=null&&details.getData().getCustomData().getSongs_artist_ids()!=null){
                this.songsArtistIdsItems = details.getData().getCustomData().getSongs_artist_ids();
            }
//            this.imageContent = ImageLayer.getInstance().getPosterImageUrl(details.getData(),AppConstants.SQUARE_IMAGE_TYPE);
            this.imageContent = AppCommonMethod.getImageContent(details.getData());

            this.season = "";
            if (details.getData().getLinkedContent() != null) {
                this.seriesId = String.valueOf(details.getData().getLinkedContent().getId());
            }
            this.sku = details.getData().getSku() == null ? "" : details.getData().getSku();
            this.episodeNo = details.getData().getEpisodeNumber() == null ? "" : details.getData().getEpisodeNumber();
            this.brightcoveVideoId = details.getData().getBrightcoveContentId() == null ? "" : details.getData().getBrightcoveContentId();
            this.series = String.valueOf(details.getData().getId());
            this.status = details.getData().getStatus() == null ? "" : details.getData().getStatus();

            this.sub_genres=details.getData().getCustomData().getSub_genres();
            this.year=String.valueOf(details.getData().getCustomData().getRelease_year());
            this.releaseYear=String.valueOf(details.getData().getCustomData().getRelease_year1());
            this.longDescription = details.getData().getLongDescription() == null ? "" : details.getData().getLongDescription().toString().trim();

            //series realated data
            this.vodCount = 0;
            this.seasonNumber = details.getData().getSeasonNumber() == null ? "" : details.getData().getSeasonNumber().toString().replaceAll("\\.0*$", "");
            if (details.getData().getVideo() != null) {
                this.duration = details.getData().getVideo().getDuration();
                this.contentRelease = details.getData().getVideo().getContentRelease() == null ? "" : details.getData().getVideo().getContentRelease();
                this.languages = details.getData().getVideo().getLanguages() == null ? "" : details.getData().getVideo().getLanguages();
                this.certificate = details.getData().getVideo().getCertificates() == null ? "" : details.getData().getVideo().getCertificates();
            }
        } catch (Exception e) {
            Logger.e("parsing error", Objects.requireNonNull(e.getMessage()));
            Logger.w(e);
        }

    }

    //for asset details.......
//    public EnveuVideoItemBean(VideosItem details, int contentOrder, String imageType, String imageIdentifier, boolean type, boolean isIntentRelatedContent) {
//        try {
//            this.title = details.getTitle() == null ? "" : details.getTitle();
//            this.contentOrder = contentOrder;
//            this.status = details.getStatus() == null ? "" : details.getStatus();
//            this.contentSlug = details.getContentSlug() == null ? "" : details.getContentSlug();
//            this.id = details.getId();
//            this.isSelected = false;
//            if (details.getContentType().equalsIgnoreCase(AppConstants.VIDEO)) {
//                if (details.getVideo() != null) {
//                    this.videoDetails = details.getVideo();
//                }
//            }else  if (details.getContentType().equalsIgnoreCase(AppConstants.PERSON)) {
//                if (details.getPersonContent() != null) {
//                    this.contentType = details.getPersonContent().getPersonType();
//                }
//            } else  if (details.getContentType().equalsIgnoreCase(AppConstants.AUDIO)) {
//                if (details.getAudioContent() != null) {
//                    this.contentType = details.getAudioContent().getAudioType();
//                }
//            }  else {
//                if (details.getContentType().equalsIgnoreCase(AppConstants.CUSTOM)) {
//                    if (details.getCustomContent() != null) {
//                        this.customType = details.getCustomContent().getCustomType();
//                    }
//                }
//            }
//            if (details.getCustomData()!=null&&details.getCustomData().getArtistSongId()!=null){
//                this.artistSongId=details.getCustomData().getArtistSongId();
//            }
//            if (details.getCustomData()!=null&&details.getCustomData().getArtistAlbumId()!=null){
//                this.artistAlbumId=details.getCustomData().getArtistAlbumId();
//            }
//
//            if (details.getExternalRefId() != null && !details.getExternalRefId().equalsIgnoreCase("")) {
//                this.externalRefId = details.getExternalRefId();
//                this.setExternalRefId(externalRefId);
//            }
//
//            if (details.getCustomData()!=null&&details.getCustomData().getTrailerLinkedWithId()!=null){
//                this.trailerReferenceId=details.getCustomData().getTrailerLinkedWithId();
//            }
//
//            if (details.getContentType().equalsIgnoreCase(AppConstants.LIVE)) {
//                if (details.getLiveContent() != null) {
//                    this.liveContent = details.getLiveContent();
//                    this.setLiveContent(liveContent);
//                    //  this.epgChannelId = details.getLiveContent().getEpgChannelId();
//                }
//
//                this.posterURL = ImageLayer.getInstance().getLiveImageUrl(details);
//
//            }  else if (details.getContentType().equalsIgnoreCase(AppConstants.AUDIO)) {
//                if (Objects.requireNonNull(details.getAudioContent().getAudioType()).equalsIgnoreCase(AppConstants.SONGS)) {
//                    Log.d("imageIdentifier", "EnveuVideoItemBean: imageIdentifier " + imageIdentifier);
//                    if (imageIdentifier.equalsIgnoreCase("")) {
//                        this.posterURL = ImageLayer.getInstance().getSongsPosterImageUrl(details, AppConstants.IMG_0);
//                    } else  {
//                        this.posterURL = ImageLayer.getInstance().getSongsPosterImageUrl(details, imageIdentifier);
//                    }
//                }
//            } else {
//                if (isIntentRelatedContent) {
//                    this.posterURL = ImageLayer.getInstance().getRelatedContentImageUrl(details, AppConstants.IMG_0);
//                } else {
//                    if (type == true) {
//                        this.posterURL = ImageLayer.getInstance().getPopularSearchImageUrl(details);
//                    } else {
//                        this.posterURL = ImageLayer.getInstance().getPosterImageUrl(details, imageIdentifier);
//                        this.status = details.getStatus();
//                    }
//                }
//
//                Log.w("getPlaylistVideo->", this.posterURL);
//            }
//
//            //Gson gson = new Gson();
//            this.images = details.getImages();
//            if(details.getImages()!=null && AppCommonMethod.getImageContentForRailItem(details.getImages())!=null) {
//                this.imageContent = AppCommonMethod.getImageContentForRailItem(details.getImages());
//            }
//            this.assetType = details.getContentType() == null ? "" : details.getContentType();
//            this.parentContent = details.getParentContent();
//            this.profilePosterUrl = ImageLayer.getInstance().getProfilePosterUrl(details);
//            this.description = details.getDescription() == null ? "" : details.getDescription().trim();
//            this.contentProvider = details.getContentProvider() == null ? "" : details.getContentProvider();
//            this.premium = details.getPremium();
//            this.longDescription = details.getLongDescription() == null ? "" : details.getLongDescription().toString().trim();
//            this.sku = details.getSku() == null ? "" : details.getSku();
//            this.isNew = false;
//            this.episodeNo = details.getEpisodeNumber() == null ? "" : details.getEpisodeNumber();
//            this.brightcoveVideoId = details.getBrightcoveContentId() == null ? "" : details.getBrightcoveContentId();
//            this.series = "";
//        } catch (Exception e) {
//            Logger.w(e);
//        }
//    }
    public EnveuVideoItemBean(VideosItem details, int contentOrder, String imageType, String imageIdentifier, boolean type,boolean isMusicApp, boolean isIntentRelatedContent) {
        try {
            Log.d("imageType", "EnveuVideoItemBean: "+ imageType);
            Log.d("callThisMethodApi", "HomeRails: "+ imageType);
            this.title = Optional.ofNullable(details.getTitle()).orElse("");
            this.contentOrder = contentOrder;
            this.assetType = Optional.ofNullable(details.getContentType()).orElse("");
            this.status = Optional.ofNullable(details.getStatus()).orElse("");
            this.contentSlug = Optional.ofNullable(details.getContentSlug()).orElse("");
            this.playCount = Optional.of(details.getPlayCount()).orElse(0);
            this.id = details.getId();
            this.isSelected = false;

            switch (details.getContentType()) {
                case AppConstants.VIDEO:
                    this.videoDetails = details.getVideo();
                    break;
                case AppConstants.PERSON:
                    this.contentType = Optional.ofNullable(details.getPersonContent())
                            .map(PersonContent::getPersonType)
                            .orElse(null);
                    break;
                case AppConstants.AUDIO:
                    this.contentType = Optional.ofNullable(details.getAudioContent())
                            .map(AudioContent::getAudioType)
                            .orElse(null);
                    break;
                case AppConstants.CUSTOM:
                    this.customType = Optional.ofNullable(details.getCustomContent())
                            .map(CustomContentData::getCustomType)
                            .orElse(null);
                    break;

                case AppConstants.ARTICLE:
                    this.articleContent = details.getArticleContent();
                    break;
            }

            if (details.getCustomData() != null) {
                this.customDataV3 = details.getCustomData();
                this.artistSongId = details.getCustomData().getArtistSongId();
                this.artistAlbumId = details.getCustomData().getArtistAlbumId();
                if (details.getCustomData().getTrailerLinkedWithId()!= null && details.getCustomData().getTrailerLinkedWithId().size() > 0) {
                    this.trailerReferenceId = String.valueOf(details.getCustomData().getTrailerLinkedWithId().get(0).getId());
                }
                if (details.getCustomData().getTrackNumber() !=null) {
                    this.trackNumber = details.getCustomData().getTrackNumber();
                }
            }

            if (details.getCustomData() != null && details.getCustomData().getReelCreatorId() != null){
                String json = new Gson().toJson(details.getCustomData());
                this.customData = new Gson().fromJson(json, com.enveu.beanModelV3.videoDetailV3.CustomData .class);
            }

            if (details.getPersonContent() != null){
                personContent = details.getPersonContent();
            }

            this.externalRefId = Optional.ofNullable(details.getExternalRefId()).orElse("");
            if (!this.externalRefId.isEmpty()) {
                this.setExternalRefId(externalRefId);
            }

            ImageLayer imageLayer = ImageLayer.getInstance();
            if (AppConstants.LIVE.equals(details.getContentType())) {
                this.liveContent = details.getLiveContent();
                if (this.liveContent != null) {
                    this.setLiveContent(this.liveContent);
                }
                this.posterURL = imageLayer.getLiveImageUrl(details);
            } else if (AppConstants.AUDIO.equals(details.getContentType())) {
                this.posterURL = imageLayer.getSongsPosterImageUrl(details,imageType);
            }  else if(AppConstants.VIDEO.equals(details.getContentType()) && isIntentRelatedContent) {
                this.posterURL = imageLayer.getRelatedContentImageUrl(details, false);
            }else if(AppConstants.CUSTOM.equals(details.getContentType()) && isIntentRelatedContent) {
                this.posterURL = imageLayer.getRelatedContentImageUrl(details, false);
            } else {
                this.posterURL = isIntentRelatedContent ? imageLayer.getRelatedContentImageUrl(details, isMusicApp)
                        : (type ? imageLayer.getPopularSearchImageUrl(details,isMusicApp)
                        : imageLayer.getPosterImageUrl(details, imageType));
            }

            this.images = details.getImages();
            if (this.images != null) {
                this.imageContent = AppCommonMethod.getImageContentForRailItem(this.images);
            }
            this.imageContent = AppCommonMethod.getImageContent(details);
            this.parentContent = details.getParentContent();
            this.profilePosterUrl = imageLayer.getProfilePosterUrl(details);
            this.description = Optional.ofNullable(details.getDescription()).orElse("").trim();
            this.contentProvider = Optional.ofNullable(details.getContentProvider()).orElse("");
            this.premium = details.getPremium();
            this.longDescription = Optional.ofNullable(details.getLongDescription()).orElse("").toString().trim();
            this.sku = Optional.ofNullable(details.getSku()).orElse("");
            this.isNew = false;
            this.episodeNo = Optional.ofNullable(details.getEpisodeNumber()).orElse("");
            this.brightcoveVideoId = Optional.ofNullable(details.getBrightcoveContentId()).orElse("");
            this.series = "";
        } catch (Exception e) {
            Logger.w(e);
        }
    }

    //for continue watching.......
    public EnveuVideoItemBean(DataItem details) {

        try {

            this.title = details.getTitle() == null ? "" : details.getTitle();
            this.description = details.getDescription() == null ? "" : details.getDescription().trim();
            this.assetGenres = details.getGenres() == null ? new ArrayList<>() : details.getGenres();
            this.contentSlug = details.getContentSlug() == null ? "" : details.getContentSlug();
            this.assetCast = details.getCast() == null ? new ArrayList<>() : details.getCast();
            this.assetType = details.getContentType() == null ? "" : details.getContentType();
            this.contentProvider = details.getContentProvider() == null ? "" : details.getContentProvider();
            this.assetCast = details.getCast() == null ? new ArrayList<>() : details.getCast();
            this.premium = details.getPremium();

            if (details.getExternalRefId() != null && !details.getExternalRefId().equalsIgnoreCase("")) {
                this.externalRefId = details.getExternalRefId();
                this.setExternalRefId(externalRefId);
            }

            if(details.getContentType().equalsIgnoreCase(AppConstants.AUDIO)) {
                if (Objects.requireNonNull(details.getAudioContent().getAudioType()).equalsIgnoreCase(AppConstants.SONGS)) {
                    this.posterURL = ImageLayer.getInstance().getSongsPosterImageUrl(details,AppConstants.SQR);
                } else {
                    this.posterURL = ImageLayer.getInstance().getPosterImageUrl(details);
                }
            } else {
                this.posterURL = ImageLayer.getInstance().getPosterImageUrl(details);
            }

            if (details.getContentType().equalsIgnoreCase(AppConstants.VIDEO)) {
                if (details.getVideo() != null) {
                    this.videoDetails = details.getVideo();
                    Gson gson = new Gson();
                    String tmp = gson.toJson(videoDetails);
                    this.setVideoDetails(videoDetails);
                }
            }else  if (details.getContentType().equalsIgnoreCase(AppConstants.PERSON)) {
                if (details.getPersonContent() != null) {
                    this.contentType = details.getPersonContent().getPersonType();
                }
            } else  if (details.getContentType().equalsIgnoreCase(AppConstants.AUDIO)) {
                if (details.getAudioContent() != null) {
                    this.contentType = details.getAudioContent().getAudioType();
                }
            }  else {
                if (details.getContentType().equalsIgnoreCase(AppConstants.CUSTOM)) {
                    if (details.getCustomContent() != null) {
                        this.customType = details.getCustomContent().getCustomType();
                    }
                }
            }
            this.assetType = details.getContentType() == null ? "" : details.getContentType();
            this.images = details.getImages();
            this.imageContent = AppCommonMethod.getImageContent(details);
            this.assetGenres = details.getGenres() == null ? new ArrayList<>() : details.getGenres();
            this.season = "";
            this.id = details.getId();
            this.sku = details.getSku() == null ? "" : details.getSku();
            this.isNew = false;
            this.episodeNo = details.getEpisodeNumber() == null ? "" : details.getEpisodeNumber();
            this.status = details.getStatus() == null ? "" : details.getStatus();
            if (details.getPosition() != null) {
                this.videoPosition = details.getPosition();
            }

            this.longDescription = details.getLongDescription() == null ? "" : details.getLongDescription().toString().trim();

            this.duration = (long) details.getVideo().getDuration();
            this.contentRelease= details.getVideo().getContentRelease() == null ? "" :details.getVideo().getContentRelease();
            this.languages= details.getVideo().getLanguages() == null ? "" :details.getVideo().getLanguages();
            this.certificate= details.getVideo().getCertificates() == null ? "" :details.getVideo().getCertificates();
        } catch (Exception ignored) {
            Logger.e("ContinueWatching", ignored.getMessage());
        }

    }

    private List<SongsAlbumIdItem> songs_artist_ids;
    //for search data.......
    public EnveuVideoItemBean(ItemsItem details,boolean isMusicApp) {
        try {
            this.id = details.getId();
            this.status = details.getStatus() == null ? "" : details.getStatus();
            this.title = details.getTitle() == null ? "" : details.getTitle();
            this.description = details.getDescription() == null ? "" : details.getDescription().trim();
            this.contentSlug = details.getContentSlug() == null ? "" : details.getContentSlug();
            this.assetGenres = details.getGenres() == null ? new ArrayList<>() : details.getGenres();
            this.assetCast = details.getCast() == null ? new ArrayList<>() : details.getCast();
            this.contentProvider = details.getContentProvider() == null ? "" : details.getContentProvider();
            this.assetCast = details.getCast() == null ? new ArrayList<>() : details.getCast();

            if (details.getContentType() != null){
                this.assetType = details.getContentType();
            }

            if (details.getExternalRefId() != null){
                this.externalRefId = details.getExternalRefId();
            }

            if (details.getParentContent() != null){
                this.personContent = details.getPersonContent();
            }

            if (details.getContentType().equalsIgnoreCase(AppConstants.VIDEO)) {
                if (details.getVideo() != null) {
                    this.videoDetails = details.getVideo();
                    this.setVideoDetails(videoDetails);
                }
                if (isMusicApp){
                    this.contentType = details.getContentType();
                }
            }else  if (details.getContentType().equalsIgnoreCase(AppConstants.PERSON)) {
                if (details.getPersonContent() != null) {
                    this.contentType = details.getPersonContent().getPersonType();
                }
            } else  if (details.getContentType().equalsIgnoreCase(AppConstants.AUDIO)) {
                if (details.getAudioContent() != null) {
                    this.contentType = details.getAudioContent().getAudioType();
                }
                if (details.getCustomData().getSongs_artist_ids() != null){
                    songs_artist_ids=details.getCustomData().getSongs_artist_ids();
                }
            }   else {
                this.contentType = details.getContentType();
                if (details.getContentType().equalsIgnoreCase(AppConstants.CUSTOM)) {
                    if (details.getCustomContent() != null) {
                        this.customContent=details.getCustomContent();
                        this.customType = details.getCustomContent().getCustomType();
                    }
                }
            }
            setCustomDataV3(details.getCustomData());
            this.images = details.getImages();
            this.imageContent = AppCommonMethod.getImageContent(details);
            if (details.getContentType().equalsIgnoreCase(AppConstants.AUDIO)) {
                this.posterURL = ImageLayer.getInstance().getSongsPosterImageUrl(details , AppConstants.SQR);
            } else  {
                this.posterURL = ImageLayer.getInstance().getPosterImageUrl1(details,isMusicApp);
            }
            if (details.getExternalRefId() != null && !details.getExternalRefId().equalsIgnoreCase("")) {
                this.externalRefId = details.getExternalRefId();
                this.setExternalRefId(externalRefId);
            }
            this.premium = details.getPremium();
            this.assetGenres = details.getGenres() == null ? new ArrayList<>() : details.getGenres();
            this.season = "";
            this.sku = details.getSku() == null ? "" : details.getSku();
            this.isNew = false;
            this.episodeNo = details.getEpisodeNumber() == null ? "" : details.getEpisodeNumber();
            this.assetType = details.getContentType() == null ? "" : details.getContentType();
            this.brightcoveVideoId = details.getBrightcoveContentId() == null ? "" : details.getBrightcoveContentId();
            this.series = "";
            this.longDescription = details.getLongDescription() == null ? "" : details.getLongDescription().toString().trim();
            this.externalRefId = details.getExternalRefId() == null ? "" : details.getExternalRefId();

            if (details.getCustomData() != null && details.getCustomData().getReelCreatorId() != null){
                String json = new Gson().toJson(details.getCustomData());
                this.customData = new Gson().fromJson(json, com.enveu.beanModelV3.videoDetailV3.CustomData .class);
            }
        } catch (Exception e) {
            Logger.w("search" + e);
        }
    }

    public String getDisplay_tags() {
        return display_tags;
    }

    public void setDisplay_tags(String display_tags) {
        this.display_tags = display_tags;
    }

    public String getSeasonNumber() {
        return seasonNumber;
    }

    public void setSeasonNumber(String seasonNumber) {
        this.seasonNumber = seasonNumber;
    }

    public int getContentOrder() {
        return contentOrder;
    }

    public void setContentOrder(int contentOrder) {
        this.contentOrder = contentOrder;
    }

    public int getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(int responseCode) {
        this.responseCode = responseCode;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getContentRelease() {
        return contentRelease;
    }

    public void setContentRelease(String contentRelease) {
        this.contentRelease = contentRelease;
    }

    public String getLanguages() {
        return languages;
    }

    public void setLanguages(String languages) {
        this.languages = languages;
    }

    public String getCertificate() {
        return certificate;
    }

    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }

    public String getName() {
        return name;
    }

    public ArrayList getSeasons() {
        return seasons;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getVodCount() {
        return vodCount;
    }

    public void setVodCount(int vodCount) {
        this.vodCount = vodCount;
    }

    public int getSeasonCount() {
        return seasonCount;
    }

    public List<SongsAlbumIdItem> getSongs_artist_ids() { return songs_artist_ids; }

    public void setSeasonCount(int seasonCount) {
        this.seasonCount = seasonCount;
    }

    public String getThumbnailImage() {
        return thumbnailImage;
    }

    public void setThumbnailImage(String thumbnailImage) {
        this.thumbnailImage = thumbnailImage;
    }

   /* public Object getVastTag() {
        return vastTag;
    }

    public void setVastTag(Object vastTag) {
        this.vastTag = vastTag;
    }*/

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLongDescription() {
        return longDescription;
    }

    public void setLongDescription(String longDescription) {
        this.longDescription = longDescription;
    }

    public List<String> getAssetKeywords() {
        return assetKeywords;
    }

    public void setAssetKeywords(List<String> assetKeywords) {
        this.assetKeywords = assetKeywords;
    }

    public int getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(int likeCount) {
        this.likeCount = likeCount;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Object getSvod() {
        return svod;
    }

    public void setSvod(Object svod) {
        this.svod = svod;
    }

    public String getContentProvider() {
        return contentProvider;
    }

    public void setContentProvider(String contentProvider) {
        this.contentProvider = contentProvider;
    }

    public List<String> getAssetCast() {
        return assetCast;
    }

    public void setAssetCast(List<String> assetCast) {
        this.assetCast = assetCast;
    }

    public boolean isPremium() {
        return premium;
    }

    public void setPremium(boolean premium) {
        this.premium = premium;
    }

    public String getPosterURL() {
        return posterURL;
    }

    public ImageContent getImageContent() {
        return imageContent;
    }

    public void setPosterURL(String posterURL) {
        this.posterURL = posterURL;
    }

    public Object getPrice() {
        return price;
    }

    public void setPrice(Object price) {
        this.price = price;
    }

    public List<String> getAssetGenres() {
        return assetGenres;
    }

    public void setAssetGenres(List<String> assetGenres) {
        this.assetGenres = assetGenres;
    }

    public String getSeason() {
        return season;
    }

    public String getSkipintro_endTime() {
        return skipintro_endTime;
    }

    public String getSkipintro_startTime() {
        return skipintro_startTime;
    }

    public void setSeason(String season) {
        this.season = season;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public boolean isNew() {
        return isNew;
    }

    public void setNew(boolean aNew) {
        this.isNew = aNew;
    }

    public Object getTvod() {
        return tvod;
    }

    public void setTvod(Object tvod) {
        this.tvod = tvod;
    }

    public Object getEpisodeNo() {
        return episodeNo;
    }

    public void setEpisodeNo(Object episodeNo) {
        this.episodeNo = episodeNo;
    }

    public String getAssetType() {
        return assetType;
    }

    public void setAssetType(String assetType) {
        this.assetType = assetType;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public String getUploadedAssetKey() {
        return uploadedAssetKey;
    }

    public void setUploadedAssetKey(String uploadedAssetKey) {
        this.uploadedAssetKey = uploadedAssetKey;
    }

    public String getBrightcoveVideoId() {
        return brightcoveVideoId;
    }

    public void setBrightcoveVideoId(String brightcoveVideoId) {
        this.brightcoveVideoId = brightcoveVideoId;
    }

    public String getSeries() {
        return series;
    }

    public void setSeries(String series) {
        this.series = series;
    }

    public String getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(String seriesId) {
        this.seriesId = seriesId;
    }

    public Object getPlans() {
        return plans;
    }

    public void setPlans(Object plans) {
        this.plans = plans;
    }

    public long getPublishedDate() {
        return publishedDate;
    }

    public void setPublishedDate(long publishedDate) {
        this.publishedDate = publishedDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getVideoPosition() {
        return videoPosition;
    }

    public void setVideoPosition(long videoPosition) {
        this.videoPosition = videoPosition;
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    public String getImageType() {
        return imageType;
    }

    public String getParentalRating() {
        return parentalRating;
    }

    public void setParentalRating(String parentalRating) {
        this.parentalRating = parentalRating;
    }

    public String getComingSoon() {
        return iscomingsoon;
    }

    public void setComingSoon(String iscomingsoon) {
        this.iscomingsoon = iscomingsoon;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getYear() {
        return year;
    }

    public String getReleaseYear() {
        return releaseYear;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public void setReleaseYear(String releaseYear) {
        this.releaseYear = releaseYear;
    }

    public String getProducer() {
        return producer;
    }

    public void setProducer(String producer) {
        this.producer = producer;
    }


    public String getTrailerReferenceId() {
        return trailerReferenceId;
    }

    public void setTrailerReferenceId(String trailerReferenceId) {
        this.trailerReferenceId = trailerReferenceId;
    }

    public String getQuality() {
        return quality;
    }

    public void setQuality(String quality) {
        this.quality = quality;
    }

    public String getSponsors() {
        return sponsors;
    }

    public void setSponsors(String sponsors) {
        this.sponsors = sponsors;
    }

    public String getIsNewS() {
        return isNewS;
    }

    public void setIsNewS(String isNewS) {
        this.isNewS = isNewS;
    }

    public String getIsVIP() {
        return isVIP;
    }

    public void setIsVIP(String isVIP) {
        this.isVIP = isVIP;
    }

    public void setVastTag(String VastTag) {
        this.vastTag = VastTag;
    }

    public String getVastTag() {
        return vastTag;
    }

    public String getWidevineLicence() {
        return widevineLicence;
    }

    public void setWidevineLicence(String widevineLicence) {
        this.widevineLicence = widevineLicence;
    }

    public CustomContentData getCustomContent() {
        return customContent;
    }

    public void setCustomContent(CustomContentData customContent) {
        this.customContent = customContent;
    }

    public String getGetWidevineURL() {
        return getWidevineURL;
    }

    public void setGetWidevineURL(String getWidevineURL) {
        this.getWidevineURL = getWidevineURL;
    }

    public String getDescription_two() {
        return description_two;
    }

    public void setDescription_two(String description_two) {
        this.description_two = description_two;
    }

    public String getDescription_three() {
        return description_three;
    }

    public void setDescription_three(String description_three) {
        this.description_three = description_three;
    }

    public String getProfilePosterUrl() {
        return profilePosterUrl;
    }

    public void setProfilePosterUrl(String profilePosterUrl) {
        this.profilePosterUrl = profilePosterUrl;
    }

    public SinglesArtistItem getSinglesArtistItem() {
        return singlesArtistItem;
    }

    public void setSinglesArtistItem(SinglesArtistItem singlesArtistItem) {
        this.singlesArtistItem = singlesArtistItem;
    }

    public com.enveu.beanModelV3.videoDetailV3.CustomData getCustomData() {
        return customData;
    }

    public void setCustomData(com.enveu.beanModelV3.videoDetailV3.CustomData customData) {
        this.customData = customData;
    }

    public com.enveu.beanModelV3.playListModelV2.CustomData getCustomDataV3(){
        return  customDataV3;
    }

    public void setCustomDataV3(
            com.enveu.beanModelV3.playListModelV2.CustomData customData){
        this.customDataV3 = customData;
    }


    @Override
    public String toString() {
        return
                "VideosItem{" +
                        "vastTag = '" + vastTag + '\'' +
                        "seriesCustomData = '" + seriesCustomData + '\'' +
                        ",description = '" + description + '\'' +
                        ",assetKeywords = '" + assetKeywords + '\'' +
                        ",likeCount = '" + likeCount + '\'' +
                        ",title = '" + title + '\'' +
                        ",skipintro_startTime = '" + skipintro_startTime + '\'' +
                        ",skipintro_endTime = '" + skipintro_endTime + '\'' +
                        ",svod = '" + svod + '\'' +
                        ",contentProvider = '" + contentProvider + '\'' +
                        ",assetCast = '" + assetCast + '\'' +
                        ",premium = '" + premium + '\'' +
                        ",isCurrentlyPlaying = '" + isCurrentlyPlaying + '\'' +
                        ",posterURL = '" + posterURL + '\'' +
                        ",contentSlug = '" + contentSlug + '\'' +
                        ",profilePosterUrl = '" + profilePosterUrl + '\'' +
                        ",price = '" + price + '\'' +
                        ",assetGenres = '" + assetGenres + '\'' +
                        ",season = '" + season + '\'' +
                        ",id = '" + id + '\'' +
                        ",sku = '" + sku + '\'' +
                        ",new = '" + isNew + '\'' +
                        ",tvod = '" + tvod + '\'' +
                        ",episodeNo = '" + episodeNo + '\'' +
                        ",assetType = '" + assetType + '\'' +
                        ",commentCount = '" + commentCount + '\'' +
                        ",uploadedAssetKey = '" + uploadedAssetKey + '\'' +
                        ",brightcoveVideoId = '" + brightcoveVideoId + '\'' +
                        ",series = '" + series + '\'' +
                        ",plans = '" + plans + '\'' +
                        ",publishedDate = '" + publishedDate + '\'' +
                        ",startTime = '" + startTime + '\'' +
                        ",status = '" + status + '\'' +
                        ",trailerReferenceId = '" + trailerReferenceId + '\'' +
                        ",video = '" + videoDetails + '\'' +
                        ",video = '" + videoDetailsEpisodes + '\'' +
                        ",customContent = '" + customContent + '\'' +
                        ",customType = '" + customType + '\'' +
                        ",parentContent = '" + parentContent + '\'' +
                        ",parentContent2 = '" + parentContent2 + '\'' +
                        ",liveContent = '" + liveContent + '\'' +
                        ",isDrmDisabled = '" + isDrmDisabled + '\'' +
                        ",externalRefId = '" + externalRefId + '\'' +
                        ",lyrics = '" + lyrics + '\'' +
                        "}";
    }


    public String getCustomType() {
        return customType;
    }

    public void setCustomType(String customType) {
        this.customType = customType;
    }

    public Drawable getVipImageDrawable() {
        OttApplication application = OttApplication.Companion.getInstance();
        Drawable drawable = null;
        try {
            //  drawable = ContextCompat.getDrawable(application, R.drawable.vip_icon_120);
        } catch (Exception e) {
            Logger.w(e);
        }
        return drawable;
    }

    public Drawable getNewSeriesImageDrawable() {
        OttApplication application = OttApplication.Companion.getInstance();
        try {
            if (KsPreferenceKeys.getInstance().getAppLanguage().equalsIgnoreCase("English")) {
                return ContextCompat.getDrawable(application, R.drawable.series_icon_120);
            } else if (KsPreferenceKeys.getInstance().getAppLanguage().equalsIgnoreCase("Thai")) {
                return ContextCompat.getDrawable(application, R.drawable.series_thai_icon);
            } else {
                return ContextCompat.getDrawable(application, R.drawable.series_icon_120);
            }
        } catch (Exception e) {
            return ContextCompat.getDrawable(application, R.drawable.series_icon_120);
        }
    }

    public Drawable getEpisodeImageDrawable() {
        OttApplication application = OttApplication.Companion.getInstance();
        try {
            if (KsPreferenceKeys.getInstance().getAppLanguage().equalsIgnoreCase("English")) {
                return ContextCompat.getDrawable(application, R.drawable.episode_icon_120);
            } else if (KsPreferenceKeys.getInstance().getAppLanguage().equalsIgnoreCase("Thai")) {
                return ContextCompat.getDrawable(application, R.drawable.episode_thai_icon);
            } else {
                return ContextCompat.getDrawable(application, R.drawable.episode_icon_120);
            }
        } catch (Exception e) {
            return ContextCompat.getDrawable(application, R.drawable.episode_icon_120);
        }
    }

    public Drawable getNewMoviesDrawable() {
        OttApplication application = OttApplication.Companion.getInstance();
        try {
            if (KsPreferenceKeys.getInstance().getAppLanguage().equalsIgnoreCase("English")) {
                return ContextCompat.getDrawable(application, R.drawable.new_movie_120);
            } else if (KsPreferenceKeys.getInstance().getAppLanguage().equalsIgnoreCase("Thai")) {
                return ContextCompat.getDrawable(application, R.drawable.new_movie_thai120);
            } else {
                return ContextCompat.getDrawable(application, R.drawable.new_movie_120);
            }
        } catch (Exception e) {
            return ContextCompat.getDrawable(application, R.drawable.new_movie_120);
        }
    }

    public int getSearchViewType() {
        return SearchViewType;
    }

    public void setSearchViewType(int searchViewType) {
        SearchViewType = searchViewType;
    }

    public List<MovieArtistsResponse> getMovieArtistsIds() {
        return movieArtistId;
    }

    public void setMovieArtistsIds(List<MovieArtistsResponse> movies_artists_ids) {
        this.movieArtistId = movies_artists_ids;
    }

    public List<MovieArtistsResponse> getMovieDirectorId() {
        return movieDirectorId;
    }

    public void setMovieDirectorId(List<MovieArtistsResponse> directorId) {
        this.movieDirectorId = directorId;
    }
}