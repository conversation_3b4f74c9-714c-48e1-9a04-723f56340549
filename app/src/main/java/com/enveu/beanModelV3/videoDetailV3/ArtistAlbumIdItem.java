package com.enveu.beanModelV3.videoDetailV3;

import android.os.Parcelable;

import com.enveu.client.playlist.beanv2_0.ImagesItem;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class ArtistAlbumIdItem implements Serializable {

	@SerializedName("images")
	private List<ImagesItem> images;

	@SerializedName("externalIdentifier")
	private Object externalIdentifier;

	@SerializedName("contentSlug")
	private String contentSlug;

	@SerializedName("mediaType")
	private String mediaType;

	@SerializedName("id")
	private int id;

	@SerializedName("title")
	private String title;

	@SerializedName("contentType")
	private String contentType;



	public final static Parcelable.Creator<ArtistAlbumIdItem> CREATOR = new Parcelable.Creator<ArtistAlbumIdItem>() {


		@SuppressWarnings({
				"unchecked"
		})
		public ArtistAlbumIdItem createFromParcel(android.os.Parcel in) {
			return new ArtistAlbumIdItem(in);
		}

		public ArtistAlbumIdItem[] newArray(int size) {
			return (new ArtistAlbumIdItem[size]);
		}

	};


	protected ArtistAlbumIdItem(android.os.Parcel in) {
		in.readList(this.images, (Object.class.getClassLoader()));
		this.externalIdentifier = in.readValue((Object.class.getClassLoader()));
		this.contentSlug = ((String) in.readValue((String.class.getClassLoader())));
		this.mediaType = ((String) in.readValue((String.class.getClassLoader())));
		this.id = ((int) in.readValue((int.class.getClassLoader())));
		this.title = ((String) in.readValue((String.class.getClassLoader())));
		this.contentType = ((String) in.readValue((String.class.getClassLoader())));


	}

	public void writeToParcel(android.os.Parcel dest) {
		dest.writeValue(images);
		dest.writeValue(externalIdentifier);
		dest.writeValue(contentSlug);
		dest.writeValue(mediaType);
		dest.writeValue(id);
		dest.writeValue(title);
		dest.writeValue(contentType);
	}


	public List<ImagesItem> getImages(){
		return images;
	}

	public Object getExternalIdentifier(){
		return externalIdentifier;
	}

	public String getContentSlug(){
		return contentSlug;
	}

	public String getMediaType(){
		return mediaType;
	}

	public int getId(){
		return id;
	}

	public String getTitle(){
		return title;
	}

	public String getContentType(){
		return contentType;
	}
}