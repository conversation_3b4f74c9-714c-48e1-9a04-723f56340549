package com.enveu.beanModelV3.videoDetailV3;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class ArtistSongIdItem implements Serializable {

	@SerializedName("images")
	private List<Object> images;

	@SerializedName("externalIdentifier")
	private String externalIdentifier;

	@SerializedName("contentSlug")
	private String contentSlug;

	@SerializedName("mediaType")
	private String mediaType;

	@SerializedName("id")
	private int id;

	@SerializedName("title")
	private String title;

	@SerializedName("contentType")
	private String contentType;

	public List<Object> getImages(){
		return images;
	}

	public String getExternalIdentifier(){
		return externalIdentifier;
	}

	public String getContentSlug(){
		return contentSlug;
	}

	public String getMediaType(){
		return mediaType;
	}

	public int getId(){
		return id;
	}

	public String getTitle(){
		return title;
	}

	public String getContentType(){
		return contentType;
	}
}