
package com.enveu.beanModelV3.videoDetailV3;


import com.enveu.beanModelV3.RelatedRailsCommonData;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.SinglesArtistItem;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.SongsArtistIdsItem;
import com.enveu.client.playlist.beanv2_0.ReelCreatorIdModel;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.AlbumArtistIdItem;

import java.io.Serializable;
import java.util.List;


public class CustomData implements Serializable {

    @SerializedName("albums-artist-ids")
    private List<AlbumArtistIdItem> albumArtistId;

    public List<AlbumArtistIdItem> getAlbumArtistId(){
        return albumArtistId;
    }
    @SerializedName("movies-artists-ids")
    private List<MovieArtistsResponse> movies_artists_ids;

    @SerializedName("director")
    private List<MovieArtistsResponse> director;

    @SerializedName("actor")
    private List<MovieArtistsResponse> actor;


    @SerializedName("episode-series-id")
    private String episodeSeriesId;

    @SerializedName("episode-season-id")
    @Expose
    private Season_series episode_season_id;

    @SerializedName("trailer-linked-with-id")
    private String trailerLinkedWithId;

    @SerializedName("play-page")
    private String playPage;

    @SerializedName("orientation")
    private String orientation;

    @SerializedName("game-category")
    private String gameCategory;

    @SerializedName("age-category")
    private String ageCategory;


    @SerializedName("artist-albums-ids")
    private List<ArtistAlbumIdItem> artistAlbumId;

    @SerializedName("songs-artist-ids")
    private List<SongsArtistIdsItem> songs_artist_ids;

    @SerializedName("lyrics")
    private String lyrics;

    @SerializedName("genres")
    private List<RelatedRailsCommonData.Data.Item.CustomData.Genre> genres;
    @SerializedName("sub-genres")
    private List<RelatedRailsCommonData.Data.Item.CustomData.SubGenre> sub_genres;
    @SerializedName("year-of-release")
    String release_year;
    @SerializedName("release-year")
    String release_year1;

    @SerializedName("is_exclusive")
    boolean is_exclusive;
    @SerializedName("songs-albums-id")
    private SongsAlbumIdItem songs_albums_id;

    @SerializedName("singles-artist-id")
    private SinglesArtistItem singles_artist_id;

    @SerializedName("showTitle")
    private String showTitle;

    @SerializedName("reel-creator-id")
    private ReelCreatorIdModel reelCreatorId;

    public ReelCreatorIdModel getReelCreatorId() {
        return reelCreatorId;
    }

    public void setReelCreatorId(ReelCreatorIdModel reelCreatorId) {
        this.reelCreatorId = reelCreatorId;
    }

    public String getShowTitle() {
        return showTitle;
    }

    public void setShowTitle(String showTitle) {
        this.showTitle = showTitle;
    }

    public List<MovieArtistsResponse> getMovieArtistsIds(){
        return movies_artists_ids;
    }
    public void setMovieDirector(List<MovieArtistsResponse> director) {
        this.director = director;
    }

    public void setActor(List<MovieArtistsResponse> actor) {
        this.actor = actor;
    }
    public List<MovieArtistsResponse> getMovieDirectorId() {
        return director;
    }

    public List<MovieArtistsResponse> getActorId() {
        return actor;
    }
    public List<ArtistAlbumIdItem> getArtistAlbumId(){
        return artistAlbumId;
    }

    @SerializedName("artist-songs-ids")
    private List<ArtistSongIdItem> artistSongId;

    public List<ArtistSongIdItem> getArtistSongId(){
        return artistSongId;
    }

    public String getLyrics() {
        return lyrics;
    }

    public void setLyrics(String lyrics) {
        this.lyrics = lyrics;
    }

    public String getTrailerLinkedWithId() {
        return trailerLinkedWithId;
    }

    public String getEpisodeSeriesId(){
        return episodeSeriesId;
    }

    public String getPlayPage() {
        return playPage;
    }

    public String getOrientation() {
        return orientation;
    }

    public String getGameCategory() {
        return gameCategory;
    }

    public String getAgeCategory() {
        return ageCategory;
    }



    public List<MovieArtistsResponse> getMovies_artists_ids() {
        return movies_artists_ids;
    }

    public List<MovieArtistsResponse> getDirector() {
        return director;
    }

    public List<MovieArtistsResponse> getActor() {
        return actor;
    }

    public Season_series getEpisode_season_id() {
        return episode_season_id;
    }


    public boolean isIs_exclusive() {
        return is_exclusive;
    }

    public SongsAlbumIdItem getSongs_albums_id() {
        return songs_albums_id;
    }

    public List<SongsArtistIdsItem> getSongs_artist_ids() {
        return songs_artist_ids;
    }

    public SinglesArtistItem getSingles_artist_id() {
        return singles_artist_id;
    }

    public List<RelatedRailsCommonData.Data.Item.CustomData.Genre> getGenres() {
        return genres;
    }
    public List<RelatedRailsCommonData.Data.Item.CustomData.SubGenre> getSub_genres() {
        return sub_genres;
    }
    public String getRelease_year() {
        return release_year;
    }
    public String getRelease_year1() {
        return release_year1;
    }
}