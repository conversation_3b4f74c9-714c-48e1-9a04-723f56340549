package com.enveu.beanModelV3.videoDetailV3

import org.json.JSONArray
import org.json.JSONObject
import java.io.Serializable

class Season_series (
    val id: Int,
    val externalIdentifier: String?,
    val title: String,
    val contentSlug: String,
    val contentType: String,
    val mediaType: String,
    val description: String,
    val images: List<Image_Container>
) : Serializable

data class Image_Container(
    val id: Long,
    val imageKey: String,
    val src: String,
    val height: Double,
    val width: Double,
    val colorPalette: List<String>,
    val dominantColor: String,
    val imageType: String?,
    val status: String,
    val tag: String,
    val originalImageSizeInBytes: Int,
    val isDefault: Boolean,
    val showTitle: Boolean
) : Serializable
