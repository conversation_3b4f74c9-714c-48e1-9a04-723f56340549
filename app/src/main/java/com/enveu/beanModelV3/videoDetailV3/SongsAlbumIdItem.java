package com.enveu.beanModelV3.videoDetailV3;

import android.os.Parcelable;

import com.enveu.client.playlist.beanv2_0.Image;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class SongsAlbumIdItem implements Serializable {

	@SerializedName("images")
	private List<Image> images;

	@SerializedName("externalIdentifier")
	private Object externalIdentifier;

	@SerializedName("contentSlug")
	private String contentSlug;

	@SerializedName("mediaType")
	private String mediaType;

	@SerializedName("id")
	private int id;

	@SerializedName("title")
	private String title;

	@SerializedName("contentType")
	private String contentType;



	public final static Parcelable.Creator<SongsAlbumIdItem> CREATOR = new Parcelable.Creator<SongsAlbumIdItem>() {


		@SuppressWarnings({
				"unchecked"
		})
		public SongsAlbumIdItem createFromParcel(android.os.Parcel in) {
			return new SongsAlbumIdItem(in);
		}

		public SongsAlbumIdItem[] newArray(int size) {
			return (new SongsAlbumIdItem[size]);
		}

	};


	protected SongsAlbumIdItem(android.os.Parcel in) {
		in.readList(this.images, (Object.class.getClassLoader()));
		this.externalIdentifier = in.readValue((Object.class.getClassLoader()));
		this.contentSlug = ((String) in.readValue((String.class.getClassLoader())));
		this.mediaType = ((String) in.readValue((String.class.getClassLoader())));
		this.id = ((int) in.readValue((int.class.getClassLoader())));
		this.title = ((String) in.readValue((String.class.getClassLoader())));
		this.contentType = ((String) in.readValue((String.class.getClassLoader())));


	}

	public void writeToParcel(android.os.Parcel dest) {
		dest.writeValue(images);
		dest.writeValue(externalIdentifier);
		dest.writeValue(contentSlug);
		dest.writeValue(mediaType);
		dest.writeValue(id);
		dest.writeValue(title);
		dest.writeValue(contentType);
	}


	public List<Image> getImages() {
		return images;
	}

	public Object getExternalIdentifier(){
		return externalIdentifier;
	}

	public String getContentSlug(){
		return contentSlug;
	}

	public String getMediaType(){
		return mediaType;
	}

	public int getId(){
		return id;
	}

	public String getTitle(){
		return title;
	}

	public String getContentType(){
		return contentType;
	}
}