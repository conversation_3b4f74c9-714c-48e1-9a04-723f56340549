package com.enveu.beanModelV3.videoDetailV3.list;

import java.util.List;

public class Content{
	private Object longDescription;
	private Object liveContent;
	private Object imageContent;
	private List<Object> keywords;
	private Object externalRefId;
	private Accessibility accessibility;
	private String description;
	private String contentSlug;
	private CustomData customData;
	private Object video;
	private String title;
	private Object parentalRating;
	private Object organizationId;
	private long lastUpdated;
	private long dateCreated;
	private boolean premium;
	private Object customContent;
	private int id;
	private String sku;
	private Object analyticsId;
	private String contentType;
	private List<ImagesItem> images;
	private AudioContent audioContent;
	private Object styleInfo;
	private SeoInfo seoInfo;
	private String contentSource;
	private Object contentReviewRating;
	private List<Object> targetingTags;
	private long publishedDate;
	private Object parentContent;
	private Object personContent;

	public Object getLongDescription(){
		return longDescription;
	}

	public Object getLiveContent(){
		return liveContent;
	}

	public Object getImageContent(){
		return imageContent;
	}

	public List<Object> getKeywords(){
		return keywords;
	}

	public Object getExternalRefId(){
		return externalRefId;
	}

	public Accessibility getAccessibility(){
		return accessibility;
	}

	public String getDescription(){
		return description;
	}

	public String getContentSlug(){
		return contentSlug;
	}

	public CustomData getCustomData(){
		return customData;
	}

	public Object getVideo(){
		return video;
	}

	public String getTitle(){
		return title;
	}

	public Object getParentalRating(){
		return parentalRating;
	}

	public Object getOrganizationId(){
		return organizationId;
	}

	public long getLastUpdated(){
		return lastUpdated;
	}

	public long getDateCreated(){
		return dateCreated;
	}

	public boolean isPremium(){
		return premium;
	}

	public Object getCustomContent(){
		return customContent;
	}

	public int getId(){
		return id;
	}

	public String getSku(){
		return sku;
	}

	public Object getAnalyticsId(){
		return analyticsId;
	}

	public String getContentType(){
		return contentType;
	}

	public List<ImagesItem> getImages(){
		return images;
	}

	public AudioContent getAudioContent(){
		return audioContent;
	}

	public Object getStyleInfo(){
		return styleInfo;
	}

	public SeoInfo getSeoInfo(){
		return seoInfo;
	}

	public String getContentSource(){
		return contentSource;
	}

	public Object getContentReviewRating(){
		return contentReviewRating;
	}

	public List<Object> getTargetingTags(){
		return targetingTags;
	}

	public long getPublishedDate(){
		return publishedDate;
	}

	public Object getParentContent(){
		return parentContent;
	}

	public Object getPersonContent(){
		return personContent;
	}
}