package com.enveu.beanModelV3.videoDetailV3.list;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class CustomData implements Serializable {

	@SerializedName("songs-albums-id")
	private SongsAlbumsId songsAlbumsId;

	@SerializedName("lyrics")
	private String lyrics;

	@SerializedName("song-writers")
	public ArrayList<CommonCustomData> songWitter;
	@SerializedName("composer")
	private ArrayList<CommonCustomData> composer;

	@SerializedName("session-artist")
	private List<CommonCustomData> sessionArtist;
	@SerializedName("genres")
	private List<CommonCustomData> genres;
	@SerializedName("music-publisher")
	private List<CommonCustomData> musicPublisher;

	public ArrayList<CommonCustomData> arranger;

	@SerializedName("mix-and-mastering")
	private List<CommonCustomData> mastering;

	@SerializedName("producers")
	private List<CommonCustomData> producers;

	@SerializedName("licensor")
	private List<CommonCustomData> licensor;

	@SerializedName("recording-engineers-audio")
	private List<CommonCustomData> recordingEngineersAudio;
	@SerializedName("recording-engineers-video")
	private List<CommonCustomData> recordingEngineersVideo;

	@SerializedName("conductor")
	private List<CommonCustomData> conductors;

	@SerializedName("distributor")
	private List<CommonCustomData> distributor;

	@SerializedName("songs-artist-ids")
	private List<SongsArtistIdsItem> songsArtistIds;

	@SerializedName("RelatedInstructor")
	private String relatedInstructor;

	@SerializedName("trackNumber")
	private String trackNumber;

	public String getTrackNumber() {
		return trackNumber;
	}

	public void setTrackNumber(String trackNumber) {
		this.trackNumber = trackNumber;
	}

	public SongsAlbumsId getSongsAlbumsId(){
		return songsAlbumsId;
	}

	public List<SongsArtistIdsItem> getSongsArtistIds() {
		return songsArtistIds;
	}

	public List<CommonCustomData>  getArranger() {
		return arranger;
	}

	public List<CommonCustomData> getConductors() {
		return conductors;
	}

	public List<CommonCustomData> getProducers() {
		return producers;
	}

	public List<CommonCustomData> getMastering() {
		return mastering;
	}

	public ArrayList<CommonCustomData> getComposer() {
		return composer;
	}

	public List<CommonCustomData> getSessionArtist() {
		return sessionArtist;
	}

	public List<CommonCustomData> getMusicPublisher() {
		return musicPublisher;
	}

	public List<CommonCustomData> getRecordingEngineersAudio() {
		return recordingEngineersAudio;
	}

	public List<CommonCustomData> getRecordingEngineersVideo() {
		return recordingEngineersVideo;
	}

	public List<CommonCustomData> getDistributor() {
		return distributor;
	}

    public String getLyrics() {
        return lyrics;
    }

    public void setLyrics(String lyrics) {
        this.lyrics = lyrics;
    }

	public ArrayList<CommonCustomData> getSongWitter() {
		return songWitter;
	}

	public List<CommonCustomData> getLicensor() {
		return licensor;
	}

	public List<CommonCustomData> getGenres() {
		return genres;
	}

	public String getRelatedInstructor() {
		return relatedInstructor;
	}

}