package com.enveu.beanModelV3.videoDetailV3.list;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class DataItem implements Parcelable {

	@SerializedName("longDescription")
	private String longDescription;

	public com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent getImageContent() {
		return imageContent;
	}
	@SerializedName("liveContent")
	private Object liveContent;


	public void setImageContent(ImageContent imageContent) {
		this.imageContent = imageContent;
	}

	@SerializedName("imageContent")
	private com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent imageContent;

	@SerializedName("keywords")
	private List<Object> keywords;

	@SerializedName("externalRefId")
	private String externalRefId;

	@SerializedName("accessibility")
	private Accessibility accessibility;

	@SerializedName("description")
	private String description;

	@SerializedName("contentSlug")
	private String contentSlug;

	@SerializedName("customData")
	private CustomData customData;

	@SerializedName("customDataV3")
	private com.enveu.beanModelV3.playListModelV2.CustomData customDataV3;

	@SerializedName("video")
	private Object video;

	@SerializedName("title")
	private String title;

	@SerializedName("parentalRating")
	private Object parentalRating;

	@SerializedName("organizationId")
	private Object organizationId;

	@SerializedName("lastUpdated")
	private long lastUpdated;

	@SerializedName("dateCreated")
	private long dateCreated;

	@SerializedName("premium")
	private boolean premium;

	@SerializedName("customContent")
	private Object customContent;

	@SerializedName("id")
	private int id;

	@SerializedName("sku")
	private String sku;

	@SerializedName("analyticsId")
	private Object analyticsId;

	@SerializedName("contentType")
	private String contentType;

	@SerializedName("images")
	private List<ImagesItem> images;

	@SerializedName("audioContent")
	private AudioContent audioContent;

	@SerializedName("styleInfo")
	private Object styleInfo;

	@SerializedName("seoInfo")
	private SeoInfo seoInfo;

	@SerializedName("contentSource")
	private String contentSource;

	@SerializedName("contentReviewRating")
	private Object contentReviewRating;

	@SerializedName("targetingTags")
	private List<Object> targetingTags;

	@SerializedName("publishedDate")
	private long publishedDate;

	@SerializedName("playCount")
	private int playCount;

	@SerializedName("parentContent")
	private Object parentContent;

	@SerializedName("personContent")
	private Object personContent;

	protected DataItem(Parcel in) {
		longDescription = in.readString();
		externalRefId = in.readString();
		description = in.readString();
		contentSlug = in.readString();
		title = in.readString();
		lastUpdated = in.readLong();
		dateCreated = in.readLong();
		premium = in.readByte() != 0;
		id = in.readInt();
		playCount = in.readInt();
		sku = in.readString();
		contentType = in.readString();
		contentSource = in.readString();
		publishedDate = in.readLong();
	}

	public static final Creator<DataItem> CREATOR = new Creator<DataItem>() {
		@Override
		public DataItem createFromParcel(Parcel in) {
			return new DataItem(in);
		}

		@Override
		public DataItem[] newArray(int size) {
			return new DataItem[size];
		}

	};


	public String getLongDescription(){
		return longDescription;
	}

	public Object getLiveContent(){
		return liveContent;
	}


	public List<Object> getKeywords(){
		return keywords;
	}

	public String getExternalRefId(){
		return externalRefId;
	}

	public Accessibility getAccessibility(){
		return accessibility;
	}

	public String getDescription(){
		return description;
	}

	public String getContentSlug(){
		return contentSlug;
	}

	public CustomData getCustomData(){
		return customData;
	}

	public com.enveu.beanModelV3.playListModelV2.CustomData getCustomDataV3() {
		return customDataV3;
	}

	public Object getVideo(){
		return video;
	}

	public String getTitle(){
		return title;
	}

	public Object getParentalRating(){
		return parentalRating;
	}

	public Object getOrganizationId(){
		return organizationId;
	}

	public long getLastUpdated(){
		return lastUpdated;
	}

	public long getDateCreated(){
		return dateCreated;
	}

	public boolean isPremium(){
		return premium;
	}

	public Object getCustomContent(){
		return customContent;
	}

	public int getId(){
		return id;
	}

	public String getSku(){
		return sku;
	}

	public Object getAnalyticsId(){
		return analyticsId;
	}

	public String getContentType(){
		return contentType;
	}

	public List<ImagesItem> getImages(){
		return images;
	}

	public AudioContent getAudioContent(){
		return audioContent;
	}

	public Object getStyleInfo(){
		return styleInfo;
	}

	public SeoInfo getSeoInfo(){
		return seoInfo;
	}

	public String getContentSource(){
		return contentSource;
	}

	public Object getContentReviewRating(){
		return contentReviewRating;
	}

	public List<Object> getTargetingTags(){
		return targetingTags;
	}

	public long getPublishedDate(){
		return publishedDate;
	}

	public Object getParentContent(){
		return parentContent;
	}

	public Object getPersonContent(){
		return personContent;
	}

	public int getPlayCount() {
		return playCount;
	}

	@Override
	public int describeContents() {
		return 0;
	}



	@Override
	public void writeToParcel(@NonNull Parcel parcel, int i) {
		parcel.writeString(longDescription);
		parcel.writeString(externalRefId);
		parcel.writeString(description);
		parcel.writeString(contentSlug);
		parcel.writeString(title);
		parcel.writeLong(lastUpdated);
		parcel.writeLong(dateCreated);
		parcel.writeByte((byte) (premium ? 1 : 0));
		parcel.writeInt(id);
		parcel.writeInt(playCount);
		parcel.writeString(sku);
		parcel.writeString(contentType);
		parcel.writeString(contentSource);
		parcel.writeLong(publishedDate);
	}
}