package com.enveu.beanModelV3.videoDetailV3.list;

import java.util.List;

public class ImageContent{
	private int originalImageSizeInBytes;
	private String src;
	private String imageKey;
	private String dominantColor;
	private boolean isDefault;
	private boolean showTitle;
	private Object width;
	private Long id;
	private String tag;
	private List<String> colorPalette;
	private String imageType;
	private Object height;
	private String status;

	public int getOriginalImageSizeInBytes(){
		return originalImageSizeInBytes;
	}

	public String getSrc(){
		return src;
	}

	public String getImageKey(){
		return imageKey;
	}

	public String getDominantColor(){
		return dominantColor;
	}

	public boolean isIsDefault(){
		return isDefault;
	}

	public boolean isShowTitle(){
		return showTitle;
	}

	public Object getWidth(){
		return width;
	}

	public Long getId(){
		return id;
	}

	public String getTag(){
		return tag;
	}

	public List<String> getColorPalette(){
		return colorPalette;
	}

	public String getImageType(){
		return imageType;
	}

	public Object getHeight(){
		return height;
	}

	public String getStatus(){
		return status;
	}
}