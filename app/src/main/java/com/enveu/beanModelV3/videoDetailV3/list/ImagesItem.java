package com.enveu.beanModelV3.videoDetailV3.list;

import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class ImagesItem implements Serializable {

	@SerializedName("originalImageSizeInBytes")
	private int originalImageSizeInBytes;

	@SerializedName("src")
	private String src;

	@SerializedName("imageKey")
	private String imageKey;

	@SerializedName("dominantColor")
	private String dominantColor;

	@SerializedName("isDefault")
	private boolean isDefault;

	@SerializedName("showTitle")
	private boolean showTitle;

	@SerializedName("width")
	private Object width;

	@SerializedName("id")
	private int id;

	@SerializedName("tag")
	private String tag;

	@SerializedName("colorPalette")
	private List<String> colorPalette;

	@SerializedName("imageType")
	private String imageType;

	@SerializedName("height")
	private Object height;

	@SerializedName("status")
	private String status;

	@SerializedName("longDescription")
	private Object longDescription;

	@SerializedName("liveContent")
	private Object liveContent;

	@SerializedName("imageContent")
	private ImageContent imageContent;

	@SerializedName("keywords")
	private List<Object> keywords;

	@SerializedName("externalRefId")
	private Object externalRefId;

	@SerializedName("accessibility")
	private Accessibility accessibility;

	@SerializedName("description")
	private String description;

	@SerializedName("contentSlug")
	private String contentSlug;

	@SerializedName("customData")
	private CustomData customData;

	@SerializedName("video")
	private Object video;

	@SerializedName("title")
	private String title;

	@SerializedName("parentalRating")
	private Object parentalRating;

	@SerializedName("organizationId")
	private Object organizationId;

	@SerializedName("lastUpdated")
	private long lastUpdated;

	@SerializedName("dateCreated")
	private long dateCreated;

	@SerializedName("premium")
	private boolean premium;

	@SerializedName("customContent")
	private Object customContent;

	@SerializedName("sku")
	private String sku;

	@SerializedName("analyticsId")
	private Object analyticsId;

	@SerializedName("contentType")
	private String contentType;

	@SerializedName("images")
	private Object images;

	@SerializedName("audioContent")
	private Object audioContent;

	@SerializedName("styleInfo")
	private Object styleInfo;

	@SerializedName("seoInfo")
	private Object seoInfo;

	@SerializedName("contentSource")
	private String contentSource;

	@SerializedName("contentReviewRating")
	private Object contentReviewRating;

	@SerializedName("targetingTags")
	private List<Object> targetingTags;

	@SerializedName("publishedDate")
	private Object publishedDate;

	@SerializedName("parentContent")
	private Object parentContent;

	@SerializedName("personContent")
	private Object personContent;

	public int getOriginalImageSizeInBytes(){
		return originalImageSizeInBytes;
	}

	public String getSrc(){
		return src;
	}

	public String getImageKey(){
		return imageKey;
	}

	public String getDominantColor(){
		return dominantColor;
	}

	public boolean isIsDefault(){
		return isDefault;
	}

	public boolean isShowTitle(){
		return showTitle;
	}

	public Object getWidth(){
		return width;
	}

	public int getId(){
		return id;
	}

	public String getTag(){
		return tag;
	}

	public List<String> getColorPalette(){
		return colorPalette;
	}

	public String getImageType(){
		return imageType;
	}

	public Object getHeight(){
		return height;
	}

	public String getStatus(){
		return status;
	}

	public Object getLongDescription(){
		return longDescription;
	}

	public Object getLiveContent(){
		return liveContent;
	}

	public ImageContent getImageContent(){
		return imageContent;
	}

	public List<Object> getKeywords(){
		return keywords;
	}

	public Object getExternalRefId(){
		return externalRefId;
	}

	public Accessibility getAccessibility(){
		return accessibility;
	}

	public String getDescription(){
		return description;
	}

	public String getContentSlug(){
		return contentSlug;
	}

	public CustomData getCustomData(){
		return customData;
	}

	public Object getVideo(){
		return video;
	}

	public String getTitle(){
		return title;
	}

	public Object getParentalRating(){
		return parentalRating;
	}

	public Object getOrganizationId(){
		return organizationId;
	}

	public long getLastUpdated(){
		return lastUpdated;
	}

	public long getDateCreated(){
		return dateCreated;
	}

	public boolean isPremium(){
		return premium;
	}

	public Object getCustomContent(){
		return customContent;
	}

	public String getSku(){
		return sku;
	}

	public Object getAnalyticsId(){
		return analyticsId;
	}

	public String getContentType(){
		return contentType;
	}

	public Object getImages(){
		return images;
	}

	public Object getAudioContent(){
		return audioContent;
	}

	public Object getStyleInfo(){
		return styleInfo;
	}

	public Object getSeoInfo(){
		return seoInfo;
	}

	public String getContentSource(){
		return contentSource;
	}

	public Object getContentReviewRating(){
		return contentReviewRating;
	}

	public List<Object> getTargetingTags(){
		return targetingTags;
	}

	public Object getPublishedDate(){
		return publishedDate;
	}

	public Object getParentContent(){
		return parentContent;
	}

	public Object getPersonContent(){
		return personContent;
	}
}

