package com.enveu.beanModelV3.videoDetailV3.mediaContentList;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class AlbumArtistIdItem implements Serializable {

	@SerializedName("images")
	private List<ImagesItem> images;

	@SerializedName("externalIdentifier")
	private Object externalIdentifier;

	@SerializedName("contentSlug")
	private String contentSlug;

	@SerializedName("mediaType")
	private String mediaType;

	@SerializedName("id")
	private int id;

	@SerializedName("title")
	private String title;

	@SerializedName("contentType")
	private String contentType;

	public List<ImagesItem> getImages(){
		return images;
	}

	public Object getExternalIdentifier(){
		return externalIdentifier;
	}

	public String getContentSlug(){
		return contentSlug;
	}

	public String getMediaType(){
		return mediaType;
	}

	public int getId(){
		return id;
	}

	public String getTitle(){
		return title;
	}

	public String getContentType(){
		return contentType;
	}
}