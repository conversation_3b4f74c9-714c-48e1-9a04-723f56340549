package com.enveu.beanModelV3.videoDetailV3.mediaContentList

import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.google.gson.annotations.SerializedName

data class ArtistListResponse(

    @field:SerializedName("data")
    val data: ArtistList? = null,

    @field:SerializedName("debugMessage")
    val debugMessage: Any? = null,

    @field:SerializedName("responseCode")
    val responseCode: Int? = null
)

data class  ArtistList(
    @field:SerializedName("items")
    val items: List<DataItem>? = null,

    @field:SerializedName("pageSize")
    val pageSize: Int? = null,

    @field:SerializedName("totalElements")
    val totalElements: Int? = null,

    @field:SerializedName("pageNumber")
    val pageNumber: Int? = null,

    @field:SerializedName("totalPages")
    val totalPages: Int? = null
)