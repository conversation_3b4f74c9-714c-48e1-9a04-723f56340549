package com.enveu.beanModelV3.videoDetailV3.mediaContentList;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class AudioContent{

	@SerializedName("chapterNo")
	private Object chapterNo;

	@SerializedName("duration")
	private int duration;

	@SerializedName("adSupported")
	private Object adSupported;

	@SerializedName("vastTag")
	private Object vastTag;

	@SerializedName("isAdSupported")
	private Object isAdSupported;

	@SerializedName("images")
	private Object images;

	@SerializedName("drmDisabled")
	private Object drmDisabled;

	@SerializedName("episodeNo")
	private Object episodeNo;

	@SerializedName("offlineEnabled")
	private boolean offlineEnabled;

	@SerializedName("audioType")
	private String audioType;

	@SerializedName("audios")
	private List<AudiosItem> audios;

	@SerializedName("seasonNo")
	private Object seasonNo;

	@SerializedName("lyrics")
	private String lyrics;

	public Object getChapterNo(){
		return chapterNo;
	}

	public int getDuration(){
		return duration;
	}

	public Object getAdSupported(){
		return adSupported;
	}

	public Object getVastTag(){
		return vastTag;
	}

	public Object getIsAdSupported(){
		return isAdSupported;
	}

	public Object getImages(){
		return images;
	}

	public Object getDrmDisabled(){
		return drmDisabled;
	}

	public Object getEpisodeNo(){
		return episodeNo;
	}

	public boolean isOfflineEnabled(){
		return offlineEnabled;
	}

	public String getAudioType(){
		return audioType;
	}

	public List<AudiosItem> getAudios(){
		return audios;
	}

	public Object getSeasonNo(){
		return seasonNo;
	}

	public String getLyrics() {
		return lyrics;
	}
}