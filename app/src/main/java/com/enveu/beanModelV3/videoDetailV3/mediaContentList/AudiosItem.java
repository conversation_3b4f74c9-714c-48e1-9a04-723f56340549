package com.enveu.beanModelV3.videoDetailV3.mediaContentList;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class AudiosItem{

	@SerializedName("langCode")
	private String langCode;

	@SerializedName("encodingRates")
	private List<Object> encodingRates;

	@SerializedName("errorMessage")
	private Object errorMessage;

	@SerializedName("language")
	private String language;

	@SerializedName("mimeType")
	private Object mimeType;

	@SerializedName("type")
	private String type;

	@SerializedName("duration")
	private Object duration;

	@SerializedName("originalSizeInBytes")
	private Object originalSizeInBytes;

	@SerializedName("default")
	private boolean jsonMemberDefault;

	@SerializedName("variant")
	private Object variant;

	@SerializedName("name")
	private String name;

	@SerializedName("externalIdentifier")
	private String externalIdentifier;

	@SerializedName("id")
	private int id;

	@SerializedName("status")
	private String status;

	public String getLangCode(){
		return langCode;
	}

	public List<Object> getEncodingRates(){
		return encodingRates;
	}

	public Object getErrorMessage(){
		return errorMessage;
	}

	public String getLanguage(){
		return language;
	}

	public Object getMimeType(){
		return mimeType;
	}

	public String getType(){
		return type;
	}

	public Object getDuration(){
		return duration;
	}

	public Object getOriginalSizeInBytes(){
		return originalSizeInBytes;
	}

	public boolean isJsonMemberDefault(){
		return jsonMemberDefault;
	}

	public Object getVariant(){
		return variant;
	}

	public String getName(){
		return name;
	}

	public String getExternalIdentifier(){
		return externalIdentifier;
	}

	public int getId(){
		return id;
	}

	public String getStatus(){
		return status;
	}
}