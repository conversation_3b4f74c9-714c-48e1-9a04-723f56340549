package com.enveu.beanModelV3.videoDetailV3.mediaContentList;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class CustomData{

	@SerializedName("albums-artist-ids")
	private List<AlbumArtistIdItem> albumArtistId;

	public List<AlbumArtistIdItem> getAlbumArtistId(){
		return albumArtistId;
	}

	@SerializedName("songs-albums-id")
	private SongsAlbumsId songsAlbumsId;

	@SerializedName("songs-artist-ids")
	private List<SongsArtistIdsItem> songsArtistIds;

	@SerializedName("RelatedInstructor")
	private String relatedInstructor;

	public SongsAlbumsId getSongsAlbumsId(){
		return songsAlbumsId;
	}

	public List<SongsArtistIdsItem> getSongsArtistIds(){
		return songsArtistIds;
	}

	public String getRelatedInstructor(){
		return relatedInstructor;
	}
}