package com.enveu.beanModelV3.videoDetailV3.mediaContentList;

import com.enveu.beanModelV3.videoDetailV3.list.DataItem;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class Data{

	@SerializedName("pageNumber")
	private int pageNumber;

	@SerializedName("totalPages")
	private int totalPages;

	@SerializedName("pageSize")
	private int pageSize;

	@SerializedName("items")
	private List<DataItem> items;

	@SerializedName("totalElements")
	private int totalElements;

	public int getPageNumber(){
		return pageNumber;
	}

	public int getTotalPages(){
		return totalPages;
	}

	public int getPageSize(){
		return pageSize;
	}

	public List<DataItem> getItems(){
		return items;
	}

	public int getTotalElements(){
		return totalElements;
	}
}