package com.enveu.beanModelV3.videoDetailV3.mediaContentList;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class ImageContent implements Serializable {

	@SerializedName("originalImageSizeInBytes")
	private int originalImageSizeInBytes;

	@SerializedName("src")
	private String src;

	@SerializedName("imageKey")
	private String imageKey;

	@SerializedName("dominantColor")
	private String dominantColor;

	@SerializedName("isDefault")
	private boolean isDefault;

	@SerializedName("showTitle")
	private boolean showTitle;

	@SerializedName("width")
	private Object width;

	@SerializedName("id")
	private Long id;

	@SerializedName("tag")
	private String tag;

	@SerializedName("colorPalette")
	private List<String> colorPalette;

	@SerializedName("imageType")
	private String imageType;

	@SerializedName("height")
	private Object height;

	@SerializedName("status")
	private String status;

	public int getOriginalImageSizeInBytes(){
		return originalImageSizeInBytes;
	}

	public String getSrc(){
		return src;
	}

	public String getImageKey(){
		return imageKey;
	}

	public String getDominantColor(){
		return dominantColor;
	}

	public boolean isIsDefault(){
		return isDefault;
	}

	public boolean isShowTitle(){
		return showTitle;
	}

	public Object getWidth(){
		return width;
	}

	public Long getId(){
		return id;
	}

	public String getTag(){
		return tag;
	}

	public List<String> getColorPalette(){
		return colorPalette;
	}

	public String getImageType(){
		return imageType;
	}

	public Object getHeight(){
		return height;
	}

	public String getStatus(){
		return status;
	}

	public void setColorPalette(List<String> colorPalette) {
		this.colorPalette = colorPalette;
	}

	public void setSrc(String src) {
		this.src = src;
	}

	public void setImageType(String imageType) {
		this.imageType = imageType;
	}

	public void setDominantColor(String dominantColor) {
		this.dominantColor = dominantColor;
	}
}