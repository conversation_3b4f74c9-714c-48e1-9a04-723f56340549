package com.enveu.beanModelV3.videoDetailV3.mediaContentList;

import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class ImagesItem implements Serializable {

	@SerializedName("originalImageSizeInBytes")
	private int originalImageSizeInBytes;

	@SerializedName("src")
	private String src;

	@SerializedName("imageKey")
	private String imageKey;

	@SerializedName("dominantColor")
	private String dominantColor;

	@SerializedName("isDefault")
	private boolean isDefault;

	@SerializedName("showTitle")
	private boolean showTitle;

	@SerializedName("width")
	private Object width;

	@SerializedName("id")
	private int id;

	@SerializedName("tag")
	private String tag;

	@SerializedName("colorPalette")
	private List<String> colorPalette;

	@SerializedName("imageType")
	private String imageType;

	@SerializedName("height")
	private Object height;

	@SerializedName("status")
	private String status;

	@SerializedName("longDescription")
	private Object longDescription;

	@SerializedName("liveContent")
	private Object liveContent;

	@SerializedName("imageContent")
	private ImageContent imageContent;

	@SerializedName("keywords")
	private List<Object> keywords;

	@SerializedName("externalRefId")
	private Object externalRefId;

	@SerializedName("accessibility")
	private Accessibility accessibility;

	@SerializedName("description")
	private String description;

	@SerializedName("contentSlug")
	private String contentSlug;

	@SerializedName("customData")
	private CustomData customData;

	@SerializedName("video")
	private Object video;

	@SerializedName("title")
	private String title;

	@SerializedName("parentalRating")
	private Object parentalRating;

	@SerializedName("organizationId")
	private Object organizationId;

	@SerializedName("lastUpdated")
	private long lastUpdated;

	@SerializedName("dateCreated")
	private long dateCreated;

	@SerializedName("premium")
	private boolean premium;

	@SerializedName("customContent")
	private Object customContent;

	@SerializedName("sku")
	private String sku;

	@SerializedName("analyticsId")
	private Object analyticsId;

	@SerializedName("contentType")
	private String contentType;

	@SerializedName("images")
	private Object images;

	@SerializedName("audioContent")
	private Object audioContent;

	@SerializedName("styleInfo")
	private Object styleInfo;

	@SerializedName("seoInfo")
	private Object seoInfo;

	@SerializedName("contentSource")
	private String contentSource;

	@SerializedName("contentReviewRating")
	private Object contentReviewRating;

	@SerializedName("targetingTags")
	private List<Object> targetingTags;

	@SerializedName("publishedDate")
	private Object publishedDate;

	@SerializedName("parentContent")
	private Object parentContent;

	@SerializedName("personContent")
	private Object personContent;




	public final static Parcelable.Creator<ImagesItem> CREATOR = new Parcelable.Creator<ImagesItem>() {


		@SuppressWarnings({
				"unchecked"
		})
		public ImagesItem createFromParcel(android.os.Parcel in) {
			return new ImagesItem(in);
		}

		public ImagesItem[] newArray(int size) {
			return (new ImagesItem[size]);
		}

	}
			;


	protected ImagesItem(android.os.Parcel in) {

		this.originalImageSizeInBytes = ((int) in.readValue((Integer.class.getClassLoader())));
		this.src = ((String) in.readValue((String.class.getClassLoader())));
		this.imageKey = ((String) in.readValue((String.class.getClassLoader())));
		this.dominantColor = ((String) in.readValue((String.class.getClassLoader())));
		this.isDefault = ((boolean) in.readValue((boolean.class.getClassLoader())));
		this.showTitle = ((boolean) in.readValue((boolean.class.getClassLoader())));
		this.width = in.readValue((Object.class.getClassLoader()));
		this.id = ((int) in.readValue((Integer.class.getClassLoader())));
		this.tag = ((String) in.readValue((String.class.getClassLoader())));
		in.readList(this.colorPalette, (String.class.getClassLoader()));
		this.imageType = ((String) in.readValue((String.class.getClassLoader())));
		this.height = in.readValue((Object.class.getClassLoader()));
		this.imageType = ((String) in.readValue((String.class.getClassLoader())));
		this.status = ((String) in.readValue((String.class.getClassLoader())));
		this.longDescription = in.readValue((Object.class.getClassLoader()));
		this.liveContent = in.readValue((Object.class.getClassLoader()));
		this.imageContent = ((ImageContent) in.readValue((ImageContent.class.getClassLoader())));
		in.readList(this.keywords, (Object.class.getClassLoader()));
		this.externalRefId = in.readValue((Object.class.getClassLoader()));
		this.accessibility = ((Accessibility) in.readValue((Accessibility.class.getClassLoader())));
		this.description = ((String) in.readValue((String.class.getClassLoader())));
		this.contentSlug = ((String) in.readValue((String.class.getClassLoader())));
		this.customData = ((CustomData) in.readValue((CustomData.class.getClassLoader())));
		this.video = in.readValue((Object.class.getClassLoader()));
		this.title = ((String) in.readValue((String.class.getClassLoader())));
		this.parentalRating = in.readValue((Object.class.getClassLoader()));
		this.organizationId = in.readValue((Object.class.getClassLoader()));
		this.lastUpdated = ((long) in.readValue((long.class.getClassLoader())));
		this.dateCreated = ((long) in.readValue((long.class.getClassLoader())));
		this.premium = ((boolean) in.readValue((boolean.class.getClassLoader())));
		this.customContent = in.readValue((Object.class.getClassLoader()));
		this.sku = ((String) in.readValue((String.class.getClassLoader())));
		this.analyticsId = in.readValue((Object.class.getClassLoader()));
		this.contentType = ((String) in.readValue((String.class.getClassLoader())));
		this.images = in.readValue((Object.class.getClassLoader()));
		this.audioContent = in.readValue((Object.class.getClassLoader()));
		this.styleInfo = in.readValue((Object.class.getClassLoader()));
		this.seoInfo = in.readValue((Object.class.getClassLoader()));
		this.contentSource = ((String) in.readValue((String.class.getClassLoader())));
		this.contentReviewRating = in.readValue((Object.class.getClassLoader()));
		in.readList(this.targetingTags, (Object.class.getClassLoader()));
		this.publishedDate = in.readValue((Object.class.getClassLoader()));
		this.parentContent = in.readValue((Object.class.getClassLoader()));
		this.personContent = in.readValue((Object.class.getClassLoader()));
	}


	public void writeToParcel(android.os.Parcel dest) {
		dest.writeValue(originalImageSizeInBytes);
		dest.writeValue(src);
		dest.writeValue(imageKey);
		dest.writeValue(dominantColor);
		dest.writeValue(isDefault);
		dest.writeValue(showTitle);
		dest.writeValue(width);
		dest.writeValue(id);
		dest.writeValue(tag);
		dest.writeList(colorPalette);
		dest.writeList(keywords);
		dest.writeList(targetingTags);
		dest.writeValue(imageType);
		dest.writeValue(height);
		dest.writeValue(status);
		dest.writeValue(longDescription);
		dest.writeValue(liveContent);
		dest.writeValue(imageContent);
		dest.writeValue(externalRefId);
		dest.writeValue(accessibility);
		dest.writeValue(description);
		dest.writeValue(contentSlug);
		dest.writeValue(customData);
		dest.writeValue(video);
		dest.writeValue(title);
		dest.writeValue(parentalRating);
		dest.writeValue(organizationId);
		dest.writeValue(lastUpdated);
		dest.writeValue(dateCreated);
		dest.writeValue(premium);
		dest.writeValue(customContent);
		dest.writeValue(sku);
		dest.writeValue(analyticsId);
		dest.writeValue(contentType);
		dest.writeValue(images);
		dest.writeValue(audioContent);
		dest.writeValue(styleInfo);
		dest.writeValue(seoInfo);
		dest.writeValue(contentSource);
		dest.writeValue(contentReviewRating);
		dest.writeValue(publishedDate);
		dest.writeValue(parentContent);
		dest.writeValue(personContent);
	}

	public int getOriginalImageSizeInBytes(){
		return originalImageSizeInBytes;
	}

	public String getSrc(){
		return src;
	}

	public String getImageKey(){
		return imageKey;
	}

	public String getDominantColor(){
		return dominantColor;
	}

	public boolean isIsDefault(){
		return isDefault;
	}

	public boolean isShowTitle(){
		return showTitle;
	}

	public Object getWidth(){
		return width;
	}

	public int getId(){
		return id;
	}

	public String getTag(){
		return tag;
	}

	public List<String> getColorPalette(){
		return colorPalette;
	}

	public String getImageType(){
		return imageType;
	}

	public Object getHeight(){
		return height;
	}

	public String getStatus(){
		return status;
	}

	public Object getLongDescription(){
		return longDescription;
	}

	public Object getLiveContent(){
		return liveContent;
	}

	public ImageContent getImageContent(){
		return imageContent;
	}

	public List<Object> getKeywords(){
		return keywords;
	}

	public Object getExternalRefId(){
		return externalRefId;
	}

	public Accessibility getAccessibility(){
		return accessibility;
	}

	public String getDescription(){
		return description;
	}

	public String getContentSlug(){
		return contentSlug;
	}

	public CustomData getCustomData(){
		return customData;
	}

	public Object getVideo(){
		return video;
	}

	public String getTitle(){
		return title;
	}

	public Object getParentalRating(){
		return parentalRating;
	}

	public Object getOrganizationId(){
		return organizationId;
	}

	public long getLastUpdated(){
		return lastUpdated;
	}

	public long getDateCreated(){
		return dateCreated;
	}

	public boolean isPremium(){
		return premium;
	}

	public Object getCustomContent(){
		return customContent;
	}

	public String getSku(){
		return sku;
	}

	public Object getAnalyticsId(){
		return analyticsId;
	}

	public String getContentType(){
		return contentType;
	}

	public Object getImages(){
		return images;
	}

	public Object getAudioContent(){
		return audioContent;
	}

	public Object getStyleInfo(){
		return styleInfo;
	}

	public Object getSeoInfo(){
		return seoInfo;
	}

	public String getContentSource(){
		return contentSource;
	}

	public Object getContentReviewRating(){
		return contentReviewRating;
	}

	public List<Object> getTargetingTags(){
		return targetingTags;
	}

	public Object getPublishedDate(){
		return publishedDate;
	}

	public Object getParentContent(){
		return parentContent;
	}

	public Object getPersonContent(){
		return personContent;
	}
}