package com.enveu.beanModelV3.videoDetailV3.mediaContentList;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class ItemsItem{

	@SerializedName("longDescription")
	private String longDescription;

	@SerializedName("liveContent")
	private Object liveContent;

	@SerializedName("imageContent")
	private Object imageContent;

	@SerializedName("keywords")
	private List<Object> keywords;

	@SerializedName("externalRefId")
	private String externalRefId;

	@SerializedName("accessibility")
	private Accessibility accessibility;

	@SerializedName("description")
	private String description;

	@SerializedName("contentSlug")
	private String contentSlug;

	@SerializedName("customData")
	private CustomData customData;

	@SerializedName("video")
	private Object video;

	@SerializedName("title")
	private String title;

	@SerializedName("parentalRating")
	private Object parentalRating;

	@SerializedName("organizationId")
	private Object organizationId;

	@SerializedName("lastUpdated")
	private long lastUpdated;

	@SerializedName("dateCreated")
	private long dateCreated;

	@SerializedName("premium")
	private boolean premium;

	@SerializedName("customContent")
	private Object customContent;

	@SerializedName("id")
	private int id;

	@SerializedName("sku")
	private String sku;

	@SerializedName("analyticsId")
	private Object analyticsId;

	@SerializedName("contentType")
	private String contentType;

	@SerializedName("images")
	private List<ImagesItem> images;

	@SerializedName("audioContent")
	private AudioContent audioContent;

	@SerializedName("styleInfo")
	private Object styleInfo;

	@SerializedName("seoInfo")
	private SeoInfo seoInfo;

	@SerializedName("contentSource")
	private String contentSource;

	@SerializedName("contentReviewRating")
	private Object contentReviewRating;

	@SerializedName("targetingTags")
	private List<Object> targetingTags;

	@SerializedName("publishedDate")
	private long publishedDate;

	@SerializedName("parentContent")
	private Object parentContent;

	@SerializedName("personContent")
	private Object personContent;

	public String getLongDescription(){
		return longDescription;
	}

	public Object getLiveContent(){
		return liveContent;
	}

	public Object getImageContent(){
		return imageContent;
	}

	public List<Object> getKeywords(){
		return keywords;
	}

	public String getExternalRefId(){
		return externalRefId;
	}

	public Accessibility getAccessibility(){
		return accessibility;
	}

	public String getDescription(){
		return description;
	}

	public String getContentSlug(){
		return contentSlug;
	}

	public CustomData getCustomData(){
		return customData;
	}

	public Object getVideo(){
		return video;
	}

	public String getTitle(){
		return title;
	}

	public Object getParentalRating(){
		return parentalRating;
	}

	public Object getOrganizationId(){
		return organizationId;
	}

	public long getLastUpdated(){
		return lastUpdated;
	}

	public long getDateCreated(){
		return dateCreated;
	}

	public boolean isPremium(){
		return premium;
	}

	public Object getCustomContent(){
		return customContent;
	}

	public int getId(){
		return id;
	}

	public String getSku(){
		return sku;
	}

	public Object getAnalyticsId(){
		return analyticsId;
	}

	public String getContentType(){
		return contentType;
	}

	public List<ImagesItem> getImages(){
		return images;
	}

	public AudioContent getAudioContent(){
		return audioContent;
	}

	public Object getStyleInfo(){
		return styleInfo;
	}

	public SeoInfo getSeoInfo(){
		return seoInfo;
	}

	public String getContentSource(){
		return contentSource;
	}

	public Object getContentReviewRating(){
		return contentReviewRating;
	}

	public List<Object> getTargetingTags(){
		return targetingTags;
	}

	public long getPublishedDate(){
		return publishedDate;
	}

	public Object getParentContent(){
		return parentContent;
	}

	public Object getPersonContent(){
		return personContent;
	}
}