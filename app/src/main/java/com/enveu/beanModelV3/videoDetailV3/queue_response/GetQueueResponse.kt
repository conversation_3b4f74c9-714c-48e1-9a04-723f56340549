package com.enveu.beanModelV3.videoDetailV3.queue_response

import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.google.gson.annotations.SerializedName

data class GetQueueResponse(

	@field:SerializedName("data")
	val data: GetQueueData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	val responseCode: Int? = null
)

data class ImagesItems(

	@field:SerializedName("originalImageSizeInBytes")
	val originalImageSizeInBytes: Any? = null,

	@field:SerializedName("src")
	val src: String? = null,

	@field:SerializedName("imageKey")
	val imageKey: String? = null,

	@field:SerializedName("dominantColor")
	val dominantColor: Any? = null,

	@field:SerializedName("isDefault")
	val isDefault: Boolean? = null,

	@field:SerializedName("showTitle")
	val showTitle: Boolean? = null,

	@field:SerializedName("width")
	val width: Any? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("tag")
	val tag: String? = null,

	@field:SerializedName("colorPalette")
	val colorPalette: List<Any?>? = null,

	@field:SerializedName("imageType")
	val imageType: String? = null,

	@field:SerializedName("height")
	val height: Any? = null,

	@field:SerializedName("status")
	val status: String? = null
)

data class GetQueueData(

	@field:SerializedName("owner")
	val owner: Owner? = null,

	@field:SerializedName("images")
	val images: Any? = null,

	@field:SerializedName("subscribedUsers")
	val subscribedUsers: Any? = null,

	@field:SerializedName("orderedContents")
	val orderedContents: List<OrderedContentsItems?>? = null,

	@field:SerializedName("description")
	val description: String? = null,

	@field:SerializedName("playlistSlug")
	val playlistSlug: String? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("type")
	val type: String? = null,

	@field:SerializedName("lastUpdated")
	val lastUpdated: Long? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Long? = null,

	@field:SerializedName("subscriberCount")
	val subscriberCount: Int? = null,

	@field:SerializedName("subscribedByRequestedUser")
	val subscribedByRequestedUser: Boolean? = null,

	@field:SerializedName("collaborators")
	val collaborators: List<Any?>? = null,

	@field:SerializedName("id")
	val id: String? = null,

	@field:SerializedName("contentCount")
	val contentCount: Int? = null,

	@field:SerializedName("status")
	val status: String? = null
)

data class GenresItems(

	@field:SerializedName("images")
	val images: List<Any?>? = null,

	@field:SerializedName("externalIdentifier")
	val externalIdentifier: String? = null,

	@field:SerializedName("contentSlug")
	val contentSlug: Any? = null,

	@field:SerializedName("description")
	val description: Any? = null,

	@field:SerializedName("mediaType")
	val mediaType: String? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("contentType")
	val contentType: String? = null
)

data class OrderedContentsItems(

	@field:SerializedName("removed")
	val removed: Boolean? = null,

	@field:SerializedName("id")
	val id: String? = null,

	@field:SerializedName("content")
	val content: DataItem? = null,

	@field:SerializedName("order")
	val order: Int? = null
)

data class Accessibility(

	@field:SerializedName("accessibilitySchedule")
	val accessibilitySchedule: Any? = null,

	@field:SerializedName("checkAccessibility")
	val checkAccessibility: Boolean? = null
)

data class SeoMetaInfo(
	val any: Any? = null
)

data class AudioContent(

	@field:SerializedName("externalUrl")
	val externalUrl: Any? = null,

	@field:SerializedName("vastTag")
	val vastTag: Any? = null,

	@field:SerializedName("images")
	val images: Any? = null,

	@field:SerializedName("episodeNo")
	val episodeNo: Any? = null,

	@field:SerializedName("audioType")
	val audioType: String? = null,

	@field:SerializedName("textTracks")
	val textTracks: Any? = null,

	@field:SerializedName("seasonNo")
	val seasonNo: Any? = null,

	@field:SerializedName("chapterNo")
	val chapterNo: Any? = null,

	@field:SerializedName("duration")
	val duration: Any? = null,

	@field:SerializedName("adSupported")
	val adSupported: Any? = null,

	@field:SerializedName("isAdSupported")
	val isAdSupported: Any? = null,

	@field:SerializedName("drmDisabled")
	val drmDisabled: Any? = null,

	@field:SerializedName("offlineEnabled")
	val offlineEnabled: Any? = null,

	@field:SerializedName("audios")
	val audios: Any? = null,

	@field:SerializedName("hostingSource")
	val hostingSource: String? = null,

	@field:SerializedName("lyrics")
	val lyrics: Any? = null
)

data class Owner(

	@field:SerializedName("profilePicURL")
	val profilePicURL: Any? = null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("id")
	val id: Int? = null
)

data class SeoInfo(

	@field:SerializedName("description")
	val description: String? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("seoMetaInfo")
	val seoMetaInfo: SeoMetaInfo? = null,

	@field:SerializedName("tags")
	val tags: List<Any?>? = null
)

data class SongsAlbumsId(

	@field:SerializedName("images")
	val images: List<ImagesItems?>? = null,

	@field:SerializedName("externalIdentifier")
	val externalIdentifier: String? = null,

	@field:SerializedName("contentSlug")
	val contentSlug: String? = null,

	@field:SerializedName("description")
	val description: String? = null,

	@field:SerializedName("mediaType")
	val mediaType: String? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("contentType")
	val contentType: String? = null
)

data class SongsArtistIdsItems(

	@field:SerializedName("images")
	val images: List<ImagesItems?>? = null,

	@field:SerializedName("externalIdentifier")
	val externalIdentifier: String? = null,

	@field:SerializedName("contentSlug")
	val contentSlug: String? = null,

	@field:SerializedName("description")
	val description: String? = null,

	@field:SerializedName("mediaType")
	val mediaType: String? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("contentType")
	val contentType: String? = null
)

data class Content(

	@field:SerializedName("longDescription")
	val longDescription: Any? = null,

	@field:SerializedName("liveContent")
	val liveContent: Any? = null,

	@field:SerializedName("imageContent")
	val imageContent: Any? = null,

	@field:SerializedName("keywords")
	val keywords: List<Any?>? = null,

	@field:SerializedName("externalRefId")
	val externalRefId: Any? = null,

	@field:SerializedName("accessibility")
	val accessibility: Accessibility? = null,

	@field:SerializedName("description")
	val description: Any? = null,

	@field:SerializedName("contentSlug")
	val contentSlug: String? = null,

	@field:SerializedName("customData")
	val customData: CustomData? = null,

	@field:SerializedName("video")
	val video: Any? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("parentalRating")
	val parentalRating: Any? = null,

	@field:SerializedName("organizationId")
	val organizationId: Any? = null,

	@field:SerializedName("lastUpdated")
	val lastUpdated: Long? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Long? = null,

	@field:SerializedName("premium")
	val premium: Boolean? = null,

	@field:SerializedName("customContent")
	val customContent: Any? = null,

	@field:SerializedName("articleContent")
	val articleContent: Any? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("sku")
	val sku: String? = null,

	@field:SerializedName("analyticsId")
	val analyticsId: Any? = null,

	@field:SerializedName("contentType")
	val contentType: String? = null,

	@field:SerializedName("images")
	val images: Any? = null,

	@field:SerializedName("audioContent")
	val audioContent: AudioContent? = null,

	@field:SerializedName("styleInfo")
	val styleInfo: Any? = null,

	@field:SerializedName("seoInfo")
	val seoInfo: SeoInfo? = null,

	@field:SerializedName("playCount")
	val playCount: Int? = null,

	@field:SerializedName("contentSource")
	val contentSource: String? = null,

	@field:SerializedName("contentReviewRating")
	val contentReviewRating: Any? = null,

	@field:SerializedName("targetingTags")
	val targetingTags: List<Any?>? = null,

	@field:SerializedName("publishedDate")
	val publishedDate: Long? = null,

	@field:SerializedName("parentContent")
	val parentContent: Any? = null,

	@field:SerializedName("personContent")
	val personContent: Any? = null
)

data class CustomData(

	@field:SerializedName("discNumber")
	val discNumber: String? = null,

	@field:SerializedName("streamingAvailability")
	val streamingAvailability: String? = null,

	@field:SerializedName("trackOrder")
	val trackOrder: String? = null,

	@SerializedName("lyrics")
	private var lyrics: String? = null,

	@field:SerializedName("trackNumber")
	val trackNumber: String? = null,

	@field:SerializedName("genres")
	val genres: List<GenresItems?>? = null,

	@field:SerializedName("songs-albums-id")
	val songsAlbumsId: SongsAlbumsId? = null,

	@field:SerializedName("songs-artist-ids")
	val songsArtistIds: List<SongsArtistIdsItems?>? = null,

	@field:SerializedName("pLine")
	val pLine: String? = null,

	@field:SerializedName("trackIsrc")
	val trackIsrc: String? = null
)
