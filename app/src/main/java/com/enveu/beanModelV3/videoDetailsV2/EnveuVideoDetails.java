
package com.enveu.beanModelV3.videoDetailsV2;

import com.enveu.beanModelV3.playListModelV2.AudioContent;
import com.enveu.beanModelV3.playListModelV2.CustomContentData;
import com.enveu.beanModelV3.playListModelV2.PersonContent;
import com.enveu.beanModelV3.videoDetailV3.CustomData;
import com.enveu.beanModelV3.videoDetailV3.list.ImagesItem;
import com.enveu.client.playlist.beanv2_0.LiveContent;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class EnveuVideoDetails {

    @SerializedName("dateCreated")
    @Expose
    private long dateCreated;
    @SerializedName("lastUpdated")
    @Expose
    private long lastUpdated;
    @SerializedName("contentSlug")
    @Expose
    private String contentSlug;
    @SerializedName("contentSource")
    @Expose
    private String contentSource;
    @SerializedName("id")
    @Expose
    private Integer id;
    @SerializedName("adKeys")
    @Expose
    private Object adKeys;
    @SerializedName("cuePoints")
    @Expose
    private Object cuePoints;
    @SerializedName("customFields")
    @Expose
    private Object customFields;

    @SerializedName("customData")
    @Expose
    private CustomData customData;
    @SerializedName("deliveryType")
    @Expose
    private String deliveryType;
    @SerializedName("description")
    @Expose
    private String description;

    @SerializedName("longDescription")
    @Expose
    private String longDescription;
    @SerializedName("duration")
    @Expose
    private int duration;
    @SerializedName("economics")
    @Expose
    private String economics;
    @SerializedName("brightcoveContentId")
    @Expose
    private String brightcoveContentId;
    @SerializedName("images")
    @Expose
    private List<ImagesItem> images;
    @SerializedName("title")
    @Expose
    private String title;
    @SerializedName("status")
    @Expose
    private String status;
    @SerializedName("keywords")
    @Expose
    private List<Object> keywords;
    @SerializedName("audioContent")
    @Expose
    private AudioContent audioContent;
    @SerializedName("textTracks")
    @Expose
    private List<Object> textTracks;
    @SerializedName("audioTracks")
    @Expose
    private Object audioTracks;
    @SerializedName("contentType")
    @Expose
    private String contentType;
    @SerializedName("genres")
    @Expose
    private List<String> genres;
    @SerializedName("cast")
    @Expose
    private List<String> cast;
    @SerializedName("sku")
    @Expose
    private String sku;
    @SerializedName("seasonNumber")
    @Expose
    private Object seasonNumber;
    @SerializedName("episodeNumber")
    @Expose
    private Object episodeNumber;
    @SerializedName("publishedDate")
    @Expose
    private long publishedDate;
    @SerializedName("seasons")
    @Expose
    private List<Integer> seasons = null;
    @SerializedName("contentMonetization")
    @Expose
    private Object contentMonetization;
    @SerializedName("linkedContent")
    @Expose
    private LinkedContent linkedContent;
    @SerializedName("contentProvider")
    @Expose
    private String contentProvider;
    @SerializedName("complete")
    @Expose
    private Object complete;
    @SerializedName("drmdisabled")
    @Expose
    private Boolean drmdisabled;
    @SerializedName("premium")
    @Expose
    private Boolean premium;
    @SerializedName("offlineEnabled")
    @Expose
    private Boolean offlineEnabled;

    @SerializedName("video")
    @Expose
    private VideoDetails video;

    @SerializedName("liveContent")
    @Expose
    private LiveContent liveContent;

    @SerializedName("personContent")
    @Expose
    private PersonContent personContent;


    @SerializedName("customContent")
    @Expose
    private CustomContentData customContent;

    @SerializedName("playCount")
    @Expose
    private int playCount;

    @SerializedName("externalRefId")
    @Expose
    private String externalRefId;

    public CustomContentData getCustomContent() {
        return customContent;
    }

    public void setCustomContent(CustomContentData customContent) {
        this.customContent = customContent;
    }

    public PersonContent getPersonContent() {
        return personContent;
    }

    public VideoDetails getVideo() {
        return video;
    }

    public void setVideo(VideoDetails video) {
        this.video = video;
    }

    public String getExternalRefId() {
        return externalRefId;
    }

    public void setExternalRefId(String externalRefId) {
        this.externalRefId = externalRefId;
    }



    public long getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(long dateCreated) {
        this.dateCreated = dateCreated;
    }

    public long getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(long lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public String getContentSource() {
        return contentSource;
    }

    public void setContentSource(String contentSource) {
        this.contentSource = contentSource;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Object getAdKeys() {
        return adKeys;
    }

    public void setAdKeys(Object adKeys) {
        this.adKeys = adKeys;
    }

    public Object getCuePoints() {
        return cuePoints;
    }

    public void setCuePoints(Object cuePoints) {
        this.cuePoints = cuePoints;
    }

    public Object getCustomFields() {
        return customFields;
    }

    public void setCustomFields(Object customFields) {
        this.customFields = customFields;
    }


    public CustomData getCustomData() {
        return customData;
    }

    public void setCustomData(CustomData customData) {
        this.customData = customData;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public String getEconomics() {
        return economics;
    }

    public void setEconomics(String economics) {
        this.economics = economics;
    }

    public String getBrightcoveContentId() {
        return brightcoveContentId;
    }

    public void setBrightcoveContentId(String brightcoveContentId) {
        this.brightcoveContentId = brightcoveContentId;
    }

    public List<ImagesItem> getImages() {
        return images;
    }

    public void setImages(List<ImagesItem> images) {
        this.images = images;
    }

    public String getLongDescription() {
        return longDescription;
    }

    public void setLongDescription(String longDescription) {
        this.longDescription = longDescription;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<Object> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<Object> keywords) {
        this.keywords = keywords;
    }

    public List<Object> getTextTracks() {
        return textTracks;
    }

    public void setTextTracks(List<Object> textTracks) {
        this.textTracks = textTracks;
    }

    public Object getAudioTracks() {
        return audioTracks;
    }

    public void setAudioTracks(Object audioTracks) {
        this.audioTracks = audioTracks;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public List<String> getGenres() {
        return genres;
    }

    public void setGenres(List<String> genres) {
        this.genres = genres;
    }

    public List<String> getCast() {
        return cast;
    }

    public void setCast(List<String> cast) {
        this.cast = cast;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Object getSeasonNumber() {
        return seasonNumber;
    }

    public void setSeasonNumber(Object seasonNumber) {
        this.seasonNumber = seasonNumber;
    }

    public Object getEpisodeNumber() {
        return episodeNumber;
    }

    public void setEpisodeNumber(Object episodeNumber) {
        this.episodeNumber = episodeNumber;
    }

    public long getPublishedDate() {
        return publishedDate;
    }

    public void setPublishedDate(long publishedDate) {
        this.publishedDate = publishedDate;
    }

    public List<Integer> getSeasons() {
        return seasons;
    }

    public void setSeasons(List<Integer> seasons) {
        this.seasons = seasons;
    }

    public Object getContentMonetization() {
        return contentMonetization;
    }

    public void setContentMonetization(Object contentMonetization) {
        this.contentMonetization = contentMonetization;
    }

    public LinkedContent getLinkedContent() {
        return linkedContent;
    }

    public void setLinkedContent(LinkedContent linkedContent) {
        this.linkedContent = linkedContent;
    }

    public String getContentProvider() {
        return contentProvider;
    }

    public void setContentProvider(String contentProvider) {
        this.contentProvider = contentProvider;
    }

    public Object getComplete() {
        return complete;
    }

    public void setComplete(Object complete) {
        this.complete = complete;
    }

    public Boolean getDrmdisabled() {
        return drmdisabled;
    }

    public void setDrmdisabled(Boolean drmdisabled) {
        this.drmdisabled = drmdisabled;
    }

    public Boolean getPremium() {
        return premium;
    }

    public void setPremium(Boolean premium) {
        this.premium = premium;
    }

    public Boolean getOfflineEnabled() {
        return offlineEnabled;
    }

    public void setOfflineEnabled(Boolean offlineEnabled) {
        this.offlineEnabled = offlineEnabled;
    }

    public LiveContent getLiveContent() {
        return liveContent;
    }

    public AudioContent getAudioContent(){
        return audioContent;
    }

    public void setAudioContent(AudioContent audioContent) {
        this.audioContent = audioContent;
    }

    public void setLiveContent(LiveContent liveContent) {
        this.liveContent = liveContent;
    }

    public String getContentSlug() {
        return contentSlug;
    }

    public void setContentSlug(String contentSlug) {
        this.contentSlug = contentSlug;
    }

    public void setPersonContent(PersonContent personContent) {
        this.personContent = personContent;
    }

    public int getPlayCount() {
        return playCount;
    }

    public void setPlayCount(int playCount) {
        this.playCount = playCount;
    }
}
