package com.enveu.bean_model_v2_0.MusicPlaylist


import com.google.gson.annotations.SerializedName

data class AddSongPlaylist(
    @SerializedName("data")
    val data: List<Data>?,
    @SerializedName("debugMessage")
    val debugMessage: String?, // string
    @SerializedName("responseCode")
    val responseCode: Int? // 2000
) {
    data class Data(
        @SerializedName("contentCount")
        val contentCount: Int?, // 1
        @SerializedName("description")
        val description: String?, // Test Playlist Description
        @SerializedName("id")
        val id: String?, // 6655c8643f0ee40f0305a458
        @SerializedName("images")
        val images: List<Any?>?,
        @SerializedName("orderedContents")
        val orderedContents: List<Any?>?,
        @SerializedName("owner")
        val owner: Owner?,
        @SerializedName("playlistSlug")
        val playlistSlug: String?, // test-playlist
        @SerializedName("status")
        val status: String?, // PUBLIC
        @SerializedName("subscribedUsers")
        val subscribedUsers: List<Any?>?,
        @SerializedName("title")
        val title: String?, // Test Playlist
        @SerializedName("type")
        val type: String? // PUBLIC
    ) {
        class Owner
    }
}