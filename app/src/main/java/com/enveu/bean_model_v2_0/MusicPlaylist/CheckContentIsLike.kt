package com.enveu.bean_model_v2_0.MusicPlaylist


import com.google.gson.annotations.SerializedName

data class CheckContentIsLike(
    @SerializedName("data")
    val `data`: List<Data?>?,
    @SerializedName("debugMessage")
    val debugMessage: Any?, // null
    @SerializedName("responseCode")
    val responseCode: Int? // 2000
) {
    data class Data(
        @SerializedName("contentId")
        val contentId: String?, // 390
        @SerializedName("present")
        val present: Boolean? // false
    )
}