package com.enveu.bean_model_v2_0.MusicPlaylist


import com.google.gson.annotations.SerializedName

data class CreatePlaylist(
    @SerializedName("description")
    val description: String?, // Test Playlist Description
    @SerializedName("images")
    val images: List<Image?>?,
    @SerializedName("orderedContents")
    val orderedContents: List<OrderedContent?>?,
    @SerializedName("title")
    val title: String? // Test Playlist
) {
    data class Image(
        @SerializedName("colorPalette")
        val colorPalette: List<String?>?,
        @SerializedName("dominantColor")
        val dominantColor: String?, // string
        @SerializedName("height")
        val height: Int?, // 0
        @SerializedName("id")
        val id: Int?, // 0
        @SerializedName("imageKey")
        val imageKey: String?, // string
        @SerializedName("isDefault")
        val isDefault: Boolean?, // true
        @SerializedName("src")
        val src: String?, // string
        @SerializedName("tag")
        val tag: String?, // string
        @SerializedName("width")
        val width: Int? // 0
    )

    data class OrderedContent(
        @SerializedName("content")
        val content: Content?,
        @SerializedName("order")
        val order: Int? // 1
    ) {
        data class Content(
            @SerializedName("id")
            val id: Int? // 1
        )
    }
}