package com.enveu.bean_model_v2_0.MusicPlaylist


import com.google.gson.annotations.SerializedName

data class MyPlaylistData(
    @SerializedName("data")
    val `data`: Data?,
    @SerializedName("debugMessage")
    val debugMessage: Any?, // null
    @SerializedName("responseCode")
    val responseCode: Int? // 2000
) {
    data class Data(
        @SerializedName("items")
        val items: ArrayList<Item?>?,
        @SerializedName("pageNumber")
        val pageNumber: Int?, // 0
        @SerializedName("pageSize")
        val pageSize: Int?, // 10
        @SerializedName("totalElements")
        val totalElements: Int?, // 1
        @SerializedName("totalPages")
        val totalPages: Int? // 1
    ) {
        data class Item(
            @SerializedName("contentCount")
            val contentCount: Int?, // null
            @SerializedName("dateCreated")
            val dateCreated: Long?, // 1720772741587
            @SerializedName("description")
            val description: String?, // null
            @SerializedName("id")
            val id: String?, // 6690e885d407c7646bcf78dd
            @SerializedName("images")
            val images: Any?, // null
            @SerializedName("lastUpdated")
            val lastUpdated: Long?, // 1720772741587
            @SerializedName("orderedContents")
            val orderedContents: Any?, // null
            @SerializedName("owner")
            val owner: Any?, // null
            @SerializedName("playlistSlug")
            val playlistSlug: String?, // test-mrfj
            @SerializedName("status")
            val status: String?, // PRIVATE
            @SerializedName("subscribedUsers")
            val subscribedUsers: Any?, // null
            @SerializedName("title")
            val title: String?, // test
            @SerializedName("type")
            val type: String? // USER_DEFINED
        )
    }
}