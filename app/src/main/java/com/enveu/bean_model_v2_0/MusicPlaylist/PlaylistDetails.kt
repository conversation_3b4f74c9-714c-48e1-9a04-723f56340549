package com.enveu.bean_model_v2_0.MusicPlaylist


import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.google.gson.annotations.SerializedName

data class PlaylistDetails(
    @SerializedName("data")
    val `data`: Data?,
    @SerializedName("debugMessage")
    val debugMessage: Any?, // null
    @SerializedName("responseCode")
    val responseCode: Int?, // 2000
) {
    data class Data(
        @SerializedName("contentCount")
        val contentCount: Int?, // 2
        @SerializedName("dateCreated")
        val dateCreated: Long?, // 1719905287415
        @SerializedName("description")
        val description: String?, // My Liked Playlist
        @SerializedName("id")
        val id: String?, // 6683ac07fa4f8c0010d89e2f
        @SerializedName("images")
        val images: Any?, // null
        @SerializedName("lastUpdated")
        val lastUpdated: Long?, // 1719905287415
        @SerializedName("orderedContents")
        val orderedContents: MutableList<OrderedContent?>?,
        @SerializedName("owner")
        val owner: Any?, // null
        @SerializedName("playlistSlug")
        val playlistSlug: String?, // d853785b-875d-43a7-af94-9817268e7581-liked
        @SerializedName("status")
        val status: String?, // PRIVATE
        @SerializedName("subscribedUsers")
        val subscribedUsers: Any?, // null
        @SerializedName("title")
        val title: String?, // Liked Playlist
        @SerializedName("type")
        val type: String?, // LIKED
    ) {
        data class OrderedContent(
            @SerializedName("content")
            val content: DataItem?,
            @SerializedName("id")
            val id: String?, // 6694ea6535e954525cdef343
            @SerializedName("order")
            val order: Int?, // 0
        ) {
            data class Content(
                @SerializedName("accessibility")
                val accessibility: Accessibility?,
                @SerializedName("analyticsId")
                val analyticsId: Any?, // null
                @SerializedName("articleContent")
                val articleContent: Any?, // null
                @SerializedName("audioContent")
                val audioContent: AudioContent?,
                @SerializedName("contentReviewRating")
                val contentReviewRating: Any?, // null
                @SerializedName("contentSlug")
                val contentSlug: String?, // lied-voor-jezus
                @SerializedName("contentSource")
                val contentSource: String?, // ENVEU_OVP
                @SerializedName("contentType")
                val contentType: String?, // AUDIO
                @SerializedName("customContent")
                val customContent: Any?, // null
                @SerializedName("customData")
                val customData: CustomData?,
                @SerializedName("dateCreated")
                val dateCreated: Long?, // 1719989757106
                @SerializedName("description")
                val description: String?,
                @SerializedName("externalRefId")
                val externalRefId: String?, // gQGYxR5t
                @SerializedName("id")
                val id: Int?, // 335
                @SerializedName("imageContent")
                val imageContent: Any?, // null
                @SerializedName("images")
                val images: Any?, // null
                @SerializedName("keywords")
                val keywords: List<Any?>?,
                @SerializedName("lastUpdated")
                val lastUpdated: Long?, // 1720248562011
                @SerializedName("liveContent")
                val liveContent: Any?, // null
                @SerializedName("longDescription")
                val longDescription: Any?, // null
                @SerializedName("organizationId")
                val organizationId: Int?, // 1
                @SerializedName("parentContent")
                val parentContent: Any?, // null
                @SerializedName("parentalRating")
                val parentalRating: Any?, // null
                @SerializedName("personContent")
                val personContent: Any?, // null
                @SerializedName("premium")
                val premium: Boolean?, // false
                @SerializedName("publishedDate")
                val publishedDate: Long?, // 1720248562010
                @SerializedName("seoInfo")
                val seoInfo: Any?, // null
                @SerializedName("sku")
                val sku: String?, // MEDIA_390b60eb-4db0-4897-b802-83c417fd59ba
                @SerializedName("styleInfo")
                val styleInfo: Any?, // null
                @SerializedName("targetingTags")
                val targetingTags: List<Any?>?,
                @SerializedName("title")
                val title: String?, // Lied Voor Jezus
                @SerializedName("video")
                val video: Any?, // null
            ) {
                data class Accessibility(
                    @SerializedName("accessibilitySchedule")
                    val accessibilitySchedule: Any?, // null
                    @SerializedName("checkAccessibility")
                    val checkAccessibility: Boolean?, // false
                )

                data class AudioContent(
                    @SerializedName("adSupported")
                    val adSupported: Any?, // null
                    @SerializedName("audioType")
                    val audioType: String?, // SONGS
                    @SerializedName("audios")
                    val audios: List<Audio?>?,
                    @SerializedName("chapterNo")
                    val chapterNo: Any?, // null
                    @SerializedName("drmDisabled")
                    val drmDisabled: Any?, // null
                    @SerializedName("duration")
                    val duration: Int?, // 303805
                    @SerializedName("episodeNo")
                    val episodeNo: Any?, // null
                    @SerializedName("externalUrl")
                    val externalUrl: Any?, // null
                    @SerializedName("hostingSource")
                    val hostingSource: Any?, // null
                    @SerializedName("images")
                    val images: Any?, // null
                    @SerializedName("isAdSupported")
                    val isAdSupported: Any?, // null
                    @SerializedName("lyrics")
                    val lyrics: String?, // null
                    @SerializedName("offlineEnabled")
                    val offlineEnabled: Boolean?, // false
                    @SerializedName("seasonNo")
                    val seasonNo: Any?, // null
                    @SerializedName("vastTag")
                    val vastTag: Any?, // null
                ) {
                    data class Audio(
                        @SerializedName("default")
                        val default: Boolean?, // true
                        @SerializedName("duration")
                        val duration: Any?, // null
                        @SerializedName("encodingRates")
                        val encodingRates: List<Any?>?,
                        @SerializedName("errorMessage")
                        val errorMessage: Any?, // null
                        @SerializedName("externalIdentifier")
                        val externalIdentifier: String?, // lWx0AtCC
                        @SerializedName("id")
                        val id: Int?, // 64
                        @SerializedName("langCode")
                        val langCode: String?, // en
                        @SerializedName("language")
                        val language: String?, // English
                        @SerializedName("mimeType")
                        val mimeType: Any?, // null
                        @SerializedName("name")
                        val name: String?, // English
                        @SerializedName("originalSizeInBytes")
                        val originalSizeInBytes: Any?, // null
                        @SerializedName("status")
                        val status: String?, // READY
                        @SerializedName("type")
                        val type: String?, // primary
                        @SerializedName("variant")
                        val variant: Any?, // null
                    )
                }

                data class CustomData(
                    @SerializedName("arranger")
                    val arranger: List<Any?>?,
                    @SerializedName("composer")
                    val composer: List<Composer?>?,
                    @SerializedName("director")
                    val director: List<Any?>?,
                    @SerializedName("distributor")
                    val distributor: List<Any?>?,
                    @SerializedName("genres")
                    val genres: List<Any?>?,
                    @SerializedName("licensor")
                    val licensor: List<Any?>?,
                    @SerializedName("producers")
                    val producers: List<Any?>?,
                    @SerializedName("rating")
                    val rating: Any?, // null
                    @SerializedName("lyrics")
                    val lyrics: String?, // null
                    @SerializedName("session-artist")
                    val sessionArtist: List<Any?>?,
                    @SerializedName("song-writers")
                    val songWriters: List<Any?>?,
                    @SerializedName("songs-albums-id")
                    val songsAlbumsId: SongsAlbumsId?,
                    @SerializedName("songs-artist-ids")
                    val songsArtistIds: List<SongsArtistId?>?,
                    @SerializedName("sub-genres")
                    val subGenres: List<Any?>?,
                ) {
                    data class Composer(
                        @SerializedName("contentSlug")
                        val contentSlug: String?, // hollands-mannen-ensemble
                        @SerializedName("contentType")
                        val contentType: String?, // PERSON
                        @SerializedName("description")
                        val description: String?,
                        @SerializedName("externalIdentifier")
                        val externalIdentifier: Any?, // null
                        @SerializedName("id")
                        val id: Int?, // 329
                        @SerializedName("images")
                        val images: List<Any?>?,
                        @SerializedName("mediaType")
                        val mediaType: String?, // ARTISTS
                        @SerializedName("title")
                        val title: String?, // Hollands Mannen Ensemble
                    )

                    data class SongsAlbumsId(
                        @SerializedName("contentSlug")
                        val contentSlug: String?, // hme-kerst
                        @SerializedName("contentType")
                        val contentType: String?, // CUSTOM
                        @SerializedName("description")
                        val description: Any?, // null
                        @SerializedName("externalIdentifier")
                        val externalIdentifier: Any?, // null
                        @SerializedName("id")
                        val id: Int?, // 331
                        @SerializedName("images")
                        val images: List<Image?>?,
                        @SerializedName("mediaType")
                        val mediaType: String?, // ALBUMS
                        @SerializedName("title")
                        val title: String?, // HME KERST
                    ) {
                        data class Image(
                            @SerializedName("colorPalette")
                            val colorPalette: List<String?>?,
                            @SerializedName("dominantColor")
                            val dominantColor: String?, // #886a64
                            @SerializedName("height")
                            val height: Double?, // 1600.0
                            @SerializedName("id")
                            val id: Int?, // 106
                            @SerializedName("imageKey")
                            val imageKey: String?, // nieuw_1719989579264.png
                            @SerializedName("imageType")
                            val imageType: String?, // 1x1
                            @SerializedName("isDefault")
                            val isDefault: Boolean?, // false
                            @SerializedName("originalImageSizeInBytes")
                            val originalImageSizeInBytes: Int?, // 4412022
                            @SerializedName("showTitle")
                            val showTitle: Boolean?, // false
                            @SerializedName("src")
                            val src: String?, // https://d3ffi1kkogkk1a.cloudfront.net/imageStore/nieuw_1719989579264.png
                            @SerializedName("status")
                            val status: String?, // PUBLISHED
                            @SerializedName("tag")
                            val tag: String?, // img1
                            @SerializedName("width")
                            val width: Double?, // 1600.0
                        )
                    }

                    data class SongsArtistId(
                        @SerializedName("contentSlug")
                        val contentSlug: String?, // arjan-huizer
                        @SerializedName("contentType")
                        val contentType: String?, // PERSON
                        @SerializedName("description")
                        val description: Any?, // null
                        @SerializedName("externalIdentifier")
                        val externalIdentifier: Any?, // null
                        @SerializedName("id")
                        val id: Int?, // 297
                        @SerializedName("images")
                        val images: List<Any?>?,
                        @SerializedName("mediaType")
                        val mediaType: String?, // ARTISTS
                        @SerializedName("title")
                        val title: String?, // Arjan Huizer
                    )
                }
            }
        }
    }
}