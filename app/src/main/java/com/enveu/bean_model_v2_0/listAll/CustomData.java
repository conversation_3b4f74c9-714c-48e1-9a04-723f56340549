package com.enveu.bean_model_v2_0.listAll;

import com.enveu.beanModelV3.RelatedRailsCommonData;
import com.enveu.beanModelV3.videoDetailV3.MovieArtistsResponse;
import com.enveu.beanModelV3.videoDetailV3.Season_series;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class CustomData implements Serializable {

    @SerializedName("episode-series-id")
    int episode_series_id;
    @SerializedName("movies-artists-ids")
    private List<MovieArtistsResponse> movies_artists_ids;
    @SerializedName("director")
    private List<MovieArtistsResponse> director;
    @SerializedName("episode-season-id")
    @Expose
    private Season_series episode_season_id;
    @SerializedName("trailer_reference_id")
    int trailer_reference_id;
    @SerializedName("genres")
    private List<RelatedRailsCommonData.Data.Item.CustomData.Genre> genres;
    @SerializedName("sub-genres")
    private List<RelatedRailsCommonData.Data.Item.CustomData.Genre> sub_genres;
    @SerializedName("is_exclusive")
    boolean is_exclusive;
    @SerializedName("year-of-release")
    int release_date=0;
    @SerializedName("release-year")
    int release_year;
    public int getEpisode_series_id() {
        return episode_series_id;
    }

    public void setEpisode_series_id(int episode_series_id) {
        this.episode_series_id = episode_series_id;
    }

    public int getRelease_year() {
        return release_year;
    }

    public void setRelease_year(int release_year) {
        this.release_year = release_year;
    }

    public Season_series getEpisode_season_id() {
        return episode_season_id;
    }

    public void setEpisode_season_id(Season_series episode_season_id) {
        this.episode_season_id = episode_season_id;
    }

    public int getTrailer_reference_id() {
        return trailer_reference_id;
    }

    public void setTrailer_reference_id(int trailer_reference_id) {
        this.trailer_reference_id = trailer_reference_id;
    }

    public boolean isIs_exclusive() {
        return is_exclusive;
    }

    public void setIs_exclusive(boolean is_exclusive) {
        this.is_exclusive = is_exclusive;
    }

    public List<RelatedRailsCommonData.Data.Item.CustomData.Genre> getGenres() {
        return genres;
    }
    public List<RelatedRailsCommonData.Data.Item.CustomData.Genre> getSub_genres() {
        return sub_genres;
    }
    public int getRelease_date() {
        return release_date;
    }

    public List<MovieArtistsResponse> getMoviesArtistsIds() {
        return movies_artists_ids;
    }
    public void setMoviesArtistsIds(List<MovieArtistsResponse> movies_artists_ids) {
        this.movies_artists_ids = movies_artists_ids;
    }
}
