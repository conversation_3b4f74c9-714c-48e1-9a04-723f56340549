
package com.enveu.bean_model_v2_0.listAll;

import android.os.Parcelable;

import com.enveu.client.playlist.beanv2_0.ImagesItem;
import com.enveu.client.playlist.beanv2_0.LiveContent;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.enveu.beanModelV3.playListModelV2.CustomContentData;
import com.enveu.beanModelV3.videoDetailsV2.VideoDetails;

import java.util.List;


public class Item implements Parcelable
{
    @SerializedName("customContent")
    @Expose
    private CustomContentData customContent;

    @SerializedName("dateCreated")
    @Expose
    private Long dateCreated;
    @SerializedName("lastUpdated")
    @Expose
    private Long lastUpdated;
    @SerializedName("id")
    @Expose
    private Integer id;
    @SerializedName("title")
    @Expose
    private String title;
    @SerializedName("description")
    @Expose
    private String description;
    @SerializedName("contentType")
    @Expose
    private String contentType;
    @SerializedName("keywords")
    @Expose
    private List<Object> keywords = null;
    @SerializedName("premium")
    @Expose
    private Boolean premium;
    @SerializedName("sku")
    @Expose
    private String sku;
    @SerializedName("publishedDate")
    @Expose
    private Long publishedDate;
    @SerializedName("customData")
    @Expose
    private SeriesCustomData customData;
    @SerializedName("parentalRating")
    @Expose
    private Object parentalRating;
    @SerializedName("parentContent")
    @Expose
    private ParentContent parentContent;
    @SerializedName("video")
    @Expose
    private VideoDetails video;

    @SerializedName("liveContent")
    @Expose
    private LiveContent liveContent;

    @SerializedName("externalRefId")
    @Expose
    private String externalRefId;
    @SerializedName("contentSource")
    @Expose
    private String contentSource;

    @SerializedName("images")
    @Expose
    private List<ImagesItem> images;

    @SerializedName("episodeNumber")
    @Expose
    private Object episodeNumber;

    public final static Creator<Item> CREATOR = new Creator<Item>() {


        @SuppressWarnings({
            "unchecked"
        })
        public Item createFromParcel(android.os.Parcel in) {
            return new Item(in);
        }

        public Item[] newArray(int size) {
            return (new Item[size]);
        }

    }
    ;

    protected Item(android.os.Parcel in) {
        this.dateCreated = ((Long) in.readValue((Long.class.getClassLoader())));
        this.lastUpdated = ((Long) in.readValue((Long.class.getClassLoader())));
        this.id = ((Integer) in.readValue((Integer.class.getClassLoader())));
        this.title = ((String) in.readValue((String.class.getClassLoader())));
        this.description = ((String) in.readValue((String.class.getClassLoader())));
        this.contentType = ((String) in.readValue((String.class.getClassLoader())));
        in.readList(this.keywords, (String.class.getClassLoader()));
        this.premium = ((Boolean) in.readValue((Boolean.class.getClassLoader())));
        this.sku = ((String) in.readValue((String.class.getClassLoader())));
        this.publishedDate = ((Long) in.readValue((Long.class.getClassLoader())));
        this.customData = ((SeriesCustomData) in.readValue((SeriesCustomData.class.getClassLoader())));
        this.parentalRating = in.readValue((Object.class.getClassLoader()));
        this.episodeNumber = in.readValue((Object.class.getClassLoader()));
        this.parentContent = ((ParentContent) in.readValue((ParentContent.class.getClassLoader())));
        this.video = ((VideoDetails) in.readValue((VideoDetails.class.getClassLoader())));
        this.liveContent = ((LiveContent) in.readValue((LiveContent.class.getClassLoader())));
        this.images = (List<ImagesItem>) in.readValue((ImagesItem.class.getClassLoader()));
        this.externalRefId = ((String) in.readValue((String.class.getClassLoader())));
        this.contentSource = ((String) in.readValue((String.class.getClassLoader())));
    }

    public Item() {
    }


    public CustomContentData getCustomContent() {
        return customContent;
    }

    public void setCustomContent(CustomContentData customContent) {
        this.customContent = customContent;
    }



    public List<ImagesItem> getImages() {
        return images;
    }

    public void setImages(List<ImagesItem> images) {
        this.images = images;
    }

    public Long getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Long dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Long getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(Long lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }


    public List<Object> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<Object> keywords) {
        this.keywords = keywords;
    }

    public Boolean getPremium() {
        return premium;
    }

    public void setPremium(Boolean premium) {
        this.premium = premium;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Long getPublishedDate() {
        return publishedDate;
    }

    public void setPublishedDate(Long publishedDate) {
        this.publishedDate = publishedDate;
    }


    public SeriesCustomData getCustomData() {
        return customData;
    }

    public void setCustomData(SeriesCustomData customData) {
        this.customData = customData;
    }

    public Object getParentalRating() {
        return parentalRating;
    }

    public void setParentalRating(Object parentalRating) {
        this.parentalRating = parentalRating;
    }

    public ParentContent getParentContent() {
        return parentContent;
    }

    public void setParentContent(ParentContent parentContent) {
        this.parentContent = parentContent;
    }

    public VideoDetails getVideo() {
        return video;
    }

    public void setVideo(VideoDetails video) {
        this.video = video;
    }

    public String getExternalRefId() {
        return externalRefId;
    }

    public void setExternalRefId(String externalRefId) {
        this.externalRefId = externalRefId;
    }

    public String getContentSource() {
        return contentSource;
    }

    public void setContentSource(String contentSource) {
        this.contentSource = contentSource;
    }

    public LiveContent getLiveContent() {
        return liveContent;
    }

    public void setLiveContent(LiveContent liveContent) {
        this.liveContent = liveContent;
    }

    public Object getEpisodeNumber() {
        return episodeNumber;
    }

    public void setEpisodeNumber(Object episodeNumber) {
        this.episodeNumber = episodeNumber;
    }

    public void writeToParcel(android.os.Parcel dest, int flags) {
        dest.writeValue(dateCreated);
        dest.writeValue(lastUpdated);
        dest.writeValue(id);
        dest.writeValue(title);
        dest.writeValue(description);
        dest.writeValue(contentType);
        dest.writeList(keywords);
        dest.writeValue(premium);
        dest.writeValue(sku);
        dest.writeValue(publishedDate);
        dest.writeValue(customData);
        dest.writeValue(liveContent);
        dest.writeValue(parentalRating);
        dest.writeValue(episodeNumber);
        dest.writeValue(parentContent);
        dest.writeValue(video);
        dest.writeValue(externalRefId);
        dest.writeValue(contentSource);
        dest.writeValue(images);
    }

    public int describeContents() {
        return  0;
    }

}
