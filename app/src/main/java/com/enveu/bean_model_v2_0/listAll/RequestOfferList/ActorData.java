package com.enveu.bean_model_v2_0.listAll.RequestOfferList;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class ActorData implements Serializable {

    @SerializedName("title")
    @Expose
    private String title;

    @SerializedName("contentSlug")
    @Expose
    private String contentSlug;

    @SerializedName("contentType")
    @Expose
    private String contentType;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContentSlug() {
        return contentSlug;
    }

    public void setContentSlug(String contentSlug) {
        this.contentSlug = contentSlug;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getMediaType() {
        return mediaType;
    }

    public void setMediaType(String mediaType) {
        this.mediaType = mediaType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @SerializedName("mediaType")
    @Expose
    private String mediaType;

    @SerializedName("description")
    @Expose
    private String description;
}
