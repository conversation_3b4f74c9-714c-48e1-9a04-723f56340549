
package com.enveu.bean_model_v2_0.listAll;


import android.os.Parcelable;

import com.enveu.beanModelV3.videoDetailV3.Season_series;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.SongsArtistIdsItem;
import com.enveu.bean_model_v2_0.listAll.RequestOfferList.ActorData;
import com.enveu.bean_model_v2_0.videoDetailBean.DirectorModelItem;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class SeriesCustomData implements Serializable {
    @SerializedName("season-number")
    @Expose
    private String season_number;

    @SerializedName("round-number")
    @Expose
    private String round_number;

    @SerializedName("match-number")
    @Expose
    private String match_number;

    @SerializedName("match-start-date-time")
    @Expose
    private String match_start_date_time;

    @SerializedName("matches-round-id")
    @Expose
    private String matches_round_id;


    @SerializedName("round-season-id")
    @Expose
    private String round_season_id;

    @SerializedName("matches-season-id")
    @Expose
    private String matches_season_id;

    @SerializedName("matches-tournament-id")
    @Expose
    private String matches_tournament_id;

    @SerializedName("trailer_reference_id")
    @Expose
    private String trailer_reference_id;

//    @SerializedName("season-series-id")
//    @Expose
//    private Season_series season_series_id;

    @SerializedName("songs-artist-ids")
    private List<SongsArtistIdsItem> songs_artist_ids;

    @SerializedName("episode-series-id")
    @Expose
    private Season_series episode_series_id;

    @SerializedName("episode-season-id")
    @Expose
    private Season_series episode_season_id;


    @SerializedName("season-interview-id")
    @Expose
    private String season_interview_id;

    @SerializedName("showTitle")
    @Expose
    private String showTitle;

    @SerializedName("playlist-ids")
    @Expose
    private String playlist_ids;

    public String getPlaylist_ids() {
        return playlist_ids;
    }

    public void setPlaylist_ids(String playlist_ids) {
        this.playlist_ids = playlist_ids;
    }

    public String getShowTitle() {
        return showTitle;
    }

    public void setShowTitle(String showTitle) {
        this.showTitle = showTitle;
    }

    @SerializedName("round-interview-id")
    @Expose
    private String round_interview_id;

    @SerializedName("season-tournament-id")
    @Expose
    private String season_tournament_id;

    @SerializedName("round-tournament-id")
    @Expose
    private String round_tournament_id;


    public ArrayList<ActorData> getActorData() {
        return actorData;
    }

    public void setActorData(ArrayList<ActorData> actorData) {
        this.actorData = actorData;
    }

    @SerializedName("actor")
    @Expose
    private ArrayList<ActorData> actorData;

    @SerializedName("director")
    private List<DirectorModelItem> director;

    public List<DirectorModelItem> getDirector() {
        return director;
    }

    public void setDirector(List<DirectorModelItem> directorModel) {
        this.director = directorModel;
    }


    public String getTrailer_reference_id() {
        return trailer_reference_id;
    }

    public void setTrailer_reference_id(String trailer_reference_id) {
        this.trailer_reference_id = trailer_reference_id;
    }

    public Season_series getEpisode_series_id() {
        return episode_series_id;
    }

    public void setEpisode_series_id(Season_series episode_series_id) {
        this.episode_series_id = episode_series_id;
    }

    public Season_series getEpisode_season_id() {
        return episode_season_id;
    }

    public void setEpisode_season_id(Season_series episode_season_id) {
        this.episode_season_id = episode_season_id;
    }

    public String getSeason_number() {
        return season_number;
    }

    public void setSeason_number(String season_number) {
        this.season_number = season_number;
    }



//    public Season_series getSeason_series_id() {
//        return season_series_id;
//    }
//
//    public void setSeason_series_id(Season_series season_series_id) {
//        this.season_series_id = season_series_id;
//    }

    public String getSeason_interview_id() {
        return season_interview_id;
    }

    public void setSeason_interview_id(String season_interview_id) {
        this.season_interview_id = season_interview_id;
    }

    public String getSeason_tournament_id() {
        return season_tournament_id;
    }

    public void setSeason_tournament_id(String season_tournament_id) {
        this.season_tournament_id = season_tournament_id;
    }

    public String getMatch_number() {
        return match_number;
    }

    public void setMatch_number(String match_number) {
        this.match_number = match_number;
    }

    public String getMatch_start_date_time() {
        return match_start_date_time;
    }

    public void setMatch_start_date_time(String match_start_date_time) {
        this.match_start_date_time = match_start_date_time;
    }

    public String getMatches_round_id() {
        return matches_round_id;
    }

    public void setMatches_round_id(String matches_round_id) {
        this.matches_round_id = matches_round_id;
    }

    public String getMatches_season_id() {
        return matches_season_id;
    }

    public void setMatches_season_id(String matches_season_id) {
        this.matches_season_id = matches_season_id;
    }

    public String getMatches_tournament_id() {
        return matches_tournament_id;
    }

    public void setMatches_tournament_id(String matches_tournament_id) {
        this.matches_tournament_id = matches_tournament_id;
    }

    public String getRound_number() {
        return round_number;
    }

    public String getRound_season_id() {
        return round_season_id;
    }

    public String getRound_interview_id() {
        return round_interview_id;
    }

    public String getRound_tournament_id() {
        return round_tournament_id;
    }

    public List<SongsArtistIdsItem> getSongs_artist_ids() {
        return songs_artist_ids;
    }

    public final static Parcelable.Creator<SeriesCustomData> CREATOR = new Parcelable.Creator<SeriesCustomData>() {


        public SeriesCustomData createFromParcel(android.os.Parcel in) {

            return new SeriesCustomData(in);
        }

        public SeriesCustomData[] newArray(int size) {
            return (new SeriesCustomData[size]);
        }

    };

    protected SeriesCustomData(android.os.Parcel in) {
        this.season_number = ((String) in.readValue((String.class.getClassLoader())));
       // this.season_series_id = (Season_series) in.readValue((String.class.getClassLoader()));
        this.trailer_reference_id = ((String) in.readValue((String.class.getClassLoader())));
       // this.episode_season_id = (Season_series) in.readValue((String.class.getClassLoader()));
      //  this.episode_series_id = (Season_series) in.readValue((String.class.getClassLoader()));
        this.season_tournament_id = ((String) in.readValue((String.class.getClassLoader())));
        this.season_interview_id = ((String) in.readValue((String.class.getClassLoader())));
        this.match_number = ((String) in.readValue((String.class.getClassLoader())));
        this.match_start_date_time = ((String) in.readValue((String.class.getClassLoader())));
        this.matches_round_id = ((String) in.readValue((String.class.getClassLoader())));
        this.matches_season_id = ((String) in.readValue((String.class.getClassLoader())));
        this.matches_tournament_id = ((String) in.readValue((String.class.getClassLoader())));

        this.round_season_id = ((String) in.readValue((String.class.getClassLoader())));
        this.round_number = ((String) in.readValue((String.class.getClassLoader())));
        this.round_interview_id = ((String) in.readValue((String.class.getClassLoader())));
        this.round_tournament_id = ((String) in.readValue((String.class.getClassLoader())));

        this.songs_artist_ids = (List<SongsArtistIdsItem>) in.readValue((String.class.getClassLoader()));

    }

    public void writeToParcel(android.os.Parcel dest) {
        dest.writeValue(season_number);
       // dest.writeValue(season_series_id);
        dest.writeValue(trailer_reference_id);
       // dest.writeValue(episode_season_id);
      //  dest.writeValue(episode_series_id);
        dest.writeValue(season_tournament_id);
        dest.writeValue(season_interview_id);
        dest.writeValue(match_number);
        dest.writeValue(match_start_date_time);
        dest.writeValue(matches_round_id);
        dest.writeValue(matches_season_id);
        dest.writeValue(matches_tournament_id);

        dest.writeValue(round_season_id);
        dest.writeValue(round_number);
        dest.writeValue(round_interview_id);
        dest.writeValue(round_tournament_id);
        dest.writeValue(songs_artist_ids);

    }

    public int describeContents() {
        return  0;
    }

}