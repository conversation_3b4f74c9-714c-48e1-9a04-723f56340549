package com.enveu.bean_model_v2_0.videoDetailBean

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class DirectorModel(

	@field:SerializedName("DirectorModel")
	val directorModel: List<DirectorModelItem?>? = null
)

data class ImagesItem(

	@field:SerializedName("originalImageSizeInBytes")
	val originalImageSizeInBytes: Int? = null,

	@field:SerializedName("src")
	val src: String? = null,

	@field:SerializedName("imageKey")
	val imageKey: String? = null,

	@field:SerializedName("dominantColor")
	val dominantColor: String? = null,

	@field:SerializedName("isDefault")
	val isDefault: Boolean? = null,

	@field:SerializedName("showTitle")
	val showTitle: Any? = null,

	@field:SerializedName("width")
	val width: Double? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("tag")
	val tag: String? = null,

	@field:SerializedName("colorPalette")
	val colorPalette: List<String?>? = null,

	@field:SerializedName("imageType")
	val imageType: String? = null,

	@field:SerializedName("height")
	val height: Double? = null,

	@field:SerializedName("status")
	val status: Any? = null
):Serializable

data class DirectorModelItem(

	@field:SerializedName("images")
	val images: List<ImagesItem?>? = null,

	@field:SerializedName("externalIdentifier")
	val externalIdentifier: Any? = null,

	@field:SerializedName("contentSlug")
	val contentSlug: String? = null,

	@field:SerializedName("description")
	val description: String? = null,

	@field:SerializedName("mediaType")
	val mediaType: String? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("contentType")
	val contentType: String? = null
):Serializable
