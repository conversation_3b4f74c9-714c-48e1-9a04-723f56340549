package com.enveu.bean_model_v2_0.videoDetailBean.liveDetailBean

import kotlinx.parcelize.Parcelize
import android.os.Parcelable

@Parcelize
data class LiveStatusResponse(
	var data: Data? = null,
	val debugMessage: String? = null,
	var responseCode: Int? = null
) : Parcelable

@Parcelize
data class Data(
	val currentViewCount: String? = null,
	val eventStatus: String? = null,
	val streamStatus: String? = null
) : Parcelable
