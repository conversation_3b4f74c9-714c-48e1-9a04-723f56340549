package com.enveu.callbacks.apicallback;

import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory;
import com.enveu.networking.errormodel.ApiErrorModel;

import java.util.List;

public interface ApiResponseModel<T> {
    void onStart();

    default void onSuccess(T response){

    }
    default void onSuccess(List<BaseCategory> catList){

    }
    default void onError(ApiErrorModel apiError){

    }

    default void onFailure(ApiErrorModel httpError){

    }
}