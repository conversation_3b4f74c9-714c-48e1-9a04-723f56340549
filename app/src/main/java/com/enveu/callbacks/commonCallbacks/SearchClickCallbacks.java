package com.enveu.callbacks.commonCallbacks;

import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModel.popularSearch.ItemsItem;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;

public interface SearchClickCallbacks {
    void onEnveuItemClicked(EnveuVideoItemBean itemValue);
    void onShowAllItemClicked(RailCommonData itemValue,String header);
    void onShowAllProgramClicked(RailCommonData itemValue);
    void onPopularSearchItemClicked(ItemsItem itemValue);
}
