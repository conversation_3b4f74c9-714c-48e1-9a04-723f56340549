package com.enveu.callbacks.player.callback

import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent


interface OnSongItemClick {
    fun songItemClick(songList: List<DataItem>, song: DataItem,extarnalRefId: String, imageContent: ImageContent?,image: String?, playQueueItems:Boolean?,isQueueItemClick: Boolean = false,songsPosition: Int = 0)
}