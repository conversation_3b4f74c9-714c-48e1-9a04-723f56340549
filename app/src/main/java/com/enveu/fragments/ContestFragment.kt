package com.enveu.fragments

import android.os.Bundle
import com.enveu.beanModel.TabsBaseFragment
import com.enveu.fragments.home.viewModel.HomeFragmentViewModel

class ContestFragment(private val tabID : String) : TabsBaseFragment<HomeFragmentViewModel?>() {
    constructor() : this("0")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setViewModel(ContestFragmentViewModel::class.java, tabID)
    }
}