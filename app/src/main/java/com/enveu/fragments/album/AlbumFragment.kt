package com.enveu.fragments.album

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.mainPLayer.MainPlayerActivity
import com.enveu.activities.sponsorArtist.ArtistAndSponserActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.PaymentDetailPage
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.EnvMediaContentList
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent
import com.enveu.callbacks.player.callback.OnAudioItemClickInteraction
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.callbacks.player.callback.SongClick
import com.enveu.callbacks.player.callback.SongClickListener
import com.enveu.databinding.FragmentAlbumBinding
import com.enveu.fragments.album.adapter.SongsAdapter
import com.enveu.fragments.artist.ArtistAllSongsListFragment
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.player.ui.AudioInteractionFragment
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.BottomDialogFragment
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import java.io.Serializable


class AlbumFragment : Fragment(), SongClick, CommonDialogFragment.EditDialogListener,
    OnAudioItemClickInteraction {

    private lateinit var binding: FragmentAlbumBinding
    private var adapter: SongsAdapter? = null
    private var contentSlug = ""
    private var mediaType = ""
    private var assetID = ""
    private var isLoggedIn :Boolean = false
    private var videoDetails: EnveuVideoItemBean? = null
    private var page = 0
    private var pageSize = 50
    var songList: List<DataItem> = ArrayList()
    private var token: String? = null
    private var preference: KsPreferenceKeys? = null
    private var onSongItemClick: OnSongItemClick? = null
    private var isUserNotEntitle = false
    private var playerListener: MainPlayerActivity.PlayerListener? = null
    private var resEntitle: ResponseEntitle? = null
    private val stringsHelper by lazy { StringsHelper }
    private var featureFlagModel: FeatureFlagModel? = null
    private var thumbnailImageUrl :String?= null
    private var geners = ""
    private var isFromGenre : Boolean = false
    private var genresId = mutableListOf<Int>()
    private var containGenresId = ""
    private var artistAllSongsListFragment: ArtistAllSongsListFragment? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (activity != null && activity is HomeActivity)
            (activity as HomeActivity).toolFrame(1)
        (activity as HomeActivity).detailFrameVisibility()
        preference = KsPreferenceKeys.getInstance()
        token = preference?.appPrefAccessToken
        if (preference?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
        }

        featureFlagModel = AppConfigMethod.parseFeatureFlagList()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        return FragmentAlbumBinding.inflate(LayoutInflater.from(requireContext()), container, false).run {
                binding = this
                root
            }
        parseColor()
    }


    private fun setClicks() {
        binding.goBack.setOnClickListener {
            ArtistAndSponserActivity.AppState.isSkipped = false
            activity?.onBackPressed()
        }
        if (featureFlagModel?.featureFlag?.IS_GENRES_CLICKABLE == true){
            binding.genre.setOnClickListener {
                isFromGenre = true

                genresId.forEachIndexed { index, genre ->
                    if (index == genresId.size.minus(1)) {
                        containGenresId += genre.toString()
                    }else {
                        containGenresId += "${genre}, "
                    }
                }
                setArtistAllSongsListing()
            }
        }
    }

    var railInjectionHelper: RailInjectionHelper? = null


    private val assetDetailsByID: Unit
        get() {
            railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
            railInjectionHelper!!.getAssetDetailsV2(assetID, requireContext())
                .observe(requireActivity()) { assetResponse: ResponseModel<*>? ->
                    if (assetResponse != null) {
                        val gson = Gson()
                        val json = gson.toJson(assetResponse.baseCategory)
                        Log.w("getAssetDetailsV2-->>>>>", json)

                        if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                        } else if (assetResponse.status.equals(
                                APIStatus.SUCCESS.name,
                                ignoreCase = true
                            )
                        ) {
                            parseDetail(assetResponse)
                        } else if (assetResponse.status.equals(
                                APIStatus.ERROR.name,
                                ignoreCase = true
                            )
                        ) {
                            if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {
                                commonDialog(
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                        getString(R.string.popup_error)
                                    ),
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                        getString(R.string.popup_something_went_wrong)
                                    ),
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                        getString(R.string.popup_continue)
                                    ), ""
                                )
                            }
                        } else if (assetResponse.status.equals(
                                APIStatus.FAILURE.name,
                                ignoreCase = true
                            )
                        ) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.popup_something_went_wrong)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                ), ""
                            )
                        }
                    }
                }
        }


    private val assetDetails: Unit
        get() {
            railInjectionHelper?.getAssetDetailsbySlug(contentSlug)
                ?.observe(requireActivity()) { assetResponse: ResponseModel<*>? ->
                    if (assetResponse != null) {
                        if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                        } else if (assetResponse.status.equals(
                                APIStatus.SUCCESS.name, ignoreCase = true
                            )
                        ) {
                            parseDetail(assetResponse)
                        } else if (assetResponse.status.equals(
                                APIStatus.ERROR.name, ignoreCase = true
                            )
                        ) {
                            if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {

                            }
                        } else if (assetResponse.status.equals(
                                APIStatus.FAILURE.name, ignoreCase = true
                            )
                        ) {

                        }
                    }
                }
        }

    private fun parseDetail(assetResponse: ResponseModel<*>) {
        stopShimmer()
        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
        if (enveuCommonResponse.enveuVideoItemBeans.size > 0) {
            videoDetails = enveuCommonResponse.enveuVideoItemBeans[0]
            AnalyticsUtils.trackScreenView(
                context,
                AppConstants.CONTENT_DETAIL + " - " + videoDetails?.title
            )
        }
        setUiData()
        if (!isAdded) {
            Log.w("AlbumFragment", "Fragment not attached to an activity, parseDetail aborted.")
            return
        }
        videoDetails?.id?.let {
            getSongsForAlbum(it.toString())
        }
//        val envMediaDetailList = assetResponse.baseCategory as EnvMediaContentList
//        songList = envMediaDetailList.data.items
//        songList?.let {
//            it.get(0)
//        }
    }

    private fun getSongsForAlbum(albumId: String) {
        if (!isAdded) {
            Log.w(
                "AlbumFragment",
                "Fragment not attached to an activity, getSongsForAlbum aborted."
            )
            return
        }
        val activity = requireActivity()
        val customData = "songs-albums-id|OR:$albumId"
        railInjectionHelper?.getMediaContentList(customData, page, pageSize)
            ?.observe(activity) { assetResponse: ResponseModel<*>? ->
                if (assetResponse != null) {
                    if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                    } else if (assetResponse.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                        setSongAdapter(assetResponse)
                    } else if (assetResponse.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {

                    }
                }
            }
    }

    private fun callShimmer() {
        binding.seriesShimmer.visibility = View.VISIBLE
        binding.mShimmer.seriesShimmerScroll1.isEnabled = false
        binding.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding.mShimmer.sfShimmer1.startShimmer()
        binding.mShimmer.sfShimmer2.startShimmer()
        binding.mShimmer.sfShimmer3.startShimmer()
        binding.mShimmer.flBackIconImage.bringToFront()
    }

    private fun stopShimmer() {
        binding.seriesShimmer.visibility = View.GONE
        binding.mShimmer.sfShimmer1.startShimmer()
        binding.mShimmer.sfShimmer2.startShimmer()
        binding.mShimmer.sfShimmer3.startShimmer()
    }

    private fun setSongAdapter(assetResponse: ResponseModel<*>) {
        // Extract the list of songs from the response
        if (songList != null && songList.isNotEmpty()) {
            (songList as ArrayList).clear()
        }
        val envMediaDetailList = assetResponse.baseCategory as EnvMediaContentList
        songList = envMediaDetailList.data.items
        audioInteractionFragment?.getAllSongsList(songList)

        Log.d("songList", "Album: "+ songList.size.toString())

        // Sort the songList by trackNumber if it exists
        (songList as MutableList<DataItem>?)?.sortWith(compareBy {
            it.customData.trackNumber?.toIntOrNull() ?: Int.MAX_VALUE
        })
        Log.d("songList", "Album: "+ songList.size.toString())
        // Check if adapter is already initialized
        if (adapter != null) {
            adapter?.addItems(songList as MutableList<DataItem>)
            return
        }
      //  songList = envMediaDetailList.data.items
        adapter = featureFlagModel?.let {
            SongsAdapter(
                songList as MutableList<DataItem>,
                this@AlbumFragment,
                featureFlag = it,false,
                0
            )
        }
        thumbnailImageUrl?.let {
            adapter?.setThumbnailImgOfPoster(it)
        }
        // Initialize the adapter with the sorted song list
        binding.myRecycleView.adapter = adapter

        // Add scroll listener to handle pagination
        binding.myRecycleView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                val visibleItemCount = layoutManager.childCount
                val totalItemCount = layoutManager.itemCount
                val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount
                    && firstVisibleItemPosition >= 0
                    && totalItemCount >= pageSize
                ) {
                    page++
                    // temporary comment this Line
                 // getSongsForAlbum(albumId = videoDetails?.id.toString())
                }
            }
        })
    }

    private fun setUiData() {
        binding.contentTitle.text = videoDetails?.title
        if (videoDetails?.description?.isNotEmpty() == true && videoDetails?.description?.isNotBlank() == true) {
            binding.description.text = videoDetails?.description
        }else{ binding.description.visibility=View.GONE }
        thumbnailImageUrl = videoDetails?.imageContent?.src
        videoDetails?.imageContent?.src?.let {
            ImageHelper.getInstance(requireActivity()).loadListImage(binding.imgscr, it)
        }

        var artistName = ""
        videoDetails?.albumArtistId?.forEachIndexed { index, item ->
            if (index == videoDetails?.albumArtistId?.size?.minus(1)) {
                artistName += item.title
            } else {
                artistName += "${item.title}, "
            }
        }

        var metadetails=getMetaDetails(videoDetails!!,artistName)

        binding.genre.text = metadetails
        binding.llRootTopChild.background = AppCommonMethod.setGradientBackgroundColor(
           Color.parseColor(AppCommonMethod.getDominantColor(videoDetails?.imageContent)),
            Color.parseColor("#0000006b"),
            Color.parseColor("#00000000"),
            "TOP_TO_BOTTOM"
        )

        if (isAdded && context != null) {
            setUserInteractionFragment(videoDetails?.id)
        }
    }

    private fun getMetaDetails(videoDetails: EnveuVideoItemBean,artistName:String): CharSequence? {
        var metaDetail=StringBuilder()
        metaDetail.appendLine(artistName)
        videoDetails.genres?.forEachIndexed { index, genre ->
            if (index == videoDetails?.customData?.genres?.size?.minus(1)) {
                geners += genre.title
            }else {
                geners += "${genre.title}, "
            }
        }
        videoDetails.sub_genres?.forEachIndexed{ index,sub_gener->
            if (index == 0) {
                geners += "${sub_gener.title}"
            } else {
                geners += "${sub_gener.title}, "
            }
        }
        if (geners.isNotEmpty()){
            metaDetail.appendLine(geners)
        }
        videoDetails.genres?.forEachIndexed { index, genre ->
            genre.id?.let { genresId.add(it) }
        }
        videoDetails.sub_genres?.forEachIndexed { index, subGenre ->
            subGenre.id?.let { genresId.add(it) }
        }
        if (!videoDetails.year.isNullOrEmpty() && !videoDetails.year.equals("0")){
            metaDetail.appendLine(videoDetails.year)
        }
        return metaDetail
    }

    private fun setArtistAllSongsListing(){
        val songClick = object : SongClickListener {
            override fun onSongItemClick(song: DataItem) {
                onSongClick(song)
            }

            override fun onSongThreeDotClick(song: DataItem) {
                onThreeDotClick(song)
            }

        }
        val bundle = Bundle()
        bundle.putString(AppConstants.BUNDLE_ASSET_ID, assetID)
        bundle.putString(AppConstants.THUMBNAIL_IMG,thumbnailImageUrl)
        bundle.putString("genres", geners)
        bundle.putBoolean("isFromGenre", isFromGenre)
        bundle.putString("genreIds", containGenresId)
        bundle.putString("redirectFrom", "albumFragment")
        val transaction = requireActivity().supportFragmentManager.beginTransaction()
        artistAllSongsListFragment = ArtistAllSongsListFragment()
        artistAllSongsListFragment?.arguments = bundle
        artistAllSongsListFragment?.songItemListenerCallback(songClick)
        transaction.add(R.id.content_frame, artistAllSongsListFragment!!, "23").addToBackStack(null)
            .commit()
    }


    private var audioInteractionFragment: AudioInteractionFragment? = null
    private fun setUserInteractionFragment(contentId: Int?) {
        val transaction = childFragmentManager?.beginTransaction()
        val args = Bundle()
        contentId?.let { args.putInt(AppConstants.BUNDLE_ASSET_ID, it) }
        args.putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
        args.putSerializable(AppConstants.BUNDLE_SERIES_DETAIL, videoDetails)
        args.putString(AppConstants.BUNDLE_CONTENT_SLUG, contentSlug)
        args.putString(AppConstants.THUMBNAIL_IMG,thumbnailImageUrl)
        args.putString(AppConstants.AUDIO_INTERACTION_REDIRECTION,AppConstants.ALBUM)
        args.putSerializable(AppConstants.ALL_SONG_LIST, songList as Serializable)
        args.putSerializable(AppConstants.BUNDLE_BIO_TITLE,videoDetails?.title)
        args.putSerializable(AppConstants.BIO_DISCRIPTION,videoDetails?.longDescription)
        args.putBoolean(AppConstants.SHOULD_PLAY_QUEUE_ITEM, true)
        audioInteractionFragment = AudioInteractionFragment()
        audioInteractionFragment!!.arguments = args
        audioInteractionFragment!!.passInstance(this)
        setDataModleandSendToAudioInteractionFrag()
        transaction?.replace(R.id.fragment_audio_interaction, audioInteractionFragment!!)
        transaction?.commit()
    }


    private fun setDataModleandSendToAudioInteractionFrag() {
        val gson=Gson()
        val json=gson.toJson(videoDetails)
        val songDetail:DataItem=gson.fromJson(json,DataItem::class.java)
        audioInteractionFragment?.setAttachedFragmentDetails(songDetail)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        callShimmer()
        setViewModel()
        setClicks()
        getIntentData()


        if (!contentSlug.isNullOrEmpty()) {
            Log.d("branchRedirections", "AlbumFragmnet CintentSlug: $mediaType--$assetID--$contentSlug")

            assetDetails
        } else if (assetID != "") {
            Log.d(
                "branchRedirections",
                "AlbumFragmnet NotCintentSlug: $mediaType--$assetID--$contentSlug"
            )
            assetDetailsByID
        } else {
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                    getString(R.string.popup_error)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                    getString(R.string.popup_this_content_not_available)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                ), ""
            )
        }

    }

    private fun getIntentData() {
        contentSlug = arguments?.getString(AppConstants.BUNDLE_CONTENT_SLUG) ?: ""
        mediaType = arguments?.getString(AppConstants.BUNDLE_ASSET_TYPE) ?: ""
        assetID = arguments?.getString(AppConstants.BUNDLE_ASSET_ID)?:""

        Log.d("branchRedirections", "AlbumFragmnet: $mediaType--$assetID--$contentSlug")


    }

    private fun setViewModel() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]

    }

    override fun onPlayClick(playQueueItems:Boolean?) {
        if (isLoggedIn) {
            KsPreferenceKeys.getInstance().setShuffleEnable(false)
            KsPreferenceKeys.getInstance().setRepeatEnable(false)
            if (!songList.isNullOrEmpty()) {
                if (featureFlagModel?.featureFlag?.IS_SPONSOR==true) {
                    if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()) {
                        if (!ArtistAndSponserActivity.AppState.isSkipped) {
                            ActivityLauncher.getInstance().artistAndSponserActivity(requireActivity(), ArtistAndSponserActivity::class.java)
                        } else {
                            initiatePlayback(playQueueItems)
                            ArtistAndSponserActivity.AppState.isSkipped = false
                        }
                    } else {
                        ArtistAndSponserActivity.AppState.isSkipped = false
                        initiatePlayback(playQueueItems)
                    }
                }else{
                    initiatePlayback(playQueueItems)
                }
            }
        } else {
            ActivityLauncher.getInstance().loginActivity(
                requireActivity(), ActivityLogin::class.java, ""
            )
        }

    }

    private fun initiatePlayback(playQueueItems: Boolean?) {
        if (songList[0].isPremium) {
                        // onSongItemClick!!.songItemClick(songList,"Y4SfmXMB",songList[0].id)
            checkEntitlement(token, songList[0], AppCommonMethod.getImageContent(songList[0]), playQueueItems)
        } else {
            if (onSongItemClick != null) {
                onSongItemClick!!.songItemClick(
                    songList,
                    songList[0],
                    songList[0].externalRefId,
                    AppCommonMethod.getImageContent(songList[0]),
                    AppCommonMethod.getSongsPosterImageUrl(songList[0]),
                    playQueueItems
                )
            }
        }
    }

    override fun onShuffle() {
        if (isLoggedIn) {
            KsPreferenceKeys.getInstance().setRepeatEnable(false)
            if (!songList.isNullOrEmpty()) {
                if (songList[0].isPremium) {
                    // onSongItemClick!!.songItemClick(songList,"Y4SfmXMB",songList[0].id)
                    checkEntitlement(token, songList[0], AppCommonMethod.getImageContent(songList[0]), false)
                } else {
                    if (onSongItemClick != null) {
                        onSongItemClick!!.songItemClick(
                            songList,
                            songList[0],
                            songList[0].externalRefId,
                            AppCommonMethod.getImageContent(songList[0]),
                            AppCommonMethod.getSongsPosterImageUrl(songList[0]),
                            false
                        )
                    }
                }
            }
        } else {
            ActivityLauncher.getInstance().loginActivity(
                requireActivity(), ActivityLogin::class.java, ""
            )
        }

    }

    override fun onDetach() {
        super.onDetach()
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).toolFrame(0)
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).detailFrameVisibility()
    }

    override fun onSongClick(song: DataItem) {
        if (isLoggedIn) {
            preference?.setShuffleEnable(false)
            KsPreferenceKeys.getInstance().setRepeatEnable(false)
            if (featureFlagModel?.featureFlag?.IS_SPONSOR==true) {
                if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()) {
                    if (!ArtistAndSponserActivity.AppState.isSkipped) {
                        ActivityLauncher.getInstance().artistAndSponserActivity(requireActivity(), ArtistAndSponserActivity::class.java)
                    } else {
                        initiateSingleItemPlayback(song)
                        ArtistAndSponserActivity.AppState.isSkipped = false
                    }
                } else {
                    ArtistAndSponserActivity.AppState.isSkipped = false
                    initiateSingleItemPlayback(song)
                }
            }else{
                initiateSingleItemPlayback(song)
            }
        } else {
            ActivityLauncher.getInstance().loginActivity(
                requireActivity(), ActivityLogin::class.java, ""
            )
        }
    }

    private fun initiateSingleItemPlayback(song: DataItem) {
        if (song.isPremium) {
            // onSongItemClick!!.songItemClick(songList,"Y4SfmXMB",songList[0].id)
            checkEntitlement(token, song, AppCommonMethod.getImageContent(song), false)
        } else {
            if (onSongItemClick != null) {
                onSongItemClick!!.songItemClick(
                    songList,
                    song,
                    song.externalRefId,
                    AppCommonMethod.getImageContent(song),
                    AppCommonMethod.getSongsPosterImageUrl(song),
                    false
                )
            }
        }
    }

    override fun onThreeDotClick(song: DataItem) {
        val bottomSheetDialog = BottomDialogFragment.getInstance(song,"")
        thumbnailImageUrl?.let {
            bottomSheetDialog.setDefaultImgOfTopPoster(it)
        }
        bottomSheetDialog.show(activity?.supportFragmentManager!!, "Bottom Sheet Dialog Fragment")
    }

    override fun onDeleteItemClick(song: DataItem) {
    }

    override fun onActionBtnClicked() {
        if (isUserNotEntitle) {
            ActivityLauncher.getInstance().goToDetailPlanScreen(activity, PaymentDetailPage::class.java, true, resEntitle)
        }
    }

    override fun onCancelBtnClicked() {

    }

    private fun parseColor() {
        binding!!.stringData = stringsHelper
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        try {
            onSongItemClick = context as OnSongItemClick
            playerListener = context as MainPlayerActivity.PlayerListener
        } catch (e: Exception) {
            e.printStackTrace()
        }
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).toolFrame(1)
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).detailFrameVisibility()
    }

    private fun checkEntitlement(token: String?, song: DataItem, imageContent: ImageContent?, playQueueItems: Boolean?) {
        binding?.pBar?.visibility = View.VISIBLE
        if (isLoggedIn) {
            railInjectionHelper?.hitApiEntitlement(token, song.sku)?.observe(this@AlbumFragment) { responseEntitle ->
                binding!!.pBar.visibility = View.GONE
                if (responseEntitle != null && responseEntitle.data != null) {
//                    responseEntitle?.data?.maxAllowedConcurrency?.let {  preference?.planMaxAllowedConcurrency = it  }
                    resEntitle = responseEntitle
                    if (responseEntitle.data.entitled) {
                        railInjectionHelper?.externalRefID(responseEntitle.data?.accessToken, responseEntitle.data?.sku)?.observe(this) { drm ->
                            if (onSongItemClick != null) {
                                onSongItemClick!!.songItemClick(
                                    songList,
                                    song,
                                    drm.data?.externalRefId!!,
                                    imageContent,
                                    AppCommonMethod.getSongsPosterImageUrl(song),
                                    playQueueItems
                                )
                            }
                        }
                    } else {
                        isUserNotEntitle = true
                        binding!!.pBar.visibility = View.GONE
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                                getString(R.string.popup_notEntitled)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_select_plan.toString(),
                                getString(R.string.popup_select_plan)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_purchase.toString(),
                                getString(R.string.popup_purchase)
                            ), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_cancel.toString(), getString(R.string.popup_cancel))
                        )
                    }
                } else {
                    binding!!.pBar.visibility = View.GONE
                    if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                        //  clearCredientials(preference)
                        ActivityLauncher.getInstance().loginActivity(
                            requireActivity(), ActivityLogin::class.java, ""
                        )
                    } else {
                        binding!!.pBar.visibility = View.GONE
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            ), ""
                        )
                    }
                }
            }
        } else {
            ActivityLauncher.getInstance().loginActivity(
                requireActivity(), ActivityLogin::class.java, ""
            )
        }

    }

    private fun commonDialog(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = requireActivity().supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }
}



