package com.enveu.fragments.album.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.callbacks.player.callback.SongClick
import com.enveu.databinding.SongsLayoutItemBinding
import com.enveu.player.utils.TimeUtils
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ImageHelper

class SongsAdapter() : RecyclerView.Adapter<SongListViewHolder>() {
    private var songList: MutableList<DataItem>? = null
    private var tempSongList: MutableList<DataItem>? = null
    private var onSongClick: SongClick? = null
    private var listner: SongsItemClick? = null
    private var featureFlag : FeatureFlagModel?= null
    private var watchList:MutableList<EnveuVideoItemBean>?=null
    private var redirection:String=""
    private var topContentImgrl=""
    private var songImg=""
    private var isQueue = false
    private var playingIndex = 0

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SongListViewHolder {
        return SongListViewHolder(SongsLayoutItemBinding.inflate(LayoutInflater.from(parent.context),parent,false))
    }

    constructor(songList: MutableList<DataItem>, onSongClick: SongClick , featureFlag : FeatureFlagModel,isQueue:Boolean,playingIndex:Int) : this() {
        this.songList = songList
        this.playingIndex=playingIndex
        this.onSongClick = onSongClick
        this.featureFlag = featureFlag
        this.isQueue = isQueue
    }
    constructor(watchList: MutableList<EnveuVideoItemBean>, listner: SongsItemClick,redirection:String):this(){
        this.watchList=watchList
        this.listner=listner
        this.redirection=redirection
    }

    override fun getItemCount(): Int {
        if (redirection == AppConstants.PLAYLISTDETAIL){
            return watchList!!.size
        }else {
            return songList!!.size
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addItems(newItems: MutableList<DataItem>?) {
        val startPos = songList?.size
        songList?.addAll(newItems!!)
        notifyItemRangeInserted(startPos!!, newItems?.size!!)
        notifyDataSetChanged()
    }


    override fun onBindViewHolder(holder: SongListViewHolder, position: Int) {
        if (redirection == AppConstants.PLAYLISTDETAIL){
            holder.binding.textSongName.text = watchList!![position].title
//            var artistName = ""
//            watchList!![position].description?.forEachIndexed { index, item ->
//                artistName += "${item}, "
//            }
//            artistName.isNotEmpty().let {
//                if (it){ holder.binding.textArtistName.text = artistName }else{ holder.binding.textArtistName.visibility=View.GONE}
//            }
            if (!watchList!![position].description.isNullOrEmpty()){
                holder.binding.textArtistName.text = watchList!![position].description
            }else{
                holder.binding.textArtistName.visibility=View.GONE
            }
           // val songImg=AppCommonMethod.getImageContent(watchList!![0].images)
//            val songImg=watchList!![0].imageContent.src
//            if (!songImg.isNullOrEmpty() && watchList!![0].images !=null){
//                ImageHelper.getInstance(holder.binding.albumImage.context)
//                    .loadListSQRImageForAlbumList(holder.binding.albumImage, watchList!![0].images[0].src)
                try {
                    if (watchList!![position].imageContent != null && watchList!![position].imageContent.src != null) {
                        songImg = watchList!![position].imageContent.src
                    }
                    if (songImg.isNotEmpty() && watchList!![position].images !=null){
                        ImageHelper.getInstance(holder.binding.albumImage.context)
                            .loadListSQRImageForAlbumList(holder.binding.albumImage, watchList!![position].images[0].src)
                    }else {
                        watchList!![position].posterURL?.let {
                            ImageHelper.getInstance(holder.binding.albumImage.context)
                                .loadListSQRImageForAlbumList(holder.binding.albumImage, it)
                        } ?: {
                            if (topContentImgrl.isNotEmpty()) {
                                ImageHelper.getInstance(holder.binding.albumImage.context)
                                    .loadListSQRImageForAlbumList(holder.binding.albumImage, topContentImgrl)
                            }
                        }
                    }
                } catch (e:Exception){

                }
        }else {
            holder.binding.textSongName.text = songList!![position].title
            var artistName = ""
            songList!![position].customData?.songsArtistIds?.forEachIndexed { index, item ->
                if (index == songList!![position].customData?.songsArtistIds?.size?.minus(1)) {
                    artistName += item.title
                } else {
                    artistName += "${item.title}, "
                }
            }
            if (artistName.isNotEmpty() && artistName.isNotBlank()) {
                holder.binding.textArtistName.text = artistName
            }else{
                holder.binding.textArtistName.visibility=View.GONE
            }
            if (songList!![position].imageContent !=null && songList!![position].imageContent.src !=null) {
                songImg=songList!![position].imageContent.src
            }
            if (!songImg.isNullOrEmpty()){
                ImageHelper.getInstance(holder.binding.albumImage.context)
                    .loadListSQRImageForAlbumList(holder.binding.albumImage,songImg)
            }else if (songList!![position].customData !=null && songList!![position].customData.songsAlbumsId!=null && songList!![position].customData.songsAlbumsId?.images?.isNotEmpty() == true) {
                val posterUrl: String = songList!![position].customData.songsAlbumsId.images[0].src
                ImageHelper.getInstance(holder.binding.albumImage.context)
                    .loadListSQRImageForAlbumList(holder.binding.albumImage, posterUrl)
            }else if (topContentImgrl.isNotEmpty()){
                ImageHelper.getInstance(holder.binding.albumImage.context)
                    .loadListSQRImageForAlbumList(holder.binding.albumImage, topContentImgrl)
            }
//        if (featureFlag?.featureFlag?.PLAY_COUNT == true){
//            holder.binding.textArtistName.text = AppCommonMethod.formatViewCount(songList!![position].playCount)+" Plays." +" "+ artistName
//        }else{
//            holder.binding.textArtistName.text = artistName
//        }
            songList?.get(position)?.audioContent?.duration?.let { duration ->
                holder.binding.songDuration.text = TimeUtils.formatDurationJw(duration.toLong() / 1000)

            }
            if (null!= songList!![position].customData) {
                // holder.binding.textRowNumber.text = songList!![position].customData.trackNumber
            }
        }
        holder.binding.linearLayoutView.setOnClickListener {
            if (redirection == AppConstants.PLAYLISTDETAIL){
                listner?.onSongsItemClick(watchList!![position], watchList!![position].isPremium, position
                )
            }else{
                songList?.get(position)?.let {
                    onSongClick!!.onSongClick(it)
                }
            }
        }
        if (isQueue) {
            val context = holder.binding.textSongName.context
            if (playingIndex == position) {

                holder.binding.textSongName.setTextColor(Color.parseColor("#FFFFFF"))
                holder.binding.gif.visibility=View.GONE
                holder.binding.dot.visibility = View.GONE
                holder.binding.delete.visibility = View.VISIBLE


//                holder.binding.textSongName.setTextColor(context.getColor(R.color.main_btn_selected_bg_color)) // App theme color
//                holder.binding.dot.visibility = View.GONE
//                holder.binding.delete.visibility = View.GONE
//                holder.binding.gif.setColorFilter(holder.binding.gif.context.getColor(R.color.main_btn_selected_bg_color))
//                Glide.with(holder.binding.gif.context)
//                    .asGif() // Ensure the content is treated as GIF
//                    .load(R.drawable.sound_blue_audio_wave) // Or a local resource
//                    .into(holder.binding.gif)
//                holder.binding.gif.visibility=View.VISIBLE
//                /*   Glide.with(OttApplication.context)
//                       .asGif() // Ensure the content is treated as GIF
//                       .load(R.drawable.sound_gif) // Or a local resource
//                       .into(holder.binding.delete)*/
            } else {
                holder.binding.textSongName.setTextColor(Color.parseColor("#FFFFFF"))
                holder.binding.gif.visibility=View.GONE
                holder.binding.dot.visibility = View.GONE
                holder.binding.delete.visibility = View.VISIBLE
            }
        } else {
            holder.binding.textSongName.setTextColor(Color.parseColor("#FFFFFF"))
            holder.binding.gif.visibility=View.GONE
            holder.binding.dot.visibility = View.VISIBLE
            holder.binding.delete.visibility = View.GONE
        }
        holder.binding.delete.setOnClickListener{
            songList?.get(position)?.let {
                onSongClick?.onDeleteItemClick(it)
            }
        }
        holder.binding.dot.setOnClickListener {
            if (redirection.equals(AppConstants.PLAYLISTDETAIL)){
                val oth=watchList
                oth?.get(position)?.let {
                    listner?.onMoreClick(it)
                }
            }else{
                songList?.get(position)?.let {
                    onSongClick?.onThreeDotClick(it)
                }
            }
        }
    }
    fun setThumbnailImgOfPoster(imgUrl: String) {
        topContentImgrl=imgUrl
    }

}

interface SongsItemClick {
    fun onSongsItemClick(assetId: EnveuVideoItemBean?, isPremium: Boolean, position: Int)
    fun onMoreClick(itemDetail:EnveuVideoItemBean)

}

class SongListViewHolder(val binding: SongsLayoutItemBinding) :
    RecyclerView.ViewHolder(binding.root) {

}
