package com.enveu.fragments.artist

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ArtistListResponse
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.callbacks.player.callback.SongClick
import com.enveu.callbacks.player.callback.SongClickListener
import com.enveu.databinding.FragmentArtistAllSongsListBinding
import com.enveu.fragments.artist.adapter.ArtistSongsAdapter
import com.enveu.utils.PaginationScrollListener
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.google.android.exoplayer2.util.Log
import com.google.gson.Gson

class ArtistAllSongsListFragment : BaseBindingFragment<FragmentArtistAllSongsListBinding>(), SongClick {
    var railInjectionHelper: RailInjectionHelper? = null
    var songList: ArrayList<DataItem> = ArrayList()
    private lateinit var adapter: ArtistSongsAdapter
    private var featureFlag : FeatureFlagModel?= null
    private var assetID = ""
    private var thumbnailImageUrl = ""
    private var isFromGenre = false
    private var genres = ""
    private var genresId = ""
    private var redirectFrom = ""
    private var preference: KsPreferenceKeys? = null
    private var onSongItemClick: OnSongItemClick? = null
    private var songClick : SongClickListener? = null
    private var totalPages : Int = 0
    private var assetId = ""

    override fun inflateBindingLayout(inflater: LayoutInflater): FragmentArtistAllSongsListBinding {
        return FragmentArtistAllSongsListBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        railInjectionHelper = ViewModelProvider(requireActivity())[RailInjectionHelper::class.java]
        onSongItemClick = context as OnSongItemClick

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        preference = KsPreferenceKeys.getInstance()
        featureFlag = AppConfigMethod.parseFeatureFlagList()
        thumbnailImageUrl = arguments?.getString(AppConstants.THUMBNAIL_IMG).toString()
        assetID = arguments?.getString(AppConstants.BUNDLE_ASSET_ID) ?: ""
        genres = arguments?.getString("genres", genres).toString()
        isFromGenre = arguments?.getBoolean("isFromGenre", isFromGenre) == true
        genresId = arguments?.getString("genreIds", genresId).toString()
        redirectFrom = arguments?.getString("redirectFrom", redirectFrom).toString()
        setToolBar()
        if (isFromGenre){
            setSongsListingFromGenreClick(assetID, 0, 20, genresId)
        }else{
            setSongsListing(assetID, 0, 20)
        }
    }

    private fun setToolBar() {
        binding!!.toolbar.logoMain2.visibility = View.GONE
        binding!!.toolbar.backLayout.visibility = View.VISIBLE
        binding!!.toolbar.rlToolBar.visibility = View.VISIBLE
        binding!!.toolbar.titleMid.visibility = View.VISIBLE
        binding!!.toolbar.searchIcon.visibility = View.GONE
        binding!!.toolbar.clNotification.visibility = View.GONE
        binding!!.toolbar.titleMid.setBackgroundResource(0)
        binding!!.toolbar.backLayout.setOnClickListener { parentFragmentManager.popBackStack() }
        if (isFromGenre){
            binding!!.toolbar.titleMid.text = genres
        }else{
            binding!!.toolbar.titleMid.text = "Songs"
        }
    }

    fun parseFeatureFlagList(): FeatureFlagModel {
        val featureFlagConfigString = KsPreferenceKeys.getInstance().getString(
            KsPreferenceKeys.FEATURE_FLAG_CONFIG, "")
        return Gson().fromJson(featureFlagConfigString, FeatureFlagModel::class.java)
    }
    private fun setSongsListing(assetId : String, pageNumber : Int, pageSize : Int){
        binding.pBar.visibility = View.VISIBLE
        railInjectionHelper?.getAssetDetailsV3Api(assetId, pageNumber, pageSize)?.observe(viewLifecycleOwner) { assetResponse ->
            binding.pBar.visibility = View.GONE
            if (assetResponse != null && !assetResponse.data?.items.isNullOrEmpty()) {
                assetResponse.data?.items?.let { songList.addAll(it) }
                Log.d("assetResponseDataSize", assetResponse.data?.items.toString())
                totalPages = assetResponse.data?.totalPages?:0
                if (::adapter.isInitialized){
                    adapter.notifyDataSetChanged()
                }else {
                    setSongsListingAdapter(assetResponse)
                }
            }
        }
    }

    private fun setSongsListingFromGenreClick(assetId : String, pageNumber : Int, pageSize : Int, genresId : String){
        binding.pBar.visibility = View.VISIBLE
        val assetIds = if (redirectFrom == "albumFragment") {
            "songs-albums-id|OR:$assetId"
        } else {
            "songs-artist-ids|OR:LIKE|$assetId"
        }
        railInjectionHelper?.getAssetDetailsV3Api(assetIds, pageNumber, pageSize, genresId)?.observe(viewLifecycleOwner) { assetResponse ->
            binding.pBar.visibility = View.GONE
            if (assetResponse != null && !assetResponse.data?.items.isNullOrEmpty()) {
                assetResponse.data?.items?.let { songList.addAll(it) }
                Log.d("assetResponseDataSize", assetResponse.data?.items.toString())
                totalPages = assetResponse.data?.totalPages?:0
                if (::adapter.isInitialized){
                    adapter.notifyDataSetChanged()
                }else {
                    setSongsListingAdapter(assetResponse)
                }
            }
        }
    }

    private fun setSongsListingAdapter(assetResponse: ArtistListResponse) {
        val layoutManager = LinearLayoutManager(requireActivity())
        binding.songsListingRv.layoutManager = layoutManager
        if (assetResponse.data != null) {
            adapter = featureFlag?.let {
                ArtistSongsAdapter(songList as MutableList<DataItem>, this@ArtistAllSongsListFragment, featureFlag = it,false)
            }!!
            binding.songsListingRv.adapter = adapter
            }
           thumbnailImageUrl.let { adapter.setThumbnailImgOfPoster(it) }

           binding.songsListingRv.addOnScrollListener(object : PaginationScrollListener(layoutManager){
               override fun onLoadMore(page: Int) {
                   if (totalPages > page) {
                       if (isFromGenre){
                           setSongsListingFromGenreClick(assetID, 0, 20, genresId)
                       }else{
                           setSongsListing(assetID, 0, 20)
                       }
                   }
               }
           })

        }

    override fun onSongClick(song: DataItem) {
        songClick?.onSongItemClick(song)
    }

    override fun onThreeDotClick(song: DataItem) {
        songClick?.onSongThreeDotClick(song)
    }

    override fun onDeleteItemClick(song: DataItem) {

    }

    fun songItemListenerCallback(songClick: SongClickListener){
        this.songClick = songClick
    }

}