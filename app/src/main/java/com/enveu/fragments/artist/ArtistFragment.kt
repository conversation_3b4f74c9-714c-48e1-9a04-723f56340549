package com.enveu.fragments.artist

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.enveu.R
import com.enveu.activities.articleContent.WebViewActivity
import com.enveu.activities.detail.ui.DetailActivity
import com.enveu.activities.detail.viewModel.DetailViewModel
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.mainPLayer.MainPlayerActivity
import com.enveu.activities.sponsorArtist.ArtistAndSponserActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.PaymentDetailPage
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseActivity
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.RelatedRailsCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.ArtistSongIdItem
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ArtistListResponse
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent
import com.enveu.callbacks.player.callback.OnAudioItemClickInteraction
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.callbacks.player.callback.SongClick
import com.enveu.callbacks.player.callback.SongClickListener
import com.enveu.databinding.FragmentAlbumBinding
import com.enveu.fragments.artist.adapter.ArtistSongsAdapter
import com.enveu.fragments.artist.adapter.ClickItem
import com.enveu.fragments.artist.adapter.RelatedArtistContentAlbum
import com.enveu.fragments.artist.adapter.RelatedArtistContentArticle
import com.enveu.fragments.artist.adapter.RelatedArtistContentSingles
import com.enveu.fragments.artist.adapter.RelatedArtistContentVideo
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.player.ui.AudioInteractionFragment
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.BottomDialogFragment
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.SpacingItemDecoration
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson


class ArtistFragment : BaseBindingFragment<FragmentAlbumBinding>(), SongClick,
    CommonDialogFragment.EditDialogListener,
    OnAudioItemClickInteraction {
    private lateinit var adapter: ArtistSongsAdapter
    private var contentSlug = ""
    private var assetID = ""
    private var mediaType = ""
    private var videoDetails: EnveuVideoItemBean? = null
    var songList: List<DataItem> = ArrayList()
    private var token: String? = null
    private var preference: KsPreferenceKeys? = null
    private var resEntitle: ResponseEntitle? = null
    private var playerListener: MainPlayerActivity.PlayerListener? = null
    private val stringsHelper by lazy { StringsHelper }
    private var isUserNotEntitle = false
    private var onSongItemClick: OnSongItemClick? = null
    var railInjectionHelper: RailInjectionHelper? = null
    private var viewModel: DetailViewModel? = null
    private var audioInteractionFragment: AudioInteractionFragment? = null
    private var featureFlag : FeatureFlagModel?= null
    private var genres = mutableListOf<Int>()
    private var thumbnailImageUrl :String?= null
    private var artistAllSongsListFragment: ArtistAllSongsListFragment? = null
    private var isLoggedIn :Boolean = false
    private var geners = ""
    private var isFromGenre : Boolean = false
    private var genresId = mutableListOf<Int>()
    private var containGenresId = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (activity != null && activity is HomeActivity)
            (activity as HomeActivity).toolFrame(1)
        (activity as HomeActivity).detailFrameVisibility()
        preference = KsPreferenceKeys.getInstance()
        featureFlag = AppConfigMethod.parseFeatureFlagList()
        token = preference?.appPrefAccessToken
        if (preference?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
        }

    }

    private val assetDetailsByID: Unit
        get() {
            railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
            railInjectionHelper!!.getAssetDetailsV3(assetID).observe(requireActivity()) { assetResponse: ResponseModel<*>? ->
                    if (assetResponse != null) {
                        val gson = Gson()
                        val json = gson.toJson(assetResponse.baseCategory)
                        Log.w("getAssetDetailsV2-->>>>>", json)
                        if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {

                        } else if (assetResponse.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                            parseDetail(assetResponse)
                        } else if (assetResponse.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                            if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {
                                commonDialog(
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                        getString(R.string.popup_error)
                                    ),
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                        getString(R.string.popup_something_went_wrong)
                                    ),
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                        getString(R.string.popup_continue)
                                    ), ""
                                )
                            }
                        } else if (assetResponse.status.equals(
                                APIStatus.FAILURE.name,
                                ignoreCase = true
                            )
                        ) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.popup_something_went_wrong)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                ), ""
                            )
                        }
                    }
                }
        }


    private val assetDetails: Unit
        get() {
            railInjectionHelper!!.getAssetDetailsbySlug(if (!contentSlug.isNullOrEmpty()) contentSlug else assetID)
                .observe(requireActivity()) { assetResponse: ResponseModel<*>? ->
                    if (assetResponse != null) {
                        if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                            //Do nothing
                        } else if (assetResponse.status.equals(
                                APIStatus.SUCCESS.name,
                                ignoreCase = true
                            )
                        ) {
                            stopShimmer()
                            parseDetail(assetResponse)
                        } else if (assetResponse.status.equals(
                                APIStatus.ERROR.name,
                                ignoreCase = true
                            )
                        ) {
                            if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {

                            }
                        } else if (assetResponse.status.equals(
                                APIStatus.FAILURE.name,
                                ignoreCase = true
                            )
                        ) {

                        }
                    }
                }
        }

    private fun parseDetail(assetResponse: ResponseModel<*>) {
        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
        if (enveuCommonResponse.enveuVideoItemBeans.size > 0) {
            videoDetails = enveuCommonResponse.enveuVideoItemBeans[0]
            videoDetails?.genres?.forEach {
                genres.add(it?.id!!)
            }
            AnalyticsUtils.trackScreenView(
                context,
                AppConstants.CONTENT_DETAIL + " - " + videoDetails?.title
            )
        }
        setUiData()
        getSongIds()
//        videoDetails?.artistSongId?.let {
//            getSongIds(it)
//        }
    }

    private fun setArtistAllSongsListing(){
        val songClick = object : SongClickListener{
            override fun onSongItemClick(song: DataItem) {
                onSongClick(song)
            }

            override fun onSongThreeDotClick(song: DataItem) {
                onThreeDotClick(song)
            }

        }
        val bundle = Bundle()
        bundle.putString(AppConstants.BUNDLE_ASSET_ID, assetID)
        bundle.putString(AppConstants.THUMBNAIL_IMG,thumbnailImageUrl)
        bundle.putString("genres", geners)
        bundle.putBoolean("isFromGenre", isFromGenre)
        bundle.putString("genreIds", containGenresId)
        bundle.putString("redirectFrom", "artistFragment")
        val transaction = requireActivity().supportFragmentManager.beginTransaction()
        artistAllSongsListFragment = ArtistAllSongsListFragment()
        artistAllSongsListFragment?.arguments = bundle
        artistAllSongsListFragment?.songItemListenerCallback(songClick)
        transaction.add(R.id.content_frame, artistAllSongsListFragment!!, "23").addToBackStack(null)
            .commit()
    }

    private fun setUserInteractionFragment(id: Int) {
        val transaction = childFragmentManager.beginTransaction()
        val args = Bundle()
        args.putInt(AppConstants.BUNDLE_ASSET_ID, id)
        args.putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
        args.putSerializable(AppConstants.BUNDLE_SERIES_DETAIL, videoDetails)
        args.putString(AppConstants.BUNDLE_CONTENT_SLUG, contentSlug)
        args.putString(AppConstants.THUMBNAIL_IMG,thumbnailImageUrl)
        args.putString(AppConstants.AUDIO_INTERACTION_REDIRECTION, AppConstants.ARTISTS)
        args.putBoolean(AppConstants.SHOULD_PLAY_QUEUE_ITEM, true)
        args.putString(AppConstants.BUNDLE_BIO_TITLE,videoDetails?.title)
        args.putString(AppConstants.BIO_DISCRIPTION,videoDetails?.longDescription)
        audioInteractionFragment = AudioInteractionFragment()
        audioInteractionFragment!!.arguments = args
        audioInteractionFragment!!.passInstance(this)
        setDataModleandSendToAudioInteractionFrag()
        transaction.replace(R.id.fragment_audio_interaction, audioInteractionFragment!!)
        transaction.commit()
    }
    private fun setDataModleandSendToAudioInteractionFrag() {
        val gson=Gson()
        val json=gson.toJson(videoDetails)
        val songDetail:DataItem=gson.fromJson(json,DataItem::class.java)
        audioInteractionFragment?.setAttachedFragmentDetails(songDetail)
    }
    private fun callShimmer() {
        binding.seriesShimmer.visibility = View.VISIBLE
        binding.mShimmer.seriesShimmerScroll1.isEnabled = false
        binding.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding.mShimmer.sfShimmer1.startShimmer()
        binding.mShimmer.sfShimmer2.startShimmer()
        binding.mShimmer.sfShimmer3.startShimmer()
        binding.mShimmer.flBackIconImage.bringToFront()
    }

    private fun stopShimmer() {
        binding.seriesShimmer.visibility = View.GONE
        binding.mShimmer.sfShimmer1.startShimmer()
        binding.mShimmer.sfShimmer2.startShimmer()
        binding.mShimmer.sfShimmer3.startShimmer()
    }

    override fun onDetach() {
        super.onDetach()
        if (activity != null && activity is HomeActivity) {
            (activity as HomeActivity).toolFrame(0)
            (activity as HomeActivity).detailFrameVisibility()
        }
    }

    private fun getSongIds(artistSongId: List<ArtistSongIdItem>) {
        var songIds = ""
        artistSongId.forEach {
            songIds += "${it.id},"
        }
        getSongsForArtist(assetID, 0, 100)
        setRelatedRails()
    }
    private fun getSongIds(){
        getSongsForArtist(assetID, 0, 100)
        setRelatedRails()
    }

    private fun setUiData() {
        binding.contentTitle.text = videoDetails?.title
        binding.description.visibility=View.GONE
        val metadetails=getMetaDetails(videoDetails!!)

        binding.genre.text=metadetails
        thumbnailImageUrl = videoDetails?.imageContent?.src
        videoDetails?.imageContent?.src?.let {
            ImageHelper.getInstance(binding?.imgscr?.context)
                .loadListImage(binding.imgscr, it)

        }

        binding.llRootTopChild.background = AppCommonMethod.setGradientBackgroundColor(
            Color.parseColor(
                AppCommonMethod
                    .getDominantColor(videoDetails?.imageContent)
            ),
            Color.parseColor("#0000006b"),
            Color.parseColor("#00000000"),
            "TOP_TO_BOTTOM"
        )
        if (isAdded && context != null) {
            setUserInteractionFragment(videoDetails!!.id)
        }

    }

    private fun getMetaDetails(videoDetails: EnveuVideoItemBean): CharSequence {
        val metaDetail=StringBuilder()
        videoDetails.genres?.forEachIndexed { index, genre ->
            if (index == videoDetails.customData?.genres?.size?.minus(1)) {
                geners += genre.title
            }else {
                geners += "${genre.title}, "
            }
        }
        videoDetails.sub_genres?.forEachIndexed{ index,sub_gener->
            if (index == videoDetails.sub_genres?.size?.minus(1)) {
                geners += "${sub_gener.title}"
            } else {
                geners += "${sub_gener.title}, "
            }
        }
        if (geners.isNotEmpty()){
            metaDetail.appendLine(geners)
        }
        videoDetails.genres?.forEachIndexed { index, genre ->
            genre.id?.let { genresId.add(it) }
        }
        videoDetails.sub_genres?.forEachIndexed { index, subGenre ->
            subGenre.id?.let { genresId.add(it) }
        }
        if (!videoDetails.year.isNullOrEmpty() && !videoDetails.year.equals("0")){
            metaDetail.appendLine(videoDetails.year)
        }
        return metaDetail
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        callShimmer()
        setViewModel()
        getIntentData()
        parseColor()
        setClicks()

        if (!contentSlug.isNullOrEmpty()) {
            assetDetails
        } else if (!assetID.isNullOrEmpty()) {
            assetDetailsByID
        } else {
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                    getString(R.string.popup_error)
                ), stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                    getString(R.string.popup_this_content_not_available)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                ), ""
            )
        }

    }

    override fun inflateBindingLayout(inflater: LayoutInflater): FragmentAlbumBinding {
        return FragmentAlbumBinding.inflate(inflater)
    }

    private fun setClicks() {
        ArtistAndSponserActivity.AppState.isSkipped = false
        binding.goBack.setOnClickListener {
            activity?.onBackPressed()
        }
        binding.showAllListButton.setOnClickListener {
            isFromGenre = false
            setArtistAllSongsListing()
        }
        if (featureFlag?.featureFlag?.IS_GENRES_CLICKABLE == true){
            binding.genre.setOnClickListener {
                isFromGenre = true

                genresId.forEachIndexed { index, genre ->
                    if (index == genresId.size.minus(1)) {
                        containGenresId += genre.toString()
                    }else {
                        containGenresId += "${genre}, "
                    }
                }
                setArtistAllSongsListing()
            }
        }
    }

    private fun getIntentData() {
        contentSlug = arguments?.getString(AppConstants.BUNDLE_CONTENT_SLUG) ?: ""
        assetID = arguments?.getString(AppConstants.BUNDLE_ASSET_ID) ?: ""
        mediaType = arguments?.getString(AppConstants.BUNDLE_ASSET_TYPE) ?: ""
    }

    private fun setViewModel() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        viewModel = ViewModelProvider(this)[DetailViewModel::class.java]
    }

    private fun getSongsForArtist(assetId : String, pageNumber : Int, pageSize : Int) {
        binding.showAllListButton.visibility = View.GONE
        railInjectionHelper?.getAssetDetailsV3Api(assetId, pageNumber, pageSize)?.observe(viewLifecycleOwner) { assetResponse ->
            if (assetResponse != null && assetResponse.data != null
                && assetResponse.data.items != null &&((assetResponse.data?.items?.size ?: 0) >= 4)){
                binding.showAllListButton.visibility = View.VISIBLE
            }else{
                binding.showAllListButton.visibility = View.GONE
            }
            if (assetResponse != null && !assetResponse.data?.items.isNullOrEmpty()) {
                setSongAdapter(assetResponse)
            }
        }
    }

    private fun setSongAdapter(assetResponse: ArtistListResponse) {
        if (songList != null && songList.isNotEmpty()) {
            (songList as ArrayList).clear()
        }
        if (assetResponse.data != null) {
            songList = assetResponse.data.items?:emptyList()
            Log.d("songList", "Artist: "+ songList.size.toString())

            // removeAllQueue()
            // Sort the songList by trackNumber if it exists
           /* (songList as MutableList<DataItem>?)?.sortWith(compareBy {
                it.customData.trackNumber?.toIntOrNull() ?: Int.MAX_VALUE
            })*/
            Log.d("songList", "Artist: "+ songList.size.toString())

            binding.tvMainTitle.text=getString(R.string.songs)
            binding.tvMainTitle.visibility=View.VISIBLE

            adapter = featureFlag?.let {
                ArtistSongsAdapter(songList as MutableList<DataItem>, this@ArtistFragment, featureFlag = it,true)
            }!!
            binding.myRecycleView.adapter = adapter

            thumbnailImageUrl?.let {
                adapter.setThumbnailImgOfPoster(it)
            }
        }
        audioInteractionFragment?.getAllSongsList(songList)

    }

    override fun onPlayClick(playQueueItems:Boolean?) {
        if (isLoggedIn) {
            KsPreferenceKeys.getInstance().setShuffleEnable(false)
            KsPreferenceKeys.getInstance().setRepeatEnable(false)
            if (!songList.isNullOrEmpty()) {
                if (featureFlag?.featureFlag?.IS_SPONSOR==true) {
                    if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()) {
                        if (!ArtistAndSponserActivity.AppState.isSkipped) {
                            ActivityLauncher.getInstance().artistAndSponserActivity(requireActivity(), ArtistAndSponserActivity::class.java)
                        } else {
                            initiatePlayback(playQueueItems)
                            ArtistAndSponserActivity.AppState.isSkipped = false
                        }
                    } else {
                        ArtistAndSponserActivity.AppState.isSkipped = false
                        initiatePlayback(playQueueItems)
                    }
                }else{
                    initiatePlayback(playQueueItems)
                }
            }
        } else {
            ActivityLauncher.getInstance().loginActivity(
                requireActivity(),
                ActivityLogin::class.java, ""
            )
        }

    }

    private fun initiatePlayback(playQueueItems: Boolean?) {
        if (songList[0].isPremium) {
            // onSongItemClick!!.songItemClick(songList,"Y4SfmXMB",songList[0].id)
            checkEntitlement(token, songList[0], AppCommonMethod.getImageContent(songList[0]), playQueueItems)
        } else {
            if (onSongItemClick != null) {
                val imageContent = AppCommonMethod.getImageContent(songList[0])
                onSongItemClick!!.songItemClick(
                    songList,
                    songList[0],
                    songList[0].externalRefId,
                    imageContent,
                    AppCommonMethod.getSongsPosterImageUrl(songList[0]),
                    playQueueItems
                )
            }
        }
    }

    override fun onShuffle() {
        if (isLoggedIn) {
            KsPreferenceKeys.getInstance().setRepeatEnable(false)
            if (!songList.isNullOrEmpty()) {
                if (songList[0].isPremium) {
                    // onSongItemClick!!.songItemClick(songList,"Y4SfmXMB",songList[0].id)
                    checkEntitlement(
                        token,
                        songList[0],
                        AppCommonMethod.getImageContent(songList[0]),
                        false
                    )
                } else {
                    if (onSongItemClick != null) {
                        val imageContent = AppCommonMethod.getImageContent(songList[0])
                        onSongItemClick!!.songItemClick(
                            songList,
                            songList[0],
                            songList[0].externalRefId,
                            imageContent,
                            AppCommonMethod.getSongsPosterImageUrl(songList[0]),
                            false
                        )
                    }
                }
            }
        } else {
            ActivityLauncher.getInstance().loginActivity(
                requireActivity(),
                ActivityLogin::class.java, ""
            )
        }

    }

    private fun checkEntitlement(
        token: String?,
        song: DataItem,
        imageContent: ImageContent?,
        playQueueItems: Boolean?
    ) {
        binding?.pBar?.visibility = View.VISIBLE
        if (isLoggedIn)  {
            railInjectionHelper?.hitApiEntitlement(token, song.sku)?.observe(this@ArtistFragment) { responseEntitle ->
                binding!!.pBar.visibility = View.GONE
                if (responseEntitle != null && responseEntitle.data != null) {
                    resEntitle = responseEntitle
//                    preference?.planMaxAllowedConcurrency = responseEntitle.data.maxAllowedConcurrency
                    if (responseEntitle.data.entitled) {
                        railInjectionHelper?.externalRefID(
                            responseEntitle.data?.accessToken,
                            responseEntitle.data?.sku
                        )?.observe(this) { drm ->
                            if (onSongItemClick != null) {
                                val imageContent1 = AppCommonMethod.getImageContent(songList[0])
                                onSongItemClick!!.songItemClick(
                                    songList,
                                    song,
                                    drm.data?.externalRefId!!,
                                    imageContent1,
                                    AppCommonMethod.getSongsPosterImageUrl(song),
                                    playQueueItems
                                )
                            }
                        }
                    } else {
                        binding!!.pBar.visibility = View.GONE
                        isUserNotEntitle = true
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                                getString(R.string.popup_notEntitled)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_select_plan.toString(),
                                getString(R.string.popup_select_plan)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_purchase.toString(),
                                getString(R.string.popup_purchase)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                                getString(R.string.popup_cancel)
                            )
                        )
                    }
                } else {
                    binding!!.pBar.visibility = View.GONE
                    if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                        //  clearCredientials(preference)
                        ActivityLauncher.getInstance().loginActivity(
                            requireActivity(),
                            ActivityLogin::class.java, ""
                        )
                    } else {
                        binding!!.pBar.visibility = View.GONE
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            ), ""
                        )
                    }
                }
            }

        } else {
            ActivityLauncher.getInstance().loginActivity(
                requireActivity(),
                ActivityLogin::class.java, ""
            )
        }

    }

    private fun commonDialog(title: String, description: String, cancelBtn : String, actionBtn: String) {
        val fm = requireActivity().supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstanceWithCancelBtn(title, description, cancelBtn, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun onThreeDotClick(song: DataItem) {
        val bottomSheetDialog = BottomDialogFragment.getInstance(song,"")
        thumbnailImageUrl?.let {
            bottomSheetDialog.setDefaultImgOfTopPoster(it)
        }
        bottomSheetDialog.show(activity?.supportFragmentManager!!, "Bottom Sheet Dialog Fragment")
    }

    override fun onDeleteItemClick(song: DataItem) {

    }

    override fun onActionBtnClicked() {
        if (isUserNotEntitle) {
            ActivityLauncher.getInstance().goToDetailPlanScreen(activity, PaymentDetailPage::class.java, true, resEntitle)
        }
    }

    override fun onCancelBtnClicked() {

    }


    private fun parseColor() {
        binding!!.stringData = stringsHelper
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        try {
            onSongItemClick = context as OnSongItemClick
            playerListener = context as MainPlayerActivity.PlayerListener
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    //    if (songList != null) {
//        if (songList[0].isPremium) {
//            onSongItemClick!!.songItemClick(songList,"Y4SfmXMB")
//            // checkEntitlement(token,songList[0].sku)
//        }else{
//            if (onSongItemClick != null) {
//                onSongItemClick!!.songItemClick(songList,"Y4SfmXMB")
//            }
//        }
//    }
    override fun onSongClick(song: DataItem) {
        if (isLoggedIn) {
            preference?.setShuffleEnable(false)
            KsPreferenceKeys.getInstance().setRepeatEnable(false)
            if (featureFlag?.featureFlag?.IS_SPONSOR==true) {
                if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()) {
                    if (!ArtistAndSponserActivity.AppState.isSkipped) {
                        ActivityLauncher.getInstance().artistAndSponserActivity(requireActivity(), ArtistAndSponserActivity::class.java)
                    } else {
                        initiateSingleItemPlayback(song)
                        ArtistAndSponserActivity.AppState.isSkipped = false
                    }
                } else {
                    ArtistAndSponserActivity.AppState.isSkipped = false
                    initiateSingleItemPlayback(song)
                }
            }else{
                initiateSingleItemPlayback(song)
            }
        } else {
            ActivityLauncher.getInstance().loginActivity(
                requireActivity(), ActivityLogin::class.java, ""
            )
        }
    }

    private fun initiateSingleItemPlayback(song: DataItem) {
        if (song.isPremium) {
            checkEntitlement(
                token,
                song,
                if (!song.images.isNullOrEmpty()) AppCommonMethod.getImageContent(song) else null, false)
        } else {
            if (onSongItemClick != null) {
                val imageContent = AppCommonMethod.getImageContent(songList[0])
                onSongItemClick!!.songItemClick(
                    songList,
                    song,
                    song.externalRefId,
                    imageContent,
                    AppCommonMethod.getSongsPosterImageUrl(song),
                    false
                )
            }
        }
    }

    private fun setRecycleView(data: RelatedRailsCommonData, type: String) {
        binding?.relatedArtistRV?.addItemDecoration(SpacingItemDecoration(12, SpacingItemDecoration.HORIZONTAL))
        binding?.tvArtistTitle?.text=getString(R.string.artist_title_txt)
        binding?.albumRelatedLayoutRV?.addItemDecoration(SpacingItemDecoration(12, SpacingItemDecoration.HORIZONTAL))
        binding?.albumWithArtistLayoutRV?.addItemDecoration(SpacingItemDecoration(12, SpacingItemDecoration.HORIZONTAL))
        binding?.tvAlbumTitle?.text=getString(R.string.ablbum_title_txt)
        binding?.relatedVideoRV?.addItemDecoration(SpacingItemDecoration(12, SpacingItemDecoration.HORIZONTAL))
        binding?.relatedArticleRV?.addItemDecoration(SpacingItemDecoration(12, SpacingItemDecoration.HORIZONTAL))
        if (type.equals("Artist", ignoreCase = true)) {
            binding?.relatedArtistRV?.setHasFixedSize(true)
            binding?.relatedArtistRV?.adapter =
                featureFlag?.let {
                    RelatedArtistContentArticle(data, it, object : ClickItem {
                        override fun moreRailItemClick(data: RelatedRailsCommonData.Data.Item, position: Int) {
                            data.contentSlug?.let { contentSlug ->
                                data.contentType?.let { contentType ->
                                    data.id?.let { id ->
                                        AppCommonMethod.launchArtistAndAlbum(
                                            requireContext(),"ARTISTS",
                                            contentSlug, id
                                        )
                                    }
                                }
                            }
                        }
                    })
                }
        }
        if (type.equals("Album", ignoreCase = true)) {
            binding?.albumRelatedLayoutRV?.setHasFixedSize(true)
            binding?.albumRelatedLayoutRV?.adapter = RelatedArtistContentAlbum(data, featureFlag, object : ClickItem {
                override fun moreRailItemClick(data :RelatedRailsCommonData.Data.Item, position: Int) {
                    data.contentSlug?.let { contentSlug ->
                            data.id?.let { id ->
                                AppCommonMethod.launchArtistAndAlbum(
                                    requireContext(),"ALBUMS",
                                    contentSlug, id
                                )
                            }
                    }
                }
            })
        }
        if (type.equals("AlbumWithArtist", ignoreCase = true)) {
            binding?.albumWithArtistLayoutRV?.setHasFixedSize(true)
            binding?.albumWithArtistLayoutRV?.adapter = RelatedArtistContentAlbum(data, featureFlag, object : ClickItem {
                override fun moreRailItemClick(data :RelatedRailsCommonData.Data.Item, position: Int) {
                    data.contentSlug?.let { contentSlug ->
                        data.id?.let { id ->
                            AppCommonMethod.launchArtistAndAlbum(
                                requireContext(),"ALBUMS",
                                contentSlug, id
                            )
                        }
                    }
                }
            })
        }
        if (type.equals("Video", ignoreCase = true)) {
            binding?.relatedVideoRV?.setHasFixedSize(true)
            binding?.relatedVideoRV?.adapter = RelatedArtistContentVideo(data,featureFlag ,object : ClickItem {
                override fun moreRailItemClick(data :RelatedRailsCommonData.Data.Item , position: Int) {
                        if (featureFlag?.featureFlag?.IS_ENT_MUSIC_ENABLED == true) {
                            (context as? HomeActivity)?.dismissMiniPlayer()
                        }
                        ActivityLauncher.getInstance().detailScreenBrightCove(context as BaseActivity, DetailActivity::class.java, data.id!!,false, "", "")
                }
            })
        }
        if (type.equals("Article", ignoreCase = true)) {
            binding?.relatedArticleRV?.setHasFixedSize(true)
            binding?.relatedArticleRV?.adapter =
                RelatedArtistContentSingles(data, featureFlag, object : ClickItem {
                    override fun moreRailItemClick(data: RelatedRailsCommonData.Data.Item,position: Int) {
                        if (!data.articleContent?.articleBody.isNullOrEmpty()) {
                            requireActivity().startActivity(
                                Intent(
                                    context,
                                    WebViewActivity::class.java
                                ).putExtra(
                                    AppConstants.ARTICLE_CONTENT_URL,
                                    data.articleContent?.articleBody
                                ).putExtra(AppConstants.TITLE, data.title)
                            )
                        }
                    }
                })
        }
    }

//    viewModel?.getArtistDetails(genres.toString()?: "")?.observe(viewLifecycleOwner) {
    private fun setRelatedRails() {

        if (featureFlag?.featureFlag?.RELATED_RECOMMENDATION_ARTIST == true){
            val genresAsString=convertListToCommaSeparatedString(genres)
            viewModel?.getArtistDetails(genresAsString)?.observe(viewLifecycleOwner) {
                if (!it?.data?.items.isNullOrEmpty()) {
                    binding?.relatedArtist?.visibility = View.VISIBLE
                    setRecycleView(it ,"Artist")
                }
            }
        }

        if (featureFlag?.featureFlag?.RELATED_RECOMMENDATION_ALBUM == true){
            val customdata="albums-artist-ids|OR:LIKE|${videoDetails?.id}"
            viewModel?.getArtistAlbum(customdata)?.observe(viewLifecycleOwner){
                if (!it?.data?.items.isNullOrEmpty()) {
                    binding?.albumRelatedLayout?.visibility = View.VISIBLE
                    setRecycleView(it, "Album")
                }
            }
        }

        if (featureFlag?.featureFlag?.RELATED_RECOMMENDATION_ALBUM == true){
            val customdata="conductor|OR:${videoDetails?.id},session-artist|OR:LIKE|${videoDetails?.id}"
            viewModel?.getArtistAlbum(customdata)?.observe(viewLifecycleOwner){
                if (!it?.data?.items.isNullOrEmpty()) {
                    Log.d("AlbumWithArtist",it.data?.items.toString())
                    binding?.albumWithArtistLayout?.visibility = View.VISIBLE
                    setRecycleView(it, "AlbumWithArtist")
                }
            }
        }

        if (featureFlag?.featureFlag?.RELATED_RECOMMENDATION_VIDEO == true)
             viewModel?.getArtistVideo("${videoDetails?.id}")?.observe(viewLifecycleOwner){
            if (!it?.data?.items.isNullOrEmpty()) {
                binding?.relatedVideo?.visibility = View.VISIBLE
                setRecycleView(it, "Video")
            }
        }

        if (featureFlag?.featureFlag?.RELATED_RECOMMENDATION_ARTICLE == true){
            viewModel?.getRelateArticled("${videoDetails?.id}")?.observe(viewLifecycleOwner){
                if (!it?.data?.items.isNullOrEmpty()) {
                    binding?.relatedArticle?.visibility = View.VISIBLE
                    setRecycleView(it, "Article")
                }
            }
        }

        if (featureFlag?.featureFlag?.RELATED_RECOMMENDATION_SINGLES == true){

        }
    }

    fun convertListToCommaSeparatedString(arrayList: List<Int?>): String {
        return arrayList
            .mapNotNull { it?.toString() }  // Convert non-null integers to strings
            .joinToString(",")             // Join the strings with a comma
    }
}