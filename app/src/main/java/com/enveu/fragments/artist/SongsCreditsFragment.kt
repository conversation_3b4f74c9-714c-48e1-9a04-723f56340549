package com.enveu.fragments.artist

import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.UnderlineSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.databinding.SongsCreditsFragmentBinding
import com.enveu.utils.constants.AppConstants

class SongsCreditsFragment(private val currentSong: DataItem) : Fragment() {
    private var binding : SongsCreditsFragmentBinding?= null
    private var artists : String = ""
    private var composer : String = ""
    private var arangers : String = ""
    private var seasonArtist : String = ""
    private var songWriters : String = ""
    private var producers : String = ""
    private var mix_And_Master : String= ""
    private var conductor : String= ""
    private var musicPublisher : String= ""
    private var licensor : String= ""
    private var recordingEngineersAudio : String= ""
    private var recordingEngineersVideo : String= ""
    private var distributor : String= ""
    private var redirection:String=""


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return SongsCreditsFragmentBinding.inflate(inflater,container, false).run {
            binding = this
            root
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (arguments?.isEmpty == false){
            redirection= requireArguments().getString(AppConstants.FROM_REDIRECTION).toString()
        }
        setToolbar()

        currentSong.title?.let {
            binding?.songTitle?.text = it
        }
        binding?.cancelText?.setOnClickListener {
            requireActivity().onBackPressed()
        }
        if (currentSong.customData?.songsArtistIds != null) {
            binding?.performedll?.visibility = view.visibility
            currentSong.customData?.songsArtistIds?.forEachIndexed { index, item ->
                if (index == currentSong.customData?.songsArtistIds?.size?.minus(1)) {
                    artists += item.title
                } else {
                    artists += "${item.title} \u2022 "
                }
            }
            if (artists.isNotEmpty()) {
                binding?.mainArtists?.text = artists
            }else{
                binding?.performedll?.visibility = View.GONE
            }
        }

        if (currentSong.customData?.sessionArtist != null){
            binding?.sessionArtistsLl?.visibility = View.VISIBLE
            currentSong.customData.sessionArtist?.forEachIndexed { index, arranger ->
                if (index == currentSong.customData?.sessionArtist?.size?.minus(1)) {
                    seasonArtist += arranger.title
                } else {
                    seasonArtist += "${arranger.title} \u2022 "
                }
            }
            if (seasonArtist.isNotEmpty()) {
                binding?.sessionArtists?.text = seasonArtist
            }else{
                binding?.sessionArtistsLl?.visibility = View.GONE
            }
        }

        if (currentSong.customData?.musicPublisher != null){
            binding?.musicPublisherLl?.visibility = View.VISIBLE
            currentSong.customData.musicPublisher?.forEachIndexed { index, arranger ->
                if (index == currentSong.customData?.musicPublisher?.size?.minus(1)) {
                    musicPublisher += arranger.title
                } else {
                    musicPublisher += "${arranger.title} \u2022 "
                }
            }
            if (musicPublisher.isNotEmpty()) {
                binding?.musicPublisher?.text = musicPublisher
            }else{
                binding?.musicPublisherLl?.visibility = View.GONE
            }
        }


        if (currentSong.customData?.licensor != null){
            binding?.sourceLicensorLl?.visibility = View.VISIBLE
            currentSong.customData.licensor?.forEachIndexed { index, arranger ->
                if (index == currentSong.customData?.licensor?.size?.minus(1)) {
                    licensor += arranger.title
                } else {
                    licensor += "${arranger.title} \u2022 "
                }
            }
            if (licensor.isNotEmpty()) {
                binding?.sourceLicensor?.text = licensor
            }else{
                binding?.sourceLicensorLl?.visibility = View.GONE
            }
        }

        if (currentSong.customData?.arranger != null){
            binding?.arrangersll?.visibility = View.VISIBLE
            currentSong.customData.arranger?.forEachIndexed { index, arranger ->
                if (index == currentSong.customData?.arranger?.size?.minus(1)) {
                    arangers += arranger.title
                } else {
                    arangers += "${arranger.title} \u2022 "
                }
            }
            if (arangers.isNotEmpty()) {
                binding?.arrangers?.text = arangers
            }else{
                binding?.arrangersll?.visibility = View.GONE
            }

        }

        if (currentSong.customData?.songWitter != null) {
            binding?.lyricistsLl?.visibility = view.visibility
            currentSong.customData.songWitter?.forEachIndexed { index, arranger ->
                if (index == currentSong.customData?.songWitter?.size?.minus(1)) {
                    songWriters += arranger.title
                } else {
                    songWriters += "${arranger.title} \u2022 "
                }
            }
            if (songWriters.isNotEmpty()) {
                binding?.lyricists?.text = songWriters
            }else{
                binding?.lyricistsLl?.visibility = View.GONE
            }
        }

        if (currentSong.customData?.conductors != null) {
            binding?.conductorLl?.visibility = View.VISIBLE
            currentSong.customData.conductors?.forEachIndexed { index, arranger ->
                if (index == currentSong.customData?.conductors?.size?.minus(1)) {
                    conductor += arranger.title
                } else {
                    conductor += "${arranger.title} \u2022 "
                }
            }
            if (conductor.isNotEmpty()) {
                binding?.conductor?.text = conductor
            }else{
                binding?.conductorLl?.visibility = View.GONE
            }
        }

            if (currentSong.customData?.producers != null) {
                binding?.sourceProducersLl?.visibility = View.VISIBLE
                binding?.producedbyll?.visibility = View.VISIBLE

                currentSong.customData.producers?.forEachIndexed { index, item ->
                    if (index == currentSong.customData?.producers?.size?.minus(1)) {
                        producers += item.title
                    } else {
                        producers += "${item.title} \u2022 "
                    }
                }
                if (producers.isNotEmpty()) {
                    binding?.producers?.text = producers
                    binding?.sourceProducers?.text = producers
                }else{
                    binding?.producedbyll?.visibility = View.GONE
                    binding?.sourceProducersLl?.visibility = View.GONE
                }
            }

        if (currentSong.customData?.mastering != null) {
            currentSong.customData.mastering?.forEachIndexed { index, item ->
                binding?.mixAndMasteringLl?.visibility = View.VISIBLE

                if (index == currentSong.customData?.mastering?.size?.minus(1)) {
                    mix_And_Master += item.title
                } else {
                    mix_And_Master += "${item.title} \u2022 "
                }
            }
            if (mix_And_Master.isNotEmpty()) {
                binding?.mixAndMastering?.text = mix_And_Master
            }else{
                binding?.mixAndMasteringLl?.visibility = View.GONE
            }
        }
        if (currentSong.customData?.composer != null) {
            binding?.writtenbyll?.visibility = View.VISIBLE
            currentSong.customData.composer?.forEachIndexed { index, item ->
                if (index == currentSong.customData?.composer?.size?.minus(1)) {
                    composer += item.title
                } else {
                    composer += "${item.title} \u2022 "
                }
            }
            if (composer.isNotEmpty()) {
                binding?.composers?.text = composer
            }else{
                binding?.writtenbyll?.visibility = View.GONE
            }
        }
        if (currentSong.customData?.recordingEngineersAudio != null) {
            binding?.recordingEngineersAudioLl?.visibility = View.VISIBLE
            currentSong.customData.recordingEngineersAudio?.forEachIndexed { index, item ->
                if (index == currentSong.customData?.recordingEngineersAudio?.size?.minus(1)) {
                    recordingEngineersAudio += item.title
                } else {
                    recordingEngineersAudio += "${item.title} \u2022 "
                }
            }
            if (recordingEngineersAudio.isNotEmpty()) {
                binding?.recordingEngineersAudio?.text = recordingEngineersAudio
            }else{
                binding?.recordingEngineersAudioLl?.visibility = View.GONE
            }
        }

        if (currentSong.customData?.recordingEngineersVideo != null) {
            binding?.recordingEngineersVideoLl?.visibility = View.VISIBLE
            currentSong.customData.recordingEngineersVideo?.forEachIndexed { index, item ->
                if (index == currentSong.customData?.recordingEngineersVideo?.size?.minus(1)) {
                    recordingEngineersVideo += item.title
                } else {
                    recordingEngineersVideo += "${item.title} \u2022 "
                }
            }
            if (recordingEngineersVideo.isNotEmpty()) {
                binding?.recordingEngineersVideo?.text = recordingEngineersVideo
            }else{
                binding?.recordingEngineersVideoLl?.visibility = View.GONE
            }
        }
        if (currentSong.customData?.distributor != null) {
            binding?.distributorLl?.visibility = View.VISIBLE
            currentSong.customData.distributor?.forEachIndexed { index, item ->
                if (index == currentSong.customData?.distributor?.size?.minus(1)) {
                    distributor += item.title
                } else {
                    distributor += "${item.title} \u2022 "
                }
            }
            if (distributor.isNotEmpty()) {
                binding?.distributor?.text = distributor
            }else{
                binding?.distributorLl?.visibility = View.GONE
            }
        }

        setTitleVisibility()
        setSpannableUnderlineText()
    }

    private fun setTitleVisibility(){
        if (artists.isNotEmpty() || seasonArtist.isNotEmpty() || conductor.isNotEmpty()){
            binding?.performedByTitle?.visibility = View.VISIBLE
        }else{
            binding?.performedByTitle?.visibility = View.GONE
        }
        if (composer.isNotEmpty() || arangers.isNotEmpty() || songWriters.isNotEmpty()){
            binding?.writtenByTitle?.visibility = View.VISIBLE
        }else{
            binding?.writtenByTitle?.visibility = View.GONE
        }
        if (producers.isNotEmpty() || recordingEngineersAudio.isNotEmpty() || recordingEngineersVideo.isNotEmpty() || mix_And_Master.isNotEmpty()){
            binding?.producedByTitle?.visibility = View.VISIBLE
        }else{
            binding?.producedByTitle?.visibility = View.GONE
        }
        if (musicPublisher.isNotEmpty() || distributor.isNotEmpty()){
            binding?.sourceTitle?.visibility = View.VISIBLE
        }else{
            binding?.sourceTitle?.visibility = View.GONE
        }
    }

    private fun setSpannableUnderlineText() {
        binding?.mainArtists?.text = SpannableString(artists).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        binding?.lyricists?.text = SpannableString(songWriters).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        binding?.sessionArtists?.text = SpannableString(seasonArtist).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        binding?.conductor?.text = SpannableString(conductor).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        binding?.composers?.text = SpannableString(composer).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        binding?.arrangers?.text = SpannableString(arangers).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        binding?.producers?.text = SpannableString(producers).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        binding?.mixAndMastering?.text = SpannableString(mix_And_Master).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        binding?.recordingEngineersAudio?.text = SpannableString(recordingEngineersAudio).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        binding?.recordingEngineersVideo?.text = SpannableString(recordingEngineersVideo).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        binding?.musicPublisher?.text = SpannableString(musicPublisher).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }

        binding?.sourceLicensor?.text = SpannableString(licensor).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        binding?.distributor?.text = SpannableString(distributor).apply {
            setSpan(UnderlineSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    }

    private fun setToolbar(){
        if (redirection.equals(AppConstants.BOTTOMDAILOGFRAG,false)){
            binding?.toolbar?.root?.visibility=View.GONE
        }else {
            binding?.toolbar?.searchIcon?.visibility = View.GONE
            binding?.toolbar?.backLayout?.setOnClickListener {
                requireActivity().onBackPressed()
            }
        }
    }
}