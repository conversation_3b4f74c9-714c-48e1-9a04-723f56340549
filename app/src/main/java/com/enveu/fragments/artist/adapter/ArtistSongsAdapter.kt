package com.enveu.fragments.artist.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.callbacks.player.callback.SongClick
import com.enveu.databinding.SongsLayoutItemBinding
import com.enveu.player.utils.TimeUtils
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.helpers.ImageHelper

class ArtistSongsAdapter() :
    RecyclerView.Adapter<SongListViewHolder>() {
    private var songList: MutableList<DataItem>? = null
    private var onSongClick: SongClick? = null
    private var featureFlag: FeatureFlagModel? = null
    private var topContentImg = ""
    private var isIntentFromArtist: Boolean = true

    constructor(
        songList: MutableList<DataItem>,
        onSongClick: SongClick,
        featureFlag: FeatureFlagModel,
        isIntentFromArtist:Boolean
    ) : this() {
        this.songList = songList
        this.onSongClick = onSongClick
        this.featureFlag = featureFlag
        this.isIntentFromArtist = isIntentFromArtist
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SongListViewHolder {
        return SongListViewHolder(
            SongsLayoutItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )

    }

    override fun getItemCount(): Int {
        return if (isIntentFromArtist) {
            // Show only up to 4 items if isIntentFromArtist is true
            minOf(4, songList?.size ?: 0)
        } else {
            // Show all items if isIntentFromArtist is false
            songList?.size ?: 0
        }
    }


    override fun onBindViewHolder(holder: SongListViewHolder, position: Int) {
        holder.binding.textSongName.text = songList?.get(position)!!.title
        var artistName = ""
        songList?.get(position)!!.customData?.songsArtistIds?.forEachIndexed { index, item ->
            if (index == songList!![position].customData?.songsArtistIds?.size?.minus(1)) {
                artistName += item.title
            } else {
                artistName += "${item.title} \u2022 "
            }
        }
        if (artistName.isNotEmpty()) {
            holder.binding.textArtistName.text = artistName
        } else {
            holder.binding.textArtistName.visibility = View.GONE
        }
        if (songList!![position].customData?.songsAlbumsId?.images?.isNotEmpty() == true) {
            val posterUrl: String = songList!![position].customData.songsAlbumsId.images.get(0).src
            ImageHelper.getInstance(holder.binding.albumImage.context)
                .loadListSQRImageForAlbumList(holder.binding.albumImage, posterUrl)
        } else if (topContentImg.isNotEmpty() && topContentImg.isNotBlank()) {
            ImageHelper.getInstance(holder.binding.albumImage.context)
                .loadListSQRImageForAlbumList(holder.binding.albumImage, topContentImg)
        }
        if (featureFlag?.featureFlag?.PLAY_COUNT == true) {
            holder.binding.textArtistName.text =
                AppCommonMethod.formatViewCount(songList!![position].playCount) + " Plays." + " " + artistName
        } else {
            holder.binding.textArtistName.text = artistName
        }
        holder.binding.linearLayoutView.setOnClickListener {
            songList?.get(position)?.let {
                onSongClick!!.onSongClick(it)
            }
        }
        songList?.get(position)?.audioContent?.duration?.let { duration ->
            holder.binding.songDuration.text = TimeUtils.formatDurationJw(duration.toLong() / 1000)

        }
//        if (songList!![position].images.isNullOrEmpty()) {
//            holder.binding.imageTitle.text = songList?.get(position)!!.title
//        }else {
//
//            songList!![position].images?.let {
//                ImageHelper.getInstance(holder.binding.songImage.context)
//                    .loadListImage(
//                        holder.binding.songImage,
//                        AppCommonMethod.getImages(it)
//                    )
//            }
//        }

        holder.binding.dot.setOnClickListener {
            songList?.get(position)?.let {
                onSongClick?.onThreeDotClick(it)
            }
        }
        if (null != songList?.get(position)!!.customData.trackNumber) {
            //  holder.binding.textRowNumber.text = songList?.get(position)!!.customData.trackNumber
        }
    }

    fun setThumbnailImgOfPoster(imgUrl: String) {
        topContentImg = imgUrl
    }
}

class SongListViewHolder(val binding: SongsLayoutItemBinding) :
    RecyclerView.ViewHolder(binding.root) {

}
