package com.enveu.fragments.artist.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.beanModelV3.RelatedRailsCommonData
import com.enveu.databinding.SquareItemLargeBinding

class RelatedArtistContentSingles(
    private var data: RelatedRailsCommonData,
    private val featureFlag: FeatureFlagModel?,
    private var itemClickItem: ClickItem
)  : RecyclerView.Adapter<RelatedArtistContentSingles.RelatedViewHolder>() {

    inner class RelatedViewHolder(binding : SquareItemLargeBinding?= null) : RecyclerView.ViewHolder(binding?.root!!) {
        val image = binding?.itemImage
        val imageTitle = binding?.imageTitle
        val exclusive = binding?.exclusive
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RelatedViewHolder {
        return RelatedViewHolder(
            SquareItemLargeBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }


    override fun getItemCount(): Int {
        return data.data?.items?.size!!
    }

    override fun onBindViewHolder(holder: RelatedArtistContentSingles.RelatedViewHolder, position: Int) {

        val item = data.data?.items?.get(position)

        holder.itemView.setOnClickListener {
            item?.let { it1 -> itemClickItem.moreRailItemClick(it1,position) }
        }

        if (item?.images != null && item?.images?.isNotEmpty()!! && item.images[0]?.imageContent != null && !item.images[0]?.imageContent?.src.isNullOrEmpty()) {
            Glide.with(holder.image?.context!!).load(item.images[0]?.imageContent?.src)
                .into(holder.image);
        } else {
            holder.imageTitle?.visibility = View.VISIBLE
            holder.imageTitle?.text = item?.title
        }

        if (item?.customData?.isExclusive?.equals("true")== true ){
            holder.exclusive?.visibility = View.VISIBLE
        }
    }

}
