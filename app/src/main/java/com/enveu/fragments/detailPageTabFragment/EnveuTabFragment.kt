package com.enveu.fragments.detailPageTabFragment

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.os.postDelayed
import androidx.fragment.app.Fragment
import com.enveu.R
import com.enveu.adapters.player.EpisodeTabAdapter
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaConfig
import com.enveu.appLevelModel.MediaInfo
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.selectedSeason.SelectedSeasonModel
import com.enveu.databinding.FragmentEnveuTabBinding
import com.enveu.fragments.player.ui.CommonListAllFragment
import com.enveu.fragments.player.ui.MoreLikeThisFragment
import com.enveu.fragments.player.ui.SeasonTabFragment
import com.enveu.fragments.player.ui.TournamentSeasonTabFragment
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.constants.AppConstants.BUNDLE_MEDIA_TYPE
import com.enveu.utils.constants.AppConstants.BUNDLE_SEASON_NUMBER
import com.enveu.utils.constants.AppConstants.BUNDLE_SERIES_ID
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.google.android.material.tabs.TabLayout


// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [EnveuTabFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class EnveuTabFragment : BaseBindingFragment<FragmentEnveuTabBinding?>() {

    private var episodeTabAdapter: EpisodeTabAdapter? = null
    private var mediaConfig: MediaConfig? = null
    private var featureList: FeatureFlagModel? = null
    private var mediaType = ""
    private var handler:Handler? = null
    private var seriesId = 0
    private var isMatchDetailPage = false
    private var seasonNumber = 0
    private var seasonTabFragment: SeasonTabFragment? = null
    private var tournamentSeasonTabFragment: TournamentSeasonTabFragment? = null
    private var relatedContentFragment: MoreLikeThisFragment? = null
    private var commonListAllFragment: CommonListAllFragment? = null
    private fun getAppLevelJsonData() {
        val mediaConfigInfo: MediaInfo? = AppConfigMethod.getMediaMappingByType(mediaType)
        mediaConfig = AppConfigMethod.getMediaConfigByType(mediaConfigInfo?.pageType.toString())
        featureList = AppConfigMethod.parseFeatureFlagList()

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getIntentData()
        getAppLevelJsonData()

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setTabs()
    }

    private fun getIntentData() {
        mediaType = arguments?.getString(BUNDLE_MEDIA_TYPE) ?: ""
        seriesId = arguments?.getInt(BUNDLE_SERIES_ID) ?: 0
        seasonNumber = arguments?.getInt(BUNDLE_SEASON_NUMBER) ?: 0
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): FragmentEnveuTabBinding? {
        return FragmentEnveuTabBinding.inflate(inflater)
    }


    private fun setTabs() {
       /* binding?.tabLayout?.setTabTextColors(
            AppColors.detailPageTabItemUnselectedTxtColor(),
            AppColors.detailPageTabItemSelectedTxtColor(),
        )*/

        binding?.tabLayout?.setSelectedTabIndicatorGravity(TabLayout.INDICATOR_GRAVITY_TOP)
        episodeTabAdapter =
            EpisodeTabAdapter(
                childFragmentManager
            )

        if (mediaConfig!!.detailPage.tabs.episode.enabled) {
            callSeasonTabFragment()
        }

        if (mediaConfig!!.detailPage.tabs.fixtures.enabled) {
            callTournamentTabFragment()
        }

        if (mediaConfig!!.detailPage.tabs.moreLikeThis.enabled) {
            callMoreLikeThisFragment()
        }

        if (mediaConfig!!.detailPage.tabs.trailersAndMore.enabled) {
            callCommonListAllFragment(
                mediaConfig!!.detailPage.tabs.trailersAndMore.displayLabel,
                AppConstants.TRAILER_CUSTOM_DATA
            )
        }

        if (mediaConfig!!.detailPage.tabs.clipAndMore.enabled) {
            callCommonListAllFragment(
                mediaConfig!!.detailPage.tabs.clipAndMore.displayLabel,
                AppConstants.CLIPS_AND_MORE_CUSTOM_DATA
            )
        }

        if (mediaConfig!!.detailPage.tabs.highlights.enabled) {
            var customData  = ""
            customData = if (mediaType.equals(AppConstants.MATCHES,ignoreCase = true)) {
                AppConstants.MATCH_HIGHLIHTS_CUSTOM_DATA
            } else {
                AppConstants.HIGHLIHTS_CUSTOM_DATA
            }
            callCommonListAllFragment(mediaConfig!!.detailPage.tabs.highlights.displayLabel,customData)
        }

        if (mediaConfig!!.detailPage.tabs.interviews.enabled) {
            var customData  = ""
            customData = if (mediaType.equals(AppConstants.MATCHES,ignoreCase = true)) {
                AppConstants.MATCH_INTERVIEW_CUSTOM_DATA
            } else {
                AppConstants.INTERVIEW_CUSTOM_DATA
            }
            callCommonListAllFragment(
                mediaConfig!!.detailPage.tabs.interviews.displayLabel,
                customData
            )
        }
        if (mediaConfig!!.detailPage.tabs.replays.enabled) {
            var customData  = ""
            customData = if (mediaType.equals(AppConstants.MATCHES,ignoreCase = true)) {
                AppConstants.MATCH_REPLAYS_CUSTOM_DATA
            } else {
                AppConstants.REPLAYS_CUSTOM_DATA
            }
            callCommonListAllFragment(
                mediaConfig!!.detailPage.tabs.replays.displayLabel,
                customData
            )
        }

        if (episodeTabAdapter!!.count > 2) {
            binding?.tabLayout!!.tabMode = TabLayout.MODE_SCROLLABLE
        } else {
            binding?.tabLayout!!.tabMode = TabLayout.MODE_FIXED
        }

        binding!!.viewPager.adapter = episodeTabAdapter
        binding!!.viewPager.offscreenPageLimit = 5
        binding!!.tabLayout.setupWithViewPager(binding!!.viewPager)

       /* binding?.tabLayout?.getTabAt(0)?.view?.background = ColorsHelper.strokeBgDrawable(
            AppColors.detailPageTabUnselectedBorderColor(),
            AppColors.detailPageTabUnselectedBorderColor(),
            0f
        )
        binding?.tabLayout?.background = ColorsHelper.strokeBgDrawable(
            AppColors.detailPageTabSelectedBorderColor(),
            AppColors.detailPageTabUnselectedBorderColor(),
            0f
        )*/
        // Ensure the TabLayout uses the custom background for tabs
        binding?.tabLayout?.setSelectedTabIndicatorHeight(0) // Disable underline indicator

        // Setting up tab background and colors
        binding!!.tabLayout.addOnTabSelectedListener(object : TabLayout.BaseOnTabSelectedListener<TabLayout.Tab> {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.view?.setBackgroundResource(R.drawable.tab_indicator)
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                tab?.view?.setBackgroundResource(R.drawable.tab_indicator)
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
        binding?.tabLayout!!.visibility = View.VISIBLE

    }

    fun removeTab(position: Int) {
       /* if (binding!!.tabLayout.tabCount >= 1 && position <= binding!!.tabLayout.tabCount) {
            episodeTabAdapter!!.removeTabPage(position)
        }*/
        binding?.tabLayout!!.tabMode = TabLayout.MODE_SCROLLABLE
        allotEachTabWithEqualWidth()
        Handler(Looper.getMainLooper()).postDelayed(1800) {
            binding?.tabLayout!!.visibility = View.VISIBLE
        }

    }

    private fun allotEachTabWithEqualWidth() {
        val slidingTabStrip = binding?.tabLayout?.getChildAt(0) as ViewGroup
            for (i in 0 until binding?.tabLayout?.tabCount!!) {
                val tab = slidingTabStrip.getChildAt(i)
                val layoutParams = tab.layoutParams as LinearLayout.LayoutParams
                layoutParams.weight = 1f
                tab.layoutParams = layoutParams
            }
    }

    private fun callSeasonTabFragment() {
        seasonTabFragment = SeasonTabFragment()
        val bundleSeason = Bundle()
        bundleSeason.putInt(BUNDLE_SERIES_ID, seriesId)
        //bundleSeason.putInt(AppConstants.BUNDLE_ASSET_ID, seriesId)
        bundleSeason.putInt(BUNDLE_SEASON_NUMBER, seasonNumber)
        seasonTabFragment!!.arguments = bundleSeason
        episodeTabAdapter!!.addFragment(
            seasonTabFragment,
            mediaConfig!!.detailPage.tabs.episode.displayLabel
        )
    }


    private fun callTournamentTabFragment() {
        tournamentSeasonTabFragment = TournamentSeasonTabFragment()
        val bundleSeason = Bundle()
        bundleSeason.putInt(BUNDLE_SERIES_ID, seriesId)
        bundleSeason.putInt(BUNDLE_SEASON_NUMBER, seasonNumber)
        tournamentSeasonTabFragment!!.arguments = bundleSeason
        episodeTabAdapter!!.addFragment(tournamentSeasonTabFragment,mediaConfig!!.detailPage.tabs.fixtures.displayLabel)
    }

    fun seasonEpisodeNotify(list: ArrayList<SelectedSeasonModel>, position: Int) {
        if (seasonTabFragment != null) {
            seasonTabFragment!!.updateTotalPages()
            seasonTabFragment!!.seasonAdapter = null
            seasonTabFragment!!.selectedSeason = list[position].selectedId
          //  showLoading(binding!!.progressBar, true)
            seasonTabFragment!!.episodesListApi(
                list[position].list.id,
                list[position].list.seriesCustomData.season_number.toInt()
            )
        }
    }

    private fun callMoreLikeThisFragment() {
//        val label = if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.ENGLISH_LAN_CODE)){
//            resources.getString(R.string.detail_tab_related)
//        }
        relatedContentFragment = MoreLikeThisFragment()
        val moreLkeThisValue = Bundle()
        moreLkeThisValue.putInt(AppConstants.ID, seriesId)
        moreLkeThisValue.putString("videoType", AppConstants.SERIES)
        moreLkeThisValue.putString("contentType", AppConstants.VIDEO)
        relatedContentFragment!!.arguments = moreLkeThisValue
        episodeTabAdapter!!.addFragment(
            relatedContentFragment,
            resources.getString(R.string.detail_tab_related)
        )

    }

    private fun callCommonListAllFragment(tabTittle: String, customData: String) {
        commonListAllFragment = CommonListAllFragment()
        val bundleValue = Bundle()
        bundleValue.putInt(AppConstants.ID, seriesId)
        bundleValue.putString(AppConstants.TITLE, tabTittle)
        bundleValue.putString(AppConstants.CUSTOM_DATA, customData)
        commonListAllFragment!!.arguments = bundleValue
        episodeTabAdapter!!.addFragment(commonListAllFragment, tabTittle)

    }

    companion object {
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @param param1 Parameter 1.
         * @param param2 Parameter 2.
         * @return A new instance of fragment EnveuTabFragment.
         */
        // TODO: Rename and change types and number of parameters
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            EnveuTabFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }
}