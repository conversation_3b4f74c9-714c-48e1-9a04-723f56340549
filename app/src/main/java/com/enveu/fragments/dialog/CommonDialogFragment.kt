package com.enveu.fragments.dialog


import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import com.enveu.R
import com.enveu.baseModels.BaseActivity
import com.enveu.databinding.CustomPopupBinding
import com.enveu.utils.colorsJson.converter.AppColors
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.stringsJson.converter.StringsHelper
import androidx.core.graphics.drawable.toDrawable


class CommonDialogFragment : DialogFragment() {
    private var binding: CustomPopupBinding? = null
    private var editDialogListener: EditDialogListener? = null
    private var baseActivity: BaseActivity? = null
    private val colorsHelper by lazy { ColorsHelper }
    private val stringsHelper by lazy { StringsHelper }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        baseActivity = context as BaseActivity
    }

    fun setEditDialogCallBack(editDialogListener: EditDialogListener?) {
        this.editDialogListener = editDialogListener
    }

    @SuppressLint("UseGetLayoutInflater")
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.custom_popup, null, false)
        if (dialog!!.window != null) {
            dialog!!.window!!.setBackgroundDrawable(requireContext().getColor(R.color.transparent).toDrawable())
            dialog!!.window!!.requestFeature(Window.FEATURE_NO_TITLE)
            dialog!!.setCancelable(false)
            val titleValue = requireArguments().getString(AppConstants.TITLE)
            val message = requireArguments().getString(AppConstants.MESSAGE)
            val actionBtn = requireArguments().getString(AppConstants.ACTION_BTN)
            val cancelBtnValue = requireArguments().getString(AppConstants.CANCEL_BTN)
            parseColor()
            val btnOk = binding?.personalizeBtn
            val cancel = binding?.cancelButton
            val description = binding?.popupDiscription
            val title = binding?.popupTitle
            title?.text = titleValue
            if (titleValue!!.isEmpty()) {
                title?.visibility = View.GONE
            }
            if (!cancelBtnValue.isNullOrEmpty()){
                cancel?.visibility = View.VISIBLE
            }

            if (arguments?.getBoolean(AppConstants.SHOULD_DESCRIPTION_CENTER) == true){
                binding?.popupDiscription?.gravity = Gravity.CENTER
                binding?.popupTitle?.gravity = Gravity.CENTER
            }

            if (titleValue.contains(requireContext().getString(R.string.popup_logout))) {
                cancel?.visibility = View.VISIBLE
            }

            if (titleValue.contains((stringsHelper.instance()?.data?.config?.lang_change_lang.toString()))) {
                cancel?.visibility = View.VISIBLE
            }
            if (titleValue.contains(requireContext().getString(R.string.popup_delete_account))) {
                cancel?.visibility = View.VISIBLE
            }

            if (titleValue.contains(requireContext().getString(R.string.logout_confirmation))) {
                cancel?.visibility = View.VISIBLE
            }

            if (titleValue.contains((stringsHelper.instance()?.data?.config?.popup_under_review.toString()))) {
                cancel?.visibility = View.GONE
            }
            if (titleValue.contains(stringsHelper.instance()?.data?.config?.popup_recent_search.toString())) {
                cancel?.visibility = View.VISIBLE
                cancel?.text = stringsHelper.instance()?.data?.config?.popup_ok.toString()
            }
            if (titleValue.contains(requireContext().getString(R.string.popup_notEntitled))) {
                cancel?.visibility = View.VISIBLE
            }
            if (message!!.isEmpty()) {
                description?.visibility = View.GONE
            }

            if (message.contains(requireContext().getString(R.string.popup_user_not_verify))) {
                cancel?.visibility = View.VISIBLE
            }
            if (message.contains(requireContext().getString(R.string.delete_hunting))) {
                cancel?.visibility = View.VISIBLE
            }
            description?.text = message
            btnOk?.text = actionBtn
            cancel?.text = cancelBtnValue
            btnOk?.setOnClickListener {
                if (editDialogListener != null) editDialogListener!!.onActionBtnClicked()
                dismiss()
            }
            cancel?.setOnClickListener {
                if (editDialogListener != null) editDialogListener!!.onCancelBtnClicked()
                dismiss()
            }
            dialog!!.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE)
        }
        return binding?.root
    }

    private fun parseColor() {
        binding?.colorsData = colorsHelper
        binding?.stringData = stringsHelper
        binding?.dialogContainer?.background = ColorsHelper.strokeBgDrawable(AppColors.popupBgColor(), AppColors.popupBrColor(), 10f)
       // binding?.cancelButton?.background = ColorsHelper.strokeBgDrawable(AppColors.tphBgColor(), AppColors.tphBrColor(), 10f)
    }

    override fun onResume() {
        if (dialog?.window != null) dialog!!.window!!.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        super.onResume()
    }

    interface EditDialogListener {
        fun onActionBtnClicked()
        fun onCancelBtnClicked()
    }

    companion object {
        @JvmStatic
        fun newInstance(title: String?, message: String?, actnBtn: String?): CommonDialogFragment {
            val frag = CommonDialogFragment()
            val args = Bundle()
            args.putString(AppConstants.TITLE, title)
            args.putString(AppConstants.MESSAGE, message)
            args.putString(AppConstants.ACTION_BTN, actnBtn)
            frag.arguments = args
            return frag
        }

        @JvmStatic
        fun newInstanceWithCancelBtn(title: String?, message: String?, actnBtn: String?, cancelBtn : String?, shouldDescriptionCenter:Boolean? = false): CommonDialogFragment {
            val frag = CommonDialogFragment()
            val args = Bundle()
            args.putString(AppConstants.TITLE, title)
            args.putString(AppConstants.MESSAGE, message)
            args.putString(AppConstants.ACTION_BTN, actnBtn)
            args.putString(AppConstants.CANCEL_BTN, cancelBtn)
            args.putBoolean(AppConstants.SHOULD_DESCRIPTION_CENTER, shouldDescriptionCenter == true)
            frag.arguments = args
            return frag
        }
    }
}