package com.enveu.fragments.home.ui

import android.os.Bundle
import com.enveu.beanModel.TabsBaseFragment
import com.enveu.databinding.ActivityMainBinding
import com.enveu.fragments.home.viewModel.HomeFragmentViewModel

class HomeFragment(private val tabID : String, private val binding: ActivityMainBinding?) : TabsBaseFragment<HomeFragmentViewModel?>() {

    constructor() : this("0", null)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setViewModel(HomeFragmentViewModel::class.java, tabID, binding)
    }
}