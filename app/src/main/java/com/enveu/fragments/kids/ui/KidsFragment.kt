package com.enveu.fragments.kids.ui

import android.os.Bundle
import com.enveu.beanModel.TabsBaseFragment
import com.enveu.fragments.kids.viewModel.KidsFragmentViewModel

class KidsFragment(private val tabID : String) : TabsBaseFragment<KidsFragmentViewModel?>() {
    constructor() : this("0")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setViewModel(KidsFragmentViewModel::class.java, tabID)
    }
}