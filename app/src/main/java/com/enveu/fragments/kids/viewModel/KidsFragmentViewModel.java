package com.enveu.fragments.kids.viewModel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;

import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory;
import com.enveu.baseModels.HomeBaseViewModel;
import com.enveu.repository.home.HomeFragmentRepository;
import com.enveu.utils.constants.AppConstants;

import java.util.List;


public class KidsFragmentViewModel extends HomeBaseViewModel {

    public KidsFragmentViewModel(@NonNull Application application) {
        super(application);
    }


    @Override
    public LiveData<List<BaseCategory>> getAllCategories() {

        return HomeFragmentRepository.getInstance().getCategories(AppConstants.HOME_ENVEU);
    }


    @Override
    public void resetObject() {

    }
}