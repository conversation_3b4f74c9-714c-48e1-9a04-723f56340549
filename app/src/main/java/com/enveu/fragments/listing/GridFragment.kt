package com.enveu.fragments.listing

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.annotation.RequiresApi
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.listing.callback.ItemClickListener
import com.enveu.adapters.CommonListingAdapter
import com.enveu.adapters.commonRails.CommonCircleAdapter
import com.enveu.adapters.commonRails.CommonPosterLandscapeAdapter
import com.enveu.adapters.commonRails.CommonPosterPotraitAdapter
import com.enveu.adapters.commonRails.CommonPotraitTwoAdapter
import com.enveu.adapters.commonRails.LandscapeListingAdapter
import com.enveu.adapters.commonRails.SquareListingAdapter
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.continueWatching.DataItem
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent
import com.enveu.callbacks.commonCallbacks.CommonApiCallBack
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory
import com.enveu.client.bookmarking.bean.continuewatching.ContinueWatchingBookmark
import com.enveu.client.bookmarking.bean.continuewatching.GetContinueWatchingBean
import com.enveu.client.enums.ImageType
import com.enveu.databinding.ListingActivityBinding
import com.enveu.fragments.dialog.AlertDialogFragment
import com.enveu.fragments.dialog.AlertDialogSingleButtonFragment
import com.enveu.layersV2.ContinueWatchingLayer
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.cropImage.helpers.NetworkConnectivity
import com.enveu.utils.cropImage.helpers.ShimmerDataModel
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.GridSpacingItemDecoration
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper
import java.util.Objects

class GridFragment : BaseBindingFragment<ListingActivityBinding?>(),
    ItemClickListener, AlertDialogFragment.AlertDialogListener, OnSongItemClick {
    var playListId: String? = null
    var baseCategory: BaseCategory? = null
    var enveuLayoutType = 0
    private var counter = 0
    private var flag = 0
    private var firstVisiblePosition = 0
    private var pastVisiblesItems = 0
    private var visibleItemCount = 0
    private var totalItemCount = 0
    private var commonCircleAdapter: CommonCircleAdapter? = null
    private var commonLandscapeAdapter: LandscapeListingAdapter? = null
    private var commonPotraitTwoAdapter: CommonPotraitTwoAdapter? = null
    private var commonPosterLandscapeAdapter: CommonPosterLandscapeAdapter? = null
    private var commonPosterPotraitAdapter: CommonPosterPotraitAdapter? = null
    private var gridLayoutManager: GridLayoutManager? = null
    private var squareCommonAdapter: SquareListingAdapter? = null
    private var title: String? = null
    private var isContinueWatchingEnable = false
    private var mIsLoading = true
    private var isScrolling = false
    private var mScrollY = 0
    private var shimmerType = 0
    private var listData: RailCommonData? = null
    private var isloggedout = false
    private var preference: KsPreferenceKeys? = null
    var tabletSize = false
    private val stringsHelper by lazy { StringsHelper }
    override fun inflateBindingLayout(inflater: LayoutInflater): ListingActivityBinding {
        return ListingActivityBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).toolFrame(1)
        (activity as HomeActivity).detailFrameVisibility()

    }

    override fun onDetach() {
        super.onDetach()
        if (activity != null && activity is HomeActivity)
        (activity as HomeActivity).toolFrame(0)
        (activity as HomeActivity).detailFrameVisibility()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding!!.colorsData = ColorsHelper
        preference = KsPreferenceKeys.getInstance()
        title = arguments?.getString("title")
        isContinueWatchingEnable = arguments?.getBoolean("isContinueWatching")!!
        playListId = arguments?.getString("playListId")
        flag = arguments?.getInt("flag", 0)!!
        shimmerType = arguments?.getInt("shimmerType", 0)!!
        baseCategory = arguments?.getParcelable("baseCategory")
        tabletSize = this.resources.getBoolean(R.bool.isTablet)
        uiIntilization()
        connectionObserver()
    }

    private fun connectionObserver() {
        if (NetworkConnectivity.isOnline(requireActivity())) {
            binding!!.noConnectionLayout.visibility = View.GONE
            connectionValidation(true)
        } else {
            connectionValidation(false)
        }
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            if (counter == 0) callShimmer()
            setClicks()
            try {
                railData
            } catch (ignored: Exception) {
            }
        } else {
            noConnectionLayout()
        }
    }

    private fun callShimmer() {
        val shimmerAdapter = CommonListingAdapter(requireActivity())
        binding!!.listRecyclerview.hasFixedSize()
        binding!!.listRecyclerview.isNestedScrollingEnabled = false
        var num = 2
        if (baseCategory!!.contentImageType.equals(
                ImageType.CIR.name,
                ignoreCase = true
            ) || baseCategory!!.contentImageType.equals(ImageType.PR1.name, ignoreCase = true)
            || baseCategory!!.contentImageType.equals(ImageType.PR2.name, ignoreCase = true)
            || baseCategory!!.contentImageType.equals(ImageType.SQR.name, ignoreCase = true)
        ) {
            val tabletSize = <EMAIL>(R.bool.isTablet)
            num = 3
            if (tabletSize) {
                num = if (requireActivity().resources.configuration.orientation == 2) 5 else 4
            }
            shimmerAdapter.setDataList(
                ShimmerDataModel(
                    requireActivity()
                ).getList(5)
            )
        } else if (baseCategory!!.contentImageType.equals(ImageType.LDS.name, ignoreCase = true)
            || baseCategory!!.contentImageType.equals(ImageType.LDS2.name, ignoreCase = true)
        ) {
            val tabletSize = requireActivity().resources.getBoolean(R.bool.isTablet)
            num = 2
            if (tabletSize) {
                num = if (requireActivity().resources.configuration.orientation == 2) 4 else 3
            }
            shimmerAdapter.setDataList(
                ShimmerDataModel(
                    requireActivity()
                ).getList(4)
            )
        }
        binding!!.listRecyclerview.addItemDecoration(
            GridSpacingItemDecoration(
                num,
                5,
                true
            )
        )
        gridLayoutManager = GridLayoutManager(requireActivity(), num)
        binding!!.listRecyclerview.layoutManager = gridLayoutManager
        binding!!.listRecyclerview.adapter = shimmerAdapter
        binding!!.listRecyclerview.visibility = View.VISIBLE
    }

    private fun uiIntilization() {
        binding!!.toolbarGrid.backArrow.let { rotateImageLocaleWise(it) }
        binding!!.toolbarGrid.colorsData = ColorsHelper
        binding!!.toolbarGrid.stringData = StringsHelper
        binding!!.colorsData = ColorsHelper
        binding!!.toolbarGrid.backLayout.setOnClickListener { requireActivity().onBackPressed() }
        binding!!.toolbarGrid.titleMid.visibility = View.VISIBLE
        binding!!.toolbarGrid.logoMain2.visibility = View.GONE
        binding!!.toolbarGrid.llSearchIcon.visibility = View.GONE
        if (isContinueWatchingEnable) {
            val loginStatus = preference!!.appPrefLoginStatus.equals(
                AppConstants.UserStatus.Login.toString(),
                ignoreCase = true
            )
            if (loginStatus) {
                binding!!.toolbarGrid.titleMid.text = title
            } else {
                ActivityLauncher.getInstance()
                    .homeScreen(requireActivity(), HomeActivity::class.java,false,"","",0)
            }
        } else {
            binding!!.toolbarGrid.titleMid.text = title
        }
        //        getBinding().toolbarGrid.homeIcon.setVisibility(View.GONE);
//        getBinding().toolbar.mediaRouteButton.setVisibility(View.GONE);
        binding!!.toolbarGrid.backLayout.visibility = View.VISIBLE
        binding!!.toolbarGrid.titleSkip.visibility = View.GONE
        //    getBinding().toolbar.searchIcon.setVisibility(View.GONE);
        // getBinding().toolbarGrid.titleMidToolbar.setVisibility(View.VISIBLE);
        binding!!.listRecyclerview.hasFixedSize()
        binding!!.listRecyclerview.isNestedScrollingEnabled = false
        binding!!.listRecyclerview.addItemDecoration(
            GridSpacingItemDecoration(
                3,
                5,
                true
            )
        )

    }

    private fun setClicks() {
        binding!!.listRecyclerview.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                try {
                    if (enveuLayoutType == 0) {
                        val layoutManager = recyclerView.layoutManager as GridLayoutManager?
                        firstVisiblePosition = layoutManager?.findFirstVisibleItemPosition()!!
                        if (dy > 0) {
                            visibleItemCount = layoutManager.childCount
                            totalItemCount = layoutManager.itemCount
                            pastVisiblesItems = layoutManager.findFirstVisibleItemPosition()
                            if (mIsLoading) {
                                if (visibleItemCount + pastVisiblesItems >= totalItemCount) {
                                    // Logger.d("slidingValues"+getBinding().listRecyclerview.getAdapter().getItemCount()+" "+counter);
                                    val adapterSize = binding!!.listRecyclerview.adapter!!.itemCount
                                    if (adapterSize > 8) {
                                        mIsLoading = false
                                        counter++
                                        isScrolling = true
                                        mScrollY += dy
                                        connectionObserver()
                                    }
                                }
                            }
                        }
                    } else {
                        val layoutManager = recyclerView.layoutManager as LinearLayoutManager?
                        firstVisiblePosition = layoutManager?.findFirstVisibleItemPosition()!!
                        if (dy > 0) {
                            visibleItemCount = layoutManager.childCount
                            totalItemCount = layoutManager.itemCount
                            pastVisiblesItems = layoutManager.findFirstVisibleItemPosition()
                            if (mIsLoading) {
                                if (visibleItemCount + pastVisiblesItems >= totalItemCount) {
                                    // Logger.d("slidingValues"+getBinding().listRecyclerview.getAdapter().getItemCount()+" "+counter);
                                    val adapterSize = binding!!.listRecyclerview.adapter!!.itemCount
                                    if (adapterSize > 8) {
                                        mIsLoading = false
                                        counter++
                                        isScrolling = true
                                        mScrollY += dy
                                        connectionObserver()
                                    }
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    Logger.e("ListingActivity", "" + e)
                }
            }
        })
        binding!!.transparentLayout.visibility = View.VISIBLE
        binding!!.transparentLayout.setOnClickListener { }
    }

    // showDialog(GridActivity.this.getResources().getString(R.string.error), GridActivity.this.getResources().getString(R.string.something_went_wrong));
    @get:RequiresApi(api = Build.VERSION_CODES.M)
    private val railData: Unit
        get() {
            try {
                if (flag == 0) {
                    if (baseCategory!!.referenceName != null && (baseCategory!!.referenceName.equals(
                            AppConstants.ContentType.CONTINUE_WATCHING.name,
                            ignoreCase = true
                        ) || baseCategory!!.referenceName.equals(
                            "special_playlist",
                            ignoreCase = true
                        ))
                    ) {
                        val preference = KsPreferenceKeys.getInstance()
                        val isLogin = preference.appPrefLoginStatus
                        if (isLogin.equals(
                                AppConstants.UserStatus.Login.toString(),
                                ignoreCase = true
                            )
                        ) {
                            val token = preference.appPrefAccessToken
                            val bookmarkingViewModel =
                                ViewModelProvider(this)[BookmarkingViewModel::class.java]
                            bookmarkingViewModel.getContinueWatchingData(
                                token,
                                counter,
                                AppConstants.PAGE_SIZE
                            ).observe(this) { getContinueWatchingBean: GetContinueWatchingBean? ->
                                binding!!.transparentLayout.visibility = View.GONE
                                if (getContinueWatchingBean != null) {
                                    if (getContinueWatchingBean.isStatus) {
                                        var videoIds = ""
                                        try {
                                            val continueWatchingBookmarkLists =
                                                getContinueWatchingBean.data.continueWatchingBookmarks
                                            val continueWatchingBookmarkList =
                                                removeDuplicates(continueWatchingBookmarkLists)
                                            for (continueWatchingBookmark: ContinueWatchingBookmark in continueWatchingBookmarkList) {
                                                videoIds =
                                                    videoIds + continueWatchingBookmark.assetId.toString() + ","
                                            }
                                            Logger.w("assetIds", videoIds)
                                            ContinueWatchingLayer.getInstance()
                                                .getContinueWatchingVideos(
                                                    continueWatchingBookmarkList,
                                                    videoIds,
                                                    object :
                                                        CommonApiCallBack {
                                                        override fun onSuccess(item: Any) {
                                                            binding!!.transparentLayout.visibility =
                                                                View.GONE
                                                            if (item is List<*>) {
                                                                val enveuVideoDetails =
                                                                    item as ArrayList<DataItem>
                                                                val railCommonData =
                                                                    RailCommonData()
                                                                railCommonData.setContinueWatchingData(
                                                                    baseCategory,
                                                                    true,
                                                                    enveuVideoDetails,
                                                                    object :
                                                                        CommonApiCallBack {
                                                                        override fun onSuccess(item: Any) {
                                                                            setRail(railCommonData)
                                                                        }

                                                                        override fun onDataLoaded(orderedData: MutableList<RailCommonData>?) {
                                                                            TODO("Not yet implemented")
                                                                        }

                                                                        override fun onFailure(
                                                                            throwable: Throwable
                                                                        ) {
                                                                        }

                                                                        override fun onFinish() {}
                                                                    })
                                                            }
                                                        }

                                                        override fun onDataLoaded(orderedData: MutableList<RailCommonData>?) {
                                                            TODO("Not yet implemented")
                                                        }

                                                        override fun onFailure(throwable: Throwable) {}
                                                        override fun onFinish() {}
                                                    })
                                        } catch (ignored: Exception) {
                                        }
                                    } else if (getContinueWatchingBean.responseCode == 4302L) {
                                        isloggedout = true
                                        logoutCall()
                                        try {
                                            requireActivity().runOnUiThread(Runnable {
                                                ActivityLauncher.getInstance().homeScreen(
                                                    requireActivity(),
                                                    HomeActivity::class.java,false,"","",0
                                                )
                                            })
                                        } catch (e: Exception) {
                                        }
                                    } else if (getContinueWatchingBean.responseCode == 500L) {
                                        // showDialog(GridActivity.this.getResources().getString(R.string.error), GridActivity.this.getResources().getString(R.string.something_went_wrong));
                                    }
                                }
                            }
                        }
                    } else {
                        val railInjectionHelper =
                            ViewModelProvider(this)[RailInjectionHelper::class.java]
                        railInjectionHelper.getPlayListDetailsWithPaginationV2(
                            playListId,
                            counter,
                            AppConstants.PAGE_SIZE,
                            baseCategory
                        ).observe(this) { playlistRailData: ResponseModel<*> ->
                            if (playlistRailData.status.equals(
                                    APIStatus.START.name,
                                    ignoreCase = true
                                )
                            ) {
                            } else if (playlistRailData.status.equals(
                                    APIStatus.SUCCESS.name,
                                    ignoreCase = true
                                )
                            ) {
                                if (Objects.requireNonNull(playlistRailData) != null) {
                                    if (playlistRailData.baseCategory != null) {
                                        val railCommonData =
                                            playlistRailData.baseCategory as RailCommonData
                                        try {
                                            if (title == null || title.equals(
                                                    "",
                                                    ignoreCase = true
                                                )
                                            ) {
                                                binding!!.toolbarGrid.titleMid.text =
                                                    railCommonData.displayName
                                            }
                                        } catch (e: Exception) {
                                            Logger.w(e)
                                        }
                                        listData = railCommonData
                                        setRail(railCommonData)
                                        Logger.e("RAIL DATA", listData!!.isSeries.toString())
                                    }
                                }
                            } else if (playlistRailData.status.equals(
                                    APIStatus.ERROR.name,
                                    ignoreCase = true
                                )
                            ) {
                            } else if (playlistRailData.status.equals(
                                    APIStatus.FAILURE.name,
                                    ignoreCase = true
                                )
                            ) {
                            }
                        }
                    }
                } else {
                }
            } catch (e: Exception) {
                Logger.w(e)
            }
        }

    private fun logoutCall() {
        if (CheckInternetConnection.isOnline(Objects.requireNonNull(requireActivity()))) {
            clearCredientials(preference)
            hitApiLogout(requireActivity(), preference!!.appPrefAccessToken)
        } else {
            stringsHelper.stringParse(
                stringsHelper.instance()!!.data.config.popup_no_internet_connection_found,
                getString(R.string.popup_no_internet_connection_found)
            )
        }
    }

    private fun removeDuplicates(continueWatchingBookmarkList: List<ContinueWatchingBookmark>): List<ContinueWatchingBookmark> {
        val noRepeat: MutableList<ContinueWatchingBookmark> = ArrayList()
        try {
            for (event in continueWatchingBookmarkList) {
                var isFound = false
                // check if the event name exists in noRepeat
                for (e in noRepeat) {
                    if (e.assetId == event.assetId || e == event) {
                        isFound = true
                        break
                    }
                }
                if (!isFound) noRepeat.add(event)
            }
        } catch (ignored: Exception) {
        }
        return noRepeat
    }

    var railCommonData: RailCommonData? = null
    private fun setRail(playlistRailData: RailCommonData?) {
        railCommonData = playlistRailData
        binding!!.transparentLayout.visibility = View.GONE
        if (isScrolling) {
            if (playlistRailData!!.enveuVideoItemBeans.isNotEmpty() && playlistRailData.enveuVideoItemBeans != null) {
                setUiComponents(playlistRailData)
            }
            binding!!.progressBar.visibility = View.GONE
        } else {
            binding!!.progressBar.visibility = View.GONE
            mIsLoading = true
            if (playlistRailData != null) {
                if (baseCategory!!.referenceName != null && (baseCategory!!.referenceName.equals(
                        AppConstants.ContentType.CONTINUE_WATCHING.name,
                        ignoreCase = true
                    ) || baseCategory!!.referenceName.equals("special_playlist", ignoreCase = true))
                ) {
                    if (commonPosterLandscapeAdapter == null) {
                        RecyclerAnimator(requireActivity()).animate(binding!!.listRecyclerview)
                        commonPosterLandscapeAdapter =
                            CommonPosterLandscapeAdapter(
                                requireActivity(),
                                playlistRailData.enveuVideoItemBeans,
                                ArrayList(),
                                "VIDEO",
                                ArrayList(),
                                baseCategory,
                                this
                            )
                        binding!!.listRecyclerview.adapter = commonPosterLandscapeAdapter
                    }
                } else {
                    if (baseCategory!!.contentImageType.equals(ImageType.CIR.name, ignoreCase = true)) {
                        if (commonCircleAdapter == null) {
                            RecyclerAnimator(requireActivity()).animate(binding!!.listRecyclerview)
                            commonCircleAdapter = CommonCircleAdapter(requireActivity(), playlistRailData.enveuVideoItemBeans, "VIDEO", ArrayList(), this, playlistRailData)
                            binding!!.listRecyclerview.adapter = commonCircleAdapter
                        } else commonCircleAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                        mIsLoading = playlistRailData.maxContent != commonCircleAdapter!!.itemCount

                    } else if (baseCategory!!.contentImageType.equals(ImageType.SQR.name, ignoreCase = true)) {
                        if (squareCommonAdapter == null) { RecyclerAnimator(requireActivity()).animate(binding!!.listRecyclerview)
                            squareCommonAdapter = SquareListingAdapter(playlistRailData.enveuVideoItemBeans, "VIDEO",baseCategory, this)
                            binding!!.listRecyclerview.adapter = squareCommonAdapter
                        } else squareCommonAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                        mIsLoading = playlistRailData.maxContent != squareCommonAdapter!!.itemCount
                    } else if (baseCategory!!.contentImageType.equals(ImageType.LDS2.name, ignoreCase = true)) {
                        if (commonLandscapeAdapter == null) {
                            RecyclerAnimator(requireActivity()).animate(binding!!.listRecyclerview)
                            commonLandscapeAdapter = LandscapeListingAdapter(requireActivity(), playlistRailData.enveuVideoItemBeans, ArrayList(), "VIDEO", this, baseCategory, tabletSize)
                            binding!!.listRecyclerview.adapter = commonLandscapeAdapter
                        } else commonLandscapeAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                        mIsLoading = playlistRailData.maxContent != commonLandscapeAdapter!!.itemCount
                    } else if (baseCategory!!.contentImageType.equals(
                            ImageType.PR2.name,
                            ignoreCase = true
                        )
                    ) {
                        if (commonPotraitTwoAdapter == null) {
                            RecyclerAnimator(
                                requireActivity()
                            ).animate(binding!!.listRecyclerview)
                            commonPotraitTwoAdapter =
                                CommonPotraitTwoAdapter(
                                    requireActivity(),
                                    playlistRailData.enveuVideoItemBeans,
                                    "VIDEO",
                                    ArrayList(),
                                    0,
                                    this,
                                    baseCategory
                                )
                            binding!!.listRecyclerview.adapter = commonPotraitTwoAdapter
                        } else commonPotraitTwoAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                        mIsLoading =
                            playlistRailData.maxContent != commonPotraitTwoAdapter!!.itemCount
                    } else if (baseCategory!!.contentImageType.equals(
                            ImageType.LDS.name,
                            ignoreCase = true
                        )
                    ) {
                        if (commonLandscapeAdapter == null) {
                            RecyclerAnimator(
                                requireActivity()
                            ).animate(binding!!.listRecyclerview)
                            commonLandscapeAdapter =
                                LandscapeListingAdapter(
                                    requireActivity(),
                                    playlistRailData.enveuVideoItemBeans,
                                    ArrayList(),
                                    "VIDEO",
                                    this,
                                    baseCategory,
                                    tabletSize
                                )
                            binding!!.listRecyclerview.adapter = commonLandscapeAdapter
                        } else commonLandscapeAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                        mIsLoading =
                            playlistRailData.maxContent != commonLandscapeAdapter!!.itemCount
                    } else if (baseCategory!!.contentImageType.equals(
                            ImageType.PR1.name,
                            ignoreCase = true
                        )
                    ) {
                        if (commonPosterPotraitAdapter == null) {
                            RecyclerAnimator(
                                requireActivity()
                            ).animate(binding!!.listRecyclerview)
                            commonPosterPotraitAdapter =
                                CommonPosterPotraitAdapter(
                                    requireActivity(),
                                    playlistRailData.enveuVideoItemBeans,
                                    ArrayList(),
                                    "VIDEO",
                                    ArrayList(),
                                    this,
                                    baseCategory,
                                    playlistRailData
                                )
                            binding!!.listRecyclerview.adapter = commonPosterPotraitAdapter
                        } else commonPosterPotraitAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                        mIsLoading =
                            playlistRailData.maxContent != commonPosterPotraitAdapter!!.itemCount
                    }
                    binding!!.listRecyclerview.scrollToPosition(mScrollY)
                }
            }
        }
    }

    private fun setUiComponents(playlistRailData: RailCommonData?) {
        if (playlistRailData!!.enveuVideoItemBeans != null) {
            if (playlistRailData.enveuVideoItemBeans.isNotEmpty()) {
                if (Objects.requireNonNull(baseCategory!!.contentImageType)
                        .equals(ImageType.CIR.name, ignoreCase = true)
                ) {
                    commonCircleAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                    mIsLoading = playlistRailData.maxContent != commonCircleAdapter!!.itemCount
                } else if (baseCategory!!.contentImageType.equals(
                        ImageType.SQR.name,
                        ignoreCase = true
                    )
                ) {
                    if (squareCommonAdapter != null) {
                        squareCommonAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                        mIsLoading = playlistRailData.maxContent != squareCommonAdapter!!.itemCount
                    }
                } else if (baseCategory!!.contentImageType.equals(
                        ImageType.PR1.name,
                        ignoreCase = true
                    )
                ) {
                    commonPosterPotraitAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                    mIsLoading =
                        playlistRailData.maxContent != commonPosterPotraitAdapter!!.itemCount
                } else if (baseCategory!!.contentImageType.equals(
                        ImageType.PR2.name,
                        ignoreCase = true
                    )
                ) {
                    commonPotraitTwoAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                    mIsLoading = playlistRailData.maxContent != commonPotraitTwoAdapter!!.itemCount
                } else if (baseCategory!!.contentImageType.equals(
                        ImageType.LDS.name,
                        ignoreCase = true
                    )
                ) {
                    if (commonLandscapeAdapter != null) {
                        commonLandscapeAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                        mIsLoading =
                            playlistRailData.maxContent != commonLandscapeAdapter!!.itemCount
                    }
                } else if (baseCategory!!.contentImageType.equals(
                        ImageType.LDS2.name,
                        ignoreCase = true
                    )
                ) {
                    commonLandscapeAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                    mIsLoading = playlistRailData.maxContent != commonLandscapeAdapter!!.itemCount
                } else {
                    commonLandscapeAdapter!!.notifydata(playlistRailData.enveuVideoItemBeans)
                    mIsLoading = playlistRailData.maxContent != commonLandscapeAdapter!!.itemCount
                }
            } else {
                mIsLoading = false
            }
        } else {
            mIsLoading = false
        }
    }

    private fun noConnectionLayout() {
        binding!!.noConnectionLayout.visibility = View.VISIBLE
        binding!!.connection.retryTxt.setOnClickListener { connectionObserver() }
    }

    override fun onRowItemClicked(itemValue: EnveuVideoItemBean, position: Int) {
        val assetType = itemValue.assetType
        var mediType: String? = ""
        if (itemValue.assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
            mediType = itemValue.videoDetails.videoType
        } else if (itemValue.assetType.equals(AppConstants.CUSTOM, ignoreCase = true)) {
            mediType = itemValue.customType
        } else if (itemValue.assetType.equals(
                AppConstants.AUDIO,
                ignoreCase = true
            ) || itemValue.assetType.equals(AppConstants.PERSON, ignoreCase = true)
        ) {
            mediType = itemValue.contentType
        }

        AppCommonMethod.launchDetailScreen(
            requireActivity(),
            assetType,
            itemValue.id,
            itemValue.sku,
            mediType,
            itemValue.title ?: "",
            itemValue.externalRefId ?: "",
            itemValue.posterURL ?: "",
            0,
            itemValue.contentSlug ?: "",
            itemValue
        )
    }


    override fun onFinishDialog() {
        if (isloggedout) {
            logoutCall()
        } else {
            requireActivity().onBackPressed()
        }
    }

    private fun showDialog(title: String, message: String) {
        val fm = requireActivity().supportFragmentManager
        val alertDialog = AlertDialogSingleButtonFragment.newInstance(
            title,
            message,
            stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                getString(R.string.popup_ok)
            )
        )
        alertDialog.isCancelable = false
        alertDialog.setAlertDialogCallBack(this)
        alertDialog.show(fm, "fragment_alert")
    }

    override fun songItemClick(
        songList: List<com.enveu.beanModelV3.videoDetailV3.list.DataItem>,
        song: com.enveu.beanModelV3.videoDetailV3.list.DataItem,
        extarnalRefId: String,
        imageContent: ImageContent?,image:String?,
        playQueueItems:Boolean?, isQueueItemClick: Boolean, songsPosition:Int
    ) {

    }

}