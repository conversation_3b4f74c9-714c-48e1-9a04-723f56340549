package com.enveu.fragments.more.adapter

import android.content.Context
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.content.res.AppCompatResources
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView

import com.enveu.OttApplication
import com.enveu.R
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.callbacks.commonCallbacks.MoreItemClickListener

import com.enveu.databinding.MoreItemBinding
import com.enveu.utils.Logger

import com.enveu.utils.colorsJson.converter.AppColors
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.colorsJson.converter.ColorsHelper.imageViewDrawableBgColor
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.DrawableHelperAboutUs
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper

class MoreListAdapter(
    private val context: Context,
    private var itemsList: MutableList<String>,
    call: MoreItemClickListener,
    hasEntitlement: Boolean,
    onHold : Boolean,
    islogin: Boolean
) : RecyclerView.Adapter<MoreListAdapter.ViewHolder>() {
    val itemClickListener: MoreItemClickListener
    private val islogin: Boolean
    private var hasEntitlement: Boolean
    private var onHold: Boolean
    private var mLastClickTime: Long = 0
    private val stringsHelper by lazy { StringsHelper }
    private var myList = ""
    private var myFavorite = ""
    private var ugcCreate = ""
    private var sponsor = ""
    private var account = ""
    private var signout = ""
    private var settings = ""
    private var changePassword = ""
    private var my_Watch_History = ""
    private var delete_Account = ""
    private var buyNow = ""
    private var orderHistory = ""
    private var termsCondition = ""
    private var manageSubscription = ""
    private var privacyPolicy = ""
    private var deviceManagement = ""
    private var myPlaylist = ""
    private var contactUs = ""
    private var activateTVDevice = ""
    private var rateUs = ""
    private var languages = ""
    private var manageProfile = ""
    private var parentalControl = ""
    private var featureModel: FeatureFlagModel? = null


    init {
        val layoutInflater = LayoutInflater.from(context)
        itemClickListener = call
        this.islogin = islogin
        this.hasEntitlement = hasEntitlement
        this.onHold = onHold
    }

    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): ViewHolder {
        val moreItemBinding = DataBindingUtil.inflate<MoreItemBinding>(
            LayoutInflater.from(viewGroup.context),
            R.layout.more_item,
            viewGroup,
            false
        )
        moreItemBinding.colorsData = ColorsHelper
        getAppLevelJsonData()
        stringsHelper.instance()?.data?.config?.more_gaming.toString()
        context.getString(R.string.more_gaming)
        myList = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.my_playlist.toString(),
            context.getString(R.string.my_playlist)
        )
        myFavorite = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.my_favorite_for_ugc.toString(),
            context.getString(R.string.my_favorite_for_ugc)
        )
        ugcCreate = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.ugc_create.toString(),
            context.getString(R.string.ugc_create)
        )
        account = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_account.toString(),
            context.getString(R.string.more_account)
        )
        settings = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_settings.toString(),
            context.getString(R.string.more_settings)
        )
        manageSubscription = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_manage_subscription.toString(),
            context.getString(R.string.more_manage_subscription)
        )
        orderHistory = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_order_history.toString(),
            context.getString(R.string.more_order_history)
        )
        deviceManagement = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.device_management.toString(),
            context.getString(R.string.device_management)
        )
        activateTVDevice = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.activate_tv_device.toString(),
            context.getString(R.string.activate_tv_device)
        )
        privacyPolicy = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_privacy_policy.toString(),
            context.getString(R.string.more_privacy_policy)
        )
        sponsor = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.sponsor.toString(),
            context.getString(R.string.sponsor)
        )
        termsCondition = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_term_condition.toString(),
            context.getString(R.string.more_term_condition)
        )
        myPlaylist =  stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_my_playlist.toString(),
            context?.getString(R.string.more_my_playlist).toString()
        )
        changePassword = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.change_password.toString(),
            context?.getString(R.string.change_password).toString()
        )

        signout = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.sign_out.toString(),
            context?.getString(R.string.sign_out).toString()
        )

        my_Watch_History = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.my_history_more.toString(),
            context?.getString(R.string.my_history_more).toString()
        )

        delete_Account = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.delete_account_tittle.toString(),
            context?.getString(R.string.delete_account_tittle).toString()
        )

        contactUs = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_contact_us.toString(),
            context.getString(R.string.more_contact_us)
        )

        rateUs = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.rate_the_app.toString(),
            context.getString(R.string.rate_the_app)
        )

        languages = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.settings_change_lang.toString(),
            context.getString(R.string.settings_change_lang)
        )

        manageProfile = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.manage_profile.toString(),
            context.getString(R.string.manage_profile)
        )

        parentalControl = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.parental_control_menu_text.toString(),
            context.getString(R.string.parental_control_menu_text)
        )

        return ViewHolder(moreItemBinding)
    }

    private fun getAppLevelJsonData() {
        featureModel = AppConfigMethod.parseFeatureFlagList()
    }

    override fun onBindViewHolder(holder: ViewHolder, i: Int) {
        val textArray = itemsList[i].split(",", limit = 2)
        val text=textArray[0]
        if (textArray.size>1&&textArray.get(1)!=null){
            val desc=textArray[1]
            holder.moreItemBinding.moreListDesc.text = desc
            holder.moreItemBinding.moreListDesc.visibility=View.VISIBLE
        }else{
            holder.moreItemBinding.moreListDesc.visibility=View.GONE
        }
        if (i == itemsList.size -1){
            holder.moreItemBinding.viewLine.visibility = View.GONE
        }else{
            holder.moreItemBinding.viewLine.visibility = View.VISIBLE
        }
        holder.moreItemBinding.moreListTitle.text = text
        buyNow = if (hasEntitlement || onHold) {
            stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.more_manage_subscription.toString(),
                context.getString(R.string.more_manage_subscription)
            )
        } else {
            stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.more_buy_now.toString(),
                context.getString(R.string.more_buy_now)
            )
        }
        setIcons(holder.moreItemBinding.moreListIcon, i, holder.moreItemBinding, itemsList[i])
    }

    override fun getItemCount(): Int {
        return itemsList.size
    }

    private fun setIcons(v: ImageView, i: Int, binding: MoreItemBinding?, tittle: String) {
        if (tittle.equals(myList, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.more_watchlist_ic, v)
        } else if (tittle.equals(myFavorite, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.more_favourites_ic, v)
        } else if (tittle.equals(ugcCreate, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.more_ugc_ic, v)
        } else if (tittle.equals(settings, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.more_settings_ic, v)
        } else if (tittle.equals(sponsor, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.sponsor, v)
        }else if (tittle.equals(account, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.ic_account, v)
        } else if (tittle.equals(buyNow, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.ic_buy_now, v)
        } else if (tittle.equals(orderHistory, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.more_transaction_history_ic, v)
        } else if (tittle.equals(deviceManagement, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.activate_tv_icon, v)
        } else if (tittle.equals(activateTVDevice, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.multi_device, v)
        } else if (tittle.equals(privacyPolicy, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.more_privacy_policy_ic, v)
        } else if (tittle.equals(termsCondition, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.more_terms_conditions_ic, v)
        } else if (tittle.equals(contactUs, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.ic_contact_us, v)
        } else if (tittle.equals(rateUs, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.ic_star, v)
        } else if (tittle.equals(languages, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.more_languages_ic, v)
        } else if (tittle.equals(delete_Account, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.more_delete_account_ic, v)
        } else if (tittle.equals(my_Watch_History, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.ic_order_history, v)
        } else if (tittle.equals(changePassword, ignoreCase = true)) {
            callDrawableHelper(context, R.drawable.more_change_pass_ic, v)
        } else if (tittle.equals(signout, ignoreCase = true)){
            callDrawableHelper(context, R.drawable.more_logout_ic, v)
        }else if (tittle.equals(myPlaylist, ignoreCase = true)){
            callDrawableHelper(context,R.drawable.manage_playlist_ic,v)
        } else if (tittle.equals(manageProfile, ignoreCase = true)){
            callDrawableHelper(context,R.drawable.more_manage_profiles_ic,v)
        }else if (tittle.equals(parentalControl, ignoreCase = true)){
            callDrawableHelper(context,R.drawable.more_parental_control_ic,v)
        }
    }

    private fun callDrawableHelper(context: Context?, mDrawable: Int, imageView: ImageView) {
        imageView.background = AppCompatResources.getDrawable(context!!, mDrawable)
        imageViewDrawableBgColor(imageView, AppColors.itemLabelIconColor())

//        DrawableHelper
//                .withContext(context)
//                .withColor(mColor)
//                .withDrawable(mDrawable)
//                .tint()
//                .applyTo(imageView);
    }

    fun callDrawableHelperAboutUs(context: Context?, mDrawable: Int, imageView: ImageView?) {
        DrawableHelperAboutUs
            .withContext(context!!)
            .withDrawable(mDrawable)
            .tint()
            .applyTo(imageView!!)
    }

    fun updateData(updateList: List<String>, hasEntitlement : Boolean) {
        itemsList = updateList.toMutableList()
        this.hasEntitlement = hasEntitlement
        notifyDataSetChanged()
    }

    inner class ViewHolder(val moreItemBinding: MoreItemBinding) :
        RecyclerView.ViewHolder(moreItemBinding.root) {
        init {
            moreItemBinding.root.setOnClickListener { view1: View? ->
//                if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.LANGUAGE_ARABIC, ignoreCase = true)) {
//                    AppCommonMethod.updateLanguage(AppConstants.LANGUAGE_ARABIC, OttApplication.instance!!)
//                } else if (KsPreferenceKeys.getInstance().appLanguage.equals(AppConstants.ENGLISH, ignoreCase = true)) {
//                    AppCommonMethod.updateLanguage(AppConstants.ENGLISH_LAN_CODE, OttApplication.instance!!)
//                }
                Logger.e("Caption", itemsList[layoutPosition])
                if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
                    return@setOnClickListener
                }
                mLastClickTime = SystemClock.elapsedRealtime()
                if (itemsList[layoutPosition] == myList) {
                    itemClickListener.onClick(myList)
                }else if (itemsList[layoutPosition] == myFavorite) {
                    itemClickListener.onClick(myFavorite)
                }else if (itemsList[layoutPosition] == ugcCreate ) {
                    itemClickListener.onClick(ugcCreate)
                }else if (itemsList[layoutPosition] == sponsor) {
                    itemClickListener.onClick(sponsor)
                } else if (itemsList[layoutPosition] == account) {
                    itemClickListener.onClick(account)
                } else if (itemsList[layoutPosition] == settings) {
                    itemClickListener.onClick(settings)
                } else if (itemsList[layoutPosition] == buyNow) {
                    itemClickListener.onClick(buyNow)
                } else if (itemsList[layoutPosition] == orderHistory) {
                    itemClickListener.onClick(orderHistory)
                } else if (itemsList[layoutPosition] == deviceManagement) {
                    itemClickListener.onClick(deviceManagement)
                } else if (itemsList[layoutPosition] == activateTVDevice) {
                    itemClickListener.onClick(activateTVDevice)
                } else if (itemsList[layoutPosition] == privacyPolicy) {
                    itemClickListener.onClick(privacyPolicy)
                } else if (itemsList[layoutPosition] == termsCondition) {
                    itemClickListener.onClick(termsCondition)
                } else if (itemsList[layoutPosition] == contactUs) {
                    itemClickListener.onClick(contactUs)
                } else if (itemsList[layoutPosition] == rateUs) {
                    itemClickListener.onClick(rateUs)
                } else if (itemsList[layoutPosition] == languages) {
                    itemClickListener.onClick(languages)
                } else if (itemsList[layoutPosition] == changePassword){
                    itemClickListener.onClick(changePassword)
                }else if (itemsList[layoutPosition] == delete_Account){
                    itemClickListener.onClick(delete_Account)
                }else if (itemsList[layoutPosition] == my_Watch_History){
                    itemClickListener.onClick(my_Watch_History)
                }else if (itemsList[layoutPosition] == signout){
                    itemClickListener.onClick(signout)
                }else if (itemsList[layoutPosition] == myPlaylist){
                    itemClickListener.onClick(myPlaylist)
                } else if (itemsList[layoutPosition] == manageProfile){
                    itemClickListener.onClick(manageProfile)
                }else if (itemsList[layoutPosition] == parentalControl){
                    itemClickListener.onClick(parentalControl)
                }
            }
        }
    }
}