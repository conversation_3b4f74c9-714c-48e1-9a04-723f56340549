package com.enveu.fragments.more.ui



import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity.RESULT_OK
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.enveu.BuildConfig
import com.enveu.BuildConstants
import com.enveu.R
import com.enveu.activities.device_management.DeviceManagerActivity
import com.enveu.activities.follow_follower.FollowFollowingProfileActivity
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.homeactivity.viewmodel.HomeViewModel
import com.enveu.activities.multiplePlaylist.MyPlaylistFragment
import com.enveu.activities.profile.activate_device.ActivateDeviceActivity
import com.enveu.activities.profile.order_history.ui.OrderHistoryActivity
import com.enveu.activities.profile.ui.AccountSettingActivity
import com.enveu.activities.purchase.plans_layer.GetPlansLayer
import com.enveu.activities.secondary_profile.ParentalControlActivity
import com.enveu.activities.secondary_profile.SecondaryProfileActivity
import com.enveu.activities.settings.ActivitySettings
import com.enveu.activities.sponsorArtist.ArtistAndSponserActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.ActivitySelectSubscriptionPlan
import com.enveu.activities.usermanagment.ui.ChangePasswordActivity
import com.enveu.activities.usermanagment.ui.EnterOTPActivity
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.activities.videoquality.ui.ChangeLanguageActivity
import com.enveu.activities.watchList.ui.WatchListActivity
import com.enveu.adapters.shimmer.ShimmerAdapter
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.AppUserModel
import com.enveu.beanModel.userProfile.UserProfileResponse
import com.enveu.beanModelV3.mutli_profile_response.SecondaryProfileData
import com.enveu.callbacks.commonCallbacks.MoreItemClickListener
import com.enveu.cms.HelpActivity
import com.enveu.databinding.FragmentMoreBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.more.adapter.MoreListAdapter
import com.enveu.networking.response.ReelCreatorId
import com.enveu.networking.response.ReelsContentItem
import com.enveu.ugc.addshortvideo.AddShortVideoFragment
import com.enveu.ugc.addshortvideo.ShortVideoUploadFragment
import com.enveu.utils.Constants
import com.enveu.utils.Logger
import com.enveu.utils.ObjectHelper
import com.enveu.utils.TrackerUtil.MoEUserTracker.setUserProperties
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.cropImage.helpers.ShimmerDataModel
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.StringUtils
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import com.moengage.core.internal.utils.isTablet
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class MoreFragment : BaseBindingFragment<FragmentMoreBinding?>(),
    CommonDialogFragment.EditDialogListener,
    MoreItemClickListener {
    private var mPreference: KsPreferenceKeys? = null
    private var isLogin: String? = null
    private var mListLogin: MutableList<String>? = null
    private var viewModel: HomeViewModel? = null
    private var removeViewModel: RegistrationLoginViewModel? = null
    private var hasEntitlement = false
    private var onHold = false
    private var offerStatus = "false"
    private var token = ""
    private var userState = ""
    private var gaming = ""
    private var myList = ""
  //  private var ugcCreate = ""
    private var myFavorite = ""
    private var changePassword = ""
    private var sponsor = ""
    private var my_Watch_History = ""
    private var delete_Account = ""
    private var account = ""
    private var settings = ""
    private var adapter : MoreListAdapter?= null
    private var signout = ""
    private var buyNow = ""
    private var mListLogOut: MutableList<String>?= null
    private var mListWithSub: MutableList<String>?= null
    private var mListAppSettingLogOut: MutableList<String>?= null
    private var mListAppSettingLogin: MutableList<String>?= null
    private var orderHistory = ""
    private var termsCondition = ""
    private var privacyPolicy = ""
    private var isWantSignout = false
    private var activateTVDevice = ""
    private var deviceManagement = ""
    private var contactUs = ""
    private var manageProfile = ""
    private var parentalControl = ""
    private var myPlaylist = ""
    private var isDeletionRequired = false
    private var rateUs = ""
    private var languages = ""
    private val responseCode = 1001
    private val RESPONSE_CODE = 1002
    private var featureModel: FeatureFlagModel? = null
    private var stringList: ArrayList<String>? = null
    private var appSettingList: ArrayList<String>? = null
    private var mListener: OnMyListInteractionListener? = null
    private val loginStatus = KsPreferenceKeys.getInstance().appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)

    interface OnMyListInteractionListener {
        fun onFragmentInteraction()
    }

    private var logoutList: ArrayList<String>? = null
    private var appSettingLogoutList: ArrayList<String>? = null
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    public override fun inflateBindingLayout(inflater: LayoutInflater): FragmentMoreBinding {
        return FragmentMoreBinding.inflate(inflater)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
//        getAppLevelJsonData()
//        userState = KsPreferenceKeys.getInstance().appPrefLoginStatus
//        parseColor()
//        mPreference = KsPreferenceKeys.getInstance()
//        setUI()
//        modelCall()
//        clickEvent()
        binding?.toolbar?.backLayout?.visibility = View.GONE
        binding?.toolbar?.llSearchIcon?.visibility = View.GONE

      //  CoroutineScope(Dispatchers.IO).launch {
       //     delay(0)
            getAppLevelJsonData() // Assuming this is a long-running task
            userState = KsPreferenceKeys.getInstance().appPrefLoginStatus
            parseColor() // Check if this method is heavy
            mPreference = KsPreferenceKeys.getInstance()
            // Call modelCall() and clickEvent() on the main thread after the background work
        //    withContext(Dispatchers.Main) {
                setUI() // Update UI elements
                modelCall() // Initialize model-related tasks
                clickEvent() // Setup click listeners
//                Handler(Looper.getMainLooper()).postDelayed({
                    // Stop shimmer and show content after data is loaded
                    binding?.shimmerProfileMain?.visibility = View.GONE // Hide shimmer when loading is done
                    binding?.moreMainLayout?.visibility = View.VISIBLE // Show main content
//                }, )

            }
      //  }
 //   }

    private fun parseColor() {
        binding!!.stringData = stringsHelper
        binding!!.colorsData = colorsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
        // binding?.titleLayout?.background = colorsHelper.strokeBgDrawable(AppColors.appBgColor(), AppColors.profileImageBorderColor(), 200f)
    }

    private fun setUI() {
        binding?.root?.isClickable = false
        binding?.toolbar?.backLayout?.visibility = View.GONE
        binding?.toolbar?.logoMain2?.visibility = View.VISIBLE
        binding?.toolbar?.llSearchIcon?.visibility = View.GONE
        token = KsPreferenceKeys.getInstance().appPrefAccessToken
        isLogin = mPreference!!.appPrefLoginStatus
        if (mPreference?.isVerified.equals("false")){
            binding?.notVerifiedMsg?.text= stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.not_verified.toString(),
                getString(R.string.not_verified)
            )
            binding?.notVerifiedMsg?.visibility=View.VISIBLE
        }
        if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
           binding?.signIn?.text = context?.getString(R.string.more_buy_now).toString()
            val tempResponse = KsPreferenceKeys.getInstance().appPrefUser
            if (!StringUtils.isNullOrEmptyOrZero(tempResponse)) {
                setVerify()
            } else {
                val tempResponseApi = mPreference!!.appPrefProfile
                setVerifyApi(tempResponseApi)
            }
        } else {
            binding?.userNameLl?.visibility = View.GONE
            binding?.signInLayout?.visibility = View.VISIBLE

        }
        binding?.signIn?.setOnClickListener {
           if (binding?.signIn?.text?.equals(resources.getString(R.string.login_sign_in))==true){
               ActivityLauncher.getInstance()?.loginActivity(requireActivity(), ActivityLogin::class.java, "more")
           }else{
               val loginStatus = KsPreferenceKeys.getInstance().appPrefLoginStatus.equals(
                   AppConstants.UserStatus.Login.toString(), ignoreCase = true
               )
               if (loginStatus) {
                   if (hasEntitlement && offerStatus == AppConstants.PUBLISHED || offerStatus == AppConstants.NOT_FOR_SALE || onHold) {
                       ActivityLauncher.getInstance().goToPlanScreen(
                           requireActivity(),
                           ActivitySelectSubscriptionPlan::class.java,
                           "settings"
                       )
                   } else {
                       ActivityLauncher.getInstance().goToPlanScreen(
                           requireActivity(),
                           ActivitySelectSubscriptionPlan::class.java,
                           ""
                       )
                   }
               }else{
                   gotoLogin()
               }
           }

        }

        binding?.userNameLl?.setOnClickListener {
          //  ActivityLauncher.getInstance()?.ProfileActivityNew(requireActivity(), ProfileActivityNew::class.java)
//            Intent(requireActivity(), ProfileActivityNew::class.java).also {
//                startActivityForResult(it, responseCode)
//            }
        }

        binding?.notVerifiedMsg?.setOnClickListener {
            ActivityLauncher.getInstance()
                .goToEnterOTP(requireActivity(), EnterOTPActivity::class.java, "more")
        }

        try {
            val isTablet = this.resources.getBoolean(R.bool.isTablet)
            if (!isTablet) {
                val number: String = BuildConfig.VERSION_NAME
               // binding!!.buildNumber.text = number
                binding!!.versionNumberText.text = getString(R.string.app_version, number)
            }
        } catch (ignored: Exception) {
        }
        setProfileData(mPreference?.isActiveUserProfileData)

        binding?.ivProfilePic?.setOnClickListener {
           navigateToFollowFollowingActivity()
        }

        binding?.userNameEdit?.setOnClickListener {
            navigateToFollowFollowingActivity()
        }
    }

    private fun navigateToFollowFollowingActivity(){
        if (loginStatus){
            val userData = mPreference?.isActiveUserProfileData()
            val reelCreatorId = ReelCreatorId()
            reelCreatorId.id = userData?.creatorContentId?.toLong()
            reelCreatorId.externalIdentifier = userData?.id.toString()
            reelCreatorId.title = userData?.name


            Intent(requireActivity(), FollowFollowingProfileActivity::class.java).also {
                it.putExtra(Constants.CREATOR_CONTENT_BUNDLE, reelCreatorId)
                it.putExtra(Constants.FROM_MORE_FRAGMENT, true)
                startActivityForResult(it, Constants.REQUEST_CODE)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        (activity as? HomeActivity)?.hideMainToolbar()
        if (mPreference?.isVerified.equals("false")){
            binding?.notVerifiedMsg?.text= stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.not_verified.toString(),
                getString(R.string.not_verified)
            )
            binding?.notVerifiedMsg?.visibility=View.VISIBLE
        }else{
            binding?.notVerifiedMsg?.visibility=View.GONE
        }
        setProfileData(mPreference?.isActiveUserProfileData)
    }

    private fun setVerifyApi(tempResponse: String?) {
        if (!StringUtils.isNullOrEmptyOrZero(tempResponse)) {
            if (!StringUtils.isNullOrEmptyOrZero(tempResponse)) {
                binding?.userNameLl?.visibility = View.VISIBLE
                    binding?.progressBarHandleDelay?.visibility = View.GONE
                    if (!mPreference?.appPrefUserEmail.isNullOrEmpty()) {
                        binding?.userEmail?.visibility = View.VISIBLE
                        binding?.userEmail?.text=mPreference?.appPrefUserEmail
                    }
                    else {
                        binding?.userEmail?.visibility = View.GONE
                    }
                    val userName = mPreference?.userProfile?.data?.name
                    if (userName != null && !userName.equals("", ignoreCase = true) && !userName.equals("null", ignoreCase = true)) {
                        binding?.progressBarHandleDelay?.visibility = View.GONE
                        binding?.userEmail?.visibility = View.VISIBLE
                        setNameOrEmail(userName, mPreference?.appPrefUserEmail)
                    }
//                if (!mPreference?.appPrefUserProfilePic.isNullOrEmpty()){
//                    binding?.ivProfilePic?.let {
//                        Glide.with(it).load(mPreference?.appPrefUserProfilePic?.toString()).into(it)
//                    }
//                } else {
//                    binding?.ivProfilePic?.setImageResource(R.drawable.profile_avtar_logo)
//                }
                    // setUIComponets(mListLogin, true);

            }
        }
    }

    private fun subscriptionBtnUpdate(hasEntitlement: Boolean, offerStatus: String, onHold : Boolean){
        if (featureModel?.featureFlag?.IS_SUBSCRIBE_ENABLE == true){
            buyNow = if ((hasEntitlement && (offerStatus == AppConstants.PUBLISHED || offerStatus == AppConstants.NOT_FOR_SALE)) || onHold) {
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.more_manage_subscription.toString(),
                    context?.getString(R.string.more_manage_subscription).toString()
                )
            } else {
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.more_buy_now.toString(),
                    context?.getString(R.string.more_buy_now).toString()
                )
            }
            binding?.signIn?.text=buyNow
            binding?.signInLayout?.visibility = View.VISIBLE
        }else{
            binding?.signInLayout?.visibility = View.GONE
        }
    }

    private fun modelCall() {

        val isPrimaryUser = mPreference?.isPrimaryAccountUser == true

        stringList?.clear()
        logoutList?.clear()
        mListLogOut?.clear()
        mListWithSub?.clear()
        mListLogin?.clear()
        mListAppSettingLogOut?.clear()
        mListAppSettingLogin?.clear()

        gaming = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_gaming.toString(),
            context?.getString(R.string.more_gaming).toString()
        )
        myList = stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.my_playlist.toString(), context?.getString(R.string.my_playlist).toString()
        )
//        ugcCreate = stringsHelper.stringParse(
//            stringsHelper.instance()?.data?.config?.ugc_create.toString(),
//            context?.getString(R.string.ugc_create).toString()
//        )
        myFavorite = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.my_favorite_for_ugc.toString(),
            context?.getString(R.string.my_favorite_for_ugc).toString()
        )
        account = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_account.toString(),
            context?.getString(R.string.more_account).toString()
        )
        settings = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_settings.toString(),
            context?.getString(R.string.more_settings).toString()
        )
        buyNow = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_buy_now.toString(),
            context?.getString(R.string.more_buy_now).toString()
        )
        orderHistory = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_order_history.toString(),
            context?.getString(R.string.more_order_history).toString()
        )
        deviceManagement = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.device_management.toString(),
            context?.getString(R.string.device_management).toString()
        )
        activateTVDevice = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.activate_tv_device.toString(),
            context?.getString(R.string.activate_tv_device).toString()
        )
        myPlaylist =  stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_my_playlist.toString(),
            context?.getString(R.string.more_my_playlist).toString()
        )
        privacyPolicy = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_privacy_policy.toString(),
            context?.getString(R.string.more_privacy_policy).toString()
        )
        termsCondition = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_term_condition.toString(),
            context?.getString(R.string.more_term_condition).toString()
        )
        signout = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.sign_out.toString(),
            context?.getString(R.string.sign_out).toString()
        )
        contactUs = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.more_contact_us.toString(),
            context?.getString(R.string.more_contact_us).toString()
        )

        manageProfile = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.manage_profile.toString(),
            context?.getString(R.string.manage_profile).toString()
        )

        parentalControl = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.parental_control_menu_text.toString(),
            context?.getString(R.string.parental_control_menu_text).toString()
        )

        rateUs = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.rate_the_app.toString(),
            context?.getString(R.string.rate_the_app).toString()
        )

//        languages = stringsHelper.stringParse(
//            stringsHelper.instance()?.data?.config?.settings_change_lang.toString(),
//            context?.getString(R.string.settings_change_lang).toString()
//        )

        changePassword = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.change_password.toString(),
            context?.getString(R.string.change_password).toString()
        )

        sponsor = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.sponsor.toString(),
            context?.getString(R.string.sponsor).toString()
        )

        my_Watch_History = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.my_history_more.toString(),
            context?.getString(R.string.my_history_more).toString()
        )

        delete_Account = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.delete_account_tittle.toString(),
            context?.getString(R.string.delete_account_tittle).toString()
        )

        if (stringList == null) {
            stringList = ArrayList()
        }

        if (logoutList == null) {
            logoutList = ArrayList()
        }

        val logoutLabel = arrayOf(
            changePassword,
            deviceManagement,
            activateTVDevice,
            myList,
            my_Watch_History,
        )

        val label3 = arrayOf(
            sponsor,
            changePassword,
            orderHistory,
            deviceManagement,
            activateTVDevice,
            myList,
            myFavorite,
            delete_Account,
            manageProfile,
            //ugcCreate,
            my_Watch_History,
            myPlaylist,
            parentalControl,
            signout
        )

        val appSetting = arrayOf(privacyPolicy, termsCondition)

        val appSettingLogout = arrayOf(
//            languages,
            privacyPolicy,
            termsCondition
//            contactUs,
//            rateUs
        )

        appSettingList= ArrayList()
        appSettingList?.addAll(appSetting)

        appSettingLogoutList= ArrayList()
        appSettingLogoutList?.addAll(appSettingLogout)

        stringList?.addAll(label3)
        logoutList?.addAll(logoutLabel)

        if (featureModel?.featureFlag?.USER_MANAGEMENT_ENABLED == true) {
            if (!featureModel!!.featureFlag.APP_SETTINGS) {
                appSettingList!!.remove(settings)
                appSettingLogoutList?.remove(settings)
            }else{
                appSettingList?.add(0,settings)
                appSettingLogoutList?.add(0, settings)
            }
            if (!featureModel!!.featureFlag.IS_MY_PLAYLIST_ENABLE){
                stringList?.remove(myPlaylist)
            }
            if (!featureModel!!.featureFlag.MY_WATCHLIST) {
                stringList!!.remove(myList)
                logoutList?.remove(myList)
            }
            if (!featureModel!!.featureFlag.IS_UGC_CREATE) {
              //  stringList!!.remove(ugcCreate)
            }

            if (!isPrimaryUser){
                stringList!!.remove(changePassword)
                stringList!!.remove(delete_Account)
                stringList!!.remove(parentalControl)
            }

            if (!featureModel!!.featureFlag.MY_FAVORITE) {
                stringList!!.remove(myFavorite)
            }
            if (!featureModel!!.featureFlag.MY_WATCH_HISTORY) {
                stringList!!.remove(my_Watch_History)
                logoutList?.remove(my_Watch_History)
            }
            if (!featureModel!!.featureFlag.IS_SPONSOR) {
                stringList!!.remove(sponsor)
            }
            if (!featureModel!!.featureFlag.ACCOUNTS) {
                stringList!!.remove(account)
                logoutList?.remove(account)
            }
            if (!featureModel!!.featureFlag.ORDER_HISTORY) {
                stringList!!.remove(orderHistory)
                logoutList?.remove(orderHistory)
            }
            if (!featureModel!!.featureFlag.MULTIPLE_PROFILE_ENABLE) {
                stringList!!.remove(manageProfile)
                logoutList?.remove(manageProfile)
            }
            if (!featureModel!!.featureFlag.MULTIPLE_PROFILE_ENABLE) {
                stringList!!.remove(parentalControl)
                logoutList?.remove(parentalControl)
            }
            if (!featureModel?.featureFlag?.DEVICE_MANAGEMENT!!) {
                stringList!!.remove(deviceManagement)
                logoutList?.remove(deviceManagement)
            }
            if (!featureModel?.featureFlag?.IS_ACTIVATE_TV_ENABLED!!) {
                stringList!!.remove(activateTVDevice)
                logoutList?.remove(activateTVDevice)
            }
        } else {
            stringList!!.remove(settings)
            stringList!!.remove(orderHistory)
            stringList!!.remove(buyNow)
            stringList!!.remove(myList)
            stringList!!.remove(myFavorite)
            stringList!!.remove(account)
          //  stringList!!.remove(ugcCreate)
            stringList!!.remove(deviceManagement)
            stringList!!.remove(activateTVDevice)
            stringList!!.remove(manageProfile)
        }

        mListLogOut = ArrayList(logoutList!!)
        mListWithSub = ArrayList(stringList!!)
        mListAppSettingLogin = ArrayList()
        (mListAppSettingLogin as ArrayList<String>).addAll(listOf(*appSetting))
        mListAppSettingLogOut = appSettingLogoutList?.let { ArrayList(it) }
        mListLogin = ArrayList()
        (mListLogin as ArrayList<String>).addAll(listOf(*label3))
        checkPrimaryUser()
    }

    private fun checkPrimaryUser(){
        if(mPreference?.isPrimaryAccountUser == true){
            if (isLogin.equals("Login", ignoreCase = true)) {
                setAppsettingComponets(appSettingList as MutableList<String>)
                setUIComponets(mListWithSub as ArrayList<String>)
            } else {
                mListAppSettingLogOut?.let { setAppsettingComponets(it) }
                setUIComponets(mListLogOut!!)
            }
        }
        else{
//            val isActiveUserData = mPreference?.isActiveUserProfileData
//            binding?.recyclerViewHeading?.visibility = View.VISIBLE
//            binding?.signIn?.visibility = View.VISIBLE
//            binding?.userNameEdit?.visibility = View.INVISIBLE
//            binding?.usernameTv?.visibility = View.VISIBLE
//            binding?.userEmail?.visibility = View.VISIBLE
//            binding?.appSettingRecycler?.visibility = View.VISIBLE
//            setProfileData(isActiveUserData)
//            val mList: MutableList<String> = mutableListOf()
//            mList.add(manageProfile)
//            mList.add(settings)
//            mList.add(signout)
//            setUIComponets(mList)
            if (isLogin.equals("Login", ignoreCase = true)) {
                setAppsettingComponets(appSettingList as MutableList<String>)
                setUIComponets(mListWithSub as ArrayList<String>)
            } else {
                mListAppSettingLogOut?.let { setAppsettingComponets(it) }
                setUIComponets(mListLogOut!!)
            }
        }
    }
    private fun setAppsettingComponets(mList: MutableList<String>) {
           val adapter = context?.let { _context ->  MoreListAdapter(_context, mList, this, hasEntitlement, onHold, false) }
            binding!!.appSettingRecycler.hasFixedSize()
            binding!!.appSettingRecycler.isNestedScrollingEnabled = false
            binding!!.appSettingRecycler.layoutManager = LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
            binding!!.appSettingRecycler.adapter = adapter
    }

    private fun setUIComponets(mList: MutableList<String>, reBind : Boolean? = true) {
        if (reBind == true){
            adapter = context?.let { _context ->  MoreListAdapter(_context, mList, this, hasEntitlement,onHold, false) }
            binding!!.moreFragmentsRv.hasFixedSize()
            binding!!.moreFragmentsRv.isNestedScrollingEnabled = false
            binding!!.moreFragmentsRv.layoutManager = LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
            binding!!.moreFragmentsRv.adapter = adapter
        }else{
            adapter?.updateData(mList, hasEntitlement)
        }

    }

    fun clickEvent() {
        try {
            isLogin = mPreference?.appPrefLoginStatus
            if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
                viewModel = ViewModelProvider(requireActivity())[HomeViewModel::class.java]
                val token = mPreference?.appPrefAccessToken
                callGetPlans()
                if (mPreference?.isPrimaryAccountUser == true){
                    viewModel?.hitUserProfile(context, token)?.observe(requireActivity()) { userProfileResponse: UserProfileResponse? ->
                        if (userProfileResponse != null && userProfileResponse.status) {
                            binding?.progressBarHandleDelay?.visibility = View.GONE
                            updateUI(userProfileResponse)
                        }
                        else{
                            binding?.progressBarHandleDelay?.visibility = View.GONE
                        }
                    }
                }
            } else {
                binding?.titleLayout?.visibility = View.VISIBLE
                binding?.notVerifiedMsg?.visibility = View.GONE
            }
        } catch (ex: Exception) {
            Logger.w(ex)
        }
    }

    private fun getAppLevelJsonData() {
        featureModel = AppConfigMethod.parseFeatureFlagList()
    }

    private fun callGetPlans() {
        GetPlansLayer.getInstance().getEntitlementStatus(
            KsPreferenceKeys.getInstance(), token
        ) { entitlementStatus: Boolean, apiStatus: Boolean, offerStatus: String,onHold: Boolean,responseCode: Int ->
            hasEntitlement = if (apiStatus) {
                this.offerStatus = offerStatus
                entitlementStatus
            } else {
                if (responseCode == 403) {
                    logoutCall()
                    clearCredientials(mPreference)
                    ActivityLauncher.getInstance().homeActivity(requireActivity(), HomeActivity::class.java)
                }
                false
            }
            if (onHold){
                this.onHold = true
            }
            subscriptionBtnUpdate(hasEntitlement,offerStatus, onHold =  this.onHold)
        }
    }

    private fun updateUI(userProfileResponse: UserProfileResponse) {
        try {
            mPreference?.appPrefUserEmail = userProfileResponse.data?.email.toString()
            mPreference?.appPrefUserName = userProfileResponse.data?.name.toString()
            mPreference?.appPrefUserLastName = userProfileResponse.data?.customData?.lastName ?: ""
            mPreference?.appPrefUserProfilePic = userProfileResponse.data?.profilePicURL?.toString()
            var userName = mPreference!!.appPrefUserName
//            userProfileResponse.data?.profilePicURL?.toString()
//                ?.let { setUpdatedProfileImageFullUrl(it) }
            if (userName != null && !userName.equals("", ignoreCase = true)) {
                binding?.progressBarHandleDelay?.visibility = View.GONE
                setNameOrEmail(userName, mPreference?.appPrefUserEmail!!)
            } else {
                userName = mPreference!!.appPrefUserEmail
                if (userName != null && !userName.equals("", ignoreCase = true)) {
                    binding?.progressBarHandleDelay?.visibility = View.GONE
                    setNameOrEmail(userName,  mPreference?.appPrefUserEmail!!)
                }
            }
            binding!!.titleLayout.visibility = View.VISIBLE
            val gson = Gson()
            val userProfileData = gson.toJson(userProfileResponse)
            KsPreferenceKeys.getInstance().userProfileData = userProfileData
            val json = KsPreferenceKeys.getInstance().userProfileData
            val newObject = gson.fromJson(json, UserProfileResponse::class.java)
            var emailId = ""
            if (ObjectHelper.isNotEmpty(newObject.data.email)) {
                emailId = newObject.data.email as String
            }
            var phoneNumber = ""
            if (ObjectHelper.isNotEmpty(newObject.data.phoneNumber)) {
                phoneNumber = newObject.data.phoneNumber as String
            }
            var dob = 0L
            if (ObjectHelper.isNotEmpty(newObject.data.dateOfBirth)) {
                dob = newObject.data.dateOfBirth as Long
            }
            setUserProperties(
                activity,
                newObject.data.id,
                newObject.data.name,
                emailId,
                phoneNumber,
                dob
            )

        } catch (e: Exception) {
            Logger.w(e)
        }
    }

    private fun setNameOrEmail(userName: String?, email : String?) {
        binding?.usernameTv?.text = userName
        binding?.userEmail?.text = email
        if (userName?.length in 2..3) {
            binding?.profileText?.text = userName?.substring(0, 2)
        } else {
            binding?.profileText?.text = userName
        }
        setUserProfilePic()
    }

    fun updateAppSync() {
        isLogin = mPreference!!.appPrefLoginStatus
        if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            setVerify()
        }
    }

    private fun setVerify() {
        val tempResponse = mPreference!!.appPrefUser
        if (!StringUtils.isNullOrEmptyOrZero(tempResponse)) {
            val dataModel = Gson().fromJson(tempResponse, AppUserModel::class.java)
            //getBinding().loginBtn.setVisibility(View.GONE);
            if (dataModel != null) {
                val userName = dataModel.name
                if (userName != null && !userName.equals("", ignoreCase = true) && !userName.equals(
                        "null",
                        ignoreCase = true
                    )
                ) {
                    binding?.progressBarHandleDelay?.visibility = View.GONE
                    setNameOrEmail(userName,mPreference?.appPrefUserEmail!!)
                } else {
                    //  binding!!.profileText.setBackgroundResource(R.drawable.profile)
                }
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mListener = if (context is OnMyListInteractionListener) {
            context
        } else {
            throw RuntimeException(
                context.toString()
                        + " must implement OnFragmentInteractionListener"
            )
        }
    }

    override fun onClick(caption: String) {
        if (caption == myList) {
            if (loginStatus) {
                mListener?.onFragmentInteraction()
            } else {
                gotoLogin()
            }
        } else if (caption == account) {
            if (loginStatus) {
                ActivityLauncher.getInstance().goToAccountSetting(requireActivity(), AccountSettingActivity::class.java)
            } else {
                gotoLogin()
            }
        }else if(caption==sponsor){
      //  ActivityLauncher.getInstance().artistActivity(requireActivity(),ArtistActivity::class.java)
        ActivityLauncher.getInstance().artistAndSponserActivity(requireActivity(),ArtistAndSponserActivity::class.java)
        }
        else if (caption == settings) {
            ActivityLauncher.getInstance().goToSetting(requireActivity(), ActivitySettings::class.java)
        } else if (caption == buyNow) {
            if (loginStatus) {
                if ((hasEntitlement && offerStatus == AppConstants.PUBLISHED || offerStatus == AppConstants.NOT_FOR_SALE) || onHold) {
                    ActivityLauncher.getInstance().goToPlanScreen(
                        requireActivity(),
                        ActivitySelectSubscriptionPlan::class.java,
                        "settings"
                    )
                } else {
                    ActivityLauncher.getInstance().goToPlanScreen(
                        requireActivity(),
                        ActivitySelectSubscriptionPlan::class.java,
                        ""
                    )
                }
            } else {
                gotoLogin()
            }
        }
        /* else if (caption ==manageSubscription) {
              if (loginStatus) {
                  ActivityLauncher.getInstance().goToPlanScreen(requireActivity(), ActivitySelectSubscriptionPlan::class.java, "moreFragment")
              } else {
                  ActivityLauncher.getInstance().goToLogin(requireActivity(), ActivityLogin::class.java)
              }
          }*/
        else if (caption == orderHistory) {
            if (loginStatus) {
                ActivityLauncher.getInstance().orderHistroy(requireActivity(), OrderHistoryActivity::class.java)
            } else {
                gotoLogin()
            }
        } else if (caption == deviceManagement) {
            if (loginStatus) {
                ActivityLauncher.getInstance()
                    .DeviceManagement(requireActivity(), DeviceManagerActivity::class.java)
            } else {
                gotoLogin()
            }
        } else if (caption == activateTVDevice) {
            if (loginStatus) {
                ActivityLauncher.getInstance()
                    .ActivateTV(requireActivity(), ActivateDeviceActivity::class.java)
            } else {
                gotoLogin()
            }
        }  else if (caption == languages) {
                val intent = Intent(requireActivity(), ChangeLanguageActivity::class.java)
                startActivity(intent)
        } else if (caption == privacyPolicy) {
            requireActivity().startActivity(
                Intent(
                    requireActivity(),
                    HelpActivity::class.java
                ).putExtra("type", "2")
            )
        } else if (caption == termsCondition) {
            requireActivity().startActivity(
                Intent(
                    requireActivity(),
                    HelpActivity::class.java
                ).putExtra("type", "1")
            )
        } else if (caption == contactUs) {
            requireActivity().startActivity(
                Intent(requireActivity(), HelpActivity::class.java).putExtra("type", "3"))
        } else if (caption == myPlaylist){
            requireActivity().supportFragmentManager.beginTransaction().replace(R.id.content_frame, MyPlaylistFragment(), AppConstants.TAG_PLAYLIST_FRAGMENT).addToBackStack(null).commit()
        }
        else if (caption == rateUs) {
            val uri: Uri =
                Uri.parse("market://details?id=${BuildConstants.FIREBASE_ANDROID_PACKAGE}")
            val goToMarket = Intent(Intent.ACTION_VIEW, uri)
            // To count with Play market backstack, After pressing back button,
            // to taken back to our application, we need to add following flags to intent.
            goToMarket.addFlags(
                Intent.FLAG_ACTIVITY_NO_HISTORY or
                        Intent.FLAG_ACTIVITY_NEW_DOCUMENT or
                        Intent.FLAG_ACTIVITY_MULTIPLE_TASK
            )
            try {
                startActivity(goToMarket)
            } catch (e: ActivityNotFoundException) {
                startActivity(
                    Intent(
                        Intent.ACTION_VIEW,
                        Uri.parse("http://play.google.com/store/apps/details?id=${BuildConstants.FIREBASE_ANDROID_PACKAGE}")
                    )
                )
            }
        }else if (caption == changePassword){
            if (loginStatus){
                ActivityLauncher.getInstance()?.changePassword(requireActivity(),
                    ChangePasswordActivity::class.java)
            }else{
                gotoLogin()
            }
        } else if (caption == my_Watch_History){
            if (loginStatus){
                ActivityLauncher.getInstance()?.watchHistory(requireActivity(),WatchListActivity::class.java, "Watch History",true)
            }else{
                gotoLogin()
            }
        }  else if (caption == myFavorite){
            if (loginStatus){
                val screenTitle = myFavorite.substringBefore(",")
                (activity as HomeActivity).launchGridList(screenTitle)
            }else{
                gotoLogin()
            }
        }
//        else if (caption == ugcCreate){
//            if (loginStatus){
//                (activity as HomeActivity).launchShortVideoUploadActivity()
//            }else{
//                gotoLogin()
//            }
//        }
        else if (caption ==  delete_Account){
            if (loginStatus){
                deleteAccountDialog()
            }else{
                gotoLogin()
            }
        } else if (caption ==  signout){
            if (loginStatus){
                isWantSignout = true
                commonDialogWithCancel(
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_logout.toString(),
                        getString(R.string.popup_logout)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_logout_you_want_to_logout.toString(),
                        getString(R.string.popup_logout_you_want_to_logout)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                        getString(R.string.popup_ok)
                    ),
                    stringsHelper.stringParse(
                        stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                        getString(R.string.popup_cancel)
                    )
                )
            }
        }
        else if (caption == manageProfile){
            if (loginStatus){
                Intent(requireActivity(), SecondaryProfileActivity::class.java).also {
                    startActivityForResult(it, RESPONSE_CODE)
                }
            }
            else{
                gotoLogin()
            }
        }

        else if (caption == parentalControl){
            if (loginStatus){
                Intent(requireActivity(), ParentalControlActivity::class.java).also {
                    startActivity(it)
                }
            }
            else{
                gotoLogin()
            }
        }
    }
    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = activity?.supportFragmentManager!!
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = activity?.supportFragmentManager!!
        val commonDialogFragment = CommonDialogFragment.newInstanceWithCancelBtn(title, description,actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }


    private  fun deleteAccountDialog(){

        isDeletionRequired = true
        commonDialogWithCancel(
            stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_delete_account.toString(),
                getString(R.string.popup_delete_account)
            ),
            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_want_to_delete_account.toString(), getString(R.string.popup_want_to_delete_account)),
            stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                getString(R.string.popup_continue)
            ),
            stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                getString(R.string.popup_cancel)
            )
        )

    }

    private fun gotoLogin(){
        ActivityLauncher.getInstance()
            .loginActivity(requireActivity(), ActivityLogin::class.java, "")
    }

    inner class AppSyncBroadcast : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            try {
                updateAppSync()
            } catch (e: Exception) {
                Logger.w(e)
            }
        }
    }

    private fun logoutCall() {
        if (CheckInternetConnection.isOnline(requireActivity())) {
            clearCredientials(mPreference)
            hitApiLogout(activity, mPreference!!.appPrefAccessToken)
        }
    }
    private fun deleteAccount(){
        if (NetworkConnectivity.isOnline(requireActivity())) {
            binding!!.progressBar.visibility = View.VISIBLE
            val token = mPreference!!.appPrefAccessToken
            removeViewModel = ViewModelProvider(requireActivity()).get(RegistrationLoginViewModel::class.java)
            removeViewModel?.deleteAccount(token)
                ?.observe(viewLifecycleOwner) { deleteAccountResponse ->
                    binding!!.progressBar.visibility = View.GONE
                    if (deleteAccountResponse != null) {
                        if (deleteAccountResponse.responseCode == 2001) {
                            Toast.makeText(requireActivity(), getString(R.string.account_deletion_success_msg), Toast.LENGTH_SHORT).show()
                            KsPreferenceKeys.getInstance().deleteAccountRequestStatus = true
                        } else {
                            isDeletionRequired=false
                            if (deleteAccountResponse.responseCode == 4906) {
                                KsPreferenceKeys.getInstance().deleteAccountRequestStatus = true
                                commonDialog(
                                  "",
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_your_previous_account_already_review.toString(),
                                        getString(R.string.popup_your_previous_account_already_review))
                                    ,
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue))
                                )
                            } else {
                                commonDialog(
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                        getString(R.string.popup_error)
                                    ), stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                        getString(R.string.something_went_wrong)
                                    ), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue))
                                )
                            }
                        }
                    }
                }
        } else {
            Toast.makeText(
                requireActivity(),
                getString(R.string.no_internet_connection),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun setProfileData(activeUserData: SecondaryProfileData?) {
        Log.d("ProfileData", "Active User Data: $activeUserData")
        activeUserData?.let {
            binding?.usernameTv?.text = it?.name
            val params = binding?.usernameTv?.layoutParams as LinearLayout.LayoutParams
            if(context?.let { it1 -> isTablet(it1) } == true){
                params.marginStart = 65
            }
            else{ params.marginStart = 130 }
            binding?.usernameTv?.layoutParams = params
            binding?.notVerifiedMsg?.visibility = View.GONE
            setUserProfilePic()
        }

//        binding?.usernameTv?.text = activeUserData?.name
//        val params = binding?.usernameTv?.layoutParams as LinearLayout.LayoutParams
//        params.marginStart = 130
//        binding?.usernameTv?.layoutParams = params
//
//        binding?.notVerifiedMsg?.visibility = View.GONE
//        setUserProfilePic()
    }

    private fun setUserProfilePic() {
        val profilePicUrl = mPreference?.isActiveUserProfileData?.profilePicURL
        binding?.let {
            Glide.with(this)
                .load(profilePicUrl)
                .placeholder(R.drawable.profile_avtar_logo)
                .error(R.drawable.profile_avtar_logo)
                .into(it.ivProfilePic)
            if (profilePicUrl != null)
                mPreference?.appPrefUserProfilePic = profilePicUrl
        }
    }

    override fun onActionBtnClicked() {
        if (isDeletionRequired){
            deleteAccount()
        }
        if (isWantSignout){
            hitApiLogout(requireContext(),mPreference?.appPrefAccessToken)
            clearCredientials(mPreference)
            ActivityLauncher.getInstance().homeScreen(requireActivity(), HomeActivity::class.java,false,"","",0)
            isWantSignout = false
        }
    }

    override fun onCancelBtnClicked() {

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == responseCode) {
            val updatedData = data?.getStringExtra(AppConstants.UPDATE_NAME)
            val updatedProfile = data?.getStringExtra(AppConstants.UPDATE_PROFILE)
            binding?.usernameTv?.text = updatedData
            Log.d("onActivityResult", "Updated Data: $updatedData")
            setNameOrEmail(updatedData, mPreference?.userProfile?.data?.email.toString())
            context?.let { Glide.with(it).load(updatedProfile).into(binding?.ivProfilePic!!) }
        }
        if (resultCode == Activity.RESULT_OK && requestCode == RESPONSE_CODE){
            if (data?.getBooleanExtra(AppConstants.UPDATE_MULTI_PROFILE, false) == true){
                setProfileData(mPreference?.isActiveUserProfileData)
            }
        }
        if (resultCode == RESULT_OK && requestCode == Constants.REQUEST_CODE) {
            if (data?.getBooleanExtra(Constants.OPEN_SHORTS_FRAGMENT, false) == true) {
                val reelsContentItem = data.getSerializableExtra(Constants.SHORTS_REELS_BUNDLE) as ReelsContentItem
                (activity as HomeActivity).openShortsReelsFragment(reelsContentItem)
            }
        }
    }

    private fun callShimmer() {
        val shimmerAdapter = ShimmerAdapter(
            activity,
            ShimmerDataModel(activity)
                .getList(0),
            ShimmerDataModel(
                activity
            ).slides
        )
        binding?.moreFragmentsRv?.adapter = shimmerAdapter
    }

    companion object {
        const val REQUEST_CODE = 1002
    }
}