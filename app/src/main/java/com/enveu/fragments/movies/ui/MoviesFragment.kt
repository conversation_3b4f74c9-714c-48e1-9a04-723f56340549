package com.enveu.fragments.movies.ui

import android.os.Bundle
import com.enveu.beanModel.TabsBaseFragment
import com.enveu.fragments.movies.viewModel.MoviesFragmentViewModel

class MoviesFragment(private val tabID : String) : TabsBaseFragment<MoviesFragmentViewModel?>() {

    constructor() : this("0")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setViewModel(MoviesFragmentViewModel::class.java, tabID)
    }
}