package com.enveu.fragments.myList.ui

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.listing.callback.ItemClickListener
import com.enveu.activities.listing.listadapter.ListAdapter
import com.enveu.adapters.CommonShimmerAdapter
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.emptyResponse.ResponseEmpty
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.client.enums.ImageType
import com.enveu.client.watchHistory.beans.ResponseWatchHistoryAssetList
import com.enveu.databinding.FragmentMyListBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstance
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.rotateImageLocaleWise
import com.enveu.utils.stringsJson.converter.StringsHelper

class MyListFragment : BaseBindingFragment<FragmentMyListBinding?>(), OnSongItemClick,
    ItemClickListener, CommonDialogFragment.EditDialogListener {
    private var token: String? = null
    private var railInjectionHelper: RailInjectionHelper? = null
    private var bookmarkingViewModel: BookmarkingViewModel? = null
    private var counterLimit = 1
    private var visibleItemCount = 0
    private var totalItemCount = 0
    private var pastVisibleItems = 0
    private var mIsLoading = false
    private var counter = 0
    private var isScrolling = false
    private var mScrollY = 0
    private var commonLandscapeAdapter: ListAdapter? = null
    private var mPreference: KsPreferenceKeys? = null
    private var featureModel: FeatureFlagModel? = null

    private var notReload = true
    private var adapterPurchase: CommonShimmerAdapter? = null
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (activity != null && activity is HomeActivity)
            (activity as HomeActivity).toolFrame(1)
        (activity as HomeActivity).detailFrameVisibility()

    }
    @SuppressLint("SuspiciousIndentation")
    override fun onDetach() {
        super.onDetach()
        if (activity != null && activity is HomeActivity)
            (activity as HomeActivity).toolFrame(0)
            (activity as HomeActivity).detailFrameVisibility()
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        AnalyticsUtils.trackScreenView(context,AppConstants.MY_LIST)
        getAppLevelJsonData()
        binding!!.noResultFound.stringData = stringsHelper
        binding!!.colorsData = colorsHelper
        binding!!.stringData = stringsHelper
        binding!!.noResultFound.toolbar.colorsData = colorsHelper
        binding!!.noResultFound.toolbar.stringData = stringsHelper
        binding!!.toolbar.colorsData = colorsHelper
        binding!!.toolbar.stringData = stringsHelper
        binding!!.noResultFound.stringData = stringsHelper
        binding!!.noResultFound.colorsData = colorsHelper
        setToolbar()
    }
    private fun getAppLevelJsonData() {
        featureModel = AppConfigMethod.parseFeatureFlagList()
    }
    private fun setToolbar() {
        binding!!.toolbar.backArrow.let { rotateImageLocaleWise(it) }
        binding!!.toolbar.logoMain2.visibility = View.GONE
        binding!!.toolbar.titleMid.visibility = View.VISIBLE
        binding!!.toolbar.backLayout.visibility = View.VISIBLE
        val myList = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.my_list.toString(),
            getString(R.string.my_list)
        )
        val title=myList.split(",")[0]
        binding!!.toolbar.titleMid.text = title
        binding!!.toolbar.searchIcon.visibility = View.GONE
        binding!!.toolbar.titleMid.setBackgroundResource(0)
        binding!!.toolbar.backLayout.setOnClickListener { activity?.onBackPressed() }
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): FragmentMyListBinding {
        return FragmentMyListBinding.inflate(inflater)
    }

    public override fun onResume() {
        super.onResume()
        if (notReload) {
            reloadMyListData()
        }
        if (KsPreferenceKeys.getInstance().isWatchListButtonClick) {
            KsPreferenceKeys.getInstance().isWatchListButtonClick = false
            loadData()
        }
    }

    private fun loadData() {
        Handler().postDelayed({
            binding!!.watchListRecyclerView.visibility = View.VISIBLE
            binding!!.noResultLayout.visibility = View.GONE
            adapterPurchase = null
            commonLandscapeAdapter = null
            counter = 0
            counterLimit = 1
            initialize()
            callShimmer(requireActivity(), binding!!.watchListRecyclerView)
            watchListData
            swipeToRefresh()
            binding!!.watchListRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager?
                    if (dy > 0) {
                        assert(layoutManager != null)
                        visibleItemCount = layoutManager!!.childCount
                        totalItemCount = layoutManager.itemCount
                        pastVisibleItems = layoutManager.findFirstVisibleItemPosition()
                        if (mIsLoading) {
                            if (visibleItemCount + pastVisibleItems >= totalItemCount) {
                                val adapterSize = binding!!.watchListRecyclerView.adapter?.itemCount
                                if (adapterSize != null) {
                                    if (adapterSize > 8) {
                                        mIsLoading = false
                                        counter++
                                        isScrolling = true
                                        mScrollY += dy
                                        watchListData
                                    }
                                }
                            }
                        }
                    }
                }
            })
        }, 100)
    }

    private fun swipeToRefresh() {
        binding!!.swipeContainer.setOnRefreshListener {
            if (commonLandscapeAdapter != null) {
                commonLandscapeAdapter!!.clear()
            }
            KsPreferenceKeys.getInstance().setIsFirstTimeCame(false)
            reloadMyListData()
            swipeToRefreshCheck()
        }
    }

    private fun swipeToRefreshCheck() {
        if (binding!!.swipeContainer.isRefreshing) {
            binding!!.swipeContainer.isRefreshing = false
        }
    }

    private fun reloadMyListData() {
        notReload = false
        Handler().postDelayed({
            binding!!.watchListRecyclerView.visibility = View.VISIBLE
            binding!!.noResultLayout.visibility = View.GONE
            adapterPurchase = null
            commonLandscapeAdapter = null
            counter = 0
            counterLimit = 1
            initialize()
            callShimmer(requireActivity(), binding!!.watchListRecyclerView)
            watchListData
            swipeToRefresh()
            binding!!.watchListRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager?
                    if (dy > 0) {
                        assert(layoutManager != null)
                        visibleItemCount = layoutManager!!.childCount
                        totalItemCount = layoutManager.itemCount
                        pastVisibleItems = layoutManager.findFirstVisibleItemPosition()
                        if (mIsLoading) {
                            if (visibleItemCount + pastVisibleItems >= totalItemCount) {
                                val adapterSize = binding!!.watchListRecyclerView.adapter?.itemCount
                                if (adapterSize != null) {
                                    if (adapterSize > 8) {
                                        mIsLoading = false
                                        counter++
                                        isScrolling = true
                                        mScrollY += dy
                                        watchListData
                                    }
                                }
                            }
                        }
                    }
                }
            })
        }, 100)
    }

    private fun initialize() {
        mPreference = KsPreferenceKeys.getInstance()
        bookmarkingViewModel = ViewModelProvider(this)[BookmarkingViewModel::class.java]
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        binding!!.watchListRecyclerView.setHasFixedSize(true)
    }

    private fun callShimmer(context: Context, recyclerView: RecyclerView) {
        adapterPurchase = CommonShimmerAdapter()
        val mLayoutManager: RecyclerView.LayoutManager = LinearLayoutManager(context)
        recyclerView.layoutManager = mLayoutManager
        recyclerView.itemAnimator = DefaultItemAnimator()
        recyclerView.adapter = adapterPurchase
    }

    private val watchListData: Unit
        get() {
            try {
                token = mPreference!!.appPrefAccessToken
                if (counter <= counterLimit - 1) {
                    bookmarkingViewModel!!.getMywatchListData(token, counter, AppConstants.PAGE_SIZE).observe(this) { responseWatchHistoryAssetList: ResponseWatchHistoryAssetList ->
                        if (responseWatchHistoryAssetList.isStatus) {
                            if (responseWatchHistoryAssetList.data != null) {
                                counterLimit = responseWatchHistoryAssetList.data.totalPages
                                var videoIds = ""
                                if (responseWatchHistoryAssetList.data != null) {
                                    val watchHistoryList = responseWatchHistoryAssetList.data.items
                                    for (historyItem in watchHistoryList) {
                                        videoIds = videoIds + historyItem.assetId.toString() + ","
                                    }
                                    railInjectionHelper!!.getWatchHistoryAssets(watchHistoryList, videoIds,featureModel?.featureFlag?.IS_MUSIC_APP).observe(this) { railCommonData: RailCommonData? ->
                                        notReload = true
                                        if (railCommonData != null) {
                                            binding!!.watchListRecyclerView.visibility = View.VISIBLE
                                            binding!!.progressBar.visibility = View.GONE
                                            binding!!.noResultLayout.visibility = View.GONE
                                            setRail(railCommonData)
                                        } else {
                                            hideRecyclerViewData()
                                        }
                                    }
                                } else {
                                    hideRecyclerViewData()
                                }
                            } else {
                                hideRecyclerViewData()
                            }
                        } else {
                            hideRecyclerViewData()
                        }
                    }
                }
            } catch (e: Exception) {
                Logger.w(e)
            }
        }


    private fun hideRecyclerViewData() {
        binding!!.watchListRecyclerView.visibility = View.GONE
        binding!!.progressBar.visibility = View.GONE
        binding!!.toolbar.root.visibility = View.GONE
        binding!!.noResultLayout.visibility = View.VISIBLE
        binding!!.noResultFound.toolbar.backLayout.visibility = View.VISIBLE
        binding!!.noResultFound.toolbar.logoMain2.visibility = View.GONE
        binding!!.noResultFound.toolbar.searchIcon.visibility = View.GONE
        val myList = stringsHelper.stringParse(
            stringsHelper.instance()?.data?.config?.my_list.toString(),
            getString(R.string.my_list)
        )
        val title=myList.split(",")[0]
        binding!!.noResultFound.toolbar.titleMid.text = title
        binding!!.noResultFound.toolbar.titleMid.visibility = View.VISIBLE
        binding!!.noResultFound.toolbar.titleMid.setBackgroundResource(0)
        binding!!.noResultFound.toolbar.backLayout.setOnClickListener { activity?.onBackPressed() }
    }


    private var railCommonData: RailCommonData? = null
    private fun setRail(playlistRailData: RailCommonData) {
        railCommonData = playlistRailData
        if (isScrolling) {
            setUiComponents(playlistRailData)
        } else {
            binding!!.progressBar.visibility = View.GONE
            mIsLoading = true
            if (commonLandscapeAdapter == null) {
                RecyclerAnimator(requireActivity()).animate(binding!!.watchListRecyclerView)
                val imageType=
                    if(featureModel?.featureFlag?.IS_SQUARE_DEFAULT==true){
                        ImageType.SQR.name
                    }else{
                        ImageType.LDS.name
                    }
                commonLandscapeAdapter =
                    ListAdapter(
                        playlistRailData.enveuVideoItemBeans,
                        this,
                        AppCommonMethod.getListViewType(imageType),
                        requireActivity()
                    )
                commonLandscapeAdapter!!.setWatchList()
                binding!!.watchListRecyclerView.adapter = commonLandscapeAdapter
            } else {
                commonLandscapeAdapter!!.clear()
                commonLandscapeAdapter!!.notifyAdapter(playlistRailData.enveuVideoItemBeans)
            }
            mIsLoading = playlistRailData.maxContent != commonLandscapeAdapter!!.itemCount
            binding!!.watchListRecyclerView.scrollToPosition(mScrollY)
        }
        binding!!.progressBar.visibility = View.GONE
    }

    private fun setUiComponents(playlistRailData: RailCommonData) {
        commonLandscapeAdapter!!.notifyAdapter(playlistRailData.enveuVideoItemBeans)
        mIsLoading = playlistRailData.maxContent != commonLandscapeAdapter!!.itemCount
    }

    override fun onRowItemClicked(itemValue: EnveuVideoItemBean, position: Int) {
        val assetType = itemValue.assetType
        var mediType: String? = ""
        if (itemValue.assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
            mediType = itemValue.videoDetails.videoType
        } else  if (itemValue.assetType.equals(AppConstants.CUSTOM, ignoreCase = true)) {
            mediType = itemValue.customType
        }else if (itemValue.assetType.equals(AppConstants.AUDIO, ignoreCase = true)||itemValue.assetType.equals(AppConstants.PERSON, ignoreCase = true)){
            mediType=itemValue.contentType
        }
        AppCommonMethod.launchDetailScreen(requireActivity(), assetType,itemValue.id,itemValue.sku,mediType, itemValue.title?:"",itemValue.externalRefId?:"",itemValue.posterURL?:"",0,itemValue.contentSlug?:"",itemValue)
    }

    override fun onDeleteWatchListClicked(assetID: Int, tittle: String, position: Int) {
        AppCommonMethod.assetId = assetID
        commonDialog(
            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_remove.toString(), getString(R.string.popup_remove))
                    + " " + tittle,
            activity?.applicationContext?.getString(R.string.delete_hunting)!!,
            activity?.applicationContext?.getString(R.string.continue_but)!!
        )
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = activity?.supportFragmentManager!!
        val commonDialogFragment = newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun onActionBtnClicked() {
        binding!!.progressBar.visibility = View.VISIBLE
        bookmarkingViewModel!!.hitRemoveWatchlist(token, AppCommonMethod.assetId).observe(this) { responseEmpty: ResponseEmpty? ->
            if (responseEmpty != null && responseEmpty.isStatus) {
                Toast.makeText(activity, getString(R.string.remove_video_from_list), Toast.LENGTH_SHORT).show()
                counter = 0
                commonLandscapeAdapter!!.clear()
                watchListData
            } else {
                binding!!.progressBar.visibility = View.GONE
            }
        }
    }

    override fun onCancelBtnClicked() {

    }

    override fun songItemClick(
        songList: List<DataItem>,
        song: DataItem,
        extarnalRefId: String,
        imageContent: ImageContent?,
        image:String?,
        playQueueItems:Boolean?, isQueueItemClick: Boolean, songsPosition:Int) {

    }
}