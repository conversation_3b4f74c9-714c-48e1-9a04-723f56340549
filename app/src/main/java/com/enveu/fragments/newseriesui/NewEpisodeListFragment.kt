package com.enveu.fragments.newseriesui

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.enveu.activities.detail.ui.EpisodeActivity
import com.enveu.activities.detail.ui.MatchDetailActivity
import com.enveu.activities.series.adapter.SeasonAdapter
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.FirstEpisodeItem
import com.enveu.databinding.FragmentNewEpisodeListBinding
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.utils.PaginationScrollListener
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.SpacingItemDecoration

class NewEpisodeListFragment() : BaseBindingFragment<FragmentNewEpisodeListBinding>(),SeasonAdapter.EpisodeItemClick,
    FirstEpisodeItem {

    private var allEpiosdes:MutableList<EnveuVideoItemBean>? = ArrayList()
    private lateinit var railCommonData: RailCommonData
    private var seasonAdapter:SeasonAdapter?=null
    private var totalSeason=0
    private var seasonNumber=0
    private var seriesId=0
    private var currentAssetId = 0
    private var pageDataSize = 10;
    private var currentSeasonPos = 0
    private var pageNumber = 0
    private var totalPages = 0
    private var redirection=""
    private var seasonData: List<EnveuVideoItemBean>?=null
    private var railInjectionHelper:RailInjectionHelper?=null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        railInjectionHelper= ViewModelProvider(this)[RailInjectionHelper::class.java]
    }

    fun setSeasonData(seasonData: List<EnveuVideoItemBean>?) {
        this.seasonData=seasonData
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        totalSeason= arguments?.getInt("totalSeason")?:0
        seasonNumber = arguments?.getInt("seasonNumber")?:0
        seriesId = arguments?.getInt("seriesId")?:0
        currentAssetId=arguments?.getInt("currentAssetId") ?: 0
        currentSeasonPos=arguments?.getInt("position") ?: 0
        redirection=arguments?.getString(AppConstants.FROM_REDIRECTION) ?: ""
        setObserver()
        setClicks()
        callAndSetEpisodesData()
    }

    private fun setClicks() {
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setObserver() {

    }

    private fun callAndSetEpisodesData() {
        if (allEpiosdes!=null) {
            allEpiosdes?.clear()
        }
        binding!!.fnelRv.addItemDecoration(
            SpacingItemDecoration(
                8,
                SpacingItemDecoration.VERTICAL
            )
        )
        (binding!!.fnelRv.itemAnimator as SimpleItemAnimator?)!!.supportsChangeAnimations = true
        seasonData?.let {
            if (redirection.equals(AppConstants.SECOND_CONTAINER)){
                railInjectionHelper!!.getEpisodeFromSeason(it[currentSeasonPos].id, 0, 20)
                    .observe(requireActivity()) { response ->
                        setResposeOfGetEpisodeFromSeason(response)
                    }
            }else {
                getEpisodesList(it.get(currentSeasonPos).id)
            }
        }
    }

    private fun getEpisodesList(seasonId: Int) {
        railInjectionHelper!!.getEpisodeFromSeason(seasonId, pageNumber, pageDataSize).observe(requireActivity()) { response ->
            setResposeOfGetEpisodeFromSeason(response)
        }
    }

    private fun setResposeOfGetEpisodeFromSeason(response: ResponseModel<Any>?) {
        hideProgressBar()
        if (response != null) {
            if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {
            } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                if (response.baseCategory != null) {
                    val enveuCommonResponse = response.baseCategory as RailCommonData
                    railCommonData = enveuCommonResponse
                    allEpiosdes?.addAll(enveuCommonResponse.enveuVideoItemBeans)
                    allEpiosdes?.let {it1->
                        if (context is SeriesDetailActivity) (context as SeriesDetailActivity).setEpisodes(
                            seasonData?.get(currentSeasonPos)?.id.toString(),
                            it1
                        )
                    }
                        totalPages = enveuCommonResponse.pageTotal
                        setEpisodeAdapter()
                    if (context is EpisodeActivity) {
                        (context as EpisodeActivity).episodesList(enveuCommonResponse.enveuVideoItemBeans)
                    } else if (context is SeriesDetailActivity) {
                        (context as SeriesDetailActivity).episodesList(enveuCommonResponse.enveuVideoItemBeans)
                    }
                }
            } else if (response.status.equals(
                    APIStatus.ERROR.name,
                    ignoreCase = true
                )
            ) {
                if (response.errorModel.errorCode != 0) {
                    if (context is SeriesDetailActivity) {
                        (context as SeriesDetailActivity).episodesList(null)
                    }
                }
            } else if (response.status.equals(
                    APIStatus.FAILURE.name,
                    ignoreCase = true
                )
            ) {
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setEpisodeAdapter() {
        pageNumber += 1
        if (seasonAdapter == null) {
            RecyclerAnimator(activity).animate(binding!!.fnelRv)
            seasonAdapter = SeasonAdapter(
                <EMAIL>(),
                allEpiosdes!!,
                seriesId,
                currentAssetId,
                this@NewEpisodeListFragment,
                this@NewEpisodeListFragment
            )
            seasonAdapter?.setIsFromELF(true)
            val layoutManager = LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
            binding!!.fnelRv.layoutManager = layoutManager
            (binding!!.fnelRv.itemAnimator as SimpleItemAnimator?)!!.supportsChangeAnimations = true
            binding!!.fnelRv.adapter = seasonAdapter
            if (allEpiosdes?.isEmpty() == true) {
                binding.rlNoData.visibility = View.VISIBLE
                binding.fnelRv.visibility = View.INVISIBLE
            }
            callEpisodesPagination(layoutManager)
        }
        else{
            allEpiosdes?.let { allEpisodesData ->
                synchronized(allEpisodesData) {
                    seasonAdapter?.notifyItemChanged(allEpisodesData.size.minus(1))
                    binding?.fnelRv?.scrollToPosition(500)
                }
            }
        }
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): FragmentNewEpisodeListBinding {
        return FragmentNewEpisodeListBinding.inflate(inflater)
    }

    override fun onItemClick(itemValue: EnveuVideoItemBean?, isPremium: Boolean, position: Int) {
        if (context is SeriesDetailActivity){
            (context as SeriesDetailActivity).setFullscreenVisiblity(false)
            itemValue?.let {
                (context as SeriesDetailActivity).episodeClicked(it)
            }
        }
    }

    override fun getFirstItem(itemValue: EnveuVideoItemBean?) {
        Log.d("getFirstItem", "getFirstItem1: $itemValue")
    }
    override fun episodeClicked(itemBean: EnveuVideoItemBean?) {

    }

    private fun callEpisodesPagination(layoutManager: LinearLayoutManager) {
        binding!!.fnelRv.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun onLoadMore(page: Int) {
                if (totalPages > page) {
                    getEpisodesList(seasonData!!.get(currentSeasonPos).id)
                }
            }
        })
    }

    private fun hideProgressBar() {
        if (context is  SeriesDetailActivity) {
            (context as SeriesDetailActivity).isSeasonData = true
            (context as SeriesDetailActivity).stopShimmer()
            (context as SeriesDetailActivity).dismissLoading((context as SeriesDetailActivity).binding!!.progressBar)
        }  else if (context is MatchDetailActivity) {
            (context as MatchDetailActivity).isSeasonData = true
            (context as MatchDetailActivity).stopShimmer()
            (context as MatchDetailActivity).dismissLoading((context as MatchDetailActivity).binding!!.progressBar)
        } else if (context is EpisodeActivity) {
            (context as EpisodeActivity).dismissLoading((context as EpisodeActivity).binding!!.progressBar)
            (context as EpisodeActivity).isSeasonData = true
            (context as EpisodeActivity).stopShimmercheck()
        }
    }
}

interface NewSeriesEpisodeCallbackFragment{
    fun changeMoreVisiblity(flag:Boolean,position: Int)
}