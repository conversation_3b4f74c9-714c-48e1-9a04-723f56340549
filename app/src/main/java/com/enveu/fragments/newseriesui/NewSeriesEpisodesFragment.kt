package com.enveu.fragments.newseriesui

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.adapters.SeasonEpisodesAdapter
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaConfig
import com.enveu.appLevelModel.MediaInfo
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.bean_model_v2_0.listAll.SeriesCustomData
import com.enveu.databinding.FragmentNewSeriesEpisodesBinding
import com.enveu.networking.apistatus.APIStatus
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.RailInjectionHelper
import com.google.android.material.tabs.TabLayoutMediator


class NewSeriesEpisodesFragment() : BaseBindingFragment<FragmentNewSeriesEpisodesBinding?>() {

    private val viewModel: NewSeriesEpisodesViewModel by viewModels()
    private lateinit var railInjectionHelper: RailInjectionHelper
    private var mediaConfig: MediaConfig? = null
    private var featureList: FeatureFlagModel? = null
    private var mediaType = ""
    private var seriesId = 0
    private var isMatchDetailPage = false
    private var seasonNumber = 0
    private lateinit var railCommonData:RailCommonData
    var customData: SeriesCustomData?= null
    var seasonData:List<EnveuVideoItemBean>?=null
    var redirection=""

    private val scaleAnimation = AnimatorSet()
    private lateinit var tabLayoutMediator: TabLayoutMediator
    private var seriesDetailBean: EnveuVideoItemBean? = null
    private var vpPosition=0

    companion object{
        var isShowMore=false
        var vpSelectedPosition=0
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getIntentData()
        getAppLevelJsonData()
        railInjectionHelper=ViewModelProvider(this)[RailInjectionHelper::class.java]
        viewModel.setRailInjectionHelper(railInjectionHelper)
        viewModel.fragment=this@NewSeriesEpisodesFragment
    }

    private fun getAppLevelJsonData() {
        val mediaConfigInfo: MediaInfo? = AppConfigMethod.getMediaMappingByType(mediaType)
        mediaConfig = AppConfigMethod.getMediaConfigByType(mediaConfigInfo?.pageType.toString())
        featureList = AppConfigMethod.parseFeatureFlagList()

    }
    private fun getIntentData() {
        mediaType = arguments?.getString(AppConstants.BUNDLE_MEDIA_TYPE)?:""
        seriesId = arguments?.getInt(AppConstants.BUNDLE_SERIES_ID)?:0
        seasonNumber = arguments?.getInt(AppConstants.BUNDLE_SEASON_NUMBER)?:0
        redirection = arguments?.getString(AppConstants.FROM_REDIRECTION)?:""
        seriesDetailBean = arguments?.getSerializable(AppConstants.BUNDLE_SERIES_DETAIL) as EnveuVideoItemBean?
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setClicks()
        setObserver()
//        if (!SeriesDetailActivity.seriesSeasons.containsKey(seriesId.toString())) {
//            viewModel.getSessionListFromApi(seriesId)
//        } else {
//            viewModel.sessionsList = SeriesDetailActivity.seriesSeasons[seriesId.toString()]!!.enveuVideoItemBeans
//            setUI()
//        }

        val scaleX = ObjectAnimator.ofFloat(binding!!.fnseClickMore, "scaleX", 1.0f, 1.1f)
        scaleX.setDuration(100)
        scaleX.repeatCount = 1
        scaleX.repeatMode = ObjectAnimator.REVERSE

        val scaleY = ObjectAnimator.ofFloat(binding!!.fnseClickMore, "scaleY", 1.0f, 1.1f)
        scaleY.setDuration(100)
        scaleY.repeatCount = 1
        scaleY.repeatMode = ObjectAnimator.REVERSE

        scaleAnimation.playTogether(scaleX, scaleY)
    }

    @SuppressLint("SetTextI18n")
    private fun setClicks() {
        binding!!.fnseClickMore.setOnClickListener {
            scaleAnimation.start()
            if (isShowMore){
                binding!!.fnseClickMoreTxt.text="View More"
                isShowMore=false
            }else{
                binding!!.fnseClickMoreTxt.text="View Less"
                isShowMore=true
            }
            Log.d("myCheck","At NewEpisodesListFragment, isMore value $isShowMore")
            (context as SeriesDetailActivity)?.setFullscreenVisiblity(isShowMore)
        }
    }

    private fun setObserver() {
        railInjectionHelper?.getSeasonEpisodesV2(seriesId, 0, 50, AppConstants.ENT_SEASON_CUSTOM_DATA)?.observe(requireActivity()) { response ->
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {
                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    Log.d("DetailPage", "at seriesEpisodeFragment, success")
                    if (response.baseCategory != null) {
                        Log.d("DetailPage", "at seriesEpisodeFragment, response baseCategory not null")
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                        railCommonData = enveuCommonResponse
                        if (railCommonData.enveuVideoItemBeans.isNotEmpty()){
                            viewModel.sessionsList = railCommonData.enveuVideoItemBeans as MutableList<EnveuVideoItemBean>
                            seasonData=railCommonData.enveuVideoItemBeans
                            if(context is SeriesDetailActivity) (context as SeriesDetailActivity).setSeasonList(seriesId,railCommonData)
                        }else{
                            setVisiblityOfThisFragment()
                            (activity as SeriesDetailActivity).hideProgressBar()
                        }
//                        parseSeriesData(enveuCommonResponse)
                        setUI()
                    }else{
                        (activity as SeriesDetailActivity).hideProgressBar()
                        setVisiblityOfThisFragment() }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        if (context is SeriesDetailActivity) {
                            (context as SeriesDetailActivity).episodesList(null)
                        }

//                        parseSeriesData(null)
                    }
                    setVisiblityOfThisFragment()
                    (activity as SeriesDetailActivity).hideProgressBar()
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    (activity as SeriesDetailActivity).hideProgressBar()
                }
            } else {
                if (context is SeriesDetailActivity) {
                    (context as SeriesDetailActivity).episodesList(null)
                    (activity as SeriesDetailActivity).hideProgressBar()
                }
                setVisiblityOfThisFragment()
            }
        }
        viewModel.seriesSeasons.observe(this.requireActivity(), Observer {

//            Log.d("myCheck","series season response: ${it.toString()}")
        })
    }

    private fun setVisiblityOfThisFragment() {
        if (context is SeriesDetailActivity){
            (context as SeriesDetailActivity).setSeasonUIVisiblity(false)
        }
    }

    private fun setUI() {
        if (context is SeriesDetailActivity){
            (context as SeriesDetailActivity).setSeasonUIVisiblity(true)
        }
        seasonData?.let {
            val adapter = SeasonEpisodesAdapter(this.requireActivity(),seasonNumber,seriesId,seriesId,viewModel.sessionsList.size,it,redirection)
            binding!!.viewPager.adapter = adapter
            binding!!.viewPager.offscreenPageLimit = 5
            binding!!.tabLayout.setPaddingRelative(6,4,6,4)

            binding!!.viewPager.registerOnPageChangeCallback(object : OnPageChangeCallback(){
                override fun onPageScrollStateChanged(state: Int) {
                    super.onPageScrollStateChanged(state)
                }

                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    vpSelectedPosition=position
                    vpPosition=position
                }
            })

            tabLayoutMediator = TabLayoutMediator(binding!!.tabLayout, binding!!.viewPager) { tab, position ->
//            tab.setText(viewModel.sessionsList[position].title)
             //   tab.setText("Season ${position+1}")
                tab.setText(seasonData?.get(position)?.title)
            }
            tabLayoutMediator.attach()

            if (SeriesDetailActivity.isEpisodes){
                var i=1
                for (sl in viewModel.sessionsList){
                    i++
                    if (sl.id == seriesDetailBean?.customData?.episode_season_id?.id){
                        break
                    }
                }
                vpSelectedPosition=i
                setUpForDetail()
            }
        }
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): FragmentNewSeriesEpisodesBinding? {
        return FragmentNewSeriesEpisodesBinding.inflate(inflater)
    }

    override fun onDestroy() {
        super.onDestroy()
        binding!!.viewPager.unregisterOnPageChangeCallback(object :OnPageChangeCallback(){})
    }

    @SuppressLint("SetTextI18n")
    fun setUpForDetail() {
        if (vpSelectedPosition != vpPosition){
            binding!!.viewPager.setCurrentItem(vpSelectedPosition,false)
        }
        if (isShowMore){
            binding!!.fnseClickMoreTxt.text="View Less"
            val paddingBottom=(resources.displayMetrics.density * 39f)
            binding!!.viewPager.setPadding(0,0,0,paddingBottom.toInt())
        }else{
            binding!!.fnseClickMoreTxt.text="View More"
            binding!!.viewPager.setPadding(0,0,0,0)
        }
        viewModel.isShowMore.postValue(isShowMore)
    }
    fun otherToSetOtherData(pageSize:Int){
        var i=0
        for (season in viewModel.sessionsList){
            if (i!=vpPosition){
                viewModel.getEpisodeFromSeason(i,pageSize)
            }
            i++
        }
    }
}

