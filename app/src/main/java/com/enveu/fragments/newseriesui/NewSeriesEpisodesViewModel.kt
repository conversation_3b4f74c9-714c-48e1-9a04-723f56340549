package com.enveu.fragments.newseriesui

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.bean_model_v2_0.listAll.SeriesCustomData
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.utils.Logger
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.RailInjectionHelper
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class NewSeriesEpisodesViewModel : ViewModel() {
    private var railInjectionHelper: RailInjectionHelper? = null
    lateinit var fragment:NewSeriesEpisodesFragment
    lateinit var sessionsList:MutableList<EnveuVideoItemBean>
    lateinit var customData: SeriesCustomData
    val seriesSeasons=MutableLiveData< HashMap< String,ResponseModel<Any> > >()
    val response1= MutableLiveData<ResponseModel<Any>>()
    val isShowMore=MutableLiveData<Boolean>()
    val hashMapRespons= hashMapOf<String,ResponseModel<Any>>()
    var episodeApiCount=0

    val responseEpisodes=MutableLiveData<ResponseModel<Any>>()
    var isEpisodeAvailabe=false
    fun setRailInjectionHelper(rjh:RailInjectionHelper){
        if (railInjectionHelper == null) {
            railInjectionHelper = rjh
        }
    }

    fun getSessionListFromApi(seriesId:Int){
        try {
//            if (!SeriesDetailActivity.seriesSeasons.containsKey(seriesId.toString())) {
                Log.d("myCheck","launch seasonEpisode api")
                viewModelScope.launch {
                    fragment.requireActivity()?.let {
                        if (SeriesDetailActivity.isEpisodes){
                            fragment.customData.let {it1->
                                it1?.episode_series_id?.id?.let { it2 ->
                                    railInjectionHelper?.getSeasonEpisodesV2(it2, 0, 50, AppConstants.ENT_SEASON_CUSTOM_DATA)?.observe(it) { response ->
                                        response1.postValue(response)
                                        isShowMore.postValue(false)
                                    }
                                }
                            }
                        }else {
                            railInjectionHelper?.getSeasonEpisodesV2(
                                seriesId, 0, 50, AppConstants.ENT_SEASON_CUSTOM_DATA
                            )?.observe(it) { response ->
                                response1.postValue(response)
                                isShowMore.postValue(false)
                            }
                        }
                    }
//                }
                }
        } catch (e: Exception) {
            e.message?.let { Logger.d(it) }
            e.printStackTrace()
        }
    }

    fun getEpisodeFromSeason(position:Int,pageDataSize:Int) {
//        if (!hashMapRespons.containsKey(sessionsList[position].id.toString())) {
            episodeApiCount++
            viewModelScope.launch {
                Log.d("myCheck","getEpisodeFromSeason call, count $episodeApiCount")
//                delay((30*episodeApiCount).toLong())
//                if (!hashMapRespons.containsKey(sessionsList[position].id.toString())) {
                    railInjectionHelper!!.getEpisodeFromSeason(sessionsList[position].id, 0, pageDataSize)
                        .observe(fragment.requireActivity()) { response ->
                            responseEpisodes.postValue(
                                response
                            )
//                            val resData=(response.baseCategory as RailCommonData).enveuVideoItemBeans.size
                            hashMapRespons.put(sessionsList[position].id.toString(), response)
                            Log.d("myCheck"," at getEpisodeFromSeason, id:${sessionsList[position].id}, hashMapResponse ${hashMapRespons.toString()}")
                            seriesSeasons.postValue(hashMapRespons)
                        }
//                }else{ Log.d("myCheck"," at getEpisodeFromSeason after counter api doesn't call") }
            }
//        }
    }
}