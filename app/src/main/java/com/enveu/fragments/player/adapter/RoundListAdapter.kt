package com.enveu.fragments.player.adapter

import android.app.AlertDialog
import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.enveu.OttApplication
import com.enveu.R
import com.enveu.beanModel.selectedSeason.SelectedRoundModel

class RoundListAdapter(private val list: ArrayList<SelectedRoundModel>, private val listner: RoundClick) : RecyclerView.Adapter<RoundListAdapter.ViewHolder>() {
     private var alertDialog: AlertDialog? = null


     override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.all_season_listing, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.season.text = OttApplication.context.resources.getString(R.string.round) + " " + list[position].list.seriesCustomData.round_number
        if (list[position].isSelected) {
            holder.season.setTextColor(
                ContextCompat.getColor(
                    holder.season.context,
                    R.color.selected_indicator_color
                )
            )
            val boldTypeface = Typeface.defaultFromStyle(Typeface.BOLD)
            holder.season.typeface = boldTypeface
        } else {
            holder.season.setTextColor(
                ContextCompat.getColor(
                    holder.season.context,
                    R.color.series_detail_description_text_color
                )
            )
            val boldTypeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            holder.season.typeface = boldTypeface
        }
        holder.season.setOnClickListener {
            listner.onRoundClick(list,position)
        }
    }

    override fun getItemCount(): Int {
        return list.size
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var season: TextView

        init {
            season = itemView.findViewById(R.id.season_name)
        }
    }


     interface RoundClick {
         fun onRoundClick(list: ArrayList<SelectedRoundModel>, position: Int)
     }
}
