package com.enveu.fragments.player.adapter

import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.enveu.OttApplication
import com.enveu.R
import com.enveu.beanModel.selectedSeason.SelectedSeasonModel

class SeasonListAdapter(private val list: ArrayList<SelectedSeasonModel>, private val listner: SeasonClick) : RecyclerView.Adapter<SeasonListAdapter.ViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.all_season_listing, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.season.text = OttApplication.context.resources.getString(R.string.season) + " " + list[position].list.seriesCustomData.season_number
        if (list[position].isSelected) {
            holder.season.setTextColor(
                ContextCompat.getColor(
                    holder.season.context,
                    R.color.selected_indicator_color
                )
            )
            val boldTypeface = Typeface.defaultFromStyle(Typeface.BOLD)
            holder.season.typeface = boldTypeface
        } else {
            holder.season.setTextColor(
                ContextCompat.getColor(
                    holder.season.context,
                    R.color.series_detail_description_text_color
                )
            )
            val boldTypeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            holder.season.typeface = boldTypeface
        }
        holder.season.setOnClickListener {
            listner.onSeasonClick(list,position)
        }
    }

    override fun getItemCount(): Int {
        return list.size
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var season: TextView

        init {
            season = itemView.findViewById(R.id.season_name)
        }
    }


     interface SeasonClick {
         fun onSeasonClick(list: ArrayList<SelectedSeasonModel>, position: Int)
     }
}
