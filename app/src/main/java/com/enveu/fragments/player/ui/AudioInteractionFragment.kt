package com.enveu.fragments.player.ui

import android.content.Intent
import android.content.res.ColorStateList
import android.os.Bundle
import android.util.Log
import android.view.GestureDetector
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import androidx.core.widget.ImageViewCompat
import androidx.lifecycle.ViewModelProvider
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.R
import com.enveu.activities.biography.BiographyActivity
import com.enveu.activities.multiplePlaylist.MultiplePlaylistViewModel
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaConfig
import com.enveu.appLevelModel.MediaInfo
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.emptyResponse.ResponseEmpty
import com.enveu.beanModel.responseGetWatchlist.ResponseGetIsWatchlist
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.callbacks.commonCallbacks.WatchListUpdateCallback
import com.enveu.callbacks.player.callback.OnAudioItemClickInteraction
import com.enveu.client.utils.ClickHandler.disallowClick
import com.enveu.databinding.AudioInteractionFragmentBinding
import com.enveu.enums.DownloadStatus
import com.enveu.fragments.dialog.AlertDialogFragment
import com.enveu.fragments.dialog.AlertDialogSingleButtonFragment
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.ActivityTrackers
import com.enveu.utils.helpers.BottomDialogFragment
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.ToastHandler
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import java.util.Objects

class AudioInteractionFragment : BaseBindingFragment<AudioInteractionFragmentBinding?>(),
    AlertDialogFragment.AlertDialogListener, View.OnClickListener, WatchListUpdateCallback,
    CommonDialogFragment.EditDialogListener {
    private var assetId = 0
    private var preference: KsPreferenceKeys? = null
    private var token: String? = null
    private var watchListCounter = 0
    private var likeCounter = 0
    private var seriesDetailBean: EnveuVideoItemBean? = null
    private var artistFollow: Boolean = false
    private var trailerRefId: String? = null
    private var bookmarkingViewModel: BookmarkingViewModel? = null
    private var playlistViewModel: MultiplePlaylistViewModel? = null
    private var isLoggedOut = false
    private var isLogin: String? = null
    private var contentSlug: String? = null
    private var mediaType: String? = null
    private var from: String? = null
    private var mediaConfig: MediaConfig? = null
    private var featureList: FeatureFlagModel? = null
    private var onAudioItemClickInteraction: OnAudioItemClickInteraction? = null
    private var redirectFrom: String = ""
    private var playQueueItems: Boolean = false
    private var songListData: List<DataItem>? = null
    private var thumbnailImageUrl: String? = null
    private var bottomSheetDialog: BottomDialogFragment? = null
    private var songDetail: DataItem? = null
    private var watchListClick: WatchListUpdateCallback? = null
    private var biographyTitle:String?=null
    private var biographyDiscription:String?=null


    private val stringsHelper by lazy { StringsHelper }
    override fun inflateBindingLayout(inflater: LayoutInflater): AudioInteractionFragmentBinding {
        return AudioInteractionFragmentBinding.inflate(inflater)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getAssetId()
        binding!!.stringData = StringsHelper
        binding!!.colorsData = ColorsHelper
        bookmarkingViewModel = ViewModelProvider(this)[BookmarkingViewModel::class.java]
        playlistViewModel = ViewModelProvider(this)[MultiplePlaylistViewModel::class.java]
        getAppLevelJsonData()
        try {
            isLoggedOut = false
            if (activity is SeriesDetailActivity) {
                binding!!.watchList.visibility = View.GONE
            }
            isLogin = preference!!.appPrefLoginStatus
        } catch (e: Exception) {
            Logger.w(e)
        }
        setClickListeners()
        if (redirectFrom.equals(AppConstants.ARTISTS)) {
            binding?.watchList?.visibility = View.GONE
        }
    }


    private fun getAppLevelJsonData() {
        try {
            featureList = AppConfigMethod.parseFeatureFlagList()
            val mediaConfigInfo: MediaInfo? = AppConfigMethod.getMediaMappingByType(mediaType!!)
            mediaConfig = AppConfigMethod.getMediaConfigByType(mediaConfigInfo?.pageType.toString())
            callApi()
        } catch (e: Exception) {

        }
    }

    private fun callApi() {
        try {
            if (from != "playlist" && featureList!!.featureFlag.ADD_TO_WATCHLIST && mediaConfig!!.detailPage.features.isWatchlistEnabled) {
                binding!!.watchList.visibility = View.VISIBLE
                hitApiIsWatchList()
            } else {
                binding!!.watchList.visibility = View.GONE
            }
            if (from != "playlist" && featureList!!.featureFlag.LIKE && mediaConfig!!.detailPage.features.isLikeEnabled) {
                binding!!.llLike.visibility = View.GONE
                hitApiIsLike()
            } else {
                binding!!.llLike.visibility = View.GONE
            }

            if (from != "playlist" && featureList!!.featureFlag.SHARING && mediaConfig!!.detailPage.features.isShareEnabled) {
                binding!!.shareWith.visibility = View.GONE
            } else {
                binding!!.shareWith.visibility = View.GONE
            }
            if (from != "playlist") {
                binding!!.llPlay.visibility = View.VISIBLE
            } else {
                binding!!.llPlay.visibility = View.GONE
            }
            if (from != "playlist" && featureList?.featureFlag?.SHUFFLE_ENABLE == true) {
                binding!!.shufflell.visibility = View.VISIBLE
            } else {
                binding!!.shufflell.visibility = View.GONE
            }
            if (from != "playlist" && featureList?.featureFlag?.IS_FOLLOW_ALLOWED == true && mediaConfig?.detailPage?.features?.isFollowAllowed == true) {
                binding?.llFollow?.visibility = View.VISIBLE
                fetchFollowArtists(assetId)
            } else {
                binding?.llFollow?.visibility = View.GONE
            }
            if(from!= "playlist" && featureList?.featureFlag?.IS_BIOGRAPHY_ALLOWED == true && mediaConfig?.detailPage?.features?.isBiographyEnabled == true){
                binding?.llBio?.visibility=View.VISIBLE
            }
            else{
                binding?.llBio?.visibility=View.GONE
            }



        } catch (e: Exception) {

        }
    }

    private fun hitApiIsLike() {
        playlistViewModel?.checkContentIsPresentInLike(preference?.appPrefAccessToken, assetId)?.observe(requireActivity()){
            if (it != null && it.responseCode == 2000) {
                if (it.data?.get(0)?.present == true){
                    likeCounter = 1
                    setLike()
                }else{
                 resetLike()
                }
            }
        }
    }

    private fun followClick() {
        binding?.llFollow?.setOnClickListener {
            if (isLogin?.equals(AppConstants.UserStatus.Login.toString()) == true) {
                binding?.clFollow?.visibility = View.GONE
                binding?.followProgressBar?.visibility = View.VISIBLE
                if (artistFollow) {
                    removeArtist(assetId)
                } else {
                    followArtist(assetId)
                }
            } else {
                goToLogin()
            }
        }
    }

    private fun biographyClick(){
        binding?.llBio?.setOnClickListener {
          goToBiography()
        }
    }



    private fun showBottomSheetDialog() {
        songDetail?.let {
            bottomSheetDialog = BottomDialogFragment.getInstance(
                it, redirectFrom, thumbnailImageUrl, songListData, contentSlug,mediaType,this
            )
            bottomSheetDialog?.show(
                activity?.supportFragmentManager!!, "Bottom Sheet Dialog Fragment"
            )
        }
    }

    private fun setClickListeners() {
        followClick()
        biographyClick()
        binding!!.shareWith.setOnClickListener(this)
        binding!!.play.setOnClickListener {
            if (onAudioItemClickInteraction != null) onAudioItemClickInteraction!!.onPlayClick(
                playQueueItems
            )
        }

        binding?.shufflell?.setOnClickListener {
            KsPreferenceKeys.getInstance().setShuffleEnable(true)
            it?.let {v->
                AppCommonMethod.animateScaleUp(v)
            }
            if (onAudioItemClickInteraction != null) onAudioItemClickInteraction!!.onShuffle()
        }
        binding!!.downloadStatus = DownloadStatus.START
        binding!!.llMore.setOnClickListener {
            showBottomSheetDialog()
        }
    }

    private fun getAssetId() {
        val bundle = arguments
        if (bundle != null) {
            likeCounter = 0
            biographyTitle=bundle.getString(AppConstants.BUNDLE_BIO_TITLE)
            biographyDiscription=bundle.getString(AppConstants.BIO_DISCRIPTION)
            redirectFrom = bundle.getString(AppConstants.AUDIO_INTERACTION_REDIRECTION).toString()
            assetId = bundle.getInt(AppConstants.BUNDLE_ASSET_ID, 0)
            mediaType = bundle.getString(AppConstants.BUNDLE_MEDIA_TYPE)
            playQueueItems = bundle.getBoolean(AppConstants.SHOULD_PLAY_QUEUE_ITEM)
            Log.d("checkPlayQueueItems", playQueueItems.toString())
            from = bundle.getString("from")
            if (from != "playlist") {
                seriesDetailBean =
                    bundle.getSerializable(AppConstants.BUNDLE_SERIES_DETAIL) as EnveuVideoItemBean?
                trailerRefId = bundle.getString(AppConstants.BUNDLE_TRAILER_REF_ID)
                contentSlug = bundle.getString(AppConstants.BUNDLE_CONTENT_SLUG) ?: ""
            }
            thumbnailImageUrl = bundle.getString(AppConstants.THUMBNAIL_IMG)
            if (redirectFrom.equals(AppConstants.VIDEO, false)) {
                binding?.llRightSideSection?.visibility = View.GONE
                binding?.rlImageContainer?.visibility =  View.GONE
            }
            if (redirectFrom.equals(AppConstants.ARTISTS)) {
                binding?.watchList?.visibility = View.GONE
            }
        }

        ImageHelper.getInstance(binding?.thumbImg?.context)
            .loadListSQRImageForAlbumList(binding?.thumbImg, thumbnailImageUrl)


        preference = KsPreferenceKeys.getInstance()
        if (preference?.appPrefLoginStatus.equals(
                AppConstants.UserStatus.Login.toString(), ignoreCase = true
            )
        ) {
            token = preference?.appPrefAccessToken
        } else {
            resetLike()
            resetWatchList()
        }
        uiInitialisation()
    }

    fun uiInitialisation() {
        likeClick()
        watchListClick()
        binding!!.shareWith.setOnTouchListener { _: View?, motionEvent: MotionEvent? ->
            gestureDetector.onTouchEvent(
                motionEvent!!
            )
        }
    }

    private var gestureDetector = GestureDetector(activity, object : SimpleOnGestureListener() {

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            openShareDialogue()
            return true
        }

        override fun onSingleTapUp(e: MotionEvent): Boolean {
            return true
        }
    })

    private fun goToLogin() {
        ActivityLauncher.getInstance().loginActivity(activity, ActivityLogin::class.java, "")
    }
    private fun goToBiography(){
         val bundle=Bundle()
        bundle.putString(AppConstants.BUNDLE_BIO_TITLE,biographyTitle)
        bundle.putString(AppConstants.BIO_DISCRIPTION,biographyDiscription)
        ActivityLauncher.getInstance().biographyActivity(activity,BiographyActivity::class.java,bundle)

    }

    private fun watchListClick() {
        binding!!.watchList.setOnClickListener {
            if (binding!!.wProgressBar.visibility != View.VISIBLE) {
                val isLogin = preference!!.appPrefLoginStatus
                if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
                    setWatchListForAsset(1)
                } else {
                    ActivityTrackers.getInstance().setAction(
                        ActivityTrackers.WATCHLIST
                    )
                    goToLogin()
                }
            }
        }
    }

    private fun setWatchListForAsset(from: Int) {
        binding!!.wProgressBar.visibility = View.VISIBLE
        binding!!.addIcon.visibility = View.GONE
        if (watchListCounter == 0) {
            hitApiAddWatchList(from)
        } else {
            hitApiRemoveList()
        }
    }

    private fun hitApiRemoveList() {
        bookmarkingViewModel!!.hitRemoveWatchlist(token, assetId)
            .observe(viewLifecycleOwner) { responseEmpty: ResponseEmpty ->
                binding!!.wProgressBar.visibility = View.GONE
                binding!!.addIcon.visibility = View.VISIBLE
                if (Objects.requireNonNull(responseEmpty).isStatus) {
                    AnalyticsUtils.logUserInteractionEvent(
                        context,
                        AppConstants.REMOVE_WATCHLIST,
                        assetId.toString(),
                        seriesDetailBean?.title,
                        seriesDetailBean?.contentType
                    )
                    resetWatchList()
                } else {
                    if (responseEmpty.responseCode == 4302) {
                        isLoggedOut = true
                        logoutCall()
                    } else if (responseEmpty.responseCode == 500) {
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
                }
            }
    }

    private fun likeClick() {
        binding!!.llLike.setOnClickListener { setLikeForAsset() }
    }


    private fun setLikeForAsset() {
        if (disallowClick()) {
            return
        }
        if (preference!!.appPrefLoginStatus.equals(
                AppConstants.UserStatus.Login.toString(), ignoreCase = true
            )
        ) {
            if (likeCounter == 0) {
                setLike()
            } else resetLike()
        } else {
            ActivityTrackers.getInstance().setAction(ActivityTrackers.LIKE)
            goToLogin()
        }
    }

    private fun followArtist(artistID: Int) {
        bookmarkingViewModel?.followArtist(token, artistID)?.observe(viewLifecycleOwner) {
            if (it.responseCode == 2001) {
                artistFollow = true
                binding?.tvFollow?.text = getString(R.string.following)
                binding?.clFollow?.visibility = View.VISIBLE
                binding?.followProgressBar?.visibility = View.GONE
            } else {
                binding?.clFollow?.visibility = View.VISIBLE
                binding?.followProgressBar?.visibility = View.GONE
            }
        }
    }

    private fun removeArtist(artistID: Int) {
        bookmarkingViewModel?.removeArtist(token, artistID)?.observe(viewLifecycleOwner) {
            binding?.followProgressBar?.visibility = View.GONE
            binding?.clFollow?.visibility = View.VISIBLE
            if (it.responseCode == 2000) {
                artistFollow = false
                binding?.tvFollow?.text = getString(R.string.follow)
            } else {
                artistFollow = true
            }
        }
    }

    private fun fetchFollowArtists(artistID: Int) {
        bookmarkingViewModel?.fetchFollowArtist(token, artistID)?.observe(viewLifecycleOwner) {
            binding?.followProgressBar?.visibility = View.GONE
            if (it != null && it.responseCode == 2000) {
                artistFollow = true
                binding?.tvFollow?.text = getString(R.string.following)
            } else {
                artistFollow = false
            }
        }
    }

    private fun hitApiAddToPlayList(assetId: Int) {
        playlistViewModel!!.addContentPlaylist(
            KsPreferenceKeys.getInstance().appPrefAccessToken, assetId, "LIKED", null, false
        )
    }

    private fun hitApiIsWatchList() {
        if (preference!!.appPrefLoginStatus.equals(
                AppConstants.UserStatus.Login.toString(), ignoreCase = true
            )
        ) {
            bookmarkingViewModel!!.hitApiIsWatchList(token, assetId)
                .observe(viewLifecycleOwner) { responseEmpty: ResponseGetIsWatchlist ->
                    if (Objects.requireNonNull(responseEmpty).isStatus) {
                        setWatchList()
                    } else {
                        if (responseEmpty.responseCode == 4302) {
                            isLoggedOut = true
                            logoutCall()
                        } else if (responseEmpty.responseCode == 500) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.popup_something_went_wrong)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        }
                    }
                }
        }
    }

    private fun hitApiAddWatchList(from: Int) {
        if (activity is SeriesDetailActivity) {
            (activity as SeriesDetailActivity?)!!.seriesLoader()
        }
        bookmarkingViewModel!!.hitApiAddWatchList(token, assetId)
            .observe(viewLifecycleOwner) { responseEmpty: ResponseEmpty ->
                binding!!.wProgressBar.visibility = View.GONE
                binding!!.addIcon.visibility = View.VISIBLE
                if (Objects.requireNonNull(responseEmpty).isStatus) {
                    AnalyticsUtils.logUserInteractionEvent(
                        context,
                        AppConstants.ADD_TO_WATCHLIST,
                        assetId.toString(),
                        seriesDetailBean?.title,
                        seriesDetailBean?.contentType
                    )
                    setWatchList()
                } else {
                    if (responseEmpty.responseCode == 4302) {
                        isLoggedOut = true
                        logoutCall()
                    } else if (responseEmpty.responseCode == 4904) {
                        setWatchList()
                        val debugMessage = responseEmpty.debugMessage
                        //from value will bedepends on how user click of watchlist icon-->>if loggedout=2 else=2
                        if (from == 1) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.popup_something_went_wrong)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        }
                    } else if (responseEmpty.responseCode == 500) {
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    } else {
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
                }
            }
    }

    private fun openShareDialogue() {
        val id = seriesDetailBean!!.id
        val title = seriesDetailBean!!.title
        var assetType: String? =
            if (seriesDetailBean?.assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
                seriesDetailBean?.videoDetails?.videoType
            } else if (seriesDetailBean?.assetType.equals(AppConstants.CUSTOM, ignoreCase = true)) {
                seriesDetailBean?.customType
            } else if (seriesDetailBean?.assetType.equals(
                    AppConstants.AUDIO, ignoreCase = true
                ) || seriesDetailBean?.assetType.equals(AppConstants.PERSON, ignoreCase = true)
            ) {
                seriesDetailBean?.contentType
            } else {
                "MOVIE"
            }
        var imgUrl = seriesDetailBean!!.imageContent?.src
        if (disallowClick()) {
            return
        }
        imgUrl = AppCommonMethod.getListSQRImage(imgUrl!!, requireContext())
        AppCommonMethod.openShareFirebaseDynamicLinks(
            requireActivity(),
            seriesDetailBean!!
        )
    }

    private fun setLike() {
        binding!!.likeIcon.setImageResource(R.drawable.favorite_ic_solid)
        ImageViewCompat.setImageTintList(
            binding!!.likeIcon, ColorStateList.valueOf(resources.getColor(R.color.white))
        )
        if (likeCounter == 0) {
            likeCounter = 1
            hitApiAddToPlayList(assetId)
        }
    }

    private fun resetLike() {
        binding!!.likeIcon.setImageResource(R.drawable.ic_baseline_favorite_border_24)
        binding?.likeIcon?.let { ImageViewCompat.setImageTintList(it, ColorStateList.valueOf(resources.getColor(R.color.white))) }
        if (likeCounter == 1) {
            likeCounter = 0
            hitApiRemoveLike(assetId)
        }
    }

    private fun hitApiRemoveLike(assetId: Int) {
        playlistViewModel?.removeContentPlaylist(
            preference?.appPrefAccessToken!!,
           assetId,
            "LIKED",
            "",
            true
        )
    }

    private fun setWatchList() {
        binding!!.wProgressBar.visibility = View.GONE
        binding!!.addIcon.visibility = View.VISIBLE
        watchListCounter = 1
        binding!!.addIcon.setImageResource(R.drawable.added_to_watch_list)
    }

    private fun resetWatchList() {
        binding!!.wProgressBar.visibility = View.GONE
        binding!!.addIcon.visibility = View.VISIBLE
        watchListCounter = 0
        binding!!.addIcon.setImageResource(R.drawable.add_watch_list)

    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = activity?.supportFragmentManager!!
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }
    private fun showDialog(title: String, message: String) {
        val fm = requireActivity().supportFragmentManager
        val alertDialog = AlertDialogSingleButtonFragment.newInstance(
            title, message, stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                getString(R.string.popup_ok)
            )
        )
        alertDialog.isCancelable = false
        alertDialog.setAlertDialogCallBack(this)
        alertDialog.show(fm, "fragment_alert")
    }

    override fun onFinishDialog() {
        if (isLoggedOut) {
            logoutCall()
        }
    }

    private fun logoutCall() {
        if (CheckInternetConnection.isOnline(requireActivity())) {
            clearCredientials(preference)
            hitApiLogout(baseActivity, preference!!.appPrefAccessToken)
        } else {
            ToastHandler.getInstance().show(activity, getString(R.string.no_internet_connection))
        }
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.shareWith -> {
                openShareDialogue()
            }

            R.id.llLike -> {
                likeClick()
            }

            R.id.watchList -> {
                watchListClick()
            }
        }
    }


    fun getAllSongsList(songsListDataItem: List<DataItem>?) {
        this.songListData = songsListDataItem
        Log.d("songListDataItems", Gson().toJson(songListData))
    }

    fun passInstance(listener: OnAudioItemClickInteraction) {
        onAudioItemClickInteraction = listener
    }

    fun setAttachedFragmentDetails(songDetail: DataItem) {
        this.songDetail = songDetail
    }

    override fun onClick(isWatchListUpdated: Boolean) {
        if (isWatchListUpdated) {
            setWatchList()
        } else {
            resetWatchList()
        }
    }

    override fun onActionBtnClicked() {
    }

    override fun onCancelBtnClicked() {
    }

}