package com.enveu.fragments.player.ui


import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.SimpleItemAnimator
import com.enveu.activities.detail.ui.EpisodeActivity
import com.enveu.activities.detail.ui.MatchDetailActivity
import com.enveu.activities.series.adapter.RelatedContentAdapter
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.databinding.RelatedContentFragmentLayoutBinding
import com.enveu.networking.apistatus.APIStatus
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.SpacingItemDecoration
import com.enveu.utils.stringsJson.converter.StringsHelper


class CommonListAllFragment : BaseBindingFragment<RelatedContentFragmentLayoutBinding?>(), RelatedContentAdapter.EpisodeItemClick {
    private var railInjectionHelper: RailInjectionHelper? = null
    private var context: Context? = null
    private var seasonAdapter: RelatedContentAdapter? = null
    private var allEpisodes: MutableList<EnveuVideoItemBean> = ArrayList()
    private var id = 0
    private var tittle = ""
    private var customData = ""
    var playListId: String? = null
    private var railCommonData: RailCommonData? = null
    private val stringsHelper by lazy { StringsHelper }
    override fun onAttach(context: Context) {
        super.onAttach(context)
        this.context = context
    }

    override fun onDetach() {
        super.onDetach()
        context = null
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): RelatedContentFragmentLayoutBinding {
        return RelatedContentFragmentLayoutBinding.inflate(inflater)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding!!.stringData = stringsHelper
        binding!!.colorsData = ColorsHelper
        val bundle = arguments
        if (bundle != null) {
            tittle = bundle.getString(AppConstants.TITLE)!!
            id = bundle.getInt(AppConstants.ID)
            customData = bundle.getString(AppConstants.CUSTOM_DATA)!!
        }
        callCommonListAllApi
    }

    private fun hideProgressBar() {
        if (context is  SeriesDetailActivity) {
            (context as SeriesDetailActivity).isSeasonData = true
            (context as SeriesDetailActivity).stopShimmer()
            (context as SeriesDetailActivity).dismissLoading((context as SeriesDetailActivity).binding!!.progressBar)
        }  else if (context is  MatchDetailActivity) {
            (context as MatchDetailActivity).isSeasonData = true
            (context as MatchDetailActivity).stopShimmer()
            (context as MatchDetailActivity).dismissLoading((context as MatchDetailActivity).binding!!.progressBar)
        } else if (context is EpisodeActivity) {
            (context as EpisodeActivity).dismissLoading((context as EpisodeActivity).binding!!.progressBar)
            (context as EpisodeActivity).isSeasonData = true
            (context as EpisodeActivity).stopShimmercheck()
        }
    }

    private fun removeTab() {
        if (context is SeriesDetailActivity) {
            (context as SeriesDetailActivity).removeTab(3)
        } else if (context is EpisodeActivity) {
            (context as EpisodeActivity).removeTab(3)
        }
    }


    private val callCommonListAllApi: Unit
        get() {
            binding!!.seriesRecyclerView.addItemDecoration(
                SpacingItemDecoration(
                    5,
                    SpacingItemDecoration.HORIZONTAL
                )
            )
            if (activity != null) {
                railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
            }
            getCommonListAll
        }

    private val getCommonListAll: Unit
        get() {
            railInjectionHelper!!.getCommonListAll(0, 20, id.toString(),customData).observe(
                requireActivity()
            ) { response ->
                hideProgressBar()
                if (response != null) {
                    if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {

                    } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                        if (response.baseCategory != null) {
                            val enveuCommonResponse = response.baseCategory as RailCommonData
                            parseSeriesData(enveuCommonResponse)
                            binding!!.progressBar.visibility = View.GONE
                            if (seasonAdapter == null) {
                                allEpisodes = enveuCommonResponse.enveuVideoItemBeans
                                RecyclerAnimator(
                                    activity
                                ).animate(binding!!.seriesRecyclerView)
                                seasonAdapter = RelatedContentAdapter(
                                    requireActivity(),
                                    allEpisodes,
                                    this@CommonListAllFragment
                                )
                                binding!!.seriesRecyclerView.layoutManager =
                                    GridLayoutManager(getContext(), 2)
                                (binding!!.seriesRecyclerView.itemAnimator as SimpleItemAnimator?)!!.supportsChangeAnimations =
                                    false
                                binding!!.seriesRecyclerView.adapter = seasonAdapter
                            }
                            if (allEpisodes[0].videoDetails.videoType.equals(AppConstants.REPLAY)) {
                                    if (context is MatchDetailActivity) {
                                        (context as MatchDetailActivity).episodesList(allEpisodes)
                                    }
                                }
                            hideProgressBar()
                        } else {
                            removeTab()
                        }
                    } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                        if (response.errorModel.errorCode != 0) {
                            binding!!.progressBar.visibility = View.GONE
                            binding!!.seriesRecyclerView.visibility = View.GONE
                        }
                        removeTab()
                    } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                        binding!!.progressBar.visibility = View.GONE
                        binding!!.seriesRecyclerView.visibility = View.GONE
                        removeTab()
                    }
                }
            }
        }
    var totalPages = 0
    private fun parseSeriesData(railCommonData: RailCommonData) {
        this.railCommonData = railCommonData
    }

    override fun onItemClick(enveuVideoItemBean: EnveuVideoItemBean?, isPremium: Boolean, position: Int) {
        AppCommonMethod.redirectionLogic(
            requireActivity(),
            railCommonData!!,
            position,"","",""
        )
    }


}