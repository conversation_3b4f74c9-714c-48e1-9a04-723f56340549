package com.enveu.fragments.player.ui


import android.content.Context
import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.enveu.R
import com.enveu.activities.detail.ui.EpisodeActivity
import com.enveu.activities.detail.ui.MatchDetailActivity
import com.enveu.activities.series.adapter.SeasonAdapter
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModel.selectedSeason.SelectedSeasonModel
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.FirstEpisodeItem
import com.enveu.databinding.SeasonFragmentLayoutBinding
import com.enveu.networking.apistatus.APIStatus
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.SpacingItemDecoration
import com.enveu.utils.stringsJson.converter.StringsHelper


class SeasonTabFragment : BaseBindingFragment<SeasonFragmentLayoutBinding?>(), SeasonAdapter.EpisodeItemClick,
    FirstEpisodeItem {
    private var railInjectionHelper: RailInjectionHelper? = null
    private var seriesId = 0
    private var assetId = 0
    var selectedSeason = 1
    private var seasonPosition = 0
    private var context: Context? = null
    private var seasonList: ArrayList<SelectedSeasonModel>? = null
    private var currentAssetId = 0
    var seasonAdapter: SeasonAdapter? = null
    private var seasonEpisodes: MutableList<EnveuVideoItemBean> = ArrayList()
    private var allEpiosdes: MutableList<EnveuVideoItemBean> = ArrayList()
    private var mLastClickTime: Long = 0
    private var railCommonData: RailCommonData? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        this.context = context
    }

    override fun onDetach() {
        super.onDetach()
        context = null
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): SeasonFragmentLayoutBinding {
        return SeasonFragmentLayoutBinding.inflate(inflater)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding!!.stringData = StringsHelper
        binding!!.colorsData = ColorsHelper
        val bundle = arguments
        getVideoRails(bundle)
    }

    private fun getVideoRails(bundle: Bundle?) {
        if (bundle != null) {
            binding!!.seasonHeader.visibility = View.GONE
            seriesId = bundle.getInt(AppConstants.BUNDLE_SERIES_ID)
          //  assetId = bundle.getInt(AppConstants.BUNDLE_ASSET_ID)
            seasonPosition = bundle.getInt(AppConstants.BUNDLE_SEASON_NUMBER)
            Log.d("seriesId", "getVideoRails: $seriesId")
            railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
            getSeasonEpisodes()

        }


        binding!!.seasonMore.setOnClickListener { v: View? ->
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1500) {
                return@setOnClickListener
            }
            binding!!.seasonMore.visibility = View.GONE
            binding!!.progressBar.visibility = View.VISIBLE
            mLastClickTime = SystemClock.elapsedRealtime()
            totalPages++
        }


        binding!!.seasonHeader.setOnClickListener { v: View? ->
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                return@setOnClickListener
            }
            mLastClickTime = SystemClock.elapsedRealtime()

            if (seasonList == null) {
                seasonList = ArrayList()
            }

            seasonList!!.clear()

            for (i in seasonEpisodes.indices) {
                if (seasonEpisodes[i].seriesCustomData.season_number!="") {
                    val isSelected = selectedSeason == Integer.valueOf(seasonEpisodes[i].seriesCustomData.season_number)
                    seasonList!!.add(
                        SelectedSeasonModel(
                            seasonEpisodes[i],
                            seasonEpisodes[i].id,
                            isSelected
                        )
                    )
                }
            }
            if (seasonList!!.size > 1) {
                if (context is SeriesDetailActivity) {
                    (context as SeriesDetailActivity).showSeasonList(seasonList!!)
                } else if (context is EpisodeActivity) {
                    (context as EpisodeActivity).showSeasonList(seasonList!!)
                }
            }
        }
    }


    private fun hideProgressBar() {
        if (context is  SeriesDetailActivity) {
            (context as SeriesDetailActivity).isSeasonData = true
            (context as SeriesDetailActivity).stopShimmer()
            (context as SeriesDetailActivity).dismissLoading((context as SeriesDetailActivity).binding!!.progressBar)
        }  else if (context is MatchDetailActivity) {
            (context as MatchDetailActivity).isSeasonData = true
            (context as MatchDetailActivity).stopShimmer()
            (context as MatchDetailActivity).dismissLoading((context as MatchDetailActivity).binding!!.progressBar)
        } else if (context is EpisodeActivity) {
            (context as EpisodeActivity).dismissLoading((context as EpisodeActivity).binding!!.progressBar)
            (context as EpisodeActivity).isSeasonData = true
            (context as EpisodeActivity).stopShimmercheck()
        }
    }


    // all episode view to set here
    fun episodesListApi(seasonSeriesId: Int, selectedSeasonNumber: Int) {
        selectedSeason = selectedSeasonNumber
        seasonPosition = if (selectedSeasonNumber == 1) {
            0
        } else {
            selectedSeasonNumber - 1
        }
        if (allEpiosdes!=null) {
            allEpiosdes.clear()
        }
        binding!!.seriesRecyclerView.addItemDecoration(
            SpacingItemDecoration(
                8,
                SpacingItemDecoration.VERTICAL
            )
        )
        (binding!!.seriesRecyclerView.itemAnimator as SimpleItemAnimator?)!!.supportsChangeAnimations = true
        railInjectionHelper!!.getEpisodeFromSeason(seasonSeriesId, totalPages, 50).observe(requireActivity()) { response ->
            hideProgressBar()
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {
                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    if (response.baseCategory != null) {
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                        railCommonData = enveuCommonResponse
                        binding!!.comingSoon.visibility = View.GONE
                        binding!!.progressBar.visibility = View.GONE
                        // all episode view to set here
                        if (enveuCommonResponse.pageTotal - 1 > totalPages) {
                            binding!!.seasonMore.visibility = View.VISIBLE
                        } else {
                            binding!!.seasonMore.visibility = View.GONE
                        }
                        binding!!.seasonHeader.text =  resources.getString(R.string.season) + " " + selectedSeason

                        if (seasonAdapter == null) {
                            allEpiosdes = enveuCommonResponse.enveuVideoItemBeans
                            RecyclerAnimator(
                                activity
                            ).animate(binding!!.seriesRecyclerView)
                            seasonAdapter = SeasonAdapter(
                                requireActivity(),
                                allEpiosdes,
                                seriesId,
                                currentAssetId,
                                this@SeasonTabFragment,
                                this@SeasonTabFragment
                            )
                            binding!!.seriesRecyclerView.layoutManager = LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
                            (binding!!.seriesRecyclerView.itemAnimator as SimpleItemAnimator?)!!.supportsChangeAnimations = true
                            binding!!.seriesRecyclerView.adapter = seasonAdapter
                        } else {
                            allEpiosdes.addAll(enveuCommonResponse.enveuVideoItemBeans)
                            seasonAdapter!!.notifyDataSetChanged()
                        }
                        if (context is EpisodeActivity) {
                            (context as EpisodeActivity).episodesList(allEpiosdes)
                        } else if (context is SeriesDetailActivity) {
                            (context as SeriesDetailActivity).episodesList(allEpiosdes)
                        }
                        hideProgressBar()
                    }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        if (context is SeriesDetailActivity) {
                            (context as SeriesDetailActivity).episodesList(null)
                        }
                        binding!!.seasonHeader.visibility = View.GONE
                        binding!!.headerSeason.visibility = View.GONE
                        binding!!.progressBar.visibility = View.GONE
                        binding!!.comingSoon.visibility = View.VISIBLE
                        binding!!.seriesRecyclerView.visibility = View.GONE
                        binding!!.seasonMore.visibility = View.GONE
                    }
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    binding!!.seasonHeader.visibility = View.GONE
                    binding!!.headerSeason.visibility = View.GONE
                    binding!!.comingSoon.visibility = View.VISIBLE
                    binding!!.progressBar.visibility = View.GONE
                    binding!!.seriesRecyclerView.visibility = View.GONE
                    binding!!.seasonMore.visibility = View.GONE
                }
            }

        }
    }




    private fun getSeasonEpisodes() {
        binding!!.headerSeason.visibility = View.GONE
        railInjectionHelper!!.getSeasonEpisodesV2(seriesId, totalPages, 50,
            AppConstants.ENT_SEASON_CUSTOM_DATA).observe(requireActivity()) { response ->
            hideProgressBar()
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {
                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    if (response.baseCategory != null) {
                        binding!!.progressBar.visibility = View.GONE
                        binding!!.seriesRecyclerView.visibility = View.VISIBLE
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                        railCommonData = enveuCommonResponse
                        parseSeriesData(enveuCommonResponse)
                    }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        if (context is SeriesDetailActivity) {
                            (context as SeriesDetailActivity).episodesList(null)
                        }
                        binding!!.seasonHeader.visibility = View.GONE
                        binding!!.headerSeason.visibility = View.GONE
                        binding!!.progressBar.visibility = View.GONE
                        binding!!.comingSoon.visibility = View.VISIBLE
                        binding!!.seriesRecyclerView.visibility = View.GONE
                        binding!!.seasonMore.visibility = View.GONE
                        parseSeriesData(null)
                    }
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    binding!!.seasonHeader.visibility = View.GONE
                    binding!!.headerSeason.visibility = View.GONE
                    binding!!.comingSoon.visibility = View.VISIBLE
                    binding!!.progressBar.visibility = View.GONE
                    binding!!.seriesRecyclerView.visibility = View.GONE
                    binding!!.seasonMore.visibility = View.GONE
                }
            } else {
                if (context is SeriesDetailActivity) {
                    (context as SeriesDetailActivity).episodesList(null)
                }
            }
        }
    }



    var totalPages = 0
    private fun parseSeriesData(railCommonData: RailCommonData?) {
        this.railCommonData = railCommonData
        if (railCommonData != null) {
            if (railCommonData.enveuVideoItemBeans.isNotEmpty()) {
                seasonEpisodes = railCommonData.enveuVideoItemBeans
                binding!!.seasonHeader.visibility = View.VISIBLE
                binding!!.headerSeason.visibility = View.VISIBLE
                val season = selectedSeason + seasonPosition
                binding!!.seasonHeader.text =  resources.getString(R.string.season) + " " + season
                if (seasonEpisodes.size > 1) {
                    binding!!.seasonHeader.isEnabled = true
                    binding!!.seasonHeader.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.arrow_down, 0)
                }
                episodesListApi(seasonEpisodes[seasonPosition].id, selectedSeason + seasonPosition)
             }
            } else {
                binding!!.seasonHeader.visibility = View.GONE
                binding!!.headerSeason.visibility = View.GONE
                binding!!.comingSoon.visibility = View.VISIBLE
                binding!!.seriesRecyclerView.visibility = View.GONE
                binding!!.seasonMore.visibility = View.GONE
            }
        hideProgressBar()
    }

    override fun onItemClick(itemValue: EnveuVideoItemBean?, isPremium: Boolean, position: Int) {
        val assetType = itemValue?.assetType
        var mediaType: String? = ""
        if (itemValue?.assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
            mediaType = itemValue?.videoDetails?.videoType
        } else  if (itemValue?.assetType.equals(AppConstants.CUSTOM, ignoreCase = true)) {
            mediaType = itemValue?.customType
        }else if (itemValue?.assetType.equals(AppConstants.AUDIO, ignoreCase = true)||itemValue?.assetType.equals(AppConstants.PERSON, ignoreCase = true)){
            mediaType=itemValue?.contentType
        }

        AppCommonMethod.launchDetailScreen(
            requireContext(),
            assetType!!,
            itemValue.id,
            itemValue.sku,
            mediaType,
            itemValue.title?:"",
            itemValue.externalRefId?:"",
            itemValue.posterURL?:"",
            seasonPosition,
            itemValue.contentSlug?:"",
            itemValue
        )
    }


    fun updateTotalPages() {
        totalPages = 0
    }




    override fun getFirstItem(itemValue: EnveuVideoItemBean) {
        Log.d("getFirstItem", "getFirstItem1: $itemValue")
    }
    override fun episodeClicked(itemBean: EnveuVideoItemBean?) {
        TODO("Not yet implemented")
    }

}