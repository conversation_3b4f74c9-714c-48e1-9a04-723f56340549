package com.enveu.fragments.player.ui


import android.app.AlertDialog
import android.content.Context
import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.ImageView
import androidx.core.app.ActivityCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.enveu.R
import com.enveu.activities.detail.ui.EpisodeActivity
import com.enveu.activities.series.adapter.SeasonAdapter
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModel.selectedSeason.SelectedRoundModel
import com.enveu.beanModel.selectedSeason.SelectedSeasonModel
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.commonCallbacks.FirstEpisodeItem
import com.enveu.databinding.TournamentSeasonFragmentLayoutBinding
import com.enveu.fragments.detailPageTabFragment.EnveuTabFragment
import com.enveu.fragments.player.adapter.RoundListAdapter
import com.enveu.fragments.player.adapter.SeasonListAdapter
import com.enveu.networking.apistatus.APIStatus
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.SpacingItemDecoration
import com.enveu.utils.stringsJson.converter.StringsHelper


class TournamentSeasonTabFragment : BaseBindingFragment<TournamentSeasonFragmentLayoutBinding?>(), SeasonAdapter.EpisodeItemClick,
    FirstEpisodeItem,
    SeasonListAdapter.SeasonClick, RoundListAdapter.RoundClick {
    private var railInjectionHelper: RailInjectionHelper? = null
    private var seriesId = 0
    private var assetId = 0
    private var selectedSeason = 1
    private var selectedRound = 1
    private var seasonPosition = 0
    private var roundPosition = 0
    private var context: Context? = null
    private var seasonList: ArrayList<SelectedSeasonModel>? = null
    private var roundList: ArrayList<SelectedRoundModel>? = null
    private var currentAssetId = 0
    private var seasonAdapter: SeasonAdapter? = null
    private var seasonEpisodes: MutableList<EnveuVideoItemBean> = ArrayList()
    private var roundEpisodes: MutableList<EnveuVideoItemBean> = ArrayList()
    private var allEpiosdes: MutableList<EnveuVideoItemBean> = ArrayList()
    private var mLastClickTime: Long = 0
    private var railCommonData: RailCommonData? = null
    private var enveuTabFragment : EnveuTabFragment?=null


    override fun onAttach(context: Context) {
        super.onAttach(context)
        this.context = context
    }

    override fun onDetach() {
        super.onDetach()
        context = null
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): TournamentSeasonFragmentLayoutBinding {
        return TournamentSeasonFragmentLayoutBinding.inflate(inflater)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding!!.stringData = StringsHelper
        binding!!.colorsData = ColorsHelper
        val bundle = arguments
        getVideoRails(bundle)
    }

    private fun getVideoRails(bundle: Bundle?) {
        if (bundle != null) {
            binding!!.seasonHeader.visibility = View.GONE
            seriesId = bundle.getInt(AppConstants.BUNDLE_SERIES_ID)
           // assetId = bundle.getInt(AppConstants.BUNDLE_ASSET_ID)
            seasonPosition = bundle.getInt(AppConstants.BUNDLE_SEASON_NUMBER)
            Log.d("seriesId", "getVideoRails: $seriesId")
            railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
            binding!!.seriesRecyclerView.addItemDecoration(
                SpacingItemDecoration(
                    8,
                    SpacingItemDecoration.VERTICAL
                )
            )
            (binding!!.seriesRecyclerView.itemAnimator as SimpleItemAnimator?)!!.supportsChangeAnimations = true
            getSeasonEpisodes()

        }


        binding!!.seasonMore.setOnClickListener { v: View? ->
           /* if (SystemClock.elapsedRealtime() - mLastClickTime < 1500) {
                return@setOnClickListener
            }
            binding!!.seasonMore.visibility = View.GONE
            binding!!.progressBar.visibility = View.VISIBLE
            mLastClickTime = SystemClock.elapsedRealtime()
            totalPages++*/
        }


        binding!!.seasonHeader.setOnClickListener { v: View? ->
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                return@setOnClickListener
            }
            mLastClickTime = SystemClock.elapsedRealtime()

            if (seasonList == null) {
                seasonList = ArrayList()
            }
            seasonList!!.clear()
            for (i in seasonEpisodes.indices) {
                if (seasonEpisodes[i].seriesCustomData.season_number!="") {
                    val isSelected = selectedSeason == Integer.valueOf(seasonEpisodes[i].seriesCustomData.season_number)
                    seasonList!!.add(
                        SelectedSeasonModel(
                            seasonEpisodes[i],
                            seasonEpisodes[i].id,
                            isSelected
                        )
                    )
                }
            }
            if (seasonList!!.size > 1) {
                showSeasonList(seasonList!!)
            }
        }

        binding!!.roundHeader.setOnClickListener { v: View? ->
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                return@setOnClickListener
            }
            mLastClickTime = SystemClock.elapsedRealtime()

            if (roundList == null) {
                roundList = ArrayList()
            }
            roundList!!.clear()

            for (i in roundEpisodes.indices) {
                if (roundEpisodes[i].seriesCustomData.round_number!="") {
                    val isSelected = selectedRound == Integer.valueOf(roundEpisodes[i].seriesCustomData.round_number)
                    roundList!!.add(
                        SelectedRoundModel(
                            roundEpisodes[i],
                            roundEpisodes[i].id,
                            isSelected
                        )
                    )
                }
            }
            if (roundList!!.size > 1) {
                showRoundList(roundList!!)
            }
        }
    }

    private var alertDialog: AlertDialog? = null

    private fun showSeasonList(list: ArrayList<SelectedSeasonModel>) {
        binding!!.transparentLayout.visibility = View.VISIBLE
        val listAdapter = SeasonListAdapter(list,this)
        val builder = AlertDialog.Builder(requireContext())
        val inflater = LayoutInflater.from(requireContext())
        val content = inflater.inflate(R.layout.season_custom_dialog, null)
        builder.setView(content)
        val mRecyclerView = content.findViewById<RecyclerView>(R.id.my_recycler_view)
        val imageView = content.findViewById<ImageView>(R.id.close)
        imageView.setOnClickListener {
            alertDialog!!.cancel()
            binding!!.transparentLayout.visibility = View.GONE
        }

        mRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        mRecyclerView.adapter = listAdapter
        alertDialog = builder.create()
        alertDialog?.window!!.setBackgroundDrawable(
            ActivityCompat.getDrawable(
                requireContext(),
                R.color.transparent
            )
        )
        alertDialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        if (alertDialog?.window != null) alertDialog?.window!!.attributes.windowAnimations =
            R.style.SlidingDialogAnimation
        alertDialog?.show()
        val lWindowParams = WindowManager.LayoutParams()
        lWindowParams.copyFrom(alertDialog?.window!!.attributes)
        lWindowParams.width = ViewGroup.LayoutParams.MATCH_PARENT // this is where the magic happens
        lWindowParams.height = ViewGroup.LayoutParams.MATCH_PARENT
        alertDialog?.window!!.attributes = lWindowParams
    }


    private fun showRoundList(list: ArrayList<SelectedRoundModel>) {
        binding!!.transparentLayout.visibility = View.VISIBLE
        val listAdapter = RoundListAdapter(list,this)
        val builder = AlertDialog.Builder(requireContext())
        val inflater = LayoutInflater.from(requireContext())
        val content = inflater.inflate(R.layout.season_custom_dialog, null)
        builder.setView(content)
        val mRecyclerView = content.findViewById<RecyclerView>(R.id.my_recycler_view)
        val imageView = content.findViewById<ImageView>(R.id.close)
        imageView.setOnClickListener {
            alertDialog!!.cancel()
            binding!!.transparentLayout.visibility = View.GONE
        }

        mRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        mRecyclerView.adapter = listAdapter
        alertDialog = builder.create()
        alertDialog?.window!!.setBackgroundDrawable(
            ActivityCompat.getDrawable(
                requireContext(),
                R.color.transparent
            )
        )
        alertDialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        if (alertDialog?.window != null) alertDialog?.window!!.attributes.windowAnimations =
            R.style.SlidingDialogAnimation
        alertDialog?.show()
        val lWindowParams = WindowManager.LayoutParams()
        lWindowParams.copyFrom(alertDialog?.window!!.attributes)
        lWindowParams.width = ViewGroup.LayoutParams.MATCH_PARENT // this is where the magic happens
        lWindowParams.height = ViewGroup.LayoutParams.MATCH_PARENT
        alertDialog?.window!!.attributes = lWindowParams
    }



    private fun hideProgressBar() {
        if (context is SeriesDetailActivity) {
            (context as SeriesDetailActivity).isSeasonData = true
            (context as SeriesDetailActivity).stopShimmer()
            (context as SeriesDetailActivity).dismissLoading((context as SeriesDetailActivity).binding!!.progressBar)
        } else if (context is EpisodeActivity) {
            (context as EpisodeActivity).dismissLoading((context as EpisodeActivity).binding!!.progressBar)
            (context as EpisodeActivity).isSeasonData = true
            (context as EpisodeActivity).stopShimmercheck()
        }
    }


    // all episode view to set here
    private fun matchListApi(roundSeriesId: Int, selectedRoundNumber: Int) {
        selectedRound = selectedRoundNumber
        if (allEpiosdes!=null) {
            allEpiosdes.clear()
        }
       /* seasonPosition = if (selectedRoundNumber == 1) {
            0
        } else {
            selectedRoundNumber - 1
        }*/
        binding!!.roundHeader.text =  resources.getString(R.string.round) + " " + selectedRound
        railInjectionHelper!!.getMatchForRound(roundSeriesId, totalPages, 50,
            AppConstants.MATCH_FOR_ROUND_CUSTOM_DATA).observe(requireActivity()) { response ->
            hideProgressBar()
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {
                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    if (response.baseCategory != null) {
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                        railCommonData = enveuCommonResponse
                        binding!!.seriesRecyclerView.visibility = View.VISIBLE
                        binding!!.progressBar.visibility = View.GONE
                        // all episode view to set here
                        if (enveuCommonResponse.pageTotal - 1 > totalPages) {
                            binding!!.seasonMore.visibility = View.VISIBLE
                        } else {
                            binding!!.seasonMore.visibility = View.GONE
                        }

                        if (seasonAdapter == null) {
                            allEpiosdes = enveuCommonResponse.enveuVideoItemBeans
                            RecyclerAnimator(
                                activity
                            ).animate(binding!!.seriesRecyclerView)
                            seasonAdapter = SeasonAdapter(
                                requireActivity(),
                                allEpiosdes,
                                seriesId,
                                currentAssetId,
                                this@TournamentSeasonTabFragment,
                                this@TournamentSeasonTabFragment
                            )
                            binding!!.seriesRecyclerView.layoutManager = LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
                            (binding!!.seriesRecyclerView.itemAnimator as SimpleItemAnimator?)!!.supportsChangeAnimations = true
                            binding!!.seriesRecyclerView.adapter = seasonAdapter
                        } else {
                            allEpiosdes.addAll(enveuCommonResponse.enveuVideoItemBeans)
                            seasonAdapter!!.notifyDataSetChanged()
                        }
                        if (context is EpisodeActivity) {
                            (context as EpisodeActivity).episodesList(allEpiosdes)
                        } else if (context is SeriesDetailActivity) {
                            (context as SeriesDetailActivity).episodesList(allEpiosdes)
                        }
                        hideProgressBar()
                    }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        if (context is SeriesDetailActivity) {
                            (context as SeriesDetailActivity).episodesList(null)
                        }
                        binding!!.progressBar.visibility = View.GONE
                        binding!!.seriesRecyclerView.visibility = View.GONE
                        binding!!.seasonMore.visibility = View.GONE
                    }
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    binding!!.progressBar.visibility = View.GONE
                    binding!!.seriesRecyclerView.visibility = View.GONE
                    binding!!.seasonMore.visibility = View.GONE
                }
            }

        }
    }




    private fun getSeasonEpisodes() {
        binding!!.headerSeason.visibility = View.GONE
        railInjectionHelper!!.getSeasonEpisodesV2(seriesId, totalPages, 50,
            AppConstants.TOURNAMENT_SEASON_CUSTOM_DATA).observe(requireActivity()) { response ->
            hideProgressBar()
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {
                   //Do nothing
                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    if (response.baseCategory != null) {
                        binding!!.progressBar.visibility = View.GONE
                        binding!!.seriesRecyclerView.visibility = View.VISIBLE
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                        railCommonData = enveuCommonResponse
                        parseSeasonData(enveuCommonResponse)
                    }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        if (context is SeriesDetailActivity) {
                            (context as SeriesDetailActivity).episodesList(null)
                        }
                        binding!!.seasonHeader.visibility = View.GONE
                        binding!!.headerSeason.visibility = View.GONE
                        binding!!.progressBar.visibility = View.GONE
                        binding!!.seriesRecyclerView.visibility = View.GONE
                        binding!!.seasonMore.visibility = View.GONE
                        parseSeasonData(null)
                    }
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    binding!!.seasonHeader.visibility = View.GONE
                    binding!!.headerSeason.visibility = View.GONE
                    binding!!.progressBar.visibility = View.GONE
                    binding!!.seriesRecyclerView.visibility = View.GONE
                    binding!!.seasonMore.visibility = View.GONE
                }
            } else {
                if (context is SeriesDetailActivity) {
                    (context as SeriesDetailActivity).episodesList(null)
                }
            }
        }
    }
    private fun getRoundForSeason(seasonSeriesId: Int, selectedRoundNumber: Int) {
        selectedRound = selectedRoundNumber
        railInjectionHelper!!.getRoundForSeason(seasonSeriesId, totalPages, 50,
            AppConstants.ROUND_FOR_SEASON_CUSTOM_DATA).observe(requireActivity()) { response ->
            hideProgressBar()
            if (response != null) {
                if (response.status.equals(APIStatus.START.name, ignoreCase = true)) {
                   //Do nothing
                } else if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                    if (response.baseCategory != null) {
                        binding!!.progressBar.visibility = View.GONE
                        binding!!.seriesRecyclerView.visibility = View.VISIBLE
                        val enveuCommonResponse = response.baseCategory as RailCommonData
                        railCommonData = enveuCommonResponse
                        parseRoundData(enveuCommonResponse)
                    }
                } else if (response.status.equals(APIStatus.ERROR.name, ignoreCase = true)) {
                    if (response.errorModel.errorCode != 0) {
                        if (context is SeriesDetailActivity) {
                            (context as SeriesDetailActivity).episodesList(null)
                        }
                        binding!!.llRoundHeader.visibility = View.GONE
                        binding!!.roundHeader.visibility = View.GONE
                        binding!!.progressBar.visibility = View.GONE
                        parseRoundData(null)
                    }
                } else if (response.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                    binding!!.llRoundHeader.visibility = View.GONE
                    binding!!.roundHeader.visibility = View.GONE
                    binding!!.progressBar.visibility = View.GONE
                }
            } else {
                if (context is SeriesDetailActivity) {
                    (context as SeriesDetailActivity).episodesList(null)
                }
            }
        }
    }



    var totalPages = 0
    private fun parseSeasonData(railCommonData: RailCommonData?) {
        this.railCommonData = railCommonData
        if (railCommonData != null) {
            if (railCommonData.enveuVideoItemBeans.isNotEmpty()) {
                binding!!.comingSoon.visibility = View.GONE
                seasonEpisodes = railCommonData.enveuVideoItemBeans
                binding!!.seasonHeader.visibility = View.VISIBLE
                binding!!.headerSeason.visibility = View.VISIBLE
                selectedSeason = railCommonData.enveuVideoItemBeans[seasonPosition].seriesCustomData.season_number.toInt()
                binding!!.seasonHeader.text =  resources.getString(R.string.season) + " " + selectedSeason
                if (seasonEpisodes.size > 1) {
                    binding!!.seasonHeader.isEnabled = true
                }
                binding!!.seasonHeader.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.arrow_down, 0)
                getRoundForSeason(seasonEpisodes[seasonPosition].id,selectedRound)
             }
            } else {
                binding!!.roundHeader.visibility = View.GONE
                binding!!.llRoundHeader.visibility = View.GONE
                binding!!.seasonHeader.visibility = View.GONE
                binding!!.headerSeason.visibility = View.GONE
                binding!!.comingSoon.visibility = View.VISIBLE
                binding!!.seriesRecyclerView.visibility = View.GONE
                binding!!.seasonMore.visibility = View.GONE
            }
        hideProgressBar()
    }


    private fun parseRoundData(railCommonData: RailCommonData?) {
        this.railCommonData = railCommonData
        if (railCommonData != null) {
            if (railCommonData.enveuVideoItemBeans.isNotEmpty()) {
                binding!!.comingSoon.visibility = View.GONE
                roundEpisodes = railCommonData.enveuVideoItemBeans
                binding!!.llRoundHeader.visibility = View.VISIBLE
                binding!!.roundHeader.visibility = View.VISIBLE
              //  val round = selectedRound + roundPosition
                binding!!.roundHeader.text =  resources.getString(R.string.round) + " " + selectedRound
                if (roundEpisodes.size > 1) {
                    binding!!.roundHeader.isEnabled = true
                }
                binding!!.roundHeader.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.arrow_down, 0)
                matchListApi(roundEpisodes[0].id,selectedRound)
            }
        } else {
            binding!!.comingSoon.visibility = View.VISIBLE
            binding!!.roundHeader.visibility = View.GONE
            binding!!.llRoundHeader.visibility = View.GONE
            binding!!.seasonHeader.visibility = View.GONE
            binding!!.headerSeason.visibility = View.GONE
            binding!!.seriesRecyclerView.visibility = View.GONE
            binding!!.seasonMore.visibility = View.GONE
        }
        hideProgressBar()
    }

    override fun onItemClick(itemValue: EnveuVideoItemBean?, isPremium: Boolean, position: Int) {
        val assetType = itemValue?.assetType
        var mediaType: String? = ""
        if (itemValue?.assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
            mediaType = itemValue?.videoDetails?.videoType
        } else  if (itemValue?.assetType.equals(AppConstants.CUSTOM, ignoreCase = true)) {
            mediaType = itemValue?.customType
        }else if (itemValue?.assetType.equals(AppConstants.AUDIO, ignoreCase = true)||itemValue?.assetType.equals(AppConstants.PERSON, ignoreCase = true)){
            mediaType=itemValue?.contentType
        }

        AppCommonMethod.launchDetailScreen(
            requireContext(),
            assetType!!,
            itemValue.id,
            itemValue.sku,
            mediaType,
            itemValue.title?:"",
            itemValue.externalRefId?:"",
            itemValue.posterURL?:"",
            seasonPosition,
            itemValue.contentSlug?:"",
            itemValue
        )
    }

    override fun getFirstItem(itemValue: EnveuVideoItemBean) {

    }
    override fun episodeClicked(itemBean: EnveuVideoItemBean?) {
        TODO("Not yet implemented")
    }

    override fun onSeasonClick(list: ArrayList<SelectedSeasonModel>, position: Int) {
        alertDialog!!.cancel()
        binding!!.transparentLayout.visibility = View.GONE
        binding!!.seasonHeader.visibility = View.VISIBLE
        binding!!.headerSeason.visibility = View.VISIBLE
        this.selectedSeason = list[position].list.seriesCustomData.season_number.toInt()
        this.seasonPosition = position
        this.selectedRound = 1
        binding!!.seasonHeader.text =  resources.getString(R.string.season) + " " + selectedSeason
        if (seasonEpisodes.size > 1) {
            binding!!.seasonHeader.isEnabled = true
        }
        binding!!.seasonHeader.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.arrow_down, 0)

        getRoundForSeason(seasonEpisodes[seasonPosition].id,selectedRound)
    }

    override fun onRoundClick(list: ArrayList<SelectedRoundModel>, position: Int) {
        alertDialog!!.cancel()
        binding!!.transparentLayout.visibility = View.GONE
        binding!!.progressBar.visibility = View.VISIBLE
        matchListApi(list[position].list.id, list[position].list.seriesCustomData.round_number.toInt())
    }

}