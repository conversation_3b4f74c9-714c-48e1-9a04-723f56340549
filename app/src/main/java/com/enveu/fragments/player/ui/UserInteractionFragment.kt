package com.enveu.fragments.player.ui

import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.GestureDetector
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.widget.ImageViewCompat
import androidx.lifecycle.ViewModelProvider
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.R
import com.enveu.activities.detail.ui.DetailActivity
import com.enveu.activities.detail.ui.EpisodeActivity
import com.enveu.activities.multiplePlaylist.MultiplePlaylistViewModel
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaConfig
import com.enveu.appLevelModel.MediaInfo
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.emptyResponse.ResponseEmpty
import com.enveu.beanModel.responseGetWatchlist.ResponseGetIsWatchlist
import com.enveu.beanModel.responseModels.sharing.SharingModel
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.client.utils.ClickHandler.disallowClick
import com.enveu.databinding.DetailWatchlistLikeShareViewBinding
import com.enveu.enums.DownloadStatus
import com.enveu.fragments.dialog.AlertDialogFragment
import com.enveu.fragments.dialog.AlertDialogSingleButtonFragment
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.utils.Constants
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.ActivityTrackers
import com.enveu.utils.helpers.CheckInternetConnection
import com.enveu.utils.helpers.ToastHandler
import com.enveu.utils.helpers.downloads.OnDownloadClickInteraction
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.show
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.enveu.view_model.ShortsViewModel
import com.google.gson.JsonObject
import com.moengage.core.internal.utils.showToast
import java.util.Objects

class UserInteractionFragment : BaseBindingFragment<DetailWatchlistLikeShareViewBinding?>(), AlertDialogFragment.AlertDialogListener, View.OnClickListener, CommonDialogFragment.EditDialogListener {
    private var assetId = 0
    private var preference: KsPreferenceKeys? = null
    private var token: String? = null
    private var watchListCounter = 0
    private var likeCounter = 0
    private var seriesDetailBean: EnveuVideoItemBean? = null
    private var trailerRefId: String? = null
    private var bookmarkingViewModel: BookmarkingViewModel? = null
    private var isLoggedOut = false
    private var isFromChannelClick = false
    private var onDownloadClickInteraction: OnDownloadClickInteraction? = null
    private var isLogin: String? = null
    private var playlistViewModel : MultiplePlaylistViewModel?= null
    private var mediaType: String? = null
    private var mediaConfig: MediaConfig? = null
    private var featureList: FeatureFlagModel? = null
    private var stringMediaConfig: String? = null
    private var shortsViewModel:ShortsViewModel? = null
    private  var registrationLoginViewModel: RegistrationLoginViewModel?=null
    private var redirectionFrom=""
    private var lastClickTime = 0L
    private val CLICK_DELAY = 3000L // 3 second
    private val stringsHelper by lazy { StringsHelper }
    private var hideWatchlistIcon:Boolean? = false
    override fun inflateBindingLayout(inflater: LayoutInflater): DetailWatchlistLikeShareViewBinding {
        return DetailWatchlistLikeShareViewBinding.inflate(inflater)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getAssetId()
        binding!!.stringData = StringsHelper
        binding!!.colorsData = ColorsHelper
        bookmarkingViewModel = ViewModelProvider(this)[BookmarkingViewModel::class.java]
        registrationLoginViewModel = ViewModelProvider(this)[RegistrationLoginViewModel::class.java]
        playlistViewModel = ViewModelProvider(this)[MultiplePlaylistViewModel::class.java]
        shortsViewModel = ViewModelProvider(this)[ShortsViewModel::class.java]
        getAppLevelJsonData()
        val bundle = arguments
        try {
            isLoggedOut = false
            isLogin = preference!!.appPrefLoginStatus
        } catch (e: Exception) {
            Logger.w(e)
        }
        setClickListeners()
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        if (activity is OnDownloadClickInteraction) {
            onDownloadClickInteraction = activity as OnDownloadClickInteraction?
        } else {
            Logger.w(activity.toString() + " does not implement OnDownloadClickInteraction")
        }
    }

    private fun getAppLevelJsonData() {
        try {
            featureList = AppConfigMethod.parseFeatureFlagList()
            val mediaConfigInfo: MediaInfo? = AppConfigMethod.getMediaMappingByType(mediaType!!)
            mediaConfig = AppConfigMethod.getMediaConfigByType(mediaConfigInfo?.pageType.toString())
            callApi()
        } catch (e:Exception) {

        }
    }

    private fun callApi() {
        try {
            if (featureList!!.featureFlag.ADD_TO_WATCHLIST && mediaConfig!!.detailPage.features.isWatchlistEnabled && !redirectionFrom.equals(AppConstants.EPG_FRAGMENT)) {
                if (seriesDetailBean?.liveContent?.equals("true") == true){
                    binding!!.watchList.visibility = View.GONE
                } else {
                    binding!!.watchList.visibility = View.VISIBLE
                }
                hitApiIsWatchList()
            } else {
                binding!!.watchList.visibility = View.GONE
            }
            if (featureList!!.featureFlag.LIKE && mediaConfig!!.detailPage.features.isLikeEnabled) {
                binding!!.llLike.visibility = View.VISIBLE
                hitApiIsLike()
            } else {
                binding!!.llLike.visibility = View.GONE
            }

            if (featureList!!.featureFlag.SHARING && mediaConfig!!.detailPage.features.isShareEnabled) {
                binding!!.shareWith.visibility =View.VISIBLE
            } else {
                binding!!.shareWith.visibility =View.GONE
            }
        } catch (e:Exception) {

        }
    }
    private fun setClickListeners() {
        binding!!.shareWith.setOnClickListener(this)
        binding!!.downloadStatus = DownloadStatus.START
    }

    private fun hitApiIsLike() {
        preference?.appPrefAccessToken.let { it ->
            playlistViewModel?.checkContentIsPresentInLike(it, assetId)
                ?.observe(requireActivity()) { response ->
                    if (response != null && response.responseCode == 2000) {
                        if (response.data?.get(0)?.present == true) {
                            likeCounter = 1
                            setLike()
                        } else {
                            resetLike()
                        }
                    }
                }
        }

    }

    private fun getAssetId() {
        val bundle = arguments
        if (bundle != null) {
            likeCounter = 0
            assetId = bundle.getInt(AppConstants.BUNDLE_ASSET_ID)
            mediaType = bundle.getString(AppConstants.BUNDLE_MEDIA_TYPE)
            seriesDetailBean = bundle.getSerializable(AppConstants.BUNDLE_SERIES_DETAIL) as EnveuVideoItemBean?
            trailerRefId = bundle.getString(AppConstants.BUNDLE_TRAILER_REF_ID)
            redirectionFrom = bundle.getString(AppConstants.FROM_REDIRECTION) ?: ""
            hideWatchlistIcon = bundle.getBoolean(Constants.HIDE_WATCHLIST_ICON)
        }
        preference = KsPreferenceKeys.getInstance()
        if (preference!!.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            token = preference!!.appPrefAccessToken
        } else {
            resetLike()
            resetWatchList()
        }
        uiInitialisation()
        if (hideWatchlistIcon == true){
            binding?.watchList?.hide()
        }
        else{
            binding?.watchList?.show()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    fun uiInitialisation() {
        likeClick()
        watchListClick()
        binding!!.shareWith.setOnTouchListener { _: View?, motionEvent: MotionEvent? ->
            gestureDetector.onTouchEvent(
                motionEvent!!
            )
        }
    }

    private var gestureDetector = GestureDetector(activity, object : SimpleOnGestureListener() {
        override fun onLongPress(event: MotionEvent) {
            super.onLongPress(event)
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            openShareDialogue(seriesDetailBean)
            return true
        }

        override fun onSingleTapUp(e: MotionEvent): Boolean {
            return true
        }
    })

    private fun goToLogin() {
        when (activity) {
            is SeriesDetailActivity -> {
                ActivityTrackers.getInstance().setLauncherActivity("SeriesDetailActivity")
                (activity as SeriesDetailActivity?)!!.openLogin()
            }

            is EpisodeActivity -> {
                (activity as EpisodeActivity?)!!.openLoginPage(resources.getString(R.string.please_login_play))
                ActivityTrackers.getInstance().setLauncherActivity("EpisodeActivity")
            }

            is DetailActivity -> {
                ActivityTrackers.getInstance().setLauncherActivity("DetailActivity")
                (activity as DetailActivity?)!!.openLoginPage(resources.getString(R.string.please_login_play))
            }
        }
    }

    private fun watchListClick() {
        binding!!.watchList.setOnClickListener {
            if (binding!!.wProgressBar.visibility != View.VISIBLE) {
                val isLogin = preference!!.appPrefLoginStatus
                if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
                    setWatchListForAsset(1)
                } else {
                    ActivityTrackers.getInstance().setAction(ActivityTrackers.WATCHLIST)
                    goToLogin()
                }
            }
        }
    }

    fun setWatchListForAsset(from: Int) {
        binding!!.wProgressBar.visibility = View.VISIBLE
        binding!!.addIcon.visibility = View.GONE
        if (watchListCounter == 0) {
            hitApiAddWatchList(from)
        } else {
            hitApiRemoveList()
        }
    }

    private fun hitApiRemoveList() {
        bookmarkingViewModel!!.hitRemoveWatchlist(token, assetId).observe(viewLifecycleOwner) { responseEmpty: ResponseEmpty ->
            binding!!.wProgressBar.visibility = View.GONE
            binding!!.addIcon.visibility = View.VISIBLE
            if (Objects.requireNonNull(responseEmpty).isStatus) {
                AnalyticsUtils.logUserInteractionEvent(context,AppConstants.REMOVE_WATCHLIST,assetId.toString(),seriesDetailBean?.title,seriesDetailBean?.contentType)
                resetWatchList()
            } else {
                if (responseEmpty.responseCode == 4302) {
                    isLoggedOut = true
                    logoutCall()
                } else if (responseEmpty.responseCode == 500) {
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )
                }
            }
        }
    }

    private fun likeClick() {
        binding!!.llLike.setOnClickListener { setLikeForAsset() }
    }

    fun setToken(token: String?) {
        this.token = token
    }

    fun setLikeForAsset() {
            if (disallowClick()) {
                return
            }
            if (preference!!.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
                if (likeCounter == 0) setLike() else resetLike()
            } else {
                ActivityTrackers.getInstance().setAction(ActivityTrackers.LIKE)
                goToLogin()
            }
    }


    private fun hitApiIsWatchList() {
        if (preference!!.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            bookmarkingViewModel!!.hitApiIsWatchList(token, assetId).observe(viewLifecycleOwner) { responseEmpty: ResponseGetIsWatchlist ->
                if (Objects.requireNonNull(responseEmpty).isStatus) {
                    AnalyticsUtils.logUserInteractionEvent(context,AppConstants.ADD_TO_WATCHLIST,assetId.toString(),seriesDetailBean?.title,seriesDetailBean?.contentType)
                    setWatchList()
                } else {
                    if (responseEmpty.responseCode == 4302) {
                        isLoggedOut = true
                        logoutCall()
                    } else if (responseEmpty.responseCode == 500) {
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ),
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            )
                        )
                    }
                }
            }
        }
    }

    private fun hitApiAddWatchList(from: Int) {
//        if (activity is SeriesDetailActivity) {
//            (activity as SeriesDetailActivity?)!!.seriesLoader()
//        }
        bookmarkingViewModel!!.hitApiAddWatchList(token, assetId).observe(viewLifecycleOwner) { responseEmpty: ResponseEmpty ->
            binding!!.wProgressBar.visibility = View.GONE
            binding!!.addIcon.visibility = View.VISIBLE
            if (Objects.requireNonNull(responseEmpty).isStatus) {
                setWatchList()
            } else {
                if (responseEmpty.responseCode == 4302) {
                    isLoggedOut = true
                    logoutCall()
                } else if (responseEmpty.responseCode == 4904) {
                    setWatchList()
                    val debugMessage = responseEmpty.debugMessage
                    //from value will bedepends on how user click of watchlist icon-->>if loggedout=2 else=2
                    if (from == 1) {
                        showToast(requireActivity(), getString(R.string.error))
                    }
                } else if (responseEmpty.responseCode == 500) {
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )
                } else {
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )
                }
            }
        }

        val attributeObject = JsonObject().apply {
            addProperty("contentId", assetId)
            addProperty("title", seriesDetailBean?.title)
            addProperty("mediaType", seriesDetailBean?.assetType)
        }
        val eventObject = JsonObject().apply {
            addProperty("name", "ADD TO WATCHLIST")
            addProperty("identifier", "ADD_TO_WATCHLIST")
            add("attribute", attributeObject)
        }
        val additionalInfoObject = JsonObject().apply {
            addProperty("customerId", preference?.userId)
        }
        val customerObject = JsonObject().apply {
            add("additionalInformation", additionalInfoObject)
        }
        val finalJson = JsonObject().apply {
            add("event", eventObject)
            add("customer", customerObject)
        }
        shortsViewModel?.setEventTracking(finalJson, preference?.appPrefAccessToken)
    }

    private fun openShareDialogue(seriesDetailBean: EnveuVideoItemBean?) {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime < CLICK_DELAY) {
            // Ignore click
            return
        }
        lastClickTime = currentTime
        // Handle click
        if (seriesDetailBean != null) {
            AppCommonMethod.openShareFirebaseDynamicLinks(requireActivity(), seriesDetailBean)
        }

        Handler(Looper.getMainLooper()).postDelayed({
            if (activity is SeriesDetailActivity) {
                (activity as SeriesDetailActivity?)!!.dismissLoading((activity as SeriesDetailActivity?)!!.binding!!.progressBar)
            }
        }, 2000)
    }

    private fun setLike() {
        if (isAdded &&  isResumed){
            binding!!.likeIcon.setImageResource(R.drawable.favorite_ic_solid)
            ImageViewCompat.setImageTintList(
                binding!!.likeIcon, ColorStateList.valueOf(resources.getColor(R.color.like_selected_icon_color))
            )
            if (likeCounter == 0) {
                likeCounter = 1
                hitApiAddToPlayList(assetId)
            }
        }
    }


    private fun hitApiAddToPlayList(assetId: Int) {
        playlistViewModel?.addContentPlaylist(
            KsPreferenceKeys.getInstance().appPrefAccessToken, assetId, "LIKED", null, false
        )

        val attributeObject = JsonObject().apply {
            addProperty("contentId", assetId)
            addProperty("title", seriesDetailBean?.title)
            addProperty("mediaType", seriesDetailBean?.assetType)
        }
        val eventObject = JsonObject().apply {
            addProperty("name", "LIKE CONTENT")
            addProperty("identifier", "LIKE_CONTENT")
            add("attribute", attributeObject)
        }
        val additionalInfoObject = JsonObject().apply {
            addProperty("customerId", preference?.userId)
        }
        val customerObject = JsonObject().apply {
            add("additionalInformation", additionalInfoObject)
        }
        val finalJson = JsonObject().apply {
            add("event", eventObject)
            add("customer", customerObject)
        }
        shortsViewModel?.setEventTracking(finalJson, preference?.appPrefAccessToken)
    }


    private fun resetLike() {
       if (isAdded && isResumed){
           binding?.likeIcon?.setImageResource(R.drawable.like_icon)
           ImageViewCompat.setImageTintList(
               binding!!.likeIcon, ColorStateList.valueOf(resources.getColor(R.color.white))
           )
           if (likeCounter == 1) {
               likeCounter = 0
               hitApiRemoveLike(assetId)
           }
       }
    }

    private fun hitApiRemoveLike(assetId: Int) {
        playlistViewModel?.removeContentPlaylist(
            preference?.appPrefAccessToken!!,
            assetId,
            "LIKED",
            "",
            true
        )
    }


    private fun setWatchList() {
        binding!!.wProgressBar.visibility = View.GONE
        binding!!.addIcon.visibility = View.VISIBLE
        watchListCounter = 1
        binding!!.addIcon.setImageResource(R.drawable.added_watch_list_icon)
        setTextColor()
    }

    private fun setTextColor() {
        binding!!.tvWatch.setTextColor(ContextCompat.getColor(requireActivity(), R.color.white))
    }

    private fun resetWatchList() {
        binding!!.wProgressBar.visibility = View.GONE
        binding!!.addIcon.visibility = View.VISIBLE
        watchListCounter = 0
        binding!!.addIcon.setImageResource(R.drawable.more_watchlist_ic)
        binding!!.tvWatch.setTextColor(ContextCompat.getColor(requireActivity(), R.color.white))

    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = activity?.supportFragmentManager!!
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }
    private fun showDialog(title: String, message: String) {
        val fm = requireActivity().supportFragmentManager
        val alertDialog = AlertDialogSingleButtonFragment.newInstance(
            title,
            message,
            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_ok.toString(), getString(R.string.popup_ok))
        )
        alertDialog.isCancelable = false
        alertDialog.setAlertDialogCallBack(this)
        alertDialog.show(fm, "fragment_alert")
    }

    override fun onFinishDialog() {
        if (isLoggedOut) {
            logoutCall()
        }
    }

    private fun logoutCall() {
        if (CheckInternetConnection.isOnline(requireActivity())) {
            clearCredientials(preference)
            hitApiLogout(baseActivity, preference!!.appPrefAccessToken)
        } else {
            ToastHandler.getInstance().show(activity, getString(R.string.no_internet_connection))
        }
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.shareWith -> {
                openShareDialogue(seriesDetailBean)
            }

            R.id.llLike -> {
                likeClick()
            }

            R.id.watchList -> {
                watchListClick()
            }

            R.id.download_video -> {
                if (onDownloadClickInteraction != null) onDownloadClickInteraction!!.onDownloadClicked(null, 0, this)
            }

            R.id.video_downloaded -> {
                if (onDownloadClickInteraction != null) onDownloadClickInteraction!!.onDownloadCompleteClicked(view, this, null)
            }

            R.id.video_downloading -> {
                if (onDownloadClickInteraction != null) onDownloadClickInteraction!!.onProgressbarClicked(view, this, null)
            }

            R.id.pause_download -> {
                Logger.w("pauseClicked", "in")
                if (onDownloadClickInteraction != null) onDownloadClickInteraction!!.onPauseClicked(null, this)
            }
        }
    }

    override fun onActionBtnClicked() {
    }

    override fun onCancelBtnClicked() {
    }

}