package com.enveu.fragments.search


import android.annotation.SuppressLint
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.InsetDrawable
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.SearchView
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.homeactivity.viewmodel.HomeViewModel
import com.enveu.activities.search.adapter.CategoriedSearchAdapter
import com.enveu.activities.search.adapter.CommonSearchAdapter
import com.enveu.activities.search.adapter.RecentListAdapter
import com.enveu.activities.search.adapter.RecentListAdapter.KeywordItemHolderListener
import com.enveu.activities.search.adapter.SearchContentPreferenceAdapter
import com.enveu.activities.search.adapter.SortedByFilterListener
import com.enveu.activities.search.ui.ActivityResults
import com.enveu.activities.sponsorArtist.ArtistAndSponserActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.adapters.CommonShimmerAdapter
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaInfo
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.KeywordList
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModel.popularSearch.ItemsItem
import com.enveu.beanModel.search.SearchRequestModel
import com.enveu.beanModelV3.searchHistory.SearchHistory
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent
import com.enveu.callbacks.commonCallbacks.SearchClickCallbacks
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.databinding.ActivitySearchBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstance
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstanceWithCancelBtn
import com.enveu.fragments.search.adapter.SearchMainAdapter
import com.enveu.menuManager.MenuCommonFunction
import com.enveu.utils.BindingUtils.FontUtil
import com.enveu.utils.Logger
import com.enveu.utils.colorsJson.converter.AppColors
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.AppPreference
import com.enveu.utils.helpers.NetworkConnectivity
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.RecyclerAnimator
import com.enveu.utils.helpers.SpacingItemDecoration
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.Collections.reverse
import java.util.Objects

class SearchFragment : BaseBindingFragment<ActivitySearchBinding?>(),OnSongItemClick,
    SearchClickCallbacks, KeywordItemHolderListener, SearchView.OnQueryTextListener, CommonDialogFragment.EditDialogListener {
    private var searchAdapter: CategoriedSearchAdapter? = null
    private var newSearchAdapter : SearchMainAdapter ?= null
    private var model: MutableList<RailCommonData>? = null
    private var getMediaTypeList: List<MediaInfo?>?= null
    private var mLastClickTime: Long = 0
    private var onSongItemClick: OnSongItemClick? = null
    private var isShimmer = false
    private var railInjectionHelper: RailInjectionHelper? = null
    private val FILTER_REQUEST_CODE = 2000
//    val searchSortedData  = MutableList<RailCommonData>()
    private var islogin: Boolean?= false
    private var searchText: String? = ""
    private var searchKeyword: String? = ""
    private var applyFilter = false
    private var requestModel: SearchRequestModel? = null
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    private var adapter: SearchContentPreferenceAdapter? = null
    private var featureList: FeatureFlagModel? = null
    private var searchTabFragment:SearchTabFragment?=null
    private var viewmodel : HomeViewModel?= null
    private var isNewRecentSearch=false
    private var isNewSearchCall=false
    private var recentSearchLimit=5
    private var isApiCallSuccess=false
    private var recentItemBg: GradientDrawable?=null
    var mediaMapingHash=HashMap<String,String>()

    @SuppressLint("SuspiciousIndentation")
    @RequiresApi(api = Build.VERSION_CODES.Q)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (activity != null && activity is HomeActivity)
            (activity as HomeActivity).toolFrame(1)
        (activity as HomeActivity).detailFrameVisibility()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        parseColor()
        getMediaTypeList = MenuCommonFunction.getParseSearchFilter(requireContext())
        featureList = AppConfigMethod.parseFeatureFlagList()
        if(featureList?.featureFlag?.IS_RECENT_SEARCHES==true){
            binding?.recentSearchTab?.visibility=View.VISIBLE
            binding?.recentSearchRecycler?.visibility=View.VISIBLE
        }else{
            binding?.recentSearchTab?.visibility=View.GONE
            binding?.recentSearchRecycler?.visibility=View.GONE
        }
        if (featureList?.featureFlag?.IS_NEW_RECENT_SEARCH == true){
            isNewRecentSearch=true
            recentSearchLimit=10
            recentItemBg=getGradientDrawable(colorsHelper.instance()?.data?.config?.app_secondary_color)
        }else{
            binding!!.recentSearchRecycler.overScrollMode=RecyclerView.OVER_SCROLL_NEVER
        }
        val font = FontUtil.getNormal(activity)
        val searchText = binding!!.toolbar.searchView.findViewById<TextView>(androidx.appcompat.R.id.search_src_text)
        val imageView = binding!!.toolbar.searchView.findViewById<ImageView>(androidx.appcompat.R.id.search_close_btn)
        try {
            searchText.setTextColor(AppColors.searchKeywordTextColor())
            searchText.setHintTextColor(AppColors.searchKeywordHintColor())
            searchText.isCursorVisible = true
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                if (searchText.textCursorDrawable is InsetDrawable) {
                    val insetDrawable = searchText.textCursorDrawable as InsetDrawable?
                    insetDrawable!!.setColorFilter(Color.WHITE, PorterDuff.Mode.SRC_ATOP)
                    searchText.textCursorDrawable = insetDrawable
                }
                if (searchText.textSelectHandle is BitmapDrawable) {
                    val insetDrawable = searchText.textSelectHandle as BitmapDrawable?
                    insetDrawable!!.setColorFilter(Color.WHITE, PorterDuff.Mode.SRC_ATOP)
                    searchText.setTextSelectHandle(insetDrawable)
                }
                if (searchText.textSelectHandleRight is BitmapDrawable) {
                    val insetDrawable = searchText.textSelectHandleRight as BitmapDrawable?
                    insetDrawable!!.setColorFilter(Color.WHITE, PorterDuff.Mode.SRC_ATOP)
                    searchText.setTextSelectHandleRight(insetDrawable)
                }
                if (searchText.textSelectHandleLeft is BitmapDrawable) {
                    val insetDrawable = searchText.textSelectHandleLeft as BitmapDrawable?
                    insetDrawable!!.setColorFilter(Color.WHITE, PorterDuff.Mode.SRC_ATOP)
                    searchText.setTextSelectHandleLeft(insetDrawable)
                }
            }
            imageView.setColorFilter(ContextCompat.getColor(requireActivity(), R.color.series_detail_now_playing_title_color), PorterDuff.Mode.MULTIPLY)
        } catch (ex: Exception) {
            Logger.w(ex)
        }
        islogin = KsPreferenceKeys.getInstance()?.appPrefLoginStatus?.equals(
            AppConstants.UserStatus.Login.toString(), ignoreCase = true
        ) == true
        searchText.typeface = font
        clickListner()
        connectionObserver()
        viewmodel = ViewModelProvider(this)[HomeViewModel::class.java]
        binding!!.toolbar.searchView.setOnQueryTextListener(this)
        binding!!.toolbar.searchView.setIconifiedByDefault(false) // Make sure the SearchView is not iconified (collapsed)
        // Set the hint for the SearchView
        binding!!.toolbar.searchView.queryHint = getString(R.string.search_hint)
        //showSoftKeyboard(binding!!.toolbar.searchView)
        setMediaTypeMaping()
    }

    private fun setMediaTypeMaping() {
        getMediaTypeList?.let {
            for (media in it){
                if (media?.mediaType.equals(AppConstants.SONGS)){
                    media?.mediaType?.let { it1 -> mediaMapingHash.put(it1,getString(R.string.songs)) }
                }else if (media?.mediaType.equals(AppConstants.ALBUM)){
                    media?.mediaType?.let { it1 -> mediaMapingHash.put(it1,getString(R.string.albums)) }
                }else if (media?.mediaType.equals(AppConstants.ARTISTS)){
                    media?.mediaType?.let { it1 -> mediaMapingHash.put(it1,getString(R.string.artists)) }
                }else{
                    media?.mediaType?.let { it1 -> mediaMapingHash.put(it1,it1) }
                }
            }
        }
    }
    private fun parseColor() {
        binding?.stringData = stringsHelper
        binding?.colorsData = colorsHelper
        binding?.connection?.colorsData = colorsHelper
        binding?.connection?.stringData = stringsHelper
        binding?.toolbar?.colorsData = colorsHelper
        binding?.toolbar?.stringData = stringsHelper
    }

    private var searchResult = true
    private fun clickListner() {
        binding!!.noResult.visibility = View.GONE
        hitApiPopularSearch()
        if (isNewRecentSearch){  setRecyclerPropertiesForNewUI(binding?.recentSearchRecycler!!) }else {
            setRecyclerProperties(binding?.recentSearchRecycler!!)
        }
        setSearchHistoryResult()
        binding!!.toolbar.backButton.setOnClickListener {
            ArtistAndSponserActivity.AppState.isSkipped = false
            requireActivity().onBackPressed()
        }
        binding!!.toolbar.clearText.setOnClickListener { requireActivity().onBackPressed() }
        binding!!.deleteKeywords.setOnClickListener {
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.recent_searches.toString(),
                    getString(R.string.popup_recent_search)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_delete_search_history.toString(),
                    getString(R.string.popup_delete_search_history)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_search_yes.toString(),
                    getString(R.string.popup_search_yes)
                )
            )
        }
    }
    interface OnDataPass {
        fun onDataPass()
    }

    private var dataPasser: OnDataPass? = null

    private fun setSearchHistoryResult() {
        if (islogin == true) {
            viewmodel?.getSearchHistory(KsPreferenceKeys.getInstance()?.appPrefAccessToken)?.observe(requireActivity()) {
                if (isNewRecentSearch){  setRecyclerPropertiesForNewUI(binding?.recentSearchRecycler!!) }else {
                    setRecyclerProperties(binding?.recentSearchRecycler!!)
                }
                if (it.data?.items != null && it.data.items.isNotEmpty()) {
                    binding!!.llRecentSearchLayout.visibility = View.VISIBLE
                    val recentListAdapter =if (isNewRecentSearch){
                        RecentListAdapter(requireContext(), it.data.items, null,this,1,recentItemBg)
                    }else{
                        RecentListAdapter(requireContext(), it.data.items, null,this) }
                    binding?.recentSearchRecycler?.adapter = recentListAdapter
                }
            } ?: setRecentSearchAdapter()
        }else{
            setRecentSearchAdapter()
        }
    }


    private fun setRecentSearchAdapter() {
        val gson = Gson()
        val json = AppPreference.getInstance(requireActivity()).recentSearchList
        if (json.isEmpty()) {
            binding!!.llRecentSearchLayout.visibility = View.GONE
        } else {
            if (!isApiCallSuccess) {
                binding!!.llRecentSearchLayout.visibility = View.VISIBLE
                isApiCallSuccess=false
            }
            val type = object : TypeToken<List<KeywordList?>?>() {}.type
            val arrPackageData = gson.fromJson<List<KeywordList?>>(json, type)
            reverse(arrPackageData)
          if (arrPackageData.isNotEmpty()) {
                val recentListAdapter =if (isNewRecentSearch){
                    RecentListAdapter(requireActivity(),null, arrPackageData, this@SearchFragment,1,recentItemBg)
                }else{
                    RecentListAdapter(requireActivity(),null, arrPackageData, this@SearchFragment) }
                binding!!.recentSearchRecycler.adapter = recentListAdapter
            }
        }
    }

    private fun confirmDeletion() {
        val builder = AlertDialog.Builder(requireActivity(), R.style.AlertDialogStyle)
        builder.setMessage(
            stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.popup_delete_search_history.toString(),
                getString(R.string.popup_delete_search_history)
            )
        ).setCancelable(true).setPositiveButton(this.resources.getString(R.string.delete)) { dialog: DialogInterface, _: Int ->
            AppPreference.getInstance(requireActivity()).recentSearchList = ""
            binding!!.llRecentSearchLayout.visibility = View.GONE
            dialog.cancel()
        }
            .setNegativeButton(this.resources.getString(R.string.cancel)) { dialog: DialogInterface, _: Int -> dialog.cancel() }
        val alert = builder.create()
        alert.show()
        val bn = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
        bn.setTextColor(ContextCompat.getColor(requireActivity(), R.color.series_detail_now_playing_title_color))
        val bp = alert.getButton(DialogInterface.BUTTON_POSITIVE)
        bp.setTextColor(ContextCompat.getColor(requireActivity(), R.color.series_detail_episode_unselected_btn_txt_color))
    }

    private fun hitApiSearchKeyword(searchKeyword: String) {
        model = ArrayList()
        callShimmer(binding!!.searchResultRecycler)
        setRecyclerProperties(binding!!.searchResultRecycler)
        binding!!.rootView.visibility = View.GONE
        binding!!.noResult.visibility = View.GONE
        binding!!.llSearchResultLayout.visibility = View.VISIBLE
        if (isNewSearchCall){
            setNewSearchCall()
        }else {
            railInjectionHelper?.getSearch(searchKeyword, 4, 0, false, true)
                ?.observe(this@SearchFragment) { data: List<RailCommonData> ->
                    searchResult = true
                    if (data.isNotEmpty()) {
                        searchTabFragment?.let {
                            it.setNewSearchKeyword(searchKeyword)
                        }
                        if (featureList?.featureFlag?.IS_SEARCH_FILTER == true) {
                            setRecycleViewContentPreferences()
                        }
                        this.searchKeyword=searchKeyword
                        AnalyticsUtils.trackScreenView(
                            context,
                            AppConstants.SEARCH + " - " + searchKeyword
                        )
                        AnalyticsUtils.logSearchEvent(context, searchKeyword)
                        try {
                            binding!!.noResult.visibility = View.GONE
                            binding!!.rootView.visibility = View.GONE
                            for (i in data.indices) {
                                val railCommonData = data[i]
                                if (railCommonData.pageTotal > 0) {
                                    if (railCommonData.status) {
                                        val temp =
                                            RailCommonData()
                                        temp.enveuVideoItemBeans =
                                            railCommonData.enveuVideoItemBeans
                                        if (railCommonData.enveuVideoItemBeans.size > 0) {
                                            val enveuVideoItemBean =
                                                railCommonData.enveuVideoItemBeans[0]
                                            temp.assetType = enveuVideoItemBean.assetType
                                            temp.status = true
                                            temp.searchKey = searchKeyword
                                            temp.totalCount = railCommonData.pageTotal
                                            (model as ArrayList<RailCommonData>).add(temp)
                                        }
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            binding!!.llNoresult.visibility = View.VISIBLE
                            binding!!.llRecentSearchLayout.visibility = View.GONE
                            binding!!.popularSearchGroup.visibility = View.GONE
                            Logger.w(e)
                        }
                        if ((model as ArrayList<RailCommonData>).size > 0) {
                            binding!!.llRecentSearchLayout.visibility = View.GONE
                            isApiCallSuccess=true
                            RecyclerAnimator(requireActivity()).animate(binding!!.searchResultRecycler)

                            if (featureList?.featureFlag?.IS_SEARCH_FILTER == true) {
                                newSearchAdapter = SearchMainAdapter(
                                    model as ArrayList<RailCommonData>,
                                    "All",
                                    searchKeyword,
                                    railInjectionHelper!!,
                                    mediaMapingHash,
                                    this@SearchFragment,
                                    this,
                                    object : SearchMainAdapter.OnDataPass {
                                        override fun onDataPass(mediaType: String) {
                                            setMoreClickOfAll(mediaType)
                                        }

                                        override fun callPaginatedAssetType(assetType: String,page:Int): RailCommonData {
                                            return data[0]
                                        }
                                    })
                                binding!!.searchResultRecycler.adapter = newSearchAdapter
                            } else {
                                searchAdapter = CategoriedSearchAdapter(
                                    requireActivity(),
                                    model,
                                    featureList,
                                    this@SearchFragment,
                                    searchKeyword
                                )
                                binding!!.searchResultRecycler.adapter = searchAdapter
                            }
                        } else {
                            binding!!.llNoresult.visibility = View.VISIBLE
                            binding!!.llRecentSearchLayout.visibility = View.GONE
                            binding!!.popularSearchGroup.visibility = View.GONE
                            binding!!.noResult.visibility = View.VISIBLE
                            binding!!.rootView.visibility = View.VISIBLE
                            binding!!.llSearchResultLayout.visibility = View.GONE
                        }
                    } else {
                        binding!!.llNoresult.visibility = View.VISIBLE
                        binding!!.llRecentSearchLayout.visibility = View.GONE
                        binding!!.popularSearchGroup.visibility = View.GONE
                        binding!!.noResult.visibility = View.VISIBLE
                        binding!!.rootView.visibility = View.VISIBLE
                        binding!!.llSearchResultLayout.visibility = View.GONE
                    }
                    if (KsPreferenceKeys.getInstance()?.isComeWithFilterScreen == false) {
                        createRecentSearch(searchKeyword)
                    } else {
                        KsPreferenceKeys.getInstance()?.isComeWithFilterScreen = false
                    }
                    binding!!.progressBar.visibility = View.GONE
                }
        }
    }

    private fun setMoreClickOfAll(mediaType: String) {
        adapter?.setFilter(getAssetTypeByFillter(mediaType))
        if (searchTabFragment == null) {
            setSearchTabFragment(searchKeyword, mediaType)
            binding!!.searchResultRecycler.visibility = View.GONE
        } else {
            binding!!.searchResultRecycler.visibility = View.GONE
            binding!!.flSearchTabFrag.visibility = View.VISIBLE
            searchTabFragment!!.setDiffrentData(mediaType)
        }
    }
    private fun getAssetTypeByFillter(filter: String): String {
        if (filter.equals(getString(R.string.songs),true)){
            return "SONGS"
        }else if (filter.equals(getString(R.string.albums),true)){
            return "ALBUMS"
        }else if (filter.equals(getString(R.string.artists),true)){
            return "ARTISTS"
        }else{
            return filter
        }
    }
    private fun setNewSearchCall() {
//        railInjectionHelper?.getSearchForMusicContents(searchKeyword, 50, 0)
//            ?.observe(this@SearchFragment) { data: List<RailCommonData> ->
//
//            }
    }

//    private fun sortData(model: ArrayList<RailCommonData>) : ArrayList<SearchSortedData> {
//        searchSortedData.clear()
//        MenuCommonFunction.getParseSearchFilter(requireContext())?.forEach { mediaList ->
//            if (mediaList?.isSearchable == true){
//                model.forEach {
//                    searchSortedData.add(SearchSortedData(mediaList?.mediaType!!, it.enveuVideoItemBeans.filter { it.contentType == mediaList.mediaType || it.customType == mediaList.mediaType}))
//                }
//            }
//        }
//        return  searchSortedData
//    }

    override fun onDetach() {
        super.onDetach()
        if (activity != null && activity is HomeActivity) {
            (activity as HomeActivity).toolFrame(0)
            (activity as HomeActivity).detailFrameVisibility()
        }
        dataPasser = null
        KsPreferenceKeys.getInstance().isComeWithFilterScreen = false
    }
    private fun createRecentSearch(searchKeyword: String) {
        if (searchKeyword.equals("", ignoreCase = true)) {
            return
        }
        val keywordList = KeywordList()
        keywordList.keywords = searchKeyword
        keywordList.timeStamp = AppCommonMethod.currentTimeStamp
        if (AppPreference.getInstance(requireActivity()).recentSearchList.equals("", ignoreCase = true)) {
            val list: MutableList<KeywordList> = ArrayList()
            list.add(keywordList)
            val gson = Gson()
            val json = gson.toJson(list)
            AppPreference.getInstance(requireActivity()).recentSearchList = json
            setRecentSearchAdapter()
        } else {
            val gson = Gson()
            val json = AppPreference.getInstance(requireActivity()).recentSearchList
            if (json.isEmpty()) {
            } else {
                val type = object : TypeToken<List<KeywordList?>?>() {}.type
                val arrPackageData = gson.fromJson<List<KeywordList?>>(json, type)
                if (json.contains(searchKeyword)) {
                    return
                }
                val newL: MutableList<KeywordList?> = ArrayList(arrPackageData)
                if (newL.size < recentSearchLimit) {
                    newL.add(keywordList)
                } else {
                    newL.removeAt(0)
                    newL.add(keywordList)
                }
                val jsons = gson.toJson(newL)
                AppPreference.getInstance(requireActivity()).recentSearchList = jsons
                newL.reverse()
                if (newL.size > 0) {
                    val recentListAdapter =if (isNewRecentSearch){ RecentListAdapter(requireActivity(), null, newL, this@SearchFragment,1,recentItemBg) }
                        else{ RecentListAdapter(requireActivity(), null, newL, this@SearchFragment) }
                    recentListAdapter.setListLimit(recentSearchLimit)
                    binding!!.recentSearchRecycler.adapter = recentListAdapter
                }
            }
        }
    }

    private fun hitApiPopularSearch() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        if (!SDKConfig.getInstance().popularSearchId.equals("", ignoreCase = true)) {
            railInjectionHelper?.getPlayListDetailsWithPagination(SDKConfig.getInstance().popularSearchId, KsPreferenceKeys.getInstance().appPrefAccessToken, 0, 8, null)?.observe(requireActivity()) { playlistRailData: RailCommonData ->
                if (Objects.requireNonNull(playlistRailData) != null) {
                    setUiComponents(playlistRailData)
                }
            }
        }
    }

    private fun setUiComponents(jsonObject: RailCommonData) {
        if (isAdded && context != null) {
            binding!!.popularSearchRecycler.addItemDecoration(
                SpacingItemDecoration(
                    5, SpacingItemDecoration.HORIZONTAL
                )
            )
            val gridItem = if (isAdded && requireContext().resources.getBoolean(R.bool.isTablet)) {
                4
            } else {
                if (featureList?.featureFlag?.IS_SQUARE_DEFAULT == true) {
                    3
                } else {
                    2
                }
            }
            isShimmer = false
            if (jsonObject.status) {
                binding!!.tvPopularSearch.visibility = View.VISIBLE
            } else {
                binding!!.popularSearchRecycler.visibility = View.GONE
            }


            binding!!.popularSearchRecycler.layoutManager =
                GridLayoutManager(requireActivity(), gridItem)
            binding!!.popularSearchRecycler.adapter = CommonSearchAdapter(
                requireActivity(), jsonObject, this, featureList
            )
        }

    }

    private fun setRecyclerProperties(recyclerView: RecyclerView) {
        recyclerView.hasFixedSize()
        recyclerView.isNestedScrollingEnabled = false
        recyclerView.layoutManager = LinearLayoutManager(requireActivity(), RecyclerView.VERTICAL, false)
    }
    private fun setRecyclerPropertiesForNewUI(recyclerView: RecyclerView) {
        recyclerView.hasFixedSize()
        recyclerView.isNestedScrollingEnabled = false
        recyclerView.layoutManager = LinearLayoutManager(requireActivity(), RecyclerView.HORIZONTAL, false)
    }
    private fun connectionObserver() {
        connectionValidation(NetworkConnectivity.isOnline(requireActivity()))
    }

    private fun connectionValidation(aBoolean: Boolean) {
        if (aBoolean) {
            uiInitialisation()
        } else {
            noConnectionLayout()
        }
    }

    private fun uiInitialisation() {
        binding!!.searchLayout.visibility = View.VISIBLE
        binding!!.noConnectionLayout.visibility = View.GONE
        binding!!.tvPopularSearch.visibility = View.GONE
        binding!!.toolbar.searchView.requestFocus()
        callShimmer(binding!!.popularSearchRecycler)
        clickListner()
    }

    private fun callShimmer(recyclerView: RecyclerView) {
        isShimmer = true
        val adapterPurchase = CommonShimmerAdapter(true)
        val mLayoutManager: RecyclerView.LayoutManager = LinearLayoutManager(requireActivity())
        recyclerView.layoutManager = mLayoutManager
        recyclerView.itemAnimator = DefaultItemAnimator()
        recyclerView.adapter = adapterPurchase
    }

    private fun noConnectionLayout() {
        binding!!.searchLayout.visibility = View.GONE
        binding!!.progressBar.visibility = View.GONE
        binding!!.noConnectionLayout.visibility = View.VISIBLE
        binding!!.connection.retryTxt.setOnClickListener { connectionObserver() }
    }

    override fun inflateBindingLayout(inflater: LayoutInflater): ActivitySearchBinding {
        return ActivitySearchBinding.inflate(inflater)
    }

    override fun onEnveuItemClicked(itemValue: EnveuVideoItemBean) {
        val assetType = itemValue.assetType
        var mediType: String? = ""
        if (itemValue.assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
            mediType = itemValue.videoDetails.videoType
        }  else  if (itemValue.assetType.equals(AppConstants.CUSTOM, ignoreCase = true)) {
            mediType = itemValue.customType
        }else if (itemValue.assetType.equals(AppConstants.AUDIO, ignoreCase = true)||itemValue.assetType.equals(AppConstants.PERSON, ignoreCase = true)){
            mediType=itemValue.contentType
        }

        AppCommonMethod.launchDetailScreen(
            requireActivity(),
            assetType,
            itemValue.id,
            itemValue.sku,
            mediType,
            itemValue.title?:"",
            itemValue.externalRefId?:"",
            itemValue.posterURL?:"",
            0,
            itemValue.contentSlug?:"",
            itemValue
        )
    }

    override fun onShowAllItemClicked(itemValue: RailCommonData, header: String) {
        val customContentType: String = ""
        var videoType: String? = ""
        val assetType = itemValue.assetType

        if (assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
            videoType = itemValue.enveuVideoItemBeans[0].videoDetails.videoType
        }
        if (itemValue.status) {
            applyFilter = java.lang.Boolean.parseBoolean(KsPreferenceKeys.getInstance().filterApply)
            ActivityLauncher.getInstance().resultActivityBundle(
                requireActivity(), ActivityResults::class.java,
                assetType, itemValue.searchKey, itemValue.totalCount, applyFilter, customContentType, videoType, header
            )
        }
    }

    override fun onShowAllProgramClicked(itemValue: RailCommonData) {
        if (itemValue.status) {
            applyFilter = java.lang.Boolean.parseBoolean(KsPreferenceKeys.getInstance().filterApply)
        }
    }

    override fun onPopularSearchItemClicked(itemValue: ItemsItem?) {
    }


    override fun onItemClicked(itemValue: SearchHistory.Data.Item) {
        if (NetworkConnectivity.isOnline(requireActivity())) {
          hideSoftKeyboard(binding!!.toolbar.searchView)
            if (searchResult) {
                searchResult = false
                binding!!.toolbar.searchView.setQuery(itemValue.keyword?.trim { it <= ' ' }, true)
            }
        } else {
            commonDialog(
                "",
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                    getString(R.string.popup_no_internet_connection_found)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
    }

    private fun setRecycleViewContentPreferences(){
        if (getMediaTypeList != null) {
            binding?.rvContentPreference?.setHasFixedSize(true)
            adapter = SearchContentPreferenceAdapter(getMediaTypeList as MutableList<MediaInfo?>?,mediaMapingHash, object  :
                SortedByFilterListener {
                override fun filterSort(filter: String) {
//                    newSearchAdapter?.sortList(filter,model as ArrayList)
                    if (filter.equals("all",true)){
                        binding!!.searchResultRecycler.visibility=View.VISIBLE
                        binding!!.flSearchTabFrag.visibility=View.GONE
                    }else {
                        if (searchTabFragment == null) {
                            setSearchTabFragment(searchKeyword, filter)
                            binding!!.searchResultRecycler.visibility = View.GONE
                        } else {
                            binding!!.searchResultRecycler.visibility = View.GONE
                            binding!!.flSearchTabFrag.visibility = View.VISIBLE
                            searchTabFragment!!.setDiffrentData(filter)
                        }
                    }
                }
            })
            binding?.rvContentPreference?.visibility = View.VISIBLE
            binding?.rvContentPreference?.adapter = adapter
        }
    }
    private fun setSearchTabFragment(searchKeyword: String?, filter: String) {
        val bundle=Bundle()
        searchTabFragment=SearchTabFragment()
        bundle.putString("searchKeyword",searchKeyword)
        bundle.putString("filter",filter)
        searchTabFragment!!.arguments=bundle
        searchTabFragment?.let {
            requireActivity().supportFragmentManager.beginTransaction().replace(
                binding!!.flSearchTabFrag.id,it
            ).commit()
        }
    }

    override fun onItemClickedByLocal(itemValue: KeywordList?) {
        if (NetworkConnectivity.isOnline(requireActivity())) {
            hideSoftKeyboard(binding!!.toolbar.searchView)
            if (searchResult) {
                searchResult = false
                binding!!.toolbar.searchView.setQuery(itemValue?.keywords?.trim { it <= ' ' }, true)
            }
        } else {
            commonDialog(
                "",
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                    getString(R.string.popup_no_internet_connection_found)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
    }

    override fun onQueryTextSubmit(query: String): Boolean {
        if (NetworkConnectivity.isOnline(requireActivity())) {
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                return true
            }
            mLastClickTime = SystemClock.elapsedRealtime()
            hideSoftKeyboard(binding!!.toolbar.searchView)
            if (query.trim { it <= ' ' }.length > 2) {
                applyFilter = java.lang.Boolean.parseBoolean(KsPreferenceKeys.getInstance().filterApply)
                searchText = query.trim { it <= ' ' }
                Logger.d("SEARCH TEXT $searchText")
                hitApiSearchKeyword(query.trim { it <= ' ' })
            }
        } else {
            commonDialog(
                "",
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_no_internet_connection_found.toString(),
                    getString(R.string.popup_no_internet_connection_found)
                ), stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }
        return false
    }

    override fun onQueryTextChange(newText: String): Boolean {
        return false
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == FILTER_REQUEST_CODE) {
            if (data != null && data.hasExtra("selected_filter")) {
                requestModel = data.getParcelableExtra("selected_filter")
            }
           /* if (resultCode == RESULT_OK) {
                Logger.d("RETURN WITH DATA")
                if (searchText != null && searchText!!.trim { it <= ' ' }.length > 2) {
                    hitApiSearchKeyword(searchText!!.trim { it <= ' ' })
                }
            } else {
                hitApiSearchKeyword(searchText!!.trim { it <= ' ' })
            }*/
        }
    }



    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = requireActivity().supportFragmentManager
        val commonDialogFragment = newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = requireActivity().supportFragmentManager
        val commonDialogFragment = newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun onActionBtnClicked() {
        AppPreference.getInstance(requireActivity()).recentSearchList = ""
        binding!!.llRecentSearchLayout.visibility = View.GONE
        if (islogin == true){
            viewmodel?.deleteSearchHistory(KsPreferenceKeys.getInstance().appPrefAccessToken, "", "",true)
        }
    }

    override fun onCancelBtnClicked() {

    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        dataPasser = context as? OnDataPass
        onSongItemClick = context as OnSongItemClick
    }


    private fun checkEntitlement(
        token: String?,
        song: DataItem,
        imageContent: ImageContent?,
        playQueueItems: Boolean?
    ) {
        railInjectionHelper?.hitApiEntitlement(token, song.sku)?.observe(this@SearchFragment) { responseEntitle ->
            if (responseEntitle != null && responseEntitle.data != null) {
                val gson = Gson()
                val json = gson.toJson(model)
                val songList = gson.fromJson(json, Array<DataItem>::class.java).toList()

                if (responseEntitle.data.entitled) {
                    railInjectionHelper?.externalRefID(
                        responseEntitle.data?.accessToken,
                        responseEntitle.data?.sku
                    )?.observe(this) { drm ->
                        if (onSongItemClick != null) {
                            onSongItemClick!!.songItemClick(
                                songList,
                                song,
                                drm.data?.externalRefId!!,
                                imageContent,
                                AppCommonMethod.getSongsPosterImageUrl(song),
                                playQueueItems
                            )
                        }
                    }
                } else {
                    commonDialogWithCancel(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                            getString(R.string.popup_notEntitled)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_select_plan.toString(),
                            getString(R.string.popup_select_plan)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_purchase.toString(),
                            getString(R.string.popup_purchase)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                            getString(R.string.popup_cancel)
                        )
                    )
                }
            } else {
                if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                    ActivityLauncher.getInstance().loginActivity(
                        requireActivity(),
                        ActivityLogin::class.java, ""
                    )
                } else {
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )
                }
            }
        }
    }

    override fun songItemClick(
        songList: List<DataItem>,
        song: DataItem,
        extarnalRefId: String,
        imageContent: ImageContent?,
        image: String?,
        playQueueItems:Boolean?, isQueueItemClick: Boolean, songsPosition:Int
    ) {
        if (islogin == true) {
        if (!songList.isNullOrEmpty()) {
                if (featureList?.featureFlag?.IS_SPONSOR == true) {
                    if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()) {
                        if (!ArtistAndSponserActivity.AppState.isSkipped) {
                            ActivityLauncher.getInstance().artistAndSponserActivity(
                                requireActivity(),
                                ArtistAndSponserActivity::class.java
                            )
                        } else {
                            initiatePlayback(playQueueItems, song, songList)
                            ArtistAndSponserActivity.AppState.isSkipped = false
                        }
                    } else {
                        ArtistAndSponserActivity.AppState.isSkipped = false
                        initiatePlayback(playQueueItems, song, songList)
                    }
                } else {
                    initiatePlayback(playQueueItems, song, songList)
                }
            }
        }else{
            ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin::class.java, "")
        }
    }

    private fun initiatePlayback(playQueueItems: Boolean?, song: DataItem, songList: List<DataItem>) {
        if (song.customDataV3.songs_albums_id != null) {
            AppCommonMethod.setAlbumFragment(requireActivity(), song.customDataV3.songs_albums_id.contentSlug, song.customDataV3.songs_albums_id.mediaType, song.customDataV3.songs_albums_id.id.toString())
        }
        else {
            if (song.isPremium) {
                checkEntitlement(KsPreferenceKeys.getInstance()?.appPrefAccessToken, song, AppCommonMethod.getImageContent(song), playQueueItems)
            } else {
                if (onSongItemClick != null) {
                    onSongItemClick?.songItemClick(
                        songList,
                        song,
                        song.externalRefId ?: "0",  // Provide a default value if song.externalRefId is null
                        AppCommonMethod.getImageContent(song),
                        AppCommonMethod.getSongsPosterImageUrl(song), playQueueItems)

                }
            }
        }
    }

    private fun getGradientDrawable(appSecondaryColor: String?): GradientDrawable {
        val drawable = GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            cornerRadius = resources.getDimension(R.dimen.NontonSize15)
            appSecondaryColor?.let {
                setColor(Color.parseColor(appSecondaryColor))
            } ?: setColor(Color.parseColor("#273b44"))
        }
        return drawable
    }
}

data class SearchSortedData(var title:String, var seasonList:List<EnveuVideoItemBean>)