package com.enveu.fragments.search

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.enveu.R
import com.enveu.activities.sponsorArtist.ArtistAndSponserActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.baseModels.BaseBindingFragment
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.databinding.FragmentSearchTabBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstanceWithCancelBtn
import com.enveu.fragments.search.adapter.SearchAdapter
import com.enveu.utils.Logger
import com.enveu.utils.PaginationScrollListener
import com.enveu.utils.colorsJson.converter.ColorsHelper
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.AppPreference
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.ToastHandler
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson

class SearchTabFragment: BaseBindingFragment<FragmentSearchTabBinding>(),OnSongItemClick,CommonDialogFragment.EditDialogListener{
    private var searchKeyword=""
    private var filter=""
    private var railInjectionHelper: RailInjectionHelper? = null
    private val stringsHelper by lazy { StringsHelper }
    private val colorsHelper by lazy { ColorsHelper }
    private var assetType=""
    private var model: MutableList<RailCommonData>? = null
    private var searchAdapter:SearchAdapter?=null
    private var list:List<EnveuVideoItemBean>?=null
    private var onSongItemClick: OnSongItemClick? = null
    private var totalPages=0
    private var featureList: FeatureFlagModel? = null
    private var islogin: Boolean?= false

    override fun inflateBindingLayout(inflater: LayoutInflater): FragmentSearchTabBinding {
        return FragmentSearchTabBinding.inflate(inflater)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        featureList = AppConfigMethod.parseFeatureFlagList()
        searchKeyword= arguments?.getString("searchKeyword").toString()
        filter= arguments?.getString("filter").toString()
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        binding?.stringData = stringsHelper
        binding?.colorsData = colorsHelper
        list=ArrayList<EnveuVideoItemBean>()
        assetType=getAssetTypeByFillter(filter)
//        binding!!.progressSearchTabFrag.visibility=View.VISIBLE
        binding!!.flProgressSearchTab.visibility=View.VISIBLE
        binding!!.rvSearchTabFrag.visibility=View.INVISIBLE
        callApiForSearchData(0,assetType)
        islogin = KsPreferenceKeys.getInstance()?.appPrefLoginStatus?.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true) == true
    }

    private fun callApiForSearchData(page:Int,assetType:String) {
        try {
            railInjectionHelper!!.getSearchForMusicContents(searchKeyword, 20, page, assetType)
                ?.observe(viewLifecycleOwner) { data: List<RailCommonData> ->
                    if (data != null && data.isNotEmpty()) {
                        if (totalPages == 0) {
                            totalPages = data[0].pageTotal
                        }
                        list = if (list == null || list!!.isEmpty()) {
                            data[0].enveuVideoItemBeans
                        } else {
                            list!!.plus(data[0].enveuVideoItemBeans)
                        }
                        list=list?.distinctBy { it.id }
//                        binding!!.progressSearchTabFrag.visibility=View.GONE
                        binding!!.flProgressSearchTab.visibility=View.GONE
                        binding!!.rvSearchTabFrag.visibility=View.VISIBLE
                        list?.let {
                            if (page > 0) {
                                searchAdapter?.setData(it)
                            } else {
                                setSearchAdapter(it)
                            }
                        }
                    }else{
//                        binding!!.progressSearchTabFrag.visibility=View.GONE
                        binding!!.flProgressSearchTab.visibility=View.GONE
                        binding!!.rvSearchTabFrag.visibility=View.VISIBLE
                        ToastHandler.getInstance().show(requireActivity(),"Technical issue, try after some time!!!!")
                    }
                }
        }catch (e:Exception){
            e.printStackTrace()
            Logger.e(e.message!!)
        }
    }

    private fun setSearchAdapter(dataList: List<EnveuVideoItemBean>) {
        searchAdapter= SearchAdapter(dataList,filter,true,this)
        val linearLayoutManager=LinearLayoutManager(this.context)
        binding!!.rvSearchTabFrag.layoutManager=linearLayoutManager
        binding!!.rvSearchTabFrag.adapter=searchAdapter
        binding!!.rvSearchTabFrag.addOnScrollListener(object : PaginationScrollListener(linearLayoutManager){
            override fun onLoadMore(page: Int) {
                if (totalPages > page) {
                    binding!!.flProgressSearchTab.visibility=View.VISIBLE
                    callApiForSearchData(page,assetType)
                }
            }
        })
    }

    private fun getAssetTypeByFillter(filter: String): String {
        if (filter.equals(getString(R.string.songs),true)){
            return "SONGS"
        }else if (filter.equals(getString(R.string.albums),true)){
            return "ALBUMS"
        }else if (filter.equals(getString(R.string.artists),true)){
            return "ARTISTS"
        }else if (filter.equals(getString(R.string.movies),true)){
            return "MOVIE"
        }else if (filter.equals(getString(R.string.series),true)){
            return "SERIES"
        }else if (filter.equals(getString(R.string.episodes))){
            return "EPISODES"
        }else{
            return filter
        }
    }
    override fun songItemClick(
        songList: List<DataItem>,
        song: DataItem,
        extarnalRefId: String,
        imageContent: ImageContent?,
        image: String?,
        playQueueItems: Boolean?, isQueueItemClick: Boolean, songsPosition:Int
    ) {
        if (songList.isNotEmpty()) {
            if (islogin == true) {
                if (featureList?.featureFlag?.IS_SPONSOR == true) {
                    if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()) {
                        if (!ArtistAndSponserActivity.AppState.isSkipped) {
                            ActivityLauncher.getInstance().artistAndSponserActivity(
                                requireActivity(),
                                ArtistAndSponserActivity::class.java
                            )
                        } else {
                            initiatePlayback(playQueueItems, song, songList)
                            ArtistAndSponserActivity.AppState.isSkipped = false
                        }
                    } else {
                        ArtistAndSponserActivity.AppState.isSkipped = false
                        initiatePlayback(playQueueItems, song, songList)
                    }
                } else {
                    initiatePlayback(playQueueItems, song, songList)
                }
            }
        }else{
            ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin::class.java, "")
        }
    }

    private fun initiatePlayback(playQueueItems: Boolean?, song: DataItem, songList: List<DataItem>) {
        if (song.customDataV3.songs_albums_id != null) {
            AppCommonMethod.setAlbumFragment(requireActivity(), song.customDataV3.songs_albums_id.contentSlug, song.customDataV3.songs_albums_id.mediaType, song.customDataV3.songs_albums_id.id.toString())
        }
        else {
            if (song.isPremium) {
                checkEntitlement(KsPreferenceKeys.getInstance()?.appPrefAccessToken, song, AppCommonMethod.getImageContent(song), playQueueItems)
            } else {
                if (onSongItemClick != null) {
                    onSongItemClick?.songItemClick(
                        songList,
                        song,
                        song.externalRefId ?: "0",  // Provide a default value if song.externalRefId is null
                        AppCommonMethod.getImageContent(song),
                        AppCommonMethod.getSongsPosterImageUrl(song), playQueueItems)

                }
            }
        }
    }

    fun setDiffrentData(filter: String) {
        this.filter=filter
        this.assetType=getAssetTypeByFillter(filter)
        list=null
//        binding!!.progressSearchTabFrag.visibility=View.VISIBLE
        binding!!.flProgressSearchTab.visibility=View.VISIBLE
        binding!!.rvSearchTabFrag.visibility=View.INVISIBLE
        callApiForSearchData(0,assetType)
    }

    private fun checkEntitlement(
        token: String?,
        song: DataItem,
        imageContent: ImageContent?,
        playQueueItems: Boolean?
    ) {
        railInjectionHelper?.hitApiEntitlement(token, song.sku)?.observe(this@SearchTabFragment) { responseEntitle ->
            if (responseEntitle != null && responseEntitle.data != null) {
                val gson = Gson()
                val json = gson.toJson(model)
                val songList = gson.fromJson(json, Array<DataItem>::class.java).toList()

                if (responseEntitle.data.entitled) {
                    railInjectionHelper?.externalRefID(
                        responseEntitle.data?.accessToken,
                        responseEntitle.data?.sku
                    )?.observe(this) { drm ->
                        if (onSongItemClick != null) {
                            onSongItemClick!!.songItemClick(
                                songList,
                                song,
                                drm.data?.externalRefId!!,
                                imageContent,
                                AppCommonMethod.getSongsPosterImageUrl(song),
                                playQueueItems
                            )
                        }
                    }
                } else {
                    commonDialogWithCancel(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                            getString(R.string.popup_notEntitled)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_select_plan.toString(),
                            getString(R.string.popup_select_plan)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_purchase.toString(),
                            getString(R.string.popup_purchase)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_cancel.toString(),
                            getString(R.string.popup_cancel)
                        )
                    )
                }
            } else {
                if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                    ActivityLauncher.getInstance().loginActivity(
                        requireActivity(),
                        ActivityLogin::class.java, ""
                    )
                } else {
                    commonDialog(
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_error.toString(),
                            getString(R.string.popup_error)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                            getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                            getString(R.string.popup_continue)
                        )
                    )
                }
            }
        }
    }
    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = requireActivity().supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = requireActivity().supportFragmentManager
        val commonDialogFragment = newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun onActionBtnClicked() {
        AppPreference.getInstance(requireActivity()).recentSearchList = ""
        if (KsPreferenceKeys.getInstance().appPrefAccessToken.isNotEmpty()){
//            viewmodel?.deleteSearchHistory(KsPreferenceKeys.getInstance().appPrefAccessToken, "", "",true)
        }
    }

    override fun onCancelBtnClicked() {
    }

    fun setNewSearchKeyword(searchKeyword: String) {
        this.searchKeyword=searchKeyword
    }
}