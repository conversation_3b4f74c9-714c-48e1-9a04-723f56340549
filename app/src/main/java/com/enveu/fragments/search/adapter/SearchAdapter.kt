package com.enveu.fragments.search.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.SongsAlbumIdItem
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.client.playlist.beanv2_0.CommonCustomData
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.google.gson.Gson
import java.util.ArrayList


class SearchAdapter(
    private var list: List<EnveuVideoItemBean>,
    private val filter: String,
    private val moreClick: Boolean,
    private var onSongItemClick: OnSongItemClick? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        const val ArtistTypeView = 1
        const val SongTypeView = 2
        const val VideoTypeView = 3
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ArtistTypeView -> {
                ArtistViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.artist_search_layout_item, parent, false)
                )
            }
            VideoTypeView -> {
                VideoSearchHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.video_search_layout_item, parent, false)
                )
            }

            SongTypeView -> {
                SongViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.search_layout_item, parent, false)
                )
            }

            else -> {
                throw IllegalArgumentException("Invalid view type")
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (position >= 0 && position < list.size && list[position].contentType == "ARTISTS") {
            ArtistTypeView
        } else if (position >= 0 && position < list.size && list[position].contentType == "VIDEO") {
            VideoTypeView
        } else if (position >= 0 && position < list.size && list[position].contentType == AppConstants.CUSTOM) {
            if (list[position].customContent != null && list[position].customContent.customType == AppConstants.ALBUM){
                SongTypeView
            }else {
                VideoTypeView
            }
        }else {
            SongTypeView
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (position >= 0 && position < list.size) {
            when (getItemViewType(position)) {
                ArtistTypeView -> {
                    val artistHolder = holder as ArtistViewHolder
                    artistHolder.title.text = list[position].title
                    if (list[position].description.isNotEmpty() && list[position].description.isNotBlank()) {
                        artistHolder.desc.text = list[position].description
                    }else{
                        artistHolder.desc.visibility=View.GONE
                    }
                    artistHolder.itemView.setOnClickListener {
                        // Handle artist item click here
                        AppCommonMethod.launchArtistAndAlbum(
                            holder.itemView.context,
                            list[position]?.contentType!!,
                            list[position]?.contentSlug!!,
                            list[position].id
                        )
                    }
                    if (list[position].imageContent != null && list[position].imageContent.src != null && list[position].imageContent.src.isNotEmpty()) {
                        Glide.with(holder.itemView.context)
                            .load(list[position].imageContent.src)
                            .into(artistHolder.imageView)
                    } else {
                        holder.image_title.text = list[position].title
                    }
                }

                SongTypeView -> {
                    val songHolder = holder as SongViewHolder
                    songHolder.title.text = list[position].title
//                    songHolder.desc.text = list[position]?.artistAlbumId?.get(position)?.title
                    if (list[position].songs_artist_ids != null){
                        val artists = list[position].songs_artist_ids?.let {
                            getSongArtistIdFromList( it )
                        }
                        if (!artists.isNullOrEmpty() && artists.isNotBlank()){
                            songHolder.desc.text=artists
                        }else{ songHolder.desc.visibility=View.GONE }
                    }else if (list[position].customDataV3 !=null && list[position].customDataV3.genres !=null){
//                        val genres= getGenersForAlbum(list[position].customDataV3.genres)
//                        if (genres.isNotEmpty() && genres.isNotBlank()){
//                            songHolder.desc.text=genres
//                        }else{ songHolder.desc.visibility=View.GONE }
                    }else{
                        songHolder.desc.visibility=View.GONE
                    }
                    if (list.get(position).customType == "ALBUMS") {
                        Glide.with(holder.itemView.context)
                            .load(list[position]?.images?.get(0)?.imageContent?.src)
                            .into(songHolder.imageView)
                    } else {
                        val image = list[position]?.imageContent?.src
                        if (!image.isNullOrEmpty()) {
                            Glide.with(holder.itemView.context)
                                .load(image)
                                .into(songHolder.imageView)
                        }
                    }
                    songHolder.itemLayout.setOnClickListener {
                        // Handle artist item click here\
                        if (list.get(position).customType == "ALBUMS") {
                            AppCommonMethod.launchArtistAndAlbum(
                                holder.itemView.context,
                                list[position]?.customType!!,
                                list[position]?.contentSlug!!,
                                list[position].id
                            )
                        } else {
                            val gson = Gson()
                            val json = gson.toJson(list)
                            val songList = gson.fromJson(json, Array<DataItem>::class.java).toList()
                            onSongItemClick!!.songItemClick(
                                songList,
                                songList[position],
                                if (songList[position].externalRefId == null) "" else songList[position].externalRefId,
                                AppCommonMethod.getImageContent(songList[0]),
                                AppCommonMethod.getSongsPosterImageUrl(songList[0]),
                                 false
                            )
                        }
                    }
                }

                VideoTypeView -> {
                    val videoHolder = holder as VideoSearchHolder
                    videoHolder.title.text = list[position].title
                    if (list[position].description.isNotEmpty() && list[position].description.isNotBlank()) {
                        videoHolder.desc.text = list[position].description
                    }else{
                        videoHolder.desc.visibility=View.GONE
                    }
                    videoHolder.itemView.setOnClickListener {
                        // Handle artist item click here
                        AppCommonMethod.launchDetailScreen(
                            holder.itemView.context,
                            "", list[position].id,
                            list[position].sku,
                            list[position].contentType,
                            list[position].title,
                            list[position].externalRefId,
                            list[position].posterURL,
                            0,
                            list[position].contentSlug,
                            list[position]
                        )
                    }
                    if (list[position].imageContent != null && list[position].imageContent.src != null && list[position].imageContent.src.isNotEmpty()) {
                        Glide.with(holder.itemView.context)
                            .load(list[position].imageContent.src)
                            .into(videoHolder.imageView)
                        list[position].posterURL=list[position].imageContent.src
                    } else {
                        videoHolder.imageTitle.text = list[position].title
                    }
                }
            }
        }
    }

    private fun getGenersForAlbum(genres1: MutableList<CommonCustomData>): CharSequence {
        var geners=""
        genres1.forEachIndexed { index, genre ->
            if (index == genres1.size.minus(1)) {
                geners += genre.title
            }else {
                geners += "${genre.title}, "
            }
        }
        return geners
    }
    private fun getSongArtistIdFromList(songsArtistIds: List<SongsAlbumIdItem>): CharSequence? {
        var artists=""
        songsArtistIds.forEachIndexed { index, artist ->
            if (index == songsArtistIds.size.minus(1)) {
                artists += artist.title
            }else {
                artists += "${artist.title}, "
            }
        }
        return artists
    }

    override fun getItemCount(): Int {
        return if (!filter.equals("All", ignoreCase = true) || moreClick) {
            list.size
        } else {
            if (list.size > 3) {
                3
            } else {
                list.size
            }
        }
    }

    fun setData(enveuVideoItemBeans: List<EnveuVideoItemBean>) {
        list=list.plus(enveuVideoItemBeans)
        list= list?.distinctBy { it.id }!!
        notifyDataSetChanged()
    }

    private inner class SongViewHolder(item: View) : RecyclerView.ViewHolder(item) {
        val title: TextView = item.findViewById(R.id.text_song_name)
        val imageView: ImageView = item.findViewById(R.id.song_image_Search)
        val desc: TextView = item.findViewById(R.id.description)
        val itemLayout : LinearLayout = item.findViewById(R.id.linear_layout_view)
    }

    private inner class ArtistViewHolder(item: View) : RecyclerView.ViewHolder(item) {
        val imageView: ImageView = item.findViewById(R.id.artist_image_Search)
        val title: TextView = item.findViewById(R.id.text_song_name)
        val desc: TextView = item.findViewById(R.id.description)
        val image_title: TextView = item.findViewById(R.id.image_title)
    }

    private inner class VideoSearchHolder(item: View) : RecyclerView.ViewHolder(item){
        val title: TextView = item.findViewById(R.id.text_song_name)
        val imageView: ImageView = item.findViewById(R.id.artist_image_Search)
        val imageTitle: TextView = item.findViewById(R.id.image_title)
        val desc: TextView = item.findViewById(R.id.description)
    }

}
