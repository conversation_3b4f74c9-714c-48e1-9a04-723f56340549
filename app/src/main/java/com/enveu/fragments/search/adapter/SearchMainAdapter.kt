package com.enveu.fragments.search.adapter

import android.annotation.SuppressLint
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.databinding.NewSearchLayoutBinding
import com.enveu.fragments.search.SearchFragment
import com.enveu.utils.PaginationScrollListener
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.RailInjectionHelper

class SearchMainAdapter(
    private var list: MutableList<RailCommonData>,
    private var filter: String,
    private val searchKeyword:String,
    private var railInjectionHelper: RailInjectionHelper,
    private var mediaMapingHash:HashMap<String,String>,
    private val context: SearchFragment,
    private var listnear: OnSongItemClick?,
    private val onDataPass: OnDataPass
) : RecyclerView.Adapter<SearchMainAdapter.SearchViewHolder>() {
    private var moreClick = false
    private var totalPages=0

    class SearchViewHolder(binding: NewSearchLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {
        val header = binding.header
        val more = binding.more
        var headerLayout = binding.headerLayout
        var recyclerView = binding.mainLayout
        var noresult = binding.noResultFound
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchViewHolder {
        return SearchViewHolder(
            NewSearchLayoutBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return list.size
    }

    @SuppressLint("CutPasteId")
    override fun onBindViewHolder(holder: SearchViewHolder, @SuppressLint("RecyclerView") position: Int) {
        val clickText=getTitle(list[position].enveuVideoItemBeans[0]).toString()
        if (list[position].enveuVideoItemBeans.isNotEmpty() && filter.equals("all", ignoreCase = true)) {
            holder.noresult.visibility = View.GONE
            holder.recyclerView.visibility = View.VISIBLE
            if (filter.equals("all", ignoreCase = true)) {
                if (!list[position].assetType.isNullOrEmpty() && list[position].enveuVideoItemBeans.isNotEmpty()) {
                    holder.headerLayout.visibility=View.VISIBLE
                    holder.header.text=getTitle(list[position].enveuVideoItemBeans[0])
                } else {
                    holder.headerLayout.visibility = View.GONE
                }
            } else {
                holder.headerLayout.visibility = View.GONE
            }
            val linierLayout=LinearLayoutManager(context.context)
            holder.itemView.findViewById<RecyclerView>(R.id.new_search_layout).layoutManager=linierLayout
            val searchAdapter = SearchAdapter(list[position].enveuVideoItemBeans, filter, moreClick = moreClick, listnear)
            holder.itemView.findViewById<RecyclerView>(R.id.new_search_layout).adapter =
                searchAdapter

            holder.more.setOnClickListener {
//                filter = getTitle(list[position].assetType).toString()
                onDataPass.onDataPass(getTitle(list[position].enveuVideoItemBeans[0]).toString())
//                this.list =
//                    list.filter { getTitle(it.assetType).toString() == getTitle(list[position].assetType).toString() } as MutableList<RailCommonData>
//                moreClick = true
//                notifyDataSetChanged()
            }
        } else if (list[position].enveuVideoItemBeans.isNotEmpty() && filter.equals(clickText,true)){
            totalPages = list[position].totalCount
            Log.d("totalPages", "onBindViewHolder: "+totalPages)
            holder.headerLayout.visibility = View.GONE
            val linierLayout=LinearLayoutManager(context.context)
            holder.itemView.findViewById<RecyclerView>(R.id.new_search_layout).layoutManager=linierLayout
            val searchAdapter = SearchAdapter(list[position].enveuVideoItemBeans, filter, moreClick = moreClick, listnear)
            holder.itemView.findViewById<RecyclerView>(R.id.new_search_layout).adapter =
                searchAdapter
            holder.itemView.findViewById<RecyclerView>(R.id.new_search_layout).isNestedScrollingEnabled=true

            callApiAndSetData(searchAdapter,list[position].assetType,2)
            holder.itemView.findViewById<RecyclerView>(R.id.new_search_layout).addOnScrollListener(object : PaginationScrollListener(linierLayout){
                override fun onLoadMore(page: Int) {
                    if (totalPages > page) {
                        callApiAndSetData(searchAdapter,list[position].assetType,page)
                    }
                }
            })

            holder.more.visibility=View.GONE
        }else {
            holder.headerLayout.visibility = View.GONE
            if (!filter.equals("all", ignoreCase = true)){
                holder.recyclerView.visibility = View.GONE
                holder.noresult.visibility = View.VISIBLE
            }
        }
    }

    private fun callApiAndSetData(searchAdapter: SearchAdapter, assetType: String, page: Int) {
        railInjectionHelper!!.getSearchForMusicContents(searchKeyword,20,page,assetType)?.
        observe(context) { data: List<RailCommonData> ->
            if (data != null){
                searchAdapter.setData(data[0].enveuVideoItemBeans)
            }
        }
    }

    private fun getTitle(dataSearch: EnveuVideoItemBean?): CharSequence? {
        var titel = if (dataSearch?.assetType.equals("AUDIO")){
            context?.getString(R.string.songs)
        }else if (dataSearch?.assetType.equals("CUSTOM")){
            if (dataSearch?.customContent != null){
                if (dataSearch.customContent.customType.equals(AppConstants.ALBUM)){
                    context?.getString(R.string.albums)
                } else if (dataSearch.customContent.customType.equals(AppConstants.ENT_SERIES)){
                    context?.getString(R.string.series)
                } else {
                    dataSearch.customContent.customType.toString()
                }
            } else {
                "Custom"
            }
        }else if (dataSearch?.assetType.equals("PERSON")){
            context?.getString(R.string.artists)
        }else if (dataSearch?.assetType.equals("VIDEO")){
            if (dataSearch?.videoDetails != null){
                if (dataSearch.videoDetails?.videoType.equals(AppConstants.Movies)){
                    context.getString(R.string.movie)
                }else if (dataSearch.videoDetails?.videoType.equals(AppConstants.EPISODES)){
                    context.getString(R.string.episodes)
                }else{
                    dataSearch.videoDetails?.videoType.toString()
                }
            } else {
                "Video"
            }
        }else{ "" }
        return titel
    }

    @SuppressLint("NotifyDataSetChanged")
    fun sortList(filter: String, list: MutableList<RailCommonData>) {
        if (filter.equals("ALL", ignoreCase = true)) {
            this.moreClick = false
            this.list = list
            this.filter = filter
        } else {
            this.filter = filter
            this.list = list.filter { filter.equals(getTitle(list[0].enveuVideoItemBeans[0]).toString(),true) }.toMutableList()
        }
        notifyDataSetChanged()
    }

    interface OnDataPass {
        fun onDataPass(mediaType: String)
        fun callPaginatedAssetType(assetType: String,page: Int):RailCommonData
    }
}

