package com.enveu.fragments.series.ui

import android.os.Bundle
import com.enveu.beanModel.TabsBaseFragment
import com.enveu.fragments.series.viewModel.SeriesFragmentViewModel

class SeriesFragment(private val tabID : String) : TabsBaseFragment<SeriesFragmentViewModel?>() {
    constructor() : this("0")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setViewModel(SeriesFragmentViewModel::class.java, tabID)
    }
}