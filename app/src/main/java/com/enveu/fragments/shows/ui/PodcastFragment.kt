package com.enveu.fragments.shows.ui

import android.os.Bundle
import com.enveu.beanModel.TabsBaseFragment
import com.enveu.fragments.shows.viewModel.PodcastFragmentViewModel

class PodcastFragment(private val tabID : String) : TabsBaseFragment<PodcastFragmentViewModel?>() {
    constructor() : this("0")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setViewModel(PodcastFragmentViewModel::class.java, tabID)
    }
}