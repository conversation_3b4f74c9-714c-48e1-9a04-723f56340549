package com.enveu.fragments.singles

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.enveu.R
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.mainPLayer.MainPlayerActivity
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.callbacks.player.callback.OnAudioItemClickInteraction
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.callbacks.player.callback.SongClick
import com.enveu.databinding.FragmentAlbumBinding
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.player.ui.AudioInteractionFragment
import com.enveu.fragments.singles.adapter.EpisodeItemClick
import com.enveu.fragments.singles.adapter.SinglesAdapter
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.BottomDialogFragment
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.gson.Gson


class SinglesDetailFragment : Fragment(), SongClick, CommonDialogFragment.EditDialogListener,
    EpisodeItemClick,
    OnAudioItemClickInteraction {

    private lateinit var binding: FragmentAlbumBinding
    private var adapter: SinglesAdapter? = null
    private var contentSlug = ""
    private var mediaType = ""
    private var assetID = ""
    private var videoDetails: EnveuVideoItemBean? = null
    private var page = 0
    private var pageSize = 4
    private var railCommonDataList: MutableList<EnveuVideoItemBean> = ArrayList()
    private var token: String? = null
    private var preference: KsPreferenceKeys? = null
    private var onSongItemClick: OnSongItemClick? = null
    private var isUserNotEntitle = false
    private var playerListener: MainPlayerActivity.PlayerListener? = null
    private var resEntitle: ResponseEntitle? = null
    private val stringsHelper by lazy { StringsHelper }
    private var railCommonData: RailCommonData? = null
    private var featureFlag : FeatureFlagModel?= null
    private var thumbnailImageUrl :String?= null



    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (activity != null && activity is HomeActivity)
            (activity as HomeActivity).toolFrame(1)
            (activity as HomeActivity).detailFrameVisibility()
        preference = KsPreferenceKeys.getInstance()
        token = preference?.appPrefAccessToken
        featureFlag = AppConfigMethod.parseFeatureFlagList()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        return FragmentAlbumBinding.inflate(LayoutInflater.from(requireContext()), container, false)
            .run {
                binding = this
                root
            }
        parseColor()
    }


    private fun setClicks() {
        binding.goBack.setOnClickListener {
            activity?.onBackPressed()
        }
    }

    var railInjectionHelper: RailInjectionHelper? = null


    private val assetDetailsByID: Unit
        get() {
            railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
            railInjectionHelper!!.getAssetDetailsV2(assetID, requireContext())
                .observe(requireActivity()) { assetResponse: ResponseModel<*>? ->
                    if (assetResponse != null) {
                        val gson = Gson()
                        val json = gson.toJson(assetResponse.baseCategory)
                        Log.w("getAssetDetailsV2-->>>>>", json)

                        if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {

                        } else if (assetResponse.status.equals(
                                APIStatus.SUCCESS.name,
                                ignoreCase = true
                            )
                        ) {
                            parseDetail(assetResponse)
                        } else if (assetResponse.status.equals(
                                APIStatus.ERROR.name,
                                ignoreCase = true
                            )
                        ) {
                            if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {
                                commonDialog(
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                        getString(R.string.popup_error)
                                    ),
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                        getString(R.string.popup_something_went_wrong)
                                    ),
                                    stringsHelper.stringParse(
                                        stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                        getString(R.string.popup_continue)
                                    )
                                )
                            }
                        } else if (assetResponse.status.equals(APIStatus.FAILURE.name, ignoreCase = true)) {
                            commonDialog(
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                    getString(R.string.popup_error)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                    getString(R.string.popup_something_went_wrong)
                                ),
                                stringsHelper.stringParse(
                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                    getString(R.string.popup_continue)
                                )
                            )
                        }
                    }
                }
        }





    private val assetDetails: Unit
        get() {
            railInjectionHelper?.getAssetDetailsbySlug(contentSlug)
                ?.observe(requireActivity()) { assetResponse: ResponseModel<*>? ->
                    if (assetResponse != null) {
                        if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {
                        } else if (assetResponse.status.equals(
                                APIStatus.SUCCESS.name, ignoreCase = true
                            )
                        ) {
                            parseDetail(assetResponse)
                        } else if (assetResponse.status.equals(
                                APIStatus.ERROR.name, ignoreCase = true
                            )
                        ) {
                            if (assetResponse.errorModel != null && assetResponse.errorModel.errorCode != 0) {

                            }
                        } else if (assetResponse.status.equals(
                                APIStatus.FAILURE.name, ignoreCase = true
                            )
                        ) {

                        }
                    }
                }
        }

    private fun parseDetail(assetResponse: ResponseModel<*>) {
        stopShimmer()
        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
        if (enveuCommonResponse.enveuVideoItemBeans.size > 0) {
            videoDetails = enveuCommonResponse.enveuVideoItemBeans[0]

            AnalyticsUtils.trackScreenView(context,AppConstants.CONTENT_DETAIL + " - " + videoDetails?.title )
        }
        setUiData()
        videoDetails?.id?.let {
            getSongsForSingles(it.toString())
        }

    }

    private fun getSongsForSingles(singlesId: String) {
        val activity = requireActivity()
        railInjectionHelper?.getRelatedContent(0,50,videoDetails?.assetType,singlesId.toInt())
            ?.observe(activity) { assetResponse: ResponseModel<*>? ->
                if (assetResponse != null) {
                    if (assetResponse.status.equals(APIStatus.START.name, ignoreCase = true)) {

                    } else if (assetResponse.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                        val enveuCommonResponse = assetResponse.baseCategory as RailCommonData
                        railCommonData = enveuCommonResponse
                        railCommonDataList = enveuCommonResponse.enveuVideoItemBeans
                        setSongAdapter()
                    } else if (assetResponse.status.equals(
                            APIStatus.ERROR.name, ignoreCase = true
                        )
                    ) {
                    }
                }
            }
    }

    private fun callShimmer() {
        binding.seriesShimmer.visibility = View.VISIBLE
        binding.mShimmer.seriesShimmerScroll1.isEnabled = false
        binding.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding.mShimmer.seriesShimmerScroll2.isEnabled = false
        binding.mShimmer.sfShimmer1.startShimmer()
        binding.mShimmer.sfShimmer2.startShimmer()
        binding.mShimmer.sfShimmer3.startShimmer()
        binding.mShimmer.flBackIconImage.bringToFront()
    }

    private fun stopShimmer() {
        binding.seriesShimmer.visibility = View.GONE
        binding.mShimmer.sfShimmer1.startShimmer()
        binding.mShimmer.sfShimmer2.startShimmer()
        binding.mShimmer.sfShimmer3.startShimmer()
    }

    private fun setSongAdapter() {
        if (railCommonDataList.size>0) {
            binding.headerTitleLayout.visibility = View.VISIBLE
        } else {
            binding.headerTitleLayout.visibility = View.GONE
        }
        adapter = featureFlag?.let { SinglesAdapter( railCommonDataList, this@SinglesDetailFragment,this@SinglesDetailFragment, featureFlag = it) }
        binding.myRecycleView.adapter = adapter
    }

    private fun setUiData() {
        binding.contentTitle.text = videoDetails?.title
        binding.description.visibility = View.GONE
        thumbnailImageUrl =   videoDetails?.imageContent?.src
        videoDetails?.imageContent?.src?.let {
            ImageHelper.getInstance(requireActivity()).loadListImage(binding.imgscr, it)


        }
        binding.genre.text = videoDetails?.singlesArtistItem?.title
        binding.metaLayout.background = AppCommonMethod.setGradientBackgroundColor(
            Color.parseColor(
                AppCommonMethod.getDominantColor(videoDetails?.imageContent)
            ), Color.parseColor("#00000000"), "TOP_TO_BOTTOM"
        )

        setUserInteractionFragment(videoDetails!!.id)

    }

    private var audioInteractionFragment: AudioInteractionFragment? = null
    private fun setUserInteractionFragment(id: Int) {
        val transaction = childFragmentManager?.beginTransaction()
        val args = Bundle()
        args.putInt(AppConstants.BUNDLE_ASSET_ID, id)
        args.putString(AppConstants.BUNDLE_MEDIA_TYPE, mediaType)
        args.putSerializable(AppConstants.BUNDLE_SERIES_DETAIL, videoDetails)
        args.putString(AppConstants.BUNDLE_CONTENT_SLUG, contentSlug)
        args.putString(AppConstants.THUMBNAIL_IMG,thumbnailImageUrl)
        audioInteractionFragment = AudioInteractionFragment()
        audioInteractionFragment!!.arguments = args
        audioInteractionFragment!!.passInstance(this)
        transaction?.replace(R.id.fragment_audio_interaction, audioInteractionFragment!!)
        transaction?.commit()
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        callShimmer()
        setViewModel()
        setClicks()
        getIntentData()


        if (contentSlug.isNotEmpty()) {
            Log.d("branchRedirections", "AlbumFragmnet CintentSlug: $mediaType--$assetID--$contentSlug")

            assetDetails
        } else if (assetID != "") {
            Log.d("branchRedirections", "AlbumFragmnet NotCintentSlug: $mediaType--$assetID--$contentSlug")
            assetDetailsByID
        } else {
            commonDialog(
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                    getString(R.string.popup_error)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                    getString(R.string.popup_this_content_not_available)
                ),
                stringsHelper.stringParse(
                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                    getString(R.string.popup_continue)
                )
            )
        }

    }

    private fun getIntentData() {
        contentSlug = arguments?.getString(AppConstants.BUNDLE_CONTENT_SLUG) ?: ""
        mediaType = arguments?.getString(AppConstants.BUNDLE_ASSET_TYPE) ?: ""
        assetID = arguments?.getString(AppConstants.BUNDLE_ASSET_ID) ?: ""


    }

    private fun setViewModel() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]

    }

    override fun onPlayClick(playQueueItems:Boolean?) {

    }

    override fun onShuffle() {

    }

    override fun onDetach() {
        super.onDetach()
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).toolFrame(0)
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).detailFrameVisibility()
    }

    override fun onSongClick(song: DataItem) {

    }

    override fun onItemClick(enveuVideoItemBean: EnveuVideoItemBean?, isPremium: Boolean, position: Int) {
        AppCommonMethod.redirectionLogic(
            requireActivity(),
            railCommonData!!,
            position,"","",""
        )
    }

    override fun onThreeDotClick(song: DataItem) {
        val bottomSheetDialog = BottomDialogFragment.getInstance(song,"")
        bottomSheetDialog.show(activity?.supportFragmentManager!!, "Bottom Sheet Dialog Fragment")
    }

    override fun onDeleteItemClick(song: DataItem) {

    }

    override fun onActionBtnClicked() {

    }

    override fun onCancelBtnClicked() {

    }
    private fun parseColor() {
        binding.stringData = stringsHelper
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        onSongItemClick = context as OnSongItemClick
        playerListener = context as MainPlayerActivity.PlayerListener
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).toolFrame(1)
        if (activity != null && activity is HomeActivity) (activity as HomeActivity).detailFrameVisibility()
    }


    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = requireActivity().supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }
}



