package com.enveu.fragments.singles.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.player.callback.SongClick
import com.enveu.databinding.SongsLayoutItemBinding
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.helpers.ImageHelper

class SinglesAdapter() : RecyclerView.Adapter<SongListViewHolder>() {
    private var singleList: MutableList<EnveuVideoItemBean>? = null
    private var onSongClick: SongClick? = null
    private var listner: EpisodeItemClick? = null
    private var featureFlag : FeatureFlagModel?= null
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SongListViewHolder {
        return SongListViewHolder(SongsLayoutItemBinding.inflate(LayoutInflater.from(parent.context),parent,false))
    }

    constructor(singleList: MutableList<EnveuVideoItemBean>, onSongClick: SongClick, listner: EpisodeItemClick,featureFlag : FeatureFlagModel) : this() {
        this.singleList = singleList
        this.onSongClick = onSongClick
        this.listner = listner
        this.featureFlag = featureFlag
    }

    override fun getItemCount(): Int {
        return singleList!!.size
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addItems(newItems: MutableList<EnveuVideoItemBean>?) {
        val startPos = singleList?.size
        singleList?.addAll(newItems!!)
        notifyItemRangeInserted(startPos!!, newItems?.size!!)
        notifyDataSetChanged()
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: SongListViewHolder, position: Int) {
        holder.binding.textSongName.text = singleList!![position].title
     //   holder.binding.textRowNumber.text = singleList!![position].trackNumber
        holder.binding.textArtistName.text = singleList!![position].title
        if (singleList!![position].albumArtistId.get(position).images.isNotEmpty()) {
            val posterUrl: String = singleList!![position].albumArtistId.get(position).images.get(0).src
            ImageHelper.getInstance(holder.binding.albumImage.context)
                .loadListSQRImageForAlbumList(holder.binding.albumImage, posterUrl)
        }
//        if (singleList!![position].playCount != 0 && featureFlag?.featureFlag?.PLAY_COUNT == true) {
//            holder.binding.textArtistName.text = AppCommonMethod.formatViewCount(singleList!![position].playCount)+ holder.binding.textArtistName.resources.getString(
//                R.string.play)+". " + singleList!![position]?.title
//        } else {
//            holder.binding.textArtistName.text = singleList!![position].title
//        }

        holder.binding.linearLayoutView.setOnClickListener {
            listner?.onItemClick(
                singleList!![position],
                singleList!![position].isPremium,
                position
            )
        }

       /* holder.binding.dot.setOnClickListener {
            singleList!![position].title?.get(position)?.let {
                onSongClick?.onThreeDotClick(it)
            }
        }*/

    }
}

interface EpisodeItemClick {
    fun onItemClick(assetId: EnveuVideoItemBean?, isPremium: Boolean, position: Int)
}

class SongListViewHolder(val binding: SongsLayoutItemBinding) :
    RecyclerView.ViewHolder(binding.root) {

}
