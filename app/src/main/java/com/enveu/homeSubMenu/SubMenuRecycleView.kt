package com.enveu.homeSubMenu

import android.annotation.SuppressLint
import android.graphics.Typeface
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.RecyclerView
import com.enveu.OttApplication.Companion.context
import com.enveu.R
import com.enveu.databinding.SubMenuLayoutBinding
import com.enveu.menuManager.model.MenuManagerModel.Data.OrderedMenuItem

class SubMenuRecycleView(private val list: List<OrderedMenuItem>, private val onClick: (OrderedMenuItem) -> Unit) : RecyclerView.Adapter<SubMenuRecycleView.SubMenuViewHolder>() {
    private var selectedPosition = 0

    inner class SubMenuViewHolder(val binding: SubMenuLayoutBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SubMenuViewHolder {
        return SubMenuViewHolder(binding = SubMenuLayoutBinding.inflate(LayoutInflater.from(parent.context),parent,false))
    }

    override fun getItemCount(): Int {
     return list.size
    }

    @SuppressLint("SuspiciousIndentation")
    override fun onBindViewHolder(holder: SubMenuViewHolder, @SuppressLint("RecyclerView") position: Int) {
      holder.binding.menu.text = list[position].menuItem?.displayName
        val typeface = if (position == selectedPosition) {
            ResourcesCompat.getFont(context, R.font.poppins_bold)
        } else {
            ResourcesCompat.getFont(context, R.font.poppins_regular)
        }
        holder.binding.menu.typeface = typeface

        holder.binding.menu.setOnClickListener {
            val previousPosition = selectedPosition
            selectedPosition = position
            notifyItemChanged(previousPosition)
            notifyItemChanged(selectedPosition)
            list[position]?.let { selectedItem -> onClick(selectedItem) }
            Log.d("SubMenuAdapter", "Clicked item: ${list[position].menuItem?.description}")
        }
    }
}