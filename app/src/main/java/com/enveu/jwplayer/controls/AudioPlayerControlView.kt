package com.enveu.jwplayer.controls


import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.SeekBar
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.databinding.AudioPlayerControlViewBinding
import com.enveu.jwplayer.listener.AudioControlClickListener
import com.enveu.jwplayer.model.PlayBackSpeedItem
import com.enveu.jwplayer.playBackSpeedAdapter.PlayBackSpeedAdapter
import com.enveu.player.extension.percent
import com.enveu.player.extension.percentOf
import com.enveu.player.utils.ClickHandler
import com.enveu.player.utils.TimeUtils
import com.enveu.utils.commonMethods.AppCommonMethod
import java.util.Random

class AudioPlayerControlView : FrameLayout {

    private var binding: AudioPlayerControlViewBinding
    private var dragging: Boolean = false
    private var controlClickListener: AudioControlClickListener? = null
    private var showNext: Boolean = false
    private var showPrevious: Boolean = false
    private var videoDuration: Long = 0
    private var visibilityHandler = Handler(Looper.getMainLooper())

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : this(
        context,
        attrs,
        defStyleAttr,
        0
    )

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes) {
        binding = AudioPlayerControlViewBinding.inflate(LayoutInflater.from(context), this, true)
    }


    fun attachListener(controlClickListener: AudioControlClickListener) {
        this.controlClickListener = controlClickListener

        binding.root.setOnClickListener {
            if (ClickHandler.allowClick()) {
                controlClickListener.onOutsideClicked(binding.root)
            }
        }

        binding.ivEpBack.setOnClickListener {
            controlClickListener.onBackClicked()
        }
/*
        binding.ivEpSetting.setOnClickListener {
            controlClickListener.onSettingClicked()
        }*/
        binding.ivEpRewind.setOnClickListener {
            controlClickListener.onRewindClick()
        }
        binding.ivEpPlayPause.setOnClickListener {
            controlClickListener.onPlayPauseClicked()
        }
        binding.ivEpFastFwd.setOnClickListener {
            controlClickListener.onFastForwardClick()
        }
        binding.ivEpFullscreen.setOnClickListener {
            controlClickListener.onFullscreenClicked()
        }
        binding.tvPreviousEpisode.setOnClickListener {
            controlClickListener.onPreviousClicked()
        }

        binding.tvNextEpisode.setOnClickListener {
            controlClickListener.onNextClicked()
        }

        binding.ivPlayBackSpeed.setOnClickListener {
            controlClickListener.onPlayBackSpeedClicked()
        }


        binding.epTimeBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                binding.epTimeBar.progress = progress
                binding.tvEpPosition.text = TimeUtils.formatDuration(progress.toLong())
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                dragging = true
                controlClickListener.onProgressDragStart()
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                dragging = false
                controlClickListener.onProgressDragStop(seekBar.progress.percentOf(videoDuration))
            }
        })
    }


    fun updateDuration(duration: Double) {
        videoDuration = duration.toLong()
        binding.epTimeBar.max = 100
        binding.tvEpDuration.text = TimeUtils.formatDurationJw(duration.toLong())
    }

    fun updateProgress(progress: Double) {
        binding.epTimeBar.progress = progress.toLong().percent(videoDuration)
        binding.tvEpPosition.text = TimeUtils.formatDurationJw(progress.toLong())
    }


    fun setPlayBackSpeed(speedTracks: MutableList<PlayBackSpeedItem>, selectedPlayBackSpeed: Double) {
        updateRecyclerViewVisibility()
        visibilityHandler.removeCallbacksAndMessages(null)
        val adapter = PlayBackSpeedAdapter(speedTracks, playBackSpeedItemClick, selectedPlayBackSpeed)
        binding.rvQuality.adapter = adapter
    }


    private val playBackSpeedItemClick = object : PlayBackSpeedAdapter.PlayBackSpeeditem {
        override fun playBackSpeedItemClick(playBackSpeed: Int, userSelectedPlayBack: Double) {
            if (controlClickListener != null) {
                updateRecyclerViewVisibility()
                controlClickListener?.onPlayBackSpeedSelected(userSelectedPlayBack)
            }
        }
    }

    private fun updateRecyclerViewVisibility() {
        if (binding.rvQuality.visibility == View.VISIBLE) {
            binding.rvQuality.visibility = View.GONE
        } else {
            binding.rvQuality.visibility = View.VISIBLE
        }
    }

     private fun togglePlayPauseUi() {
        val isPlaying = binding.ivEpPlayPause.tag
         binding.isPlaying=true
        if (isPlaying is Boolean) {
            val iconResId = if (!isPlaying) {
                R.drawable.ic_ep_pause
            } else {
                R.drawable.ic_ep_pause
            }
            binding.ivEpPlayPause.setImageResource(iconResId)
            binding.ivEpPlayPause.tag = !isPlaying
        }
    }


    fun updatePlayPauseIcon(isPlaying: Boolean) {
        binding.ivEpPlayPause.tag = isPlaying
        togglePlayPauseUi()
    }


    fun toggleControlVisibility() {
        visibilityHandler.removeCallbacksAndMessages(null)
        val visibility = if (binding.epControlsBackground.visibility == View.VISIBLE) {
            View.VISIBLE
        } else {
            View.VISIBLE
        }
        updateControlVisibility(visibility)
       // addHideHandler()
    }



    private fun updateControlVisibility(visibility: Int) {
        controlsVisibilityForAudio(visibility)
    }


    private fun controlsVisibilityForAudio(visibility: Int,) {
        binding.epControlsBackground.visibility = visibility
        binding.ivEpBack.visibility = visibility
        binding.tvTittle.visibility = visibility
        binding.ivEpRewind.visibility = visibility
        binding.ivEpPlayPause.visibility = visibility
        binding.ivEpFastFwd.visibility = visibility
        binding.tvEpPosition.visibility = visibility
        binding.epTimeBar.visibility = visibility
        binding.tvEpDuration.visibility = visibility


        if (visibility == View.VISIBLE) {
            binding.tvNextEpisode.visibility = if (showNext) View.VISIBLE else View.INVISIBLE
            binding.tvPreviousEpisode.visibility = if (showPrevious) View.VISIBLE else View.INVISIBLE
        } else {
            binding.tvNextEpisode.visibility = View.INVISIBLE
            binding.tvPreviousEpisode.visibility = View.INVISIBLE
        }
    }

    fun shouldShowNext(next: Boolean) {
        this.showNext = next

    }

    fun shouldShowPrevious(show: Boolean) {
        this.showPrevious = show
    }


    fun updateNetworkState(isConnected: Boolean) {
        if (isConnected) {
            binding.tvInternetConnection.visibility = View.INVISIBLE
        } else {
            binding.tvInternetConnection.setText(R.string.no_internet_connection)
            binding.tvInternetConnection.visibility = View.VISIBLE
        }
    }


    fun setPodcastUi(tittle: String) {
        binding.tvTittle.text = tittle
        try {
            binding.podCastImage.bringToFront()
            binding.podCastImage.visibility = View.VISIBLE
            Glide.with(this)
                .load(R.drawable.podcast)
                .into(binding.podCastImage)

            var imageArray: IntArray? = null
            imageArray = AppCommonMethod.getImageArray(context)
            // Generate a random index to select an image from the array
            val randomIndex: Int = Random().nextInt(imageArray!!.size)
            // Set the selected image to the ImageView
            binding.randomImage.setImageResource(imageArray[randomIndex])
        } catch (e:Exception) {
            Log.d("Exception", "setPodcastUi: $e")
        }

    }

}