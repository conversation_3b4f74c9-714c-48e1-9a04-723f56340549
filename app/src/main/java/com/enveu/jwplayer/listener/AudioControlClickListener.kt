package com.enveu.jwplayer.listener

import android.view.View

abstract class AudioControlClickListener {

    open fun getOrientation(){}
    open fun onOutsideClicked(root: View) {}
    open fun onPlayPauseClicked() {}
    open fun onFullscreenClicked() {}
    open fun onBackClicked() {}
    open fun onSettingClicked() {}
    open fun onRewindClick() {}
    open fun onFastForwardClick() {}
    open fun onProgressDragStart() {}
    open fun onProgressDragStop(position: Long) {}
    open fun onPreviousClicked() {}
    open fun onMoreClicked() {}
    open fun onNextClicked() {}
    open fun onSkipIntroClicked() {}
    open fun onVideoQualitySelected(trackName: Int, selectedTrack: String) {}
    open fun onSettingSelected(selectedSetting: String) {}
    open fun onCaptionSelected(trackIndex: Int) {}
    open fun onAudioSelected(trackIndex: Int) {}
    open fun onPlayBackSpeedSelected(userSelectedSpeed: Double) {}

    open fun onPlayBackSpeedClicked() {}
}