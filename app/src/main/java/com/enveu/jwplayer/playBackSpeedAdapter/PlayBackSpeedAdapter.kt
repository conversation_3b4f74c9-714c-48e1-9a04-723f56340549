package com.enveu.jwplayer.playBackSpeedAdapter


import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.enveu.R
import com.enveu.jwplayer.model.PlayBackSpeedItem
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys

class PlayBackSpeedAdapter(
    private val mList: MutableList<PlayBackSpeedItem>,
    playBackSpeedItemClick: PlayBackSpeeditem,
    selectedPlayBackSpeed: Double
) : RecyclerView.Adapter<PlayBackSpeedAdapter.ViewHolder>() {
    var playBackSpeedItemClick = playBackSpeedItemClick
    var selectedPlayBackSpeed = selectedPlayBackSpeed


    // create new views
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        // inflates the card_view_design view
        // that is used to hold list item
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.play_back_items, parent, false)

        return ViewHolder(view)
    }

    // binds the list items to a view
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        holder.titleText.text = mList[position].playBackSpeed.toString() + "x"

        if (mList[position].playBackSpeed.toString() == KsPreferenceKeys.getInstance().playBackSpeed) {
            holder.titleText.setBackgroundResource(R.drawable.ic_rectangle_background_selected_blue)
            holder.titleText.setTextColor(holder.titleText.context.resources.getColor(com.moengage.inbox.ui.R.color.moe_white));
        } else {
            holder.titleText.setBackgroundResource(R.drawable.ic_rectangle_background_selected)
            holder.titleText.setTextColor(holder.titleText.context.resources.getColor(R.color.buy_now_pay_now_btn_text_color));
        }

        holder.titleText.setOnClickListener {
            mList[position].id?.let { it1 ->
                playBackSpeedItemClick.playBackSpeedItemClick(it1, mList[position].playBackSpeed)
            }
            KsPreferenceKeys.getInstance().playBackSpeed = mList[position].playBackSpeed.toString()
            holder.titleText.setBackgroundResource(R.drawable.ic_rectangle_background_selected_blue)
            holder.titleText.setTextColor(holder.titleText.context.resources.getColor(com.moengage.inbox.ui.R.color.moe_white));
        }
    }

    // return the number of the items in the list
    override fun getItemCount(): Int {
        return mList.size
    }

    // Holds the views for adding it to image and text
    class ViewHolder(ItemView: View) : RecyclerView.ViewHolder(ItemView) {
        var titleText: TextView = itemView.findViewById(R.id.title_text)
    }

    interface PlayBackSpeeditem {
        fun playBackSpeedItemClick(playBackSpeed: Int, userSelectedPlayBack: Double)
    }
}
