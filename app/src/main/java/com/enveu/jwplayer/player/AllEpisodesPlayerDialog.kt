package com.enveu.jwplayer.player

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.databinding.ItemMoreEpisodesLayoutBinding
import com.enveu.jwplayer.player.adapter.MoreEpisodesAdapter
import com.enveu.networking.apistatus.APIStatus
import com.enveu.utils.helpers.RailInjectionHelper
import com.enveu.utils.hide
import com.enveu.utils.show

class AllEpisodesPlayerDialog : DialogFragment() {
        private lateinit var railInjectionHelper: RailInjectionHelper
        private var allEpisodes:ArrayList<EnveuVideoItemBean>? = ArrayList()
        private lateinit var binding: ItemMoreEpisodesLayoutBinding
        private var singleEpisodesItems:EnveuVideoItemBean? = null
        private var episodesItemClickListener: EpisodesItemClickListener? = null
        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            setStyle(STYLE_NO_FRAME, com.enveu.player.R.style.TransparentFragment)

        }
        override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
            val dialog = super.onCreateDialog(savedInstanceState)
            dialog.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            dialog.window?.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            return dialog
        }

        override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
        ): View {
            return ItemMoreEpisodesLayoutBinding.inflate(inflater).run { binding = this
                root }
        }

        override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
            super.onViewCreated(view, savedInstanceState)
            railInjectionHelper = ViewModelProvider(requireActivity())[RailInjectionHelper::class.java]
            getAllSeriesEpisodes()
            binding.dismic.setOnClickListener {
                dismiss()
            }
        }

    fun getSingleEpisodesItems(singleEpisodesItems: EnveuVideoItemBean?, episodesItemClickListener: EpisodesItemClickListener?) {
        this.singleEpisodesItems = singleEpisodesItems
        this.episodesItemClickListener = episodesItemClickListener
    }

    private fun getAllSeriesEpisodes(){
        binding.progressBar.show()
        binding.progressBar.bringToFront()
        val episodesSeasons = singleEpisodesItems?.seriesCustomData?.episode_season_id
        binding.tvTitle.text = episodesSeasons?.title
        if (episodesSeasons != null) {
            railInjectionHelper.getEpisodeFromSeason(episodesSeasons.id, 0, 10).observe(viewLifecycleOwner) { response ->
                binding.progressBar.hide()
                if (response != null) {
                    if (response.status.equals(APIStatus.SUCCESS.name, ignoreCase = true)) {
                        if (response.baseCategory != null) {
                            val enveuCommonResponse = response.baseCategory as RailCommonData
                            allEpisodes = enveuCommonResponse.enveuVideoItemBeans as ArrayList<EnveuVideoItemBean>?
                            setEpisodeAdapter()
                        }
                    }
                }
            }
        }
    }

    private fun setEpisodeAdapter() {
        val moreEpisodesAdapter = MoreEpisodesAdapter(allEpisodes, episodesItemClickListener)
        binding.moreEpisodesRecycler.adapter = moreEpisodesAdapter
        binding.moreEpisodesRecycler.layoutManager = LinearLayoutManager(requireActivity(), LinearLayoutManager.HORIZONTAL, false)
    }
}
