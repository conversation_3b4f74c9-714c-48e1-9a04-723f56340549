package com.enveu.jwplayer.player

import com.google.gson.annotations.SerializedName

data class ChromeCastUrlResponse(

	@field:SerializedName("sources")
	val sources: List<SourcesItem?>? = null,

	@field:SerializedName("reference_id")
	val referenceId: String? = null,

	@field:SerializedName("custom_fields")
	val customFields: CustomFields? = null,

	@field:SerializedName("offline_enabled")
	val offlineEnabled: Boolean? = null,

	@field:SerializedName("link")
	val link: Any? = null,

	@field:SerializedName("description")
	val description: Any? = null,

	@field:SerializedName("created_at")
	val createdAt: String? = null,

	@field:SerializedName("long_description")
	val longDescription: Any? = null,

	@field:SerializedName("tags")
	val tags: List<Any?>? = null,

	@field:SerializedName("cue_points")
	val cuePoints: List<Any?>? = null,

	@field:SerializedName("duration")
	val duration: Int? = null,

	@field:SerializedName("economics")
	val economics: String? = null,

	@field:SerializedName("account_id")
	val accountId: String? = null,

	@field:SerializedName("updated_at")
	val updatedAt: String? = null,

	@field:SerializedName("text_tracks")
	val textTracks: List<TextTracksItem?>? = null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("id")
	val id: String? = null,

	@field:SerializedName("published_at")
	val publishedAt: String? = null,

	@field:SerializedName("ad_keys")
	val adKeys: Any? = null
)

data class CustomFields(
	val any: Any? = null
)

data class TextTracksItem(

	@field:SerializedName("default")
	val jsonMemberDefault: Boolean? = null,

	@field:SerializedName("account_id")
	val accountId: String? = null,

	@field:SerializedName("sources")
	val sources: List<SourcesItem?>? = null,

	@field:SerializedName("src")
	val src: String? = null,

	@field:SerializedName("mime_type")
	val mimeType: String? = null,

	@field:SerializedName("kind")
	val kind: String? = null,

	@field:SerializedName("srclang")
	val srclang: String? = null,

	@field:SerializedName("width")
	val width: Int? = null,

	@field:SerializedName("id")
	val id: String? = null,

	@field:SerializedName("label")
	val label: String? = null,

	@field:SerializedName("asset_id")
	val assetId: Any? = null,

	@field:SerializedName("height")
	val height: Int? = null,

	@field:SerializedName("in_band_metadata_track_dispatch_type")
	val inBandMetadataTrackDispatchType: String? = null
)

data class SourcesItem(

	@field:SerializedName("src")
	val src: String? = null,

	@field:SerializedName("codecs")
	val codecs: String? = null,

	@field:SerializedName("profiles")
	val profiles: String? = null,

	@field:SerializedName("type")
	val type: String? = null,

	@field:SerializedName("ext_x_version")
	val extXVersion: String? = null
)
