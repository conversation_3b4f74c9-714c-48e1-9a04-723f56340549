package com.enveu.jwplayer.player

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.enveu.databinding.FragmentLiveChannelBinding
import com.enveu.epg.models.EPGItem
import com.enveu.jwplayer.player.adapter.LiveChannelPlayerAdapter
import com.enveu.utils.Constants
import com.enveu.utils.getImageUrl
import com.enveu.utils.setImageWithGlide


class LiveChannelDialog : DialogFragment() {
    private lateinit var binding: FragmentLiveChannelBinding
    private var epgScheduleItems: EPGItem? = null
    private var epgScheduleItemClickListener: EpgScheduleItemClickListener? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.enveu.player.R.style.TransparentFragment)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        dialog.window?.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return FragmentLiveChannelBinding.inflate(inflater).run { binding = this
            root }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.dismiss.setOnClickListener {
            dismiss()
        }
        setEpisodeAdapter()
    }

    fun getScheduleItems(epgScheduleItems: EPGItem?, epgScheduleItemClickListener: EpgScheduleItemClickListener?) {
        this.epgScheduleItems = epgScheduleItems
        this.epgScheduleItemClickListener = epgScheduleItemClickListener
    }

    private fun setEpisodeAdapter() {
        val scheduleItems = epgScheduleItems?.schedules
        if (scheduleItems.isNullOrEmpty()){
            binding.nextText.visibility = View.GONE
        }
        else{
            binding.nextText.visibility = View.VISIBLE
        }
        nowPlayingProgram()
       // val filterProgramList = epgScheduleItems?.schedules?.filter { !it.isCurrentlyPlaying }
        val liveChannelPlayerAdapter = LiveChannelPlayerAdapter(epgScheduleItems?.schedules, epgScheduleItemClickListener)
        binding.epgScheduleRecycler.adapter = liveChannelPlayerAdapter
        binding.epgScheduleRecycler.layoutManager = LinearLayoutManager(requireActivity(), LinearLayoutManager.HORIZONTAL, false)
    }

    private fun nowPlayingProgram(){
        val nowPlayingProgram = epgScheduleItems?.schedules?.find { it.isCurrentlyPlaying }
        binding.titleText.text = nowPlayingProgram?.program?.title
        val images = getImageUrl(Constants.SIXTEEN_INTO_NINE, nowPlayingProgram?.program?.images, binding.headerImage.context)
        binding.headerImage.setImageWithGlide(binding.headerImage.context, images)
        val channelImages = getImageUrl(Constants.SIXTEEN_INTO_NINE, epgScheduleItems?.mediaContent?.images, binding.channelImageView.context)
        binding.channelImageView.setImageWithGlide(binding.channelImageView.context, channelImages)
    }
}