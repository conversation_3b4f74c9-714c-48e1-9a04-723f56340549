package com.enveu.jwplayer.player

import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.detail.ui.LiveLinearDetailsActivity
import com.enveu.baseModels.BaseActivity
import com.enveu.databinding.ActivityLivePlayerBinding
import com.enveu.epg.models.EPGItem
import com.enveu.player.EnveuPlayer
import com.enveu.player.VideoMetadata
import com.enveu.player.brightcove.listener.BcovePlayerListener
import com.enveu.utils.Constants
import com.enveu.utils.constants.AppConstants
import com.enveu.view_model.ShortsViewModel
import com.google.gson.Gson

class LivePlayerActivity : BaseActivity() {
    private lateinit var binding: ActivityLivePlayerBinding
    private var enveuPlayer: EnveuPlayer? = null
    private var epgScheduleItem: EPGItem? = null
    private var shortsViewModel: ShortsViewModel? = null
    private var liveChannelFragmentDialog: LiveChannelDialog? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        shortsViewModel = ViewModelProvider(this)[ShortsViewModel::class.java]

        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        window.setBackgroundDrawableResource(R.color.buy_now_pay_now_btn_text_color)
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        binding = DataBindingUtil.setContentView(this, R.layout.activity_live_player)
        initPlayer()
    }

    private fun initPlayer() {

        hideSystemUI()
        val externalRefId = intent.getStringExtra(Constants.EXTERNAL_REF_ID)
        val playerUrl = intent.getStringExtra(Constants.PLAYER_URL)
        val title = intent.getStringExtra(Constants.CONTENT_TITLE)
        val description = intent.getStringExtra(Constants.CONTENT_DESCRIPTION)
        if (intent.getStringExtra(Constants.EPG_ITEM) != null) {
            val epgJson = intent.getStringExtra(Constants.EPG_ITEM)
            epgScheduleItem = Gson().fromJson(epgJson, EPGItem::class.java)
        }
        if (enveuPlayer != null) {
            enveuPlayer?.releasePlayer()
        }
        val videoMetadata = VideoMetadata()

        if (!playerUrl.isNullOrEmpty()){
            videoMetadata.bcoveVideoId = null
            videoMetadata.videoUrl = playerUrl.toString()
        }
        else{
            videoMetadata.videoUrl = ""
            videoMetadata.bcoveVideoId = externalRefId
//            getChromeCastUrl(videoMetadata.bcoveVideoId)
        }

        videoMetadata.title = title?:""
        videoMetadata.description = description?:""
        videoMetadata.mediaType = EnveuPlayer.LIVE
        fitPlayerToFullScreen()
        enveuPlayer = EnveuPlayer.Builder()
            .playerType(EnveuPlayer.PLAYER_BRIGHTCOVE)
            .playerListener(playerListener)
            .bcovePolicyKey(SDKConfig.BRIGHT_COVE_POLICY_KEY)
            .bcoveAccountId(SDKConfig.BRIGHT_COVE_ACCOUNT_ID)
            .videoMetadata(videoMetadata)
            .seekToPosition(0L)
            .setViewType(EnveuPlayer.HOTSTAR_PLAYER_VIEW_TYPE)
            .playerControlTimeout(5_000)
            .isTvPlayer(false)
            .updateLivePlayer(true)
            .showVideoQuality(true)
            .setBookmarkEnabled(true)
            .contentType("LIVE")
            .setBookmarkPercentValue(10)
            .setBookmarkFinishPercentValue(95)
            .setBookmarkInterval(10)
            .isVideoQualityAvailable(true)
            .build()
        val transaction = supportFragmentManager.beginTransaction()
        transaction.replace(R.id.content_player, enveuPlayer!!.getPlayerView(), "EnveuPlayer")
        transaction.commit()

        Handler(Looper.getMainLooper()).postDelayed({
            val intent = Intent("KEY_EVENT_TAG").apply { putExtra(EnveuPlayer.CHROME_CAST_URL, playerUrl) }
            LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
        }, 2000)

    }

    private fun getChromeCastUrl(playBackUrl: String?) {
        shortsViewModel?.getChromeCastUrl(SDKConfig.BRIGHT_COVE_ACCOUNT_ID, playBackUrl)?.observe(this){
            if (!it.isNullOrEmpty()) {
                val intent = Intent("KEY_EVENT_TAG").apply {
                    putExtra(EnveuPlayer.CHROME_CAST_URL, it)
                }
                LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
            }
        }
    }

    private fun fitPlayerToFullScreen() {
        val params: ViewGroup.LayoutParams = binding.contentPlayer.layoutParams
        params.width = ViewGroup.LayoutParams.MATCH_PARENT
        params.height = ViewGroup.LayoutParams.MATCH_PARENT
        binding.contentPlayer.requestLayout()
    }

    private val playerListener = object : BcovePlayerListener() {
        override fun onLiveMoreCalled() {
            super.onLiveMoreCalled()
            openMoreEpisodesDialog()
        }
    }

    private val epgScheduleItemClickListener = object : EpgScheduleItemClickListener {
        override fun onEpgScheduleItemClickListener(position: Int) {
            val singleProgram = epgScheduleItem?.schedules?.get(position)
           // goToDetailPage(epgScheduleItem, singleProgram?.epgChannelId, singleProgram?.program?.id.toString(), false)
        }
    }

    fun openMoreEpisodesDialog() {
        if (epgScheduleItem != null) {
            if (liveChannelFragmentDialog == null) {
                liveChannelFragmentDialog = LiveChannelDialog()
                liveChannelFragmentDialog?.getScheduleItems(epgScheduleItem, epgScheduleItemClickListener)
            }
            liveChannelFragmentDialog?.show(supportFragmentManager, "TransparentDialogFragment")
        }
    }

    private fun goToDetailPage(epgItem: EPGItem?, channelId: String?, showId: String?, isFromChannelClick: Boolean) {
        val epgJson=Gson().toJson(epgItem)
        val bundle = Bundle()
        bundle.putString(AppConstants.FROM_REDIRECTION, AppConstants.EPG_FRAGMENT)
        bundle.putString(AppConstants.EPG_ITEM, epgJson.toString())
        bundle.putString(AppConstants.EPG_CHANNEL_ID, channelId)
        bundle.putString(AppConstants.EPG_SHOW_ID, showId)
        bundle.putBoolean(AppConstants.IS_FROM_CHANNEL_CLICK, isFromChannelClick)
        startActivity(Intent(this, LiveLinearDetailsActivity::class.java).also {
            it.putExtras(bundle)
        })
        finish()
    }


    @Suppress("DEPRECATION")
    private fun hideSystemUI() {
        supportActionBar?.hide()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window.setDecorFitsSystemWindows(false)
            val controller = window.insetsController
            controller?.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
            controller?.systemBarsBehavior =
                WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        } else {
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION)
        }
    }
}