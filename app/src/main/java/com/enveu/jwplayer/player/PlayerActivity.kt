package com.enveu.jwplayer.player

import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.WindowCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.OttApplication
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.detail.ContentMetaData
import com.enveu.activities.detail.viewModel.DetailViewModel
import com.enveu.activities.device_management.DeviceManagementViewModel
import com.enveu.activities.series.ui.SeasonEpisodes
import com.enveu.activities.series.ui.SeasonEpisodesList
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.ActivitySelectSubscriptionPlan
import com.enveu.activities.usermanagment.ui.EnterOTPActivity
import com.enveu.baseModels.BaseActivity
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.client.bookmarking.bean.GetBookmarkResponse
import com.enveu.databinding.ActivityPlayerBinding
import com.enveu.enums.EVENT_STATUS
import com.enveu.enums.StreamStatus
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstanceWithCancelBtn
import com.enveu.networking.response.AllReelsListItems
import com.enveu.player.EnveuPlayer
import com.enveu.player.VideoMetadata
import com.enveu.player.brightcove.listener.BcovePlayerListener
import com.enveu.player.model.Progress
import com.enveu.utils.Constants
import com.enveu.utils.Logger
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.enveu.view_model.ShortsViewModel
import com.google.gson.Gson
import com.google.gson.JsonObject
import org.json.JSONObject
import java.io.Serializable
import kotlin.math.log

class PlayerActivity : BaseActivity(), Serializable, CommonDialogFragment.EditDialogListener {
    private lateinit var binding: ActivityPlayerBinding
    private var enveuPlayer: EnveuPlayer? = null
    private var contentMetaData: ContentMetaData? = null
    private var seasonEpisodesList: List<EnveuVideoItemBean>? = null
    var contentId: Int? = 0
    private var countDownTimer: CountDownTimer? = null
    private var startSessionApiStart = false
    private var token: String? = null
    private var isLogin: String? = null
    private var currentPlayingIndex: Int? = -1
    private var deviceManagementViewModel : DeviceManagementViewModel?= null
    private var playbackUrl: String? = null
    private var isUserVerified: String? = null
    private var preference: KsPreferenceKeys? = null
    private var isLoggedIn = false
    private var viewModel: DetailViewModel? = null
    private var bookmarkingViewModel: BookmarkingViewModel? = null
    private var isUserNotVerify = false
    private var isUserNotEntitle = false
    private val stringsHelper by lazy { StringsHelper }
    private var isGeoBlocking = false
    private var streamingStatusEnd = false
    private var isBingeWatchEnable: Boolean? = false
    private var resEntitle: ResponseEntitle? = null
    private var shortsViewModel: ShortsViewModel? = null
    private var bookmarkPosition = 0.0
    private var allEpisodesDialog: AllEpisodesPlayerDialog?= null
    private var singleContentItems:EnveuVideoItemBean?= null

    private val playerListener = object : BcovePlayerListener() {

        override fun onBackClick() {
                super.onBackClick()
                if (startSessionApiStart){
                    stopPlaySessionID()
                }
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                finish()
            }

        override fun onMoreEpisodesClickListener() {
            super.onMoreEpisodesClickListener()
            openMoreEpisodesDialog()
        }

//        override fun onError(error: String) {
//            super.onError(error)
//            if (startSessionApiStart){
//                stopPlaySessionID()
//            }
//            detailsPlayerCallBack?.onPlayerError()
//        }
//
//        override fun onComplete() {
//            super.onComplete()
//            if (isBingeWatchEnable!!) {
//                if (startSessionApiStart){
//                    stopPlaySessionID()
//                }
//                playNextEpisode()
//            } else {
//                if (startSessionApiStart){
//                    stopPlaySessionID()
//                }
//                detailsPlayerCallBack?.onCompleted()
//            }
//        }

            override fun onBingeWatchCalled() {
                super.onBingeWatchCalled()
                playNextEpisode()
            }

        override fun onBookmark(progress: Progress) {
            super.onBookmark(progress)
            if(!contentMetaData?.contentType.equals(AppConstants.Trailer) || !contentMetaData?.contentType.equals(AppConstants.CLIP) || !contentMetaData?.contentType.equals(AppConstants.REPLAY)) {
                if(contentMetaData?.isLive == true || contentMetaData?.contentType.equals(AppConstants.MATCHES,ignoreCase = true)) {
                    checkLiveStatus()
                } else {
                    val i: Int = progress.position.toInt()
                    if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
                        bookmarkingViewModel?.bookmarkVideo(token, contentMetaData?.contentId!!, i)
                        setEventTrackingBookmark(progress)
                    }
                }
            }
        }

        override fun onBookmarkFinish(progress: Progress) {
            super.onBookmarkFinish(progress)
            if(!contentMetaData?.contentType.equals(AppConstants.Trailer) || !contentMetaData?.contentType.equals(AppConstants.CLIP) || !contentMetaData?.contentType.equals(AppConstants.REPLAY)) {
                if (contentMetaData?.isLive == false || !contentMetaData?.contentType.equals(AppConstants.MATCHES, ignoreCase = true)) {
                    if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
                        bookmarkingViewModel?.finishBookmark(token, contentMetaData?.contentId!!)
                    }
                }
            }
        }

    }

    private fun setEventTrackingBookmark(progress: Progress){
        val attributeObject = JsonObject().apply {
            addProperty("contentId", contentMetaData?.contentId)
            addProperty("title", contentMetaData?.contentTitle)
            addProperty("mediaType", contentMetaData?.contentType)
            addProperty("viewPercentage", progress.position)
        }

        val eventObject = JsonObject().apply {
            addProperty("name", "BOOKMARKING")
            addProperty("identifier", "BOOKMARKING")
            add("attribute", attributeObject)
        }

        val additionalInfoObject = JsonObject().apply {
            addProperty("customerId", preference?.userId)
        }

        val customerObject = JsonObject().apply {
            add("additionalInformation", additionalInfoObject)
        }

        val finalJson = JsonObject().apply {
            add("event", eventObject)
            add("customer", customerObject)
        }

        shortsViewModel?.setEventTracking(finalJson, preference?.appPrefAccessToken)
    }


    private fun setEventTrackingStartPlayer(){
        val attributeObject = JsonObject().apply {
            addProperty("contentId", contentMetaData?.contentId)
            addProperty("title", contentMetaData?.contentTitle)
            addProperty("mediaType", contentMetaData?.contentType)
        }
        val eventObject = JsonObject().apply {
            addProperty("name", "CONTENT VIEWED")
            addProperty("identifier", "CONTENT_VIEWED")
            add("attribute", attributeObject)
        }
        val additionalInfoObject = JsonObject().apply {
            addProperty("customerId", preference?.userId)
        }
        val customerObject = JsonObject().apply {
            add("additionalInformation", additionalInfoObject)
        }
        val finalJson = JsonObject().apply {
            add("event", eventObject)
            add("customer", customerObject)
        }

        shortsViewModel?.setEventTracking(finalJson, preference?.appPrefAccessToken)
    }

    private val episodesItemClickListener = object : EpisodesItemClickListener {
        override fun onEpisodesItemsClickListener(enveuVideoItemBean: EnveuVideoItemBean?) {
            allEpisodesDialog?.dismiss()
            setNewPlayer(enveuVideoItemBean)
        }
    }

    private fun getChromeCastUrl(playBackUrl: String?) {
       shortsViewModel?.getChromeCastUrl(SDKConfig.BRIGHT_COVE_ACCOUNT_ID, playBackUrl)?.observe(this){
           if (!it.isNullOrEmpty()) {
               val intent = Intent("KEY_EVENT_TAG").apply { putExtra(EnveuPlayer.CHROME_CAST_URL, it) }
               LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
           }
       }
    }

    private fun setNewPlayer(enveuVideoItemBean:EnveuVideoItemBean?){
        val contentMetaData = ContentMetaData()
        contentMetaData.playBackUrl = enveuVideoItemBean?.externalRefId
        contentMetaData.contentTitle = enveuVideoItemBean?.title
        contentMetaData.isLive = false
        contentMetaData.contentType = enveuVideoItemBean?.assetType
        contentMetaData.mediaType = AppConstants.episodes
        contentMetaData.contentId = enveuVideoItemBean?.id
        contentMetaData.isDrmDisabled = enveuVideoItemBean?.drmDisabled == true
        contentMetaData.widevineUrl = widevineUrl
        contentMetaData.fileUrl = fileUrl
        this.contentMetaData = contentMetaData
        singleContentItems = enveuVideoItemBean
        initPlayer(contentMetaData)
    }
    fun openMoreEpisodesDialog() {
        if (allEpisodesDialog == null) {
            allEpisodesDialog = AllEpisodesPlayerDialog()
            allEpisodesDialog?.getSingleEpisodesItems(singleContentItems, episodesItemClickListener)
        }
        allEpisodesDialog?.show(supportFragmentManager, "TransparentDialogFragment")
    }

    private var signedUrl = ""
    private var drmBaseUrl = ""
    private var drmToken = ""

    private fun playNextEpisode() {
        if (isLoggedIn) {
                if (shouldShowBingeWatch(seasonEpisodesList?.size)) {
                    if (seasonEpisodesList != null && seasonEpisodesList!!.isNotEmpty()) {
                        val songListSize = seasonEpisodesList!!.size
                        for (i in 0 until songListSize) {
                            val id = seasonEpisodesList!![i].id
                            if (songListSize != null) {
                                if (id == contentId && i < songListSize - 1) {
                                    com.enveu.player.utils.Logger.d("id: $contentId")
                                    val nextAudioItem = seasonEpisodesList?.get(i + 1)
                                    this.contentId = nextAudioItem?.id!!
                                    checkGeoBlocking()
                                    if (isGeoBlocking) {
                                        commonDialog(
                                            stringsHelper.stringParse(
                                                stringsHelper.instance()?.data?.config?.geo_blocking_title.toString(),
                                                getString(R.string.geo_blocking_title)
                                            ),
                                            "",
                                            stringsHelper.stringParse(
                                                stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                                                getString(R.string.ok)
                                            )
                                        )
                                    } else {
                                        if (nextAudioItem.isPremium) {
                                            checkEntitlement(
                                                KsPreferenceKeys.getInstance().appPrefAccessToken,
                                                nextAudioItem.sku, nextAudioItem
                                            )
                                        } else {
                                            if (isUserVerified.equals("true", ignoreCase = true)) {
                                                if (!nextAudioItem.drmDisabled) {
                                                    viewModel?.getLicenseUrl(
                                                        "",
                                                        nextAudioItem.sku,
                                                        AppConstants.JW_DRM_KEY
                                                    )?.observe(this) {
                                                        if (it != null) {
                                                            signedUrl = it.toString()
                                                            drmBaseUrl =
                                                                signedUrl.substringBefore("?")
                                                            drmToken =
                                                                signedUrl.substringAfter("token=")
                                                            callJwApiToGetWidevineUrl(
                                                                drmBaseUrl,
                                                                drmToken,
                                                                nextAudioItem.title,
                                                                nextAudioItem.assetType,
                                                                nextAudioItem.drmDisabled
                                                            )
                                                        } else {
                                                            commonDialog(
                                                                stringsHelper.stringParse(
                                                                    stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                                                    getString(R.string.popup_error)
                                                                ),
                                                                stringsHelper.stringParse(
                                                                    stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                                                    getString(R.string.popup_something_went_wrong)
                                                                ),
                                                                stringsHelper.stringParse(
                                                                    stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                                                    getString(R.string.popup_continue)
                                                                )
                                                            )
                                                        }
                                                    }

                                                } else {
                                                    if (null != nextAudioItem.externalRefId && !nextAudioItem.externalRefId.equals("", ignoreCase = true)) {
                                                        playbackUrl = nextAudioItem.externalRefId
                                                    }
                                                    val contentMetaData = ContentMetaData()
                                                    contentMetaData.playBackUrl = playbackUrl
                                                    contentMetaData.mediaType = this.contentMetaData?.mediaType
                                                    contentMetaData.contentTitle = nextAudioItem.title
                                                    contentMetaData.isLive = false
                                                    contentMetaData.contentType = nextAudioItem.assetType
                                                    contentMetaData.isDrmDisabled = nextAudioItem.drmDisabled
                                                    if (contentMetaData.mediaType.equals(AppConstants.EPISODES,ignoreCase = true) || contentMetaData.mediaType.equals(AppConstants.Movie,ignoreCase = true) && preference?.concurrencyEnable == true  && preference?.appPrefLoginStatus.equals(
                                                            AppConstants.UserStatus.Login.toString(), ignoreCase = true))
                                                    {
                                                      //  deviceCheckStatus()
                                                        initPlayer(contentMetaData)
                                                    } else {
                                                        initPlayer(contentMetaData)
                                                    }
                                                }

                                            } else {
                                                isUserNotVerify = true
                                                commonDialog(
                                                    "",
                                                    stringsHelper.stringParse(
                                                        stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(),
                                                        getString(R.string.popup_user_not_verify)
                                                    ),
                                                    stringsHelper.stringParse(
                                                        stringsHelper.instance()?.data?.config?.popup_verify.toString(),
                                                        getString(R.string.popup_verify)
                                                    )
                                                )
                                            }
                                        }
                                    }

                                    break
                                }
                            }
                        }
                    }
                } else {
                    if (enveuPlayer != null)
                        enveuPlayer?.releasePlayer()
                    finish()
                }
        }else{
            ActivityLauncher.getInstance().loginActivity(this@PlayerActivity, ActivityLogin::class.java, "")

        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopSessionUpdateTimer()
        stopPlaySessionID()
    }

    private var widevineUrl: String? = ""
    private var fileUrl: String? = ""
    private fun callJwApiToGetWidevineUrl(
        signedUrl: String,
        drmToken: String,
        title: String,
        assetType: String,
        drmDisabled: Boolean
    ) {
        viewModel?.getWidevineUrl(signedUrl,drmToken)?.observe(this) {
            widevineUrl = it?.playlist?.get(0)?.sources?.get(0)?.drm?.widevine?.url.toString()
            fileUrl = it?.playlist?.get(0)?.sources?.get(0)?.file.toString()
            if (widevineUrl != "" && fileUrl != "") {
                val contentMetaData = ContentMetaData()
                contentMetaData.playBackUrl = playbackUrl
                contentMetaData.mediaType = this.contentMetaData?.mediaType
                contentMetaData.contentTitle = title
                contentMetaData.isLive = false
                contentMetaData.contentType = assetType
                contentMetaData.isDrmDisabled = drmDisabled
                if (contentMetaData.mediaType.equals(AppConstants.EPISODES,ignoreCase = true) || contentMetaData.mediaType.equals(AppConstants.Movie,ignoreCase = true) && preference?.concurrencyEnable == true  && isLogin.equals(
                        AppConstants.UserStatus.Login.toString(), ignoreCase = true))
                {
                    deviceCheckStatus()
                } else {
                    initPlayer(contentMetaData)
                }
            }

        }
    }

    private fun checkEntitlement(token: String?, sku: String?, nextAudioItem: EnveuVideoItemBean) {
        viewModel?.hitApiEntitlement(token, sku)?.observe(this@PlayerActivity) { responseEntitle ->
           // binding!!.progressBar.visibility = View.GONE
            if (null != responseEntitle && null != responseEntitle.data) {
                resEntitle = responseEntitle
//                maxAllowedConcurrencyLimit = responseEntitle.data.maxAllowedConcurrency
                if (responseEntitle.data.entitled) {
                    if (isUserVerified.equals("true", ignoreCase = true)) {
                        viewModel?.externalRefID(responseEntitle.data.accessToken,responseEntitle.data.sku)?.observe(this){
                            playbackUrl = it?.data?.externalRefId
                            val contentMetaData = ContentMetaData()
                            contentMetaData.playBackUrl = playbackUrl
                            contentMetaData.mediaType = this.contentMetaData?.mediaType
                            contentMetaData.contentTitle = nextAudioItem.title
                            contentMetaData.isLive = false
                            contentMetaData.contentType = nextAudioItem.assetType
                            if (contentMetaData.mediaType.equals(AppConstants.EPISODES,ignoreCase = true) || contentMetaData.mediaType.equals(AppConstants.Movie,ignoreCase = true) && preference?.concurrencyEnable == true  && isLogin.equals(
                                    AppConstants.UserStatus.Login.toString(), ignoreCase = true))
                            {
                                deviceCheckStatus()
                            } else {
                                initPlayer(contentMetaData)
                            }                       }

                    } else {
                        isUserNotVerify = true
                        commonDialog(
                            "",
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(),
                                getString(R.string.popup_user_not_verify)
                            ),
                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_verify.toString(), getString(R.string.popup_verify))
                        )
                    }
                } else {
                    isUserNotEntitle = true
                    commonDialogWithCancel(
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(), getString(R.string.popup_notEntitled)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_select_plan.toString(), getString(R.string.popup_select_plan)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_purchase.toString(), getString(R.string.popup_purchase)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_cancel.toString(), getString(R.string.popup_cancel))
                    )
                }
            } else {
                if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                  //  clearCredientials(preference)
                    ActivityLauncher.getInstance().loginActivity(this@PlayerActivity, ActivityLogin::class.java, "")
                } else {
                    commonDialog(
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_error.toString(), getString(R.string.popup_error)),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(), getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue))
                    )
                }
            }
        }

    }

    private fun shouldShowBingeWatch(size: Int?): Boolean {
        var shouldBingeWatchShow: Boolean? = false
        currentPlayingIndex = currentPlayingIndex!! + 1
        Log.d("currentPlayingIndex", "size: $size")
        Log.d("currentPlayingIndex", "getCurrentPlayingIndex: $currentPlayingIndex")
        if (size != null) {
            shouldBingeWatchShow = currentPlayingIndex!! < size
        }
        if (!shouldBingeWatchShow!!) {
            isBingeWatchEnable = false
        }
        return shouldBingeWatchShow

    }

    private fun getCurrentPlayingIndex(seasonEpisodesList: List<EnveuVideoItemBean>): Int? {
        var currentPlayingIndex: Int? = 0
        val total = seasonEpisodesList.size
        for (i in 0 until total) {
            val id = seasonEpisodesList[i].id
            if (id == contentId) {
                currentPlayingIndex = i
                break
            }
        }
        Log.d("currentPlayingIndex", "getCurrentPlayingIndex: $currentPlayingIndex")
        return currentPlayingIndex
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        window.setBackgroundDrawableResource(R.color.buy_now_pay_now_btn_text_color)
        window.setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN)
        binding = DataBindingUtil.setContentView(this,R.layout.activity_player)
        preference = KsPreferenceKeys.getInstance()
        hideSystemUI()
        token = preference?.appPrefAccessToken
        isLogin = preference?.appPrefLoginStatus
        if (preference?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
        }
//        maxAllowedConcurrencyLimit = if (preference?.planMaxAllowedConcurrency != null) {
//            preference?.planMaxAllowedConcurrency!!
//        } else {
//            preference?.configMaxAllowedConcurrency!!
//        }
//        maxAllowedConcurrencyLimit = preference?.planMaxAllowedConcurrency
        isUserVerified = preference?.isVerified
        viewModel = ViewModelProvider(this)[DetailViewModel::class.java]
        deviceManagementViewModel = ViewModelProvider(this)[DeviceManagementViewModel::class.java]
        bookmarkingViewModel = ViewModelProvider(this)[BookmarkingViewModel::class.java]
        shortsViewModel = ViewModelProvider(this)[ShortsViewModel::class.java]
        getIntentValue()
        parseColor()
    }

    private fun getIntentValue() {
        contentMetaData = intent.getParcelableExtra(AppConstants.CONTENT_META_DATA)

        if (intent.getStringExtra(Constants.SINGLE_CONTENT_BUNDLE) != null) {
            val singleEpisodesJson = intent.getStringExtra(Constants.SINGLE_CONTENT_BUNDLE)
            singleContentItems = Gson().fromJson(singleEpisodesJson, EnveuVideoItemBean::class.java)
        }
        this.contentId = contentMetaData?.contentId
        try {
            if (intent.extras?.getSerializable(Constants.ALL_EPISODES_LIST) != null) {
               val seasonEpisodes = intent.extras?.getSerializable(Constants.ALL_EPISODES_LIST) as SeasonEpisodes
               val seasonEpisodesObject = Gson().fromJson(seasonEpisodes.seasonEpisodesJson, SeasonEpisodesList::class.java)
                seasonEpisodesList = seasonEpisodesObject.seasonEpisodesList
                if (seasonEpisodesList?.isNotEmpty() == true) {
                    isBingeWatchEnable = true
                    currentPlayingIndex = getCurrentPlayingIndex(seasonEpisodesList!!)
                } else {
                    isBingeWatchEnable = false
                }

            }
        } catch (e: Exception) {
            isBingeWatchEnable = false
            Logger.w(e)
        }
        callBookMarkApi()
    }

    private fun parseColor() {
        binding.stringData = stringsHelper
    }

    private fun initUi() {

        if (contentMetaData != null) {
            this.contentId = contentMetaData?.contentId
            if ((contentMetaData?.mediaType.equals(AppConstants.EPISODES,ignoreCase = true) || contentMetaData?.mediaType.equals(AppConstants.Movie,ignoreCase = true)) && preference?.concurrencyEnable == true  && isLoggedIn)
            {
                deviceCheckStatus()
            } else {
                initPlayer(contentMetaData)
            }
        }
    }

    private fun deviceCheckStatus() {
        var activeDeviceID = false
        deviceManagementViewModel?.getAllDevices(
            0,
            "ACTIVE",
            preference?.appPrefAccessToken
        )?.observe(this) {
            if (it.responseCode == 2000) {
                for (item in it.data?.items!!) {
                    if (item.deviceId == AppCommonMethod.getDeviceId(OttApplication.instance!!.contentResolver)) {
                        activeDeviceID = true
                        break
                    }
                }
                if (activeDeviceID) {
                    startSessionPlayback()
                }
            } else {
                showConcurrencyDialog(
                    this.resources.getString(R.string.error),
                    resources.getString(R.string.something_went_wrong),
                    resources.getString(R.string.ok)
                )
            }
        }
    }


    private fun startSessionUpdateTimer() {
        countDownTimer = object : CountDownTimer(
            Long.MAX_VALUE,
            preference?.updatePlaybackSessionTime?.toLong()!! * 1000
        ) {
            override fun onTick(millisUntilFinished: Long) {
                if (startSessionApiStart) {
                    preference?.appPrefAccessToken?.let { token ->
                        preference?.sessionId?.let { sessionID ->
                            deviceManagementViewModel?.updatePlaybackSession(
                                token,
                                sessionID
                            )?.observe(this@PlayerActivity) {
                                if (it != null) {
                                    when (it.responseCode) {
                                        2000 -> {}
                                        4098 -> {
                                            stopSessionUpdateTimer()
                                            enveuPlayer?.pause()
                                            showConcurrencyDialog(
                                                "",
                                                resources.getString(R.string.no_season_found_this_session_id),
                                                resources.getString(R.string.ok)
                                            )
                                        }

                                        4302 -> {
                                            stopSessionUpdateTimer()
                                            enveuPlayer?.pause()
                                            showConcurrencyDialog(
                                                "",
                                                resources.getString(R.string.user_login_limit),
                                                resources.getString(R.string.ok)
                                            )
                                        }

                                        else -> {
                                            stopSessionUpdateTimer()
                                            enveuPlayer?.pause()
                                            showConcurrencyDialog(
                                                "",
                                                resources.getString(R.string.something_went_wrong),
                                                resources.getString(R.string.ok)
                                            )
                                        }
                                    }
                                } else {
                                    stopSessionUpdateTimer()
                                    enveuPlayer?.pause()
                                    showConcurrencyDialog(
                                        "",
                                        resources.getString(R.string.something_went_wrong),
                                        resources.getString(R.string.ok)
                                    )
                                }
                            }
                        }
                    }
                }
            }

            override fun onFinish() {
                // Perform any final actions here if needed
            }
        }

        // Start the countdown timer
        countDownTimer?.start()
    }


    private fun stopSessionUpdateTimer() {
        countDownTimer?.cancel()
        countDownTimer = null
    }


    private fun stopPlaySessionID(isStartPlay : Boolean?=null) {
        preference?.appPrefAccessToken?.let { token ->
            preference?.sessionId?.let { sessionId ->
                deviceManagementViewModel?.stopPlaybackSession(token, sessionId)
                    ?.observe(this) {
                        if (it != null) {
                            stopSessionUpdateTimer()
                            if (it.responseCode == 2000) {
                                preference?.sessionId = ""
                                if (isStartPlay == true){
                                    startSessionPlayback()
                                }
                            }else{
                                showConcurrencyDialog("",resources.getString(R.string.something_went_wrong),"ok")
                            }
                        }else{
                            showConcurrencyDialog("",resources.getString(R.string.something_went_wrong),"ok")
                        }
                    }
            }
        }
    }

    private fun showConcurrencyDialog(title: String, description: String, actionBtn: String) {
        try {
            var fm: FragmentManager = this.supportFragmentManager
            var commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
            commonDialogFragment.setEditDialogCallBack(this)
            commonDialogFragment.show(fm, AppConstants.MESSAGE)
        }catch (e :Exception){
            e.printStackTrace()
        }

    }

    private fun startSessionPlayback() {
        if (!preference?.sessionId.isNullOrEmpty()){
            stopPlaySessionID(true)
        }else {
            deviceManagementViewModel?.startSessionPlayBack(
                preference?.appPrefAccessToken!!,
                contentId.toString(),
               ""
            )?.observe(this@PlayerActivity) {
                when {
                    it != null && it.responseCode == 2000 -> {
                        startSessionApiStart = true
                        preference?.sessionId = it.data?.sessionId ?: ""
                        initPlayer(contentMetaData)
                    }
                    it.responseCode == 4099 -> {
                        enveuPlayer?.pause()
                        showConcurrencyDialog(
                            resources.getString(R.string.screen_limit),
                            resources.getString(R.string.You_can_play)+" ${preference?.planMaxAllowedConcurrency} "+resources.getString(R.string.screen_at_a_time_Please_stop_playing_another_screen),
                            resources.getString(R.string.ok)
                        )
                    }
                    else -> {
                        enveuPlayer?.pause()
                        showConcurrencyDialog(
                            "",
                            resources.getString(R.string.something_went_wrong),
                            resources.getString(R.string.ok)
                        )
                    }
                }
            }
        }
    }


    private fun initPlayer(contentMetaData: ContentMetaData?) {
        getChromeCastUrl(contentMetaData?.playBackUrl)
        if (enveuPlayer!=null) {
            enveuPlayer?.releasePlayer()
        }
        val videoMetadata = VideoMetadata()
        videoMetadata.bcoveVideoId = contentMetaData?.playBackUrl
        videoMetadata.title = contentMetaData?.contentTitle?:""
        videoMetadata.description = getGenresSubStrings()?:""
        videoMetadata.mediaType = EnveuPlayer.VOD
        videoMetadata.vasTagsAdsUrl = singleContentItems?.vastTag?:SDKConfig.getInstance().configVastTag
        fitPlayerToFullScreen()
        enveuPlayer = EnveuPlayer.Builder()
            .playerType(EnveuPlayer.PLAYER_BRIGHTCOVE)
            .playerListener(playerListener)
            .bcovePolicyKey(SDKConfig.BRIGHT_COVE_POLICY_KEY)
            .bcoveAccountId(SDKConfig.BRIGHT_COVE_ACCOUNT_ID)
            .videoMetadata(videoMetadata)
            .bingeWatchJson(getBingeWatchJsonObject())
            .seekToPosition(0L)
            .setViewType(EnveuPlayer.HOTSTAR_PLAYER_VIEW_TYPE)
            .playerControlTimeout(5_000)
            .isTvPlayer(false)
            .updateLivePlayer(false)
            .videoQuality(getPLayerQuality())
            .showVideoQuality(true)
            .setBookmarkEnabled(true)
            .setBookmarkPosition(bookmarkPosition)
            .contentType(contentMetaData?.mediaType?:"")
            .setBookmarkPercentValue(10)
            .setBookmarkFinishPercentValue(95)
            .setBookmarkInterval(10)
            .isVideoQualityAvailable(true)
            .build()
        val transaction = supportFragmentManager.beginTransaction()
        transaction.replace(R.id.content_player, enveuPlayer!!.getPlayerView(), "EnveuPlayer")
        transaction.commit()

        if (startSessionApiStart) {
            Handler(Looper.getMainLooper()).postDelayed({
                startSessionUpdateTimer()
            },30000)
        }
        setEventTrackingStartPlayer()
    }

    private fun getGenresSubStrings(): String {
        val finalDescription:String?
        val genresStrings = singleContentItems?.customData?.genres?.map { it.title }
        finalDescription = if (!genresStrings.isNullOrEmpty()){
            if (singleContentItems?.parentalRating != null){
                "${singleContentItems?.parentalRating}, ${genresStrings.joinToString(", ")}"
            } else{
                "${genresStrings.joinToString(", ")}"
            }
        } else{
            ""
        }
      return finalDescription
    }

    private fun getPLayerQuality(): Int {
        return when (preference?.qualityName) {
            "SD" -> {
                EnveuPlayer.QUALITY_LOW
            }
            "HD" -> {
                EnveuPlayer.QUALITY_MEDIUM
            }
            "Full HD" -> {
                EnveuPlayer.QUALITY_HIGH
            }
            else -> {
                EnveuPlayer.QUALITY_AUTO
            }
        }
    }

    private fun fitPlayerToFullScreen() {
        val params: ViewGroup.LayoutParams = binding.contentPlayer.layoutParams
        params.width = ViewGroup.LayoutParams.MATCH_PARENT
        params.height = ViewGroup.LayoutParams.MATCH_PARENT
        binding.contentPlayer.requestLayout()
    }

    private fun getBingeWatchJsonObject(): JSONObject {
        val jsonObject = JSONObject()
        jsonObject.put(AppConstants.TIMER, 30)
        jsonObject.put(AppConstants.BINGE_WATCH_POSITION, 0L)
        if (contentMetaData?.mediaType.equals(AppConstants.EPISODES,ignoreCase = true) && currentPlayingIndex!! < seasonEpisodesList?.size!! - 1){
            jsonObject.put(AppConstants.BINGE_WATCHING_ENABLED, true)
        }else{
            jsonObject.put(AppConstants.BINGE_WATCHING_ENABLED, false)
        }

        return jsonObject
    }
    private fun getMediaType(): Int {
        return if (contentMetaData?.isLive == true)
            EnveuPlayer.LIVE
        else
            EnveuPlayer.VOD
    }
    override fun onBackPressed() {
        val orientation = this.resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            if (KsPreferenceKeys.getInstance().concurrencyEnable == true && isLogin.equals(
                    AppConstants.UserStatus.Login.toString(), ignoreCase = true
                )
            ) {
                stopPlaySessionID()
            }
            finish()
        } else {
                enveuPlayer?.onBackPressed()
        }
        stopSessionUpdateTimer()
        stopPlaySessionID()
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        super.onBackPressed()
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = supportFragmentManager
        val commonDialogFragment = newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    override fun onActionBtnClicked() {
        if(streamingStatusEnd) {
            if (enveuPlayer != null) {
                enveuPlayer?.releasePlayer()
                onBackPressed()
            }
        }
        if (isUserNotVerify) {
            ActivityLauncher.getInstance().goToEnterOTP(this, EnterOTPActivity::class.java, "DetailPage")
        }
        if (isUserNotEntitle) {
            ActivityLauncher.getInstance().goToPlanScreen(this@PlayerActivity, ActivitySelectSubscriptionPlan::class.java, "")
        }
    }

    override fun onCancelBtnClicked() {
    }


    private fun checkGeoBlocking() {
        viewModel!!.getGeoBlocking(contentId.toString())
            .observe(this@PlayerActivity) { response ->
                if (response != null && response.data != null) {
                    if(response.data.isIsBlocked) {
                        isGeoBlocking = true
                    }
                } else {
                    commonDialog(stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_error.toString(), getString(R.string.popup_error)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(), getString(R.string.popup_something_went_wrong)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)
                        )
                    )
                }
            }
    }

    private fun callBookMarkApi() {
        try {
            this.let { contentMetaData?.contentId?.let { it1 ->
                viewModel!!.getBookMarkByVideoId(token, it1).observe(it) { getBookmarkResponse: GetBookmarkResponse? ->
                    if (getBookmarkResponse != null && getBookmarkResponse.bookmarks != null) {
                        bookmarkPosition = getBookmarkResponse.bookmarks[0].position.toDouble()
                        initUi()
                    }else{
                        initUi()
                    }
                }
            } }
        } catch (e:Exception) {
            initUi()
        }
    }

    private var combinedStreamingStatus: String? = ""
    private fun checkLiveStatus() {
        viewModel!!.checkLiveStatus(contentMetaData?.contentId!!).observe(this@PlayerActivity) { response ->
            if (response?.data != null) {
                Log.d("checkLiveStatus", "checkLiveStatus: ${response.data.toString()}")
                val streamStatus = StreamStatus.valueOf(response.data!!.streamStatus !!)
                val eventStatus = EVENT_STATUS.valueOf(response.data!!.eventStatus !!)
                combinedStreamingStatus = AppCommonMethod.getStreamStatus(streamStatus, eventStatus).toString()
                println("Combined Status: $combinedStreamingStatus")
                val message = getValueString(combinedStreamingStatus!!)
                if (combinedStreamingStatus.equals(AppConstants.ERROR) || combinedStreamingStatus.equals(AppConstants.END)) {
                    streamingStatusEnd = true
                    commonDialog(
                        message,
                        "",
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                            getString(R.string.ok)
                        )
                    )
                }
            }
        }
    }

    @Suppress("DEPRECATION")
    private fun hideSystemUI() {
        supportActionBar?.hide()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window.setDecorFitsSystemWindows(false)
            val controller = window.insetsController
            controller?.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
            controller?.systemBarsBehavior =
                WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        } else {
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION)
        }
    }


    private fun getValueString(value: String): String {
        return when (value) {
            AppConstants.SCHEDULED -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.MATCH_START_ON.toString(), getString(R.string.MATCH_START_ON))
            AppConstants.NOT_ATTENDED -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.MATCH_NOT_ATTENDED.toString(), getString(R.string.MATCH_NOT_ATTENDED))
            AppConstants.READY -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.MATCH_READY.toString(), getString(R.string.MATCH_READY))
            AppConstants.END -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.MATCH_END.toString(), getString(R.string.MATCH_END))
            AppConstants.ERROR -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.STREAM_ERROR.toString(), getString(R.string.STREAM_ERROR))
            AppConstants.ABOUT_END -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.MATCH_ABOUT_END.toString(), getString(R.string.MATCH_ABOUT_END))
            else -> stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.STREAM_ERROR.toString(), getString(R.string.STREAM_ERROR))
        }
    }

    override fun onUserLeaveHint() {
        super.onUserLeaveHint()
        val intent = Intent("KEY_EVENT_TAG").apply { putExtra(EnveuPlayer.ON_USER_LEAVE_HINT, true) }
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
    }
}




