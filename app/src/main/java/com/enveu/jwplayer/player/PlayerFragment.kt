package com.enveu.jwplayer.player

import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.ViewModelProvider
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.OttApplication
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.detail.viewModel.DetailViewModel
import com.enveu.activities.device_management.DeviceManagementViewModel
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.client.bookmarking.bean.GetBookmarkResponse
import com.enveu.databinding.PlayerFragmentLayoutBinding
import com.enveu.enums.EVENT_STATUS
import com.enveu.enums.StreamStatus
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.fragments.dialog.CommonDialogFragment.Companion.newInstanceWithCancelBtn
import com.enveu.player.EnveuPlayer
import com.enveu.player.VideoMetadata
import com.enveu.player.base.BasePlayerFragment
import com.enveu.player.jwplayer.listener.JwPlayerListener
import com.enveu.player.model.Progress
import com.enveu.player.model.TrackItem
import com.enveu.utils.Logger
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import org.json.JSONObject

class PlayerFragment : BasePlayerFragment() ,CommonDialogFragment.EditDialogListener,SeriesDetailActivity.PlayerCallback {
    private var isLogin: String? = null
    private var binding: PlayerFragmentLayoutBinding?= null
    private var contentTitle: String? = null
    private var enveuPlayer: EnveuPlayer?= null
    private var mediaType: String? = null
    private var isLive: Boolean? = null
    private var bookmarkingViewModel: BookmarkingViewModel? = null
    private var countDownTimer: CountDownTimer? = null
    private var detailViewModel: DetailViewModel? = null
    private var combinedStreamingStatus: String? = ""
    private var bookmarkPosition = 0.0
    private var contentID: Int? = 0
    private var externalRefId: String? = ""
    private var preference: KsPreferenceKeys? = KsPreferenceKeys.getInstance()
    var seasonEpisodesList: List<EnveuVideoItemBean>? = null
    private var maxAllowedConcurrencyLimit = 1
    private var currentPlayingIndex: Int? = -1
    private var playbackUrl: String? = null
    private var deviceManagementViewModel : DeviceManagementViewModel?= null
    private var isUserVerified: String? = null
    private var isLoggedIn = false
    private var viewModel: DetailViewModel? = null
    private var detailsPlayerCallBack : DetailsPlayerCallBack? = null
    private var isUserNotVerify = false
    private var startSessionApiStart = false
    private var isUserNotEntitle = false
    private val stringsHelper by lazy { StringsHelper }
    private var isGeoBlocking = false
    private var streamingStatusEnd = false
    private var isBingeWatchEnable: Boolean? = false
    private var resEntitle: ResponseEntitle? = null
    private var isDrmDisabled: Boolean? = false
    private var jwLicenseKey: String? = ""
    private var token: String? = null


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return PlayerFragmentLayoutBinding.inflate(inflater, container, false).run {
            binding = this
            root
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        preference = KsPreferenceKeys.getInstance()
        token = preference?.appPrefAccessToken
        isLogin = preference?.appPrefLoginStatus
        if (preference?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
        }
        isUserVerified = preference?.isVerified
        viewModel = ViewModelProvider(this)[DetailViewModel::class.java]
        deviceManagementViewModel = ViewModelProvider(this)[DeviceManagementViewModel::class.java]
        bookmarkingViewModel = ViewModelProvider(this)[BookmarkingViewModel::class.java]

//        maxAllowedConcurrencyLimit = if (preference?.planMaxAllowedConcurrency != null) {
//            preference?.planMaxAllowedConcurrency!!
//        } else {
//            preference?.configMaxAllowedConcurrency!!
//        }

        val args = arguments
        if (args != null) {
            playbackUrl = args.getString("contentUrl")
            contentTitle = args.getString("contentTitle")
            mediaType = args.getString("mediaType")
            isLive = args.getBoolean("IsLive")
            contentID = args.getInt("contentID")
            contentType = args.getString("contentType")
            bookmarkingEnabled = args.getBoolean("bookmarkingEnabled")
            isDrmDisabled = args.getBoolean("isDrmDisabled")
           // widevineUrl = args.getString("widevineUrl")
          //  fileUrl = args.getString("fileUrl")

            try {
                if (args.getSerializable("episodeList") as List<EnveuVideoItemBean> != null) {
                    seasonEpisodesList = args.getSerializable("episodeList") as List<EnveuVideoItemBean>

                    if (seasonEpisodesList!!.isNotEmpty()) {
                        isBingeWatchEnable = true
                        currentPlayingIndex = getCurrentPlayingIndex(seasonEpisodesList!!)
                    }

                }
            } catch (e: Exception) {
                Logger.w(e)
            }
        }
        jwLicenseKey = AppCommonMethod.getDrmKeyValue(isDrmDisabled!!)
        callBookMarkApi()
        if (activity is SeriesDetailActivity) {
            (activity as SeriesDetailActivity).playerCallbackInstance(this)
        }
    }

    private fun uiInit() {
        binding?.pBar?.visibility = View.VISIBLE
        if (preference?.appPrefLoginStatus.equals(
                AppConstants.UserStatus.Login.toString(), ignoreCase = true
            )
        ) {
            isLoggedIn = true
        }
        bookmarkingViewModel = ViewModelProvider(this)[BookmarkingViewModel::class.java]
        detailViewModel = ViewModelProvider(this)[DetailViewModel::class.java]

        if (mediaType.equals(AppConstants.EPISODES,ignoreCase = true) || mediaType.equals(AppConstants.Movie,ignoreCase = true) && preference?.concurrencyEnable == true  && isLogin.equals(
                AppConstants.UserStatus.Login.toString(), ignoreCase = true))
        {
            deviceCheckStatus()
        } else {
            initPlayer()
        }
    }

    override fun pause() {
    }

    override fun play() {
        Log.d("play", "play: ")
    }

    override fun playDrmVideoFromUri(videoUri: String, seekToPosition: Long) {

    }

    override fun playVideoFromId(videoId: String, seekToPosition: Long) {

    }

    override fun playVideoFromUri(videoUri: String, seekToPosition: Long) {

    }

    override fun releasePlayer() {

    }

    override fun seekJwPlayerTo(position: Double) {

    }

    override fun seekPlayerTo(position: Long) {

    }

    override fun showMore(show: Boolean) {

    }

    override fun showNext(show: Boolean) {

    }

    override fun showPrevious(show: Boolean) {

    }

    override fun toggleControlOrientation(orientation: Int) {

    }

    private val playerListener = object : JwPlayerListener() {
        override fun onPlay() {
            // AnalyticsUtils.logPlayerEvent(this@PlayerActivity,AppConstants.CONTENT_PLAY,contentMetaData.contentId,contentMetaData.contentTitle,contentMetaData.contentType,"","")
            super.onPlay()
            Log.d("onPlay", "onPlay: ")
        }

//        override fun onError(error: String) {
//            super.onError(error)
//            if (startSessionApiStart){
//                stopPlaySessionID()
//            }
//            detailsPlayerCallBack?.onPlayerError()
//        }
//
//        override fun onComplete() {
//            super.onComplete()
//            if (isBingeWatchEnable!!) {
//                if (startSessionApiStart){
//                    stopPlaySessionID()
//                }
//                playNextEpisode()
//            } else {
//                if (startSessionApiStart){
//                    stopPlaySessionID()
//                }
//                detailsPlayerCallBack?.onCompleted()
//            }
//
//        }
//
//        override fun onBuffer() {
//            Log.d("onBuffer", "onBuffer: ")
//        }


        override fun onPause() {
            //  AnalyticsUtils.logPlayerEvent(this@PlayerActivity,AppConstants.CONTENT_PAUSE,contentMetaData.contentId,contentMetaData.contentTitle,contentMetaData.contentType,"","")
            super.onPause()
        }

        override fun onFullscreenClick() {
            super.onFullscreenClick()
            val params: ViewGroup.LayoutParams = binding?.contentPlayer?.layoutParams!!
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            params.height = ViewGroup.LayoutParams.MATCH_PARENT
            binding?.contentPlayer?.requestLayout()
        }

        private fun fixOrientation(orientation: Int) {
            requireActivity().requestedOrientation = orientation
        }
   
        override fun onBackClick() {
            super.onBackClick()
            if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                fixOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT)
                if (startSessionApiStart){
                    stopPlaySessionID()
                }
            } else {
                if (startSessionApiStart){
                    stopPlaySessionID()
                }
                requireActivity().finish()
            }
        }

        override fun onBingeWatchCalled() {
            super.onBingeWatchCalled()
            playNextEpisode()
        }

        override fun onBookmark(progress: Progress) {
            super.onBookmark(progress)
            if (!contentType.equals(AppConstants.Trailer) || !contentType.equals(AppConstants.CLIP) || !contentType.equals(
                    AppConstants.REPLAY
                )
            ) {
                if (isLive == true || contentType.equals(AppConstants.MATCHES, ignoreCase = true)) {
                    checkLiveStatus()
                } else {
                    val i: Int = progress.position.toInt()
                    if (isLoggedIn) {
                        contentID?.let {
                            bookmarkingViewModel?.bookmarkVideo(
                                preference?.appPrefAccessToken, it, i
                            )
                        }
                    }
                }
            }
        }

        override fun onBookmarkFinish(progress: Progress) {
            super.onBookmarkFinish(progress)
            if (!contentType.equals(AppConstants.Trailer) || !contentType.equals(AppConstants.CLIP) || !contentType.equals(
                    AppConstants.REPLAY
                )
            ) {
                if (isLive == false || !contentType.equals(
                        AppConstants.MATCHES, ignoreCase = true
                    )
                ) {
                    if (isLoggedIn) {
                        bookmarkingViewModel?.finishBookmark(
                            preference?.appPrefAccessToken, contentID!!
                        )
                    }
                }
            }
        }
    }



    private  fun callBookMarkApi() {
        try {
            this.let { contentID?.let { it1 ->
                viewModel!!.getBookMarkByVideoId(preference?.appPrefAccessToken, it1).observe(requireActivity()) { getBookmarkResponse: GetBookmarkResponse? ->
                    if (getBookmarkResponse != null && getBookmarkResponse.bookmarks != null) {
                        bookmarkPosition = getBookmarkResponse.bookmarks[0].position.toDouble()
                        uiInit()
                    }else{
                        uiInit()
                    }
                }
            } }
        } catch (e:Exception) {
            uiInit()
        }
    }

    private fun checkLiveStatus() {
        detailViewModel?.checkLiveStatus(contentID!!)?.observe(requireActivity()) { response ->
            if (response?.data != null) {
                Log.d("checkLiveStatus", "checkLiveStatus: ${response.data.toString()}")
                val streamStatus = StreamStatus.valueOf(response.data!!.streamStatus!!)
                val eventStatus = EVENT_STATUS.valueOf(response.data!!.eventStatus!!)
                combinedStreamingStatus =
                    AppCommonMethod.getStreamStatus(streamStatus, eventStatus).toString()
                println("Combined Status: $combinedStreamingStatus")
                val message = getValueString(combinedStreamingStatus!!)
                if (combinedStreamingStatus.equals(AppConstants.ERROR) || combinedStreamingStatus.equals(
                        AppConstants.END
                    )
                ) {
                    streamingStatusEnd = true
                    commonDialog(
                        message, "", stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_ok.toString(),
                            getString(R.string.ok)
                        )
                    )
                }
            }
        }
    }
    private fun getBingeWatchJsonObject(): JSONObject {
        Log.d("isBingeWatchEnable", "getBingeWatchJsonObject: $isBingeWatchEnable")
        val jsonObject = JSONObject()
        jsonObject.put(AppConstants.TIMER, 30)
        jsonObject.put(AppConstants.BINGE_WATCH_POSITION, 0L)
        if (mediaType.equals(AppConstants.EPISODES,ignoreCase = true) && currentPlayingIndex!! < seasonEpisodesList?.size!! - 1){
            jsonObject.put(AppConstants.BINGE_WATCHING_ENABLED, true)
        }else{
            jsonObject.put(AppConstants.BINGE_WATCHING_ENABLED, false)
        }

        return jsonObject
    }
    private fun initPlayer() {
        if (enveuPlayer!=null) {
            enveuPlayer?.releasePlayer()
        }
        val videoMetadata = VideoMetadata()
        videoMetadata.videoUrl = playbackUrl.toString()
        videoMetadata.title = contentTitle!!
        videoMetadata.mediaType = EnveuPlayer.VOD

        enveuPlayer =
            EnveuPlayer.Builder().playerType(EnveuPlayer.PLAYER_BRIGHTCOVE).playerListener(playerListener)
                .bcovePolicyKey("BCpkADawqM212bA_YQ7KJXf_oS9dkYzAVlPzOa_P7YqOcRXquscsP5P5Q1Oo9iSYsPFCyrm0wl1FYtfUUGAdxlRLNgNnnX1xyNNAK-uTSiK43051O7bzKdEn0U3F6mCxLyfVIobLT8AECdT2QEC9g-aQLx2o2M3gSYGuow")
               // .jwLicenseKey(jwLicenseKey!!)
               // .isDrmDisabled(isDrmDisabled!!)
               // .widevineUrl(widevineUrl)
              //  .fileUrl(fileUrl)
                .bcoveAccountId("*************")
                .videoMetadata(videoMetadata)
                .bingeWatchJson(getBingeWatchJsonObject())
                .seekToPosition(0L)
                .playerControlTimeout(5_000)
                .isTvPlayer(false)
                .setViewType(EnveuPlayer.HOTSTAR_PLAYER_VIEW_TYPE)
                .videoQuality(getPLayerQuality())
                .showVideoQuality(true)
                .setBookmarkEnabled(bookmarkingEnabled)
               // .setBookmarkPosition(bookmarkPosition)
                .autoPortrait(true)
                .selectedLanguage("English")
                .contentType(mediaType!!)
                .setBookmarkPercentValue(10)
                .setBookmarkFinishPercentValue(95)
                .setBookmarkInterval(10)
                .isVideoQualityAvailable(true).build()

        val transaction = requireActivity().supportFragmentManager.beginTransaction()
        transaction.replace(R.id.content_player, enveuPlayer?.getPlayerView()!!, "EnveuPlayer")
        transaction.addToBackStack(null)
        transaction.commit()

        if (startSessionApiStart) {
            startSessionUpdateTimer()
        }
    }
    private fun shouldShowBingeWatch(size: Int?): Boolean {
        var shouldBingeWatchShow: Boolean? = false
        currentPlayingIndex = currentPlayingIndex!! + 1
        Log.d("currentPlayingIndex", "size: $size")
        Log.d("currentPlayingIndex", "getCurrentPlayingIndex: $currentPlayingIndex")
        if (size != null) {
            shouldBingeWatchShow = currentPlayingIndex!! < size
        }
        if (!shouldBingeWatchShow!!) {
            isBingeWatchEnable = false
        }
        return shouldBingeWatchShow
    }


    private fun getPLayerQuality(): Int {

        if (preference?.qualityName == "SD") {
            return EnveuPlayer.QUALITY_LOW
        } else if (preference?.qualityName == "HD") {
            return EnveuPlayer.QUALITY_MEDIUM
        } else if (preference?.qualityName == "Full HD") {
            return EnveuPlayer.QUALITY_HIGH
        } else {
            return EnveuPlayer.QUALITY_AUTO
        }
    }

    fun reloadPlayer(
        contentURL: String,
        contentTitle: String,
        mediaType: String,
        isLive: Boolean,
        contentID: Int,
        contentType: String,
        bookmarkingEnabled: Boolean,
        drmDisabled: Boolean,
        widevineUrl: String,
        fileUrl: String
    ) {
        enveuPlayer?.releasePlayer()
        this.playbackUrl = contentURL
        this.contentTitle = contentTitle
        this.mediaType = mediaType
        this.isLive = isLive
        this.contentID = contentID
        this.contentType = contentType
        this.bookmarkingEnabled = bookmarkingEnabled
        this.isDrmDisabled = drmDisabled
      //  this.widevineUrl = widevineUrl
      //  this.fileUrl = fileUrl
        jwLicenseKey = AppCommonMethod.getDrmKeyValue(this.isDrmDisabled!!)
        callBookMarkApi()
    }
    override fun changeTrack(trackItem: TrackItem, type: String, position: Int) {
    }

    override fun checkNetworkConnectivity(isConnected: Boolean) {
    }
    private fun checkGeoBlocking() {
        viewModel!!.getGeoBlocking(contentID.toString())
            .observe(requireActivity()) { response ->
                if (response != null && response.data != null) {
                    if(response.data.isIsBlocked) {
                        isGeoBlocking = true
                    }
                } else {
                    commonDialog(stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_error.toString(), getString(R.string.popup_error)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(), getString(R.string.popup_something_went_wrong)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue)
                        )
                    )
                }
            }
    }

    private var signedUrl = ""
    private var drmBaseUrl = ""
    private var drmToken = ""
    private fun playNextEpisode() {
        if (isLoggedIn) {
            if (shouldShowBingeWatch(seasonEpisodesList?.size)) {
                if (seasonEpisodesList != null && seasonEpisodesList!!.isNotEmpty()) {
                    val songListSize = seasonEpisodesList!!.size
                    for (i in 0 until songListSize) {
                        val id = seasonEpisodesList!![i].id
                        if (songListSize != null) {
                            if (id == contentID && i < songListSize - 1) {
                                com.enveu.player.utils.Logger.d("id: $contentID")
                                 val nextAudioItem = seasonEpisodesList?.get(i + 1)
                                 this.contentID = nextAudioItem?.id!!
                                 this.playbackUrl = SDKConfig.getInstance().playbacK_URL + nextAudioItem.externalRefId + ".m3u8"
                                jwLicenseKey = AppCommonMethod.getDrmKeyValue(nextAudioItem.drmDisabled)
                                checkGeoBlocking()
                                if (isGeoBlocking) {
                                    commonDialog(
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.geo_blocking_title.toString(),
                                            getString(R.string.geo_blocking_title)
                                        ),
                                        "",
                                        stringsHelper.stringParse(
                                            stringsHelper.instance()?.data?.config?.popup_ok.toString(), getString(R.string.ok)
                                        )
                                    )
                                }else {
                                    if (nextAudioItem.isPremium) {
                                        checkEntitlement(KsPreferenceKeys.getInstance().appPrefAccessToken,
                                            nextAudioItem.sku, nextAudioItem
                                        )
                                    } else {
                                        if (isUserVerified.equals("true", ignoreCase = true)) {
                                            if (!nextAudioItem.drmDisabled) {
                                                viewModel?.getLicenseUrl("",nextAudioItem.sku,AppConstants.JW_DRM_KEY)?.observe(this){
                                                    if (it != null){
                                                        signedUrl = it.toString()
                                                        drmBaseUrl = signedUrl.substringBefore("?")
                                                        drmToken = signedUrl.substringAfter("token=")
                                                        callJwApiToGetWidevineUrl(drmBaseUrl,drmToken)
                                                    }else{
                                                        commonDialog(
                                                            stringsHelper.stringParse(
                                                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                                                getString(R.string.popup_error)
                                                            ),
                                                            stringsHelper.stringParse(
                                                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                                                getString(R.string.popup_something_went_wrong)
                                                            ),
                                                            stringsHelper.stringParse(
                                                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                                                getString(R.string.popup_continue)
                                                            )
                                                        )
                                                    }
                                                }

                                            } else {
                                                if (null != nextAudioItem.externalRefId && !nextAudioItem.externalRefId.equals("", ignoreCase = true)) {
                                                    playbackUrl = SDKConfig.getInstance().playbacK_URL + nextAudioItem.externalRefId + ".m3u8"
                                                }
                                                contentTitle = nextAudioItem.title
                                                isLive = false
                                                contentType = nextAudioItem.assetType
                                                if (mediaType.equals(AppConstants.EPISODES,ignoreCase = true) || mediaType.equals(AppConstants.Movie,ignoreCase = true) && preference?.concurrencyEnable == true  && isLogin.equals(
                                                        AppConstants.UserStatus.Login.toString(), ignoreCase = true))
                                                {
                                                    deviceCheckStatus()
                                                } else {
                                                    initPlayer()
                                                }                                          }
                                            if (null != nextAudioItem.externalRefId && !nextAudioItem.externalRefId.equals("", ignoreCase = true)) {
                                                playbackUrl = SDKConfig.getInstance().playbacK_URL + nextAudioItem.externalRefId + ".m3u8"
                                            }
                                            contentTitle = nextAudioItem.title
                                            isLive = false
                                            contentType = nextAudioItem.assetType

                                        } else {
                                            isUserNotVerify = true
                                            commonDialog(
                                                "",
                                                stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(), getString(R.string.popup_user_not_verify)),
                                                stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_verify.toString(), getString(R.string.popup_verify))
                                            )
                                        }
                                    }
                                }

                                break
                            }
                        }
                    }

                }
            } else {
                if (enveuPlayer != null)
                    enveuPlayer?.releasePlayer()
            }
        }else{
            ActivityLauncher.getInstance()
                .loginActivity(requireActivity(), ActivityLogin::class.java, "")
        }
    }

    private fun callJwApiToGetWidevineUrl(signedUrl: String, drmToken: String) {
        viewModel?.getWidevineUrl(signedUrl,drmToken)?.observe(this){
          //  widevineUrl = it?.playlist?.get(0)?.sources?.get(0)?.drm?.widevine?.url.toString()
           // fileUrl = it?.playlist?.get(0)?.sources?.get(0)?.file.toString()
          //  if (widevineUrl != "" && fileUrl != "") {
                if (mediaType.equals(AppConstants.EPISODES,ignoreCase = true) || mediaType.equals(AppConstants.Movie,ignoreCase = true) && preference?.concurrencyEnable == true  && isLogin.equals(
                        AppConstants.UserStatus.Login.toString(), ignoreCase = true))
                {
                    deviceCheckStatus()
                } else {
                    initPlayer()
                }
            }
        }
  //  }




    private fun checkEntitlement(token: String?, sku: String?, nextAudioItem: EnveuVideoItemBean) {
        viewModel?.hitApiEntitlement(token, sku)?.observe(requireActivity()) { responseEntitle ->
            // binding!!.progressBar.visibility = View.GONE
            if (null != responseEntitle && null != responseEntitle.data) {
                resEntitle = responseEntitle
                maxAllowedConcurrencyLimit = responseEntitle.data.maxAllowedConcurrency
                if (responseEntitle.data.entitled) {
                    if (isUserVerified.equals("true", ignoreCase = true)) {
                        viewModel?.externalRefID(responseEntitle.data.accessToken,responseEntitle.data.sku)?.observe(this){
                            playbackUrl = SDKConfig.getInstance().playbacK_URL + it?.data?.externalRefId + ".m3u8"
                            contentTitle = nextAudioItem.title
                            isLive = false
                            contentType = nextAudioItem.assetType
                            if (mediaType.equals(AppConstants.EPISODES,ignoreCase = true) || mediaType.equals(AppConstants.Movie,ignoreCase = true) && preference?.concurrencyEnable == true  && isLogin.equals(
                                    AppConstants.UserStatus.Login.toString(), ignoreCase = true))
                            {
                                deviceCheckStatus()
                            } else {
                                initPlayer()
                            }
                        }

                    } else {
                        isUserNotVerify = true
                        commonDialog(
                            "",
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_user_not_verify.toString(),
                                getString(R.string.popup_user_not_verify)
                            ),
                            stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_verify.toString(), getString(R.string.popup_verify))
                        )
                    }
                } else {
                    isUserNotEntitle = true
                    commonDialogWithCancel(
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(), getString(R.string.popup_notEntitled)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_select_plan.toString(), getString(R.string.popup_select_plan)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_purchase.toString(), getString(R.string.popup_purchase)),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_cancel.toString(), getString(R.string.popup_cancel))
                    )
                }
            } else {
                if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                    //  clearCredientials(preference)
                    ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin::class.java, "")
                } else {
                    commonDialog(
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_error.toString(), getString(R.string.popup_error)),
                        stringsHelper.stringParse(
                            stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(), getString(R.string.popup_something_went_wrong)
                        ),
                        stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_continue.toString(), getString(R.string.popup_continue))
                    )
                }
            }
        }
    }



    override fun onActionBtnClicked() {
    }

    override fun onCancelBtnClicked() {
    }

    private fun commonDialog(title: String, description: String, actionBtn: String) {
        val fm = requireActivity().supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun commonDialogWithCancel(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = requireActivity().supportFragmentManager
        val commonDialogFragment = newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }

    private fun getValueString(value: String): String {
        return when (value) {
            AppConstants.SCHEDULED -> stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.MATCH_START_ON.toString(),
                getString(R.string.MATCH_START_ON)
            )

            AppConstants.NOT_ATTENDED -> stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.MATCH_NOT_ATTENDED.toString(),
                getString(R.string.MATCH_NOT_ATTENDED)
            )

            AppConstants.READY -> stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.MATCH_READY.toString(),
                getString(R.string.MATCH_READY)
            )

            AppConstants.END -> stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.MATCH_END.toString(),
                getString(R.string.MATCH_END)
            )

            AppConstants.ERROR -> stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.STREAM_ERROR.toString(),
                getString(R.string.STREAM_ERROR)
            )

            AppConstants.ABOUT_END -> stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.MATCH_ABOUT_END.toString(),
                getString(R.string.MATCH_ABOUT_END)
            )

            else -> stringsHelper.stringParse(
                stringsHelper.instance()?.data?.config?.STREAM_ERROR.toString(),
                getString(R.string.STREAM_ERROR)
            )
        }
    }

    private fun deviceCheckStatus() {
        var activeDeviceID = false
        deviceManagementViewModel?.getAllDevices(
            0,
            "ACTIVE",
            preference?.appPrefAccessToken
        )?.observe(viewLifecycleOwner) {
            if (it.responseCode == 2000) {
                for (item in it.data?.items!!) {
                    if (item.deviceId == AppCommonMethod.getDeviceId(OttApplication.instance!!.contentResolver)) {
                        activeDeviceID = true
                        break
                    }
                }
                if (activeDeviceID) {
                    startSessionPlayback()
                }
            } else {
                showConcurrencyDialog(
                    this.resources.getString(R.string.error),
                    resources.getString(R.string.something_went_wrong),
                    resources.getString(R.string.ok)
                )
            }
        }
    }


    private fun startSessionUpdateTimer() {
        countDownTimer = object : CountDownTimer(
            Long.MAX_VALUE,
            preference?.updatePlaybackSessionTime?.toLong()!! * 1000
        ) {
            override fun onTick(millisUntilFinished: Long) {
                Log.d("Harsh","Update")
                if (startSessionApiStart) {
                    preference?.appPrefAccessToken?.let { token ->
                        preference?.sessionId?.let { sessionID ->
                            deviceManagementViewModel?.updatePlaybackSession(
                                token,
                                sessionID
                            )?.observe(viewLifecycleOwner) {
                                if (it != null) {
                                    when (it.responseCode) {
                                        2000 -> {}
                                        4098 -> {
                                            stopSessionUpdateTimer()
                                            enveuPlayer?.pause()
                                            showConcurrencyDialog(
                                                "",
                                                resources.getString(R.string.no_season_found_this_session_id),
                                                resources.getString(R.string.ok)
                                            )
                                        }

                                        4302 -> {
                                            stopSessionUpdateTimer()
                                            enveuPlayer?.pause()
                                            showConcurrencyDialog(
                                                "",
                                                resources.getString(R.string.user_login_limit),
                                                resources.getString(R.string.ok)
                                            )
                                        }

                                        else -> {
                                            stopSessionUpdateTimer()
                                            enveuPlayer?.pause()
                                            showConcurrencyDialog(
                                                "",
                                                resources.getString(R.string.something_went_wrong),
                                                resources.getString(R.string.ok)
                                            )
                                        }
                                    }
                                } else {
                                    stopSessionUpdateTimer()
                                    enveuPlayer?.pause()
                                    showConcurrencyDialog(
                                        "",
                                        resources.getString(R.string.something_went_wrong),
                                        resources.getString(R.string.ok)
                                    )
                                }
                            }
                        }
                    }
                }
            }

            override fun onFinish() {
                // Perform any final actions here if needed
            }
        }

        // Start the countdown timer
        countDownTimer?.start()
    }


    private fun stopSessionUpdateTimer() {
        countDownTimer?.cancel()
        countDownTimer = null
    }


    private fun stopPlaySessionID(isStartPlay : Boolean?=null) {
        preference?.appPrefAccessToken?.let { token ->
            preference?.sessionId?.let { sessionId ->
                deviceManagementViewModel?.stopPlaybackSession(token, sessionId)
                   ?.observe(viewLifecycleOwner) {
                        if (it != null) {
                            stopSessionUpdateTimer()
                            if (it.responseCode == 2000) {
                                preference?.sessionId = ""
                                if (isStartPlay == true){
                                    startSessionPlayback()
                                }
                            }else{
                                showConcurrencyDialog("",resources.getString(R.string.something_went_wrong),"ok")
                            }
                        }else{
                            showConcurrencyDialog("",resources.getString(R.string.something_went_wrong),"ok")
                        }
                    }
            }
        }
    }

    private fun showConcurrencyDialog(title: String, description: String, actionBtn: String) {
        try {
            var fm: FragmentManager = requireActivity().supportFragmentManager
            var commonDialogFragment = CommonDialogFragment.newInstance(title, description, actionBtn)
            commonDialogFragment.setEditDialogCallBack(this)
            commonDialogFragment.show(fm, AppConstants.MESSAGE)
        }catch (e :Exception){
            e.printStackTrace()
        }

    }

    private fun startSessionPlayback() {
        if (!preference?.sessionId.isNullOrEmpty()){
            stopPlaySessionID(true)
        }else {
            deviceManagementViewModel?.startSessionPlayBack(
                preference?.appPrefAccessToken!!,
                contentID.toString(),
                contentTitle!!
                )?.observe(viewLifecycleOwner) {
                    when {
                        it != null && it.responseCode == 2000 -> {
                            startSessionApiStart = true
                            preference?.sessionId = it.data?.sessionId ?: ""
                            initPlayer()
                        }
                        it.responseCode == 4099 -> {
                            enveuPlayer?.pause()
                            showConcurrencyDialog(
                                resources.getString(R.string.screen_limit),
                                resources.getString(R.string.You_can_play)+" $maxAllowedConcurrencyLimit "+resources.getString(R.string.screen_at_a_time_Please_stop_playing_another_screen),
                                resources.getString(R.string.ok)
                            )
                        }
                        else -> {
                            enveuPlayer?.pause()
                            showConcurrencyDialog(
                                "",
                                resources.getString(R.string.something_went_wrong),
                                resources.getString(R.string.ok)
                            )
                        }
                    }
                }
        }
    }


    private fun getCurrentPlayingIndex(seasonEpisodesList: List<EnveuVideoItemBean>): Int? {
        var currentPlayingIndex: Int? = 0
        val total = seasonEpisodesList.size
        for (i in 0 until total) {
            val id = seasonEpisodesList[i].id
            if (id == contentID) {
                currentPlayingIndex = i
                break
            }
        }
        Log.d("currentPlayingIndex", "getCurrentPlayingIndex: $currentPlayingIndex")
        return currentPlayingIndex
    }

    fun setDetailsPlayerCallBack(detailsPlayerCallBack: DetailsPlayerCallBack) {
        this.detailsPlayerCallBack = detailsPlayerCallBack
    }
    interface DetailsPlayerCallBack{
        fun onPlayerError()
        fun onCompleted()
    }

    override fun stopPlayer() {
        enveuPlayer?.releasePlayer()
    }
}