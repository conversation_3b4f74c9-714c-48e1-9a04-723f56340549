package com.enveu.jwplayer.player.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.databinding.ItemAllChannelsLayoutBinding
import com.enveu.epg.models.EPGSchedule
import com.enveu.jwplayer.player.EpgScheduleItemClickListener
import com.enveu.utils.Constants
import com.enveu.utils.getImageUrl
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.setImageWithGlide


class LiveChannelPlayerAdapter(
    private val epgSchedulesList: List<EPGSchedule>?,
    private val epgScheduleItemClickListener: EpgScheduleItemClickListener?
):RecyclerView.Adapter<LiveChannelPlayerAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemAllChannelsLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return epgSchedulesList?.size?:0
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val epgScheduleItems = epgSchedulesList?.get(position)?.program
        holder.binding.titleText.text = epgScheduleItems?.title
        val finalImages = getImageUrl(Constants.SIXTEEN_INTO_NINE, epgScheduleItems?.images, holder.binding.headerImage.context)
        holder.binding.headerImage.setImageWithGlide(holder.binding.headerImage.context, finalImages)
    }

   inner class ViewHolder(val binding: ItemAllChannelsLayoutBinding): RecyclerView.ViewHolder(binding.root) {
       init {
           binding.parentLayout.setOnClickListener {
               epgScheduleItemClickListener?.onEpgScheduleItemClickListener(bindingAdapterPosition)
           }
       }
    }
}