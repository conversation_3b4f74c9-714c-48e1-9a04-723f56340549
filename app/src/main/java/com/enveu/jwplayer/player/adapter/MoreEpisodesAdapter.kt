package com.enveu.jwplayer.player.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.databinding.ItemAllEpisodesLayoutBinding
import com.enveu.jwplayer.player.EpisodesItemClickListener
import com.enveu.utils.helpers.ImageHelper

class MoreEpisodesAdapter(
    private val allEpisodesList: ArrayList<EnveuVideoItemBean>?,
    private val episodesItemClickListener: EpisodesItemClickListener?
) :RecyclerView.Adapter<MoreEpisodesAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemAllEpisodesLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun getItemCount(): Int {
       return allEpisodesList?.size?:0
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val episodesItems = allEpisodesList?.get(position)

        holder.binding.titleText.text = episodesItems?.title
        holder.binding.descriptionText.text = episodesItems?.description
        ImageHelper.getInstance(holder.binding.headerImage.context).loadListImage(holder.binding.headerImage, episodesItems?.posterURL)
    }

   inner class ViewHolder(val binding: ItemAllEpisodesLayoutBinding):RecyclerView.ViewHolder(binding.root) {
       init {
          binding.parentLayout.setOnClickListener {
              episodesItemClickListener?.onEpisodesItemsClickListener(allEpisodesList?.get(bindingAdapterPosition))
          }
       }
    }
}