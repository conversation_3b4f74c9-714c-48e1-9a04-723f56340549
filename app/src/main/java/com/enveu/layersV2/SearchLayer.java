package com.enveu.layersV2;

import android.content.Context;

import androidx.lifecycle.LiveData;

import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.callbacks.apicallback.ApiResponseModel;
import com.enveu.networking.apiendpoints.ApiInterface;
import com.enveu.networking.apiendpoints.RequestConfig;
import com.enveu.networking.servicelayer.APIServiceLayer;
import com.enveu.utils.commonMethods.AppConfigMethod;

import java.util.List;

public class SearchLayer {

    private static SearchLayer searchLayerInstance;
    private static ApiInterface endpoint;
    ApiResponseModel callBack;

    List<String> searchKeyList;
    private SearchLayer() {

    }

    public static SearchLayer getInstance() {
        if (searchLayerInstance == null) {
            endpoint = RequestConfig.getEnveuClient().create(ApiInterface.class);
            searchLayerInstance = new SearchLayer();
        }
        return (searchLayerInstance);
    }


    public LiveData<List<RailCommonData>> getSearchData(String keyword, int size, int page,boolean isV2Search,boolean isMusicApp) {
        if (searchKeyList == null) {
            searchKeyList = AppConfigMethod.getSearchableKeys();
        }
        if (searchKeyList.size() == 3 ) {
            return APIServiceLayer.getInstance().getSearchData(keyword, size, page, isV2Search, isMusicApp);
        } else if (searchKeyList.size() == 2) {
            return APIServiceLayer.getInstance().getSearchData2(searchKeyList,keyword, size, page, isMusicApp);
        } else if (searchKeyList.size() == 4) {
            return APIServiceLayer.getInstance().getSearchData4(searchKeyList,keyword, size, page, isMusicApp);
        } else {
            return APIServiceLayer.getInstance().getSearchData1(searchKeyList,keyword, size, page, isMusicApp);
        }
    }
    public LiveData<List<RailCommonData>> getSearchData(String keyword, int size, int page,String assetType) {
        return APIServiceLayer.getInstance().getSearchDataForSingleAsset(searchKeyList,keyword,size,page,assetType);
    }

    public LiveData<RailCommonData> getSingleCategorySearch(String keyword, String type, int size, int page, boolean applyFilter,String customContentType,String videoType,String header) {
        return APIServiceLayer.getInstance().getSingleCategorySearch(keyword, type, size, page, applyFilter, customContentType,videoType,header);
    }

    public LiveData<RailCommonData> getProgramSearch(String keyword, int size, int page,boolean applyFilter) {
        return APIServiceLayer.getInstance().getProgramSearch(keyword, size, page, applyFilter);
    }


    public LiveData<List<RailCommonData>> getAllArtist(String keyword, int size, int page) {
        return APIServiceLayer.getInstance().getAllArtist(keyword,size,page);
    }

    public LiveData<List<RailCommonData>> hitGenreData(Context context, String contentType, String customType, int offSet, int size, String trackEvent, String locale) {
        return APIServiceLayer.getInstance().getGenreData(context, contentType, customType, offSet, size, trackEvent, locale);
    }


    public LiveData<List<RailCommonData>> hitGenreDataFilter(Context context, String contentType, String customType, String id, String keyword , int offSet, int size, String trackEvent,String personType, String locale) {
        return APIServiceLayer.getInstance().getGenreDataFilter(context, contentType, customType,id,keyword, offSet, size, trackEvent,personType, locale);
    }

}
