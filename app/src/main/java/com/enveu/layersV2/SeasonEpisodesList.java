package com.enveu.layersV2;

import com.enveu.callbacks.RequestOffferCallBack.RequestOfferCallBack;
import com.enveu.callbacks.apicallback.ApiResponseModel;
import com.enveu.callbacks.likelistCallback.ApiLikeList;
import com.enveu.networking.apiendpoints.ApiInterface;
import com.enveu.networking.apiendpoints.RequestConfig;
import com.enveu.networking.servicelayer.APIServiceLayer;

public class SeasonEpisodesList {

    private static SeasonEpisodesList seasonEpisodesListInstance;
    private static ApiInterface endpoint;



    public static SeasonEpisodesList getInstance() {
        if (seasonEpisodesListInstance == null) {
            endpoint = RequestConfig.getEnveuClient().create(ApiInterface.class);
            seasonEpisodesListInstance = new SeasonEpisodesList();
        }
        return (seasonEpisodesListInstance);
    }

    public void getSeasonEpisodesV2(int seriesId, int pageNumber, int size,String customData, ApiResponseModel listener) {
       new APIServiceLayer().getSeasonEpisodesV2(seriesId, pageNumber, size,customData,listener);
    }

    public void getRoundForSeason(int seriesId, int pageNumber, int size,String customData, ApiResponseModel listener) {
        new APIServiceLayer().getRoundForSeason(seriesId, pageNumber, size,customData,listener);
    }

    public void getMatchForRound(int seriesId, int pageNumber, int size,String customData, ApiResponseModel listener) {
        new APIServiceLayer().getMatchForRound(seriesId, pageNumber, size,customData,listener);
    }

    public void getAllEpisodesV2(int seriesId, int pageNumber, int size, ApiResponseModel listener) {
       new APIServiceLayer().getAllEpisodesV2(seriesId, pageNumber, size,listener);
    }

    public void getLiveEventMatch(int matchId, int pageNumber, int size, ApiResponseModel listener) {
        new APIServiceLayer().getLiveEventMatch(matchId, pageNumber, size,listener);
    }

    public void getIsLikeGOI(String token,int pageNumber, int size, ApiLikeList listener) {
        APIServiceLayer.getInstance().getIsLikeGOI(token, pageNumber, size,listener);
    }



    public void getRelatedContent(int pageNumber, int size,String contentType,int id, ApiResponseModel listener) {
        new APIServiceLayer().getRelatedContent(pageNumber,size,contentType,id,listener);
    }

    public void getRecommendedSongs(int pageNumber, int size,String contentType,String sortBy, ApiResponseModel listener) {
        new APIServiceLayer().getRecommendedSongs(pageNumber,size,contentType,sortBy,listener);
    }


    public void getCommonListAll(int pageNumber, int size,String interViewAssetId,String customData, ApiResponseModel listener) {
        new APIServiceLayer().getCommonListAll(pageNumber,size,interViewAssetId,customData,listener);
    }

    public void getRequestedOfferForUser(int page, int size, String token, int userId, String offers, String currentLanguageCode, RequestOfferCallBack requestOfferCallBack) {
        APIServiceLayer.getInstance().getRequestedOfferForUser(page, size, token,userId,offers,currentLanguageCode,requestOfferCallBack);
    }
}
