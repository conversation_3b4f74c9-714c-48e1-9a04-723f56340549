package com.enveu.layersV2;

import android.util.Log;

import androidx.annotation.NonNull;

import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModelV3.videoDetailsV2.EnveuVideoDetailsBean;
import com.enveu.callbacks.apicallback.ApiResponseModel;
import com.enveu.callbacks.commonCallbacks.CommonApiCallBack;
import com.enveu.client.baseCollection.baseCategoryServices.BaseCategoryServices;
import com.enveu.client.videoDetail.beanv3_0.list.EnvSongDetail;
import com.enveu.client.videoDetail.beanv3_0.mediaContentList.EnvMediaContentList;
import com.enveu.client.videoDetail.callBacks.EnvVideoContentResponse;
import com.enveu.networking.apiendpoints.ApiInterface;
import com.enveu.networking.apiendpoints.RequestConfig;
import com.enveu.networking.errormodel.ApiErrorModel;
import com.enveu.networking.servicelayer.APIServiceLayer;
import com.enveu.utils.Logger;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.config.LanguageLayer;
import com.google.gson.Gson;

import java.util.Objects;

import retrofit2.Response;

public class VideoDetailLayer {

    private static VideoDetailLayer videoDetailLayerInstance;
    private static ApiInterface endpoint;
    ApiResponseModel callBack;
    private String languageCode;

    private VideoDetailLayer() {

    }

    public static VideoDetailLayer getInstance() {
        if (videoDetailLayerInstance == null) {
            if (RequestConfig.getEnveuClient() != null){
                endpoint = RequestConfig.getEnveuClient().create(ApiInterface.class);
            }
            videoDetailLayerInstance = new VideoDetailLayer();
        }
        return (videoDetailLayerInstance);
    }


    public void getVideoDetails(String manualImageAssetId, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        Gson gson = new Gson();
        BaseCategoryServices.Companion.getInstance().getEnvVideoDetails(manualImageAssetId, languageCode, new EnvVideoContentResponse<com.enveu.client.videoDetail.beanv2_0.EnvMediaDetail>() {

            @Override
            public void success(boolean status, @NonNull Response<com.enveu.client.videoDetail.beanv2_0.EnvMediaDetail> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getData() != null) {
                        if (response.body().getResponseCode() == 2000) {
                            String json = gson.toJson(response.body());
                            Log.w("playlistCall-->>>>>", json);
                            EnveuVideoDetailsBean er = gson.fromJson(json, EnveuVideoDetailsBean.class);
                            RailCommonData railCommonData = new RailCommonData();
                            AppCommonMethod.getEnvAssetDetail(railCommonData, er);
                            callBack.onSuccess(railCommonData);
                        }
                    }
                } else {
                    ApiErrorModel errorModel = new ApiErrorModel(response.code(), response.message());
                    callBack.onError(errorModel);
                }
            }

            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                ApiErrorModel errorModel = new ApiErrorModel(500, message);
                callBack.onFailure(errorModel);
            }
        });


    }

    public void getVideoDetailsV3(String manualImageAssetId, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        Gson gson = new Gson();
        BaseCategoryServices.Companion.getInstance().getEnvVideoDetailsV3(manualImageAssetId, languageCode, new EnvVideoContentResponse<>() {
            @Override
            public void success(boolean status, @NonNull Response<com.enveu.client.videoDetail.beanv3_0.EnvMediaDetail> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getData() != null) {
                        if (response.body().getResponseCode() == 2000) {
//                            String json = gson.toJson(response.body());
//                            com.enveu.beanModelV3.videoDetailV3.list.EnvMediaDetailList envMediaDetailList = gson.fromJson(json, com.enveu.beanModelV3.videoDetailV3.list.EnvMediaDetailList.class);
//                            callBack.onSuccess(envMediaDetailList);
                            try {
                                String json = gson.toJson(response.body());
                                com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetailsBean er = gson.fromJson(json, com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetailsBean.class);
                                RailCommonData railCommonData = new RailCommonData();
                                AppCommonMethod.getEnvAssetDetail(railCommonData, er);
                                callBack.onSuccess(railCommonData);
                            } catch (Exception e) {
                                Logger.d("checkException", Objects.requireNonNull(e.getMessage()));
                            }

                        }
                    }
                } else {
                    ApiErrorModel errorModel = new ApiErrorModel(response.code(), response.message());
                    callBack.onError(errorModel);
                }
            }

            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                ApiErrorModel errorModel = new ApiErrorModel(500, message);
                callBack.onFailure(errorModel);
            }
        });


    }


    public void getRelatedContentV3(int id, int pageNumber, int pageSize, String contentType, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        Gson gson = new Gson();
        BaseCategoryServices.Companion.getInstance().getRelatedContentV3(id, pageNumber, pageSize, contentType, new EnvVideoContentResponse<EnvSongDetail>() {
            @Override
            public void success(boolean status, @NonNull Response<EnvSongDetail> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getData() != null) {
                        if (response.body().getResponseCode() == 2000) {
                            String json = gson.toJson(response.body());
                            com.enveu.beanModelV3.videoDetailV3.list.EnvSongDetail envMediaDetailList = gson.fromJson(json, com.enveu.beanModelV3.videoDetailV3.list.EnvSongDetail.class);
                            callBack.onSuccess(envMediaDetailList);

                        }
                    }
                } else {
                    ApiErrorModel errorModel = new ApiErrorModel(response.code(), response.message());
                    callBack.onError(errorModel);
                }
            }

            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                ApiErrorModel errorModel = new ApiErrorModel(500, message);
                callBack.onFailure(errorModel);
            }
        });

    }


    public void getEnvMediaContentList(String customData, int pageNumber, int pagesize, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        Gson gson = new Gson();
        BaseCategoryServices.Companion.getInstance().getEnvMediaContentList(customData, pageNumber, pagesize,languageCode, new EnvVideoContentResponse<EnvMediaContentList>() {

            @Override
            public void success(boolean status, @NonNull Response<EnvMediaContentList> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getData() != null) {
                        if (response.body().getResponseCode() == 2000) {
                            String json = gson.toJson(response.body());
                            com.enveu.beanModelV3.videoDetailV3.mediaContentList.EnvMediaContentList envMediaDetailList = gson.fromJson(json, com.enveu.beanModelV3.videoDetailV3.mediaContentList.EnvMediaContentList.class);
                            callBack.onSuccess(envMediaDetailList);
                        }
                    }
                } else {
                    ApiErrorModel errorModel = new ApiErrorModel(response.code(), response.message());
                    callBack.onError(errorModel);
                }
            }

            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                ApiErrorModel errorModel = new ApiErrorModel(500, message);
                callBack.onFailure(errorModel);
            }
        });
    }

    public void getVideoDetailsBySlug(String contentSlug, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        Gson gson = new Gson();

        BaseCategoryServices.Companion.getInstance().getEnvVideoDetailsBySlug(contentSlug, languageCode, "","",new EnvVideoContentResponse<com.enveu.client.videoDetail.beanv3_0.EnvMediaDetail>() {

            @Override
            public void success(boolean status, @NonNull Response<com.enveu.client.videoDetail.beanv3_0.EnvMediaDetail> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getData() != null) {
                        if (response.body().getResponseCode() == 2000) {
                            try {
                                String json = gson.toJson(response.body());
                                com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetailsBean er = gson.fromJson(json, com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetailsBean.class);
                                RailCommonData railCommonData = new RailCommonData();
                                AppCommonMethod.getEnvAssetDetail(railCommonData, er);
                                callBack.onSuccess(railCommonData);
                            }catch (Exception e){
                                Logger.w(e);
                            }
                        }
                    }
                } else {
                    ApiErrorModel errorModel = new ApiErrorModel(response.code(), response.message());
                    callBack.onError(errorModel);
                }
            }

            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                ApiErrorModel errorModel = new ApiErrorModel(500, message);
                callBack.onFailure(errorModel);
            }
        });


    }


    public void getAssetTypeHero(String manualImageAssetId, CommonApiCallBack commonApiCallBack) {
        APIServiceLayer.getInstance().getAssetTypeHero(manualImageAssetId, commonApiCallBack);
    }


}