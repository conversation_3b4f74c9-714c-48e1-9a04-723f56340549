package com.enveu.menuManager

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import com.bumptech.glide.Glide
import com.google.gson.Gson
import com.enveu.R
import com.enveu.appLevelModel.MediaInfo
import com.enveu.appLevelModel.MediaMappingInfo
import com.enveu.menuManager.model.MenuManagerModel
import java.io.IOException
import java.io.InputStream


object MenuCommonFunction {
    fun getParseMenu(context: Context): MenuManagerModel? {
        return try {
            val gson = Gson()
            val colorFile: InputStream = context.assets.open("Menu.json")
            val size: Int = colorFile.available()
            val buffer = ByteArray(size)
            colorFile.read(buffer)
            colorFile.close()
            gson.fromJson(String(buffer), MenuManagerModel::class.java)
        } catch (ex: IOException) {
            ex.printStackTrace()
            null
        }
    }
    @SuppressLint("SuspiciousIndentation")
    fun getParseSearchFilter(context: Context): List<MediaInfo?>?? {
        return try {
            val gson = Gson()
            val colorFile: InputStream = context.assets.open("media-type-mapping.json")
            val size: Int = colorFile.available()
            val buffer = ByteArray(size)
            colorFile.read(buffer)
            colorFile.close()
            val mediaInfo = gson.fromJson(String(buffer), MediaMappingInfo::class.java)
            mediaInfo?.data?.filter { it?.isSearchable == true }

        } catch (ex: IOException) {
            ex.printStackTrace()
            null
        }
    }


    @SuppressLint("UseCompatLoadingForDrawables")
     fun fetchIconDrawableAsync(iconUrl: String?, context: Context): Drawable {
        return if (!iconUrl.isNullOrEmpty()) {
                Glide.with(context)
                    .load(iconUrl)
                    .submit()
                    .get()
        }else{
            context.resources.getDrawable(R.mipmap.ic_launcher)
        }
    }
}