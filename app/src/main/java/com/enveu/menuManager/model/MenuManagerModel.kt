package com.enveu.menuManager.model


import com.google.gson.annotations.SerializedName

data class MenuManagerModel(
    @SerializedName("data")
    val `data`: Data?,
    @SerializedName("debugMessage")
    val debugMessage: Any?, // null
    @SerializedName("responseCode")
    val responseCode: Int? // 2000
) {
    data class Data(
        @SerializedName("appId")
        val appId: String?, // App-olenqpncqr
        @SerializedName("id")
        val id: Int?, // 14
        @SerializedName("name")
        val name: String?, // iOS Mobile App Menu
        @SerializedName("orderedMenuItems")
        val orderedMenuItems: List<OrderedMenuItem?>?
    ) {
        data class OrderedMenuItem(
            @SerializedName("displayOrder")
            val displayOrder: Int?, // 0
            @SerializedName("menuItem")
            val menuItem: MenuItem?
        ) {
            data class MenuItem(
                @SerializedName("customData")
                val customData: CustomData?,
                @SerializedName("subMenu")
                val subMenu: Any?,
                @SerializedName("customValue")
                val customValue: Any?, // null
                @SerializedName("description")
                val description: String?, // Home
                @SerializedName("childMenuItems")
                val childMenuItems : List<OrderedMenuItem?>?,
                @SerializedName("displayName")
                val displayName: String?, // Home
                @SerializedName("icon")
                val icon: String?, // https://d3ffi1kkogkk1a.cloudfront.net/Doxzilla_1646210064598_proj-1646210075817/statics/icons/icon16.png
                @SerializedName("iconIdentifier")
                val iconIdentifier: String?, // icon16
                @SerializedName("id")
                val id: Int?, // 70
                @SerializedName("keywords")
                val keywords: List<Any?>?,
                @SerializedName("menuItemActionType")
                val menuItemActionType: String?, // SCREEN
                @SerializedName("personalizedValue")
                val personalizedValue: String?, // MY_FAVOURITE
                @SerializedName("position")
                val position: String?, // TOP
                @SerializedName("screenId")
                val screenId: String?, // scr-qxcokikric
                @SerializedName("slug")
                val slug: Any?, // null
                @SerializedName("targetingTags")
                val targetingTags: List<Any?>?,
                @SerializedName("type")
                val type: String?, // MAIN
                @SerializedName("isMoreGroup")
                val isMoreGroup : Boolean = false
            ) {
                class CustomData
            }
        }
    }
}