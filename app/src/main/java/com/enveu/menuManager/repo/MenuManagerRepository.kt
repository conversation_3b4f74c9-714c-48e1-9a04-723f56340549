package com.enveu.menuManager.repo

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.enveu.menuManager.MenuCommonFunction
import com.enveu.menuManager.model.MenuManagerModel
import com.enveu.networking.apiendpoints.ApiInterface
import com.enveu.networking.apiendpoints.RequestConfig
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class MenuManagerRepository {
    fun getMenu(local: String, context: Context): LiveData<MenuManagerModel?> {
        val menuMutableLiveData = MutableLiveData<MenuManagerModel?>()

        // Get the parsed menu data from the local JSON file
        val localMenuData = MenuCommonFunction.getParseMenu(context)

        RequestConfig.getMenuManagerClient().create(ApiInterface::class.java)
            .getMenuDetails(local)
            .enqueue(object : Callback<MenuManagerModel> {
                override fun onResponse(
                    call: Call<MenuManagerModel>, response: Response<MenuManagerModel>
                ) {
                    if (response.isSuccessful && response.body() != null) {
                        // Update the local JSON file with the API response data
                        menuMutableLiveData.postValue(response.body())
                    }else{
                        menuMutableLiveData.postValue(localMenuData)
                    }
                }

                override fun onFailure(call: Call<MenuManagerModel>, t: Throwable) {
                    // If API call fails, continue using the local menu data
                    menuMutableLiveData.postValue(localMenuData)
                }
            })

        return menuMutableLiveData
    }
}