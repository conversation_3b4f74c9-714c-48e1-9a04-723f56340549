package com.enveu.menuManager.viewmodel

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.enveu.menuManager.model.MenuManagerModel
import com.enveu.menuManager.repo.MenuManagerRepository

class MenuViewModel : ViewModel() {
    private var menuManagerRepository: MenuManagerRepository = MenuManagerRepository()
    fun getMenuManager(local : String,context : Context): LiveData<MenuManagerModel?> {
        return menuManagerRepository.getMenu(local, context)
    }
}