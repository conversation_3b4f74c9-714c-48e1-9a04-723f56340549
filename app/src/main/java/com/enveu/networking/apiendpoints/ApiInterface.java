package com.enveu.networking.apiendpoints;


import com.enveu.activities.multiplePlaylist.MultiplePlaylistRepo;
import com.enveu.activities.profile.order_history.model.OrderHistoryModel;
import com.enveu.activities.profile.order_history.model.TransactionHistoryModel;
import com.enveu.activities.watchList.model.VideoIdModel;
import com.enveu.beanModel.AssetHistoryContinueWatching.ResponseAssetHistory;
import com.enveu.beanModel.LoginDeviceModel.LoginDeviceModel;
import com.enveu.beanModel.changePassword.ResponseChangePassword;
import com.enveu.beanModel.configBean.ResponseConfig;
import com.enveu.beanModel.connectFb.ResponseConnectFb;
import com.enveu.beanModel.emptyResponse.ResponseEmpty;
import com.enveu.beanModel.enveuCommonRailData.config.EnveuConfigResponse;
import com.enveu.beanModel.mobileAds.ResponseMobileAds;
import com.enveu.beanModel.popularSearch.ResponsePopularSearch;
import com.enveu.beanModel.responseGetWatchlist.ResponseGetIsWatchlist;
import com.enveu.beanModel.responseIsLike.ResponseIsLike;
import com.enveu.beanModel.responseModels.LoginResponse.LoginResponseModel;
import com.enveu.beanModel.responseModels.RegisterSignUpModels.ResponseRegisteredSignup;
import com.enveu.beanModel.responseModels.SignUp.SignUpResponseModel;
import com.enveu.beanModel.responseModels.genre_response.GenreResponse;
import com.enveu.beanModel.responseModels.landingTabResponses.playlistResponse.PlaylistResponses;
import com.enveu.beanModel.responseModels.landingTabResponses.railData.PlaylistRailData;
import com.enveu.beanModel.responseModels.sharing.SharingModel;
import com.enveu.beanModel.sponsorUserTracking.UserTrackingResponse;
import com.enveu.beanModel.userProfile.UserProfileResponse;
import com.enveu.beanModelV3.RelatedRailsCommonData;
import com.enveu.beanModelV3.mutli_profile_response.AddProfileRequestBody;
import com.enveu.beanModelV3.mutli_profile_response.AddSecondaryProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.GenerateOtpResponse;
import com.enveu.beanModelV3.mutli_profile_response.GetAllProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.ProfileSwitchResponse;
import com.enveu.beanModelV3.mutli_profile_response.RemoveSecondaryProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.SecondaryProfileData;
import com.enveu.beanModelV3.mutli_profile_response.UpdateSecondaryProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.ValidateOtpResponse;
import com.enveu.beanModelV3.playListModelV2.EnveuCommonResponse;
import com.enveu.beanModelV3.searchGenres.SearchGenres;
import com.enveu.beanModelV3.searchHistory.SearchHistory;
import com.enveu.beanModelV3.searchRating.SearchRating;
import com.enveu.beanModelV3.searchV2.ResponseSearch;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ArtistListResponse;
import com.enveu.beanModelV3.videoDetailV3.queue_response.AddToQueueRequestModel;
import com.enveu.beanModelV3.videoDetailV3.queue_response.AddToQueueResponse;
import com.enveu.beanModelV3.videoDetailV3.queue_response.GetQueueResponse;
import com.enveu.beanModelV3.videoDetailV3.queue_response.RemoveQueueResponse;
import com.enveu.beanModelV3.videoDetailsV2.EnveuVideoDetailsBean;
import com.enveu.bean_model_v2_0.MusicPlaylist.AddSongPlaylist;
import com.enveu.bean_model_v2_0.MusicPlaylist.CheckContentIsLike;
import com.enveu.bean_model_v2_0.MusicPlaylist.CreatePlaylist;
import com.enveu.bean_model_v2_0.MusicPlaylist.DeleteMusicPlaylist;
import com.enveu.bean_model_v2_0.MusicPlaylist.MyPlaylistData;
import com.enveu.bean_model_v2_0.MusicPlaylist.PlaylistDetails;
import com.enveu.bean_model_v2_0.geo.GeoResponse;
import com.enveu.bean_model_v2_0.listAll.ListAllContent;
import com.enveu.bean_model_v2_0.videoDetailBean.EnvVideoDetailsBean;
import com.enveu.jwplayer.player.ChromeCastUrlResponse;
import com.enveu.menuManager.model.MenuManagerModel;
import com.enveu.redeemcoupon.RedeemCouponResponseModel;
import com.enveu.repository.userinteraction.followModel.FollowArtist;
import com.enveu.userAssetList.ResponseUserAssetList;
import com.enveu.utils.config.bean.dmsResponse.ConfigBean;
import com.enveu.utils.recoSense.bean.RecosenceResponse;
import com.google.gson.JsonObject;

import org.json.JSONObject;

import java.util.List;
import java.util.Map;

import app.doxzilla.bean_model.active_device_management.startPLayBackSessionModel_AppLevel.StartSessionModel;
import app.doxzilla.bean_model.active_device_management.stopPlaybackSession.StopPlaybackSessionModel;
import app.doxzilla.bean_model.active_device_management.updatePlayBackSession.UpdatePlaybackSessionModel;
import io.reactivex.Observable;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.Headers;
import retrofit2.http.PATCH;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;
import retrofit2.http.Url;


public interface ApiInterface {

    String version = "v6/";
    String searchAPIVersion = "v5_0/";
    String mediaContentAPIVersion = "v5_0/";
    String userActivityContentAPIVersion = "v5_0/";


    @Headers("x-platform: android")
    @GET("config")
    Call<ResponseConfig> getConfiguration(@Query("getLatest") String param1);

    @Headers("x-platform: android")
    @POST("user/login/manual")
    Call<LoginResponseModel> getLogin(@Body JsonObject apiLogin);


    @GET(mediaContentAPIVersion + "mediaContent/getGeoInfo")
    Call<GeoResponse> getGeoInfo();

    @Headers("x-platform: android")
    @POST("user/register/manual")
    Call<SignUpResponseModel> getSignUp(@Body JsonObject apiSignUp);

    @Headers("x-platform: android")
    @PATCH("user/update/profile")
    Call<ResponseRegisteredSignup> getRegistrationStep(@Body JsonObject user);

    @POST("v1_0/dynamicLink/generateLink")
    Call<SharingModel> getDynamicLink(@Body JsonObject data);

    @GET("content/listAll")
    Call<EnveuCommonResponse> getLinkedContentList(@Query("linkedContentId") String seriesId, @Query("seasonNumber") int seasonNumber, @Query("page") int page, @Query("size") int size);

    @GET("user/forgotPassword")
    Call<JsonObject> getForgotPassword(@Query("email") String param1);

    @Headers("x-platform: android")
    @POST("user/changePassword")
    Call<ResponseChangePassword> getChangePassword(@Body JsonObject user);

    @Headers("x-platform: android")
    @POST("user/login/fb")
    Call<LoginResponseModel> getFbLogin(@Body JsonObject user);

    @Headers("x-platform: android")
    @POST("user/connectToFb")
    Call<ResponseConnectFb> getConnectFb(@Body JsonObject user);

    @Headers("x-platform: android")
    @POST("user/login/fb/force")
    Call<LoginResponseModel> getForceFbLogin(@Body JsonObject user);

    @Headers("x-platform: android")
    @GET("user/logout")
    Call<JsonObject> getLogout(@Query("removeAllSession") boolean param1);

    @Headers("x-platform: android")
    @POST("user/logout/false")
    Call<JsonObject> getUserLogout();


    @Headers("x-platform: android")
    @GET("user/sendVerificationEmail")
    Call<ResponseEmpty> getVerifyUser();


    @Headers("x-platform: android")
    @GET("popularSearch")
    Call<ResponsePopularSearch> getPopularSearch(@Query("size") int size, @Query("page") int page);

    @Headers("x-platform: android")
    @GET("searchSeries")
    Call<ResponsePopularSearch> getSearchKeyword(@Query("query") String keyword, @Query("size") int size, @Query("page") int page);

    @Headers("x-platform: android")
    @GET("homescreen/getConfig")
    Call<PlaylistResponses> getPlaylists(@Query("id") int Id);

    @Headers("x-platform: android")
    @GET("homescreen/getBaseCategories")
    Call<PlaylistRailData> getPlaylistsData(@Query("id") int id, @Query("pageNo") int pageNo, @Query("length") int length);


    @Headers("x-platform: android")
    @GET("homescreen/getBaseCategories")
    Observable<PlaylistRailData> getPlaylistsDataHome(@Query("id") int Id, @Query("pageNo") int pageNo, @Query("length") int length);


    @Headers("x-platform: android")
    @GET("ad/fetchAds")
    Call<ResponseMobileAds> getAdsData(@Query("navigationScreenId") int Id, @Query("platform") String platform);


    @POST("continueWatching/assetHistory")
    Call<ResponseAssetHistory> getAssetHistory(@Body JsonObject assetRequest);

    @POST("continueWatching/userAssetList")
    Call<ResponseUserAssetList> getAssetList(@Body JsonObject assetRequest);


    @GET("config")
    Call<EnveuConfigResponse> getConfig();


    //V2 changes applied in below APIs-->> Versioning moved to endpoints
    @GET(mediaContentAPIVersion + "mediaPlaylist/contents")
    Call<EnveuCommonResponse> getPlaylistDetailsById(@Header("x-auth") String token, @Query("playlistId") String playListId, @Query("locale") String locale, @Query("page") int pageNumber, @Query("size") int pageSize);


    @GET(mediaContentAPIVersion + "mediaPlaylist/contents")
    Call<EnveuCommonResponse> getPlaylistDetailsByIdWithPG(@Query("playlistId") String playListId, @Query("locale") String locale,
                                                           @Query("page") int pageNumber, @Query("size") int pageSize, @Query("parentalRating")
                                                           String parentalRating);


    //V2 PI for getting asset details
    @GET(mediaContentAPIVersion + "mediaContent")
    Call<EnveuVideoDetailsBean> getVideoDetails(@Query("mediaContentId") String manualImageAssetId, @Query("locale") String locale);

    @GET(mediaContentAPIVersion + "mediaContent")
    Call<EnveuVideoDetailsBean> getVideoDetailsPG(@Query("mediaContentId") String manualImageAssetId, @Query("locale") String locale, @Query("parentalRating") String parentalRating);

    @GET(mediaContentAPIVersion + "mediaContent")
    Call<EnveuVideoDetailsBean> getEnvVideoDetailsPG(@Query("mediaContentId") String manualImageAssetId);


    // v2 API for getting related content -->> all the season episodes


    @GET("v4/content/like/listAll")
    Call<com.enveu.bean_model_v2_0.listAll.likeList.Response> getIsLikeGOI(@Query("page") int pageNumber,
                                                                           @Query("size") int pageSize,
                                                                           @Query("locale") String locale);

    @GET("participation/listAll")
    Call<com.enveu.bean_model_v2_0.listAll.RequestOfferList.Response> getRequestOfferForUser(@Query("userId") int userId,
                                                                                             @Query("customType") String customType,
                                                                                             @Query("page") int pageNumber,
                                                                                             @Query("size") int pageSize,
                                                                                             @Query("locale") String locale);


    // v2 API for getting related content -->> all the episodes


    @GET(mediaContentAPIVersion + "mediaContent/listAll")
    Call<ListAllContent> getSeasonEpisodesV2(@Query("customData") String customData,
                                             @Query("page") int pageNumber,
                                             @Query("size") int pageSize,
                                             @Query("sortBy") String sortBy,
                                             @Query("sortByCustomDataKey") String sortByCustomDataKey,
                                             @Query("customDataSortKeyType") String customDataSortKeyType,
                                             @Query("locale") String locale);

    @GET(mediaContentAPIVersion + "mediaContent/listAll")
    Call<ListAllContent> getAllEpisodesV2(@Query("customData") String customData, @Query("page") int pageNumber,
                                          @Query("size") int pageSize, @Query("sortBy") String sortBy,
                                          @Query("locale") String locale);

    @GET(mediaContentAPIVersion + "mediaContent/listAll")
    Call<ListAllContent> getLiveEventMatch(@Query("customData") String customData, @Query("page") int pageNumber,
                                           @Query("size") int pageSize,
                                           @Query("locale") String locale);

    @GET(mediaContentAPIVersion + "mediaContent/{id}/getRelatedContents")
    Call<EnveuCommonResponse> getRelatedContentForVideo(@Path("id") int asset, @Query("locale") String locale,
                                                        @Query("page") int pageNumber,
                                                        @Query("size") int pageSize,
                                                        @Query("contentType") String contentType
    );

    @GET(mediaContentAPIVersion + "mediaContent/listAll")
    Call<ListAllContent> getRecommendedSongs(@Query("sortBy") String sortBy, @Query("locale") String locale,
                                             @Query("page") int pageNumber,
                                             @Query("size") int pageSize,
                                             @Query("contentType") String contentType
    );


    @GET(mediaContentAPIVersion + "mediaContent/listAll")
    Call<ListAllContent> getCommonListAll(@Query("locale") String locale,
                                          @Query("page") int pageNumber,
                                          @Query("size") int pageSize,
                                          @Query("customData") String customData);


    @GET(mediaContentAPIVersion + "mediaContent")
    Call<VideoIdModel> getVideoIdData(@Query("mediaContentId") String manualImageAssetId, @Query("locale") String locale);


    @Headers("x-platform: android")
    @GET(searchAPIVersion + "search")
    io.reactivex.Observable<ResponseSearch> getSearchByFilters(@Query("keyword") String keyword,
                                                               @Query("contentType") String type,
                                                               @Query("size") int size,
                                                               @Query("page") int page,
                                                               @Query("locale") String locale,
                                                               @Query("customType") String customType,
                                                               @Query("audioType") String audioType,
                                                               @Query("liveType") String liveType,
                                                               @Query("personType") String personType,
                                                               @Query("videoType") String videoType,
                                                               @Query("trackEvent") Boolean trackEvent,
                                                               @Query("genre") String genre);

    @Headers("x-platform: android")
    @GET(searchAPIVersion + "search")
    Call<ResponseSearch> getSearchByFiltersOnV2(@Query("keyword") String keyword,
                                                @Query("contentType") String type,
                                                @Query("size") int size,
                                                @Query("page") int page,
                                                @Query("locale") String locale,
                                                @Query("customType") String customType,
                                                @Query("audioType") String audioType,
                                                @Query("liveType") String liveType,
                                                @Query("personType") String personType,
                                                @Query("videoType") String videoType,
                                                @Query("trackEvent") Boolean trackEvent);

    @GET(searchAPIVersion + "search")
    Call<ResponseSearch> getAllArtist(@Query("keyword") String keyword,
                                      @Query("contentType") String type,
                                      @Query("size") int size,
                                      @Query("page") int page,
                                      @Query("locale") String locale,
                                      @Query("personType") String customType,
                                      @Query("trackEvent") Boolean trackEvent);


    @GET(searchAPIVersion + "search")
    Call<ResponseSearch> getAllArtistWithoutKeyword(
            @Query("contentType") String type,
            @Query("size") int size,
            @Query("page") int page,
            @Query("locale") String locale,
            @Query("customType") String customType,
            @Query("trackEvent") Boolean trackEvent);


    @Headers("x-platform: android")
    @GET(searchAPIVersion + "search")
    Call<ResponseSearch> getVideoSearchResults(@Query("keyword") String keyword,
                                               @Query("contentType") String type,
                                               @Query("size") int size, @Query("page") int page,
                                               @Query("locale") String locale,
                                               @Query("videoType") String videoType,
                                               @Query("customType") String customType);

    @GET(searchAPIVersion + "search?")
    Call<GenreResponse> getGenreResult(@Query("contentType") String contentType,
                                       @Query("customType") String customType,
                                       @Query("offset") int offSet,
                                       @Query("size") int size,
                                       @Query("trackEvent") String trackEvent,
                                       @Query("locale") String locale);

    @GET(searchAPIVersion + "search?")
    Call<GenreResponse> getGenreResultFilter(@Query("contentType") String contentType,
                                             @Query("genre") String ids,
                                             @Query("keyword") String keyword,
                                             @Query("offset") int offSet,
                                             @Query("size") int size,
                                             @Query("trackEvent") String trackEvent,
                                             @Query("personType") String personType,
                                             @Query("locale") String locale);

    @GET(searchAPIVersion + "search?")
    Call<GenreResponse> getGenreResultFilterWithoutKeyWord(@Query("contentType") String contentType,
                                                           @Query("genre") String ids,
                                                           @Query("offset") int offSet,
                                                           @Query("size") int size,
                                                           @Query("trackEvent") String trackEvent,
                                                           @Query("personType") String personType,
                                                           @Query("locale") String locale);


    @Headers("x-platform: android")
    @GET(searchAPIVersion + "search")
    Call<ResponseSearch> getSearchResults(@Query("keyword") String keyword, @Query("contentType") List<String> type, @Query("size") int size, @Query("page") int page, @Query("locale") String locale);

    @Headers("x-platform: android")
    @GET(searchAPIVersion + "search")
    Call<ResponseSearch> getSearchResultsByFilters(@Query("keyword") String keyword, @Query("contentType") List<String> type, @Query("size") int size, @Query("page") int page, @Query("locale") String locale,
                                                   @Query("filters") List<String> filterGenreSavedListKeyForApi, @Query("sort") List<String> filterSortSavedListKeyForApi);
    //till this

    @GET("v2/menuManager/getMenuDetails")
    Call<MenuManagerModel> getMenuDetails(@Query("locale") String locale);

    @DELETE(version + "content/like/delete/{assetId}")
    Call<ResponseEmpty> deleteLike(@Path("assetId") int asset);

    @DELETE(version + "content/like/delete/{assetId}")
    Call<ResponseEmpty> deleteLikeGOI(@Path("assetId") int asset);

    @DELETE(version + "user/watchlist/delete/{assetId}")
    Call<ResponseEmpty> removeWatchlist(@Path("assetId") int asset);

    @DELETE(version + "user/watchlist/delete/{assetId}")
    Call<ResponseEmpty> removeWatchlistGOI(@Path("assetId") int asset);

    @GET(version + "user/watchlist/get/{assetId}")
    Call<ResponseGetIsWatchlist> getIsWatchList(@Path("assetId") int asset);


    @POST(version + "user/watchlist/add/{assetId}")
    Call<ResponseEmpty> addToWatchList(@Path("assetId") int asset);

    @POST(version + "user/watchlist/add/{assetId}")
    Call<ResponseEmpty> addToWatchListGOIVod(@Path("assetId") int asset);

    @POST(version + "user/watchlist/add/CUSTOM/{assetId}")
    Call<ResponseEmpty> addToWatchListGOICustom(@Path("assetId") int asset);


    @POST(version + "user/watchHistory/add/{assetId}")
    Call<ResponseEmpty> addToWatchHistory(@Query("assetId") int asset);


    @POST(version + "content/like/{assetId}")
    Call<ResponseEmpty> addToLikeVod(@Path("assetId") int asset);

    @POST(version + "content/follow/{assetId}")
    Call<FollowArtist> followArtist(@Path("assetId") int asset);

    @GET(version + "content/follow/{assetId}")
    Call<FollowArtist> getFollowArtist(@Path("assetId") int asset);

    @DELETE(version + "content/follow/delete/{assetId}")
    Call<FollowArtist> removeArtist(@Path("assetId") int asset);

    @POST(version + "content/like/CUSTOM/{assetId}")
    Call<ResponseEmpty> addToLikeGOICustom(@Path("assetId") int asset);

    @POST(version + "content/like/{assetId}")
    Call<ResponseEmpty> addToLikeGOIVod(@Path("assetId") int asset);


    @GET(version + "content/like/{assetId}")
    Call<ResponseIsLike> getIsLikeVod(@Path("assetId") int asset);


    @GET("getConfig")
    Call<ConfigBean> getConfig(@Query("version") int manualImageAssetId);

    @POST("webhooks")
    Call<RecosenceResponse> getRecoClick(@Body JSONObject assetRequest);

    @GET(mediaContentAPIVersion + "mediaContent")
    Call<EnvVideoDetailsBean> getEnvVideoDetails(@Query("mediaContentId") String manualImageAssetId, @Query("locale") String locale);


    @POST("coupon/redeemCoupon")
    Call<RedeemCouponResponseModel> redeemCoupon(@Query("redeemCode") String redeemCode);

    @GET("v3/order/orderHistory")
    Call<OrderHistoryModel> getOrderHistory(@Query("page") String page, @Query("size") String size);

    @GET(version + "subscription/getPlanPurchaseHistory")
    Call<TransactionHistoryModel> getTransactionHistory(@Header("x-auth") String token, @Query("page") String page, @Query("size") String size);

    @POST(version + "user/device/linkUserDevice")
    Call<LoginDeviceModel> getLoginDeviceResponse(@Query("forceLogout") Boolean forceLogout);


    @Headers("x-platform: android")
    @POST(version + "user/device/updateDeviceDetails")
    Call<LoginDeviceModel> getUpdatedDeviceResponse();

    @GET(searchAPIVersion + "search/history")
    Call<SearchHistory> getSearchHistory();

    @DELETE(searchAPIVersion + "search/history")
    Call<SearchHistory> deleteSearchHistory(@Query("locale") String locale, @Query("keyword") String keyword, @Query("clearAll") Boolean clearAll);

    @GET(mediaContentAPIVersion + "mediaContent/listAll")
    Call<SearchGenres> getGenres(@Query("customType") String customType, @Query("contentType") String contentType, @Query("locale") String locale);

    @GET(mediaContentAPIVersion + "mediaContent/listAll")
    Call<SearchRating> getRating(@Query("customType") String customType, @Query("contentType") String contentType);

    @POST(userActivityContentAPIVersion + "userActivity/playlist/addNew")
    Call<CreatePlaylist> createMusicPlaylist(@Body JsonObject body);

    @POST("v1_0/analytics/appUserActivity/track")
    Call<UserTrackingResponse> getSponsorUserTracking(@Body JsonObject body);

    @GET(userActivityContentAPIVersion + "userActivity/playlist/listAll")
    Call<MyPlaylistData> getMusicPlaylists(@Query("type") String type);

    @DELETE(userActivityContentAPIVersion + "userActivity/playlist/delete")
    Call<DeleteMusicPlaylist> deleteMusicPlaylist(@Query("playlistId") String playlistId);

    @GET(userActivityContentAPIVersion + "userActivity/playlist")
    Call<PlaylistDetails> getMyPlaylistDetails(@Query("playlistSlug") String playlistSlug, @Query("locale") String locale);

    @PATCH(userActivityContentAPIVersion + "userActivity/playlist/addContentsToPlaylists")
    Call<AddSongPlaylist> addContentToPlaylist(@Body MultiplePlaylistRepo.ApiRequestForAddContentOne body);

    @PATCH(userActivityContentAPIVersion + "userActivity/playlist/addContentsToPlaylists")
    Call<AddSongPlaylist> addContentToPlaylistForLike(@Body MultiplePlaylistRepo.ApiRequestForAddContent body);

    @PATCH(userActivityContentAPIVersion + "userActivity/playlist/addContentsToPlaylists")
    Call<AddSongPlaylist> addContentToPlaylist(@Body MultiplePlaylistRepo.ApiRequestAddContent body);

    @PATCH(userActivityContentAPIVersion + "userActivity/playlist/removeContentsFromPlaylist")
    Call<MyPlaylistData> removeSongFromPlaylist(@Body MultiplePlaylistRepo.ApiRequestForRemoveContent body);

    @PATCH(userActivityContentAPIVersion + "userActivity/playlist/checkIfContentPresent")
    Call<CheckContentIsLike> checkContentPresentLike(@Query("contentId") int contentID);

    @PUT(userActivityContentAPIVersion + "userActivity/playlist/update")
    Call<MyPlaylistData> renamePlaylistName(@Body MultiplePlaylistRepo.ApiRequestForRenamePlaylist body);

    @GET(mediaContentAPIVersion + "mediaContent/listAll")
    Call<RelatedRailsCommonData> getMediaContentList(@Query("contentType") String contentType, @Query("videoType") String videoType, @Query("audioType") String audioType, @Query("customData") String customData, @Query("customType") String customType, @Query("genre") String genre, @Query("articleType") String articleType, @Query("page") int page, @Query("size") int size, @Query("locale") String locale);

    @PATCH(userActivityContentAPIVersion + "userActivity/playlist/addContentsToPlaylists")
    Call<AddToQueueResponse> addToQueueApi(@Body AddToQueueRequestModel data);

    @GET(userActivityContentAPIVersion + "userActivity/content/interactions/details")
    Call<JsonObject> checkFollowAndLike(@QueryMap Map<String, String> queryParams);

    @GET(userActivityContentAPIVersion + "userActivity/playlist/listAll")
    Call<JsonObject> getUserPlaylistData(@QueryMap Map<String, String> queryParams);

    @GET(userActivityContentAPIVersion + "userActivity/playlist")
    Call<JsonObject> getUserPlaylistDataBySlug(@QueryMap Map<String, String> queryParams);

    @GET(userActivityContentAPIVersion + "userActivity/playlist")
    Call<GetQueueResponse> allGetQueueListApi(@Query("type") String type);

    @GET(mediaContentAPIVersion+"mediaPlaylist/getPlaylistByIds")
    Call<JsonObject> getPlayListByIds(@QueryMap Map<String,String> queryParams);

    @GET(mediaContentAPIVersion + "mediaContent/listAll")
    Call<ArtistListResponse> getMediaContentV3(@Query("contentType") String contentType, @Query("audioType") String audioType, @Query("customData") String customData, @Query("page") int pageNumber,
                                               @Query("size") int pageSize, @Query("sortBy") String sortBy, @Query("sortOrder") String sortOrder,
                                               @Query("locale") String locale);

    @GET(mediaContentAPIVersion + "mediaContent/listAll")
    Call<ArtistListResponse> getMediaContentV3(@Query("contentType") String contentType, @Query("audioType") String audioType, @Query("customData") String customData, @Query("page") int pageNumber,
                                               @Query("size") int pageSize, @Query("gener") String generId, @Query("sortBy") String sortBy, @Query("sortOrder") String sortOrder,
                                               @Query("locale") String locale);

    @GET(mediaContentAPIVersion + "mediaContent/listAll")
    Call<ListAllContent> getArtistSongsList(@Query("contentType") String contentType, @Query("audioType") String audioType, @Query("customData") String customData, @Query("page") int pageNumber,
                                            @Query("size") int pageSize, @Query("sortBy") String sortBy, @Query("sortOrder") String sortOrder,
                                            @Query("locale") String locale);


    @PATCH(userActivityContentAPIVersion + "userActivity/playlist/removeContentsFromPlaylist")
    Call<RemoveQueueResponse> removeAllQueueApi(@Body JsonObject data);


    @POST(version + "user/secondaryAccount/add")
    Call<AddSecondaryProfileResponse> addSecondaryProfile(@Header("x-auth") String token, @Body AddProfileRequestBody data);

    @GET(version + "user/secondaryAccount/listAll")
    Call<GetAllProfileResponse> getAllSecondaryProfileList(@Header("x-auth") String token);

    @POST(version + "user/secondaryAccount/update")
    Call<UpdateSecondaryProfileResponse> updateSecondaryProfile(@Header("x-auth") String token, @Body SecondaryProfileData data);

    @DELETE(version + "user/secondaryAccount")
    Call<RemoveSecondaryProfileResponse> deleteSecondaryProfile(@Header("x-auth") String token, @Query("accountId") String accountId);

    @POST(version + "user/secondaryAccount/switchAccount")
    Call<ProfileSwitchResponse> switchAnotherProfile(@Header("x-auth") String token, @Query("accountId") String accountId);

    @PATCH(version + "/user/profile/update")
    Call<UserProfileResponse> updateParentProfile(@Header("x-auth") String token, @Body SecondaryProfileData data);

    @POST(version + "user/otp/generate")
    Call<GenerateOtpResponse> generateOtpParentalPin(@Header("x-auth") String token, @Body JsonObject data);

    @POST(version + "user/otp/validate")
    Call<ValidateOtpResponse> validateParentalLockOtp(@Header("x-auth") String token, @Body JsonObject data);

    @POST(version + "user/secondaryAccount/switchAccount")
    Call<ProfileSwitchResponse> switchMainProfile(@Header("x-auth") String token, @Query("accountId") String accountId);


    @POST(version + "user/device/startPlaybackSession")
    Call<StartSessionModel> getStartSessionPlayback(@Query("contentId") String contentId, @Query("contentName") String contentName);

    @POST(version + "user/device/stopPlaybackSession")
    Call<StopPlaybackSessionModel> getStopPlayback(@Query("sessionId") String sessionId);

    @GET(version + "user/device/updatePlaybackSessionStatus")
    Call<UpdatePlaybackSessionModel> getUpdatePlayback(@Query("sessionId") String sessionId);

    @GET(mediaContentAPIVersion + "epg/schedule/program/listAll")
    Call<JsonObject> getEPGData(@QueryMap Map<String, String> queryParams);

    @POST(mediaContentAPIVersion+"mediaContent/ugc/create")
    Call<JsonObject> callApiForUploadCreate(@Header("x-auth") String token,@Body JsonObject body);
    @GET
    Call<ChromeCastUrlResponse> getChromeCastUrl(@Url String url,
                                                 @Header("accept") String acceptKey);
}

