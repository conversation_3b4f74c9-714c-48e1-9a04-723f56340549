package com.enveu.networking.detailPlayer;

import com.enveu.activities.detail.viewModel.Response;
import com.enveu.activities.purchase.purchase_model.PurchaseResponseModel;
import com.enveu.beanModel.AssetHistoryContinueWatching.ResponseAssetHistory;
import com.enveu.beanModel.JwDrmResponse.DrmResponse;
import com.enveu.beanModel.addComment.ResponseAddComment;
import com.enveu.beanModel.allComments.ResponseAllComments;
import com.enveu.beanModel.cancelPurchase.ResponseCancelPurchase;
import com.enveu.beanModel.deleteComment.ResponseDeleteComment;
import com.enveu.beanModel.drm.DRM;
import com.enveu.beanModel.emptyResponse.ResponseEmpty;
import com.enveu.beanModel.entitle.ResponseEntitle;
import com.enveu.beanModel.isLike.ResponseIsLike;
import com.enveu.beanModel.isWatchList.ResponseContentInWatchlist;
import com.enveu.beanModel.like.ResponseAddLike;
import com.enveu.beanModel.membershipAndPlan.ResponseMembershipAndPlan;
import com.enveu.beanModel.responseModels.detailPlayer.ResponseDetailPlayer;
import com.enveu.beanModel.responseModels.series.SeriesResponse;
import com.enveu.beanModel.responseModels.series.season.SeasonResponse;
import com.enveu.beanModel.watchList.ResponseWatchList;
import com.enveu.bean_model_v2_0.videoDetailBean.liveDetailBean.LiveStatusResponse;
import com.enveu.jwplayer.cast.PlayDetailResponse;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;
import com.google.gson.JsonObject;

import org.json.JSONObject;

import java.util.ArrayList;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.Headers;
import retrofit2.http.PATCH;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Query;
import retrofit2.http.Url;

public interface APIDetails {

    String version = "v6/";

    @Headers("x-platform: android")
    @GET("detail/videoAsset/{Id}")
    Call<ResponseDetailPlayer> getPlayerDetails(@Path("Id") int id);


    @Headers("x-platform: android")
    @GET("homescreen/getYouMayLikeContent")
    Call<SeasonResponse> getYouMayLike(@Query("contentTypeId") int contentTypeId, @Query("pageNo") int pageNo, @Query("length") int length);


    @Headers("x-platform: android")
    @GET("detail/series/{seriesId}")
    Call<SeriesResponse> getSeriesDetails(@Path("seriesId") int seriesId);

    @Headers("x-platform: android")
    @GET("season/vodBySeason")
    io.reactivex.Observable<SeasonResponse> getSeasonEpisodeMulti(@Query("seasonId") int seasonId, @Query("pageNo") int pageNo, @Query("length") int length);


    @Headers("x-platform: android")
    @GET("season/vodBySeason")
    Call<SeasonResponse> getSeasonEpisodeSingle(@Query("seasonId") int seasonId, @Query("pageNo") int pageNo, @Query("length") int length);


    @Headers("x-platform: android")
    @GET("series/vodBySeries")
    Call<SeasonResponse> getVOD(@Query("seriesId") int seriesId, @Query("pageNo") int pageNo, @Query("length") int length);


    @Headers("x-platform: android")
    @POST("watchList/addToWatchList")
    Call<ResponseWatchList> getAddToWatchList(@Body JsonObject data);

    @Headers("x-platform: android")
    @POST("watchList/isContentPresentOnWatchList")
    Call<ResponseContentInWatchlist> getIsContentWatchList(@Body JsonObject data);


    @Headers("x-platform: android")
    @POST("like/islike")
    Call<ResponseIsLike> getIsLike(@Body JsonObject data);


    @Headers("x-platform: android")
    @POST("like/likes")
    Call<ResponseAddLike> getAddLike(@Body JsonObject data);


    @Headers("x-platform: android")
    @POST("like/delete")
    Call<ResponseEmpty> getUnLike(@Body JsonObject data);


    @Headers("x-platform: android")
    @POST("watchList/removeFromWatchList")
    Call<ResponseEmpty> getRemoveFromWatchList(@Query("watchListItemId") String id);

    @Headers("x-platform: android")
    @GET(version+"subscription/checkEntitlement")
    Call<ResponseEntitle> checkEntitlement(@Query("vodSKU") String sku);


    @GET
    Call<PlayDetailResponse> getSubtitle(@Url String url);
    @Headers("x-platform: android")
    @GET("v5_0/mediaContent/geo/check?")
    Call<Response> getGeoBlocking(@Query("mediaContentId") String mediaContentId);


    @GET("v2_0/mediaContent/live/status")
    Call<LiveStatusResponse> checkLiveStatus(@Query("mediaContentId") int mediaContentId);

    @Headers("x-platform: android")
    @POST("comment/listComments")
    Call<ResponseAllComments> getAllComments(@Query("size") String size, @Query("page") int page, @Body JsonObject data);


    @Headers("x-platform: android")
    @POST("comment/addComment")
    Call<ResponseAddComment> getAddComments(@Body JsonObject data);

    @Headers("x-platform: android")
    @POST("comment/delete")
    Call<ResponseDeleteComment> getDeleteComment(@Query("commentId") String commentId);


    @Headers("x-platform: android")
    @POST("order/new")
    Call<PurchaseResponseModel> getCreateNewPurchase(@Body JsonObject data);

    @Headers("x-platform: android")
    @POST("payment/initiate")
    Call<PurchaseResponseModel> initiatePurchase(@Body JsonObject data);


    @Headers("x-platform: android")
    @POST("payment/{paymentId}")
    Call<PurchaseResponseModel> updatePurchase(@Path("paymentId") String paymentId , @Body JsonObject data);

    @Headers("x-platform: android")
    @GET(version+"subscription/getPlans")
    Call<ResponseMembershipAndPlan> getPlans(@Query("subscriptionOfferType") String a, @Query("subscriptionCheckEntitlement") boolean b);

    @Headers("x-platform: android")
    @GET(version+"purchases/cancelPurchase")
    Call<ResponseCancelPurchase> cancelPurchase();


    @Headers("x-platform: android")
    @POST("continueWatching/addBookmark")
    Call<ResponseEmpty> getBookMark(@Body JsonObject assetRequest);


    @POST("continueWatching/assetHistory")
    Call<ResponseAssetHistory> getAssetHistory(@Body JsonObject assetRequest);

    @GET("v5_0/mediaContent/getPlayableId")
    Call<DRM> getSKUForPLayer(@Query("token")String token, @Query("sku")String SKU);

    @GET("v5_0/mediaContent/getPlayableId")
    Call<DRM> getSignedUrlForDrmPlayback(@Query("token")String token,@Query("sku")String SKU, @Query("playerId")String licenseKey);

    @GET()
    Call<DrmResponse> getWidevineUrl(@Url String url,@Query("token") String token);
}
