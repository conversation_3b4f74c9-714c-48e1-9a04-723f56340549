package com.enveu.networking.response

import com.google.gson.annotations.SerializedName

data class AddCommentResponse(

	@field:SerializedName("data")
	val data: AddComment? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	var responseCode: Int? = null
)

data class AddComment(

	@field:SerializedName("lastUpdated")
	val lastUpdated: Any? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Long? = null,

	@field:SerializedName("contentId")
	val contentId: Int? = null,

	@field:SerializedName("comment")
	val comment: String? = null,

	@field:SerializedName("id")
	val id: String? = null,

	@field:SerializedName("parentId")
	val parentId: Any? = null
)
