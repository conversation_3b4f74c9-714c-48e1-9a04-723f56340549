package com.enveu.networking.response

import com.google.gson.annotations.SerializedName

data class CreateContentDetailsResponse(

	@field:SerializedName("data")
	val data: CreateContentData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	var responseCode: Int? = null
)

data class CreateContentData(

	@field:SerializedName("longDescription")
	val longDescription: Any? = null,

	@field:SerializedName("liveContent")
	val liveContent: Any? = null,

	@field:SerializedName("imageContent")
	val imageContent: Any? = null,

	@field:SerializedName("keywords")
	val keywords: List<Any?>? = null,

	@field:SerializedName("externalRefId")
	val externalRefId: String? = null,

	@field:SerializedName("accessibility")
	val accessibility: Accessibility? = null,

	@field:SerializedName("description")
	val description: Any? = null,

	@field:SerializedName("contentSlug")
	val contentSlug: String? = null,

	@field:SerializedName("customData")
	val customData: CreateContentCustomData? = null,

	@field:SerializedName("likeCount")
	val likeCount: Int? = null,

	@field:SerializedName("video")
	val video: Any? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("parentalRating")
	val parentalRating: Any? = null,

	@field:SerializedName("organizationId")
	val organizationId: Any? = null,

	@field:SerializedName("lastUpdated")
	val lastUpdated: Long? = null,

	@field:SerializedName("shareCount")
	val shareCount: Int? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Long? = null,

	@field:SerializedName("premium")
	val premium: Boolean? = null,

	@field:SerializedName("customContent")
	val customContent: Any? = null,

	@field:SerializedName("articleContent")
	val articleContent: Any? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("sku")
	val sku: String? = null,

	@field:SerializedName("analyticsId")
	val analyticsId: Any? = null,

	@field:SerializedName("contentType")
	val contentType: String? = null,

	@field:SerializedName("images")
	val images: Any? = null,

	@field:SerializedName("audioContent")
	val audioContent: Any? = null,

	@field:SerializedName("styleInfo")
	val styleInfo: Any? = null,

	@field:SerializedName("mediaType")
	val mediaType: Any? = null,

	@field:SerializedName("seoInfo")
	val seoInfo: Any? = null,

	@field:SerializedName("commentCount")
	val commentCount: Int? = null,

	@field:SerializedName("playCount")
	val playCount: Int? = null,

	@field:SerializedName("contentSource")
	val contentSource: String? = null,

	@field:SerializedName("contentReviewRating")
	val contentReviewRating: Any? = null,

	@field:SerializedName("targetingTags")
	val targetingTags: List<Any?>? = null,

	@field:SerializedName("publishedDate")
	val publishedDate: Any? = null,

	@field:SerializedName("parentContent")
	val parentContent: Any? = null,

	@field:SerializedName("personContent")
	val personContent: PersonContent? = null
)

data class Accessibility(

	@field:SerializedName("accessibilitySchedule")
	val accessibilitySchedule: Any? = null,

	@field:SerializedName("checkAccessibility")
	val checkAccessibility: Boolean? = null
)

data class CreateContentCustomData(

	@field:SerializedName("userName")
	val userName: String? = null,

	@field:SerializedName("followers")
	val followers: String? = null,

	@field:SerializedName("following")
	val following: String? = null
)

data class PersonContent(

	@field:SerializedName("lastUpdated")
	val lastUpdated: Long? = null,

	@field:SerializedName("personalInfo")
	val personalInfo: PersonalInfo? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Long? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("personType")
	val personType: String? = null
)

data class PersonalInfo(

	@field:SerializedName("gender")
	val gender: Any? = null,

	@field:SerializedName("dob")
	val dob: Any? = null,

	@field:SerializedName("weight")
	val weight: Any? = null,

	@field:SerializedName("height")
	val height: Any? = null
)
