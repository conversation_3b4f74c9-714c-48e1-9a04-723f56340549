package com.enveu.networking.response

import com.enveu.beanModelV3.videoDetailV3.list.ImagesItem
import com.google.gson.annotations.SerializedName

data class CreatorShortsResponse(

	@field:SerializedName("data")
	val data: CreatorShortsData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	var responseCode: Int? = null
)

data class CreatorShortsCustomData(

	@field:SerializedName("reel-creator-id")
	val reelCreatorId: CreatorReelCreatorId? = null
)

data class CreatorShortsItem(

	@field:SerializedName("longDescription")
	val longDescription: Any? = null,

	@field:SerializedName("liveContent")
	val liveContent: Any? = null,

	@field:SerializedName("imageContent")
	val imageContent: Any? = null,

	@field:SerializedName("keywords")
	val keywords: Any? = null,

	@field:SerializedName("externalRefId")
	val externalRefId: String? = null,

	@field:SerializedName("accessibility")
	val accessibility: Any? = null,

	@field:SerializedName("description")
	val description: String? = null,

	@field:SerializedName("contentSlug")
	val contentSlug: String? = null,

	@field:SerializedName("customData")
	val customData: CreatorShortsCustomData? = null,

	@field:SerializedName("likeCount")
	val likeCount: Int? = null,

	@field:SerializedName("video")
	val video: CreatorShortsVideo? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("parentalRating")
	val parentalRating: Any? = null,

	@field:SerializedName("organizationId")
	val organizationId: Any? = null,

	@field:SerializedName("lastUpdated")
	val lastUpdated: Long? = null,

	@field:SerializedName("shareCount")
	val shareCount: Int? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Long? = null,

	@field:SerializedName("premium")
	val premium: Boolean? = null,

	@field:SerializedName("customContent")
	val customContent: Any? = null,

	@field:SerializedName("articleContent")
	val articleContent: Any? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("sku")
	val sku: String? = null,

	@field:SerializedName("analyticsId")
	val analyticsId: Any? = null,

	@field:SerializedName("contentType")
	val contentType: String? = null,

	@field:SerializedName("images")
	val images:List<ImagesItem>? = null,

	@field:SerializedName("audioContent")
	val audioContent: Any? = null,

	@field:SerializedName("styleInfo")
	val styleInfo: Any? = null,

	@field:SerializedName("mediaType")
	val mediaType: String? = null,

	@field:SerializedName("seoInfo")
	val seoInfo: Any? = null,

	@field:SerializedName("commentCount")
	val commentCount: Int? = null,

	@field:SerializedName("playCount")
	val playCount: Int? = null,

	@field:SerializedName("contentSource")
	val contentSource: String? = null,

	@field:SerializedName("contentReviewRating")
	val contentReviewRating: Any? = null,

	@field:SerializedName("targetingTags")
	val targetingTags: Any? = null,

	@field:SerializedName("publishedDate")
	val publishedDate: Any? = null,

	@field:SerializedName("parentContent")
	val parentContent: Any? = null,

	@field:SerializedName("personContent")
	val personContent: Any? = null
)

data class CreatorShortsData(

	@field:SerializedName("pageNumber")
	val pageNumber: Int? = null,

	@field:SerializedName("totalPages")
	val totalPages: Int? = null,

	@field:SerializedName("pageSize")
	val pageSize: Int? = null,

	@field:SerializedName("pageInfo")
	val pageInfo: PageInfo? = null,

	@field:SerializedName("items")
	val items: ArrayList<ReelsContentItem?>? = null,

	@field:SerializedName("totalElements")
	val totalElements: Int? = null
)


data class CreatorShortsVideo(

	@field:SerializedName("externalUrl")
	val externalUrl: String? = null,

	@field:SerializedName("vastTag")
	val vastTag: Any? = null,

	@field:SerializedName("seasons")
	val seasons: Any? = null,

	@field:SerializedName("images")
	val images: Any? = null,

	@field:SerializedName("plays")
	val plays: Int? = null,

	@field:SerializedName("episodeNo")
	val episodeNo: Any? = null,

	@field:SerializedName("videoType")
	val videoType: String? = null,

	@field:SerializedName("textTracks")
	val textTracks: Any? = null,

	@field:SerializedName("seasonNo")
	val seasonNo: Any? = null,

	@field:SerializedName("chapterNo")
	val chapterNo: Any? = null,

	@field:SerializedName("duration")
	val duration: Int? = null,

	@field:SerializedName("audioTracks")
	val audioTracks: Any? = null,

	@field:SerializedName("mediaPlaybackType")
	val mediaPlaybackType: Any? = null,

	@field:SerializedName("vMap")
	val vMap: Any? = null,

	@field:SerializedName("cuePoints")
	val cuePoints: Any? = null,

	@field:SerializedName("isAdSupported")
	val isAdSupported: Any? = null,

	@field:SerializedName("drmDisabled")
	val drmDisabled: Any? = null,

	@field:SerializedName("offlineEnabled")
	val offlineEnabled: Any? = null,

	@field:SerializedName("adsType")
	val adsType: Any? = null,

	@field:SerializedName("hostingSource")
	val hostingSource: String? = null
)

data class CreatorShortsImageContent(

	@field:SerializedName("originalImageSizeInBytes")
	val originalImageSizeInBytes: Any? = null,

	@field:SerializedName("src")
	val src: String? = null,

	@field:SerializedName("imageKey")
	val imageKey: String? = null,

	@field:SerializedName("dominantColor")
	val dominantColor: Any? = null,

	@field:SerializedName("isDefault")
	val isDefault: Any? = null,

	@field:SerializedName("showTitle")
	val showTitle: Any? = null,

	@field:SerializedName("width")
	val width: Double? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("tag")
	val tag: String? = null,

	@field:SerializedName("colorPalette")
	val colorPalette: List<Any?>? = null,

	@field:SerializedName("imageType")
	val imageType: String? = null,

	@field:SerializedName("height")
	val height: Double? = null,

	@field:SerializedName("status")
	val status: Any? = null
)

data class CreatorReelCreatorId(

	@field:SerializedName("images")
	val images: Any? = null,

	@field:SerializedName("externalIdentifier")
	val externalIdentifier: String? = null,

	@field:SerializedName("contentSlug")
	val contentSlug: String? = null,

	@field:SerializedName("description")
	val description: Any? = null,

	@field:SerializedName("mediaType")
	val mediaType: String? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("contentType")
	val contentType: String? = null
)

data class CreatorShortsImagesItem(

	@field:SerializedName("longDescription")
	val longDescription: Any? = null,

	@field:SerializedName("liveContent")
	val liveContent: Any? = null,

	@field:SerializedName("imageContent")
	val imageContent: CreatorShortsImageContent? = null,

	@field:SerializedName("keywords")
	val keywords: Any? = null,

	@field:SerializedName("externalRefId")
	val externalRefId: Any? = null,

	@field:SerializedName("accessibility")
	val accessibility: Any? = null,

	@field:SerializedName("description")
	val description: Any? = null,

	@field:SerializedName("contentSlug")
	val contentSlug: Any? = null,

	@field:SerializedName("customData")
	val customData: Any? = null,

	@field:SerializedName("likeCount")
	val likeCount: Int? = null,

	@field:SerializedName("video")
	val video: Any? = null,

	@field:SerializedName("title")
	val title: Any? = null,

	@field:SerializedName("parentalRating")
	val parentalRating: Any? = null,

	@field:SerializedName("organizationId")
	val organizationId: Any? = null,

	@field:SerializedName("lastUpdated")
	val lastUpdated: Any? = null,

	@field:SerializedName("shareCount")
	val shareCount: Int? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Any? = null,

	@field:SerializedName("premium")
	val premium: Any? = null,

	@field:SerializedName("customContent")
	val customContent: Any? = null,

	@field:SerializedName("articleContent")
	val articleContent: Any? = null,

	@field:SerializedName("id")
	val id: Any? = null,

	@field:SerializedName("sku")
	val sku: Any? = null,

	@field:SerializedName("analyticsId")
	val analyticsId: Any? = null,

	@field:SerializedName("contentType")
	val contentType: String? = null,

	@field:SerializedName("images")
	val images: Any? = null,

	@field:SerializedName("audioContent")
	val audioContent: Any? = null,

	@field:SerializedName("styleInfo")
	val styleInfo: Any? = null,

	@field:SerializedName("mediaType")
	val mediaType: Any? = null,

	@field:SerializedName("seoInfo")
	val seoInfo: Any? = null,

	@field:SerializedName("commentCount")
	val commentCount: Int? = null,

	@field:SerializedName("playCount")
	val playCount: Int? = null,

	@field:SerializedName("contentSource")
	val contentSource: Any? = null,

	@field:SerializedName("contentReviewRating")
	val contentReviewRating: Any? = null,

	@field:SerializedName("targetingTags")
	val targetingTags: Any? = null,

	@field:SerializedName("publishedDate")
	val publishedDate: Any? = null,

	@field:SerializedName("parentContent")
	val parentContent: Any? = null,

	@field:SerializedName("personContent")
	val personContent: Any? = null
)
