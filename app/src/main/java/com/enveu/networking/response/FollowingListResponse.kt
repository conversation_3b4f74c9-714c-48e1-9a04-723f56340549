package com.enveu.networking.response

import com.google.gson.annotations.SerializedName

data class FollowingListResponse(

	@field:SerializedName("data")
	val data: FollowingData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	var responseCode: Int? = null
)

data class FollowingListItem(

	@field:SerializedName("profilePicURL")
	val profilePicURL: String? = null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("userName")
	val userName: String? = null,

	@field:SerializedName("verified")
	val verified: Boolean? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("creatorContentId")
	val creatorContentId: Int? = null,

	@field:SerializedName("isFollowing")
    var isFollowing: Boolean? = null
)

data class FollowingData(

	@field:SerializedName("pageNumber")
	val pageNumber: Int? = null,

	@field:SerializedName("totalPages")
	val totalPages: Int? = null,

	@field:SerializedName("pageSize")
	val pageSize: Int? = null,

	@field:SerializedName("items")
	val items: ArrayList<FollowingListItem?>? = null,

	@field:SerializedName("totalElements")
	val totalElements: Long? = null
)
