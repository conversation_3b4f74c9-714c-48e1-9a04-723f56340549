package com.enveu.networking.response

import com.enveu.utils.Constants
import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class GetPlaylistDetailsResponse(

	@field:SerializedName("data")
	val data: PlaylistData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	var responseCode: Int? = null
):Serializable

data class ReelCreatorId(

	@field:SerializedName("images")
	val images: List<ImagesItem?>? = null,

	@field:SerializedName("externalIdentifier")
	var externalIdentifier: String? = null,

	@field:SerializedName("contentSlug")
	val contentSlug: String? = null,

	@field:SerializedName("description")
	val description: String? = null,

	@field:SerializedName("mediaType")
	val mediaType: String? = null,

	@field:SerializedName("id")
	var id: Long? = null,

	@field:SerializedName("title")
	var title: String? = null,

	@field:SerializedName("posterUrl")
	var posterUrl: String? = null,

	@field:SerializedName("contentType")
	val contentType: String? = null
):Serializable

data class ItemsItem(

	@field:SerializedName("contentOrder")
	val contentOrder: Int? = null,

	@field:SerializedName("content")
	val content: ReelsContentItem? = null
):Serializable

data class CustomData(
	@field:SerializedName("reel-creator-id")
	val reelCreatorId: ReelCreatorId? = null
):Serializable


data class ReelsContentItem(

	@field:SerializedName("longDescription")
	val longDescription: Any? = null,

	@field:SerializedName("liveContent")
	val liveContent: Any? = null,

	@field:SerializedName("imageContent")
	val imageContent: Any? = null,

	@field:SerializedName("position")
	var position: Int? = null,

	@field:SerializedName("keywords")
	val keywords: Any? = null,

	@field:SerializedName("hashtagClicked")
	var hashtagClicked: String? = null,

	@field:SerializedName("externalRefId")
	val externalRefId: String? = null,

	@field:SerializedName("accessibility")
	val accessibility: Any? = null,

	@field:SerializedName("description")
	val description: String? = null,

	@field:SerializedName("contentSlug")
	val contentSlug: String? = null,

	@field:SerializedName("customData")
	val customData: CustomData? = null,

	@field:SerializedName("likeCount")
	var likeCount: Int? = null,

	@field:SerializedName("video")
	val video: Video? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("parentalRating")
	val parentalRating: Any? = null,

	@field:SerializedName("organizationId")
	val organizationId: Any? = null,

	@field:SerializedName("lastUpdated")
	val lastUpdated: Long? = null,

	@field:SerializedName("shareCount")
	val shareCount: Int? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Long? = null,

	@field:SerializedName("premium")
	val premium: Boolean? = null,

	@field:SerializedName("customContent")
	val customContent: Any? = null,

	@field:SerializedName("articleContent")
	val articleContent: Any? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("sku")
	val sku: String? = null,

	@field:SerializedName("isAlreadyLike")
    var isAlreadyLike: Boolean = false,

	@field:SerializedName("isAlreadyFollow")
	var isFollowing: Boolean = false,

	@field:SerializedName("isAlreadyFavorite")
	var isFavorite: Boolean = false,

	@field:SerializedName("analyticsId")
	val analyticsId: Any? = null,

	@field:SerializedName("contentType")
	val contentType: String? = null,

	@field:SerializedName("src")
	var src: String? = null,

	@field:SerializedName("images")
	val images:List<com.enveu.beanModelV3.videoDetailV3.list.ImagesItem>? = null,

	@field:SerializedName("audioContent")
	val audioContent: Any? = null,

	@field:SerializedName("styleInfo")
	val styleInfo: Any? = null,

	@field:SerializedName("mediaType")
	val mediaType: String? = null,

	@field:SerializedName("seoInfo")
	val seoInfo: Any? = null,

	@field:SerializedName("commentCount")
	var commentCount: Int? = null,

	@field:SerializedName("playCount")
	val playCount: Int? = null,

	@field:SerializedName("contentSource")
	val contentSource: String? = null,

	@field:SerializedName("contentReviewRating")
	val contentReviewRating: Any? = null,

	@field:SerializedName("targetingTags")
	val targetingTags: Any? = null,

	@field:SerializedName("publishedDate")
	val publishedDate: Long? = null,

	@field:SerializedName("parentContent")
	val parentContent: Any? = null,

	@field:SerializedName("personContent")
	val personContent: Any? = null,

	@field:SerializedName("reelsNavigationInfo")
	var reelsNavigationInfo: ReelsNavigationInfo? = null
):Serializable

data class PlaylistData(

	@field:SerializedName("displayDescription")
	val displayDescription: String? = null,

	@field:SerializedName("displayTitle")
	val displayTitle: String? = null,

	@field:SerializedName("images")
	val images: List<ImagesItem?>? = null,

	@field:SerializedName("items")
	val items: ArrayList<ReelsContentItem?>? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("pageSize")
	val pageSize: Int? = null,

	@field:SerializedName("totalElements")
	val totalElements: Int? = null,

	@field:SerializedName("pageNumber")
	val pageNumber: Int? = null,

	@field:SerializedName("totalPages")
	val totalPages: Int? = null,


):Serializable

data class Video(

	@field:SerializedName("externalUrl")
	val externalUrl: Any? = null,

	@field:SerializedName("vastTag")
	val vastTag: Any? = null,

	@field:SerializedName("seasons")
	val seasons: Any? = null,

	@field:SerializedName("images")
	val images: Any? = null,

	@field:SerializedName("plays")
	val plays: Int? = null,

	@field:SerializedName("episodeNo")
	val episodeNo: Any? = null,

	@field:SerializedName("videoType")
	val videoType: String? = null,

	@field:SerializedName("textTracks")
	val textTracks: Any? = null,

	@field:SerializedName("seasonNo")
	val seasonNo: Any? = null,

	@field:SerializedName("chapterNo")
	val chapterNo: Any? = null,

	@field:SerializedName("duration")
	val duration: Int? = null,

	@field:SerializedName("audioTracks")
	val audioTracks: Any? = null,

	@field:SerializedName("mediaPlaybackType")
	val mediaPlaybackType: Any? = null,

	@field:SerializedName("vMap")
	val vMap: Any? = null,

	@field:SerializedName("cuePoints")
	val cuePoints: Any? = null,

	@field:SerializedName("isAdSupported")
	val isAdSupported: Any? = null,

	@field:SerializedName("drmDisabled")
	val drmDisabled: Any? = null,

	@field:SerializedName("offlineEnabled")
	val offlineEnabled: Any? = null,

	@field:SerializedName("adsType")
	val adsType: Any? = null,

	@field:SerializedName("hostingSource")
	val hostingSource: String? = null
):Serializable


data class ReelsNavigationInfo(

	@field:SerializedName("reelsPosition")
	var reelsPosition: Int? = null,

	@field:SerializedName("reelHonorId")
	var reelHonorId: Long? = null,

	@field:SerializedName("totalReels")
	var totalReels: Int? = null,

	@field:SerializedName("loadedPageNumber")
	var loadedPageNumber: Int? = null,

	@field:SerializedName("reelsPageNumber")
	var reelsPageNumber: Int? = null,

	@field:SerializedName("shouldExtraApiCall")
	var shouldExtraApiCall: Boolean = true,

	@field:SerializedName("navigatePage")
	var navigatePage: String = Constants.UGC_SECTION,

):Serializable

data class AllReelsListItems(

	@field:SerializedName("reelsArrayItems")
	var reelsItemsList: ArrayList<ReelsContentItem?>? = null,
):Serializable
