package com.enveu.networking.response

import com.google.gson.annotations.SerializedName

data class HashTagsSearchResponse(

	@field:SerializedName("data")
	val data: HashTagsData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	val responseCode: Int? = null
)

data class PageInfo(

	@field:SerializedName("perpage")
	val perpage: Int? = null,

	@field:SerializedName("total")
	val total: Int? = null,

	@field:SerializedName("pages")
	val pages: Int? = null,

	@field:SerializedName("field")
	val field: Any? = null,

	@field:SerializedName("page")
	val page: Any? = null,

	@field:SerializedName("sort")
	val sort: Any? = null
)

data class HashTagsItems(

	@field:SerializedName("contentsCount")
	val contentsCount: Int? = null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("id")
	val id: String? = null,

	@field:SerializedName("status")
	val status: String? = null
)

data class HashTagsData(

	@field:SerializedName("pageInfo")
	val pageInfo: PageInfo? = null,

	@field:SerializedName("items")
	val items: ArrayList<HashTagsItems?>? = null
)
