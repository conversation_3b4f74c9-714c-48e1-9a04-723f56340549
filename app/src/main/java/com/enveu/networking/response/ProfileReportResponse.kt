package com.enveu.networking.response

import com.google.gson.annotations.SerializedName

data class ProfileReportResponse(

	@field:SerializedName("data")
	val data: ProfileReportData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	var responseCode: Int? = null
)

data class ProfileReportData(

	@field:SerializedName("pageNumber")
	val pageNumber: Int? = null,

	@field:SerializedName("totalPages")
	val totalPages: Int? = null,

	@field:SerializedName("pageSize")
	val pageSize: Int? = null,

	@field:SerializedName("items")
	val items: ArrayList<ProfileReportItem?>? = null,

	@field:SerializedName("totalElements")
	val totalElements: Int? = null
)

data class ProfileReportItem(

	@field:SerializedName("reason")
	val reason: String? = null,

	@field:SerializedName("reasonOrder")
	val reasonOrder: Int? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("customReason")
	var customReason: String? = null


)
