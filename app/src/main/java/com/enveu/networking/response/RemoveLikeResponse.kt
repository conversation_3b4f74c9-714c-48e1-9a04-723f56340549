package com.enveu.networking.response

import com.google.gson.annotations.SerializedName

data class RemoveLikeResponse(

	@field:SerializedName("data")
	val data: RemoveLikeData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	var responseCode: Int? = null
)

data class RemoveLikeData(

	@field:SerializedName("owner")
	val owner: Any? = null,

	@field:SerializedName("images")
	val images: Any? = null,

	@field:SerializedName("subscribedUsers")
	val subscribedUsers: Any? = null,

	@field:SerializedName("orderedContents")
	val orderedContents: Any? = null,

	@field:SerializedName("description")
	val description: String? = null,

	@field:SerializedName("playlistSlug")
	val playlistSlug: String? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("type")
	val type: String? = null,

	@field:SerializedName("lastUpdated")
	val lastUpdated: Long? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Long? = null,

	@field:SerializedName("contents")
	val contents: Any? = null,

	@field:SerializedName("subscriberCount")
	val subscriberCount: Int? = null,

	@field:SerializedName("subscribedByRequestedUser")
	val subscribedByRequestedUser: Boolean? = null,

	@field:SerializedName("collaborators")
	val collaborators: Any? = null,

	@field:SerializedName("id")
	val id: String? = null,

	@field:SerializedName("contentCount")
	val contentCount: Int? = null,

	@field:SerializedName("status")
	val status: String? = null
)
