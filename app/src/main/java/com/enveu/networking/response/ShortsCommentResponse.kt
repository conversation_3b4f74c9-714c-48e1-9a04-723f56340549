package com.enveu.networking.response

import com.google.gson.annotations.SerializedName

data class ShortsCommentResponse(

	@field:SerializedName("data")
	val data: ShortsCommentData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	var responseCode: Int? = null
)

data class ShortsCreator(

	@field:SerializedName("profilePicURL")
	var profilePicURL: String? = null,

	@field:SerializedName("name")
	var name: String? = null,

	@field:SerializedName("id")
	var id: String? = null,

	@field:SerializedName("userName")
	var userName: String? = null,

	@field:SerializedName("creatorId")
    var creatorId: String? = null,
)

data class ShortsCommentData(

	@field:SerializedName("pageNumber")
	val pageNumber: Int? = null,

	@field:SerializedName("totalPages")
	val totalPages: Int? = null,

	@field:SerializedName("pageSize")
	val pageSize: Int? = null,

	@field:SerializedName("items")
	val items: ArrayList<ShortsCommentItem?>? = null,

	@field:SerializedName("totalElements")
	val totalElements: Int? = null
)

data class ShortsCommentItem(

	@field:SerializedName("lastUpdated")
	var lastUpdated: Any? = null,

	@field:SerializedName("creator")
	var creator: ShortsCreator? = null,

	@field:SerializedName("replyCount")
	val replyCount: Int? = null,

	@field:SerializedName("dateCreated")
	var dateCreated: Long? = null,

	@field:SerializedName("contentId")
	var contentId: Int? = null,

	@field:SerializedName("commentId")
	var commentId: String? = null,

	@field:SerializedName("comment")
	var comment: String? = null,

	@field:SerializedName("status")
	val status: String? = null
)
