package com.enveu.networking.response

import com.google.gson.annotations.SerializedName

data class SubmitReasonResponse(

	@field:SerializedName("data")
	val data: SubmitReasonData? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	var responseCode: Int? = null
)

data class SubmitReasonData(

	@field:SerializedName("lastUpdated")
	val lastUpdated: Long? = null,

	@field:SerializedName("other")
	val other: Any? = null,

	@field:SerializedName("dateCreated")
	val dateCreated: Long? = null,

	@field:SerializedName("reasonId")
	val reasonId: Int? = null,

	@field:SerializedName("entityType")
	val entityType: String? = null,

	@field:SerializedName("entityId")
	val entityId: String? = null,

	@field:SerializedName("id")
	val id: String? = null,

	@field:SerializedName("appUserId")
	val appUserId: Int? = null,

	@field:SerializedName("status")
	val status: String? = null
)
