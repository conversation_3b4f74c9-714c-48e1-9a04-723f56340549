package com.enveu.networking.response

import com.google.gson.annotations.SerializedName

data class UniqueUserNameResponse(

	@field:SerializedName("data")
	val data: Data? = null,

	@field:SerializedName("debugMessage")
	val debugMessage: Any? = null,

	@field:SerializedName("responseCode")
	var responseCode: Int? = null
)

data class Data(

	@field:SerializedName("suggestion")
	val suggestion: ArrayList<String>? = null,

	@field:SerializedName("exists")
	val exists: Boolean? = null
)
