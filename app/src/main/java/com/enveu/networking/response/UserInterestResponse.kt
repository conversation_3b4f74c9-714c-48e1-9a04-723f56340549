package com.enveu.networking.response

import com.enveu.beanModelV3.videoDetailV3.list.ImagesItem
import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class UserInterestResponse(

    @field:SerializedName("data")
    val data: InterestData? = null,

    @field:SerializedName("debugMessage")
    val debugMessage: Any? = null,

    @field:SerializedName("responseCode")
    var responseCode: Int? = null
)

data class InterestData(

    @field:SerializedName("pageNumber")
    val pageNumber: Int? = null,

    @field:SerializedName("totalPages")
    val totalPages: Int? = null,

    @field:SerializedName("pageSize")
    val pageSize: Int? = null,

    @field:SerializedName("items")
    val items: ArrayList<InterestItems?>? = null,

    @field:SerializedName("totalElements")
    val totalElements: Int? = null
)

data class ImageContent(

    @field:SerializedName("originalImageSizeInBytes")
    val originalImageSizeInBytes: Int? = null,

    @field:SerializedName("src")
    val src: String? = null,

    @field:SerializedName("imageKey")
    val imageKey: String? = null,

    @field:SerializedName("dominantColor")
    val dominantColor: String? = null,

    @field:SerializedName("isDefault")
    val isDefault: Boolean? = null,

    @field:SerializedName("showTitle")
    val showTitle: Any? = null,

    @field:SerializedName("width")
    val width: Double? = null,

    @field:SerializedName("id")
    val id: Int? = null,

    @field:SerializedName("tag")
    val tag: String? = null,

    @field:SerializedName("colorPalette")
    val colorPalette: List<String?>? = null,

    @field:SerializedName("imageType")
    val imageType: String? = null,

    @field:SerializedName("height")
    val height: Double? = null,

    @field:SerializedName("status")
    val status: Any? = null
)

data class ImagesItem(


//	val id: Int?,
//	val imageKey: String?,
//	val src: String?,
//	val height: Double?,
//	val width: Double?,
//	val colorPalette: List<String>?,
//	val dominantColor: String?,
//	val imageType: String?,
//	val status: Any?,
//	val tag: String?,
//	val originalImageSizeInBytes: Int?,
//	val isDefault: Boolean?,
//	val showTitle: Any?

    @field:SerializedName("id")
    val id: String? = null,

    @field:SerializedName("imageKey")
    val imageKey: String? = null,

    @field:SerializedName("src")
    var src: String? = null,
    @field:SerializedName("imageType")
    val imageType: String? = null
) : Serializable

data class CustomContent(

    @field:SerializedName("lastUpdated")
    val lastUpdated: Any? = null,

    @field:SerializedName("customType")
    val customType: String? = null,

    @field:SerializedName("images")
    val images: Any? = null,

    @field:SerializedName("dateCreated")
    val dateCreated: Any? = null,

    @field:SerializedName("id")
    val id: Int? = null
)

data class InterestItems(

    @field:SerializedName("longDescription")
    val longDescription: Any? = null,

    @field:SerializedName("liveContent")
    val liveContent: Any? = null,

    @field:SerializedName("imageContent")
    val imageContent: Any? = null,

    @field:SerializedName("keywords")
    val keywords: Any? = null,

    @field:SerializedName("externalRefId")
    val externalRefId: Any? = null,

    @field:SerializedName("accessibility")
    val accessibility: Any? = null,

    @field:SerializedName("description")
    val description: Any? = null,

    @field:SerializedName("contentSlug")
    val contentSlug: String? = null,

    @field:SerializedName("customData")
    val customData: Any? = null,

    @field:SerializedName("video")
    val video: Any? = null,

    @field:SerializedName("isSelected")
    val isSelected: Boolean? = null,

    @field:SerializedName("title")
    val title: String? = null,

    @field:SerializedName("parentalRating")
    val parentalRating: Any? = null,

    @field:SerializedName("organizationId")
    val organizationId: Any? = null,

    @field:SerializedName("lastUpdated")
    val lastUpdated: Long? = null,

    @field:SerializedName("dateCreated")
    val dateCreated: Long? = null,

    @field:SerializedName("premium")
    val premium: Boolean? = null,

    @field:SerializedName("customContent")
    val customContent: CustomContent? = null,

    @field:SerializedName("articleContent")
    val articleContent: Any? = null,

    @field:SerializedName("id")
    val id: Int? = null,

    @field:SerializedName("sku")
    val sku: String? = null,

    @field:SerializedName("analyticsId")
    val analyticsId: Any? = null,

    @field:SerializedName("contentType")
    val contentType: String? = null,

    @field:SerializedName("images")
    val images: List<ImagesItem>? = null,

    @field:SerializedName("audioContent")
    val audioContent: Any? = null,

    @field:SerializedName("styleInfo")
    val styleInfo: Any? = null,

    @field:SerializedName("mediaType")
    val mediaType: String? = null,

    @field:SerializedName("seoInfo")
    val seoInfo: Any? = null,

    @field:SerializedName("playCount")
    val playCount: Int? = null,

    @field:SerializedName("contentSource")
    val contentSource: String? = null,

    @field:SerializedName("contentReviewRating")
    val contentReviewRating: Any? = null,

    @field:SerializedName("targetingTags")
    val targetingTags: Any? = null,

    @field:SerializedName("publishedDate")
    val publishedDate: Any? = null,

    @field:SerializedName("parentContent")
    val parentContent: Any? = null,

    @field:SerializedName("personContent")
    val personContent: Any? = null
)
