package com.enveu.networking.response.screen_id

import com.google.gson.annotations.SerializedName

data class ScreenIdResponse(

	@field:SerializedName("data")
	val data: Data? = null,

	@field:SerializedName("responseCode")
	val responseCode: Int? = null
)

data class CustomFields(
	val any: Any? = null
)

data class WidgetsItem(

	@field:SerializedName("isRandomized")
	val isRandomized: Boolean? = null,

	@field:SerializedName("endDate")
	val endDate: Any? = null,

	@field:SerializedName("imageIdentifier")
	val imageIdentifier: String? = null,

	@field:SerializedName("customFields")
	val customFields: CustomFields? = null,

	@field:SerializedName("displayOrder")
	val displayOrder: Int? = null,

	@field:SerializedName("description")
	val description: String? = null,

	@field:SerializedName("isScheduled")
	val isScheduled: Boolean? = null,

	@field:SerializedName("type")
	val type: String? = null,

	@field:SerializedName("webBGImage")
	val webBGImage: Any? = null,

	@field:SerializedName("mobileBGImage")
	val mobileBGImage: Any? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("containerId")
	val containerId: String? = null,

	@field:SerializedName("tvBGImage")
	val tvBGImage: Any? = null,

	@field:SerializedName("height")
	val height: Any? = null,

	@field:SerializedName("item")
	val item: Item? = null,

	@field:SerializedName("kalturaOTTImageType")
	val kalturaOTTImageType: Any? = null,

	@field:SerializedName("layout")
	val layout: String? = null,

	@field:SerializedName("mainImage")
	val mainImage: Any? = null,

	@field:SerializedName("appScreens")
	val appScreens: Any? = null,

	@field:SerializedName("name")
	var name: String? = null,

	@field:SerializedName("width")
	val width: Any? = null,

	@field:SerializedName("supportedDevices")
	val supportedDevices: List<SupportedDevicesItem?>? = null,

	@field:SerializedName("targetingTags")
	val targetingTags: List<Any?>? = null,

	@field:SerializedName("startDate")
	val startDate: Any? = null,

	@field:SerializedName("status")
	val status: String? = null,

	@field:SerializedName("brightcoveImageType")
	val brightcoveImageType: Any? = null
)

data class SupportedDevicesItem(

	@field:SerializedName("deviceIdentifier")
	val deviceIdentifier: String? = null,

	@field:SerializedName("formFactor")
	val formFactor: Any? = null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("state")
	val state: String? = null,

	@field:SerializedName("shortName")
	val shortName: String? = null,

	@field:SerializedName("platform")
	val platform: Any? = null
)

data class MultilingualTitle(
	@field:SerializedName("en-US")
	val english: String? = null,

	@field:SerializedName("ar-SA")
	val arabic: String? = null,
)

data class MoreViewConfig(

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("filters")
	val filters: List<Any?>? = null,

	@field:SerializedName("sortable")
	val sortable: Boolean? = null
)

data class Playlist(

	@field:SerializedName("playlistId")
	val playlistId: String? = null,

	@field:SerializedName("kalturaChannelId")
	val kalturaChannelId: Any? = null,

	@field:SerializedName("predefPlaylistType")
	val predefPlaylistType: Any? = null,

	@field:SerializedName("forLoggedInUser")
	val forLoggedInUser: Any? = null,

	@field:SerializedName("brightcovePlaylistId")
	val brightcovePlaylistId: String? = null,

	@field:SerializedName("mediaPlaylistType")
	val mediaPlaylistType: String? = null,

	@field:SerializedName("forAnonymousUser")
	val forAnonymousUser: Any? = null,

	@field:SerializedName("externalPlaylistId")
	val externalPlaylistId: Any? = null,

	@field:SerializedName("type")
	val type: String? = null,

	@field:SerializedName("playlistName")
	val playlistName: String? = null,

	@field:SerializedName("tags")
	val tags: List<Any?>? = null,

	@field:SerializedName("enveuPlaylistId")
	val enveuPlaylistId: Int? = null,

	@field:SerializedName("mediaPlaylistSource")
	val mediaPlaylistSource: String? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("kEntryId")
	val kEntryId: Any? = null,

	@field:SerializedName("referenceName")
	val referenceName: String? = null,

	@field:SerializedName("status")
	val status: String? = null
)

data class Item(

	@field:SerializedName("railCardType")
	val railCardType: String? = null,

	@field:SerializedName("multilingualTitle")
	val multilingualTitle: MultilingualTitle? = null,

	@field:SerializedName("showHeader")
	val showHeader: Boolean? = null,

	@field:SerializedName("columns")
	val columns: Any? = null,

	@field:SerializedName("pageSize")
	val pageSize: Int? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("rows")
	val rows: Any? = null,

	@field:SerializedName("enableMultilingualTitle")
	val enableMultilingualTitle: Boolean? = null,

	@field:SerializedName("moreViewConfig")
	val moreViewConfig: MoreViewConfig? = null,

	@field:SerializedName("showMoreButton")
	val showMoreButton: Boolean? = null,

	@field:SerializedName("playlist")
	val playlist: Playlist? = null,

	@field:SerializedName("listingLayout")
	val listingLayout: String? = null,

	@field:SerializedName("listingLayoutContentSize")
	val listingLayoutContentSize: Int? = null,

	@field:SerializedName("imageType")
	val imageType: String? = null,

	@field:SerializedName("railCardSize")
	val railCardSize: String? = null
)

data class Data(

	@field:SerializedName("settings")
	val settings: Any? = null,

	@field:SerializedName("screen")
	val screen: String? = null,

	@field:SerializedName("expiry")
	val expiry: Long? = null,

	@field:SerializedName("widgets")
	val widgets: List<WidgetsItem?>? = null
)
