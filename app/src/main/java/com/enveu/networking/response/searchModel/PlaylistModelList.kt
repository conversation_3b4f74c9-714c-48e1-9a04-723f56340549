package com.enveu.networking.response.searchModel

import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.networking.response.playlist_response.ContentItemsList
import com.enveu.networking.response.screen_id.WidgetsItem
import com.google.gson.annotations.SerializedName

data class PlaylistModelList(
    @field:SerializedName("widgets")
    var widgets: WidgetsItem? = null,
    @field:SerializedName("items")
    var items: List<EnveuVideoItemBean?>? = null,
)