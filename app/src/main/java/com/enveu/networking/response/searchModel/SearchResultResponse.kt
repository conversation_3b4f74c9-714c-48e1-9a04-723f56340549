package com.enveu.networking.response.searchModel

import com.enveu.beanModelV3.searchV2.PageInfo
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.google.gson.annotations.SerializedName

data class SearchResultResponse(

    @field:SerializedName("data")
    var data: Data? = null,

    @field:SerializedName("debugMessage")
    val debugMessage: Any? = null,

    @field:SerializedName("responseCode")
    val responseCode: Int? = null
)

data class Data(

    @field:SerializedName("pageInfo")
    var pageInfo: PageInfo? = null,

    @field:SerializedName("items")
    var items: ArrayList<EnveuVideoItemBean?>? = null
)
