package com.enveu.networking.servicelayer;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.enveu.activities.PlayableCallBack;
import com.enveu.activities.watchList.model.VideoIdModel;
import com.enveu.beanModel.drm.DRM;
import com.enveu.beanModel.entitle.ResponseEntitle;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModel.responseModels.genre_response.GenreResponse;
import com.enveu.beanModel.sponsorUserTracking.UserTrackingResponse;
import com.enveu.beanModelV3.RelatedRailsCommonData;
import com.enveu.beanModelV3.continueWatching.DataItem;
import com.enveu.beanModelV3.playListModelV2.EnveuCommonResponse;
import com.enveu.beanModelV3.searchGenres.SearchGenres;
import com.enveu.beanModelV3.searchHistory.SearchHistory;
import com.enveu.beanModelV3.searchRating.SearchRating;
import com.enveu.beanModelV3.searchV2.Data;
import com.enveu.beanModelV3.searchV2.ResponseSearch;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ArtistListResponse;
import com.enveu.beanModelV3.videoDetailV3.queue_response.AddToQueueRequestModel;
import com.enveu.beanModelV3.videoDetailV3.queue_response.AddToQueueResponse;
import com.enveu.beanModelV3.videoDetailV3.queue_response.GetQueueResponse;
import com.enveu.beanModelV3.videoDetailV3.queue_response.RemoveQueueResponse;
import com.enveu.beanModelV3.videoDetailsV2.EnveuVideoDetailsBean;
import com.enveu.bean_model_v2_0.geo.GeoResponse;
import com.enveu.bean_model_v2_0.listAll.ListAllContent;
import com.enveu.bean_model_v2_0.videoDetailBean.EnvVideoDetailsBean;
import com.enveu.callbacks.RequestOffferCallBack.RequestOfferCallBack;
import com.enveu.callbacks.apicallback.ApiResponseModel;
import com.enveu.callbacks.apicallback.GeoInfoListener;
import com.enveu.callbacks.commonCallbacks.CommonApiCallBack;
import com.enveu.callbacks.likelistCallback.ApiLikeList;
import com.enveu.client.api_callback.NetworkResultCallback;
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory;
import com.enveu.client.baseCollection.baseCategoryServices.BaseCategoryServices;
import com.enveu.client.bookmarking.bean.continuewatching.ContinueWatchingBookmark;
import com.enveu.client.callBacks.EnveuCallBacks;
import com.enveu.client.playlist.beanv2_0.EnvPlaylistContents;
import com.enveu.client.playlist.callBacks.EnvPlaylistResponse;
import com.enveu.client.watchHistory.beans.ItemsItem;
import com.enveu.jwplayer.player.ChromeCastUrlResponse;
import com.enveu.networking.apiendpoints.ApiInterface;
import com.enveu.networking.apiendpoints.RequestConfig;
import com.enveu.networking.detailPlayer.APIDetails;
import com.enveu.networking.errormodel.ApiErrorModel;
import com.enveu.utils.Logger;
import com.enveu.utils.MediaTypeConstants;
import com.enveu.utils.ObjectHelper;
import com.enveu.utils.SearchApiParams;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.commonMethods.AppConfigMethod;
import com.enveu.utils.config.ImageLayer;
import com.enveu.utils.config.LanguageLayer;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class APIServiceLayer {

    private static APIServiceLayer projectRepository;
    private static ApiInterface endpoint;
    private ApiResponseModel callBack;
    private List<EnveuVideoItemBean> enveuVideoItemBeans;
    private List<SearchApiParams> searchApiParams;
    private static final String API_SERVICE_LAYER = "APIServiceLayer";
    String genresItems = "";


    public synchronized static APIServiceLayer getInstance() {
        if (projectRepository == null) {
            if (RequestConfig.getEnveuClient() != null) {
                endpoint = RequestConfig.getEnveuClient().create(ApiInterface.class);
            }
            projectRepository = new APIServiceLayer();
        }
        return projectRepository;
    }

    public LiveData<List<BaseCategory>> getCategories(String screenId) {
        MutableLiveData<List<BaseCategory>> liveData = new MutableLiveData<>();
        BaseCategoryServices.Companion.getInstance().categoryService("v2/", screenId, new EnveuCallBacks() {
            @Override
            public void success(boolean status, List<BaseCategory> categoryList) {
                if (status) {
                    Collections.sort(categoryList, new Comparator<BaseCategory>() {
                        @Override
                        public int compare(BaseCategory o1, BaseCategory o2) {
                            return o1.getDisplayOrder().compareTo(o2.getDisplayOrder());
                        }
                    });
                    liveData.postValue(categoryList);
                }
            }

            @Override
            public void failure(boolean status, int errorCode, String errorMessage) {
                liveData.postValue(new ArrayList<>());
            }
        });
        return liveData;
    }

    String languageCode = "";

    public void getPlayListById(String playListId, String accessToken, int pageNumber, int pageSize, String genres, NetworkResultCallback<EnvPlaylistContents> networkResultCallback) {
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        BaseCategoryServices.Companion.getInstance().getEnvPlaylistDetailsById(playListId, accessToken, languageCode, pageNumber, pageSize, "",genres, new EnvPlaylistResponse() {
            @Override
            public void success(boolean status, @NonNull Response<EnvPlaylistContents> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getData() != null && response.body().getData().getItems().size() > 0) {
                        if (response.body().getResponseCode() == 2000) {
                            networkResultCallback.success(true,response);
                        } else {
                            networkResultCallback.success(false,null);
                        }
                    } else {
                        networkResultCallback.success(false,null);
//                        enveuCommonResponseMutableLiveData.postValue(null);
                    }
                } else {
                    networkResultCallback.failure(true,500,"API not succsess");
//                    enveuCommonResponseMutableLiveData.postValue(null);
                }
            }

            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                networkResultCallback.failure(true,errorCode,message);
                Log.d("HomeDataCall","at getPlayListById, response failed");
                Log.d("getPlayListById", "getPlayListById: " + playListId);
//                enveuCommonResponseMutableLiveData.postValue(null);
            }
        });


//        return enveuCommonResponseMutableLiveData;
    }

    public MutableLiveData<JsonObject> getEPGData(String channelIds,long startDateTime,long endDateTime, int size){
        Map<String, String> queryParams = new HashMap<>();
        MutableLiveData<JsonObject> responseToSendBack = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getEnveuClient().create(ApiInterface.class);
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        queryParams.put("channelIds",channelIds);
        queryParams.put("startDateTime", String.valueOf(startDateTime));
        queryParams.put("endDateTime", String.valueOf(endDateTime));
        queryParams.put("size", String.valueOf(size));
        queryParams.put("locale", languageCode);

        Call<JsonObject> call = endpoint.getEPGData(queryParams);
        call.enqueue(new Callback<JsonObject>() {
            @Override
            public void onResponse(Call<JsonObject> call, Response<JsonObject> response) {
                if (response.body() != null && response.isSuccessful()){
                    responseToSendBack.postValue(response.body());
                }else {
                    responseToSendBack.postValue(null);
                }
            }

            @Override
            public void onFailure(Call<JsonObject> call, Throwable t) {
                responseToSendBack.postValue(null);
            }
        });
        return responseToSendBack;
    }

    public void getGeoInfo(GeoInfoListener geoInfoListener) {
        ApiInterface endpoint = RequestConfig.getEnveuClient().create(ApiInterface.class);
        if (endpoint != null) {
            endpoint.getGeoInfo().enqueue(new Callback<GeoResponse>() {
                @Override
                public void onResponse(Call<GeoResponse> call, Response<GeoResponse> response) {
                    if (response.body() != null) {
                        geoInfoListener.getGeoInfo(true, response.body());
                    } else {
                        geoInfoListener.getGeoInfo(false, null);
                    }
                }

                @Override
                public void onFailure(Call<GeoResponse> call, Throwable t) {
                    geoInfoListener.getGeoInfo(false, null);

                }
            });

        }


    }


    public void getAssetTypeHero(String manualImageAssetId, CommonApiCallBack commonApiCallBack) {
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {

            endpoint.getVideoDetails(manualImageAssetId, languageCode).enqueue(new Callback<EnveuVideoDetailsBean>() {
                @Override
                public void onResponse(Call<EnveuVideoDetailsBean> call, Response<EnveuVideoDetailsBean> response) {
                    if (response.isSuccessful()) {
                        commonApiCallBack.onSuccess(response.body().getData());
                    } else {
                        commonApiCallBack.onFailure(new Throwable("Details Not Found"));

                    }
                }

                @Override
                public void onFailure(Call<EnveuVideoDetailsBean> call, Throwable t) {
                    commonApiCallBack.onFailure(new Throwable("Details Not Found"));

                }
            });

        }
    }

    public void getPlayListByWithPagination(String playlistID,
                                            int pageNumber,
                                            int pageSize,
                                            BaseCategory screenWidget, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            endpoint.getPlaylistDetailsById(KsPreferenceKeys.getInstance().getAppPrefAccessToken(), playlistID, languageCode, pageNumber, pageSize).enqueue(new Callback<>() {
                @Override
                public void onResponse(Call<EnveuCommonResponse> call, Response<EnveuCommonResponse> response) {
                    if (response.body() != null && response.body().getData() != null) {
                        RailCommonData railCommonData = new RailCommonData(response.body().getData(), screenWidget, false, 0);
                        railCommonData.setStatus(true);
                        callBack.onSuccess(railCommonData);
                    } else {
                        ApiErrorModel errorModel = new ApiErrorModel(response.code(), response.message());
                        callBack.onError(errorModel);
                    }
                }

                @Override
                public void onFailure(Call<EnveuCommonResponse> call, Throwable t) {
                    ApiErrorModel errorModel = new ApiErrorModel(500, t.getMessage());
                    callBack.onFailure(errorModel);
                }
            });

        }

    }

    private void parseEpisodeResponseAsRailCommonData(Response<ListAllContent> response) {
        if (response.body() != null && response.body().getData() != null) {
            if (response.body().getData().getPageNumber() == 0 && response.body().getData().getTotalElements() == 0) {
                ApiErrorModel errorModel = new ApiErrorModel(500, "");
                Log.d("DetailPage","at parseEpisodeResponseAsRailCommonData, fail");
                callBack.onError(errorModel);
            } else {
                RailCommonData railCommonData = new RailCommonData(response.body().getData());
                railCommonData.setStatus(true);
                try {
                    Log.d("DetailPage","at parseEpisodeResponseAsRailCommonData, page data done");
                    railCommonData.setTotalCount(response.body().getData().getTotalElements());
                    railCommonData.setPageTotal(response.body().getData().getTotalPages());
                } catch (Exception e) {
                    Log.d("DetailPage","at parseEpisodeResponseAsRailCommonData, exception of page data");
                    Log.e(API_SERVICE_LAYER, "parseEpisodeResponseAsRailCommonData: ", e);
                }
                Log.d("DetailPage","at parseEpisodeResponseAsRailCommonData, going for success");
                callBack.onSuccess(railCommonData);
            }

        } else {
            ApiErrorModel errorModel = new ApiErrorModel(response.code(), response.message());
            Log.d("DetailPage","at parseEpisodeResponseAsRailCommonData, response null");
            callBack.onError(errorModel);
        }

    }


    public void getSeasonEpisodesV2(int seriesId, int pageNumber, int size, String customData, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            String finalCustomData = customData + seriesId;
            endpoint.getSeasonEpisodesV2(finalCustomData, pageNumber, size, "CUSTOM_DATA", "season-number", "NUMBER", languageCode).enqueue(new Callback<ListAllContent>() {
                @Override
                public void onResponse(@NonNull Call<ListAllContent> call, @NonNull Response<ListAllContent> response) {
                    Log.d("DetailPage","at getSeasonEpisodesV2, success");
                    parseEpisodeResponseAsRailCommonData(response);
                }

                @Override
                public void onFailure(@NonNull Call<ListAllContent> call, @NonNull Throwable t) {
                    ApiErrorModel errorModel = new ApiErrorModel(500, t.getMessage());
                    Log.d("DetailPage","at getSeasonEpisodesV2, failiur "+t.getMessage());
                    callBack.onFailure(errorModel);
                }
            });
        }

    }


    public void getRoundForSeason(int seriesId, int pageNumber, int size, String customData, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            String finalCustomData = customData + seriesId;
            endpoint.getSeasonEpisodesV2(finalCustomData, pageNumber, size, "CUSTOM_DATA", "round-number", "NUMBER", languageCode).enqueue(new Callback<ListAllContent>() {
                @Override
                public void onResponse(@NonNull Call<ListAllContent> call, @NonNull Response<ListAllContent> response) {
                    parseEpisodeResponseAsRailCommonData(response);
                }

                @Override
                public void onFailure(@NonNull Call<ListAllContent> call, @NonNull Throwable t) {
                    ApiErrorModel errorModel = new ApiErrorModel(500, t.getMessage());
                    callBack.onFailure(errorModel);
                }
            });
        }

    }

    public void getMatchForRound(int seriesId, int pageNumber, int size, String customData, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            String finalCustomData = customData + seriesId;
            endpoint.getSeasonEpisodesV2(finalCustomData, pageNumber, size, "CUSTOM_DATA", "match-start-date-time", "NUMBER", languageCode).enqueue(new Callback<ListAllContent>() {
                @Override
                public void onResponse(@NonNull Call<ListAllContent> call, @NonNull Response<ListAllContent> response) {
                    parseEpisodeResponseAsRailCommonData(response);
                }

                @Override
                public void onFailure(@NonNull Call<ListAllContent> call, @NonNull Throwable t) {
                    ApiErrorModel errorModel = new ApiErrorModel(500, t.getMessage());
                    callBack.onFailure(errorModel);
                }
            });
        }

    }


    public void getAllEpisodesV2(int seasonId, int pageNumber, int size, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            String customData = "episode-season-id|OR:" + seasonId;

            endpoint.getAllEpisodesV2(customData, pageNumber, size, "EPISODE_NUMBER", languageCode).enqueue(new Callback<ListAllContent>() {
                @Override
                public void onResponse(@NonNull Call<ListAllContent> call, @NonNull Response<ListAllContent> response) {
                    parseEpisodeResponseAsRailCommonData(response);
                }

                @Override
                public void onFailure(@NonNull Call<ListAllContent> call, @NonNull Throwable t) {
                    ApiErrorModel errorModel = new ApiErrorModel(500, t.getMessage());
                    callBack.onFailure(errorModel);
                }
            });
        }


    }

    public void getLiveEventMatch(int matchId, int pageNumber, int size, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            String customData = "live-event-match-id|OR:" + matchId;

            endpoint.getLiveEventMatch(customData, pageNumber, size, languageCode).enqueue(new Callback<ListAllContent>() {
                @Override
                public void onResponse(@NonNull Call<ListAllContent> call, @NonNull Response<ListAllContent> response) {
                    Gson gson = new Gson();
                    String json = gson.toJson(response.body());
                    parseEpisodeResponseAsRailCommonData(response);
                }

                @Override
                public void onFailure(@NonNull Call<ListAllContent> call, @NonNull Throwable t) {
                    ApiErrorModel errorModel = new ApiErrorModel(500, t.getMessage());
                    callBack.onFailure(errorModel);
                }
            });
        }


    }

    public void getIsLikeGOI(String token, int pageNumber, int size, ApiLikeList listener) {
        ApiInterface enveuSubscriptionEndPoint = RequestConfig.getEnveuSubscriptionClient(token).create(ApiInterface.class);
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        enveuSubscriptionEndPoint.getIsLikeGOI(pageNumber, size, languageCode).enqueue(new Callback<com.enveu.bean_model_v2_0.listAll.likeList.Response>() {
            @Override
            public void onResponse(@NonNull Call<com.enveu.bean_model_v2_0.listAll.likeList.Response> call, @NonNull Response<com.enveu.bean_model_v2_0.listAll.likeList.Response> response) {
                Gson gson = new Gson();
                String json = gson.toJson(response.body());
                if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                    listener.onSuccess(true, response.body());
                } else {
                    listener.onSuccess(false, response.body());
                }
            }

            @Override
            public void onFailure(@NonNull Call<com.enveu.bean_model_v2_0.listAll.likeList.Response> call, @NonNull Throwable t) {
                listener.onFailure(false, "0", "");
            }
        });

    }


    public void getCommonListAll(int pageNumber, int size, String interViewAssetId, String customData, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            String finalCustomData = customData + interViewAssetId;
            endpoint.getCommonListAll(languageCode, pageNumber, size, finalCustomData).enqueue(new Callback<ListAllContent>() {
                @Override
                public void onResponse(@NonNull Call<ListAllContent> call, @NonNull Response<ListAllContent> response) {
                    parseEpisodeResponseAsRailCommonData(response);
                }

                @Override
                public void onFailure(@NonNull Call<ListAllContent> call, @NonNull Throwable t) {
                    ApiErrorModel errorModel = new ApiErrorModel(500, t.getMessage());
                    callBack.onFailure(errorModel);
                }
            });
        }


    }

    public void getRecommendedSongs(int pageNumber, int size, String contentType, String sortBy, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            endpoint.getRecommendedSongs(sortBy, languageCode, pageNumber, size, contentType).enqueue(new Callback<ListAllContent>() {
                @Override
                public void onResponse(@NonNull Call<ListAllContent> call, @NonNull Response<ListAllContent> response) {
                    parseEpisodeResponseAsRailCommonData(response);
                }

                @Override
                public void onFailure(@NonNull Call<ListAllContent> call, @NonNull Throwable t) {
                    ApiErrorModel errorModel = new ApiErrorModel(500, t.getMessage());
                    callBack.onFailure(errorModel);
                }
            });
        }
    }



    private void parseResponseAsRailCommonData(Response<EnveuCommonResponse> response, boolean intentFromRelatedContent) {
        if (response.body() != null && response.body().getData() != null) {
            if (response.body().getData().getPageNumber() == 0 && response.body().getData().getTotalElements() == 0) {
                ApiErrorModel errorModel = new ApiErrorModel(500, "");
                callBack.onError(errorModel);
            } else {
                RailCommonData railCommonData = new RailCommonData(response.body().getData(), intentFromRelatedContent);
                railCommonData.setStatus(true);
                try {
                    railCommonData.setTotalCount(response.body().getData().getTotalElements());
                    railCommonData.setPageTotal(response.body().getData().getTotalPages());
                } catch (Exception ignore) {

                }
                callBack.onSuccess(railCommonData);
            }

        } else {
            ApiErrorModel errorModel = new ApiErrorModel(response.code(), response.message());
            callBack.onError(errorModel);
        }

    }

    public void getSeriesData(String assetID, boolean isIntentFromExpedition, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            endpoint.getEnvVideoDetails(assetID, languageCode).enqueue(new Callback<EnvVideoDetailsBean>() {
                @Override
                public void onResponse(@NonNull Call<EnvVideoDetailsBean> call, @NonNull Response<EnvVideoDetailsBean> response) {
                    if (response.isSuccessful()) {
                        RailCommonData railCommonData = new RailCommonData();
                        AppCommonMethod.getEpisodeAssetDetail(railCommonData, response);
                        callBack.onSuccess(railCommonData);

                    } else {
                        ApiErrorModel errorModel = new ApiErrorModel(response.code(), response.message());
                        callBack.onError(errorModel);
                    }
                }

                @Override
                public void onFailure(@NonNull Call<EnvVideoDetailsBean> call, @NonNull Throwable t) {
                    ApiErrorModel errorModel = new ApiErrorModel(500, t.getMessage());
                    callBack.onFailure(errorModel);
                }
            });
        }


    }

    public MutableLiveData<RailCommonData> getSearchPopularPlayList(String playlistID, String accessToken, int pageNumber, int pageSize, BaseCategory screenWidget) {

        MutableLiveData<RailCommonData> railCommonDataMutableLiveData = new MutableLiveData<>();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        Gson gson = new Gson();
        BaseCategoryServices.Companion.getInstance().getEnvPlaylistDetailsById(playlistID,accessToken,"en-US", pageNumber, pageSize, "", getGenresItems(),new EnvPlaylistResponse() {
            @Override
            public void success(boolean status, @NonNull Response<EnvPlaylistContents> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getData() != null && response.body().getData().getItems().size() > 0) {
                        if (response.body() != null && response.body().getData() != null) {
                            String json = gson.toJson(response.body());
                            Log.w("playlistCall-->>>>>", json);
                            EnveuCommonResponse er = gson.fromJson(json, EnveuCommonResponse.class);
                            RailCommonData railCommonData = new RailCommonData(er.getData(), screenWidget, true,0);
                            railCommonData.setStatus(true);
                            railCommonDataMutableLiveData.postValue(railCommonData);
                        } else {
                            RailCommonData railCommonData = new RailCommonData();
                            railCommonData.setStatus(false);
                            railCommonDataMutableLiveData.postValue(railCommonData);
                        }
                    } else {
                        RailCommonData railCommonData = new RailCommonData();
                        railCommonData.setStatus(false);
                        railCommonDataMutableLiveData.postValue(railCommonData);
                    }
                } else {
                    RailCommonData railCommonData = new RailCommonData();
                    railCommonData.setStatus(false);
                    railCommonDataMutableLiveData.postValue(railCommonData);
                }
            }

            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                RailCommonData railCommonData = new RailCommonData();
                railCommonData.setStatus(false);
                railCommonDataMutableLiveData.postValue(railCommonData);
            }
        });

        return railCommonDataMutableLiveData;

    }

    public void getContinueWatchingVideos(List<ContinueWatchingBookmark> continueWatchingBookmarkList, String manualImageAssetId, CommonApiCallBack commonApiCallBack) {
        Gson gson = new Gson();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            endpoint.getVideoIdData(manualImageAssetId, languageCode).enqueue(new Callback<VideoIdModel>() {
                @Override
                public void onResponse(@NonNull Call<VideoIdModel> call, @NonNull Response<VideoIdModel> response) {
                    if (response.body() != null && response.isSuccessful()) {
                        ArrayList<DataItem> enveuVideoDetailsArrayList = new ArrayList<>();
                        List<VideoIdModel.Datum> list = response.body().getData();
                        String arrayData = gson.toJson(list);
                        ArrayList<DataItem> enveuVideoDetails = gson.fromJson(arrayData, new TypeToken<List<DataItem>>() {
                        }.getType());
                        for (ContinueWatchingBookmark item : continueWatchingBookmarkList) {
                            for (DataItem enveuVideoDetail : enveuVideoDetails) {
                                if (item.getAssetId().intValue() == enveuVideoDetail.getId()) {
                                    if (item.getPosition() != null) {
                                        enveuVideoDetail.setPosition(item.getPosition());
                                    }
                                    enveuVideoDetailsArrayList.add(enveuVideoDetail);
                                }
                            }
                        }
                        commonApiCallBack.onSuccess(enveuVideoDetailsArrayList);
                    } else {
                        commonApiCallBack.onFailure(new Throwable("DETAILS_NOT_FOUND"));

                    }
                }

                @Override
                public void onFailure(@NonNull Call<VideoIdModel> call, @NonNull Throwable t) {
                    commonApiCallBack.onFailure(new Throwable("DETAILS_NOT_FOUND"));

                }
            });

        }
    }

    public void getWatchListVideos(List<ItemsItem> continueWatchingBookmarkList, String manualImageAssetId, CommonApiCallBack commonApiCallBack) {
        Gson gson = new Gson();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            endpoint.getVideoIdData(removeLastComma(manualImageAssetId), languageCode).enqueue(new Callback<VideoIdModel>() {
                @Override
                public void onResponse(Call<VideoIdModel> call, Response<VideoIdModel> response) {
                    if (response.body() != null && response.isSuccessful()) {
                        ArrayList<DataItem> enveuVideoDetailsArrayList = new ArrayList<>();
                        String json = gson.toJson(response.body().getData());
                        List<VideoIdModel.Datum> list = response.body().getData();
                        String arrayData = gson.toJson(list);
                        ArrayList<DataItem> enveuVideoDetails = gson.fromJson(arrayData, new TypeToken<List<DataItem>>() {
                        }.getType());


                        for (ItemsItem item : continueWatchingBookmarkList) {
                            for (DataItem enveuVideoDetail : enveuVideoDetails) {
                                if (item.getAssetId() == enveuVideoDetail.getId()) {
                                    enveuVideoDetailsArrayList.add(enveuVideoDetail);
                                }
                            }
                        }
                        commonApiCallBack.onSuccess(enveuVideoDetailsArrayList);
                    } else {
                        commonApiCallBack.onFailure(new Throwable("Details Not Found"));

                    }
                }

                @Override
                public void onFailure(Call<VideoIdModel> call, Throwable t) {
                    commonApiCallBack.onFailure(new Throwable("Details Not Found"));

                }
            });

        }

    }

    public static String removeLastComma(String str) {
        if (str != null && str.length() > 0 && str.charAt(str.length() - 1) == ',') {
            return str.substring(0, str.length() - 1);
        }
        return str;
    }


    private List<RailCommonData> mModel;

    public LiveData<List<RailCommonData>> getSearchData(String keyword, int size, int page,boolean isV2Search,boolean isMusicApp) {
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();

        MutableLiveData<List<RailCommonData>> responsePopular = new MutableLiveData<>();
        try {
            String searchableKeys = null;
            List<String> searchKeyList=AppConfigMethod.getSearchableKeys();
            searchableKeys = searchKeyList.toString();
            String formattedKeys = searchableKeys.substring(1, searchableKeys.length() - 1);
            Log.d("searchableKeys", "getSearchData: " + searchableKeys);
            Log.d("searchableKeys", "getSearchData: " + formattedKeys);
            ApiInterface endpoint = RequestConfig.getClientSearch("").create(ApiInterface.class);
            String contentTypes = AppConstants.VIDEO + "," + AppConstants.CUSTOM + "," + AppConstants.LIVE + "," + AppConstants.PERSON + "," + AppConstants.AUDIO;

            String customType="";
            if (isV2Search){
                contentTypes=AppConstants.VIDEO+","+AppConstants.CUSTOM;
                customType="ENT_SERIES,ENT_SEASON";

                mModel = new ArrayList<>();
                try {
                    if (endpoint != null) {
                        endpoint.getSearchByFiltersOnV2(keyword, contentTypes, size, page, languageCode, customType, "", "", "", formattedKeys, true)
                                .enqueue(new Callback<ResponseSearch>() {
                                    @Override
                                    public void onResponse(@NonNull Call<ResponseSearch> call1, @NonNull Response<ResponseSearch> response) {
                                        if (response != null && response.isSuccessful()) {
                                            if (response.code() == 200) {
                                                RailCommonData railCommonData = new RailCommonData();
                                                assert response.body() != null;
                                                final Data data = response.body().getData();
                                                if (data != null && data.getItems() != null) {
                                                    railCommonData.setStatus(true);
                                                    List<com.enveu.beanModelV3.searchV2.ItemsItem> searchItems = data.getItems();
                                                    List<EnveuVideoItemBean> enveuVideoItemBeans = new ArrayList<>();
                                                    for (com.enveu.beanModelV3.searchV2.ItemsItem videoItem : searchItems) {
                                                        EnveuVideoItemBean enveuVideoItemBean = new EnveuVideoItemBean(videoItem,isMusicApp);
                                                        enveuVideoItemBeans.add(enveuVideoItemBean);
                                                    }
                                                    railCommonData.setEnveuVideoItemBeans(enveuVideoItemBeans);
                                                    railCommonData.setPageTotal(data.getPageInfo().getTotal());
                                                    railCommonData.setStatus(true);
                                                } else {
                                                    railCommonData.setStatus(false);
                                                }
                                                mModel.add(railCommonData);

                                            } else {
                                                responsePopular.postValue(mModel);
                                            }
                                        }else {
                                        }
                                        responsePopular.postValue(mModel);
                                    }

                                    @Override
                                    public void onFailure(Call<ResponseSearch> call, Throwable t) {
                                        RailCommonData railCommonData = new RailCommonData();
                                        railCommonData.setStatus(false);
                                        mModel.add(railCommonData);
                                        responsePopular.postValue(mModel);
                                    }
                                });
                    }
                } catch (Exception e) {
                    RailCommonData railCommonData = new RailCommonData();
                    railCommonData.setStatus(false);
                    mModel.add(railCommonData);
                    responsePopular.postValue(mModel);
                }
            }else {
                if (searchApiParams == null){
                    searchApiParams=AppConfigMethod.getSearchApiParameters(searchKeyList);
                }
                if (searchApiParams.size()>2) {
                    Observable<ResponseSearch> apiCall1 = null;
                    Observable<ResponseSearch> apiCall2 = null;
                    Observable<ResponseSearch> apiCall3 = null;

                    apiCall1 = endpoint.getSearchByFilters(keyword,
                                    searchApiParams.get(0).getContentType(), size, page, languageCode, searchApiParams.get(0).getCustomType(), searchApiParams.get(0).getAudioType(), searchApiParams.get(0).getLiveType(), searchApiParams.get(0).getPersonType(), searchApiParams.get(0).getVideoType(), false, getGenresItems())
                            .subscribeOn(Schedulers.io())
                            .observeOn(Schedulers.io());

                    apiCall2 = endpoint.getSearchByFilters(keyword,
                                    searchApiParams.get(1).getContentType(), size, page, languageCode, searchApiParams.get(1).getCustomType(), searchApiParams.get(1).getAudioType(), searchApiParams.get(1).getLiveType(), searchApiParams.get(1).getPersonType(), searchApiParams.get(1).getVideoType(), false, getGenresItems())
                            .subscribeOn(Schedulers.io())
                            .observeOn(Schedulers.io());

                    apiCall3 = endpoint.getSearchByFilters(keyword,
                                    searchApiParams.get(2).getContentType(), size, page, languageCode, searchApiParams.get(2).getCustomType(), searchApiParams.get(2).getAudioType(), searchApiParams.get(2).getLiveType(), searchApiParams.get(2).getPersonType(), searchApiParams.get(2).getVideoType(), false, getGenresItems())
                            .subscribeOn(Schedulers.io())
                            .observeOn(Schedulers.io());

                    Observable<List<ResponseSearch>> combined = Observable.zip(apiCall1, apiCall2, apiCall3, (data1, data2, data3) -> {
                        List<ResponseSearch> combinedList = new ArrayList<>();
                        combinedList.add(data1);
                        combinedList.add(data2);
                        combinedList.add(data3);
                        return combinedList;
                    }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
                    combined.subscribe(new Observer<List<ResponseSearch>>() {
                        @Override
                        public void onSubscribe(Disposable d) {
                            Logger.d("on subscribe");
                        }
                        @Override
                        public void onNext(@NonNull List<ResponseSearch> responseSearchList) {
                            Logger.d("response search: " + responseSearchList);
                            mModel=setResponseSearchList(responseSearchList,isMusicApp);

                            responsePopular.postValue(mModel);
                        }
                        @Override
                        public void onError(@NonNull Throwable e) {
                            Logger.w(e);
                            responsePopular.postValue(new ArrayList<>());
                        }
                        @Override
                        public void onComplete() {
                            Logger.d("completed");
                        }
                    });
                }
            }
        } catch (Exception e) {
            mModel = new ArrayList<>();
            RailCommonData railCommonData = new RailCommonData();
            railCommonData.setStatus(false);
            mModel.add(railCommonData);
            responsePopular.postValue(mModel);
        }
        return responsePopular;
    }
    public LiveData<List<RailCommonData>> getSearchData1(List<String> searchKeyList, String keyword, int size, int page, boolean isMusicApp) {
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();

        MutableLiveData<List<RailCommonData>> responsePopular = new MutableLiveData<>();
        try {
            ApiInterface endpoint = RequestConfig.getClientSearch("").create(ApiInterface.class);
                if (searchApiParams == null){
                    searchApiParams=AppConfigMethod.getSearchApiParameters(searchKeyList);
                }
                if (!searchApiParams.isEmpty()) {
                    Observable<ResponseSearch> apiCall1 = null;

                    apiCall1 = endpoint.getSearchByFilters(keyword,
                                    searchApiParams.get(0).getContentType(), size, page, languageCode, searchApiParams.get(0).getCustomType(), searchApiParams.get(0).getAudioType(), searchApiParams.get(0).getLiveType(), searchApiParams.get(0).getPersonType(), searchApiParams.get(0).getVideoType(), false, getGenresItems())
                            .subscribeOn(Schedulers.io())
                            .observeOn(Schedulers.io());
                    Observable<List<ResponseSearch>> combined = apiCall1
                            .map(commons -> {
                                List<ResponseSearch> combinedList = new ArrayList<>();
                                combinedList.add(commons);
                                return combinedList;
                            })
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread());
                    combined.subscribe(new Observer<List<ResponseSearch>>() {
                        @Override
                        public void onSubscribe(Disposable d) {
                            Logger.d("on subscribe");
                        }
                        @Override
                        public void onNext(@NonNull List<ResponseSearch> responseSearchList) {
                            Logger.d("response search: " + responseSearchList);
                            mModel=setResponseSearchList(responseSearchList,isMusicApp);

                            responsePopular.postValue(mModel);
                        }
                        @Override
                        public void onError(@NonNull Throwable e) {
                            Logger.w(e);
                            responsePopular.postValue(new ArrayList<>());
                        }
                        @Override
                        public void onComplete() {
                            Logger.d("completed");
                        }
                    });
                }
        } catch (Exception e) {
            mModel = new ArrayList<>();
            RailCommonData railCommonData = new RailCommonData();
            railCommonData.setStatus(false);
            mModel.add(railCommonData);
            responsePopular.postValue(mModel);
        }
        return responsePopular;
    }
    public LiveData<List<RailCommonData>> getSearchData2(List<String> searchKeyList, String keyword, int size, int page, boolean isMusicApp) {
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();

        MutableLiveData<List<RailCommonData>> responsePopular = new MutableLiveData<>();
        try {
            ApiInterface endpoint = RequestConfig.getClientSearch("").create(ApiInterface.class);
            if (searchApiParams == null){
                searchApiParams=AppConfigMethod.getSearchApiParameters(searchKeyList);
            }
            if (searchApiParams.size()>1) {
                Observable<ResponseSearch> apiCall1 = null;
                Observable<ResponseSearch> apiCall2 = null;

                apiCall1 = endpoint.getSearchByFilters(keyword,
                                searchApiParams.get(0).getContentType(), size, page, languageCode, searchApiParams.get(0).getCustomType(), searchApiParams.get(0).getAudioType(), searchApiParams.get(0).getLiveType(), searchApiParams.get(0).getPersonType(), searchApiParams.get(0).getVideoType(), false, getGenresItems())
                        .subscribeOn(Schedulers.io())
                        .observeOn(Schedulers.io());

                apiCall2 = endpoint.getSearchByFilters(keyword,
                                searchApiParams.get(1).getContentType(), size, page, languageCode, searchApiParams.get(1).getCustomType(), searchApiParams.get(1).getAudioType(), searchApiParams.get(1).getLiveType(), searchApiParams.get(1).getPersonType(), searchApiParams.get(1).getVideoType(), false, getGenresItems())
                        .subscribeOn(Schedulers.io())
                        .observeOn(Schedulers.io());

                Observable<List<ResponseSearch>> combined = Observable.zip(apiCall1, apiCall2, (data1, data2) -> {
                    List<ResponseSearch> combinedList = new ArrayList<>();
                    combinedList.add(data1);
                    combinedList.add(data2);
                    return combinedList;
                }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
                combined.subscribe(new Observer<List<ResponseSearch>>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        Logger.d("on subscribe");
                    }
                    @Override
                    public void onNext(@NonNull List<ResponseSearch> responseSearchList) {
                        Logger.d("response search: " + responseSearchList);
                        mModel=setResponseSearchList(responseSearchList,isMusicApp);

                        responsePopular.postValue(mModel);
                    }
                    @Override
                    public void onError(@NonNull Throwable e) {
                        Logger.w(e);
                        responsePopular.postValue(new ArrayList<>());
                    }
                    @Override
                    public void onComplete() {
                        Logger.d("completed");
                    }
                });
            }
        } catch (Exception e) {
            mModel = new ArrayList<>();
            RailCommonData railCommonData = new RailCommonData();
            railCommonData.setStatus(false);
            mModel.add(railCommonData);
            responsePopular.postValue(mModel);
        }
        return responsePopular;
    }
    public LiveData<List<RailCommonData>> getSearchData4(List<String> searchKeyList, String keyword, int size, int page, boolean isMusicApp) {
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();

        MutableLiveData<List<RailCommonData>> responsePopular = new MutableLiveData<>();
        try {
            ApiInterface endpoint = RequestConfig.getClientSearch("").create(ApiInterface.class);
            if (searchApiParams == null){
                searchApiParams=AppConfigMethod.getSearchApiParameters(searchKeyList);
            }
            if (searchApiParams.size()>3) {
                Observable<ResponseSearch> apiCall1 = null;
                Observable<ResponseSearch> apiCall2 = null;
                Observable<ResponseSearch> apiCall3 = null;
                Observable<ResponseSearch> apiCall4 = null;

                apiCall1 = endpoint.getSearchByFilters(keyword,
                                searchApiParams.get(0).getContentType(), size, page, languageCode, searchApiParams.get(0).getCustomType(), searchApiParams.get(0).getAudioType(), searchApiParams.get(0).getLiveType(), searchApiParams.get(0).getPersonType(), searchApiParams.get(0).getVideoType(), false, getGenresItems())
                        .subscribeOn(Schedulers.io())
                        .observeOn(Schedulers.io());

                apiCall2 = endpoint.getSearchByFilters(keyword,
                                searchApiParams.get(1).getContentType(), size, page, languageCode, searchApiParams.get(1).getCustomType(), searchApiParams.get(1).getAudioType(), searchApiParams.get(1).getLiveType(), searchApiParams.get(1).getPersonType(), searchApiParams.get(1).getVideoType(), false, getGenresItems())
                        .subscribeOn(Schedulers.io())
                        .observeOn(Schedulers.io());

                apiCall3 = endpoint.getSearchByFilters(keyword,
                                searchApiParams.get(2).getContentType(), size, page, languageCode, searchApiParams.get(2).getCustomType(), searchApiParams.get(2).getAudioType(), searchApiParams.get(2).getLiveType(), searchApiParams.get(2).getPersonType(), searchApiParams.get(2).getVideoType(), false, getGenresItems())
                        .subscribeOn(Schedulers.io())
                        .observeOn(Schedulers.io());

                apiCall4 = endpoint.getSearchByFilters(keyword,
                                searchApiParams.get(3).getContentType(), size, page, languageCode, searchApiParams.get(3).getCustomType(), searchApiParams.get(3).getAudioType(), searchApiParams.get(3).getLiveType(), searchApiParams.get(3).getPersonType(), searchApiParams.get(3).getVideoType(), false, getGenresItems())
                        .subscribeOn(Schedulers.io())
                        .observeOn(Schedulers.io());

                Observable<List<ResponseSearch>> combined = Observable.zip(apiCall1, apiCall2, apiCall3, apiCall4, (data1, data2, data3, data4) -> {
                    List<ResponseSearch> combinedList = new ArrayList<>();
                    combinedList.add(data1);
                    combinedList.add(data2);
                    combinedList.add(data3);
                    combinedList.add(data4);
                    return combinedList;
                }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
                combined.subscribe(new Observer<List<ResponseSearch>>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        Logger.d("on subscribe");
                    }
                    @Override
                    public void onNext(@NonNull List<ResponseSearch> responseSearchList) {
                        Logger.d("response search: " + responseSearchList);
                        mModel=setResponseSearchList(responseSearchList,isMusicApp);

                        responsePopular.postValue(mModel);
                    }
                    @Override
                    public void onError(@NonNull Throwable e) {
                        Logger.w(e);
                        responsePopular.postValue(new ArrayList<>());
                    }
                    @Override
                    public void onComplete() {
                        Logger.d("completed");
                    }
                });
            }
        } catch (Exception e) {
            mModel = new ArrayList<>();
            RailCommonData railCommonData = new RailCommonData();
            railCommonData.setStatus(false);
            mModel.add(railCommonData);
            responsePopular.postValue(mModel);
        }
        return responsePopular;
    }
    private List<RailCommonData> setResponseSearchList(List<ResponseSearch> responseSearchList, boolean isMusicApp) {
        List<RailCommonData> mModel = new ArrayList<>();
        try {
            final int dataSize = ObjectHelper.getSize(responseSearchList);
            for (int i = 0; i < dataSize; i++) {
                RailCommonData railCommonData = new RailCommonData();
                final Data data = responseSearchList.get(i).getData();
                if (data != null && data.getItems() != null) {
                    railCommonData.setStatus(true);
                    List<com.enveu.beanModelV3.searchV2.ItemsItem> searchItems = data.getItems();
                    List<EnveuVideoItemBean> enveuVideoItemBeans = new ArrayList<>();
                    for (com.enveu.beanModelV3.searchV2.ItemsItem videoItem : searchItems) {
                        EnveuVideoItemBean enveuVideoItemBean = new EnveuVideoItemBean(videoItem, isMusicApp);
                        Gson gson = new Gson();
                        String tmp = gson.toJson(enveuVideoItemBean);
                        enveuVideoItemBeans.add(enveuVideoItemBean);
                    }
                    railCommonData.setEnveuVideoItemBeans(enveuVideoItemBeans);
                    railCommonData.setPageTotal(data.getPageInfo().getTotal());
                    railCommonData.setStatus(true);
                } else {
                    railCommonData.setStatus(false);
                }
                mModel.add(railCommonData);
            }
        } catch (Exception e) {
            RailCommonData railCommonData = new RailCommonData();
            railCommonData.setStatus(false);
            mModel.add(railCommonData);
        }
        return mModel;
    }

    public LiveData<List<RailCommonData>> getSearchDataForSingleAsset(List<String> searchKeyList, String keyword, int size, int page, String assetType) {
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        MutableLiveData<List<RailCommonData>> responsePopular = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getClientSearch("").create(ApiInterface.class);
        try {
            if (searchApiParams == null){
                searchApiParams=AppConfigMethod.getSearchApiParameters(searchKeyList);
            }
            SearchApiParams searchApiParam = null;
            for (SearchApiParams searchApiParam1:searchApiParams) {
                if (searchApiParam1.getMediaType().equalsIgnoreCase(assetType)){
                    searchApiParam=searchApiParam1;
                    break;
                }
            }
            Observable<ResponseSearch> common = null;

            if (searchApiParam !=null) {
                common = endpoint.getSearchByFilters(keyword,
                                searchApiParam.getContentType(), size, page, languageCode, searchApiParam.getCustomType(), searchApiParam.getAudioType(), searchApiParam.getLiveType(), searchApiParam.getPersonType(), searchApiParam.getVideoType(), false, getGenresItems())
                        .subscribeOn(Schedulers.io())
                        .observeOn(Schedulers.io());
            }

            if (common !=null) {
                Observable<List<ResponseSearch>> combined = common
                        .map(commons -> {
                            List<ResponseSearch> combinedList = new ArrayList<>();
                            combinedList.add(commons);
                            return combinedList;
                        })
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread());
                combined.subscribe(new Observer<List<ResponseSearch>>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        Logger.d("on subscribe");
                    }

                    @Override
                    public void onNext(@NonNull List<ResponseSearch> responseSearchList) {
                        Logger.d("response search: " + responseSearchList);
                        mModel = setResponseSearchList(responseSearchList,true);

                        responsePopular.postValue(mModel);
                    }
                    @Override
                    public void onError(@NonNull Throwable e) {
                        Logger.w(e);
                        responsePopular.postValue(new ArrayList<>());
                    }
                    @Override
                    public void onComplete() {
                        Logger.d("completed");
                    }
                });
            }else {
                mModel = new ArrayList<>();
                RailCommonData railCommonData = new RailCommonData();
                railCommonData.setStatus(false);
                mModel.add(railCommonData);
                responsePopular.postValue(mModel);
            }
        }catch (Exception e) {
            mModel = new ArrayList<>();
            RailCommonData railCommonData = new RailCommonData();
            railCommonData.setStatus(false);
            mModel.add(railCommonData);
            responsePopular.postValue(mModel);
        }
        return responsePopular;
    }

    public LiveData<RailCommonData> getSingleCategorySearch(String keyword, String type, int size, int page, boolean applyFilter, String customContentType, String videoType, String header) {
        MutableLiveData<RailCommonData> responsePopular;
        Call<ResponseSearch> call = null;

        responsePopular = new MutableLiveData<>();
        String StringVideoTypes = MediaTypeConstants.getInstance().getMovie() + "," + MediaTypeConstants.getInstance().getEpisode() + "," + MediaTypeConstants.getInstance().getDocumentaries();

        String contentTypes = AppConstants.VIDEO + "," + AppConstants.CUSTOM;

        {
            try {
                ApiInterface backendApi = RequestConfig.getClientSearch("").create(ApiInterface.class);
                if (type.equalsIgnoreCase(AppConstants.VIDEO) || type.equalsIgnoreCase(AppConstants.CUSTOM)) {
                    if (header.equalsIgnoreCase(AppConstants.SEARCH_RESULT) || header.equalsIgnoreCase(AppConstants.SPANISH_SEARCH_RESULT)) {
                        call = backendApi.getVideoSearchResults(keyword, contentTypes, size, page, languageCode, StringVideoTypes, MediaTypeConstants.getInstance().getSeries());
                    } /*else if (header.equalsIgnoreCase(AppConstants.episodes)){
                        call = backendApi.getVideoSearchResults(keyword, type, size, page, languageCode,MediaTypeConstants.getInstance().getEpisode());
                    }  else if (header.equalsIgnoreCase(AppConstants.Documentaries)){
                        call = backendApi.getVideoSearchResults(keyword, type, size, page, languageCode,MediaTypeConstants.getInstance().getDocumentaries());
                    }*/
                }


                if (call != null) {
                    call.enqueue(new Callback<ResponseSearch>() {
                        @Override
                        public void onResponse(@NonNull Call<ResponseSearch> call, @NonNull Response<ResponseSearch> data) {
                            if (data.isSuccessful()) {
                                if (data.code() == 200) {
                                    RailCommonData railCommonData = null;
                                    railCommonData = new RailCommonData();
                                    assert data.body() != null;
                                    if (data.body().getData() != null && data.body().getData().getItems() != null) {
                                        railCommonData.setStatus(true);
                                        List<com.enveu.beanModelV3.searchV2.ItemsItem> itemsItem = data.body().getData().getItems();
                                        enveuVideoItemBeans = new ArrayList<>();
                                        Gson gson = new Gson();
                                        String json = gson.toJson(itemsItem);
                                        for (com.enveu.beanModelV3.searchV2.ItemsItem videoItem : itemsItem) {
                                            EnveuVideoItemBean enveuVideoItemBean = new EnveuVideoItemBean(videoItem,false);
                                            if (type.equalsIgnoreCase(MediaTypeConstants.getInstance().getSeries()) && videoItem.getSeasons() != null)
                                                enveuVideoItemBean.setSeasonCount(videoItem.getSeasons().size());

                                            enveuVideoItemBeans.add(enveuVideoItemBean);
                                        }

                                        railCommonData.setEnveuVideoItemBeans(enveuVideoItemBeans);
                                        railCommonData.setPageTotal(data.body().getData().getPageInfo().getTotal());
                                        railCommonData.setStatus(true);
                                        responsePopular.postValue(railCommonData);
                                    } else {
                                        railCommonData.setStatus(false);
                                        responsePopular.postValue(railCommonData);
                                    }

                                } else {
                                    responsePopular.postValue(new RailCommonData());
                                }

                            }

                        }

                        @Override
                        public void onFailure(@NonNull Call<ResponseSearch> call, @NonNull Throwable t) {
                            responsePopular.postValue(new RailCommonData());
                        }
                    });
                }

            } catch (Exception e) {
                responsePopular.postValue(new RailCommonData());
            }


        }
        return responsePopular;
    }

    public LiveData<SearchHistory> getSearchHistory(String token) {
        MutableLiveData<SearchHistory> responsePopular = new MutableLiveData<>();
        ApiInterface searchHistoryApi = RequestConfig.getClientSearch(token).create(ApiInterface.class);

        searchHistoryApi.getSearchHistory().enqueue(new Callback<SearchHistory>() {
            @Override
            public void onResponse(@NonNull Call<SearchHistory> call, @NonNull Response<SearchHistory> response) {
                if (response.isSuccessful()) {
                    if (response.code() == 200) {
                        responsePopular.postValue(response.body());
                    } else {
                        responsePopular.postValue(null);
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<SearchHistory> call, @NonNull Throwable t) {
                responsePopular.postValue(null);
            }
        });
        return responsePopular;
    }

   public LiveData<RelatedRailsCommonData> getRelatedArtist(String genres) {
            MutableLiveData<RelatedRailsCommonData> responsePopular = new MutableLiveData<>();
            endpoint.getMediaContentList("PERSON","", "ARTISTS" ,"","",genres,"",0,20,"en-US").enqueue(new Callback<RelatedRailsCommonData>() {
                @Override
                public void onResponse(@NonNull Call<RelatedRailsCommonData> call, @NonNull Response<RelatedRailsCommonData> response) {
                    if (response.isSuccessful()) {
                        if (response.code() == 200) {
                            responsePopular.postValue(response.body());
                            } else {
                            responsePopular.postValue(null);
                        }
                    }
                }
                @Override
                public void onFailure(@NonNull Call<RelatedRailsCommonData> call, @NonNull Throwable t) {
                    responsePopular.postValue(null);
            }
            });
            return responsePopular;
    }

    public LiveData<RelatedRailsCommonData> getRelatedVideo(String customData) {
            MutableLiveData<RelatedRailsCommonData> responsePopular = new MutableLiveData<>();
            endpoint.getMediaContentList("VIDEO","MOVIES", "" ,"movies-artists-ids|OR:LIKE|"+ customData,"","","",0,20,"en-US").enqueue(new Callback<RelatedRailsCommonData>() {
                @Override
                public void onResponse(@NonNull Call<RelatedRailsCommonData> call, @NonNull Response<RelatedRailsCommonData> response) {
                    if (response.isSuccessful()) {
                        if (response.code() == 200) {
                            responsePopular.postValue(response.body());
                            } else {
                            responsePopular.postValue(null);
                        }
                    }
                }
                @Override
                public void onFailure(@NonNull Call<RelatedRailsCommonData> call, @NonNull Throwable t) {
                    responsePopular.postValue(null);
            }
            });
            return responsePopular;
    }

    public LiveData<RelatedRailsCommonData> getRelatedArticle(String customData) {
            MutableLiveData<RelatedRailsCommonData> responsePopular = new MutableLiveData<>();
            endpoint.getMediaContentList("ARTICLE","", "" ,"article-parent-id|OR:LIKE|"+customData,"","","ARTICLE",0,20,"en-US").enqueue(new Callback<RelatedRailsCommonData>() {
                @Override
                public void onResponse(@NonNull Call<RelatedRailsCommonData> call, @NonNull Response<RelatedRailsCommonData> response) {
                    if (response.isSuccessful()) {
                        if (response.code() == 200) {
                            responsePopular.postValue(response.body());
                            } else {
                            responsePopular.postValue(null);
                        }
                    }
                }
                @Override
                public void onFailure(@NonNull Call<RelatedRailsCommonData> call, @NonNull Throwable t) {
                    responsePopular.postValue(null);
            }
            });
            return responsePopular;
    }
    public LiveData<RelatedRailsCommonData> getRelatedAlbum(String customData) {
            MutableLiveData<RelatedRailsCommonData> responsePopular = new MutableLiveData<>();
            endpoint.getMediaContentList("CUSTOM","", "" ,customData,"ALBUMS","","",0,20,"en-US").enqueue(new Callback<RelatedRailsCommonData>() {
                @Override
                public void onResponse(@NonNull Call<RelatedRailsCommonData> call, @NonNull Response<RelatedRailsCommonData> response) {
                    if (response.isSuccessful()) {
                        if (response.code() == 200) {
                            responsePopular.postValue(response.body());
                            } else {
                            responsePopular.postValue(null);
                        }
                    }
                }
                @Override
                public void onFailure(@NonNull Call<RelatedRailsCommonData> call, @NonNull Throwable t) {
                    responsePopular.postValue(null);
            }
            });
            return responsePopular;
    }

    public LiveData<SearchHistory> deleteSearchHistory(String token , String local , String keyword , Boolean clearAll) {
            MutableLiveData<SearchHistory> responsePopular = new MutableLiveData<>();
            ApiInterface searchHistoryApi = RequestConfig.getClientSearch(token).create(ApiInterface.class);

            searchHistoryApi.deleteSearchHistory("en-US" ,keyword, clearAll).enqueue(new Callback<SearchHistory>() {
                @Override
                public void onResponse(@NonNull Call<SearchHistory> call, @NonNull Response<SearchHistory> response) {
                    if (response.isSuccessful()) {
                        if (response.code() == 200) {
                            responsePopular.postValue(response.body());
                            } else {
                            responsePopular.postValue(null);
                        }
                    }
                }
                @Override
                public void onFailure(@NonNull Call<SearchHistory> call, @NonNull Throwable t) {
                    responsePopular.postValue(null);
            }
            });
            return responsePopular;
    }

    public LiveData<SearchGenres> getSearchGenres( String contentType, String customType) {
        MutableLiveData<SearchGenres> responseGenres = new MutableLiveData<>();
        endpoint.getGenres(customType, contentType,"en-US").enqueue(new Callback<SearchGenres>() {
            @Override
            public void onResponse(Call<SearchGenres> call, Response<SearchGenres> response) {
                if (response.code() == 200 && response.body().getData() != null){
                    responseGenres.postValue(response.body());
                } else {
                    responseGenres.postValue(null);
                }
            }

            @Override
            public void onFailure(Call<SearchGenres> call, Throwable t) {
                responseGenres.postValue(null);
            }
        });
        return responseGenres;
    }

    public LiveData<UserTrackingResponse> getSponsorUserTracking(JsonObject jsonObject,String token) {
        MutableLiveData<UserTrackingResponse> mutableLiveData = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getSponsorUserTrackingClient(token).create(ApiInterface.class);
        if (endpoint!=null) {
            endpoint.getSponsorUserTracking(jsonObject).enqueue(new Callback<UserTrackingResponse>() {
                @Override
                public void onResponse(Call<UserTrackingResponse> call, Response<UserTrackingResponse> response) {
                    if (response.code() == 200 && response.body() != null && response.body().getData() != null) {
                        mutableLiveData.postValue(response.body());
                    } else {
                        mutableLiveData.postValue(null);
                    }
                }

                @Override
                public void onFailure(Call<UserTrackingResponse> call, Throwable t) {
                    mutableLiveData.postValue(null);
                }
            });
        }

        return mutableLiveData;
    }

    public LiveData<SearchRating> getSearchRating(String token, String contentType, String customType){
        MutableLiveData<SearchRating> responseGenres = new MutableLiveData<>();
        endpoint.getRating(customType, contentType).enqueue(new Callback<SearchRating>() {
            @Override
            public void onResponse(Call<SearchRating> call, Response<SearchRating> response) {
                if (response.code() == 200 && response.body().getData() != null){
                    responseGenres.postValue(response.body());
                } else {
                    responseGenres.postValue(null);
                }
            }

            @Override
            public void onFailure(Call<SearchRating> call, Throwable t) {
                responseGenres.postValue(null);
            }
        });
        return responseGenres;
    }

    public LiveData<RailCommonData> getProgramSearch(String keyword, int size, int page, boolean applyFilter) {
        MutableLiveData<RailCommonData> responsePopular;
        Call<ResponseSearch> call = null;
        responsePopular = new MutableLiveData<>();

        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        try {
            ApiInterface backendApi = RequestConfig.getClientSearch("").create(ApiInterface.class);

            Logger.d("SearchValues-->>" + keyword + " " + size + " " + page);

            List<String> contentTypes = Arrays.asList(MediaTypeConstants.getInstance().getMovie(),
                    MediaTypeConstants.getInstance().getSeries(),
                    MediaTypeConstants.getInstance().getLive(),
                    MediaTypeConstants.getInstance().getShow());

            if (applyFilter) {
                List<String> filterGenreSavedListKeyForApi = KsPreferenceKeys.getInstance().getDataGenreListKeyValue();
                List<String> filterSortSavedListKeyForApi = KsPreferenceKeys.getInstance().getDataSortListKeyValue();
                if (filterGenreSavedListKeyForApi != null && filterGenreSavedListKeyForApi.size() > 0 || filterSortSavedListKeyForApi != null && filterSortSavedListKeyForApi.size() > 0) {
                    call = backendApi.getSearchResultsByFilters(keyword, contentTypes, size, page, languageCode, filterGenreSavedListKeyForApi, filterSortSavedListKeyForApi);
                }
            } else {
                call = backendApi.getSearchResults(keyword, contentTypes, size, page, languageCode);
            }

            if (call != null) call.enqueue(new Callback<ResponseSearch>() {
                @Override
                public void onResponse(@NonNull Call<ResponseSearch> call, @NonNull Response<ResponseSearch> response) {
                    if (response.code() == 200) {
                        RailCommonData railCommonData = null;
                        final ResponseSearch body = response.body();
                        if (body != null) {
                            railCommonData = new RailCommonData();
                            final Data data = body.getData();
                            if (data != null && data.getItems() != null) {
                                railCommonData.setStatus(true);
                                List<com.enveu.beanModelV3.searchV2.ItemsItem> itemsItem =
                                        data.getItems();
                                enveuVideoItemBeans = new ArrayList<>();
                                for (com.enveu.beanModelV3.searchV2.ItemsItem videoItem : itemsItem) {
                                    EnveuVideoItemBean enveuVideoItemBean = new EnveuVideoItemBean(videoItem,false);
                                    enveuVideoItemBean.setPosterURL(ImageLayer.getInstance().getPosterImageUrl(videoItem));
                                    if (videoItem.getSeasons() != null)
                                        enveuVideoItemBean.setSeasonCount(
                                                videoItem.getSeasons().size());

                                    enveuVideoItemBeans.add(enveuVideoItemBean);
                                }

                                railCommonData.setEnveuVideoItemBeans(enveuVideoItemBeans);
                                railCommonData.setPageTotal(data.getPageInfo().getTotal());
                                railCommonData.setStatus(true);
                            } else {
                                railCommonData.setStatus(false);
                            }
                        }
                        responsePopular.postValue(railCommonData);
                    } else {
                        responsePopular.postValue(new RailCommonData());
                    }
                }

                @Override
                public void onFailure(@NonNull Call<ResponseSearch> call, @NonNull Throwable t) {
                    responsePopular.postValue(new RailCommonData());
                }
            });
            else Logger.e("Call not sent");

        } catch (Exception e) {
            responsePopular.postValue(new RailCommonData());
        }

        return responsePopular;
    }

    public void getRequestedOfferForUser(int page, int size, String token, int userId, String offers, String currentLanguageCode, RequestOfferCallBack requestOfferCallBack) {
        ApiInterface enveuSubscriptionEndPoint = RequestConfig.getRequestOfferClient(token).create(ApiInterface.class);
        if (enveuSubscriptionEndPoint != null) {
            enveuSubscriptionEndPoint.getRequestOfferForUser(userId, offers, page, size, currentLanguageCode).enqueue(new Callback<com.enveu.bean_model_v2_0.listAll.RequestOfferList.Response>() {
                @Override
                public void onResponse(Call<com.enveu.bean_model_v2_0.listAll.RequestOfferList.Response> call, Response<com.enveu.bean_model_v2_0.listAll.RequestOfferList.Response> response) {
                    if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                        requestOfferCallBack.success(true, response.body());
                    } else {
                        requestOfferCallBack.success(false, response.body());
                    }
                }

                @Override
                public void onFailure(Call<com.enveu.bean_model_v2_0.listAll.RequestOfferList.Response> call, Throwable t) {
                    requestOfferCallBack.failure(false, "0", "");
                }
            });
        }
    }


    public void getRelatedContent(int pageNumber, int size, String contentType, int id, ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();
        if (endpoint != null) {
            endpoint.getRelatedContentForVideo(id, languageCode, pageNumber, size, contentType).enqueue(new Callback<EnveuCommonResponse>() {
                @Override
                public void onResponse(@NonNull Call<EnveuCommonResponse> call, @NonNull Response<EnveuCommonResponse> response) {
                    Log.d("RelatedCall","at getRelatedContent, response success");
                    parseResponseAsRailCommonData(response, true);
                }

                @Override
                public void onFailure(@NonNull Call<EnveuCommonResponse> call, @NonNull Throwable t) {
                    Log.d("RelatedCall","at getRelatedContent, response fail");
                    ApiErrorModel errorModel = new ApiErrorModel(500, t.getMessage());
                    callBack.onFailure(errorModel);
                }
            });
        }
    }


    public LiveData<List<RailCommonData>> getGenreData(Context context, String contentType, String customType, int offSet, int size, String trackEvent, String locale) {
        MutableLiveData<List<RailCommonData>> genreResponseMutableLiveData = new MutableLiveData<>();

        ApiInterface endpoint = RequestConfig.getClientSearch("").create(ApiInterface.class);
        Call<GenreResponse> call = null;

        call = endpoint.getGenreResult(contentType, customType, offSet, size, trackEvent, locale);
        call.enqueue(new Callback<GenreResponse>() {
            @Override
            public void onResponse(Call<GenreResponse> call, Response<GenreResponse> response) {
//                if (response.isSuccessful() && response.body() != null) {
//                    genreResponseMutableLiveData.postValue(response.body());
//                }else {
//                    genreResponseMutableLiveData.postValue(response.body());
//                }
                if (response.isSuccessful()) {
                    Log.d("responseFromApiIs", new Gson().toJson(response.body()));
                    mModel = new ArrayList<>();
                    try {
                        RailCommonData railCommonData = new RailCommonData();
                        assert response.body() != null;
                        final Data data = response.body().getData();
                        if (data != null && data.getItems() != null) {
                            railCommonData.setStatus(true);
                            List<com.enveu.beanModelV3.searchV2.ItemsItem> searchItems = data.getItems();
                            List<EnveuVideoItemBean> enveuVideoItemBeans = new ArrayList<>();
                            for (com.enveu.beanModelV3.searchV2.ItemsItem videoItem : searchItems) {
                                EnveuVideoItemBean enveuVideoItemBean = new EnveuVideoItemBean(videoItem,true);
                                enveuVideoItemBeans.add(enveuVideoItemBean);
                            }
                            railCommonData.setEnveuVideoItemBeans(enveuVideoItemBeans);
                            railCommonData.setPageTotal(data.getPageInfo().getTotal());
                            railCommonData.setStatus(true);
                        } else {
                            railCommonData.setStatus(false);
                        }
                        mModel.add(railCommonData);
                    } catch (Exception e) {
                        RailCommonData railCommonData = new RailCommonData();
                        railCommonData.setStatus(false);
                        mModel.add(railCommonData);
                    }
                    genreResponseMutableLiveData.postValue(mModel);
                    Log.d("responseFromApiIs", "true");

                }
            }

            @Override
            public void onFailure(Call<GenreResponse> call, Throwable t) {
                genreResponseMutableLiveData.postValue(new ArrayList<>());

            }
        });

        return genreResponseMutableLiveData;
    }


    public LiveData<List<RailCommonData>> getGenreDataFilter(Context context, String contentType, String customType, String id, String keyword, int offSet, int size, String trackEvent,String personType, String locale) {
                MutableLiveData<List<RailCommonData>> genreResponseMutableLiveData = new MutableLiveData<>();
                ApiInterface endpoint = RequestConfig.getClientSearch("").create(ApiInterface.class);
                Call<GenreResponse> call = null;
                if (keyword != null && !keyword.equalsIgnoreCase("")) {

                    call = endpoint.getGenreResultFilter(contentType, id, keyword, offSet, size, trackEvent,personType, locale);
                    call.enqueue(new Callback<GenreResponse>() {
                        @Override
                        public void onResponse(Call<GenreResponse> call, Response<GenreResponse> response) {
                            if (response.isSuccessful()) {
                                Log.d("responseFromApiIs", new Gson().toJson(response.body()));
                                mModel = new ArrayList<>();
                                try {
                                    RailCommonData railCommonData = new RailCommonData();
                                    assert response.body() != null;
                                    final Data data = response.body().getData();
                                    if (data != null && data.getItems() != null) {
                                        railCommonData.setStatus(true);
                                        List<com.enveu.beanModelV3.searchV2.ItemsItem> searchItems = data.getItems();
                                        List<EnveuVideoItemBean> enveuVideoItemBeans = new ArrayList<>();
                                        for (com.enveu.beanModelV3.searchV2.ItemsItem videoItem : searchItems) {
                                            EnveuVideoItemBean enveuVideoItemBean = new EnveuVideoItemBean(videoItem, true);
                                            enveuVideoItemBeans.add(enveuVideoItemBean);
                                        }
                                        railCommonData.setEnveuVideoItemBeans(enveuVideoItemBeans);
                                        railCommonData.setPageTotal(data.getPageInfo().getTotal());
                                        railCommonData.setStatus(true);
                                    } else {
                                        railCommonData.setStatus(false);
                                    }
                                    mModel.add(railCommonData);
                                } catch (Exception e) {
                                    RailCommonData railCommonData = new RailCommonData();
                                    railCommonData.setStatus(false);
                                    mModel.add(railCommonData);
                                }
                                genreResponseMutableLiveData.postValue(mModel);
                                Log.d("responseFromApiIs", "true");

                            }
                        }

                        @Override
                        public void onFailure(Call<GenreResponse> call, Throwable t) {
                            genreResponseMutableLiveData.postValue(new ArrayList<>());

                        }
                    });
                }



                else {
                    call = endpoint.getGenreResultFilterWithoutKeyWord(contentType, id, offSet, size, trackEvent,personType, locale);
                    call.enqueue(new Callback<GenreResponse>() {
                        @Override
                        public void onResponse(Call<GenreResponse> call, Response<GenreResponse> response) {
                            if (response.isSuccessful()) {
                                Log.d("responseFromApiIs", new Gson().toJson(response.body()));
                                mModel = new ArrayList<>();
                                try {
                                    RailCommonData railCommonData = new RailCommonData();
                                    assert response.body() != null;
                                    final Data data = response.body().getData();
                                    if (data != null && data.getItems() != null) {
                                        railCommonData.setStatus(true);
                                        List<com.enveu.beanModelV3.searchV2.ItemsItem> searchItems = data.getItems();
                                        List<EnveuVideoItemBean> enveuVideoItemBeans = new ArrayList<>();
                                        for (com.enveu.beanModelV3.searchV2.ItemsItem videoItem : searchItems) {
                                            EnveuVideoItemBean enveuVideoItemBean = new EnveuVideoItemBean(videoItem, true);
                                            enveuVideoItemBeans.add(enveuVideoItemBean);
                                        }
                                        railCommonData.setEnveuVideoItemBeans(enveuVideoItemBeans);
                                        railCommonData.setPageTotal(data.getPageInfo().getTotal());
                                        railCommonData.setStatus(true);
                                    } else {
                                        railCommonData.setStatus(false);
                                    }
                                    mModel.add(railCommonData);
                                } catch (Exception e) {
                                    RailCommonData railCommonData = new RailCommonData();
                                    railCommonData.setStatus(false);
                                    mModel.add(railCommonData);
                                }
                                genreResponseMutableLiveData.postValue(mModel);
                                Log.d("responseFromApiIs", "true");

                            }
                        }

                        @Override
                        public void onFailure(Call<GenreResponse> call, Throwable t) {
                            genreResponseMutableLiveData.postValue(new ArrayList<>());

                        }
                    });
                }


                return genreResponseMutableLiveData;
            }


            public LiveData<List<RailCommonData>> getAllArtist(String keyword, int size, int page) {
                languageCode = LanguageLayer.INSTANCE.getCurrentLanguageCode();

                MutableLiveData<List<RailCommonData>> artistList = new MutableLiveData<>();

                ApiInterface endpoint = RequestConfig.getClientSearch("").create(ApiInterface.class);
                Call<ResponseSearch> call = null;
//        if (keyword.equalsIgnoreCase("") || keyword.equalsIgnoreCase(null)) {
//            call = endpoint.getAllArtistWithoutKeyword("PERSON", size, page,"en-US","ARTISTS",false);
//
//        }else {
                call = endpoint.getAllArtist(keyword, "PERSON", size, page, "en-US", "ARTISTS", false);

                // }
                call.enqueue(new Callback<ResponseSearch>() {
                    @Override
                    public void onResponse(Call<ResponseSearch> call, Response<ResponseSearch> response) {
                        if (response.isSuccessful()) {
                            Log.d("responseFromApiIs", new Gson().toJson(response.body()));
                            mModel = new ArrayList<>();
                            try {
                                RailCommonData railCommonData = new RailCommonData();
                                assert response.body() != null;
                                final Data data = response.body().getData();
                                if (data != null && data.getItems() != null) {
                                    railCommonData.setStatus(true);
                                    List<com.enveu.beanModelV3.searchV2.ItemsItem> searchItems = data.getItems();
                                    List<EnveuVideoItemBean> enveuVideoItemBeans = new ArrayList<>();
                                    for (com.enveu.beanModelV3.searchV2.ItemsItem videoItem : searchItems) {
                                        EnveuVideoItemBean enveuVideoItemBean = new EnveuVideoItemBean(videoItem, true);
                                        enveuVideoItemBeans.add(enveuVideoItemBean);
                                    }
                                    railCommonData.setEnveuVideoItemBeans(enveuVideoItemBeans);
                                    railCommonData.setPageTotal(data.getPageInfo().getTotal());
                                    railCommonData.setStatus(true);
                                } else {
                                    railCommonData.setStatus(false);
                                }
                                mModel.add(railCommonData);
                            } catch (Exception e) {
                                RailCommonData railCommonData = new RailCommonData();
                                railCommonData.setStatus(false);
                                mModel.add(railCommonData);
                            }
                            artistList.postValue(mModel);
                            Log.d("responseFromApiIs", "true");

                        }
                    }

                    @Override
                    public void onFailure(Call<ResponseSearch> call, Throwable t) {
                        artistList.postValue(new ArrayList<>());
                    }
                });
                return artistList;
            }


            public void hitApiEntitlement(String token, String sku, com.enveu.activities.homeactivity.ApiInterface callback) {
                APIDetails endpoint = RequestConfig.getUserInteration(token).create(APIDetails.class);
                Call<ResponseEntitle> call = endpoint.checkEntitlement(sku);
                call.enqueue(new Callback<ResponseEntitle>() {
                    @Override
                    public void onResponse(Call<ResponseEntitle> call, Response<ResponseEntitle> response) {
                        if (response.isSuccessful()) {
                            ResponseEntitle responseEntitlement = new ResponseEntitle();
                            responseEntitlement.setResponseCode(response.code());
                            responseEntitlement.setStatus(true);
                            responseEntitlement.setData(response.body().getData());
                            callback.onSuccess(responseEntitlement);
                        } else {
                            // Assuming you have a method in ApiInterface to handle failure
                            callback.onFailure();
                        }
                    }

                    @Override
                    public void onFailure(Call<ResponseEntitle> call, Throwable t) {
                        callback.onFailure();
                    }
                });
            }

            public void getPlayableID(String accessToken, String sku, PlayableCallBack callBack) {
                APIDetails endpoint = RequestConfig.getEnveuClient().create(APIDetails.class);
                Call<DRM> call = endpoint.getSKUForPLayer(accessToken, sku);
                call.enqueue(new Callback<DRM>() {
                    @Override
                    public void onResponse(Call<DRM> call, Response<DRM> response) {
                        if (response.isSuccessful()) {
                            if (response.body() != null && response.body().getResponseCode() == 2000) {
                                callBack.onSuccess(response.body());
                            }
                        } else {
                            callBack.onFailure();
                        }
                    }

                    @Override
                    public void onFailure(Call<DRM> call, Throwable t) {
                        callBack.onFailure();
                    }
                });
            }

            public MutableLiveData<AddToQueueResponse> addToQueueApi(AddToQueueRequestModel addToQueueRequestModel) {
                MutableLiveData<AddToQueueResponse> addQueueResponse = new MutableLiveData<>();
                ApiInterface endpoint = RequestConfig.getMonetizationBaseURL(KsPreferenceKeys.getInstance().getAppPrefAccessToken()).create(ApiInterface.class);
                Call<AddToQueueResponse> call = endpoint.addToQueueApi(addToQueueRequestModel);
                call.enqueue(new Callback<AddToQueueResponse>() {
                    @Override
                    public void onResponse(@NonNull Call<AddToQueueResponse> call, @NonNull Response<AddToQueueResponse> response) {
                        if (response.isSuccessful()) {
                            if (response.body() != null && response.body().getResponseCode() == 2000) {
                                addQueueResponse.postValue(response.body());
                            }
                        } else {
                            addQueueResponse.postValue(null);
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<AddToQueueResponse> call, @NonNull Throwable t) {
                        addQueueResponse.postValue(null);
                    }
                });
                return addQueueResponse;
            }

            public MutableLiveData<JsonObject> callToCheckContentFollowLikeByUser(String contentIds){
                Map<String, String> queryParams = new HashMap<>();
                MutableLiveData<JsonObject> jsonObjectMutableLiveData = new MutableLiveData<>();
                ApiInterface endpoint = RequestConfig.getMonetizationBaseURL(KsPreferenceKeys.getInstance().getAppPrefAccessToken()).create(ApiInterface.class);
                queryParams.put("mediaContentId", contentIds);
                Call<JsonObject> call = endpoint.checkFollowAndLike(queryParams);
                call.enqueue(new Callback<JsonObject>() {
                    @Override
                    public void onResponse(@NonNull Call<JsonObject> call, @NonNull Response<JsonObject> response) {
                        if (response.isSuccessful()){
                            if (response.body() != null) {
                                jsonObjectMutableLiveData.postValue(response.body());
                            }
                        }else {
                            jsonObjectMutableLiveData.postValue(null);
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<JsonObject> call, @NonNull Throwable t) {
                        jsonObjectMutableLiveData.postValue(null);
                    }
                });
                return jsonObjectMutableLiveData;
            }


            public void getChromeCastUrl(String baseUrl, String acceptKey, NetworkResultCallback<ChromeCastUrlResponse> networkResultCallback){
                ApiInterface endpoint = RequestConfig.getClientHeader().create(ApiInterface.class);
                Call<ChromeCastUrlResponse> call = endpoint.getChromeCastUrl(baseUrl, acceptKey);
                call.enqueue(new Callback<ChromeCastUrlResponse>() {
                    @Override
                    public void onResponse(@NonNull Call<ChromeCastUrlResponse> call, @NonNull Response<ChromeCastUrlResponse> response) {
                        if (response.isSuccessful()){
                            if (response.body() != null) {
                                networkResultCallback.success(true, response);
                            }
                        }else {
                            networkResultCallback.failure(false, response.code(), "");
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ChromeCastUrlResponse> call, @NonNull Throwable t) {
                        networkResultCallback.failure(false, -1, "");
                    }
                });
            }

            public void callFavoriteListApi(String token,int page,int size, NetworkResultCallback callback) {
                Map<String, String> queryParams = new HashMap<>();
                ApiInterface endpoint = RequestConfig.getMonetizationBaseURL(token).create(ApiInterface.class);
                queryParams.put("type","FAVORITE");
                queryParams.put("page", String.valueOf(page));
                queryParams.put("size",String.valueOf(size));
                queryParams.put("targetingTagConjunction","OR");
                Call<JsonObject> call = endpoint.getUserPlaylistData(queryParams);
                call.enqueue(new Callback<JsonObject>() {
                    @Override
                    public void onResponse(Call<JsonObject> call, Response<JsonObject> response) {
                        if (response.isSuccessful()){
                            if (response.body() != null) {
                                callback.success(true, response);
                            }
                        }else {
                            callback.failure(false, response.code(),response.message());
                        }
                    }

                    @Override
                    public void onFailure(Call<JsonObject> call, Throwable t) {
                        callback.failure(false, 450,"API Failure");
                    }
                });
            }
            public void callFavoriteListApiBySlug(String token, String playListSlug, NetworkResultCallback callback){
                Map<String, String> queryParams = new HashMap<>();
                ApiInterface endpoint = RequestConfig.getMonetizationBaseURL(token).create(ApiInterface.class);
                queryParams.put("playlistSlug",playListSlug);
                Call<JsonObject> call = endpoint.getUserPlaylistDataBySlug(queryParams);
                call.enqueue(new Callback<JsonObject>() {
                    @Override
                    public void onResponse(Call<JsonObject> call, Response<JsonObject> response) {
                        if (response.isSuccessful()){
                            if (response.body() != null) {
                                callback.success(true, response);
                            }
                        }else {
                            callback.failure(false, response.code(),response.message());
                        }
                    }

                    @Override
                    public void onFailure(Call<JsonObject> call, Throwable t) {
                        callback.failure(false, 450,"API Failure");
                    }
                });
            }
            public MutableLiveData<GetQueueResponse> allGetQueueListApi(String type) {
                MutableLiveData<GetQueueResponse> allQueueListResponse = new MutableLiveData<>();
                ApiInterface endpoint = RequestConfig.getMonetizationBaseURL(KsPreferenceKeys.getInstance().getAppPrefAccessToken()).create(ApiInterface.class);
                Call<GetQueueResponse> call = endpoint.allGetQueueListApi(type);
                call.enqueue(new Callback<GetQueueResponse>() {
                    @Override
                    public void onResponse(@NonNull Call<GetQueueResponse> call, @NonNull Response<GetQueueResponse> response) {
                        if (response.isSuccessful()) {
                            if (response.body() != null && response.body().getResponseCode() == 2000) {
                                allQueueListResponse.postValue(response.body());
                            }
                        } else {
                            allQueueListResponse.postValue(null);
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<GetQueueResponse> call, @NonNull Throwable t) {
                        allQueueListResponse.postValue(null);
                    }
                });
                return allQueueListResponse;
            }

    public MutableLiveData<JsonObject> getPlayListDataByIds(String playListIds,int pageNumber, int pageSize){
        Map<String, String> queryParams = new HashMap<>();
        MutableLiveData<JsonObject> responseToSendBack = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getEnveuClient().create(ApiInterface.class);

        queryParams.put("playlistId",playListIds);
        queryParams.put("page", String.valueOf(pageNumber));
        queryParams.put("size", String.valueOf(pageSize));

        Call<JsonObject> call = endpoint.getPlayListByIds(queryParams);
        call.enqueue(new Callback<>() {
            @Override
            public void onResponse(Call<JsonObject> call, Response<JsonObject> response) {
                if (response.body() != null && response.isSuccessful()){
                    responseToSendBack.postValue(response.body());
                    Log.d("checkRailsPlaylistId", playListIds);

                }else {
                    responseToSendBack.postValue(null);
                }
            }

            @Override
            public void onFailure(Call<JsonObject> call, Throwable t) {
                responseToSendBack.postValue(null);
            }
        });
        return responseToSendBack;
    }


    public MutableLiveData<ArtistListResponse> getAssetDetailsV3Api(String assetId, int pageNumber, int pageSize) {
                MutableLiveData<ArtistListResponse> artistListResponse = new MutableLiveData<>();
                ApiInterface endpoint = RequestConfig.getEnveuClient().create(ApiInterface.class);
                Call<ArtistListResponse> call = endpoint.getMediaContentV3("AUDIO", "SONGS", "songs-artist-ids|OR:LIKE|" +assetId, pageNumber, pageSize, "PLAY_COUNT", "DESC",  LanguageLayer.INSTANCE.getCurrentLanguageCode());
                call.enqueue(new Callback<ArtistListResponse>() {
                    @Override
                    public void onResponse(@NonNull Call<ArtistListResponse> call, @NonNull Response<ArtistListResponse> response) {
                        if (response.isSuccessful()) {
                            if (response.body() != null && response.body().getResponseCode() == 2000) {
                                artistListResponse.postValue(response.body());
                            }
                        } else {
                            artistListResponse.postValue(null);
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ArtistListResponse> call, @NonNull Throwable t) {
                        Log.d("ApiFailure",t.getMessage());
                        artistListResponse.postValue(null);
                    }
                });
                return artistListResponse;
            }

            public MutableLiveData<ArtistListResponse> getAssetDetailsV3Api(String assetId, int pageNumber, int pageSize, String genresId) {
                MutableLiveData<ArtistListResponse> artistListResponse = new MutableLiveData<>();
                ApiInterface endpoint = RequestConfig.getEnveuClient().create(ApiInterface.class);
                Call<ArtistListResponse> call = endpoint.getMediaContentV3("AUDIO", "SONGS",  assetId, pageNumber, pageSize, genresId, "PLAY_COUNT", "DESC",  LanguageLayer.INSTANCE.getCurrentLanguageCode());
                call.enqueue(new Callback<ArtistListResponse>() {
                    @Override
                    public void onResponse(@NonNull Call<ArtistListResponse> call, @NonNull Response<ArtistListResponse> response) {
                        if (response.isSuccessful()) {
                            if (response.body() != null && response.body().getResponseCode() == 2000) {
                                artistListResponse.postValue(response.body());
                            }
                        } else {
                            artistListResponse.postValue(null);
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<ArtistListResponse> call, @NonNull Throwable t) {
                        artistListResponse.postValue(null);
                    }
                });
                return artistListResponse;
            }

            public void callApiForUploadCreate(JsonObject requestBody, NetworkResultCallback callback){
                String token = KsPreferenceKeys.getInstance().getAppPrefAccessToken();
                ApiInterface endpoint = RequestConfig.getEnveuClient().create(ApiInterface.class);
                Call<JsonObject> call = endpoint.callApiForUploadCreate(token,requestBody);
                call.enqueue(new Callback<JsonObject>() {
                    @Override
                    public void onResponse(Call<JsonObject> call, Response<JsonObject> response) {
                        if (response.isSuccessful()) {
                            if (response.body() != null) {
                                callback.success(true, response);
                            }
                        }
                    }
                    @Override
                    public void onFailure(Call<JsonObject> call, Throwable t) {
                        callback.failure(false, 404, ""+t.getMessage());
                    }
                });
            }

        public MutableLiveData<ArtistListResponse> getListForRail(String genres,int pageNumber, int pageSize) {
            MutableLiveData<ArtistListResponse> artistListResponse = new MutableLiveData<>();
            ApiInterface endpoint = RequestConfig.getEnveuClient().create(ApiInterface.class);
            Call<ArtistListResponse> call = endpoint.getMediaContentV3("AUDIO", "",  "", pageNumber, pageSize, genres, "", "",  LanguageLayer.INSTANCE.getCurrentLanguageCode());
            call.enqueue(new Callback<ArtistListResponse>() {
                @Override
                public void onResponse(@NonNull Call<ArtistListResponse> call, @NonNull Response<ArtistListResponse> response) {
                    if (response.isSuccessful()) {
                        if (response.body() != null && response.body().getResponseCode() == 2000) {
                            artistListResponse.postValue(response.body());
                        }
                    } else {
                        artistListResponse.postValue(null);
                    }
                }

                @Override
                public void onFailure(@NonNull Call<ArtistListResponse> call, @NonNull Throwable t) {
                    artistListResponse.postValue(null);
                }
            });
            return artistListResponse;
        }

    public MutableLiveData<RemoveQueueResponse> removeAllQueueApi() {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("removeAll", true);
                jsonObject.addProperty("type", "QUEUED");
                MutableLiveData<RemoveQueueResponse> removeQueueResponse = new MutableLiveData<>();
                ApiInterface endpoint = RequestConfig.getMonetizationBaseURL(KsPreferenceKeys.getInstance().getAppPrefAccessToken()).create(ApiInterface.class);
                Call<RemoveQueueResponse> call = endpoint.removeAllQueueApi(jsonObject);
                call.enqueue(new Callback<RemoveQueueResponse>() {
                    @Override
                    public void onResponse(@NonNull Call<RemoveQueueResponse> call, @NonNull Response<RemoveQueueResponse> response) {
                        if (response.isSuccessful()) {
                            if (response.body() != null) {
                                removeQueueResponse.postValue(response.body());
                            }
                        } else {
                            removeQueueResponse.postValue(null);
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<RemoveQueueResponse> call, @NonNull Throwable t) {
                        removeQueueResponse.postValue(null);
                    }
                });
                return removeQueueResponse;
            }




    private String getGenresItems() {
        if (Objects.equals(genresItems, "")){
            KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
            if (preference.isActiveUserProfileData() != null) {
                if (preference.isActiveUserProfileData().getPreferenceSettings() != null) {
                    if (Objects.requireNonNull(preference.isActiveUserProfileData().getPreferenceSettings()).getGenresIds() != null){
                        List<Integer> genreIds = Objects.requireNonNull(preference.isActiveUserProfileData().getPreferenceSettings()).getGenresIds();
                        if (genreIds != null && !genreIds.isEmpty()) {
                            StringBuilder genresBuilder = new StringBuilder();
                            for (int i = 0; i < genreIds.size(); i++) {
                                if (i > 0) {
                                    genresBuilder.append(",");
                                }
                                genresBuilder.append(genreIds.get(i));
                            }
                            genresItems = genresBuilder.toString();
                        }
                    }
                }
            }
        }
        Log.d("getGenresItems", genresItems);
        return genresItems;
    }
}
