package com.enveu.repository.userManagement;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.enveu.OttApplication;
import com.enveu.R;
import com.enveu.SDKConfig;
import com.enveu.activities.profile.activate_device.ActivateTVDeviceModel;
import com.enveu.activities.usermanagment.model.OtpResponse;
import com.enveu.beanModel.connectFb.ResponseConnectFb;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModel.forgotPassword.CommonResponse;
import com.enveu.beanModel.requestParamModel.RequestParamRegisterUser;
import com.enveu.beanModel.responseModels.LoginResponse.LoginResponseModel;
import com.enveu.beanModel.responseModels.RegisterSignUpModels.ResponseRegisteredSignup;
import com.enveu.beanModel.responseModels.SignUp.SignupResponseAccessToken;
import com.enveu.beanModel.responseModels.listAllAccounts.AllSecondaryAccountDetails;
import com.enveu.beanModel.responseModels.secondaryUserDetails.SecondaryUserDetailsJavaPojo;
import com.enveu.beanModel.responseModels.sharing.SharingModel;
import com.enveu.beanModel.userProfile.UserProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.AddProfileRequestBody;
import com.enveu.beanModelV3.mutli_profile_response.AddSecondaryProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.GenerateOtpResponse;
import com.enveu.beanModelV3.mutli_profile_response.GetAllProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.ProfileSwitchResponse;
import com.enveu.beanModelV3.mutli_profile_response.RemoveSecondaryProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.SecondaryProfileData;
import com.enveu.beanModelV3.mutli_profile_response.UpdateSecondaryProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.ValidateOtpResponse;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.bean_model_v2_0.authResponse.AuthResponse;
import com.enveu.bean_model_v2_0.geo.GeoResponse;
import com.enveu.callbacks.apicallback.GeoInfoListener;
import com.enveu.client.api_callback.NetworkResultCallback;
import com.enveu.client.baseCollection.baseCategoryServices.BaseCategoryServices;
import com.enveu.client.deleteAccount.DeleteAccountCallback;
import com.enveu.client.deleteAccount.DeleteAccountResponse;
import com.enveu.client.epgCallBacks.EpgCallBack;
import com.enveu.client.joinContest.joinContestCallBack.JoinContestCallback;
import com.enveu.client.loginTVDevice.callBack.ActivateTvDeviceCallBack;
import com.enveu.client.userManagement.bean.allSecondaryDetails.AllSecondaryDetails;
import com.enveu.client.userManagement.bean.allSecondaryDetails.SecondaryUserDetails;
import com.enveu.client.userManagement.bean.registrationOtp.OtpResponseModel;
import com.enveu.client.userManagement.callBacks.AllListCallBack;
import com.enveu.client.userManagement.callBacks.AuthCallBack;
import com.enveu.client.userManagement.callBacks.ForgotPasswordCallBack;
import com.enveu.client.userManagement.callBacks.GetOTPCallback;
import com.enveu.client.userManagement.callBacks.LoginCallBack;
import com.enveu.client.userManagement.callBacks.SecondaryUserCallBack;
import com.enveu.client.userManagement.callBacks.UserProfileCallBack;
import com.enveu.networking.apiendpoints.ApiInterface;
import com.enveu.networking.apiendpoints.RequestConfig;
import com.enveu.networking.intercepter.ErrorCodesIntercepter;
import com.enveu.networking.servicelayer.APIServiceLayer;
import com.enveu.utils.Logger;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.StringUtils;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;
import com.google.gson.Gson;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;


public class RegistrationLoginRepository {


    private static RegistrationLoginRepository instance;
    private MutableLiveData<String> inValidOtpMutableData = new MutableLiveData<>();

    private RegistrationLoginRepository() {
    }

    public static RegistrationLoginRepository getInstance() {
        if (instance == null) {
            instance = new RegistrationLoginRepository();
        }
        if (KsPreferenceKeys.getInstance().getAppLanguage().equals(AppConstants.LANGUAGE_ARABIC)) {
            AppCommonMethod.updateLanguage(AppConstants.LANGUAGE_ARABIC, OttApplication.Companion.getContext());
        } else if (KsPreferenceKeys.getInstance().getAppLanguage().equals(AppConstants.ENGLISH_LAN_CODE)) {
            AppCommonMethod.updateLanguage(AppConstants.ENGLISH_LAN_CODE, OttApplication.Companion.getContext());
        }
        return (instance);
    }
    public MutableLiveData<ActivateTVDeviceModel> getLoginTV(String token, JsonObject activateCode) {
        final MutableLiveData<ActivateTVDeviceModel> responseApi;
        responseApi = new MutableLiveData<>();

        BaseCategoryServices.Companion.getInstance().loginTVDevice(token, activateCode, new ActivateTvDeviceCallBack() {
                    @Override
                    public void onFailure(boolean status, int errorCode, @NonNull String message) {
                        responseApi.postValue(null);
                    }

                    @Override
                    public void onSuccess(boolean status, @Nullable Response<com.enveu.client.loginTVDevice.model.ActivateTVDeviceModel> response) {
                        Gson gson = new Gson();
                        String tmp = gson.toJson(response.body());
                        ActivateTVDeviceModel loginItemBean = gson.fromJson(tmp, ActivateTVDeviceModel.class);
                        responseApi.postValue(loginItemBean);
                    }
                }
        );
        return responseApi;
    }


    public LiveData<LoginResponseModel> getLoginAPIResponse(String username, String pwd) {
        final MutableLiveData<LoginResponseModel> responseApi;
        responseApi = new MutableLiveData<>();
        final JsonObject requestParam = new JsonObject();
        requestParam.addProperty(AppConstants.API_PARAM_EMAIL, username);
        requestParam.addProperty(AppConstants.API_PARAM_PASSWORD, pwd);

        BaseCategoryServices.Companion.getInstance().loginService(username.trim(), pwd.trim(), new LoginCallBack() {
            @Override
            public void success(boolean status, Response< com.enveu.client.userManagement.bean.LoginResponse.LoginResponseModel> response) {
                if (status) {
                    LoginResponseModel cl;
                    if (response.body() != null) {
                        String token = response.headers().get("x-auth");
                        KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
                        preference.setAppPrefAccessToken(token);
                        preference.setPrimaryAccountUser(true);
                        //Logger.e("X_AUTHTOKEN",token);
                        Gson gson = new Gson();
                        String tmp = gson.toJson(response.body());
                        LoginResponseModel loginItemBean = gson.fromJson(tmp, LoginResponseModel.class);
                        responseApi.postValue(loginItemBean);
                    } else {
                        LoginResponseModel responseModel = Objects.requireNonNull(ErrorCodesIntercepter.getInstance()).Login(response);
                        responseApi.postValue(responseModel);
                    }
                }
            }

            @Override
            public void failure(boolean status, int errorCode, String errorMessage) {
                Logger.e("", "LoginResponseToUI E" + errorMessage);
                LoginResponseModel cl = new LoginResponseModel();
                cl.setStatus(false);
                responseApi.postValue(cl);
            }
        });

        return responseApi;
    }


    public LiveData<AuthResponse> getAuthResponse() {
        final MutableLiveData<AuthResponse> responseApi;
        responseApi = new MutableLiveData<>();

        BaseCategoryServices.Companion.getInstance().getAuthUrl(new AuthCallBack() {
            @Override
            public void success(boolean status, @NonNull Response<com.enveu.client.userManagement.bean.authResponse.AuthResponse> authResponse) {
                if (status) {
                    LoginResponseModel cl;
                    if (authResponse.body() != null) {
                        //Logger.e("X_AUTHTOKEN",token);
                        Gson gson = new Gson();
                        String tmp = gson.toJson(authResponse.body());
                        AuthResponse loginItemBean = gson.fromJson(tmp, AuthResponse.class);
                        responseApi.postValue(loginItemBean);
                    } else {
                        AuthResponse responseModel = Objects.requireNonNull(ErrorCodesIntercepter.getInstance()).Auth(authResponse);
                        responseApi.postValue(responseModel);
                    }
                }
            }

            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                AuthResponse cl = new AuthResponse();
                cl.setStatus(false);
                responseApi.postValue(cl);
            }
        });
        return responseApi;
    }


    public LiveData<SignupResponseAccessToken> getSignupAPIResponse(String name, String username, String email, String dob,String pwd, String profilePicURL, boolean isNotificationEnable,String phoneNumber,String gender, String imageUrl) {

        final MutableLiveData<SignupResponseAccessToken> responseApi;
        {
            responseApi = new MutableLiveData<>();

            final JsonObject requestParam = new JsonObject();
            requestParam.addProperty(AppConstants.API_PARAM_NAME, "");
            requestParam.addProperty(AppConstants.API_PARAM_EMAIL, "");
            requestParam.addProperty(AppConstants.API_PARAM_PASSWORD, pwd);

            BaseCategoryServices.Companion.getInstance().registerWithContentPreferences(name.trim(), username.trim(), email.trim(),dob.trim(), pwd.trim(), profilePicURL, isNotificationEnable,"",gender,"","",phoneNumber,"" ,imageUrl,new LoginCallBack() {
                @Override
                public void success(boolean status, Response< com.enveu.client.userManagement.bean.LoginResponse.LoginResponseModel> response) {
                    if (status) {
                        try {
                            if (response != null && response.errorBody() == null) {
                                Logger.d("responseValue: " + response.code());
                            }
                        } catch (Exception e) {
                            Logger.w(e);
                        }
                        if (response!=null && response.body() != null){
                            if (response.code() == 200 ) {
                                Gson gson = new Gson();
                                String tmp = gson.toJson(response.body());
                                LoginResponseModel cl = gson.fromJson(tmp, LoginResponseModel.class);
                                cl.setResponseCode(200);
                                String token = response.headers().get("x-auth");
                                SignupResponseAccessToken responseModel = new SignupResponseAccessToken();
                                responseModel.setAccessToken(token);
                                responseModel.setResponseModel(cl);
                                KsPreferenceKeys.getInstance().setPrimaryAccountUser(true);
                                Logger.e("manual", "nNontonToken" + token);

                                responseApi.postValue(responseModel);
                                Logger.e("signupResponse", "REsponse" + response.body());
                            } else {
                                SignupResponseAccessToken responseModel = Objects.requireNonNull(ErrorCodesIntercepter.getInstance()).manualSignUp(response);
                                Logger.e("signupResponse", "REsponse" + response.body());
                                responseApi.postValue(responseModel);
                            }
                        }else {
                            SignupResponseAccessToken responseModel = Objects.requireNonNull(ErrorCodesIntercepter.getInstance()).manualSignUp(Objects.requireNonNull(response));
                            responseApi.postValue(responseModel);
                        }

                    }
                }

                @Override
                public void failure(boolean status, int errorCode, String errorMessage) {
                    Logger.e("", "LoginResponseToUI E" + errorMessage);
                    LoginResponseModel cl = new LoginResponseModel();
                    cl.setResponseCode(400);
                    SignupResponseAccessToken responseModel = new SignupResponseAccessToken();
                    responseModel.setResponseModel(cl);
                    responseModel.setDebugMessage(OttApplication.Companion.getInstance().getResources().getString(R.string.server_error));
                    responseApi.postValue(responseModel);
                }
            });
        }

        return responseApi;
    }


    public LiveData<ResponseRegisteredSignup> getSignupAPIResponse(Context context, RequestParamRegisterUser userDetails) {

        final MutableLiveData<ResponseRegisteredSignup> responseApi;
        responseApi = new MutableLiveData<>();
        boolean check;
        KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
        Logger.e("Token", "nNontonToken" + userDetails.getAccessToken());

        ApiInterface endpoint = RequestConfig.getClientInterceptor(userDetails.getAccessToken()).create(ApiInterface.class);
        final JsonObject requestParam = new JsonObject();
        requestParam.addProperty(AppConstants.API_PARAM_NAME, userDetails.getName());
        requestParam.addProperty(AppConstants.API_PARAM_IS_VERIFIED, userDetails.isVerified());
        requestParam.addProperty(AppConstants.API_PARAM_VERIFICATION_DATE, userDetails.getVerificationDate());

        if (StringUtils.isNullOrEmptyOrZero(userDetails.getProfilePicURL())) {
            requestParam.add(AppConstants.API_PARAM_PROFILE_PIC, JsonNull.INSTANCE);

        } else {
            requestParam.addProperty(AppConstants.API_PARAM_PROFILE_PIC, userDetails.getProfilePicURL());
        }
        check = preference.getAppPrefDOB();
        if (check)
            requestParam.add(AppConstants.API_PARAM_DOB, JsonNull.INSTANCE);
        else
            requestParam.addProperty(AppConstants.API_PARAM_DOB, userDetails.getDateOfBirth());

        check = preference.getAppPrefHasNumberEmpty();
        if (check)
            requestParam.add(AppConstants.API_PARAM_PHONE_NUMBER, JsonNull.INSTANCE);
        else
            requestParam.addProperty(AppConstants.API_PARAM_PHONE_NUMBER, String.valueOf(userDetails.getPhoneNumber()));

        requestParam.addProperty(AppConstants.API_PARAM_STATUS, userDetails.getStatus());
        requestParam.addProperty(AppConstants.API_PARAM_EXPIRY_DATE, userDetails.getExpiryDate());
        requestParam.addProperty(AppConstants.API_PARAM_GENDER, userDetails.getGender());
        requestParam.addProperty(AppConstants.API_PARAM_PROFILE_STEP, "STEP_2");

        Call<ResponseRegisteredSignup> call = endpoint.getRegistrationStep(requestParam);
        call.enqueue(new Callback<ResponseRegisteredSignup>() {
            @Override
            public void onResponse(@NonNull Call<ResponseRegisteredSignup> call, @NonNull Response<ResponseRegisteredSignup> response) {
                // SignUpResponseModel cl = response.body();

                if (response.code() == 200) {
            ResponseRegisteredSignup temp = response.body();
                    temp.setStatus(true);
                    temp.setResponseCode(response.code());
                    responseApi.postValue(response.body());
                } else if (response.code() == 401) {
                    ResponseRegisteredSignup temp = new ResponseRegisteredSignup();
                    temp.setResponseCode(response.code());
                    temp.setStatus(false);
                    responseApi.postValue(temp);
                } else {
                    ResponseRegisteredSignup temp = new ResponseRegisteredSignup();
                    temp.setResponseCode(Objects.requireNonNull(response.code()));
                    temp.setStatus(false);
                    responseApi.postValue(temp);

                }

            }

            @Override
            public void onFailure(@NonNull Call<ResponseRegisteredSignup> call, @NonNull Throwable t) {
                Logger.e("error", "REsponse" + call);
                try {
                    if (call.execute().body() != null)
                        responseApi.postValue(call.execute().body());
                    else {

                        ResponseRegisteredSignup temp = new ResponseRegisteredSignup();
                        temp.setStatus(false);
                        temp.setResponseCode(500);

                    }
                } catch (IOException e) {
                    Logger.w(e);
                }

            }
        });

        return responseApi;
    }

    public LiveData<SharingModel> getDynamicLinkFromUserModel(Context context, EnveuVideoItemBean enveuVideoItemBean) {
        final MutableLiveData<SharingModel> liveData = new MutableLiveData<>();

        ApiInterface apiInterface = RequestConfig.getSharingClientHeader().create(ApiInterface.class);
        JsonObject requestBody = buildShareRequestBody(enveuVideoItemBean);


        Call<SharingModel> call = apiInterface.getDynamicLink(requestBody);

        call.enqueue(new Callback<SharingModel>() {
            @Override
            public void onResponse(@NonNull Call<SharingModel> call, @NonNull Response<SharingModel> response) {
                if (response.isSuccessful() && response.body() != null) {
                    SharingModel result = response.body();
                    liveData.postValue(result);
                } else {
                    SharingModel errorModel = new SharingModel();
                    liveData.postValue(errorModel);
                }
            }

            @Override
            public void onFailure(@NonNull Call<SharingModel> call, @NonNull Throwable t) {
                SharingModel errorModel = new SharingModel();
                liveData.postValue(errorModel);
            }
        });

        return liveData;
    }

    private JsonObject buildShareRequestBody(EnveuVideoItemBean content) {
        JsonObject requestBody = new JsonObject();

        requestBody.addProperty("deepLink", SDKConfig.DEEP_LINK + content.getAssetType().toLowerCase() + "/" + content.getContentSlug());
        requestBody.addProperty("favicon", content.getPosterURL());
        requestBody.addProperty("displayImg", content.getPosterURL());
        requestBody.addProperty("title", content.getTitle());
        requestBody.addProperty("description", content.getDescription());
        requestBody.addProperty("domainId", SDKConfig.DOMAIN_ID);

        JsonObject appleBehaviour = new JsonObject();
        appleBehaviour.addProperty("openInApp", true);
        appleBehaviour.addProperty("deeplinkUrl",  "/detail/" + content.getAssetType().toLowerCase() + "/" + content.getContentSlug());
        appleBehaviour.addProperty("urlScheme", "enveushare");
        requestBody.add("appleBehaviour", appleBehaviour);



        JsonObject androidBehaviour = new JsonObject();
        androidBehaviour.addProperty("openInApp", true);
        androidBehaviour.addProperty("deeplinkUrl",  "/detail/" + content.getAssetType().toLowerCase() + "/" + content.getContentSlug());
        androidBehaviour.addProperty("intentScheme", "enveushare");
        androidBehaviour.addProperty("fallbackToStore", false);
        requestBody.add("androidBehaviour", androidBehaviour);

        JsonObject socialMediaTags = new JsonObject();
        socialMediaTags.addProperty("st", content.getTitle());
        socialMediaTags.addProperty("sd", content.getDescription());
        socialMediaTags.addProperty("si", content.getPosterURL());
        requestBody.add("socialMediaTags", socialMediaTags);


        JsonObject utmParams = new JsonObject();
        utmParams.addProperty("utmSource", SDKConfig.UTM_PARAM);
        requestBody.add("utmParams", utmParams);



        return requestBody;
    }


    public LiveData<CommonResponse> getForgotPasswordAPIResponse(String email) {
        final MutableLiveData<CommonResponse> responseApi;
        {
            CommonResponse commonResponse = new CommonResponse();
            responseApi = new MutableLiveData<>();

            BaseCategoryServices.Companion.getInstance().forgotPasswordService(email, new ForgotPasswordCallBack() {
                @Override
                public void success(boolean status, Response<JsonObject> response) {

                    if (response.code() == 200) {
                        commonResponse.setCode(response.code());
                        responseApi.postValue(commonResponse);
                    } else {
                        String debugMessage = "";
                        try {
                            JSONObject jObjError = new JSONObject(response.errorBody().string());
                            debugMessage = jObjError.getString("debugMessage");
                            int errorcode = jObjError.getInt("responseCode");
                            if (errorcode==4401){
                                commonResponse.setDebugMessage(OttApplication.Companion.getInstance().getString(R.string.popup_user_does_not_exists));
                                commonResponse.setCode(response.code());
                            }else {
                                commonResponse.setDebugMessage(debugMessage);
                                commonResponse.setCode(response.code());
                            }
                            Logger.e("", "" + jObjError.getString("debugMessage"));
                        } catch (Exception e) {
                            Logger.e("RegistrationLoginRepo", "" + e);
                        }


                        responseApi.postValue(commonResponse);
                    }


                }

                @Override
                public void failure(boolean status, int errorCode, String message) {
                    commonResponse.setDebugMessage("");
                    commonResponse.setCode(500);
                    responseApi.postValue(commonResponse);
                }
            });

        }

        return responseApi;
    }


    public LiveData<LoginResponseModel> getChangePwdAPIResponse(String pwd, String token, Context context) {
        final MutableLiveData<LoginResponseModel> responseApi;
        responseApi = new MutableLiveData<>();
        final JsonObject requestParam = new JsonObject();
        requestParam.addProperty(AppConstants.API_PARAM_NEW_PWD, pwd);

        BaseCategoryServices.Companion.getInstance().changePasswordService(requestParam, token, new LoginCallBack() {
            @Override
            public void success(boolean status, Response< com.enveu.client.userManagement.bean.LoginResponse.LoginResponseModel> response) {
                if (status) {
                    LoginResponseModel cl;
                    if (response.code() == 500) {
                        cl = new LoginResponseModel();
                        cl.setResponseCode(Objects.requireNonNull(response.code()));
                        responseApi.postValue(cl);

                    } else if (response.code() == 401 || response.code() == 404) {
                        cl = new LoginResponseModel();
                        cl.setResponseCode(response.code());
                        String debugMessage = "";
                        try {
                            JSONObject jObjError = new JSONObject(response.errorBody().string());
                            debugMessage = jObjError.getString("debugMessage");
                            Logger.e("", "" + jObjError.getString("debugMessage"));
                        } catch (Exception e) {
                            Logger.e("RegistrationLoginRepo", "" + e);
                        }
                        cl.setDebugMessage(debugMessage);

                        responseApi.postValue(cl);
                    } else if (response.code() == 403) {
                        cl = new LoginResponseModel();
                        cl.setResponseCode(response.code());
                        String debugMessage = "";
                        try {
                            JSONObject jObjError = new JSONObject(response.errorBody().string());
                            debugMessage = jObjError.getString("debugMessage");
                            Logger.e("", "" + jObjError.getString("debugMessage"));
                        } catch (Exception e) {
                            Logger.e("RegistrationLoginRepo", "" + e);
                        }
                        cl.setDebugMessage(OttApplication.Companion.getInstance().getResources().getString(R.string.username_must_be_loggedin));

                        responseApi.postValue(cl);
                    } else if (response.body() != null ) {
                        Logger.e("", "LoginResponseModel" + response.body());
                        String token = response.headers().get("x-auth");
                        KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
                        preference.setAppPrefAccessToken(token);
                        preference.setPrimaryAccountUser(true);
                        Gson gson = new Gson();
                        String tmp = gson.toJson(response.body());
                        LoginResponseModel loginItemBean = gson.fromJson(tmp, LoginResponseModel.class);
                        responseApi.postValue(loginItemBean);
                    }
                }
            }

            @Override
            public void failure(boolean status, int errorCode, String errorMessage) {
                Logger.e("", "LoginResponseToUI E" + errorMessage);
                LoginResponseModel cl = new LoginResponseModel();
                cl.setStatus(false);
                responseApi.postValue(cl);
            }
        });

           /* call.enqueue(new Callback<ResponseChangePassword>() {
                @Override
                public void onResponse(@NonNull Call<ResponseChangePassword> call, @NonNull ContinueWatchingModel<ResponseChangePassword> response) {
                    if (response.code() == 200) {
                        ResponseChangePassword model = response.body();
                        model.setAccessToken(response.headers().get("x-auth"));
                        model.setStatus(true);
                        responseApi.postValue(response.body());
                    } else {
                        ResponseChangePassword model = new ResponseChangePassword();
                        model.setStatus(false);
                        model.setResponseCode(Objects.requireNonNull(response.code()));
                        responseApi.postValue(model);

                    }
                }

                @Override
                public void onFailure(@NonNull Call<ResponseChangePassword> call, @NonNull Throwable t) {
                    ResponseChangePassword model = new ResponseChangePassword();
                    model.setStatus(false);
                    responseApi.postValue(model);

                }
            });*/

        return responseApi;
    }


    public LiveData<LoginResponseModel> getFbLogin(Context context, String email, String fbToken, String name, String fbId, String profilePic, boolean isEmail) {
        final MutableLiveData<LoginResponseModel> responseApi;
        {
            responseApi = new MutableLiveData<>();
            ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
            final JsonObject requestParam = new JsonObject();
            requestParam.addProperty(AppConstants.API_PARAM_FB_ID, fbId);
            requestParam.addProperty(AppConstants.API_PARAM_NAME, name);
            requestParam.addProperty(AppConstants.API_PARAM_EMAIL_ID, email);
            requestParam.addProperty(AppConstants.API_PARAM_FB_TOKEN, fbToken);
            requestParam.addProperty(AppConstants.API_PARAM_IS_FB_EMAIL, isEmail);
            if (!profilePic.equalsIgnoreCase("")) {
                requestParam.addProperty(AppConstants.API_PARAM_FB_PIC, profilePic);
            }
            Call<LoginResponseModel> call = endpoint.getFbLogin(requestParam);

            BaseCategoryServices.Companion.getInstance().fbLoginService(requestParam, new LoginCallBack() {
                @Override
                public void success(boolean status, Response< com.enveu.client.userManagement.bean.LoginResponse.LoginResponseModel> response) {
                    if (status) {
                        LoginResponseModel cl;

                        if (response.body() != null ) {
                            String token = response.headers().get("x-auth");
                            KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
                            preference.setAppPrefAccessToken(token);
                            preference.setPrimaryAccountUser(true);
                            Gson gson = new Gson();
                            String tmp = gson.toJson(response.body());
                            LoginResponseModel loginItemBean = gson.fromJson(tmp, LoginResponseModel.class);

                            responseApi.postValue(loginItemBean);
                        } else {
                            LoginResponseModel responseModel = ErrorCodesIntercepter.getInstance().fbLogin(response);
                            responseApi.postValue(responseModel);
                        }

                    }
                }

                @Override
                public void failure(boolean status, int errorCode, String message) {
                    Logger.e("ResponseError", "getFbLogin" + call.toString());
                    try {
                        responseApi.postValue(call.execute().body());
                    } catch (IOException e) {
                        Logger.w(e);
                    }
                }
            });

        }
        return responseApi;
    }

    public LiveData<LoginResponseModel> getForceFbLogin(Context context, String email, String fbToken, String name, String fbId, boolean isEmail) {
        final MutableLiveData<LoginResponseModel> responseApi;
        {
            responseApi = new MutableLiveData<>();
            ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
            final JsonObject requestParam = new JsonObject();
            requestParam.addProperty(AppConstants.API_PARAM_FB_ID, fbId);
            requestParam.addProperty(AppConstants.API_PARAM_NAME, name);
            requestParam.addProperty(AppConstants.API_PARAM_EMAIL_ID, email);
            requestParam.addProperty(AppConstants.API_PARAM_FB_TOKEN, fbToken);
            requestParam.addProperty(AppConstants.FB_MAIL, isEmail);
            Call<LoginResponseModel> call = endpoint.getFbLogin(requestParam);

            BaseCategoryServices.Companion.getInstance().fbLoginService(requestParam, new LoginCallBack() {
                @Override
                public void success(boolean status, Response< com.enveu.client.userManagement.bean.LoginResponse.LoginResponseModel> response) {
                    if (status) {
                        LoginResponseModel cl;

                        if (response.body() != null ) {
                            String token = response.headers().get("x-auth");
                            KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
                            preference.setAppPrefAccessToken(token);
                            preference.setPrimaryAccountUser(true);
                            Gson gson = new Gson();
                            String tmp = gson.toJson(response.body());
                            LoginResponseModel loginItemBean = gson.fromJson(tmp, LoginResponseModel.class);

                            responseApi.postValue(loginItemBean);
                        } else {
                            LoginResponseModel responseModel = ErrorCodesIntercepter.getInstance().fbLogin(response);
                            responseApi.postValue(responseModel);
                        }

                    }
                }

                @Override
                public void failure(boolean status, int errorCode, String message) {
                    Logger.e("ResponseError", "getFbLogin" + call.toString());
                    try {
                        responseApi.postValue(call.execute().body());
                    } catch (IOException e) {
                        Logger.w(e);
                    }
                }
            });

        }
        return responseApi;
    }


    public LiveData<LoginResponseModel> getForceFbLogin(Context context, String email, String fbToken, String name, String fbId, String profilePic, boolean isEmail) {

        final MutableLiveData<LoginResponseModel> responseApi;
        {
            responseApi = new MutableLiveData<>();
            ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
            final JsonObject requestParam = new JsonObject();
            requestParam.addProperty(AppConstants.API_PARAM_FB_ID, fbId);
            requestParam.addProperty(AppConstants.API_PARAM_NAME, name);
            requestParam.addProperty(AppConstants.API_PARAM_EMAIL_ID, email);
            requestParam.addProperty(AppConstants.API_PARAM_FB_TOKEN, fbToken);
            requestParam.addProperty(AppConstants.API_PARAM_IS_FB_EMAIL, isEmail);
            if (!profilePic.equalsIgnoreCase("")) {
                requestParam.addProperty(AppConstants.API_PARAM_FB_PIC, profilePic);
            }
            Call<LoginResponseModel> call = endpoint.getForceFbLogin(requestParam);

            BaseCategoryServices.Companion.getInstance().fbForceLoginService(requestParam, new LoginCallBack() {
                @Override
                public void success(boolean status, Response< com.enveu.client.userManagement.bean.LoginResponse.LoginResponseModel> response) {
                    LoginResponseModel cl;
                    if (status) {

                        if (response.body() != null ) {
                            Logger.e("", "LoginResponseModel" + response.body());
                            String token = response.headers().get("x-auth");
                            KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
                            preference.setAppPrefAccessToken(token);
                            preference.setPrimaryAccountUser(true);
                            Gson gson = new Gson();
                            String tmp = gson.toJson(response.body());
                            LoginResponseModel loginItemBean = gson.fromJson(tmp, LoginResponseModel.class);

                            responseApi.postValue(loginItemBean);
                        } else {
                            LoginResponseModel responseModel = ErrorCodesIntercepter.getInstance().fbLogin(response);
                            responseApi.postValue(responseModel);
                        }

                    } else {
                        cl = new LoginResponseModel();
                        cl.setResponseCode(response.code());
                        String debugMessage = context.getResources().getString(R.string.server_error);
                        cl.setDebugMessage(debugMessage);
                    }
                }

                @Override
                public void failure(boolean status, int errorCode, String message) {
                    Logger.e("ResponseError", "getFbLogin" + call.toString());
                    try {
                        responseApi.postValue(call.execute().body());
                    } catch (IOException e) {
                        Logger.w(e);
                    }
                }
            });
        }
        return responseApi;
    }


    public LiveData<ResponseConnectFb> getConnectFb(Context context, String token, JsonObject requestParam) {
        final MutableLiveData<ResponseConnectFb> responseApi;
        {
            responseApi = new MutableLiveData<>();
            ApiInterface endpoint = RequestConfig.getClientInterceptor(token).create(ApiInterface.class);
            endpoint.getConnectFb(requestParam).enqueue(new Callback<ResponseConnectFb>() {
                @Override
                public void onResponse(@NonNull Call<ResponseConnectFb> call, @NonNull Response<ResponseConnectFb> response) {
                    ResponseConnectFb model = new ResponseConnectFb();
                    if (response.code() == 200) {
                        model.setStatus(true);
                        model.setData(response.body().getData());
                        responseApi.postValue(model);
                        KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
                        preference.setAppPrefAccessToken(response.headers().get("x-auth-token"));
                    } else if (response.code() == 400) {
                        String debugMessage;
                        int code = 0;
                        try {
                            JSONObject jObjError = new JSONObject(response.errorBody().string());
                            debugMessage = jObjError.getString("debugMessage");
                            code = jObjError.getInt("responseCode");

                            model.setResponseCode(400);
                            model.setDebugMessage(debugMessage);
                            model.setStatus(false);
                            responseApi.postValue(model);
                            Logger.e("", "" + jObjError.getString("debugMessage"));
                        } catch (Exception e) {
                            Logger.e("RegistrationLoginRepo", "" + e);
                        }
                    } else {
                        model.setStatus(false);
                        responseApi.postValue(model);
                    }
                }

                @Override
                public void onFailure(@NonNull Call<ResponseConnectFb> call, @NonNull Throwable t) {
                    ResponseConnectFb model = new ResponseConnectFb();
                    model.setStatus(false);
                }
            });
            return responseApi;
        }
    }


    public LiveData<JsonObject> hitApiLogout(boolean session, String token) {
        final MutableLiveData<JsonObject> responseApi;
        {
            responseApi = new MutableLiveData<>();
            ApiInterface endpoint = RequestConfig.getClientInterceptor(token).create(ApiInterface.class);

            Call<JsonObject> call = endpoint.getLogout(session);
            call.enqueue(new Callback<JsonObject>() {
                @Override
                public void onResponse(Call<JsonObject> call, Response<JsonObject> response) {
                    try {

                    } catch (Exception e) {

                    }
                    if (response.code() == 404) {
                        JsonObject jsonObject = new JsonObject();
                        jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());
                        responseApi.postValue(jsonObject);
                    } else if (response.code() == 200) {
                        Objects.requireNonNull(response.body()).addProperty(AppConstants.API_RESPONSE_CODE, response.code());
                        responseApi.postValue(response.body());
                    } else if (response.code() == 401) {
                        JsonObject jsonObject = new JsonObject();
                        jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());
                        responseApi.postValue(jsonObject);
                    } else if (response.code() == 500) {
                        JsonObject jsonObject = new JsonObject();
                        jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());
                        responseApi.postValue(jsonObject);
                    }
                }

                @Override
                public void onFailure(Call<JsonObject> call, Throwable t) {

                }
            });


        }

        return responseApi;
    }


    public LiveData<UserProfileResponse> getUserProfile(Context context, String token) {
        MutableLiveData<UserProfileResponse> mutableLiveData = new MutableLiveData<>();
        BaseCategoryServices.Companion.getInstance().userProfileService(token, new UserProfileCallBack() {
            @Override
            public void success(boolean status, Response< com.enveu.client.userManagement.bean.UserProfile.UserProfileResponse> response) {
                UserProfileResponse cl;
                if (status) {
                    if (response != null) {
                        if (response.code() == 200) {
                            Gson gson = new Gson();
                            String tmp = gson.toJson(response.body());
                            UserProfileResponse profileItemBean = gson.fromJson(tmp, UserProfileResponse.class);
                            Log.d("profileDataSignUp", new Gson().toJson(profileItemBean));
                            if (KsPreferenceKeys.getInstance().isPrimaryAccountUser()) {
                                KsPreferenceKeys.getInstance().saveUserProfileData(profileItemBean);
                                KsPreferenceKeys.getInstance().setUserId(profileItemBean.getData().getId());
                            }
                            SecondaryProfileData secondaryProfile = new Gson().fromJson(new Gson().toJson(response.body().getData()), SecondaryProfileData.class);
                            KsPreferenceKeys.getInstance().saveActiveProfileData(secondaryProfile);
                            profileItemBean.setStatus(true);
                            mutableLiveData.postValue(profileItemBean);
                        } else {
                            cl = Objects.requireNonNull(ErrorCodesIntercepter.getInstance()).userProfile(response);
                            mutableLiveData.postValue(cl);

                        }
                    }


                }
            }

            @Override
            public void failure(boolean status, int errorCode, String message) {
                UserProfileResponse cl = new UserProfileResponse();
                cl.setStatus(false);
                mutableLiveData.postValue(cl);
            }
        });
        return mutableLiveData;
    }

    public LiveData<OtpResponse> getGenerateOTPResponse(String token) {
        final MutableLiveData<OtpResponse> responseApi;
        responseApi = new MutableLiveData<>();
        BaseCategoryServices.Companion.getInstance().generateOtpReq(token, new GetOTPCallback() {
            @Override
            public void success(boolean status, @NonNull Response<OtpResponseModel> response) {
                if (status) {
                    if (response.body() != null) {
                        Gson gson = new Gson();
                        String tmp = gson.toJson(response.body());
                        OtpResponse otpResponse = gson.fromJson(tmp, OtpResponse.class);
                        responseApi.postValue(otpResponse);
                    } else {
                        OtpResponse responseModel = ErrorCodesIntercepter.getInstance().otpResponse(response);
                        responseApi.postValue(responseModel);
                    }
                }
            }

            @Override
            public void failure(boolean status, int errorCode, @NonNull String errorMessage) {
                Logger.e("OtpResponse", "Error -> " + errorMessage);
                OtpResponse otpErrorResponse = new OtpResponse();
                otpErrorResponse.setDebugMessage(errorMessage);
                responseApi.postValue(otpErrorResponse);
            }
        });
        return responseApi;
    }

    public LiveData<OtpResponse> getOTPVerify(int otp, String token) {
        final MutableLiveData<OtpResponse> responseApi;
        responseApi = new MutableLiveData<>();
        BaseCategoryServices.Companion.getInstance().otpVerify(otp, token, new GetOTPCallback() {
            @Override
            public void success(boolean status, @NonNull Response<OtpResponseModel> response) {
                if (status) {
                    if (response.body() != null) {
                        Gson gson = new Gson();
                        String tmp = gson.toJson(response.body());
                        OtpResponse otpResponse = gson.fromJson(tmp, OtpResponse.class);
                        responseApi.postValue(otpResponse);
                    } else {
                        OtpResponse responseModel = ErrorCodesIntercepter.getInstance().otpResponse(response);
                        responseApi.postValue(responseModel);
                    }
                }
            }

            @Override
            public void failure(boolean status, int errorCode, @NonNull String errorMessage) {
                Logger.e("OtpResponse", "Error -> " + errorMessage);
                OtpResponse otpErrorResponse = new OtpResponse();
                otpErrorResponse.setDebugMessage(errorMessage);
                responseApi.postValue(otpErrorResponse);
            }
        });
        return responseApi;
    }



    public LiveData<UserProfileResponse> getUpdateProfile(Context context, String token, JsonObject jsonObject) {
        MutableLiveData<UserProfileResponse> mutableLiveData = new MutableLiveData<>();
        BaseCategoryServices.Companion.getInstance().userUpdateProfileApi(token, jsonObject, new UserProfileCallBack() {
            @Override
            public void success(boolean status, Response< com.enveu.client.userManagement.bean.UserProfile.UserProfileResponse> response) {
                UserProfileResponse cl;
                if (status) {
                    if (response != null) {
                        if (response.code() == 200) {
                            Gson gson = new Gson();
                            String tmp = gson.toJson(response.body());
                            UserProfileResponse profileItemBean = gson.fromJson(tmp, UserProfileResponse.class);
                            profileItemBean.setStatus(true);
                            if (KsPreferenceKeys.getInstance().isPrimaryAccountUser()) {
                                KsPreferenceKeys.getInstance().saveUserProfileData(profileItemBean);
                                KsPreferenceKeys.getInstance().setUserId(profileItemBean.getData().getId());
                            }
                            if (response.body().getData() != null) {
                                SecondaryProfileData secondaryProfileData = gson.fromJson(new Gson().toJson(response.body().getData()), SecondaryProfileData.class);
                                KsPreferenceKeys.getInstance().saveActiveProfileData(secondaryProfileData);
                            }
                            mutableLiveData.postValue(profileItemBean);
                        } else {
                            cl = ErrorCodesIntercepter.getInstance().userProfile(response);
                            mutableLiveData.postValue(cl);
                        }
                    }


                }
            }

            @Override
            public void failure(boolean status, int errorCode, String message) {
                UserProfileResponse cl = new UserProfileResponse();
                cl.setStatus(false);
                mutableLiveData.postValue(cl);
            }
        });
        return mutableLiveData;
    }


    public LiveData<UserProfileResponse> getUpdateProfile(Context context, String token, String name, String mobile, String spinnerValue, String dob, String address, String imageUrl, String via, String contentPreference, boolean isNotificationEnable, String parentalPin, String city, String country, String profile, String species, String type , String lastName, boolean parentalPinEnabled, List<Integer> appUserIntrests, String selectedSponserId) {
        MutableLiveData<UserProfileResponse> mutableLiveData = new MutableLiveData<>();
        BaseCategoryServices.Companion.getInstance().userUpdateProfileServiceWithNoti(token, name,mobile,spinnerValue,dob,address,imageUrl,via,contentPreference,isNotificationEnable,parentalPin,city,country,profile,species,type,lastName,parentalPinEnabled, appUserIntrests,selectedSponserId,new UserProfileCallBack() {
            @Override
            public void success(boolean status, Response< com.enveu.client.userManagement.bean.UserProfile.UserProfileResponse> response) {
                UserProfileResponse cl;
                if (status) {
                    if (response != null) {
                        if (response.code() == 200) {
                            Gson gson = new Gson();
                            String tmp = gson.toJson(response.body());
                            UserProfileResponse profileItemBean = gson.fromJson(tmp, UserProfileResponse.class);
                            profileItemBean.setStatus(true);
                            if (KsPreferenceKeys.getInstance().isPrimaryAccountUser()) {
                                KsPreferenceKeys.getInstance().saveUserProfileData(profileItemBean);
                                KsPreferenceKeys.getInstance().setUserId(profileItemBean.getData().getId());
                            }
                            mutableLiveData.postValue(profileItemBean);
                        } else {
                            cl = ErrorCodesIntercepter.getInstance().userProfile(response);
                            mutableLiveData.postValue(cl);
                        }
                    }


                }
            }

            @Override
            public void failure(boolean status, int errorCode, String message) {
                UserProfileResponse cl = new UserProfileResponse();
                cl.setStatus(false);
                mutableLiveData.postValue(cl);
            }
        });
        return mutableLiveData;
    }


    public LiveData<UserProfileResponse> updateSponsor(String token, List<Integer> appUserIntrests, String  selectedSponserId, String name,String lastName,String countryCode,String mobileNumber,String dob,String country,String city,String gender) {

        MutableLiveData<UserProfileResponse> mutableLiveData = new MutableLiveData<>();
        BaseCategoryServices.Companion.getInstance().sponsorUpdateProfileService(token, appUserIntrests,selectedSponserId, name,lastName,countryCode,mobileNumber, dob,country, city,gender, new UserProfileCallBack() {
            @Override
            public void success(boolean status, Response< com.enveu.client.userManagement.bean.UserProfile.UserProfileResponse> response) {
                UserProfileResponse cl;
                if (status) {
                    if (response != null) {
                        if (response.code() == 200) {
                            Gson gson = new Gson();
                            String tmp = gson.toJson(response.body());
                            UserProfileResponse profileItemBean = gson.fromJson(tmp, UserProfileResponse.class);
                            profileItemBean.setStatus(true);
                            mutableLiveData.postValue(profileItemBean);
                        } else {
                            cl = ErrorCodesIntercepter.getInstance().userProfile(response);
                            mutableLiveData.postValue(cl);
                        }
                    }


                }
            }

            @Override
            public void failure(boolean status, int errorCode, String message) {
                UserProfileResponse cl = new UserProfileResponse();
                cl.setStatus(false);
                mutableLiveData.postValue(cl);
            }
        });
        return mutableLiveData;
    }

    public LiveData<GeoResponse> getGeoInfo(){
        MutableLiveData<GeoResponse> geoResponseMutableLiveData=new MutableLiveData<>();
        APIServiceLayer.getInstance().getGeoInfo(new GeoInfoListener() {
            @Override
            public void getGeoInfo(boolean success, @NonNull GeoResponse geoResponse) {
                if (success){
                    geoResponseMutableLiveData.postValue(geoResponse);
                }else {
                    geoResponseMutableLiveData.postValue(null);
                }
            }
        });
        return geoResponseMutableLiveData;
    }


    private List<EnveuVideoItemBean> enveuVideoItemBeans;
    public LiveData<RailCommonData>getEpgListing(Context context, String channelId,Long startDate , Long endDate,int pageNumber,int pageSize,String languageCode) {
        MutableLiveData<RailCommonData> mutableLiveData = new MutableLiveData<>();

        BaseCategoryServices.Companion.getInstance().getEpgListing(channelId,startDate,endDate,pageNumber,pageSize,languageCode, new EpgCallBack() {
            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                mutableLiveData.postValue(null);
            }

            @Override
            public void success(boolean status, Response<com.enveu.client.epgListing.epgResponseNew.Response> response) {
                if (status) {
                    if (response != null) {
                        if (response.code() == 200) {
                            RailCommonData railCommonData = null;
                            railCommonData = new RailCommonData();

                            if (response.body()!=null) {
                                if (response.body().getData() !=null){
                                    List<com.enveu.client.epgListing.epgResponseNew.DataItem> dataItem = response.body().getData();
                                    enveuVideoItemBeans = new ArrayList<>();
                                    for (int i=0;i<dataItem.size();i++){
                                        com.enveu.client.epgListing.epgResponseNew.MediaContent mediaContent= dataItem.get(i).getMediaContent();
                                        Gson gson1 = new Gson();
                                        String tmp1 = gson1.toJson(mediaContent);
                                        EnveuVideoItemBean enveuVideoItemBean = new EnveuVideoItemBean(mediaContent);

                                        if (dataItem.get(i).getSchedules()!=null) {
                                            railCommonData.setSchedulesItemArrayList(dataItem.get(i).getSchedules());
                                            enveuVideoItemBeans.add(enveuVideoItemBean);
                                        }
                                    }
                                    railCommonData.setEnveuVideoItemBeans(enveuVideoItemBeans);
                                }

                            }
                            mutableLiveData.postValue(railCommonData);
                        } else {
                            mutableLiveData.postValue(null);
                        }
                    }
                }
                }

        });
        return mutableLiveData;
    }


    /////
    public LiveData<AllSecondaryAccountDetails>  getSecondaryAPIResponse(Context context, String token) {
        final MutableLiveData<AllSecondaryAccountDetails> responseApi;
        responseApi = new MutableLiveData<>();


        BaseCategoryServices.Companion.getInstance().AllListService( token,new AllListCallBack() {
            @Override
            public void success(boolean status, Response<AllSecondaryDetails> response) {
                if (status) {
                    AllSecondaryAccountDetails cl;
                    if (response.body() != null) {
                     /*   String token = response.headers().get("x-auth");
                        KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
                        preference.setAppPrefAccessToken(token);*/
                        Gson gson = new Gson();
                        String tmp = gson.toJson(response.body());
                        AllSecondaryAccountDetails loginItemBean = gson.fromJson(tmp, AllSecondaryAccountDetails.class);
                        responseApi.postValue(loginItemBean);
                    } else {
                        AllSecondaryAccountDetails responseModel = ErrorCodesIntercepter.getInstance().allSecondaryAccountDetailsl(response);
                        responseApi.postValue(responseModel);
                    }
                }
            }

            @Override
            public void failure(boolean status, int errorCode, String errorMessage) {
                Logger.e("", "AllSecondaryResponse E" + errorMessage);
                AllSecondaryAccountDetails cl = new AllSecondaryAccountDetails();
                cl.setDebugMessage(errorMessage);
                responseApi.postValue(cl);
            }
        });

        return responseApi;
    }

    public LiveData<SecondaryUserDetailsJavaPojo> getSecondaryUserAPIReponse(String token,String userName) {
        final MutableLiveData<SecondaryUserDetailsJavaPojo> responseApi;
        responseApi = new MutableLiveData<>();

        BaseCategoryServices.Companion.getInstance().SecondaryUserService( token,userName,new SecondaryUserCallBack() {
            @Override
            public void success(boolean status, Response<SecondaryUserDetails> response) {
                if (status) {
                    SecondaryUserDetails cl;
                    if (response.body() != null) {
                        Gson gson = new Gson();
                        String tmp = gson.toJson(response.body());
                        SecondaryUserDetailsJavaPojo loginItemBean = gson.fromJson(tmp, SecondaryUserDetailsJavaPojo.class);
                        responseApi.postValue(loginItemBean);
                    } else {
                        SecondaryUserDetailsJavaPojo responseModel = ErrorCodesIntercepter.getInstance().secondaryUserDetails(response);
                        responseApi.postValue(responseModel);
                    }
                }
            }

            @Override
            public void failure(boolean status, int errorCode, String errorMessage) {
                Logger.e("", "SecondaryUser E" + errorMessage);
                SecondaryUserDetailsJavaPojo cl = new SecondaryUserDetailsJavaPojo();
                cl.setDebugMessage(errorMessage);
                responseApi.postValue(cl);
            }
        });

        return responseApi;
    }

    public LiveData<DeleteAccountResponse> deleteAccountReq(String token) {
        MutableLiveData<DeleteAccountResponse> deleteAccountResponseMutableLiveData = new MutableLiveData<>();
        BaseCategoryServices.Companion.getInstance().deleteAccountReq(token,new DeleteAccountCallback(){

            @Override
            public void success(boolean status, @NotNull Response<DeleteAccountResponse> response) {
                try {
                    if (response.body()==null) {
                        if (response.errorBody()!=null) {
                            try{
                                JSONObject errorObject = new JSONObject(response.errorBody().string());
                                int responseCode = errorObject.getInt("responseCode");
                                String debugMessage = errorObject.getString("debugMessage");
                                if (responseCode !=0){
                                    DeleteAccountResponse deleteAccountResponse = new DeleteAccountResponse();
                                    if (responseCode == 4906) {
                                        deleteAccountResponse.setResponseCode(responseCode);
                                        deleteAccountResponse.setDebugMessage(debugMessage);
                                    } else {
                                        deleteAccountResponse.setResponseCode(AppConstants.RESPONSE_CODE_ERROR);
                                    }
                                    deleteAccountResponseMutableLiveData.postValue(deleteAccountResponse);
                                } else {
                                    DeleteAccountResponse deleteAccountResponse = new DeleteAccountResponse();
                                    deleteAccountResponse.setResponseCode(AppConstants.RESPONSE_CODE_ERROR);
                                    deleteAccountResponseMutableLiveData.postValue(deleteAccountResponse);
                                }
                            }
                            catch (Exception ignored){
                            }
                        } else {
                            DeleteAccountResponse deleteAccountResponse = new DeleteAccountResponse();
                            deleteAccountResponse.setResponseCode(AppConstants.RESPONSE_CODE_ERROR);
                            deleteAccountResponseMutableLiveData.postValue(deleteAccountResponse);
                        }
                    } else {
                        if (response.isSuccessful()) {
                            int responseCode = response.body().getResponseCode();
                            if (responseCode !=0){
                                if (response.code() == 200) {
                                    DeleteAccountResponse deleteAccountResponse = new DeleteAccountResponse();
                                    deleteAccountResponse.setResponseCode(responseCode);
                                    //deleteAccountResponse.setDebugMessage(debugMessage);
                                    deleteAccountResponseMutableLiveData.setValue(deleteAccountResponse);
                                }
                            }
                        } else {
                            DeleteAccountResponse deleteAccountResponse = new DeleteAccountResponse();
                            deleteAccountResponse.setResponseCode(AppConstants.RESPONSE_CODE_ERROR);
                            deleteAccountResponseMutableLiveData.postValue(deleteAccountResponse);
                        }

                    }
                }
                catch (Exception e){
                    DeleteAccountResponse deleteAccountResponse = new DeleteAccountResponse();
                    deleteAccountResponse.setResponseCode(AppConstants.RESPONSE_CODE_ERROR);
                    deleteAccountResponseMutableLiveData.postValue(deleteAccountResponse);
                }
            }
            @Override
            public void failure(boolean status, int errorCode, String message) {
                DeleteAccountResponse deleteAccountResponse = new DeleteAccountResponse();
                deleteAccountResponse.setResponseCode(AppConstants.RESPONSE_CODE_ERROR);
                deleteAccountResponseMutableLiveData.postValue(deleteAccountResponse);
            }
        });
        return deleteAccountResponseMutableLiveData;
    }


    public LiveData<com.enveu.client.joinContest.joinContestResponse.Response> checkUserContestApi(String token, int userId, int contestId, String languageCode) {
        MutableLiveData<com.enveu.client.joinContest.joinContestResponse.Response> joinContestMutableLiveData = new MutableLiveData<>();

        BaseCategoryServices.Companion.getInstance().checkContestForUser(token,userId,contestId,languageCode, new JoinContestCallback(){

            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                joinContestMutableLiveData.postValue(null);
            }

            @Override
            public void success(boolean status, @NonNull Response<com.enveu.client.joinContest.joinContestResponse.Response> joinContestResponse) {
                if (status && joinContestResponse.isSuccessful() && joinContestResponse.body().getData() != null) {
                    joinContestMutableLiveData.postValue(joinContestResponse.body());
                }else {
                    joinContestMutableLiveData.postValue(joinContestResponse.body());
                }
            }
        });

        return joinContestMutableLiveData;
    }

    public LiveData<com.enveu.client.joinContest.joinContestResponse.Response> joinContestForUser(String token, int userId, int contestId, String languageCode) {
        MutableLiveData<com.enveu.client.joinContest.joinContestResponse.Response> joinContestMutableLiveData = new MutableLiveData<>();

        BaseCategoryServices.Companion.getInstance().joinContestForUser(token,userId,contestId,languageCode, new JoinContestCallback(){

            @Override
            public void failure(boolean status, int errorCode, @NonNull String message) {
                joinContestMutableLiveData.postValue(null);
            }

            @Override
            public void success(boolean status, @NonNull Response<com.enveu.client.joinContest.joinContestResponse.Response> joinContestResponse) {
                if (status && joinContestResponse.isSuccessful() && joinContestResponse.body().getData() != null) {
                    joinContestMutableLiveData.postValue(joinContestResponse.body());
                }else {
                    joinContestMutableLiveData.postValue(joinContestResponse.body());
                }
            }
        });
        return joinContestMutableLiveData;
    }

    public MutableLiveData<GetAllProfileResponse> getAllSecondaryProfileList() {
        MutableLiveData<GetAllProfileResponse> profileResponse = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
        Call<GetAllProfileResponse> call = endpoint.getAllSecondaryProfileList(KsPreferenceKeys.getInstance().getAppPrefAccessToken());
        call.enqueue(new Callback<GetAllProfileResponse>() {
            @Override
            public void onResponse(@NonNull Call<GetAllProfileResponse> call, @NonNull Response<GetAllProfileResponse> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getResponseCode() == 2000) {
                        profileResponse.postValue(response.body());
                    }
                } else {
                    profileResponse.postValue(null);
                }
            }

            @Override
            public void onFailure(@NonNull Call<GetAllProfileResponse> call, @NonNull Throwable t) {
                profileResponse.postValue(null);
            }
        });
        return profileResponse;
    }

    public MutableLiveData<AddSecondaryProfileResponse> addSecondaryProfile(AddProfileRequestBody addProfileRequestBody) {
        MutableLiveData<AddSecondaryProfileResponse> addProfileResponse = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
        Call<AddSecondaryProfileResponse> call = endpoint.addSecondaryProfile(KsPreferenceKeys.getInstance().getAppPrefAccessToken(), addProfileRequestBody);
        call.enqueue(new Callback<AddSecondaryProfileResponse>() {
            @Override
            public void onResponse(@NonNull Call<AddSecondaryProfileResponse> call, @NonNull Response<AddSecondaryProfileResponse> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getResponseCode() == 2000) {
                        addProfileResponse.postValue(response.body());
                    }
                } else {
                    AddSecondaryProfileResponse errorModel = new AddSecondaryProfileResponse();
                    addProfileResponse.postValue(errorModel);
                }
            }

            @Override
            public void onFailure(@NonNull Call<AddSecondaryProfileResponse> call, @NonNull Throwable t) {
                AddSecondaryProfileResponse errorModel = new AddSecondaryProfileResponse();
                addProfileResponse.postValue(errorModel);
            }
        });
        return addProfileResponse;
    }

    public MutableLiveData<UpdateSecondaryProfileResponse> updateSecondaryProfile(SecondaryProfileData secondaryProfileData) {
        MutableLiveData<UpdateSecondaryProfileResponse> updateProfileResponse = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
        Call<UpdateSecondaryProfileResponse> call = endpoint.updateSecondaryProfile(KsPreferenceKeys.getInstance().getAppPrefAccessToken(), secondaryProfileData);
        call.enqueue(new Callback<UpdateSecondaryProfileResponse>() {
            @Override
            public void onResponse(@NonNull Call<UpdateSecondaryProfileResponse> call, @NonNull Response<UpdateSecondaryProfileResponse> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getResponseCode() == 2000) {
                        updateProfileResponse.postValue(response.body());
                    }
                } else {
                    updateProfileResponse.postValue(null);
                }
            }

            @Override
            public void onFailure(@NonNull Call<UpdateSecondaryProfileResponse> call, @NonNull Throwable t) {
                updateProfileResponse.postValue(null);
            }
        });
        return updateProfileResponse;
    }

    public MutableLiveData<RemoveSecondaryProfileResponse> deleteSecondaryProfile(String accountId) {
        MutableLiveData<RemoveSecondaryProfileResponse> deleteProfileResponse = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
        Call<RemoveSecondaryProfileResponse> call = endpoint.deleteSecondaryProfile(KsPreferenceKeys.getInstance().getAppPrefAccessToken(), accountId);
        call.enqueue(new Callback<RemoveSecondaryProfileResponse>() {
            @Override
            public void onResponse(@NonNull Call<RemoveSecondaryProfileResponse> call, @NonNull Response<RemoveSecondaryProfileResponse> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getResponseCode() == 2000) {
                        deleteProfileResponse.postValue(response.body());
                    }
                } else {
                    deleteProfileResponse.postValue(null);
                }
            }

            @Override
            public void onFailure(@NonNull Call<RemoveSecondaryProfileResponse> call, @NonNull Throwable t) {
                deleteProfileResponse.postValue(null);
            }
        });
        return deleteProfileResponse;
    }

    public MutableLiveData<ProfileSwitchResponse> switchAnotherProfile(String authToken, SecondaryProfileData secondaryProfileData) {
        MutableLiveData<ProfileSwitchResponse> switchAnotherResponse = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
        Call<ProfileSwitchResponse> call = endpoint.switchAnotherProfile(authToken, secondaryProfileData.getAccountId());
        call.enqueue(new Callback<ProfileSwitchResponse>() {
            @Override
            public void onResponse(@NonNull Call<ProfileSwitchResponse> call, @NonNull Response<ProfileSwitchResponse> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getResponseCode() == 2000) {
                            String token = response.headers().get("x-auth");
                            KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
                            preference.setAppPrefAccessToken(token);
                            preference.setUserId(response.body().getData().getId());
                            preference.saveActiveProfileData(secondaryProfileData);
                            switchAnotherResponse.postValue(response.body());
                    }
                } else {
                    switchAnotherResponse.postValue(null);
                }
            }

            @Override
            public void onFailure(@NonNull Call<ProfileSwitchResponse> call, @NonNull Throwable t) {
                switchAnotherResponse.postValue(null);
            }
        });
        return switchAnotherResponse;
    }

    public MutableLiveData<String> switchMainProfile(String accountId) {
        MutableLiveData<String> switchMainResponse = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
        Call<ProfileSwitchResponse> call = endpoint.switchMainProfile(KsPreferenceKeys.getInstance().getAppPrefAccessToken(), accountId);
        call.enqueue(new Callback<ProfileSwitchResponse>() {
            @Override
            public void onResponse(@NonNull Call<ProfileSwitchResponse> call, @NonNull Response<ProfileSwitchResponse> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getResponseCode() == 2000) {
                        String token = response.headers().get("x-auth");
                        switchMainResponse.postValue(token);
                    }
                } else {
                    switchMainResponse.postValue(null);
                }
            }

            @Override
            public void onFailure(@NonNull Call<ProfileSwitchResponse> call, @NonNull Throwable t) {
                switchMainResponse.postValue(null);
            }
        });
        return switchMainResponse;
    }

    public MutableLiveData<UserProfileResponse> updateParentProfile(SecondaryProfileData secondaryProfileData, String authToken) {
        MutableLiveData<UserProfileResponse> updateProfileResponse = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
        Call<UserProfileResponse> call = endpoint.updateParentProfile(authToken, secondaryProfileData);
        call.enqueue(new Callback<UserProfileResponse>() {
            @Override
            public void onResponse(@NonNull Call<UserProfileResponse> call, @NonNull Response<UserProfileResponse> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getResponseCode() == 2000) {
                        if (KsPreferenceKeys.getInstance().isPrimaryAccountUser()) {
                            KsPreferenceKeys.getInstance().saveUserProfileData(response.body());
                            KsPreferenceKeys.getInstance().setUserId(response.body().getData().getId());
                        }
                        else {
                            SecondaryProfileData  secondaryProfile = new Gson().fromJson(new Gson().toJson(response.body().getData()), SecondaryProfileData.class);
                            KsPreferenceKeys.getInstance().saveActiveProfileData(secondaryProfile);
                        }
                        updateProfileResponse.postValue(response.body());
                    }
                } else {
                    updateProfileResponse.postValue(null);
                }
            }

            @Override
            public void onFailure(@NonNull Call<UserProfileResponse> call, @NonNull Throwable t) {
                updateProfileResponse.postValue(null);
            }
        });
        return updateProfileResponse;
    }


    public MutableLiveData<GenerateOtpResponse> generateOtpParentalPin(String userEmail) {
        JsonObject jsonObject = new  JsonObject();
        jsonObject.addProperty("email", userEmail);
        MutableLiveData<GenerateOtpResponse> generateOtpResponse = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
        Call<GenerateOtpResponse> call = endpoint.generateOtpParentalPin(KsPreferenceKeys.getInstance().getAppPrefAccessToken(), jsonObject);
        call.enqueue(new Callback<GenerateOtpResponse>() {
            @Override
            public void onResponse(@NonNull Call<GenerateOtpResponse> call, @NonNull Response<GenerateOtpResponse> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getResponseCode() == 2000) {
                        generateOtpResponse.postValue(response.body());
                    }
                } else {
                    generateOtpResponse.postValue(null);
                }
            }

            @Override
            public void onFailure(@NonNull Call<GenerateOtpResponse> call, @NonNull Throwable t) {
                generateOtpResponse.postValue(null);
            }
        });
        return generateOtpResponse;
    }


    public MutableLiveData<ValidateOtpResponse> validateParentalLockOtp(String token, String otpText) {
        JsonObject jsonObject = new  JsonObject();
        jsonObject.addProperty("otp", otpText);
        jsonObject.addProperty("token", token);
        MutableLiveData<ValidateOtpResponse> generateOtpResponse = new MutableLiveData<>();
        ApiInterface endpoint = RequestConfig.getEnveuSubscriptionClient().create(ApiInterface.class);
        Call<ValidateOtpResponse> call = endpoint.validateParentalLockOtp(KsPreferenceKeys.getInstance().getAppPrefAccessToken(), jsonObject);
        call.enqueue(new Callback<ValidateOtpResponse>() {
            @Override
            public void onResponse(@NonNull Call<ValidateOtpResponse> call, @NonNull Response<ValidateOtpResponse> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null && response.body().getResponseCode() == 2000) {
                        generateOtpResponse.postValue(response.body());
                    }
                } else {
                    inValidOtpMutableData.postValue("IN_Valid_Otp");
                    generateOtpResponse.postValue(null);
                }
            }

            @Override
            public void onFailure(@NonNull Call<ValidateOtpResponse> call, @NonNull Throwable t) {
                generateOtpResponse.postValue(null);
            }
        });
        return generateOtpResponse;
    }

    public LiveData<String> inValidOtpData(){
        return inValidOtpMutableData;
    }


    public void uniqueUserNameApi(JsonObject jsonObject, NetworkResultCallback<JsonObject> networkResultCallback) {
        BaseCategoryServices.Companion.getInstance().uniqueUserNameApi(jsonObject, networkResultCallback);
    }
}



