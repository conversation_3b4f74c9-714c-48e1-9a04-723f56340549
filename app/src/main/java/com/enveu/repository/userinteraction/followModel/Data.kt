package com.enveu.repository.userinteraction.followModel


import com.google.gson.annotations.SerializedName

data class Data(
    @SerializedName("assetId")
    val assetId: Int?, // 315
    @SerializedName("assetType")
    val assetType: Any?, // null
    @SerializedName("dateCreated")
    val dateCreated: Long?, // 1714553803580
    @SerializedName("id")
    val id: String?, // 663203cbfa4f8c001141afa8
    @SerializedName("lastUpdated")
    val lastUpdated: Long?, // 1714553803580
    @SerializedName("projectId")
    val projectId: String?, // proj-1709139359789
    @SerializedName("userId")
    val userId: Int? // 95
)