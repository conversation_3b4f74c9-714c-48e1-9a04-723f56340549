package com.enveu.ugc

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity.RESULT_OK
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.brightcove.player.display.ExoPlayerVideoDisplayComponent
import com.brightcove.player.view.BrightcoveExoPlayerVideoView
import com.enveu.R
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.enveu.activities.follow_follower.FollowFollowingProfileActivity
import com.enveu.activities.follow_follower.HashtagsActivity
import com.enveu.activities.follow_follower.ProfileReportActivity
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.new_search.NewSearchFragment
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.baseModels.BaseFragment
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.CreatorProfileListener
import com.enveu.databinding.FragmentUgcBinding
import com.enveu.databinding.FragmentsCommentsDailogBinding
import com.enveu.databinding.ItemReportReasonLayoutBinding
import com.enveu.listener.PlayerPlayPauseCallBack
import com.enveu.networking.response.ReelCreatorId
import com.enveu.networking.response.ReelsContentItem
import com.enveu.networking.response.ShortsCommentItem
import com.enveu.networking.response.ShortsCreator
import com.enveu.utils.Constants
import com.enveu.utils.PaginationScrollListener
import com.enveu.utils.ReelsDataHolder
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.setTabLayoutBackground
import com.enveu.utils.show
import com.enveu.view_model.ShortsViewModel
import com.google.android.material.progressindicator.CircularProgressIndicator
import com.google.android.material.tabs.TabLayout
import com.google.gson.JsonArray
import com.google.gson.JsonObject
import java.util.Locale


class UgcFragment : BaseFragment() {
    private var _binding: FragmentUgcBinding? = null
    private val binding get() = _binding!!
    private var previousPlayer: BrightcoveExoPlayerVideoView? = null
    private var videoAdapter: VideoAdapter? = null
    private var shortsViewModel: ShortsViewModel? = null
    private var commentText: EditText? = null
    private var totalComment: TextView? = null
    private var progressBar: CircularProgressIndicator? = null
    private var commentRecyclerView: RecyclerView? = null
    private var replyingLayout: ConstraintLayout? = null
    private var replyingText: TextView? = null
    private var noDataFound: LinearLayout? = null
    private var replyCrossImage: ImageView? = null
    private var preference:KsPreferenceKeys? = null
    private var isLoggedIn:Boolean = false
    private var shortsCommentAdapter:ShortsCommentAdapter?= null
    private var shortsCommentList:ArrayList<ShortsCommentItem?>? = ArrayList()
    private var shortsVideoList:ArrayList<ReelsContentItem?>? = ArrayList()
    private var tempShortsVideoList:ArrayList<ReelsContentItem?>? = ArrayList()
    private var reelsContentItem:ReelsContentItem? = null
    private var shortsContentId:Int? = null
    private var reelPosition:Int? = null
    private var totalCommentsPages:Int? = 0
    private var pageNumber:Int = 0
    private var pageSize:Int = 10
    private var hasCommentData:Boolean = false
    private var position:Int?=0
    private var commentDialog:BottomSheetDialog? = null
    private var filterType:String = Constants.FOR_YOU
    private var totalPages:Int = 0
    private var reelsPageNumber:Int = 0
    private var reelsPageSize:Int = 10
    private var scrollToPosition:Int = 0
    private var shouldScrollToPosition:Boolean = false
    private var navigationPage:String = Constants.UGC_SECTION
    private var recyclerView:RecyclerView? = null
    private var linearLayoutManager:LinearLayoutManager? = null
    private var updatePosition:Int = -1


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?, ): View {
        _binding = FragmentUgcBinding.inflate(inflater, container, false)
        preference = KsPreferenceKeys.getInstance()
        isLoggedIn = preference?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)
        shortsViewModel = ViewModelProvider(this@UgcFragment)[ShortsViewModel::class.java]
        // getForYouFollowingReels(filterType, false)
        binding.viewpager.orientation = ViewPager2.ORIENTATION_VERTICAL
        binding.loginBtnUgc.setOnClickListener {
            ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin::class.java, "ugc")
        }

        val cacheLoggerHandler = Handler(Looper.getMainLooper())
        val cacheLoggerRunnable = object : Runnable {
            override fun run() {
                cacheLoggerHandler.postDelayed(this, 2000)
            }
        }
        cacheLoggerHandler.post(cacheLoggerRunnable)
        return binding.root
    }

    private fun getForYouFollowingReels(filterType: String) {
        shortsViewModel?.getPlaylistDetails(filterType, reelsPageNumber, reelsPageSize)?.observe(viewLifecycleOwner) { data ->
                binding.progressBar.hide()
                binding.viewpager.show()
                if (data.data?.items?.isNotEmpty() == true) {
                    totalPages = data.data.totalPages?:0
                    reelsPageNumber += 1
                    tempShortsVideoList = data.data.items
                    checkLoggedInUserInteraction()
                    startPlayer()
                }
            }
    }

    private fun callForUserFollowAndLikeContent(tempShortsVideoList: ArrayList<ReelsContentItem?>?) {
        val contentIds = StringBuilder()
        tempShortsVideoList?.forEach {
            it?.id?.let { id->
                contentIds.append(id).append(",")
            }
        }
        shortsViewModel?.checkAndCallFollow(contentIds.removeSuffix(",").toString())
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (arguments?.getSerializable(Constants.SHORTS_REELS_BUNDLE) != null) {
            reelsContentItem = arguments?.getSerializable(Constants.SHORTS_REELS_BUNDLE) as ReelsContentItem
            getShortsDataUsingIntent()
        }
        else{
            binding.progressBar.show()
            binding.progressBar.bringToFront()
            getForYouFollowingReels(Constants.FOR_YOU)
        }
        tabLayoutFirstPositionSelected()
        binding.tabLayoutView.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            @SuppressLint("UseCompatLoadingForDrawables", "ResourceType")
            override fun onTabSelected(tab: TabLayout.Tab) {
                if (binding.tabLayoutView.selectedTabPosition == 0) {
                    tabLayoutFirstPositionSelected()
                    playerPause()
                    shortsVideoList?.clear()
                    videoAdapter?.notifyDataSetChanged()
                    binding.notLoginForYou.hide()
                    binding.viewpager.show()
                    filterType = Constants.FOR_YOU
                    reelsPageNumber = 0
                    getForYouFollowingReels(Constants.FOR_YOU)
                    callReelsPagination(linearLayoutManager, recyclerView)
                } else {
                    tabLayoutSecondPositionSelected()
                    playerPause()
                    shortsVideoList?.clear()
                    videoAdapter?.notifyDataSetChanged()
                    if (isLoggedIn) {
                        binding.notLoginForYou.hide()
                        binding.viewpager.hide()
                        filterType = Constants.FOLLOWING
                        reelsPageNumber = 0
                        getForYouFollowingReels(Constants.FOLLOWING)
                        callReelsPagination(linearLayoutManager, recyclerView)
                    }
                    else{
                        stopBrightcovePlayer()
                        binding.notLoginForYou.show()
                        binding.progressBar.hide()
                        binding.viewpager.hide()
                    }
                }
                position = 0
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {}
            override fun onTabReselected(tab: TabLayout.Tab) {}
        })

        binding.tabLayoutView.tabGravity = TabLayout.GRAVITY_FILL
        binding.tabLayoutView.tabRippleColor = null

        binding.searchIcon.setOnClickListener {
            stopBrightcovePlayer()
            val newSearchFragment = NewSearchFragment()
            newSearchFragment.getPlayerListener(playerPlayPauseCallBack)
            val bundle = Bundle()
            bundle.putBoolean(Constants.VIA_SEARCH_UGC, true)
            newSearchFragment.arguments = bundle
            requireActivity().supportFragmentManager.beginTransaction().add(R.id.content_frame, newSearchFragment, Constants.NEW_SEARCH_FRAGMENT).addToBackStack(null).commit()
        }
        setObserver()
    }

    private val playerPlayPauseCallBack = object :PlayerPlayPauseCallBack {
        override fun onPlayPauseListener(isPlaying: Boolean) {
//            videoAdapter?.notifyDataSetChanged()
        }

        override fun onPlayPauseAtPosition(isPlaying: Boolean) {
            if (isPlaying) {
                position = binding.viewpager.currentItem
                playVideoAtPosition(position!!)
                videoAdapter?.notifyDataSetChanged()
            }
        }
    }

    private fun setObserver() {
        shortsViewModel?.userInteractionOfContents?.observe(viewLifecycleOwner){contents->
            contents?.forEach { content->
                tempShortsVideoList?.find { it?.id == content.contentId }?.apply {
                    isFollowing = content.follow
                    isAlreadyLike = content.liked
                    isFavorite = content.favourite
                    if (content.liked && likeCount == 0) {
                        likeCount = likeCount?.plus(1)
                    }
                }
            }
            tempShortsVideoList?.let { shortsVideoList?.addAll(it) }
            setAdapter()
        }
    }

    private val likeDislikeShareListener: VideoAdapter.ReelActionListener = object : VideoAdapter.ReelActionListener {
        @SuppressLint("NotifyDataSetChanged")

        override fun onReelAction(itemsItem: ReelsContentItem?, actionType: String) {
            when (actionType) {
                Constants.COMMENT -> {
                    if (isLoggedIn) {
                        if (shortsContentId != itemsItem?.id) {
                            clearBottomSheetDialog()
                            shortsContentId = itemsItem?.id
                            pageNumber = 0
                            reelPosition = itemsItem?.position
                            openCommentDialog()

                            getReelsCommentList(shortsContentId, true)
                        } else {
                            commentDialog?.show()
                            shortsCommentAdapter?.notifyDataSetChanged()
                        }
                    }
                    else{
                        ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin::class.java, "")
                    }
                }
                Constants.PROFILE -> {
                    onlyPlayerPause()
                    if (isLoggedIn) {
                        updatePosition = itemsItem?.position?:-1
                        Intent(requireActivity(), FollowFollowingProfileActivity::class.java).also {
                            it.putExtra(Constants.CREATOR_CONTENT_BUNDLE, itemsItem?.customData?.reelCreatorId)
                            it.putExtra(Constants.CONTENT_ID, itemsItem?.id)
                            it.putExtra(Constants.CREATOR_NAME, itemsItem?.customData?.reelCreatorId?.title)
                            if (itemsItem?.customData?.reelCreatorId?.images?.isNotEmpty() == true){
                                it.putExtra(Constants.IMAGE_URL, itemsItem?.customData?.reelCreatorId?.images?.get(0)?.src)
                            }
                            it.putExtra(Constants.FROM_UGC_FRAGMENT, true)
                            startActivityForResult(it, Constants.REQUEST_CODE)
                        }
                    }
                    else{
                        ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin::class.java, "")
                    }
                }

                Constants.HASHTAGS -> {
                    onlyPlayerPause()
                    if (isLoggedIn) {
                        val hashtag = itemsItem?.hashtagClicked?:""
                        Intent(requireActivity(), HashtagsActivity::class.java).also {
                            it.putExtra(Constants.HASHTAGS, hashtag)
                            startActivityForResult(it, Constants.REQUEST_CODE)
                        }
                    }
                    else{
                        ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin::class.java, "")
                    }
                }

                Constants.MORE -> {
                    if (isLoggedIn) {
                        openReportBottomSheet(itemsItem)
                    }
                    else{
                        ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin::class.java, "")
                    }
                }

                Constants.LIKE -> {
                    if (!isLoggedIn) {
                        ActivityLauncher.getInstance().loginActivity(requireActivity(),ActivityLogin::class.java,"")
                    }else{
                        if (itemsItem?.isAlreadyLike == true) {
                            setUnLikeReels(itemsItem)
                        } else {
                            setLikeReels(itemsItem)
                        }
                    }
                }

                Constants.FOLLOWING-> {
                    if (isLoggedIn){
                        itemsItem?.let { item->
                            if (itemsItem.isFollowing){
                                callFollowUnFollowApi(item, true)
                            }else{
                                callFollowUnFollowApi(item, false)
                            }
                        }
                    }
                    else{
                        ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin::class.java, "")
                    }
                }

                Constants.FAVORITE-> {
                    if (!isLoggedIn) {
                        ActivityLauncher.getInstance().loginActivity(requireActivity(),ActivityLogin::class.java,"")
                    }else{
                        if (itemsItem?.isFavorite == true) {
                            removeFavoriteReels(itemsItem)
                        } else {
                            setFavoriteReels(itemsItem)
                        }
                    }
                }

                Constants.SHARE -> {
                    if(isLoggedIn) {
                        // Handle share action
                        var videoType = ""
                        if (itemsItem?.contentType.equals(Constants.VIDEO, ignoreCase = true)) {
                            videoType = itemsItem?.video?.videoType ?: ""
                        }
                        val enveuVideoItemBean = EnveuVideoItemBean().apply {
                            posterURL = itemsItem?.images?.get(0)?.src
                            title = itemsItem?.title
                            assetType = itemsItem?.video?.videoType
                            description = itemsItem?.description
                            contentSlug = itemsItem?.contentSlug
                        }

                        AppCommonMethod.openShareFirebaseDynamicLinks(requireActivity(),enveuVideoItemBean)
                        val jsonObject = JsonObject()
                        jsonObject.addProperty("contentId", itemsItem?.id)
                        shortsViewModel?.reelsShareApi(jsonObject)
                    }
                    else{
                        ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin::class.java, "")
                    }
                }
            }
        }
    }

    private fun callFollowUnFollowApi(itemsItem: ReelsContentItem, isFollowing: Boolean) {
        val authToken = KsPreferenceKeys.getInstance()?.appPrefAccessToken?.toString()?:""
        itemsItem?.customData?.reelCreatorId?.externalIdentifier?.toInt()?.let {
            if (isFollowing){
                shortsViewModel?.setFollowUser(authToken, it)
            }else{
            shortsViewModel?.setUnFollowUser(authToken, it)
            }
        }
    }

    private fun playerPause() {
      //  previousPlayer?.pause()
        previousPlayer?.videoDisplay?.playback?.destroyPlayer()
        binding.viewpager.hide()
        binding.progressBar.hide()
    }

    private fun onlyPlayerPause(){
        previousPlayer?.pause()
    }


    private fun stopBrightcovePlayer() {
            val videoDisplayComponent = previousPlayer?.videoDisplay
            if (videoDisplayComponent is ExoPlayerVideoDisplayComponent) {
                val exoPlayer = videoDisplayComponent.exoPlayer
                exoPlayer?.stop()
            }
    }

    private fun startPlayer(){
        binding.viewpager.show()
        val videoDisplayComponent = previousPlayer?.videoDisplay
        if (videoDisplayComponent is ExoPlayerVideoDisplayComponent){
            val exoPlayer = videoDisplayComponent.exoPlayer
            exoPlayer?.play()
        }
    }

    override fun onResume() {
        super.onResume()
        startPlayer()
        if (activity != null && activity is HomeActivity) {
            (activity as HomeActivity).hideMainToolbar()
        }
    }

    private fun  setLikeReels(itemsItem: ReelsContentItem?){
         shortsViewModel?.setReelsLike(preference?.appPrefAccessToken!!, itemsItem?.id)?.observe(this){ addLikeResponse ->
             if (addLikeResponse.responseCode == 2000){

             }
         }

        val attributeObject = JsonObject().apply {
            addProperty("contentId", itemsItem?.id)
            addProperty("title", itemsItem?.title)
            addProperty("mediaType", itemsItem?.mediaType)
        }
        val eventObject = JsonObject().apply {
            addProperty("name", "LIKE CONTENT")
            addProperty("identifier", "LIKE_CONTENT")
            add("attribute", attributeObject)
        }
        val additionalInfoObject = JsonObject().apply {
            addProperty("customerId", itemsItem?.customData?.reelCreatorId?.externalIdentifier)
        }
        val customerObject = JsonObject().apply {
            add("additionalInformation", additionalInfoObject)
        }
        val finalJson = JsonObject().apply {
            add("event", eventObject)
            add("customer", customerObject)
        }

        shortsViewModel?.setEventTracking(finalJson, preference?.appPrefAccessToken)
    }

    private fun setUnLikeReels(itemsItem: ReelsContentItem?){
         shortsViewModel?.setReelsUnLike(preference?.appPrefAccessToken!!, itemsItem?.id)?.observe(this){ removeLikeResponse ->
            if (removeLikeResponse.responseCode == 2000){ }
         }
    }

    private fun setFavoriteReels(itemsItem: ReelsContentItem?){
        val jsonObject = JsonObject()
        jsonObject.addProperty("addToTop", true)
        val jsonArray = JsonArray()
        jsonArray.add(itemsItem?.id)
        jsonObject.add("mediaContentIds", jsonArray)
        jsonObject.addProperty("type", Constants.FAVORITE)
        shortsViewModel?.setLikeReels(preference?.appPrefAccessToken!!, jsonObject)?.observe(this){ addLikeResponse ->
            if (addLikeResponse.responseCode == 2000){}
        }
    }

    private fun removeFavoriteReels(itemsItem: ReelsContentItem?){
        val jsonObject = JsonObject()
        jsonObject.addProperty("playlistIds", "")
        val jsonArray = JsonArray()
        jsonArray.add(itemsItem?.id)
        jsonObject.add("mediaContentIds", jsonArray)
        jsonObject.addProperty("type", Constants.FAVORITE)
        shortsViewModel?.setUnLikeReels(preference?.appPrefAccessToken!!, jsonObject)?.observe(this){ removeLikeResponse ->
            if (removeLikeResponse.responseCode == 2000){ }
        }
    }

    @SuppressLint("Range")
    private fun openReportBottomSheet(itemsItem: ReelsContentItem?) {
        val dialog = BottomSheetDialog(requireActivity())
        val binding = ItemReportReasonLayoutBinding.inflate(layoutInflater)
        dialog.setContentView(binding.root)
        binding.dialogContainer.alpha = 200F

        val username = itemsItem?.customData?.reelCreatorId?.title
        if (username.isNullOrEmpty()) {
            binding.titleText.hide()
            binding.viewLine.hide()
        } else {
            binding.titleText.show()
            binding.viewLine.show()
            binding.titleText.text = username
        }

        binding.reportText.text = getString(R.string.report_content)
        binding.cancelText.setOnClickListener {
            dialog.dismiss()
        }
        binding.reportText.setOnClickListener {
            Intent(requireActivity(), ProfileReportActivity::class.java).also {
                it.putExtra(Constants.ENTITY_TYPE, Constants.CONTENT)
                it.putExtra(Constants.ENTITY_ID, itemsItem?.id.toString())
                startActivity(it)
            }
            dialog.dismiss()
        }
        dialog.show()
    }

    private fun openCommentDialog() {
        commentDialog = BottomSheetDialog(requireActivity())
        val binding = FragmentsCommentsDailogBinding.inflate(layoutInflater)
        commentDialog?.setContentView(binding.root)
        commentRecyclerView = commentDialog?.findViewById(R.id.commentRecyclerView)
        replyingLayout = commentDialog?.findViewById(R.id.replyingView)
        replyingText = commentDialog?.findViewById(R.id.replyingText)
        noDataFound = commentDialog?.findViewById(R.id.no_data_found)
        val crossBtu = commentDialog?.findViewById<ImageView>(R.id.crossBtu)
        replyCrossImage = commentDialog?.findViewById(R.id.replyingCrossImage)
        val sendCommentBtn = commentDialog?.findViewById<ImageView>(R.id.sendCommentBtn)
        commentText = commentDialog?.findViewById(R.id.addCommentET)
        totalComment = commentDialog?.findViewById(R.id.commentsTitle)
        progressBar = commentDialog?.findViewById(R.id.progressBar)
        commentDialog?.window?.setBackgroundDrawableResource(R.color.transparent)
        val bottomSheet = commentDialog?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.background = ColorDrawable(Color.TRANSPARENT)

        sendCommentBtn?.setOnClickListener {
            if (commentText?.text.toString().trim().isNotEmpty()){
                addShortsComment(commentText?.text.toString())
            }
        }
        commentDialog?.show()
        commentDialog?.setOnDismissListener {

        }

        crossBtu?.setOnClickListener {
            commentDialog?.dismiss()
        }
    }

    private fun clearBottomSheetDialog(){
        shortsCommentList?.clear()
        shortsCommentAdapter = null
        commentDialog = null
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setAdapter() {
        binding.viewpager.post {
            if (videoAdapter  == null) {
                 recyclerView = binding.viewpager.getChildAt(0) as RecyclerView
                linearLayoutManager = recyclerView?.layoutManager as LinearLayoutManager
                videoAdapter = VideoAdapter(shortsVideoList, recyclerView!!, likeDislikeShareListener)
                binding.viewpager.adapter = videoAdapter
                callReelsPagination(linearLayoutManager, recyclerView)
                binding.viewpager.viewTreeObserver.addOnGlobalLayoutListener {
                    playVideoAtPosition(binding.viewpager.currentItem)
                }
                binding.viewpager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                    override fun onPageSelected(position: Int) {
                        super.onPageSelected(position)
                        playVideoAtPosition(position)
                        <EMAIL> = position
                    }
                })
            }
            else{
                shortsVideoList?.let { allShortsData ->
                    synchronized(allShortsData) {
                        videoAdapter?.notifyItemChanged(allShortsData.size.minus(1))
                    }
                }
            }
            if (shouldScrollToPosition) {
                shouldScrollToPosition = false
                binding.viewpager.setCurrentItem(scrollToPosition, false)
                binding.viewpager.show()
            }
        }
    }


    private fun callReelsPagination(layoutManager: LinearLayoutManager?, recyclerView: RecyclerView?) {
        if (layoutManager != null) {
            recyclerView?.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
                override fun onLoadMore(page: Int) {
                    if (totalPages > page) {
                        if (navigationPage == Constants.UGC_SECTION) {
                            getForYouFollowingReels(filterType)
                        }
                        else if (navigationPage == Constants.CREATOR_SECTION) {
                            if (reelsContentItem?.reelsNavigationInfo?.shouldExtraApiCall == true){
                                getCreatorShorts()
                            }
                        }
                    }
                }
            })
        }
    }

    private fun getCreatorShorts(){
        shortsViewModel?.getCreatorShorts(reelsContentItem?.reelsNavigationInfo?.reelHonorId, reelsPageNumber, reelsPageSize)?.observe(viewLifecycleOwner){ reelShortsData ->
            if (reelShortsData.data?.items?.isNotEmpty() == true){
                totalPages = reelShortsData.data.totalPages?:0
                reelsPageNumber += 1
                tempShortsVideoList = reelShortsData.data.items
                checkLoggedInUserInteraction()
            }
        }
    }

    private fun playVideoAtPosition(position: Int) {
            val playerView = videoAdapter?.getVideoViewAt(position)
            if (playerView != null) {
                previousPlayer = playerView
                previousPlayer?.pause()
                playerView.start()
            } else {
                binding.viewpager.postDelayed({ playVideoAtPosition(position) }, 100)
            }

    }


    fun finishPlayer() {
        previousPlayer?.pause()
        previousPlayer?.videoDisplay?.playback?.destroyPlayer()
        previousPlayer = null
         binding.viewpager.adapter = null
         binding.viewpager.hide()
         binding.progressBar.hide()
         shortsVideoList?.clear()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        finishPlayer()
    }

    override fun onPause() {
        super.onPause()
       // playerPause()
        previousPlayer?.pause()
    }

    private fun tabLayoutFirstPositionSelected() {
        val currentLanguage = Locale.getDefault().language
        val isArabic = currentLanguage.equals(AppConstants.LANGUAGE_ARABIC, true)
        if (isArabic){
           setTabLayoutBackground(
               binding.tabLayoutView,
               R.drawable.tab_right_selected,
               R.drawable.tab_item_unselected
           )
        }
        else {
            setTabLayoutBackground(
                binding.tabLayoutView,
                R.drawable.tab_left_selected,
                R.drawable.tab_item_unselected
            )
        }
    }

    private fun tabLayoutSecondPositionSelected() {
        val currentLanguage = Locale.getDefault().language
        val isArabic = currentLanguage.equals(AppConstants.LANGUAGE_ARABIC,true)

        if (isArabic){
         setTabLayoutBackground(
             binding.tabLayoutView,
             R.drawable.tab_item_unselected,
             R.drawable.tab_left_selected
         )
        }
        else {
            setTabLayoutBackground(
                binding.tabLayoutView,
                R.drawable.tab_item_unselected,
                R.drawable.tab_right_selected
            )
        }
    }

    @SuppressLint("StringFormatMatches")
    private fun getReelsCommentList(contentId:Int?, showProgressBar:Boolean){
        if (showProgressBar) {
            progressBar?.show()
        }
         shortsViewModel?.getShortsCommentList(contentId, pageNumber, pageSize)?.observe(viewLifecycleOwner){ reelsCommentResponse ->
            progressBar?.hide()
            if (!reelsCommentResponse.data?.items.isNullOrEmpty()){
                totalCommentsPages = reelsCommentResponse.data?.totalPages
                reelsCommentResponse.data?.items?.let { shortsCommentList?.addAll(it) }
                totalComment?.text = getString(R.string.comments_bottom_text, shortsCommentList?.size)
                setShortsCommentAdapter(false)
                pageNumber += 1
                hasCommentData = true
            }
            else{
                if (!hasCommentData) {
                    totalComment?.text = getString(R.string.comments_bottom_text, 0)
                    noDataFound?.show()
                }
            }

        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setShortsCommentAdapter(isAddComment: Boolean) {
       if (shortsCommentAdapter == null){
           shortsCommentList = shortsCommentList?.reversed()?.let { ArrayList(it) }
           shortsCommentAdapter = ShortsCommentAdapter(shortsCommentList, creatorProfileListener)
           val layoutManager = LinearLayoutManager(requireActivity(), LinearLayoutManager.VERTICAL, false)
           commentRecyclerView?.layoutManager = layoutManager
           commentRecyclerView?.adapter = shortsCommentAdapter
           callCommentPagination(layoutManager)
       }
        else{
           shortsCommentList?.let { commentList ->
               synchronized(commentList) {
                   if (isAddComment) {
                       shortsCommentAdapter?.notifyItemInserted(0)
                   }
                   else{
                       shortsCommentAdapter?.notifyDataSetChanged()
                   }
               }
           }
        }
    }

    private val creatorProfileListener = object :CreatorProfileListener {
        override fun onCreatorProfileListener(shortsCreator: ShortsCreator?) {
            commentDialog?.dismiss()
            onlyPlayerPause()
            openCreatorProfilePage(shortsCreator)
        }
    }

    private fun callCommentPagination(layoutManager: LinearLayoutManager) {
        commentRecyclerView?.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun onLoadMore(page: Int) {
                if ((totalCommentsPages?:0) > page) {
                    getReelsCommentList(shortsContentId, false)
                }
            }
        })
    }


    @SuppressLint("StringFormatMatches", "NotifyDataSetChanged")
    private fun addShortsComment(commentText:String?){
          progressBar?.show()
          val jsonObject = JsonObject()
          jsonObject.addProperty(COMMENT,commentText )
          jsonObject.addProperty(CONTENT_ID, shortsContentId)
          shortsViewModel?.addShortsCommentApi(preference?.appPrefAccessToken.toString(), jsonObject)?.observe(viewLifecycleOwner){ addShortsComment ->
            if (addShortsComment.data != null){
                this.commentText?.setText("")
                progressBar?.hide()
                noDataFound?.hide()
                commentRecyclerView?.show()
                val shortsCommentItem = ShortsCommentItem()
                shortsCommentItem.commentId = addShortsComment.data.id
                shortsCommentItem.dateCreated = addShortsComment.data.dateCreated
                shortsCommentItem.comment = addShortsComment.data.comment
                shortsCommentItem.contentId = addShortsComment.data.contentId
                shortsCommentItem.lastUpdated = addShortsComment.data.lastUpdated
                val creator = ShortsCreator()
                creator.userName = preference?.isActiveUserProfileData()?.userName
                creator.id = preference?.isActiveUserProfileData()?.id.toString()
                creator.name = preference?.isActiveUserProfileData()?.name
                creator.profilePicURL = preference?.isActiveUserProfileData()?.profilePicURL.toString()
                shortsCommentItem.creator = creator
                shortsCommentList?.add(0, shortsCommentItem)
                totalComment?.text = getString(R.string.comments_bottom_text, shortsCommentList?.size)
                setShortsCommentAdapter(true)
                shortsVideoList?.getOrNull(reelPosition?:0)?.let {
                    it.commentCount = (it.commentCount?:0) + 1
                    videoAdapter?.notifyItemChanged(reelPosition?:0)
                }
            }
         }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == Constants.REQUEST_CODE) {
            if (data?.getBooleanExtra(Constants.OPEN_SHORTS_FRAGMENT, false) == true) {
                reelsContentItem = data.getSerializableExtra(Constants.SHORTS_REELS_BUNDLE) as ReelsContentItem?
                tempShortsVideoList?.clear()
                shortsVideoList?.clear()
                videoAdapter?.notifyDataSetChanged()
                getShortsDataUsingIntent()
            }
            else if (data?.hasExtra(Constants.UPDATE_FOLLOW) == true){
                val isFollow = data.getBooleanExtra(Constants.UPDATE_FOLLOW, false)
                videoAdapter?.updateFollowFollowing(updatePosition, isFollow)
            }
        }
    }

    private fun getShortsDataUsingIntent() {
        if (reelsContentItem?.reelsNavigationInfo != null){
            val reelsItemsList = ReelsDataHolder.allReelsListItems?.reelsItemsList
            if (reelsItemsList != null) {
                tempShortsVideoList?.addAll(reelsItemsList)
            }
            shouldScrollToPosition = true
            reelsPageNumber = reelsContentItem?.reelsNavigationInfo?.loadedPageNumber?:0
            totalPages = reelsContentItem?.reelsNavigationInfo?.totalReels?:0
            scrollToPosition = reelsContentItem?.reelsNavigationInfo?.reelsPosition?:0
            checkLoggedInUserInteraction()
            navigationPage = reelsContentItem?.reelsNavigationInfo?.navigatePage?:Constants.UGC_SECTION
            ReelsDataHolder.allReelsListItems?.reelsItemsList?.clear()
            ReelsDataHolder.allReelsListItems = null
            hideTabLayoutAndSearchIcon()
        }
        else {
            shortsVideoList?.add(reelsContentItem)
            setAdapter()
            getForYouFollowingReels(Constants.FOR_YOU)
        }
    }

    private fun checkLoggedInUserInteraction(){
        if (isLoggedIn){
            callForUserFollowAndLikeContent(tempShortsVideoList)
        }
        else{
            tempShortsVideoList?.let { shortsVideoList?.addAll(it) }
            setAdapter()
        }
    }

    private fun openCreatorProfilePage(shortsCreator: ShortsCreator?) {
        Intent(requireActivity(), FollowFollowingProfileActivity::class.java).also {
            val reelCreatorId = ReelCreatorId()
            reelCreatorId.id = shortsCreator?.creatorId?.toLong()
            reelCreatorId.externalIdentifier = shortsCreator?.id
            it.putExtra(Constants.CREATOR_CONTENT_BUNDLE, reelCreatorId)
            it.putExtra(Constants.CREATOR_NAME, shortsCreator?.name)
            it.putExtra(Constants.IMAGE_URL, shortsCreator?.profilePicURL)
            it.putExtra(Constants.FROM_UGC_FRAGMENT, true)
            startActivityForResult(it, Constants.REQUEST_CODE)
        }
    }

    private fun hideTabLayoutAndSearchIcon(){
        binding.tabLayoutView.hide()
        binding.searchIcon.hide()
        binding.viewpager.hide()
    }

    companion object {
        const val  COMMENT = "comment"
        const val  CONTENT_ID = "contentId"
    }
}