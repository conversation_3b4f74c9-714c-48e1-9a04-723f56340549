package com.enveu.ugc

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.Log
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.brightcove.player.edge.Catalog
import com.brightcove.player.edge.VideoListener
import com.brightcove.player.event.EventType
import com.brightcove.player.mediacontroller.BrightcoveMediaController
import com.brightcove.player.model.Video
import com.brightcove.player.view.BrightcoveExoPlayerVideoView
import com.brightcove.player.view.BrightcoveVideoView
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.databinding.ItemVideoBinding
import com.enveu.networking.response.ReelsContentItem
import com.enveu.utils.Constants
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.hide
import com.enveu.utils.likeCountFormat
import com.enveu.utils.setImageAnimation
import com.enveu.utils.show
import com.moengage.pushbase.internal.ACTION
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Locale
import java.util.regex.Pattern

class VideoAdapter(
    private val reelList: ArrayList<ReelsContentItem?>?,
    private val recyclerView: RecyclerView,
    private val listener: ReelActionListener,
) : RecyclerView.Adapter<VideoAdapter.VideoViewHolder>() {

    private val videoCache = mutableMapOf<String, Video?>()
    var isExpanded = false
    private var lastClickTime = 0L
    private val CLICK_DELAY = 3000L // 3 second


    val preference: KsPreferenceKeys = KsPreferenceKeys.getInstance()
    val isLoggedIn = preference.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)

    @SuppressLint("ClickableViewAccessibility")
    inner class VideoViewHolder(private val binding: ItemVideoBinding) :
        RecyclerView.ViewHolder(binding.root) {
        val brightcoveVideoView = binding.brightcoveVideoView
        private var gestureCoordinator: VideoGestureCoordinator? = null


        // set title from customData in userName
        @SuppressLint("SuspiciousIndentation")
        fun bind(videoId: String, reelList: List<ReelsContentItem?>?, holder: VideoViewHolder) {
            // Set lightweight UI updates
            // Delegate video loading to a separate function

             // Clean up previous gesture coordinator if exists
            gestureCoordinator?.cleanup()

            gestureCoordinator = VideoGestureCoordinator(
                context = holder.binding.root.context,
                brightcoveVideoView = holder.binding.brightcoveVideoView,
                likeImageView = holder.binding.likeIcon,
                doubleTapLikeView = holder.binding.doubleTapLike,
                playIconView = holder.binding.playIconShorts,
                onLikeChanged = { isLiked, likeCount ->
                    val reelItem = reelList?.get(bindingAdapterPosition)
                    reelItem?.isAlreadyLike = isLiked
                    reelItem?.likeCount = likeCount
                    holder.binding.likeCounts.text = reelItem?.likeCount?.let { likeCountFormat(it) } ?: "0"
                }
            )

            holder.binding.brightcoveVideoView.setOnTouchListener { _, event ->
                gestureCoordinator?.handleTouchEvent(event) ?: false
                true
            }


            val reelItemClicks = reelList?.get(bindingAdapterPosition)
               Log.d("checkPlayingItemsId", "$bindingAdapterPosition and ${reelItemClicks?.id}")
            gestureCoordinator?.setLikeState(
                liked = reelItemClicks?.isAlreadyLike ?: false,
                count = reelItemClicks?.likeCount ?: 0,
            )
//            doubleTapHandler.onSingleTapConfirmed(
//               e = MotionEvent.obtain(0, 0, MotionEvent.CLASSIFICATION_PINCH, 0f, 0f, 0)
//            )

            loadVideo(videoId)
            val reelItem = reelList?.get(bindingAdapterPosition)
            binding.username.text = reelItem?.customData?.reelCreatorId?.title ?: reelItem?.title
            binding.likeCounts.text = reelItem?.likeCount?.let { likeCountFormat(it) } ?: "0"
            binding.commentCounts.text = reelItem?.commentCount?.let { likeCountFormat(it) } ?: "0"
            binding.shareCounts.text = reelItem?.shareCount?.let { likeCountFormat(it) } ?: "0"

            val description = reelItem?.description.orEmpty()
            if (description.isNotEmpty()) {
                val spannableString = SpannableString(description)
                val hashtagPattern = Pattern.compile(Constants.HASHTAGS_REGEX)
                val matcher = hashtagPattern.matcher(description)

                while (matcher.find()) {
                    val start = matcher.start()
                    val end = matcher.end()
                    val hashtag = description.substring(start, end)

                    val clickableSpan = object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            reelItem?.hashtagClicked = hashtag
                            listener.onReelAction(reelItem, Constants.HASHTAGS)
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            super.updateDrawState(ds)
                            ds.isUnderlineText = false
                        }
                    }
                    spannableString.setSpan(clickableSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                }

                binding.hashtags.text = spannableString
                binding.hashtags.movementMethod = LinkMovementMethod.getInstance()
                binding.hashtags.highlightColor = Color.TRANSPARENT
                binding.hashtags.maxLines = 1
                binding.hashtags.ellipsize = TextUtils.TruncateAt.END

                binding.hashtags.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        binding.hashtags.viewTreeObserver.removeOnGlobalLayoutListener(this)
                        val layout = binding.hashtags.layout
                        if (layout != null && layout.lineCount > 1) {
                            binding.moreButton.visibility = View.VISIBLE
                            binding.moreButton.text = binding.followButton.context.getString(R.string.more)
                        } else {
                            binding.moreButton.visibility = View.GONE
                        }
                    }
                })
            } else {
                binding.hashtags.text = ""
                binding.moreButton.visibility = View.GONE
            }

            binding.moreButton.setOnClickListener {
                isExpanded = !isExpanded
                if (isExpanded) {
                    binding.hashtags.maxLines = Integer.MAX_VALUE
                    binding.hashtags.ellipsize = null
                    binding.moreButton.text = binding.followButton.context.getString(R.string.less)
                } else {
                    binding.hashtags.maxLines = 1
                    binding.hashtags.ellipsize = TextUtils.TruncateAt.END
                    binding.moreButton.text = binding.followButton.context.getString(R.string.more)
                }
            }

            val currentLanguage = Locale.getDefault().language
            val isArabic = currentLanguage.equals(AppConstants.LANGUAGE_ARABIC, true)
            if (isArabic) {
                binding.leftShadow.visibility = View.INVISIBLE
            }

            if (reelItem?.isAlreadyLike == true) {
                binding.likeIcon.setImageResource(R.drawable.ic_icon_like_fill)
            } else {
                binding.likeIcon.setImageResource(R.drawable.like_icon)
            }

            binding?.likeCounts?.text = reelItem?.likeCount.toString()

            if (reelItem?.isFavorite == true) {
                binding.saveIcon.setImageResource(R.drawable.saved_icon)
            } else {
                binding.saveIcon.setImageResource(R.drawable.save_icon)
            }
            if (reelItemClicks?.customData?.reelCreatorId == null) {
                binding.profileImage.setImageResource(R.drawable.app_icon_logo)
            } else {
                Glide.with(binding.profileImage.context).load(reelList[bindingAdapterPosition]?.customData?.reelCreatorId?.images?.firstOrNull()?.src)
                    .placeholder(R.drawable.profile_avtar_logo).error(R.drawable.profile_avtar_logo).into(binding.profileImage)
            }

            brightcoveVideoView.eventEmitter.on(EventType.PROGRESS) {
                val controller: BrightcoveMediaController? =
                    brightcoveVideoView.brightcoveMediaController
                controller?.hide()
            }

            binding.commentRoot.setOnClickListener {
                reelItem?.position = bindingAdapterPosition
                listener.onReelAction(reelItem, Constants.COMMENT)
            }

            binding?.constClick?.setOnClickListener {
                if (reelItem!=null && reelItem?.customData?.reelCreatorId?.id != null)
                    reelItem.position = bindingAdapterPosition
                listener.onReelAction(reelItem, Constants.PROFILE)
            }

            binding.moreIcon.setOnClickListener {
                listener.onReelAction(reelItem, Constants.MORE)
            }

            binding.likeIcon.setOnClickListener {
                listener.onReelAction(reelItem, Constants.LIKE)
                if (isLoggedIn) {
                    binding.doubleTapLike.setImageAnimation()
                    gestureCoordinator?.toggleLike()
                }
            }

            val currentUserId = KsPreferenceKeys.getInstance().appPrefUserId
            val creatorId = reelItem?.customData?.reelCreatorId?.externalIdentifier

            if (currentUserId == creatorId) {
                binding.followButton.visibility = View.GONE
                binding.moreRoot.visibility = View.GONE
            } else {
                binding.moreRoot.visibility = View.VISIBLE
                binding.followButton.visibility = View.VISIBLE

                if (reelItem?.isFollowing == true) {
                    binding.followButton.text = binding.followButton.context?.getString(R.string.following)
                    binding.followButton.isSelected = true
                } else {
                    binding.followButton.text = binding.followButton.context?.getString(R.string.follow)
                    binding.followButton.isSelected = false
                }
            }

            binding.followButton.setOnClickListener {
                if (isLoggedIn){
                    if (reelItem?.isFollowing == false) {
                        reelItem.isFollowing = true
                        binding.followButton.text = binding.followButton.context?.getString(R.string.following)
                        binding.followButton.isSelected = true
                    } else {
                        reelItem?.isFollowing = false
                        binding.followButton.text = binding.followButton.context?.getString(R.string.follow)
                        binding.followButton.isSelected = false
                    }
                }
                CoroutineScope(Dispatchers.IO).launch {
                    delay(200)
                    withContext(Dispatchers.Main) {
                        listener.onReelAction(itemsItem = reelItem, Constants.FOLLOWING)
                    }
                }
            }
            if(reelItem?.customData?.reelCreatorId?.id != null){
                binding.followButton.show()
            }
            else{
                binding.followButton.hide()
            }

            binding.shareRoot.setOnClickListener {
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastClickTime < CLICK_DELAY) {
                        // Ignore click
                        return@setOnClickListener
                    }
                    lastClickTime = currentTime
                    // Handle click
                listener.onReelAction(reelItem, Constants.SHARE)
            }

            binding.saveRoot.setOnClickListener {
                listener.onReelAction(reelItem, Constants.FAVORITE)
                if (isLoggedIn) {
                    CoroutineScope(Dispatchers.IO).launch {
                        delay(200)
                        withContext(Dispatchers.Main) {
                            if (reelItem?.isFavorite == false) {
                                reelItem?.isFavorite = true
                                binding.saveIcon.setImageResource(R.drawable.saved_icon)
                            } else {
                                reelItem?.isFavorite = false
                                binding.saveIcon.setImageResource(R.drawable.save_icon)
                            }
                        }
                    }
                }
            }
        }

        fun cleanup() {
            gestureCoordinator?.cleanup()
            gestureCoordinator = null
        }

        private fun loadVideo(videoId: String) {
            CoroutineScope(Dispatchers.IO).launch {
                val catalog = Catalog.Builder(brightcoveVideoView.eventEmitter, SDKConfig.BRIGHT_COVE_ACCOUNT_ID)
                    .setPolicy(SDKConfig.BRIGHT_COVE_POLICY_KEY)
                    .build()
                catalog.findVideoByID(videoId, object : VideoListener() {
                    override fun onVideo(video: Video?) {
                        video?.let {
                            videoCache[videoId] = it
                            CoroutineScope(Dispatchers.Main).launch {
                                brightcoveVideoView.add(it)
                                brightcoveVideoView.setMediaController(null as BrightcoveMediaController?)
                                brightcoveVideoView.eventEmitter.on(EventType.COMPLETED) {
                                    Log.d("VideoAdapter", "Video completed, restarting...")
                                    brightcoveVideoView.seekTo(0)
                                    brightcoveVideoView.start()
                                }
                            }
                        }
                    }

                    override fun onError(error: String) {
                        Log.e("Brightcove", "Video load error: $error")
                    }
                })
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VideoViewHolder {
        val binding = ItemVideoBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return VideoViewHolder(binding)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onBindViewHolder(holder: VideoViewHolder, position: Int) {
        // get the externalRefId from the contents and add it to the videos list
        val item = reelList?.get(position)
        val externalRefId = item?.externalRefId
        val videoId = externalRefId ?: ""
        holder.bind(videoId, reelList, holder)

    }

    override fun onViewRecycled(holder: VideoViewHolder) {
        super.onViewRecycled(holder)
        holder.cleanup() // Clean up gesture coordinator and handlers
        holder.brightcoveVideoView.clear()
    }

    fun getVideoViewAt(position: Int): BrightcoveExoPlayerVideoView? {
        val holder = recyclerView.findViewHolderForAdapterPosition(position) as? VideoViewHolder
        return holder?.brightcoveVideoView
    }

    override fun getItemCount(): Int = reelList?.size ?: 0

    interface ReelActionListener {
        fun onReelAction(itemsItem: ReelsContentItem?, actionType: String)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun clearData(){
        reelList?.clear()
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateFollowFollowing(position: Int?, isFollow:Boolean){
        if (position !=null && position != -1) {
            reelList?.get(position)?.isFollowing = isFollow
            notifyItemChanged(position)
        }
    }
}

/**
 * Handles all logic related to a single tap: playing/pausing the video
 * and showing/hiding the play/pause icon.
 * Optimized for smooth play/pause functionality with proper icon display.
 */
private class SingleTapHandler(
    private val videoView: BrightcoveVideoView,
    private val playIconView: ImageView
) {
    private val playDrawable = R.drawable.ic_ep_play
    private val pauseDrawable = R.drawable.ic_ep_pause
    private val TAG = "VideoPlayerDebug"
    private var iconHideHandler: Handler? = null
    private var iconHideRunnable: Runnable? = null
    private val ICON_DISPLAY_DURATION = 1500L // Show icon for 1.5 seconds

    init {
        playIconView.visibility = View.GONE
        iconHideHandler = Handler(Looper.getMainLooper())
    }

    fun performAction() {
        Log.d(TAG, "==> performAction called, current isPlaying: ${videoView.isPlaying}")

        // Cancel any pending hide operations
        cancelIconHide()

        if (videoView.isPlaying) {
            Log.d(TAG, "==> Pausing video and showing play icon")
            videoView.pause()
            showPlayIcon()
            // Don't auto-hide play icon when paused - keep it visible
        } else {
            Log.d(TAG, "==> Starting video and showing pause icon briefly")
            videoView.start()
            showPauseIconBriefly()
        }
    }

    fun syncPlayerState() {
        Log.d(TAG, "==> syncPlayerState called, isPlaying: ${videoView.isPlaying}")
        cancelIconHide()

        if (videoView.isPlaying) {
            // Video is playing, hide any visible icons
            hideIconImmediately()
        } else {
            // Video is paused, show play icon
            showPlayIcon()
        }
    }

    private fun showPlayIcon() {
        Log.d(TAG, "showPlayIcon: Displaying play icon")
        playIconView.setImageDrawable(ContextCompat.getDrawable(playIconView.context, playDrawable))

        if (playIconView.visibility != View.VISIBLE) {
            playIconView.alpha = 0f
            playIconView.visibility = View.VISIBLE
            playIconView.animate()
                .alpha(1f)
                .setDuration(200)
                .start()
        } else {
            playIconView.alpha = 1f
        }
    }

    private fun showPauseIconBriefly() {
        Log.d(TAG, "showPauseIconBriefly: Showing pause icon temporarily")
        playIconView.setImageDrawable(ContextCompat.getDrawable(playIconView.context, pauseDrawable))

        playIconView.alpha = 0f
        playIconView.visibility = View.VISIBLE
        playIconView.animate()
            .alpha(1f)
            .setDuration(200)
            .start()

        // Auto-hide pause icon after delay
        scheduleIconHide()
    }

    private fun hideIconImmediately() {
        Log.d(TAG, "hideIconImmediately: Hiding icon without animation")
        cancelIconHide()
        playIconView.visibility = View.GONE
    }

    private fun hideIconWithAnimation() {
        Log.d(TAG, "hideIconWithAnimation: Hiding icon with fade animation")
        playIconView.animate()
            .alpha(0f)
            .setDuration(200)
            .withEndAction {
                playIconView.visibility = View.GONE
            }
            .start()
    }

    private fun scheduleIconHide() {
        cancelIconHide()
        iconHideRunnable = Runnable {
            hideIconWithAnimation()
        }
        iconHideHandler?.postDelayed(iconHideRunnable!!, ICON_DISPLAY_DURATION)
    }

    private fun cancelIconHide() {
        iconHideRunnable?.let { runnable ->
            iconHideHandler?.removeCallbacks(runnable)
            iconHideRunnable = null
        }
    }

    fun cleanup() {
        cancelIconHide()
        iconHideHandler = null
    }
}


/**
 * Handles all logic related to liking: updating state, showing the heart
 * animation, and notifying the adapter of the change.
 */
private class DoubleTapHandler(
    context: Context,
    private val likeImageView: ImageView,
    private val doubleTapLikeView: View,
    private val onLikeChanged: (isLiked: Boolean, likeCount: Int) -> Unit
) {
    private var isLiked = false
    private var likeCount = 0
    private val likedDrawable = R.drawable.ic_icon_like_fill
    private val unlikedDrawable = R.drawable.like_icon

    init {
        likeImageView.setImageDrawable(ContextCompat.getDrawable(context, unlikedDrawable))
        doubleTapLikeView.visibility = View.GONE
    }

    // FIX: Always show the animation on double-tap for better UX.
    // Only change the like state if the item is not already liked.
    // This solves the "double-tap not working" issue.
    fun performDoubleTapAction() {
        showLikeAnimation() // Always show feedback
        if (!isLiked) {
            isLiked = true
            likeCount++
            updateLikeState()
            onLikeChanged(isLiked, likeCount)
        }
    }

    // Action performed when the like button is clicked
    fun toggleLike() {
        isLiked = !isLiked
        if (isLiked) {
            likeCount++
            showLikeAnimation()
        } else {
            likeCount = maxOf(0, likeCount - 1)
        }
        updateLikeState()
        onLikeChanged(isLiked, likeCount)
    }

    fun setInitialState(liked: Boolean, count: Int) {
        isLiked = liked
        likeCount = count
        updateLikeState()
    }

    private fun updateLikeState() {
        val drawable = if (isLiked) likedDrawable else unlikedDrawable
        likeImageView.setImageDrawable(ContextCompat.getDrawable(likeImageView.context, drawable))
    }

    private fun showLikeAnimation() {
        doubleTapLikeView.visibility = View.VISIBLE
        doubleTapLikeView.apply {
            scaleX = 0.7f
            scaleY = 0.7f
            animate()
                .scaleX(1.2f)
                .scaleY(1.2f)
                .setDuration(150)
                .withEndAction {
                    animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(100)
                        .start()
                }.start()
        }
        Handler(Looper.getMainLooper()).postDelayed({
            doubleTapLikeView.visibility = View.GONE
        }, 1000)
    }
}


/**
 * The main gesture listener. It coordinates and delegates actions to the handlers.
 * Optimized for better touch responsiveness and proper resource management.
 */
private class VideoGestureCoordinator(
    context: Context,
    brightcoveVideoView: BrightcoveVideoView,
    likeImageView: ImageView,
    doubleTapLikeView: View,
    playIconView: ImageView,
    onLikeChanged: (isLiked: Boolean, likeCount: Int) -> Unit
):GestureDetector.SimpleOnGestureListener()  {

    private val gestureDetector = GestureDetector(context, this)
    private val singleTapHandler = SingleTapHandler(brightcoveVideoView, playIconView)
    private val doubleTapHandler = DoubleTapHandler(context, likeImageView, doubleTapLikeView, onLikeChanged)

    init {
        // Sync player state on initialization
        singleTapHandler.syncPlayerState()
    }

    fun handleTouchEvent(event: MotionEvent): Boolean {
        return gestureDetector.onTouchEvent(event)
    }

    override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
        // This is called only when it's confirmed to be a single tap (not part of double tap)
        singleTapHandler.performAction()
        return true
    }

    override fun onDoubleTap(e: MotionEvent): Boolean {
        // This is called on double tap - like functionality
        doubleTapHandler.performDoubleTapAction()
        return true
    }

    fun toggleLike() {
        doubleTapHandler.toggleLike()
    }

    fun setLikeState(liked: Boolean, count: Int) {
        doubleTapHandler.setInitialState(liked, count)
        // Sync the player state when binding new data
        singleTapHandler.syncPlayerState()
    }

    fun cleanup() {
        singleTapHandler.cleanup()
    }
}