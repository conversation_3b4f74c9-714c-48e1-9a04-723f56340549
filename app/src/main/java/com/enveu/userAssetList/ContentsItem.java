package com.enveu.userAssetList;

import java.util.List;

public class ContentsItem {
    private Object vastTag;
    private Object transcodedFlavoursPlaylistLinks;
    private String description;
    private Object assetKeywords;
    private int likeCount;
    private String title;
    private Object transcodingJobId;
    private Object svod;
    private Object contentProvider;
    private Object duration;
    private Object assetCast;
    private String landscapeImage;
    private boolean premium;
    private Object price;
    private Object assetGenres;
    private Object season;
    private int id;
    private String portraitImage;
    private Object sku;
    private Object thumbnailURL;
    private Object tvod;
    private Object posterLandscapeImage;
    private Object episodeNo;
    private Object assetLink;
    private Object isNew;
    private Object transcodedMasterPlaylistLink;
    private String assetType;
    private int commentCount;
    private Object posterPortraitImage;
    private Object series;
    private Object plans;
    private String contentUniquenessType;
    private long publishedDate;
    private String status;
    private List<String> genres;

    public List<String> getGenres() {
        return genres;
    }

    public void setGenres(List<String> genres) {
        this.genres = genres;
    }

    public Object getVastTag() {
        return vastTag;
    }

    public void setVastTag(Object vastTag) {
        this.vastTag = vastTag;
    }

    public Object getTranscodedFlavoursPlaylistLinks() {
        return transcodedFlavoursPlaylistLinks;
    }

    public void setTranscodedFlavoursPlaylistLinks(Object transcodedFlavoursPlaylistLinks) {
        this.transcodedFlavoursPlaylistLinks = transcodedFlavoursPlaylistLinks;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Object getAssetKeywords() {
        return assetKeywords;
    }

    public void setAssetKeywords(Object assetKeywords) {
        this.assetKeywords = assetKeywords;
    }

    public int getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(int likeCount) {
        this.likeCount = likeCount;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Object getTranscodingJobId() {
        return transcodingJobId;
    }

    public void setTranscodingJobId(Object transcodingJobId) {
        this.transcodingJobId = transcodingJobId;
    }

    public Object getSvod() {
        return svod;
    }

    public void setSvod(Object svod) {
        this.svod = svod;
    }

    public Object getContentProvider() {
        return contentProvider;
    }

    public void setContentProvider(Object contentProvider) {
        this.contentProvider = contentProvider;
    }

    public Object getDuration() {
        return duration;
    }

    public void setDuration(Object duration) {
        this.duration = duration;
    }

    public Object getAssetCast() {
        return assetCast;
    }

    public void setAssetCast(Object assetCast) {
        this.assetCast = assetCast;
    }

    public String getLandscapeImage() {
        return landscapeImage;
    }

    public void setLandscapeImage(String landscapeImage) {
        this.landscapeImage = landscapeImage;
    }

    public boolean isPremium() {
        return premium;
    }

    public void setPremium(boolean premium) {
        this.premium = premium;
    }

    public Object getPrice() {
        return price;
    }

    public void setPrice(Object price) {
        this.price = price;
    }

    public Object getAssetGenres() {
        return assetGenres;
    }

    public void setAssetGenres(Object assetGenres) {
        this.assetGenres = assetGenres;
    }

    public Object getSeason() {
        return season;
    }

    public void setSeason(Object season) {
        this.season = season;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPortraitImage() {
        return portraitImage;
    }

    public void setPortraitImage(String portraitImage) {
        this.portraitImage = portraitImage;
    }

    public Object getSku() {
        return sku;
    }

    public void setSku(Object sku) {
        this.sku = sku;
    }

    public Object getThumbnailURL() {
        return thumbnailURL;
    }

    public void setThumbnailURL(Object thumbnailURL) {
        this.thumbnailURL = thumbnailURL;
    }

    public Object getTvod() {
        return tvod;
    }

    public void setTvod(Object tvod) {
        this.tvod = tvod;
    }

    public Object getPosterLandscapeImage() {
        return posterLandscapeImage;
    }

    public void setPosterLandscapeImage(Object posterLandscapeImage) {
        this.posterLandscapeImage = posterLandscapeImage;
    }

    public Object getEpisodeNo() {
        return episodeNo;
    }

    public void setEpisodeNo(Object episodeNo) {
        this.episodeNo = episodeNo;
    }

    public Object getAssetLink() {
        return assetLink;
    }

    public void setAssetLink(Object assetLink) {
        this.assetLink = assetLink;
    }

    public Object getIsNew() {
        return isNew;
    }

    public void setIsNew(Object isNew) {
        this.isNew = isNew;
    }

    public Object getTranscodedMasterPlaylistLink() {
        return transcodedMasterPlaylistLink;
    }

    public void setTranscodedMasterPlaylistLink(Object transcodedMasterPlaylistLink) {
        this.transcodedMasterPlaylistLink = transcodedMasterPlaylistLink;
    }

    public String getAssetType() {
        return assetType;
    }

    public void setAssetType(String assetType) {
        this.assetType = assetType;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public Object getPosterPortraitImage() {
        return posterPortraitImage;
    }

    public void setPosterPortraitImage(Object posterPortraitImage) {
        this.posterPortraitImage = posterPortraitImage;
    }

    public Object getSeries() {
        return series;
    }

    public void setSeries(Object series) {
        this.series = series;
    }

    public Object getPlans() {
        return plans;
    }

    public void setPlans(Object plans) {
        this.plans = plans;
    }

    public String getContentUniquenessType() {
        return contentUniquenessType;
    }

    public void setContentUniquenessType(String contentUniquenessType) {
        this.contentUniquenessType = contentUniquenessType;
    }

    public long getPublishedDate() {
        return publishedDate;
    }

    public void setPublishedDate(long publishedDate) {
        this.publishedDate = publishedDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return
                "ContentsItem{" +
                        "vastTag = '" + vastTag + '\'' +
                        ",transcodedFlavoursPlaylistLinks = '" + transcodedFlavoursPlaylistLinks + '\'' +
                        ",description = '" + description + '\'' +
                        ",assetKeywords = '" + assetKeywords + '\'' +
                        ",likeCount = '" + likeCount + '\'' +
                        ",title = '" + title + '\'' +
                        ",transcodingJobId = '" + transcodingJobId + '\'' +
                        ",svod = '" + svod + '\'' +
                        ",contentProvider = '" + contentProvider + '\'' +
                        ",duration = '" + duration + '\'' +
                        ",assetCast = '" + assetCast + '\'' +
                        ",landscapeImage = '" + landscapeImage + '\'' +
                        ",premium = '" + premium + '\'' +
                        ",price = '" + price + '\'' +
                        ",assetGenres = '" + assetGenres + '\'' +
                        ",season = '" + season + '\'' +
                        ",id = '" + id + '\'' +
                        ",portraitImage = '" + portraitImage + '\'' +
                        ",sku = '" + sku + '\'' +
                        ",thumbnailURL = '" + thumbnailURL + '\'' +
                        ",tvod = '" + tvod + '\'' +
                        ",posterLandscapeImage = '" + posterLandscapeImage + '\'' +
                        ",episodeNo = '" + episodeNo + '\'' +
                        ",assetLink = '" + assetLink + '\'' +
                        ",isNew = '" + isNew + '\'' +
                        ",transcodedMasterPlaylistLink = '" + transcodedMasterPlaylistLink + '\'' +
                        ",assetType = '" + assetType + '\'' +
                        ",commentCount = '" + commentCount + '\'' +
                        ",posterPortraitImage = '" + posterPortraitImage + '\'' +
                        ",series = '" + series + '\'' +
                        ",plans = '" + plans + '\'' +
                        ",contentUniquenessType = '" + contentUniquenessType + '\'' +
                        ",publishedDate = '" + publishedDate + '\'' +
                        ",status = '" + status + '\'' +
                        "}";
    }
}
