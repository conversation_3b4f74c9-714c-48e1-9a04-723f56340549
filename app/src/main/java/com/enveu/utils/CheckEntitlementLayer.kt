package com.enveu.utils

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import com.enveu.activities.layers.EntitlementLayer
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.callbacks.EntitlementListener
import com.enveu.fragments.dialog.CommonDialogFragment
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine


object CheckEntitlementLayer {
    fun checkEntitlement(
        token: String?,
        sku: String?,
        isUserVerified: String?,
        entitlementListener: EntitlementListener?,
        ) {
        entitlementListener?.showHideProgressBar(true)
        CoroutineScope(Dispatchers.Main).launch {
            val entitlementData = withContext(Dispatchers.IO) {
                EntitlementLayer.instance?.hitApiEntitlement(token, sku)
            }
            val entitleResponse = entitlementData?.awaitValue()
            if (entitleResponse != null && entitleResponse.data != null) {
                if (entitleResponse.data?.entitled == true) {
                    if (isUserVerified.equals("true", ignoreCase = true)) {
                        getPlayableVideoId(entitleResponse, entitlementListener)
                    } else {
                        entitlementListener?.showCommonDialog(Constants.USER_VERIFIED)
                    }
                } else {
                    entitlementListener?.showPurchaseDialog(entitleResponse)
                }
            } else {
                entitlementListener?.showCommonDialog(Constants.ENTITLE_DIALOG)
            }
        }
    }

    private fun getPlayableVideoId(entitleResponse: ResponseEntitle, entitlementListener: EntitlementListener?) {
        CoroutineScope(Dispatchers.Main).launch {
            val getPlayableData = withContext(Dispatchers.IO) {
                EntitlementLayer.instance?.hitAPIForREfID(
                    entitleResponse.data.accessToken,
                    entitleResponse.data?.sku
                )
            }
            val getPlayableResponse = getPlayableData?.awaitValue()
            if (getPlayableResponse?.data != null) {
                Log.d("responseDeferred", Gson().toJson(getPlayableResponse))
                entitlementListener?.showHideProgressBar(false)
                entitlementListener?.getPlayableIdListener(getPlayableResponse?.data?.externalUrl)
            } else {
                entitlementListener?.showCommonDialog(Constants.COMMON_DIALOG)
            }
        }
    }
}


suspend fun <T> LiveData<T>.awaitValue(): T? {
    return suspendCoroutine { cont ->
        val observer = object : Observer<T> {
            override fun onChanged(value: T) {
                cont.resume(value)
                removeObserver(this)
            }
        }
        observeForever(observer)
    }
}