package com.enveu.utils

object Constants {
    const val SQR = "SQR"
    const val PR1 = "PR1"
    const val CIR = "CIR"
    const val PR2 = "PR2"
    const val LDS3_2 = "LDS3_2"
    const val LDS = "LDS"
    const val LDS2_3 = "LDS2_3"
    const val SHORTS = "SHORTS"
    const val  SIXTEEN_INTO_NINE = "16x9"
    const val  TEN_INTO_TEN = "10x10"
    const val  TEN_INTO_TEN_ASTRISKS = "10*10"
    const val  NINE_INTO_SIXTEEN = "9x16"
    const val  ONE_INTO_ONE = "1x1"
    const val  CIRCLE_TYPE = "CIRCLE_TYPE"
    const val COMMENT = "comment"
    const val PROFILE = "profile"
    const val HASHTAGS = "hashtags"
    const val HASHTAGS_REGEX = "#[\\w]+"
    const val MORE = "MORE"
    const val SHARE = "SHARE"
    const val LIKE = "LIKE"
    const val FAVORITE = "FAVORITE"
    const val CONTENT = "CONTENT"
    const val ENTITY_ID = "ENTITY_ID"
    const val ENTITY_TYPE = "ENTITY_TYPE"
    const val CUSTOMER = "CUSTOMER"
    const val SHOULD_NAVIGATE_FOLLOWER = "SHOULD_NAVIGATE_FOLLOWER"
    const val TAB_INDEX = "TAB_INDEX"
    const val CREATOR_CONTENT_ID = "creatorContentId"
    const val CREATOR_CONTENT_BUNDLE = "creatorContentBundle"
    const val CREATOR_USER_ID = "creator_user_id"
    const val CREATOR_NAME = "creator_name"
    const val VIDEO = "Video"
    const val CONTENT_TYPE_VIDEO = "VIDEO"
    const val TIME_HEADER = "time_header"
    const val VIA_SEARCH_UGC = "VIA_SEARCH_UGC"
    const val BRIGHTCOVE_ACCOUNT_ID = "*************"
    const val BRIGHTCOVE_POLICY_KEY = "BCpkADawqM3FaZhbS7S-i2Uihgv1EDYv2qGa_iIt5RmGj3BO3X8E6EP5OWMBVAyeQhY5kBmw2QD8gyxjv1_dYZNAXnffr-uhMBLqR1avRLQ9QJTU_r2LYF65SJhNGCBPHuCbaVIHYdCV5Y_Mjr4V5WQOXPvI3bsetLISdg"
    const val FOR_YOU = "FOR_YOU"
    const val FOLLOWING = "FOLLOWING"
    const val OPEN_SHORTS_FRAGMENT = "OPEN_SHORTS_FRAGMENT"
    const val SHORTS_REELS_BUNDLE = "SHORTS_REELS_BUNDLE"
    const val REEL = "REEL"
    const val REQUEST_CODE = 1002
    const val PLAYER_URL = "PLAYER_URL"
    const val IMAGE_URL = "IMAGE_URL"
    const val GAME_ASSET_TYPE =  "ASSET_TYPE"
    const val GAME_CUSTOM_TYPE = "CUSTOM_TYPE"
    const val PLAY_OUT = "PLAYOUT"
    const val SINGLE_CONTENT_BUNDLE = "SINGLE_CONTENT_BUNDLE"
    const val ALL_EPISODES_LIST = "ALL_EPISODES_LIST"
    const val EPG_ITEM = "EPG_ITEM"
    const val OTHER = "OTHER"
    const val CONTENT_ID = "CONTENT_ID"
    const val NEW_SEARCH_FRAGMENT = "NEW_SEARCH_FRAGMENT"
    const val PLAY_OUT_CHANNEL = "PLAY_OUT_CHANNEL"
    const val CUSTOM_VALUE = "CUSTOM_VALUE"
    const val REEL_VALUE = "REEL"
    const val EPG = "EPG"
    const val CREATE = "CREATE"
    const val LINEAR_24_7 = "LINEAR_24_7"
    const val HIDE_WATCHLIST_ICON = "HIDE_WATCHLIST_ICON"
    const val CUSTOM = "CUSTOM"
    const val GAME = "GAME"
    const val WEB_VIEW_URL = "WEB_VIEW_URL"
    const val CONTENT_TITLE = "CONTENT_TITLE"
    const val CONTENT_DESCRIPTION = "CONTENT_DESCRIPTION"
    const val FROM_UGC_FRAGMENT = "FROM_UGC_FRAGMENT"
    const val FROM_MORE_FRAGMENT = "FROM_MORE_FRAGMENT"
    const val VIA_SEARCH_FRAGMENT = "VIA_SEARCH_FRAGMENT"
    const val COMPRESS_IMAGE_SIZE = 300
    const val COMPRESS_IMAGE_WIDTH = 1280
    const val COMPRESS_IMAGE_HEIGHT = 1280
    const val FOLLOWER_COUNT = "FOLLOWER_COUNT"
    const val FOLLOWING_COUNT = "FOLLOWING_COUNT"
    const val CREATOR_SECTION = "CREATOR_SECTION"
    const val FAVOURITE_SECTION = "FAVOURITE_SECTION"
    const val UGC_SECTION = "UGC_SECTION"
    const val EXTERNAL_REF_ID = "EXTERNAL_REF_ID"
    const val CREATOR_ID = "reel-creator-id:"
    const val SORT_BY = "DATE_CREATED"
    const val SORT_ORDER = "DESC"
    const val SHOULD_FOLLOW_FOLLOWING_PROFILE = "SHOULD_FOLLOW_FOLLOWING_PROFILE"


    const val ENTITLE_DIALOG = "ENTITLE_DIALOG"
    const val USER_VERIFIED = "USER_VERIFIED"
    const val COMMON_DIALOG = "COMMON_DIALOG"
    const val UPDATE_FOLLOW = "UPDATE_FOLLOW"

}