package com.enveu.utils;

public interface CustomeFields {
    String Name = "title_en";
    String Description = "description_en";
    String Genre = "genres_en";
    String Cast = "cast_en";
    String IsVip = "is_vip";
    String IsNew = "is_new";
    //will change it to parental_rating when data is ready
    String Rating = "rating";
    String Country = "country";
    String parentalRating = "parental_rating";
    String isComingSoon = "iscomingsoon";
    String rating = "parental_rating";
    String company="company";
    String year="year";
    String VastTag="vast_tag";
    String WIDEVINE_URL="widevine_content_url";
    String WIDEVINE_LICENCE="widevine_content_license_url";
    String ISLIVEDRM = "islivedrm";
    String ISSIGNEDLANGUAGE = "is_signed_language_enabled";
    String SIGNEDLANGUAGEPARENTREGRENCEID = "signed_language_parent_reference_id";
    String SIGNEDLANGUAGEREFRENCEID = "signed_language_reference_id";
    String IS_PODCAST = "is_podcast";
    String is4k = "is4k";
    String isClosedCaption = "is_close_caption";
    String isSoundTrack = "is_sound_track";
    String isAudioDescription = "is_audio_description";
    String seasonName = "season_name";
   // String trailerReferenceId = "trailer_reference_id";
    String trailerReferenceId = "trailer_ref";
    String Producer ="producer";
    String SKIP_INTRO_START ="skipintro_startTime";
    String SKIP_INTRO_END ="skipintro_endTime";
    String sponsors ="sponsors";
    String display_date ="display_date";
    String quality = "quality";
    String description_two = "description_two";
    String description_three = "description_three";
    String display_tags = "display_tags";
}
