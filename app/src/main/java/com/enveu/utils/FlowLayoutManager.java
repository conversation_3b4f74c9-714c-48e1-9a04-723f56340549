package com.enveu.utils;

import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

public class FlowLayoutManager extends RecyclerView.LayoutManager {

    @Override
    public RecyclerView.LayoutParams generateDefaultLayoutParams() {
        return new RecyclerView.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    @Override
    public boolean canScrollHorizontally() {
        return true;
    }

    @Override
    public boolean canScrollVertically() {
        return true;
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        detachAndScrapAttachedViews(recycler);

        int width = getWidth();
        int curLineWidth = 0;
        int curLineTop = 0;
        int curLineBottom = 0;

        for (int i = 0; i < getItemCount(); i++) {
            View view = recycler.getViewForPosition(i);
            addView(view);
            measureChildWithMargins(view, 0, 0);
            int childWidth = getDecoratedMeasuredWidth(view);
            int childHeight = getDecoratedMeasuredHeight(view);

            if (curLineWidth + childWidth <= width) {
                layoutDecorated(view, curLineWidth, curLineTop, curLineWidth + childWidth, curLineTop + childHeight);
                curLineWidth += childWidth;
                curLineBottom = Math.max(curLineBottom, curLineTop + childHeight);
            } else {
                curLineWidth = 0;
                curLineTop = curLineBottom;
                layoutDecorated(view, curLineWidth, curLineTop, curLineWidth + childWidth, curLineTop + childHeight);
                curLineWidth += childWidth;
                curLineBottom = curLineTop + childHeight;
            }
        }
    }
}

