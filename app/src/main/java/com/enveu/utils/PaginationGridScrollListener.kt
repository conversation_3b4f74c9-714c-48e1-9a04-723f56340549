package com.enveu.utils

import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView


abstract class PaginationGridScrollListener(private val layoutManager: GridLayoutManager, private var visibleThreshold: Int = 5) : RecyclerView.OnScrollListener() {

    private var loading = true
    private var previousTotalItemCount = 0
    private var currentPage = 0

    init {
        visibleThreshold *= layoutManager.spanCount
    }

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)
        val totalItemCount = layoutManager.itemCount
        val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()
        if (totalItemCount < previousTotalItemCount) {
            this.currentPage = 0
            this.previousTotalItemCount = totalItemCount
            if (totalItemCount == 0) {
                this.loading = true
            }
        }
        if (loading && totalItemCount > previousTotalItemCount) {
            loading = false
            previousTotalItemCount = totalItemCount
        }
        if (!loading && (lastVisibleItemPosition + visibleThreshold) >= totalItemCount) {
            currentPage++
            onLoadMore(currentPage, totalItemCount)
            loading = true
        }
    }
    abstract fun onLoadMore(page: Int, totalItemsCount: Int)

    fun resetState() {
        this.loading = true
        this.previousTotalItemCount = 0
        this.currentPage = 0
    }
}

