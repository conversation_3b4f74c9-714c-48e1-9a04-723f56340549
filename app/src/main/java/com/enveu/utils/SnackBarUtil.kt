package com.enveu.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.cardview.widget.CardView
import com.enveu.R
import com.google.android.material.snackbar.Snackbar

class SnackBarUtil {

    @SuppressLint("ResourceType")
    fun makeSnackBarAndShow(context: Activity, title:String, message:String){
        var view=context.findViewById<CardView>(R.id.sbl_root)

        val tvTitle=context.findViewById<TextView>(R.id.tv_sbl_title)
        val tvMessage=context.findViewById<TextView>(R.id.tv_sbl_msg)
        val ivSB=context.findViewById<TextView>(R.id.iv_sbl)

        tvTitle.text=title
        tvMessage.text=message

        Snackbar.make(context,view,"",Snackbar.LENGTH_LONG).show()
    }
}