package com.enveu.utils;

import android.content.Context;

import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.callbacks.apicallback.ApiResponseModel;
import com.enveu.layersV2.VideoDetailLayer;
import com.enveu.networking.errormodel.ApiErrorModel;
import com.enveu.utils.commonMethods.AppCommonMethod;

public class TrailerRedirection {

    private static TrailerRedirection trailerRedirection;
    public static TrailerRedirection getInstance(){
        if (trailerRedirection==null)
            trailerRedirection=new TrailerRedirection();

        return trailerRedirection;
    }

    public void getTrailerParentContent(String parentId, Context context){
        VideoDetailLayer.getInstance().getVideoDetails(parentId, new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {

            }

            @Override
            public void onSuccess(RailCommonData response) {
                AppCommonMethod.redirectionLogic(context, response, 0,"","","");
            }

            @Override
            public void onError(ApiErrorModel apiError) {

            }

            @Override
            public void onFailure(ApiErrorModel httpError) {

            }
        });
    }
}
