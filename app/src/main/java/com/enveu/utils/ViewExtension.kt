package com.enveu.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.text.Html
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.view.ViewCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.beanModelV3.videoDetailV3.list.ImagesItem
import com.enveu.networking.response.ItemsItem
import com.enveu.networking.response.ReelsContentItem
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.google.android.material.tabs.TabLayout
import de.hdodenhof.circleimageview.CircleImageView
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import java.util.concurrent.TimeUnit

fun TextView.htmlParseToString(htmlString:String){
    this.text = Html.fromHtml(htmlString, Html.FROM_HTML_MODE_COMPACT)
}

fun View.show() {
    this.visibility= View.VISIBLE
}
fun View.hide() {
    this.visibility= View.GONE
}

fun View.invisible() {
    this.visibility = View.INVISIBLE
}

fun showToast(context: Context, message: String) {
    if (message.isBlank()) return
    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
}

fun rotateImageLocaleWise(imageView: ImageView) {
    if (KsPreferenceKeys.getInstance().appLanguage != null) {
        if (KsPreferenceKeys.getInstance().appLanguage == AppConstants.ENGLISH_LAN_CODE) {
            imageView.rotation = 0f
        } else {
            imageView.rotation = 180f
        }
    }
}
fun isValidContextForGlide(context: Context?): Boolean {
    if (context == null) {
        return false
    }
    if (context is Activity) {
        if (context.isDestroyed || context.isFinishing) {
            return false
        }
    }
    return true
}

fun ImageView.setImageWithGlide(context: Context, imageUrl: String?) {
    if (isValidContextForGlide(context)) {
        Glide.with(context)
            .load(imageUrl)
            .timeout(4000)
            .apply(
                RequestOptions()
                    .skipMemoryCache(false)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .dontAnimate()
            )
            .into(this)
        this.clipToOutline = true
    }
}

fun ImageView.setImageWithGlideForShorts(context: Context, imageUrl: String?) {
    if (isValidContextForGlide(context)) {
        Glide.with(context)
            .load(imageUrl)
            .timeout(4000).placeholder(R.drawable.profile_avtar_logo)
            .fallback(R.drawable.profile_avtar_logo)
            .apply(
                RequestOptions()
                    .skipMemoryCache(false)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .dontAnimate()
            )
            .into(this)
        this.clipToOutline = true
    }
}

fun ImageView.setUserImageWithGlide(context: Context, imageUrl: String?) {
    val transformationUrl = getTenIntoTenImageUrl(imageUrl, context)
    if (isValidContextForGlide(this.context)) {
        Glide.with(this)
            .load(transformationUrl)
            .apply(
                RequestOptions()
                    .override(210, 210)
                    .placeholder(R.drawable.profile_avtar_logo)
                    .error(R.drawable.profile_avtar_logo)
                    .skipMemoryCache(false)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .dontAnimate()
            )
            .thumbnail(0.1f)
            .into(this)
        this.clipToOutline = true
    }
}

fun CircleImageView.loadProfileImage(imagePath: String?) {
    Glide.with(this.context)
        .load("$imagePath")
        .placeholder(R.drawable.profile_avtar_logo)
        .error(R.drawable.profile_avtar_logo)
        .into(this)
}


fun getArabicMonthName(month: Int): String {
    return arrayOf(
        "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
        "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
    )[month]
}


fun getImageUrl(imageType:String?, images: List<ImagesItem>?, context: Context? = null): String? {
  val imageObject = images?.find {it?.imageContent?.imageType  ==  imageType}
    var finalUrl:String? = null
    when (imageType) {
        Constants.NINE_INTO_SIXTEEN -> {
            finalUrl = getNineSixteenImageUrl(imageObject?.imageContent?.src, context)
        }
        Constants.SIXTEEN_INTO_NINE -> {
            finalUrl = getSixteenNineImageUrl(imageObject?.imageContent?.src, context)
        }
        Constants.ONE_INTO_ONE, Constants.TEN_INTO_TEN -> {
            finalUrl = getTenIntoTenImageUrl(imageObject?.imageContent?.src, context)
        }
        Constants.TEN_INTO_TEN_ASTRISKS -> {
            finalUrl = getTenIntoTenImageUrl(imageObject?.imageContent?.src, context)
        }
    }
   if (finalUrl.isNullOrEmpty()){
       finalUrl = imageObject?.imageContent?.src
   }
    Log.d("ImageUrl", "transformed ImageUrl: $finalUrl")
    return finalUrl
}


fun getNineSixteenImageUrl(posterURL: String?, context: Context?): String {
    val w = context?.resources?.getDimension(R.dimen.transformation_nine_sixteen_image_width)
    val h = context?.resources?.getDimension(R.dimen.transformation_nine_sixteen_image_height)
    return setImage(posterURL, w?.toInt().toString() + "x" + h?.toInt())
}

fun getSixteenNineImageUrl(posterURL: String?, context: Context?): String {
    val w = context?.resources?.getDimension(R.dimen.transformation_sixteen_nine_image_width)
    val h = context?.resources?.getDimension(R.dimen.transformation_sixteen_nine_image_height)
    return setImage(posterURL, w?.toInt().toString() + "x" + h?.toInt())
}

fun getTenIntoTenImageUrl(posterURL: String?, context: Context?): String {
    val w = context?.resources?.getDimension(R.dimen.transformation_ten_into_ten_image_width)
    val h = context?.resources?.getDimension(R.dimen.transformation_ten_into_ten_image_height)
    return setImage(posterURL, w?.toInt().toString() + "x" + h?.toInt())
}

fun setImage(oldUrl: String?, imageSize: String): String {
    val stringBuilder = StringBuilder()
    val urlImage = oldUrl?.trim { it <= ' ' }
    val one: String = SDKConfig.getInstance().webPUrl
    val two = imageSize + "/" + SDKConfig.getInstance().webP_QUALITY
    stringBuilder.append(one).append(two).append(urlImage)
    return stringBuilder.toString()
}

fun findOneByOneImage(reelList: List<ReelsContentItem?>?, position: Int): String? {
    // get customData object
    val customData = reelList?.get(position)?.customData
    // get image object from customData
    val images = customData?.reelCreatorId?.images
        // map images to ImagesItem and get 1*1 src from images array
    val imageObject = images?.find { it?.imageType == Constants.TEN_INTO_TEN_ASTRISKS }
    return imageObject?.src
}

fun setTabLayoutBackground(tabLayout: TabLayout, tab1: Int, tab2: Int) {
    val tabStrip = tabLayout.getChildAt(0) as ViewGroup
    val tabView1 = tabStrip.getChildAt(0)
    val tabView2 = tabStrip.getChildAt(1)
    if (tabView1 != null) {
        val paddingStart = tabView1.paddingStart
        val paddingTop = tabView1.paddingTop
        val paddingEnd = tabView1.paddingEnd
        val paddingBottom = tabView1.paddingBottom
        ViewCompat.setBackground(tabView1, AppCompatResources.getDrawable(tabView1.context, tab1))
        ViewCompat.setPaddingRelative(tabView1, paddingStart, paddingTop, paddingEnd, paddingBottom)
    }
    if (tabView2 != null) {
        val paddingStart = tabView2.paddingStart
        val paddingTop = tabView2.paddingTop
        val paddingEnd = tabView2.paddingEnd
        val paddingBottom = tabView2.paddingBottom
        ViewCompat.setBackground(tabView2, AppCompatResources.getDrawable(tabView2.context, tab2))
        ViewCompat.setPaddingRelative(tabView2, paddingStart, paddingTop, paddingEnd, paddingBottom)
    }
}

fun likeCountFormat(number: Number): String? {
    val suffix = charArrayOf(' ', 'k', 'M', 'B', 'T', 'P', 'E')
    val numValue = number.toLong()
    val value = Math.floor(Math.log10(numValue.toDouble())).toInt()
    val base = value / 3
    return if (value >= 3 && base < suffix.size) {
        DecimalFormat("#0.0").format(numValue / Math.pow(10.0, (base * 3).toDouble())) + suffix[base]
    } else {
        DecimalFormat("#,##0").format(numValue)
    }
}

@SuppressLint("SimpleDateFormat")
fun convertDateStringToGMT(dateString: String): Date? {
    try {
        val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        sdf.timeZone = TimeZone.getTimeZone("GMT")
        return sdf.parse(dateString)
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return null
}

fun convertDateStringToMilliseconds(dateString: String): Long {
    try {
        val sdf = SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH)
        val date = sdf.parse(dateString)
        if (date != null) {
            return date.time
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return 0L
}

@SuppressLint("StringFormatInvalid")
fun getTimeLabel(context: Context, timeInMs: Long): String {
    val current = System.currentTimeMillis()
    val diff = current - timeInMs
    if (diff < 5000) {
        return context.getString(R.string.few_seconds_ago)
    } else if (diff < TimeUtil.ONE_MINUTE_IN_MS) {
        return context.getString(R.string.seconds_ago, diff / 1000)
    } else if (diff < TimeUtil.ONE_HOUR_IN_MS) {
        val minutes = diff / TimeUtil.ONE_MINUTE_IN_MS
        return context.getString(R.string.minute_ago, minutes)
    } else if (diff < TimeUtil.ONE_DAY_IN_MS) {
        val hours = diff / TimeUtil.ONE_HOUR_IN_MS
        return if (hours > 1) {
            context.getString(R.string.hours_ago, hours)
        } else {
            context.getString(R.string.hour_ago)
        }
    } else if (diff < TimeUtil.ONE_WEEK_IN_MS) {
        val days = diff / TimeUtil.ONE_DAY_IN_MS
        return if (days > 1) {
            context.getString(R.string.days_ago, days)
        } else {
            context.getString(R.string.day_ago)
        }
    } else {
        val weeks: Long = diff / TimeUtil.ONE_WEEK_IN_MS
        return context.getString(R.string.weeks_ago, weeks)
    }
}


@SuppressLint("StringFormatInvalid")
fun timeDateFormat(context: Context, timestampMillis: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestampMillis
        val seconds = TimeUnit.MILLISECONDS.toSeconds(diff)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(diff)
        val hours = TimeUnit.MILLISECONDS.toHours(diff)
        val days = TimeUnit.MILLISECONDS.toDays(diff)
        return when {
//            seconds < 59 -> "$seconds ${context.getString(R.string.seconds_ago)}"
//            minutes < 60 -> "$minutes ${context.getString(R.string.minute_ago)}"
//            hours < 24 -> "$hours ${context.getString(R.string.hours_ago)}"
//            days < 7 -> "$days ${context.getString(R.string.day_ago)}"
//            days < 30 -> "${days / 7} ${context.getString(R.string.weeks_ago)}"
//            days < 365 -> "${days / 30} ${context.getString(R.string.month_ago)}"
            seconds < 59 -> context.getString(R.string.seconds_ago, seconds)
            minutes < 60 -> context.getString(R.string.minute_ago, minutes)
            hours < 24 -> context.getString(R.string.hours_ago, hours)
            days < 7 -> context.getString(R.string.days_ago_comment, days)
            days < 30 -> context.getString(R.string.weeks_ago, days / 7)
            days < 365 -> context.getString(R.string.months_ago_comment, days / 30)
            else -> {
                val sdf = SimpleDateFormat("dd MMM yyyy", Locale.getDefault())
                sdf.format(Date(timestampMillis))
            }
        }
}

fun ImageView.setImageAnimation(){
    this.scaleX = 0.7f
    this.scaleY = 0.7f
    this.animate()
        .scaleX(1.2f)
        .scaleY(1.2f)
        .setDuration(150)
        .withEndAction {
            this.animate()
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(100)
                .start()
        }.start()
}

@SuppressLint("DefaultLocale")
fun Long.toSuffixFormat(): String {
    return when {
        this >= 1_000_000_000 -> String.format("%.1fB", this / 1_000_000_000.0).removeSuffix(".0")
        this >= 1_000_000 -> String.format("%.1fM", this / 1_000_000.0).removeSuffix(".0")
        this >= 1_000 -> String.format("%.1fK", this / 1_000.0).removeSuffix(".0")
        else -> this.toString()
    }
}




