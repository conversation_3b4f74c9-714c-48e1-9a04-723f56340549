package com.enveu.utils.colorsJson.model

data class ColorConfig(
    val login_fgt_pwd_btn_border_color: String,
    val login_fgt_pwd_btn_bg_color: String,
    val login_fgt_pwd_btn_txt_color: String,
    val login_or_sign_in_with_line_color: String,
    val login_or_sign_in_with_line2_color: String,
    val login_or_sign_in_with_txt_color: String,
    val login_fb_img_bg_color: String,
    val login_fb_img_color: String,
    val login_apple_img_bg_color: String,
    val login_apple_img_color: String,
    val login_do_not_have_an_account_txt_color: String,
    val login_sign_up_txt_color: String,
    val login_signup_header_text_color: String,
    val signup_radio_btn_unchecked_color: String,
    val signup_radio_btn_checked_color: String,
    val signup_i_accept_the_text_color: String,
    val signup_terms_and_condition_text_color: String,
    val signup_and_the_text_color: String,
    val signup_privacy_policy_text_color: String,
    val signup_or_sign_up_with_line_color: String,
    val signup_or_sign_up_with_txt_color: String,
    val signup_fb_img_bg_color: String,
    val signup_fb_img_color: String,
    val signup_apple_img_bg_color: String,
    val signup_apple_img_color: String,
    val signup_already_have_an_account_txt_color: String,
    val signup_sign_in_txt_color: String,
    val toolbar_notification_icon_color: String,
    val toolbar_search_icon_color: String,
    val bottom_nav_selected_text_color: String,
    val bottom_nav_selected_icon_color: String,
    val bottom_nav_unselected_text_color: String,
    val bottom_nav_unselected_icon_color: String,
    val profile_short_name_text_color: String,
    val not_verified_text_color: String,
    val not_verified_text_background: String,
    val profile_long_name_text_color: String,
    val profile_drop_down_color: String,
    val profile_dob_icon_color: String,
    val profile_delete_account_text_color: String,
    val order_history_title_color: String,
    val order_history_description_txt_color: String,
    val order_history_active_btn_bg_color: String,
    val order_history_active_plan_btn_text_color: String,
    val settings_label_value_text_color: String,
    val setting_arrow_icon_color: String,
    val settings_version_txt_color: String,
    val popup_tittle_txt_color: String,
    val popup_subtitle_txt_color: String,
    val popup_continue_btn_selected_bg_color: String,
    val popup_continue_btn_txt_color: String,
    val popup_cancel_txt_color: String,
    val no_connection_title_color: String,
    val no_connection_description_color: String,
    val no_result_icon_color: String,
    val no_result_title_color: String,
    val no_result_description_color: String,
    val try_again_btn_color: String,
    val try_again_btn_txt_color: String,
    val reload_app_btn_color: String,
    val reload_app_btn_txt_color: String,
    val skip_text_color: String,
    val otp_enter_otp: String,
    val otp_digit_code_send: String,
    val album_title_color: String,
    val album_meta_data_color: String,
    val album_subtitle_color: String,
    val songs_color : String,
    val otp_email_phone: String,
    val otp_digits: String,
    val otp_code_valid: String,
    val rail_more_text_color: String,
    val rail_tittle_color: String,
    val asset_title_text_color: String,
    val asset_description_text_color: String,
    val main_btn_selected_bg_color: String,
    val main_btn_txt_color: String,
    val artist_no_result_found_text_color : String,
    val tph_border_color: String,
    val email_verify_text_color: String,
    val tph_bg_color: String,
    val tph_txt_color: String,
    val tph_hint_txt_color: String,
    val back_icon_color: String,
    val gray_back_icon_bg_color: String,
    val toolbar_text_color: String,
    val pwd_selected_eye_color: String,
    val pwd_unselected_eye_color: String,
    val logo_color: String,
    val series_detail_play_icon_color: String,
    val series_detail_quality_icon_border_color: String,
    val series_detail_description_text_color: String,
    val series_detail_sponsored_tv_color: String,
    val series_detail_sponsored_description_color: String,
    val series_detail_produced_tv_color: String,
    val series_detail_produced_description_color: String,
    val series_detail_text_expandable_color: String,
    val series_detail_episode_selected_btn_bg_color: String,
    val series_detail_episode_unselected_btn_txt_color: String,
    val series_detail_relatedVideo_selected_btn_bg_color: String,
    val series_detail_relatedVideo_unselected_btn_bg_color: String,
    val series_detail_relatedVideo_selected_btn_txt_color: String,
    val series_detail_relatedVideo_unselected_btn_txt_color: String,
    val series_detail_tab_item_selected_txt_color: String,
    val series_detail_tab_item_unselected_txt_color: String,
    val series_detail_tab_unselected_border_color: String,
    val series_detail_hd_border_color: String,
    val series_detail_hd_bg_color: String,
    val series_detail_all_episode_txt_color: String,
    val series_detail_tittle_txt_color: String,
    val series_detail_duration_text_color: String,
    val series_detail_keyword_text_color: String,
    val series_detail_quality_text_color: String,
    val series_detail_watch_trailer_text_color: String,
    val series_detail_watch_trailer_background_color: String,
    val series_detail_watch_trailer_border_color: String,
    val series_detail_episode_unselected_btn_bg_color: String,
    val series_detail_episode_selected_btn_txt_color: String,
    val series_detail_episode_duration_text_color: String,
    val search_no_result_found_txt_color: String,
    val search_keyword_hint_color: String,
    val search_keyword_text_color: String,
    val search_keyword_cut_icon_color: String,
    val search_popular_searches_txt_color: String,
    val search_recent_searches_txt_color: String,
    val search_recent_item_title_color: String,
    val search_recent_delete_icon_color: String,
    val search_main_item_title_text_color: String,
    val search_item_title_text_color: String,
    val search_item_desc_text_color: String,
    val search_item_see_all_txt_color: String,
    val detail_coming_soon_text_color: String,
    val myList_bg_color: String,
    val myList_tittle_txt_color: String,
    val myList_subTittle_txt_color: String,
    val myList_delete_icon_color: String,
    val item_label_bg_color: String,
    val buy_now_please_select_text_color: String,
    val buy_now_card_border_color: String,
    val buy_now_card_bg_color: String,
    val buy_now_card_unselected_text_color: String,
    val buy_now_card_selected_text_color: String,
    val buy_now_description_text_color: String,
    val buy_now_restore_text_color: String,
    val buy_now_pay_now_btn_bg_color: String,
    val buy_now_in_case_text_color: String,
    val item_label_text_color: String,
    val item_label_divider_color: String,
    val item_label_icon_color: String,
    val profile_Image_borderColor: String,
    val clear_Color: String,
    val popup_Bg_Color: String,
    val popup_Br_Color: String,
    val forget_title_text_color: String,
    val series_detail_my_list_unselected_color: String,
    val series_detail_share_unselected_color: String,
    val series_detail_like_unselected_color: String,
    val series_detail_my_list_selected_color: String,
    val series_detail_share_selected_color: String,
    val series_detail_like_selected_color: String,
    val series_detail_now_playing_title_color: String,
    val series_detail_now_playing_bg_color: String,
    val toolbar_bg_color: String,
    val series_detail_tab_selected_bg_color: String,
    val app_bg_color: String,
    val splash_bg_color: String,
    val selected_indicator_color: String,
    val unselected_indicator_color: String,
    val edit_text_border_color:String,
    val app_secondary_color:String,
)