package com.enveu.utils.commonMethods


import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.content.res.Resources
import android.content.res.TypedArray
import android.graphics.drawable.GradientDrawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.provider.Settings
import android.text.TextUtils
import android.util.Base64
import android.util.DisplayMetrics
import android.util.Log
import android.view.MenuItem
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.FrameLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.PopupMenu
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import com.android.billingclient.api.SkuDetails
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.enveu.OttApplication
import com.enveu.R
import com.enveu.SDKConfig
import com.enveu.activities.PlayableCallBack
import com.enveu.activities.web_view.WebViewActivity
import com.enveu.activities.detail.ContentMetaData
import com.enveu.activities.detail.ui.DetailActivity
import com.enveu.activities.detail.ui.EpisodeActivity
import com.enveu.activities.detail.ui.MatchDetailActivity
import com.enveu.activities.homeactivity.ApiInterface
import com.enveu.activities.homeactivity.ui.HomeActivity
import com.enveu.activities.purchase.in_app_billing.BillingProcessor
import com.enveu.activities.purchase.purchase_model.PurchaseModel
import com.enveu.activities.purchase.ui.VodOfferType
import com.enveu.activities.series.ui.SeriesDetailActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.EnterOTPActivity
import com.enveu.activities.usermanagment.ui.PaymentDetailPage
import com.enveu.activities.usermanagment.viewmodel.RegistrationLoginViewModel
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaConfig
import com.enveu.appLevelModel.MediaInfo
import com.enveu.baseModels.BaseActivity
import com.enveu.beanModel.configBean.ResponseConfig
import com.enveu.beanModel.drm.DRM
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModel.membershipAndPlan.ResponseMembershipAndPlan
import com.enveu.beanModel.responseModels.sharing.SharingModel
import com.enveu.beanModel.userProfile.UserProfileResponse
import com.enveu.beanModelV3.searchGenres.SearchGenres
import com.enveu.beanModelV3.searchV2.ItemsItem
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.list.ImagesItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent
import com.enveu.beanModelV3.videoDetailsV2.EnveuVideoDetails
import com.enveu.beanModelV3.videoDetailsV2.EnveuVideoDetailsBean
import com.enveu.bean_model_v2_0.videoDetailBean.Data
import com.enveu.bean_model_v2_0.videoDetailBean.EnvVideoDetailsBean
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory
import com.enveu.client.enums.ImageType
import com.enveu.client.enums.RailCardType
import com.enveu.databinding.ActivityPaymentDetailPagePlanBinding
import com.enveu.databinding.ActivityPurchaseBinding
import com.enveu.databinding.ActivitySelectSubscriptionPlanBinding
import com.enveu.enums.EVENT_STATUS
import com.enveu.enums.StreamStatus
import com.enveu.fragments.album.AlbumFragment
import com.enveu.fragments.artist.ArtistFragment
import com.enveu.fragments.dialog.DialogPlayer
import com.enveu.fragments.player.ui.UserInteractionFragment
import com.enveu.fragments.singles.SinglesDetailFragment
import com.enveu.jwplayer.player.PlayerActivity
import com.enveu.networking.servicelayer.APIServiceLayer
import com.enveu.utils.Logger
import com.enveu.utils.MediaTypeConstants
import com.enveu.utils.ObjectHelper
import com.enveu.utils.TrailerRedirection
import com.enveu.utils.config.bean.dmsResponse.ConfigBean
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.fireBaseAnalytics.AnalyticsUtils
import com.enveu.utils.helpers.ActivityTrackers
import com.enveu.utils.helpers.ImageHelper
import com.enveu.utils.helpers.StringUtils
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.facebook.login.LoginManager
import com.google.android.gms.tasks.OnCompleteListener
import com.google.android.gms.tasks.Task
import com.google.firebase.messaging.FirebaseMessaging
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.moe.pushlibrary.MoEHelper
import com.moengage.core.Properties
import com.moengage.core.internal.utils.isNullOrEmpty
import de.hdodenhof.circleimageview.CircleImageView
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter
import org.json.JSONObject
import retrofit2.Response
import java.lang.ref.WeakReference
import java.text.DateFormat
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit


class AppCommonMethod private constructor() : AppCompatActivity(), DialogPlayer.DialogListener {
    override fun onDialogFinish() {}

    companion object {
        @JvmField
        var Url = ""

        @JvmField
        var UriId = ""

        @JvmField
        val options: RequestOptions = RequestOptions()
            .centerCrop()
            .placeholder(R.drawable.default_profile_pic)
            .error(R.drawable.default_profile_pic)
            .diskCacheStrategy(DiskCacheStrategy.NONE)

        @JvmField
        val optionsSearch: RequestOptions = RequestOptions()
            .fitCenter()
            .placeholder(R.color.placeholder_bg)
            .error(R.color.placeholder_bg)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
        val optionsPlayer: RequestOptions = RequestOptions()
            .fitCenter()
            .diskCacheStrategy(DiskCacheStrategy.NONE)
        val castOptions: RequestOptions = RequestOptions()
            .placeholder(R.color.placeholder_bg)
            .error(R.color.placeholder_bg)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
        var afterLogin = false

        @JvmField
        var urlPoints = ""

        // Constants
        private const val PR1 = "9x16"
        private const val PR2 = "2x3"
        private const val LDS = "16x9"
        private const val SQR = "1x1"
        private const val CIR = "1x1"
        private const val CST = "16x9"
        private const val LDS2 = "16x9"
        private const val LDS3 = "3x2"
        private const val LDS4 = "4x3"
        private const val PR3 = "3x4"
        private const val LDS3_2 = "3x2"

        @JvmField
        var isSeasonCount = false
        var isSeriesPage = false
        var assetId = 0
        var seriesId = 0
        var seasonId = 0
        private var mActivity: WeakReference<Activity>? = null
        private var mLastClickTime: Long = 0
        private var isUserNotVerify = false
        private var isUserNotEntitle = false
        private var resEntitle: ResponseEntitle? = null

        @JvmStatic
        fun convertDpToPixel(dp: Int): Int {
            val metrics: DisplayMetrics = Resources.getSystem().displayMetrics
            return dp * (metrics.densityDpi / 160)
        }

        private const val FIT_IN_640_360 = "/fit-in/640x360"
        private const val FIT_IN_200_200 = "/fit-in/200x200"
        private const val FIT_IN_360_640 = "/fit-in/360x640"
        private const val IMG_FORMAT_SIZE = "(100):format(webP):maxbytes(400)"
        private const val CONTROL_PARAM_CONTENT_TYPE = "mediaType"
        private const val CONTROL_PARAM_ID = "id"
        private var videoDetails: EnveuVideoItemBean? = null


        @JvmStatic
        fun getImageUrl(contentType: String, shape: String): String {
            var url = ""
            var resolution = ""
            val imageFrontEndUrl: String = urlPoints
            if (shape.equals("LANDSCAPE", ignoreCase = true)) {
                resolution = FIT_IN_640_360
            } else if (shape.equals("SQUARE", ignoreCase = true)) {
                resolution = FIT_IN_200_200
            } else if (shape.equals("CIRCLE", ignoreCase = true)) {
                resolution = FIT_IN_200_200
            } else if (shape.equals("POTRAIT", ignoreCase = true)) {
                resolution = FIT_IN_360_640
            } else if (shape.equals("CROUSEL", ignoreCase = true)) {
                resolution = FIT_IN_640_360
            } else if (shape.equals("POSTER_POTRAIT", ignoreCase = true)) {
                resolution = FIT_IN_360_640
            } else if (shape.equals("POSTER_LANDSCAPE", ignoreCase = true)) {
                resolution = FIT_IN_640_360
            }
            if (contentType.equals(AppConstants.VOD, ignoreCase = true)) {
                url = (imageFrontEndUrl + resolution + AppConstants.FILTER + AppConstants.QUALITY
                        + IMG_FORMAT_SIZE + AppConstants.VIDEO_IMAGE_BASE_KEY)
            } else if (contentType.equals(AppConstants.SERIES, ignoreCase = true)) {
                url = (imageFrontEndUrl + resolution + AppConstants.FILTER + AppConstants.QUALITY
                        + IMG_FORMAT_SIZE + AppConstants.SERIES_IMAGES_BASE_KEY)
            } else if (contentType.equals(AppConstants.CAST_AND_CREW, ignoreCase = true)) {
                url = (imageFrontEndUrl + resolution + AppConstants.FILTER + AppConstants.QUALITY
                        + IMG_FORMAT_SIZE + AppConstants.CAST_CREW_IMAGES_BASE_KEY)
            } else if (contentType.equals(AppConstants.GENRE, ignoreCase = true)) {
                url = (imageFrontEndUrl + resolution + AppConstants.FILTER + AppConstants.QUALITY
                        + IMG_FORMAT_SIZE + AppConstants.GENRE_IMAGES_BASE_KEY)
            }
            return url
        }

        @Throws(NumberFormatException::class)
        fun parseTime(millis: Long): Long {
            return TimeUnit.MILLISECONDS.toSeconds(millis)
        }

        ///// Create dynamic link object
        fun createDynamicLinkObject(
            id: String?,
            mediaType: String?,
            contentSlug: String?
        ): JSONObject {
            val jsonObject = JSONObject()
            try {
                jsonObject.put(CONTROL_PARAM_CONTENT_TYPE, mediaType)
                jsonObject.put("id", id)
                jsonObject.put("contentSlug", contentSlug)
            } catch (e: Exception) {
                Logger.w(e)
            }
            return jsonObject
        }

        var dynamicLinkUri: Uri? = null

        @JvmStatic
        fun openShareFirebaseDynamicLinks(
            activity: Activity,
            enveuVideoItemBean: EnveuVideoItemBean
        ) {
            try {

                val registrationLoginViewModel: RegistrationLoginViewModel =
                    ViewModelProvider((activity as BaseActivity?)!!).get(
                        RegistrationLoginViewModel::class.java
                    )

                AnalyticsUtils.logUserInteractionEvent(
                    activity,
                    AppConstants.SHARE_CONTENT,
                    assetId.toString(),
                    enveuVideoItemBean.title,
                    enveuVideoItemBean.assetType
                )

                registrationLoginViewModel.hitSharingApi(activity, enveuVideoItemBean).observe(activity) { sharingModel: SharingModel? ->
                        if (sharingModel?.data != null) {
                            val sharingLink = sharingModel.data.dynamicLink
                            activity.runOnUiThread(Runnable {
                                if (sharingLink != null) {
                                    val title = enveuVideoItemBean.title ?: ""
//                                    val imgUrl = enveuVideoItemBean.posterURL ?: ""
                                    val sharingIntent = Intent(Intent.ACTION_SEND).apply {
                                        type = "text/plain"
                                        putExtra(
                                            Intent.EXTRA_TEXT,
                                            activity.resources.getString(R.string.checkout) + " " +
                                                    title + " on " +
                                                    activity.resources.getString(R.string.app_name) + "\n" +
                                                    sharingLink.toString()
//                                                    imgUrl.toString()
                                        )
                                    }
                                    //use this if image needed
                                    //+ "\n" +sharingModel.data.displayImg
                                    activity.startActivity(
                                        Intent.createChooser(
                                            sharingIntent,
                                            activity.resources.getString(R.string.detail_page_share)
                                        )
                                    )
                                }
                            })

//                        } else {
//                            Toast.makeText(
//                                activity,
//                                activity.resources.getString(R.string.something_went_wrong_at_our_end_please_try_later),
//                                Toast.LENGTH_SHORT
//                            ).show()
                        }
                    }
            } catch (e: Exception) {
                Log.w("appcrashOnShare", e.message!!)
            }
        }

        fun formatViewCount(viewCount: Int): String {
            return if (viewCount >= 1000) {
                "${viewCount / 1000}k"
            } else {
                viewCount.toString()
            }
        }

        fun getStreamStatus(streamStatus: StreamStatus, eventStatus: EVENT_STATUS): EVENT_STATUS {
            return when (eventStatus) {
                EVENT_STATUS.SCHEDULED -> EVENT_STATUS.SCHEDULED
                EVENT_STATUS.READY -> {
                    when (streamStatus) {
                        StreamStatus.STREAM_STARTING -> EVENT_STATUS.READY
                        else -> EVENT_STATUS.READY
                    }
                }

                EVENT_STATUS.STARTED -> {
                    when (streamStatus) {
                        StreamStatus.STREAMING -> EVENT_STATUS.STARTED
                        StreamStatus.STREAM_STARTING -> EVENT_STATUS.READY
                        StreamStatus.STREAM_STOPPING -> EVENT_STATUS.ABOUT_END
                        StreamStatus.STREAM_ERROR -> EVENT_STATUS.ERROR
                        else -> EVENT_STATUS.READY
                    }
                }

                EVENT_STATUS.NOT_ATTENDED -> EVENT_STATUS.NOT_ATTENDED
                EVENT_STATUS.END -> {
                    when (streamStatus) {
                        StreamStatus.STREAM_STOPPED -> EVENT_STATUS.END
                        StreamStatus.STREAM_STOPPING -> EVENT_STATUS.END
                        StreamStatus.STREAM_ERROR -> EVENT_STATUS.ERROR
                        else -> EVENT_STATUS.END
                    }
                }

                else -> EVENT_STATUS.END
            }
        }

        private fun createURI(
            title: String,
            assetId: Int,
            assetType: String,
            imgUrl1: String,
            contentSlug: String?,
            activity: Activity
        ): String {
            var uri = ""
            uri = try {
                val assetId1 = assetId.toString() + ""
                val assetType1 = assetType + ""
                Uri.parse("https://web-qa.saabmedia.enveu.in/detail" + "/" + assetType1.toLowerCase() + "/" + contentSlug + "?deeplink=true")
                    .buildUpon().build().toString()
            } catch (ignored: Exception) {
                ""
            }
            return uri
        }


        fun convertToMinutes(milliseconds: Long): String {
            var milliseconds = milliseconds
            var minutes = ""
            try {
                if (milliseconds % 1000 > 0) {
                    milliseconds = milliseconds + milliseconds % 1000
                }
                val minute = TimeUnit.MILLISECONDS.toMinutes(milliseconds)
                Logger.w("episodeTiming $minute   ---   $milliseconds")
                minutes = String.format(Locale.getDefault(), "%02d", minute)
            } catch (ex: Exception) {
                Logger.w(ex)
            }
            return minutes
        }

//        fun stringForTime(timeMs: Long): String {
//            val formatBuilder = StringBuilder()
//            val formatter = Formatter(formatBuilder, Locale.getDefault())
//            val totalSeconds = (timeMs + 500) / 1000
//            val seconds = totalSeconds % 60
//            val minutes = totalSeconds / 60 % 60
//            val hours = totalSeconds / 3600
//            formatBuilder.setLength(0)
//            return if (hours > 0) formatter.format("%d:%02d:%02d", hours, minutes, seconds)
//                .toString() else formatter.format("%02d:%02d", minutes, seconds).toString()
//        }

        fun stringForTime(timeMs: Long): String {
            val totalSeconds = (timeMs + 500) / 1000
            val seconds = totalSeconds % 60
            val minutes = (totalSeconds / 60) % 60
            val hours = totalSeconds / 3600

            return when {
                hours > 0 -> "${hours}hr : ${minutes}min"
                minutes > 0 -> "${minutes}min : ${seconds}sec"
                else -> "${seconds}sec"
            }
        }


        fun calculateTime(milliseconds: Long): String {
            var milliseconds = milliseconds
            if (milliseconds % 1000 > 0) {
                milliseconds = milliseconds + milliseconds % 1000
            }
            val hours = TimeUnit.MILLISECONDS.toHours(milliseconds)
            val minute = TimeUnit.MILLISECONDS.toMinutes(milliseconds) % TimeUnit.HOURS.toMinutes(1)
            val second =
                TimeUnit.MILLISECONDS.toSeconds(milliseconds) % TimeUnit.MINUTES.toSeconds(1)
            val strHour = String.format(Locale.getDefault(), "%02d", hours)
            val strMinute = String.format(Locale.getDefault(), "%02d", minute)
            val strSecond = String.format(Locale.getDefault(), "%02d", second)
            var showTime = "$minute:$strSecond"
            if (hours > 0) showTime =
                "$strHour:$strMinute:$strSecond" else if (minute > 0) showTime =
                "$strMinute:$strSecond" else if (second >= 0) showTime = "00:$strSecond"
            return showTime
        }

        @JvmStatic
        fun launchArtistAndAlbum(
            context: Context,
            assetType: String,
            contentSlug: String,
            assertID: Int
        ) {
            if (assetType.equals(AppConstants.ARTISTS, ignoreCase = true)) {
                setArtistFragment(context, contentSlug, assetType, assertID)
            } else if (assetType.equals(AppConstants.ALBUM, ignoreCase = true)) {
                setAlbumFragment(context, contentSlug, assetType, assertID.toString())
            } else if (assetType.equals(AppConstants.SINGLES, ignoreCase = true)) {
                setSingleFragment(context, contentSlug, assetType, assertID.toString())
            } else if (assetType.equals(AppConstants.PERSON, ignoreCase = true)) {
                setArtistFragment(context, contentSlug, assetType, assertID)
            }

        }

        fun getDuration(milliseconds: Long): String {
            var milliseconds = milliseconds
            if (milliseconds % 1000 > 0) {
                milliseconds += milliseconds % 1000
            }
            val hours = TimeUnit.MILLISECONDS.toHours(milliseconds)
            val minute = TimeUnit.MILLISECONDS.toMinutes(milliseconds) % TimeUnit.HOURS.toMinutes(1)
            val second =
                TimeUnit.MILLISECONDS.toSeconds(milliseconds) % TimeUnit.MINUTES.toSeconds(1)
            val strHour = String.format(Locale.getDefault(), "%01d", hours)
            val strMinute = String.format(Locale.getDefault(), "%01d", minute)
            val strSecond = String.format(Locale.getDefault(), "%01d", second)
            var showTime = "$minute:$strSecond"
            if (hours > 0) showTime =
                strHour + "h " + strMinute + "m " + strSecond + "s" else if (minute > 0) showTime =
                strMinute + "m " + strSecond + "s" else if (second >= 0) showTime =
                "00:" + strSecond + "s"
            return showTime
        }

        @JvmStatic
        fun railBadgeVisibility(view: View, isVisible: Boolean) {
            if (isVisible) {
                view.visibility = View.VISIBLE
            } else {
                view.visibility = View.INVISIBLE
            }
        }

        fun setImage(oldUrl: String, imageSize: String): String {
            Logger.d("PRPosterImage-->>: $oldUrl $imageSize")
            val stringBuilder = StringBuilder()
            val urlImage = oldUrl.trim { it <= ' ' }
            val one: String = SDKConfig.getInstance().webPUrl
            val two = imageSize + "/" + SDKConfig.getInstance().webP_QUALITY
            stringBuilder.append(one).append(two).append(urlImage)
            Logger.d("ImageUrld-->>$one  $two $urlImage")
            Logger.d("-->>StringBilder$stringBuilder")
            return stringBuilder.toString()
        }


        fun getImageArray(context: Context): IntArray {
            val typedArray: TypedArray =
                context.resources.obtainTypedArray(R.array.image_array)
            val len = typedArray.length()
            val imageArray = IntArray(len)
            for (i in 0 until len) {
                imageArray[i] = typedArray.getResourceId(i, 0)
            }
            typedArray.recycle()
            return imageArray
        }

        fun callpreference(): ResponseConfig {
            val gson = Gson()
            val json: String = KsPreferenceKeys.getInstance().appPrefConfigResponse
            return gson.fromJson(json, ResponseConfig::class.java)
        }

        fun setProfilePic(
            preference: KsPreferenceKeys,
            context: Context?,
            key: String,
            imageView: CircleImageView?
        ) {
            try {
                if (StringUtils.isNullOrEmptyOrZero(key)) {
                    ImageHelper.getInstance(context)
                        .loadImageToProfile(imageView, "")
                } else {
                    val stringBuilder = StringBuilder()
                    var url1: String = preference.appPrefCfep
                    if (key.contains("http")) {
                        stringBuilder.append(url1).append("/").append(key)
                    } else {
                        if (StringUtils.isNullOrEmpty(url1)) {
                            url1 = urlPoints
                            preference.appPrefCfep = url1
                        }
                        val url2: String = AppConstants.PROFILE_FOLDER
                        stringBuilder.append(url1).append(url2).append(key)
                    }
                    ImageHelper.getInstance(context)
                        .loadImageToProfile(imageView, stringBuilder.toString())
                }
            } catch (e: Exception) {
                Logger.e("AppCommonMEthod", "setProfilePic" + e.message)
            }
        }

        @JvmStatic
        @SuppressLint("HardwareIds")
        fun getDeviceId(contentResolver: ContentResolver?): String {
            return Settings.Secure.getString(
                contentResolver,
                Settings.Secure.ANDROID_ID
            )
        }

        @JvmStatic
        fun getDeviceName(context: Context): String? {
            val deviceName: String = if (TextUtils.isEmpty(
                    Settings.System.getString(
                        context.contentResolver,
                        AppConstants.DEVICE_NAME
                    )
                )
            ) {
                Settings.Global.getString(context.contentResolver, Settings.Global.DEVICE_NAME)
            } else {
                Settings.System.getString(context.contentResolver, AppConstants.DEVICE_NAME)
            }
            return deviceName
        }

        @JvmStatic
        fun getGenre(videosItem: EnveuVideoItemBean): String {
            var stringBuilder = StringBuilder()
            if (videosItem.assetGenres != null && !videosItem.assetGenres.isEmpty()) {
                for (i in videosItem.assetGenres.indices) {
                    stringBuilder = if (i == videosItem.assetGenres.size - 1) {
                        stringBuilder.append(videosItem.assetGenres[i])
                    } else stringBuilder.append(videosItem.assetGenres[i]).append(", ")
                }
            }
            return stringBuilder.toString()
        }

        @JvmStatic
        val speciesList: String
            get() {
                var cast = ""
                var speciesBuilder = StringBuilder()
                for (i in KsPreferenceKeys.getInstance().speciesList.indices) {
                    speciesBuilder =
                        speciesBuilder.append(KsPreferenceKeys.getInstance().speciesList.get(i))
                            .append(",")
                }
                if (speciesBuilder.length > 0) {
                    cast = speciesBuilder.toString()
                    cast = cast.substring(0, cast.length - 1)
                } else {
                    cast = ""
                }
                Logger.w("speciesList", cast)
                return cast
            }


        fun getListViewType(contentImageType: String): Int {
            return if (contentImageType.equals(ImageType.CIR.name, ignoreCase = true)) {
                0
            } else if (contentImageType.equals(ImageType.LDS.name, ignoreCase = true)) {
                1
            } else if (contentImageType.equals(ImageType.PR1.name, ignoreCase = true)) {
                2
            } else if (contentImageType.equals(ImageType.PR2.name, ignoreCase = true)) {
                3
            } else if (contentImageType.equals(ImageType.SQR.name, ignoreCase = true)) {
                4
            } else {
                1
            }
        }

        fun getBranchUrl(posterURL: String, context: Context): String {
            Logger.d("PRPosterImage-->>$posterURL")
            val w = context.resources.getDimension(R.dimen.splash_width).toInt()
            val h = context.resources.getDimension(R.dimen.splash_width).toInt()
            return setImage(posterURL, w.toString() + "x" + h)
        }

        @JvmStatic
        fun getListPRImage(posterURL: String, context: Context): String {
            Logger.d("PRPosterImage-->>$posterURL")
            val w = context.resources.getInteger(R.integer.portrait_image_width)
            val h = context.resources.getInteger(R.integer.portrait_image_height)
            return setImage(posterURL, w.toString() + "x" + h)
        }

//        @JvmStatic
//        fun getCarouselPRImage(posterURL: String, context: Context): String {
//            Logger.d("PRPosterImage-->>$posterURL")
//            val w = context.resources.getInteger(R.dimen.carousel_portrait_image_width)
//            val h = context.resources.getInteger(R.dimen.carousel_portrait_image_height)
//            return setImage(posterURL, w.toString() + "x" + h)
//        }

        @JvmStatic
        fun getListPRTwoImage(posterURL: String, context: Context): String {
            Logger.d("PRPosterImage-->>$posterURL")
            return setImage(
                posterURL, context.resources.getInteger(R.integer.portrait_image_width)
                    .toString() + "x" + context.resources.getInteger(R.integer.portrait_image_height)
            )
        }

        @JvmStatic
        fun getListCIRCLEImage(posterURL: String, context: Context): String {
            Logger.d("PRPosterImage-->>$posterURL")
            val w = context.resources.getDimension(R.dimen.circle_image_width).toInt()
            val h = context.resources.getDimension(R.dimen.circle_image_height).toInt()
            return setImage(posterURL, w.toString() + "x" + h)
        }

        @JvmStatic
        fun getListLDSImage(posterURL: String, context: Context): String {
            Logger.d("PRPosterImage-->>$posterURL")
            return setImage(
                posterURL, context.resources.getInteger(R.integer.landscape_image_width)
                    .toString() + "x" + context.resources.getInteger(R.integer.landscape_image_height)
            )
        }

        @JvmStatic
        fun getDeviceModelName(): String {
            val manufacturer = Build.MANUFACTURER
            val model = Build.MODEL
            return if (model.lowercase(Locale.getDefault()).startsWith(
                    manufacturer.lowercase(
                        Locale.getDefault()
                    )
                )
            ) {
                capitalize(model)
            } else {
                capitalize(manufacturer) + " " + model
            }
        }

        @JvmStatic
        fun getDeviceType(): String {
            val isTablet: Boolean =
                OttApplication.context.resources.getBoolean(R.bool.isTablet)
            return if (isTablet) {
                "TABLET"
            } else {
                "MOBILE"
            }
        }


        private fun capitalize(s: String?): String {
            if (s == null || s.length == 0) {
                return ""
            }
            val first = s[0]
            return if (Character.isUpperCase(first)) {
                s
            } else {
                first.uppercaseChar().toString() + s.substring(1)
            }
        }

        @JvmStatic
        fun getListSQRImage(posterURL: String, context: Context): String {
            Logger.d("PRPosterImage-->>$posterURL")
            val w = context.resources.getDimension(R.dimen.square_image_width).toInt()
            val h = context.resources.getDimension(R.dimen.square_image_height).toInt()
            return setImage(posterURL, w.toString() + "x" + h)
        }

        fun getListPRTwoImg(posterURL: String, context: Context): String {
            Logger.d("PRPosterImage-->>$posterURL")
            val w = context.resources.getDimension(R.dimen.search_toolbar_height_237dp).toInt()
            val h = context.resources.getDimension(R.dimen.search_toolbar_height_65dp).toInt()
            return setImage(posterURL, w.toString() + "x" + h)
        }

        /**
         * launchDetailScreen method will call on click of item except home rail
         * on the basis of screenType will check isDetailPage is true or not if its true will check further condition
         * item will redirect to which detail page:- will decide on the basis of media type which are defined in local json file (media-type-mapping)
         * if media type is trailer it will play directly
         * */

        @JvmStatic
        fun launchDetailScreen(
            context: Context,
            screenType: String?,
            id: Int?,
            sku: String?,
            mediaType: String?,
            tittle: String?,
            externalRefId: String,
            posterURL: String,
            seasonPosition: Int, contentSlug: String = "", itemValue: EnveuVideoItemBean
        ) {
            if (SystemClock.elapsedRealtime() - mLastClickTime < 2000) {
                return
            }
            mLastClickTime = SystemClock.elapsedRealtime()
            checkForContentType(
                mediaType.toString(),
                id,
                externalRefId,
                tittle,
                mediaType.toString(),
                posterURL,
                context,
                contentSlug = contentSlug,
                enveuVideoItemBean = listOf(itemValue),
                sku = sku,
                position = 0,
                continueWatching = false
            )
        }

        fun setGradientBackgroundColor(
            firstColor: Int,
            secondColor: Int,
            orientationValue: String
        ): GradientDrawable {
            val drawable = GradientDrawable().apply {
                colors = intArrayOf(
                    firstColor,
                    secondColor
                )
                when (orientationValue) {
                    "LEFT_TO_RIGHT" -> {
                        orientation = GradientDrawable.Orientation.LEFT_RIGHT
                    }

                    "RIGHT_TO_LEFT" -> {
                        orientation = GradientDrawable.Orientation.RIGHT_LEFT
                    }

                    "TOP_TO_BOTTOM" -> {
                        orientation = GradientDrawable.Orientation.TOP_BOTTOM
                    }

                    "BOTTOM_TO_TOP" -> {
                        orientation = GradientDrawable.Orientation.BOTTOM_TOP
                    }
                }
                gradientType = GradientDrawable.LINEAR_GRADIENT
                shape = GradientDrawable.RECTANGLE
            }
            return drawable
        }


        fun setGradientBackgroundColor(
            firstColor: Int,
            secondColor: Int,
            thirdColor: Int,
            orientationValue: String
        ): GradientDrawable {
            val drawable = GradientDrawable().apply {
                colors = intArrayOf(
                    firstColor,
                    secondColor,
                    thirdColor
                )
                when (orientationValue) {
                    "LEFT_TO_RIGHT" -> {
                        orientation = GradientDrawable.Orientation.LEFT_RIGHT
                    }

                    "RIGHT_TO_LEFT" -> {
                        orientation = GradientDrawable.Orientation.RIGHT_LEFT
                    }

                    "TOP_TO_BOTTOM" -> {
                        orientation = GradientDrawable.Orientation.TOP_BOTTOM
                    }

                    "BOTTOM_TO_TOP" -> {
                        orientation = GradientDrawable.Orientation.BOTTOM_TOP
                    }
                }
                gradientType = GradientDrawable.LINEAR_GRADIENT
                shape = GradientDrawable.RECTANGLE
            }
            return drawable
        }

        fun clearCredientials(preference: KsPreferenceKeys, context: Context) {
            try {
                val json: String = KsPreferenceKeys.getInstance().getString("DMS_Response", "")
                val isFacebook: String = preference.appPrefLoginType
                if (isFacebook.equals(
                        AppConstants.UserLoginType.FbLogin.toString(),
                        ignoreCase = true
                    )
                ) {
                    LoginManager.getInstance().logOut()
                }
                val strCurrentTheme: String = KsPreferenceKeys.getInstance().currentTheme
                val strCurrentLanguage: String = KsPreferenceKeys.getInstance().appLanguage
                val strSubscriptionURL: String =
                    KsPreferenceKeys.getInstance().subscriptioN_BASE_URL
                val strPaymentURL: String = KsPreferenceKeys.getInstance().paymenT_BASE_URL
                val isBingeWatchEnable: Boolean = KsPreferenceKeys.getInstance().bingeWatchEnable
                preference.appPrefRegisterStatus = AppConstants.UserStatus.Logout.toString()
                val menuItem = preference.dataMenuKeyValue
                preference.clear()
                KsPreferenceKeys.getInstance().saveDataMenuKeyValue(menuItem)
                KsPreferenceKeys.getInstance().setString("DMS_Response", json)
                KsPreferenceKeys.getInstance().setfirstTimeUserForKidsPIn(false)
                KsPreferenceKeys.getInstance().subscriptioN_BASE_URL = strSubscriptionURL
                KsPreferenceKeys.getInstance().paymenT_BASE_URL = strPaymentURL
                KsPreferenceKeys.getInstance().currentTheme = strCurrentTheme
                KsPreferenceKeys.getInstance().appLanguage = strCurrentLanguage
                updateLanguage(strCurrentLanguage, context as BaseActivity)
                KsPreferenceKeys.getInstance().bingeWatchEnable = isBingeWatchEnable

            } catch (e: Exception) {
                Logger.w(e)
            }
        }

        private fun redirectOnDetailPages(
            context: Context,
            id: Int, externalRefId: String?, title: String?, assetType: String?, posterURL: String?,
            screenType: String?,
            trailerRefId: String?,
            contentSlug: String?,
            enveuVideoItemBean: List<EnveuVideoItemBean>,
            sku: String?, position: Int, isContinueWatching: Boolean
        ) {
            val mediaConfigInfo: MediaInfo? = AppConfigMethod.getMediaMappingByType(screenType)
            val mediaConfig =
                AppConfigMethod.getMediaConfigByType(mediaConfigInfo?.pageType.toString())
            checkForDetailPage(
                mediaConfig,
                id,
                externalRefId,
                title,
                assetType,
                posterURL,
                context,
                mediaConfigInfo?.pageType.toString(),
                trailerRefId,
                contentSlug,
                enveuVideoItemBean,
                sku,
                position,
                isContinueWatching
            )

        }

        private fun mediaTypeRedirection(
            id: Int?,
            externalRefId: String?,
            title: String?,
            assetType: String?,
            posterURL: String?,
            pageType: String?,
            context: Context,
            trailerRefId: String?,
            contentSlug: String?,
            sku: String?,
            enveuVideoItemBean: List<EnveuVideoItemBean>,
            position: Int,
            isContinueWatching: Boolean
        ) {
            val featureList: FeatureFlagModel?
            featureList = AppConfigMethod.parseFeatureFlagList()

            if (pageType.equals(AppConstants.episode, ignoreCase = true)) {
                if (featureList.featureFlag.IS_ENT_MUSIC_ENABLED) {
                    if (context is HomeActivity) {
                        context.dismissMiniPlayer()
                    }
                }
                if (id != null) {
                    ActivityLauncher.getInstance().episodeScreenBrightcove(
                        context as BaseActivity,
                        EpisodeActivity::class.java,
                        id,
                        0
                    )
                }
            } else if (pageType.equals(AppConstants.ARTISTS, ignoreCase = true)) {
                setArtistFragment(context, contentSlug, assetType, id)
            } else if (pageType.equals(AppConstants.ALBUM, ignoreCase = true)) {
                setAlbumFragment(context, contentSlug, assetType, id.toString())
            } else if (pageType.equals(AppConstants.SINGLES, ignoreCase = true)) {
                setSingleFragment(context, contentSlug, assetType, id.toString())
            } else if (pageType.equals(AppConstants.MATCHES, ignoreCase = true)) {
                Logger.d("ContextInstanceOf", context.toString())
                if (featureList.featureFlag.IS_ENT_MUSIC_ENABLED) {
                    if (context is HomeActivity) {
                        context.dismissMiniPlayer()
                    }
                }
                if (id != null) {
                    ActivityLauncher.getInstance().matchDetailScreen(
                        context as BaseActivity,
                        MatchDetailActivity::class.java,
                        id
                    )
                }

            } else if (pageType.equals(AppConstants.Movie, ignoreCase = true)) {
                if (featureList.featureFlag.IS_ENT_MUSIC_ENABLED) {
                    if (context is HomeActivity) {
                        context.dismissMiniPlayer()
                    }
                }
                if (enveuVideoItemBean.get(position).videoDetails != null && enveuVideoItemBean[position].videoDetails.videoType.equals(AppConstants.EPISODES)) {
                    ActivityLauncher.getInstance().seriesDetailScreen(
                        context as BaseActivity,
                        SeriesDetailActivity::class.java,
                        enveuVideoItemBean[0].customDataV3.episodeSeriesId.toInt(),
                        assetType,
                        isContinueWatching
                    )
                }
                if (id != null) {
                    ActivityLauncher.getInstance().detailScreenBrightCove(context as BaseActivity, DetailActivity::class.java, id, isContinueWatching, enveuVideoItemBean?.get(position)?.assetType, enveuVideoItemBean?.get(position)?.customType)
                }

            } else if (pageType.equals(AppConstants.SERIES, ignoreCase = true)) {
                if (featureList.featureFlag.IS_ENT_MUSIC_ENABLED) {
                    if (context is HomeActivity) {
                        context.dismissMiniPlayer()
                    }
                }

                if (enveuVideoItemBean[position].customType.equals(
                        "ENT_SERIES",
                        ignoreCase = true
                    ) || enveuVideoItemBean[position].customType.equals(
                        AppConstants.SERIES,
                        ignoreCase = true
                    )
                ) {
                    if (id != null) {
                        ActivityLauncher.getInstance().seriesDetailScreen(
                            context as BaseActivity,
                            SeriesDetailActivity::class.java,
                            id,
                            assetType
                        )
                    }
                } else if (enveuVideoItemBean[position].customDataV3 != null && enveuVideoItemBean[position]?.customDataV3?.episodeSeriesId != null && enveuVideoItemBean[position].customDataV3?.episodeSeriesId?.equals(AppConstants.CUSTOM, ignoreCase = true) == true) {
                    enveuVideoItemBean[position]?.customDataV3?.episodeSeriesId?.toInt()?.let {
                        ActivityLauncher.getInstance().seriesDetailScreen(
                            context as BaseActivity, SeriesDetailActivity::class.java,
                            it, assetType, isContinueWatching
                        )
                    }
                } else {
                    verifyTrailerCondition(
                        context,
                        externalRefId,
                        id,
                        title,
                        assetType,
                        posterURL,
                        sku, enveuVideoItemBean
                    )
                }

            } else if (pageType.equals(AppConstants.Trailer, ignoreCase = true)) {
                if (trailerRefId?.isNotEmpty() == true) {
                    TrailerRedirection.getInstance().getTrailerParentContent(trailerRefId, context)
                } else {
                    verifyTrailerCondition(
                        context,
                        externalRefId,
                        id,
                        title,
                        pageType,
                        posterURL,
                        sku, enveuVideoItemBean
                    )
                }
            }
        }

        fun setAlbumFragment(
            context: Context?,
            contentSlug: String?,
            assetType: String?,
            assertID: String?
        ) {
            val albumFragment = AlbumFragment()
            val bundle = Bundle()
            bundle.putString(AppConstants.BUNDLE_CONTENT_SLUG, contentSlug)
            bundle.putString(AppConstants.BUNDLE_ASSET_TYPE, assetType)
            bundle.putString(AppConstants.BUNDLE_ASSET_ID, assertID)
            albumFragment.arguments = bundle
            (context as FragmentActivity).supportFragmentManager.beginTransaction()
                .add(R.id.content_frame, albumFragment, AppConstants.TAG_ALBUM_FRAGMENT)
                .addToBackStack(null)
                .commit()
        }

        private fun setSingleFragment(
            context: Context?,
            contentSlug: String?,
            assetType: String?,
            assertID: String
        ) {
            val singlesDetailFragment = SinglesDetailFragment()
            val bundle = Bundle()
            bundle.putString(AppConstants.BUNDLE_CONTENT_SLUG, contentSlug)
            bundle.putString(AppConstants.BUNDLE_ASSET_TYPE, assetType)
            bundle.putString(AppConstants.BUNDLE_ASSET_ID, assertID)
            singlesDetailFragment.arguments = bundle
            (context as FragmentActivity).supportFragmentManager.beginTransaction()
                .add(R.id.content_frame, singlesDetailFragment, AppConstants.TAG_SINGLE_FRAGMENT)
                .addToBackStack(null)
                .commit()
        }

        @JvmStatic
        fun getImages(images: MutableList<ImagesItem>): String {
            var imageUrl = ""
            images.forEach { imagesItem ->
                if (imagesItem.imageContent?.imageType.equals("1x1")) {
                    imageUrl = imagesItem.imageContent.src
                    return@forEach
                }
            }
            return imageUrl
        }

        @JvmStatic
        fun getSongsPosterImageUrl(videoItem: DataItem?): String? {
            var finalUrl = ""
            try {
                if (videoItem?.images != null) {
                    finalUrl = getImages(videoItem.images)
                } else if (videoItem != null && videoItem.customData != null && videoItem.customData.songsAlbumsId != null && videoItem.customData.songsAlbumsId.images != null && videoItem.customData.songsAlbumsId.images.size > 0) {
                    // Ensure getImages() is not null and has items before accessing the list
                    if (videoItem.customData.songsAlbumsId.images != null && videoItem.customData.songsAlbumsId.images.isNotEmpty()
                    ) {
                        videoItem.customData.songsAlbumsId.images.forEachIndexed { index, imagesItem ->
                            if (imagesItem.imageType.equals("1x1")) {
                                finalUrl = imagesItem.src
                                return@forEachIndexed
                            }
                        }
                    }
                }
            } catch (ignored: java.lang.Exception) {
                Log.d("Images", "SongsPosterUrl: $finalUrl")
            }
            Log.d("Images", "SongsPosterUrl: $finalUrl")
            return finalUrl
        }


        fun setArtistFragment(
            context: Context?,
            contentSlug: String?,
            assetType: String?,
            assertID: Int?
        ) {
            val artistFragment = ArtistFragment()
            val bundle = Bundle()
            bundle.putString(AppConstants.BUNDLE_CONTENT_SLUG, contentSlug)
            bundle.putString(AppConstants.BUNDLE_ASSET_TYPE, assetType)
            bundle.putString(AppConstants.BUNDLE_ASSET_ID, assertID.toString())
            artistFragment.arguments = bundle
            (context as FragmentActivity).supportFragmentManager.beginTransaction()
                .add(R.id.content_frame, artistFragment, AppConstants.TAG_ARTIST_FRAGMENT)
                .addToBackStack(null)
                .commit()
        }

        private fun checkForDetailPage(
            mediaConfig: MediaConfig?,
            id: Int?,
            externalRefId: String?,
            title: String?,
            assetType: String?,
            posterURL: String?,
            context: Context,
            pageType: String?,
            trailerRefId: String?,
            contentSlug: String?,
            enveuVideoItemBean: List<EnveuVideoItemBean>,
            sku: String?,
            position: Int,
            isContinueWatching: Boolean
        ) {
            mediaConfig?.let {
                val isDetailPage = mediaConfig.detailPage.isDetailPage
                if (isDetailPage) {
                    mediaTypeRedirection(
                        id,
                        externalRefId,
                        title,
                        assetType,
                        posterURL,
                        pageType,
                        context,
                        trailerRefId,
                        contentSlug,
                        sku,
                        enveuVideoItemBean,
                        position,
                        isContinueWatching
                    )
                } else {
                    if (pageType.equals(AppConstants.SONGS, ignoreCase = true)) {
                        if (enveuVideoItemBean[position].customDataV3 != null && enveuVideoItemBean[position].customDataV3.songs_albums_id != null) {
                            setAlbumFragment(
                                context,
                                enveuVideoItemBean[position].customDataV3.songs_albums_id.contentSlug,
                                enveuVideoItemBean[position].customDataV3.songs_albums_id.mediaType,
                                enveuVideoItemBean[position].customDataV3.songs_albums_id.id.toString()
                            )
                        } else {
                            Logger.d("ContextInstanceOf", Gson().toJson(enveuVideoItemBean))
                            if (context is HomeActivity) {
                                context.playSongs(enveuVideoItemBean[position])
                            }
                        }
                    } else if (pageType.equals(AppConstants.SINGLES, ignoreCase = true)) {
                        if (context is HomeActivity) {
                            context.playSongs(enveuVideoItemBean[position])
                        }
                    }
                }
            }


        }

        private fun checkLoggedInAndUserVerifyCondition(loggedIn: String): Boolean {
            var isLoggedIn = false
            val preference: KsPreferenceKeys = KsPreferenceKeys.getInstance()
            if (loggedIn.equals(AppConstants.LOGGED_IN, ignoreCase = true)) {
                if (preference.appPrefLoginStatus.equals(
                        AppConstants.UserStatus.Login.toString(),
                        ignoreCase = true
                    )
                ) {
                    isLoggedIn = true
                }
            } else if (loggedIn.equals(AppConstants.USER_VERIFY, ignoreCase = true)) {
                isLoggedIn = java.lang.Boolean.parseBoolean(preference.isVerified)
            }
            return isLoggedIn
        }

        private fun verifyTrailerCondition(
            context: Context,
            externalRefId: String?,
            id: Int?,
            tittle: String?,
            pageType: String?,
            posterUrl: String?,
            sku: String?,
            enveuVideoItemBean: List<EnveuVideoItemBean>
        ) {
            try {
                val stringsHelper = StringsHelper
                var trailerUrl = ""
                if (!externalRefId.isNullOrEmpty()) {
                    trailerUrl =  externalRefId
                    /*  if (checkLoggedInAndUserVerifyCondition(AppConstants.USER_VERIFY)) {*/
                    startPlayer(
                        context,
                        trailerUrl,
                        true,
                        id,
                        false,
                        tittle,
                        pageType,
                        posterUrl,
                        externalRefId, enveuVideoItemBean
                    )
                } else {
                    val preference: KsPreferenceKeys = KsPreferenceKeys.getInstance()
                    var isLoggedIn = false
                    val token: String = preference.appPrefAccessToken
                    if (preference.appPrefLoginStatus.equals(
                            AppConstants.UserStatus.Login.toString(),
                            ignoreCase = true
                        )
                    ) {
                        isLoggedIn = true
                    }
                    if (isLoggedIn) {
                        APIServiceLayer.getInstance()
                            .hitApiEntitlement(token, sku, object : ApiInterface {
                                override fun onSuccess(response: ResponseEntitle) {
                                    if (response.data != null) {
                                        if (response.data.entitled) {
                                            APIServiceLayer.getInstance().getPlayableID(
                                                response.data?.accessToken,
                                                response.data.sku,
                                                object :
                                                    PlayableCallBack {
                                                    override fun onSuccess(response: DRM) {
                                                        response.data?.externalRefId?.let { externalRefId1 ->
                                                            externalRefId1
                                                            startPlayer(
                                                                context,
                                                                trailerUrl,
                                                                true,
                                                                id,
                                                                false,
                                                                tittle,
                                                                pageType,
                                                                posterUrl,
                                                                externalRefId1, enveuVideoItemBean
                                                            )
                                                        }
                                                    }

                                                    override fun onFailure() {
                                                        Toast.makeText(
                                                            context,
                                                            context.getString(R.string.something_went_wrong),
                                                            Toast.LENGTH_LONG
                                                        ).show()
                                                    }
                                                })
                                        } else {
                                            Toast.makeText(
                                                context,
                                                context.getString(R.string.no_Subscription_managment),
                                                Toast.LENGTH_LONG
                                            ).show()
                                        }

                                    } else {
                                        Toast.makeText(
                                            context,
                                            context.getString(R.string.something_went_wrong),
                                            Toast.LENGTH_LONG
                                        ).show()

                                    }
                                }

                                override fun onFailure() {
                                    Toast.makeText(
                                        context,
                                        context.getString(R.string.something_went_wrong),
                                        Toast.LENGTH_LONG
                                    ).show()
                                }
                            })
                    } else {
                        ActivityLauncher.getInstance()
                            .loginActivity(context as BaseActivity, ActivityLogin::class.java, "")
                    }
                }
            } catch (e: Exception) {
                Log.d("Exception", "verifyTrailerCondition: $e")
            }
        }


        private fun startPlayer(
            context: Context,
            playbackUrl: String?,
            isTrailer: Boolean,
            id: Int?,
            isIntentFromLive: Boolean,
            tittle: String?,
            pageType: String?,
            posterUrl: String?,
            externalRefId: String, enveuVideoItemBean: List<EnveuVideoItemBean>
        ) {
            if (!playbackUrl.isNullOrEmpty()) {
                val contentMetaData = ContentMetaData()
                contentMetaData.playBackUrl = playbackUrl
                // contentMetaData.seasonCount = seasonCount
                //  contentMetaData.videoType = "videoType"
                contentMetaData.contentTitle = tittle
                contentMetaData.isLive = false
                contentMetaData.contentType = pageType
                contentMetaData.mediaType = pageType
                contentMetaData.contentId = id
                val intent = Intent(context, PlayerActivity::class.java)
                intent.putExtra(AppConstants.CONTENT_META_DATA, contentMetaData)
                context.startActivity(intent)

            } else {
                createShowDialog(
                    "",
                    context.getString(R.string.something_went_wrong),
                    context.getString(R.string.countinue),
                    context
                )
            }
        }

        @JvmStatic
        fun updateLanguage(language: String?, context: Context) {
            Logger.w("selectedLang--in", language!!)
            val locale = Locale(language)
            Locale.setDefault(locale)
            val res = context.resources
            val config = Configuration(res.configuration)
            config.locale = locale
            config.setLayoutDirection(locale)
            res.updateConfiguration(config, res.displayMetrics)
        }

        fun getPushToken(activity: Activity) {
            mActivity = WeakReference<Activity>(activity)
            FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener<String?> { task: Task<String?> ->
                    if (!task.isSuccessful) {
                        //Could not get FirebaseMessagingToken
                        return@OnCompleteListener
                    }
                    if (null != task.result) {
                        //Got FirebaseMessagingToken
                        val firebaseMessagingToken = Objects.requireNonNull(task.result)
                        KsPreferenceKeys.getInstance().appPrefFcmToken = firebaseMessagingToken
                        Logger.w("FCM_TOKEN", KsPreferenceKeys.getInstance().appPrefFcmToken)


                        // Log and toast
                        // String msg = getString(R.string.msg_token_fmt, token);
                        //Logger.d(TAG, msg);
                        //  Toast.makeText(MainActivity.this, msg, Toast.LENGTH_SHORT).show();
                    }
                })
            /*  FirebaseInstanceId.getInstance().getInstanceId()
                .addOnCompleteListener(new OnCompleteListener<InstanceIdResult>() {
                    @Override
                    public void onComplete(@NonNull Task<InstanceIdResult> task) {
                        if (!task.isSuccessful()) {
                            return;
                        }

                        // Get new Instance ID token
                        String token = task.getResult().getToken();
                        KsPreferenceKeys.getInstance().setAppPrefFcmToken(token);
                        Logger.w("FCM_TOKEN", KsPreferenceKeys.getInstance().getAppPrefFcmToken());


                    }
                });*/
        }

        fun showPopupMenu(
            context: Context?,
            view: View?,
            menuItems: Int,
            onMenuItemClickListener: PopupMenu.OnMenuItemClickListener
        ) {
            val popup = PopupMenu(context!!, view!!)
            popup.setOnMenuItemClickListener { item: MenuItem? ->
                onMenuItemClickListener.onMenuItemClick(
                    item
                )
            }
            popup.inflate(menuItems)
            popup.show()
        }

        @JvmStatic
        fun createManualHeroItem(
            enveuVideoItemBean: EnveuVideoItemBean, enveuVideoDetails: EnveuVideoDetails
        ) {
            enveuVideoItemBean.brightcoveVideoId = enveuVideoDetails.brightcoveContentId
            enveuVideoItemBean.assetType = enveuVideoDetails.contentType
            enveuVideoItemBean.customContent = enveuVideoDetails.customContent
            enveuVideoItemBean.title = enveuVideoDetails.title
            enveuVideoItemBean.id = enveuVideoDetails.id
            enveuVideoItemBean.contentSlug = enveuVideoDetails.contentSlug

            if (enveuVideoItemBean.assetType == AppConstants.CUSTOM) {
                enveuVideoItemBean.customContent = enveuVideoDetails.customContent
                enveuVideoItemBean.customType = enveuVideoDetails.customContent.customType
            }
            if (enveuVideoItemBean.assetType == AppConstants.VIDEO) {
                enveuVideoItemBean.videoDetails = enveuVideoDetails.video
                enveuVideoItemBean.contentType = enveuVideoDetails.video.videoType
            }
            if (enveuVideoItemBean.assetType == AppConstants.PERSON) {
                enveuVideoItemBean.contentType = enveuVideoDetails?.personContent?.personType
                enveuVideoItemBean.customType = enveuVideoDetails?.customContent?.customType
            }

            if (enveuVideoItemBean.assetType == AppConstants.AUDIO) {
                enveuVideoItemBean.contentType = enveuVideoDetails.audioContent.audioType
                // enveuVideoItemBean.customType = enveuVideoDetails.customContent.customType
            }
        }


        fun getDominantColor(imageContent: ImageContent?): String {
            if (!imageContent?.dominantColor.isNullOrEmpty()) {
                return imageContent?.dominantColor.toString()
            } else {
                if (!imageContent?.colorPalette.isNullOrEmpty() && !imageContent?.colorPalette?.get(
                        0
                    ).isNullOrEmpty() && !imageContent?.dominantColor.isNullOrEmpty()
                ) {
                    return imageContent?.dominantColor.toString()
                } else {
                    return "#b76f33"
                }
            }
        }


        fun getDominantColor(imageContent: SearchGenres.Data.Item.Image.ImageContent?): String {
            if (!imageContent?.dominantColor.isNullOrEmpty()) {
                return imageContent?.dominantColor.toString()
            } else {
                if (!imageContent?.colorPalette.isNullOrEmpty() && !imageContent?.colorPalette?.get(
                        0
                    ).isNullOrEmpty() && !imageContent?.dominantColor.isNullOrEmpty()
                ) {
                    return imageContent?.dominantColor.toString()
                } else {
                    return "#b76f33"
                }
            }
        }

        fun getDominantColorForPlaylistDetail(imageContent: ImageContent?): String {
            if (!imageContent?.dominantColor.isNullOrEmpty()) {
                return imageContent?.dominantColor.toString()
            } else {
                if (!imageContent?.colorPalette.isNullOrEmpty() && !imageContent?.colorPalette?.get(
                        0
                    ).isNullOrEmpty() && !imageContent?.dominantColor.isNullOrEmpty()
                ) {
                    return imageContent?.dominantColor.toString()
                } else {
                    return "#121210"
                }
            }
        }

        @JvmStatic
        fun getEnvAssetDetail(
            railCommonData: RailCommonData,
            response: com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetailsBean
        ) {
            try {
                val enveuVideoDetailsBean = com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetailsBean()
                val enveuVideoDetails = response.data
                enveuVideoDetailsBean.data = enveuVideoDetails
                val enveuVideoItemBean = EnveuVideoItemBean(enveuVideoDetailsBean)
                railCommonData.enveuVideoItemBeans = ArrayList<EnveuVideoItemBean>()
                railCommonData.enveuVideoItemBeans.add(enveuVideoItemBean)
            } catch (ex: Exception) {
                Logger.w(ex)
            }
        }

        @JvmStatic
        fun getEnvAssetDetail(railCommonData: RailCommonData, response: EnveuVideoDetailsBean) {
            try {
                var isMusicEnabled = getAppTypeByFeature()
                val enveuVideoDetailsBean = EnveuVideoDetailsBean()
                val enveuVideoDetails = response.data
                enveuVideoDetailsBean.data = enveuVideoDetails
                val enveuVideoItemBean = EnveuVideoItemBean(enveuVideoDetailsBean, isMusicEnabled)
                railCommonData.enveuVideoItemBeans = ArrayList()
                railCommonData.enveuVideoItemBeans.add(enveuVideoItemBean)
            } catch (ex: Exception) {
                Logger.w(ex)
            }
        }

        private fun getAppTypeByFeature(): Boolean {
            val featureFlag: FeatureFlagModel?
            featureFlag = AppConfigMethod.parseFeatureFlagList()
            if (featureFlag.featureFlag.IS_MUSIC_APP) {
                return featureFlag.featureFlag.IS_MUSIC_APP
            } else {
                return featureFlag.featureFlag.IS_MUSIC_APP
            }
        }

        fun callSocialAction(
            preference: KsPreferenceKeys,
            userInteractionFragment: UserInteractionFragment?
        ) {
            try {
                if (preference.appPrefLoginStatus.equals(
                        AppConstants.UserStatus.Login.toString(), ignoreCase = true
                    ) && userInteractionFragment != null
                ) {
                    if (ActivityTrackers.LIKE.equals(
                            ActivityTrackers.getInstance().action,
                            ignoreCase = true
                        )
                    ) {
                        userInteractionFragment.setToken(preference.appPrefAccessToken)
                        userInteractionFragment.setLikeForAsset()
                        ActivityTrackers.getInstance().setAction("")
                    } else if (ActivityTrackers.WATCHLIST.equals(
                            ActivityTrackers.getInstance().action, ignoreCase = true
                        )
                    ) {
                        userInteractionFragment.setToken(preference.appPrefAccessToken)
                        userInteractionFragment.setWatchListForAsset(2)
                        ActivityTrackers.getInstance().setAction("")
                    }
                }
            } catch (e: Exception) {
                Logger.w(e)
            }
        }

        var lang: String? = null

        @JvmStatic
        val currentTimeStamp: Long
            get() = System.currentTimeMillis() / 1000


        fun formatTimestamp(timestamp: Double): String {
            val dateFormat = SimpleDateFormat("MMM dd, yyyy hh:mm a", Locale.US)
            return dateFormat.format(Date(timestamp.toLong()))
        }


        @JvmStatic
        fun expiryDate(days: Long): String {
            val obj: DateFormat = SimpleDateFormat("dd,MMM,yyyy")
            val date = Date(days)
            return obj.format(date)
        }

        fun getTodaysDifference(completionDate: String?): Int {
            var diff = -1
            try {
                val date =
                    SimpleDateFormat("yyyy/MM/dd HH:mm:ss", Locale.getDefault()).format(Date())
                val date1: Date
                val date2: Date
                val dates = SimpleDateFormat("yyyy/MM/dd HH:mm:ss")

                //Setting dates
                date1 = dates.parse(date)
                date2 = dates.parse(completionDate)
                diff = if (date1.before(date2)) {
                    //Logger.d("DTGLogs", "DBdayDifference-->>if" + date1 + "  " + date2);
                    -1
                } else {
                    // Logger.d("DTGLogs", "DBdayDifference-->>else" + date1 + "  " + date2);
                    1
                }
            } catch (e: Exception) {
                Logger.w(e)
            }
            return diff
        }

        @JvmStatic
        val configResponse: ConfigBean
            get() {
                val gson = Gson()
                val json: String = KsPreferenceKeys.getInstance().getString("DMS_Response", "")
                return gson.fromJson(json, ConfigBean::class.java)
            }

        fun setConfigConstant(configResponse: ConfigBean?, isTablet: Boolean) {
            SDKConfig.getInstance().setConfigObject(configResponse, isTablet)
        }

        @JvmStatic
        fun getCheckBCID(brightcoveVideoId: String?): Boolean {
            return brightcoveVideoId != null && !"".equals(brightcoveVideoId, ignoreCase = true)
        }

        @JvmStatic
        fun handleTags(
            isVIPTag: String,
            isNewS: String,
            isVIP: FrameLayout,
            newSeries: FrameLayout,
            newEpisode: FrameLayout,
            newMovie: FrameLayout,
            assetType: String
        ) {
            try {
                newEpisode.visibility = View.GONE
                if (isVIPTag.equals("true", ignoreCase = true)) {
                    isVIP.visibility = View.VISIBLE
                } else {
                    isVIP.visibility = View.GONE
                }
                if (assetType.equals(MediaTypeConstants.getInstance().series, ignoreCase = true)) {
                    if (isNewS.equals("true", ignoreCase = true)) {
                        newSeries.visibility = View.VISIBLE
                    } else {
                        newSeries.visibility = View.GONE
                    }
                } else {
                    newSeries.visibility = View.GONE
                }
                if (assetType.equals(MediaTypeConstants.getInstance().movie, ignoreCase = true)) {
                    if (isNewS.equals("true", ignoreCase = true)) {
                        newMovie.visibility = View.VISIBLE
                    } else {
                        newMovie.visibility = View.GONE
                    }
                } else {
                    newMovie.visibility = View.GONE
                }
                if (assetType.equals(MediaTypeConstants.getInstance().episode, ignoreCase = true)) {
                    if (isNewS.equals("true", ignoreCase = true)) {
                        newMovie.visibility = View.VISIBLE
                    } else {
                        newMovie.visibility = View.GONE
                    }
                } else {
                    newEpisode.visibility = View.GONE
                }
            } catch (e: Exception) {
                Logger.w(e)
            }
        }

        fun createNotificationObject(notid: String?, assetType: String?): JSONObject {
            val jsonObject = JSONObject()
            try {
                jsonObject.put(CONTROL_PARAM_CONTENT_TYPE, assetType)
                jsonObject.put(CONTROL_PARAM_ID, notid)
            } catch (e: Exception) {
                Logger.w(e)
            }
            return jsonObject
        }

        fun openUrl(context: Context, url: String?) {
            val i = Intent(Intent.ACTION_VIEW)
            i.data = Uri.parse(url)
            if (i.resolveActivity(context.packageManager) != null) {
                context.startActivity(i)
            }
        }

        @JvmStatic
        fun handleTitleDesc(
            titleLayout: RelativeLayout,
            tvTitle: TextView,
            tvDescription: TextView,
            baseCategory: BaseCategory?
        ) {
            try {
                if (baseCategory != null) {
                    if (RailCardType.IMAGE_ONLY.name.equals(
                            baseCategory.railCardType,
                            ignoreCase = true
                        )
                    ) {
                        titleLayout.visibility = View.GONE
                    } else {
                        //titleLayout.setVisibility(View.VISIBLE);
                        if (RailCardType.IMAGE_TITLE.name.equals(
                                baseCategory.railCardType,
                                ignoreCase = true
                            )
                        ) {
                            titleLayout.visibility = View.VISIBLE
                            tvTitle.visibility = View.VISIBLE
                        } else {
                            if (RailCardType.IMAGE_TITLE_DESC.name.equals(
                                    baseCategory.railCardType,
                                    ignoreCase = true
                                )
                            ) {
                                titleLayout.visibility = View.VISIBLE
                                tvTitle.visibility = View.VISIBLE
                                tvDescription.visibility = View.GONE
                            } else {
                                titleLayout.visibility = View.GONE
                                tvTitle.visibility = View.GONE
                                tvDescription.visibility = View.GONE
                            }
                        }
                    }
                }
            } catch (ignored: Exception) {
                titleLayout.visibility = View.GONE
                tvTitle.visibility = View.GONE
                tvDescription.visibility = View.GONE
            }
        }

        fun getProfileUserName(userProfileResponse: UserProfileResponse?): String {
            var name = ""
            if (userProfileResponse != null && userProfileResponse.data.name != null && !userProfileResponse.data.name.equals(
                    "",
                    ignoreCase = true
                )
            ) {
                name = userProfileResponse.data.name
            }
            return name
        }

        fun getProfileUserNumber(userProfileResponse: UserProfileResponse?): String {
            var number = ""
            if (userProfileResponse != null && userProfileResponse.data.phoneNumber != null && !(userProfileResponse.data.phoneNumber as String).equals(
                    "",
                    ignoreCase = true
                )
            ) {
                number = userProfileResponse.data.phoneNumber as String
            }
            return number
        }

        fun getProfileUserGender(userProfileResponse: UserProfileResponse?): String {
            var gender = ""
            if (userProfileResponse != null && userProfileResponse.data.gender != null && !(userProfileResponse.data.gender as String).equals(
                    "",
                    ignoreCase = true
                )
            ) {
                gender = userProfileResponse.data.gender as String
            }
            return gender
        }

        fun getProfileUserDOB(userProfileResponse: UserProfileResponse?): String {
            var dob = ""
            if (userProfileResponse != null && userProfileResponse.data.dateOfBirth != null) {
                val longVv = userProfileResponse.data.dateOfBirth as Double
                val df = DecimalFormat("#")
                df.maximumFractionDigits = 0
                val ll = df.format(longVv).toLong()
                dob = ll.toString()
            }
            return dob
        }

        fun getProfileUserAddress(userProfileResponse: UserProfileResponse?): String {
            var address = ""
            if (userProfileResponse != null && userProfileResponse.data.customData != null && userProfileResponse.data.customData.address != null && !userProfileResponse.data.customData.address.equals(
                    "",
                    ignoreCase = true
                )
            ) {
                address = userProfileResponse.data.customData.address
            }
            return address
        }

        fun createPrefrenceList(newObject: UserProfileResponse): List<String?> {
            var strings: List<String?> = ArrayList()
            if (newObject.data.customData != null && newObject.data.customData.contentPreferences != null) {
                strings = Arrays.asList(
                    *newObject.data.customData.contentPreferences.split("\\s*,\\s*".toRegex())
                        .dropLastWhile { it.isEmpty() }.toTypedArray()
                )
            }
            return strings
        }

        fun check(identifier: String, saved: List<String>): Boolean {
            var contains = false
            for (i in saved.indices) {
                Logger.d("savedata6: " + saved[i] + "-" + identifier + "    " + i)
                if (saved[i].equals(identifier, ignoreCase = true)) {
                    contains = true
                    break
                } else {
                    contains = false
                }
            }
            return contains
        }

        @JvmStatic
        fun createFilterGenreList(selectedGenres: String?): List<String?> {
            var strings: List<String?> = ArrayList()
            if (selectedGenres != null && !selectedGenres.equals("", ignoreCase = true)) {
                strings = Arrays.asList(
                    *selectedGenres.split("\\s*,\\s*".toRegex()).dropLastWhile { it.isEmpty() }
                        .toTypedArray()
                )
            }
            return strings
        }

        fun createFilterSortList(selectedGenres: String?): List<String?> {
            var strings: List<String?> = ArrayList()
            if (selectedGenres != null && !selectedGenres.equals("", ignoreCase = true)) {
                strings = Arrays.asList(
                    *selectedGenres.split("\\s*,\\s*".toRegex()).dropLastWhile { it.isEmpty() }
                        .toTypedArray()
                )
            }
            return strings
        }

        @JvmStatic
        fun resetFilter(context: Context?) {
            KsPreferenceKeys.getInstance().saveDataGenre(null)
            KsPreferenceKeys.getInstance().saveDataGenreKeyValue(null)
            KsPreferenceKeys.getInstance().saveDataSort(null)
            KsPreferenceKeys.getInstance().saveDataSortKeyValue(null)
            KsPreferenceKeys.getInstance().saveDataFeature(null)
            KsPreferenceKeys.getInstance().saveDataFeatureKeyValue(null)
            KsPreferenceKeys.getInstance().filterApply = "false"
            KsPreferenceKeys.getInstance().filterGenre = 0
        }

        @JvmStatic
        fun getMultilingualTitle(
            currentLang: String?,
            multilingualTitle: JsonObject,
            englishCode: String?,
            arabicCode: String?
        ): String {
            var name = ""
            try {
                if (KsPreferenceKeys.getInstance().appLanguage.equals(
                        AppConstants.ENGLISH_LAN_CODE,
                        ignoreCase = true
                    )
                ) {
                    for ((key, value) in multilingualTitle.entrySet()) {
                        if (key.equals(englishCode, ignoreCase = true)) {
                            name = value.asString
                            break
                        }
                    }
                } else {
                    for ((key, value) in multilingualTitle.entrySet()) {
                        if (key.equals(arabicCode, ignoreCase = true)) {
                            name = value.asString
                            break
                        }
                    }
                }
            } catch (ignored: Exception) {
                name = ""
            }
            return name
        }

        /**
         * redirectionLogic method will call on click home rail only
         * on the basis of screenType will check isDetailPage is true or not if its true will check further condition
         * item will redirect to which detail page will decide on the basis of media type which are defined in local json file (media-type-mapping)
         * if media type is trailer it will play directly
         * */
        @JvmStatic
        fun redirectionLogic(
            context: Context,
            railCommonData: RailCommonData,
            position: Int,
            widgetId: String?,
            playListId: String?,
            playListName: String
        ) {
            try {
                if (railCommonData.enveuVideoItemBeans[position].assetType.equals(AppConstants.VIDEO, ignoreCase = true)) {
                    AnalyticsUtils.logContentSelectEvent(
                        context,
                        widgetId,
                        playListName,
                        playListId,
                        railCommonData.enveuVideoItemBeans[position].id.toString(),
                        railCommonData.enveuVideoItemBeans[position].title,
                        railCommonData.enveuVideoItemBeans[position].videoDetails.videoType
                    )

                    checkForContentType(
                        railCommonData.enveuVideoItemBeans[position].videoDetails.videoType,
                        railCommonData.enveuVideoItemBeans[position].id,
                        railCommonData.enveuVideoItemBeans[position].externalRefId ?: "",
                        railCommonData.enveuVideoItemBeans[position].title,
                        railCommonData.enveuVideoItemBeans[position].assetType,
                        railCommonData.enveuVideoItemBeans[position].posterURL,
                        context,
                        railCommonData.enveuVideoItemBeans[position].trailerReferenceId ?: "",
                        railCommonData.enveuVideoItemBeans[position].contentSlug ?: "",
                        railCommonData.enveuVideoItemBeans,
                        railCommonData.enveuVideoItemBeans[position].sku,
                        position,
                        railCommonData.isContinueWatching
                    )
                } else if (railCommonData.enveuVideoItemBeans[position].assetType.equals(
                        AppConstants.CUSTOM
                    )
                ) {
                    AnalyticsUtils.logContentSelectEvent(
                        context,
                        widgetId,
                        playListName,
                        playListId,
                        railCommonData.enveuVideoItemBeans[position].id.toString(),
                        railCommonData.enveuVideoItemBeans[position].title,
                        railCommonData.enveuVideoItemBeans[position].customType
                    )
                    checkForContentType(
                        railCommonData.enveuVideoItemBeans[position].customType,
                        railCommonData.enveuVideoItemBeans[position].id,
                        railCommonData.enveuVideoItemBeans[position].externalRefId ?: "",
                        railCommonData.enveuVideoItemBeans[position].title,
                        railCommonData.enveuVideoItemBeans[position].assetType,
                        railCommonData.enveuVideoItemBeans[position].posterURL,
                        context,
                        railCommonData.enveuVideoItemBeans[position].trailerReferenceId ?: "",
                        railCommonData.enveuVideoItemBeans[position].contentSlug ?: "",
                        railCommonData.enveuVideoItemBeans,
                        railCommonData.enveuVideoItemBeans[position].sku,
                        position,
                        railCommonData.isContinueWatching
                    )
                } else if (railCommonData.enveuVideoItemBeans[position].assetType.equals(
                        AppConstants.LIVE
                    )
                ) {
                    AnalyticsUtils.logContentSelectEvent(
                        context,
                        widgetId,
                        playListName,
                        playListId,
                        railCommonData.enveuVideoItemBeans[position].id.toString(),
                        railCommonData.enveuVideoItemBeans[position].title,
                        railCommonData.enveuVideoItemBeans[position].liveContent.liveType
                    )
                    checkForContentType(
                        railCommonData.enveuVideoItemBeans[position].liveContent.liveType!!,
                        railCommonData.enveuVideoItemBeans[position].id,
                        railCommonData.enveuVideoItemBeans[position].externalRefId ?: "",
                        railCommonData.enveuVideoItemBeans[position].title,
                        railCommonData.enveuVideoItemBeans[position].assetType,
                        railCommonData.enveuVideoItemBeans[position].posterURL,
                        context,
                        railCommonData.enveuVideoItemBeans[position].trailerReferenceId ?: "",
                        railCommonData.enveuVideoItemBeans[position].contentSlug ?: "",
                        railCommonData.enveuVideoItemBeans,
                        railCommonData.enveuVideoItemBeans[position].sku,
                        position,
                        railCommonData.isContinueWatching
                    )
                } else if (railCommonData.enveuVideoItemBeans[position].assetType.equals(
                        AppConstants.AUDIO
                    )
                ) {
                    AnalyticsUtils.logContentSelectEvent(
                        context,
                        widgetId,
                        playListName,
                        playListId,
                        railCommonData.enveuVideoItemBeans[position].id.toString(),
                        railCommonData.enveuVideoItemBeans[position].title,
                        railCommonData.enveuVideoItemBeans[position].contentType
                    )
                    checkForContentType(
                        railCommonData.enveuVideoItemBeans[position].contentType,
                        railCommonData.enveuVideoItemBeans[position].id,
                        railCommonData.enveuVideoItemBeans[position].externalRefId ?: "",
                        railCommonData.enveuVideoItemBeans[position].title,
                        railCommonData.enveuVideoItemBeans[position].assetType,
                        railCommonData.enveuVideoItemBeans[position].posterURL,
                        context,
                        railCommonData.enveuVideoItemBeans[position].trailerReferenceId ?: "",
                        railCommonData.enveuVideoItemBeans[position].contentSlug ?: "",
                        railCommonData.enveuVideoItemBeans,
                        railCommonData.enveuVideoItemBeans[position].sku,
                        position,
                        railCommonData.isContinueWatching
                    )
                } else if (railCommonData.enveuVideoItemBeans[position].assetType.equals(
                        AppConstants.ARTICLE
                    )
                ) {
                    if (!isNullOrEmpty(railCommonData.enveuVideoItemBeans[position].articleContent.articleBody)) {
                        AnalyticsUtils.logContentSelectEvent(
                            context,
                            widgetId,
                            playListName,
                            playListId,
                            railCommonData.enveuVideoItemBeans[position].id.toString(),
                            railCommonData.enveuVideoItemBeans[position].title,
                            railCommonData.enveuVideoItemBeans[position].articleContent.articleType
                        )
                        context.startActivity(
                            Intent(context, WebViewActivity::class.java).putExtra(
                                AppConstants.ARTICLE_CONTENT_URL,
                                railCommonData.enveuVideoItemBeans[position].articleContent.articleBody
                            )
                                .putExtra(
                                    AppConstants.TITLE,
                                    railCommonData.enveuVideoItemBeans[position].title
                                )
                        )
                    }
                }

            } catch (ex: Exception) {
                Logger.w(ex)
            }

        }

        fun checkForContentType(
            videoType: String?,
            id: Int?,
            externalRefId: String?,
            title: String?,
            assetType: String?,
            posterURL: String?,
            context: Context,
            trailerRefId: String? = "",
            contentSlug: String?,
            enveuVideoItemBean: List<EnveuVideoItemBean>,
            sku: String?, position: Int, continueWatching: Boolean
        ) {
            if (id != null) {
                redirectOnDetailPages(
                    context,
                    id,
                    externalRefId,
                    title,
                    assetType,
                    posterURL,
                    videoType,
                    trailerRefId,
                    contentSlug,
                    enveuVideoItemBean,
                    sku,
                    position,
                    continueWatching
                )
            }
        }

        fun getDecodedJwt(jwt: String): String {
            val result = StringBuilder()
            val parts = jwt.split("[.]".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            try {
                var index = 0
                for (part in parts) {
                    if (index >= 2) break
                    index++
                    val decodedBytes =
                        Base64.decode(part.toByteArray(charset("UTF-8")), Base64.URL_SAFE)
                    if (index != 0) {
                        result.append(" " + String(decodedBytes, charset("UTF-8")))
                    }
                }
            } catch (e: Exception) {
                throw RuntimeException("Couldnt decode jwt", e)
            }
            return result.toString()
        }

        fun fetchRecSubscriptionModel(
            from: String,
            responseEntitlementModel: ResponseMembershipAndPlan,
            subSkuList: MutableList<String>,
            productSkuList: MutableList<String>
        ): List<PurchaseModel> {
            val modelList: MutableList<PurchaseModel> = ArrayList<PurchaseModel>()
            try {
                if (from == AppConstants.SETTINGS) {
                    if (responseEntitlementModel.data != null && responseEntitlementModel.data.isNotEmpty()) {
                        for (i in responseEntitlementModel.data.indices) {
                            if (responseEntitlementModel.data[i].offerStatus.equals(
                                    AppConstants.PUBLISHED
                                ) || responseEntitlementModel.data[i].offerStatus.equals(
                                    AppConstants.NOT_FOR_SALE
                                )
                            ) {
                                val model =
                                    PurchaseModel()
                                if (responseEntitlementModel.data != null && responseEntitlementModel.data[i].offerType != null && responseEntitlementModel.data[i].offerType.contains(
                                        VodOfferType.RECURRING_SUBSCRIPTION.name
                                    )
                                ) {
                                    model.title = responseEntitlementModel.data[i].title
                                    model.isOnHold = responseEntitlementModel.data[i].onHold
                                    model.onGrace = responseEntitlementModel.data[i].onGrace
                                    if (responseEntitlementModel.data[i]?.customData?.androidProductId != null) {
                                        val identifier: String =
                                            responseEntitlementModel.data[i].customData.androidProductId
                                        model.customIdentifier = identifier
                                        subSkuList.add(identifier)
                                    }

                                    if (responseEntitlementModel.data[i]?.isAllowedTrial != null) {
                                        val allowedTrial: Boolean =
                                            responseEntitlementModel.data[i].isAllowedTrial
                                        model.allowedTrial = allowedTrial
                                    }
                                    model.identifier =
                                        responseEntitlementModel.data[i].identifier
                                    if (responseEntitlementModel.data[i].subscriptionOrder != null) {
                                        model.subscriptionOrder =
                                            responseEntitlementModel.data[i].subscriptionOrder
                                    }
                                    model.subscriptionType =
                                        responseEntitlementModel.data[i].offerType
                                    if (responseEntitlementModel.data[i].recurringOffer != null) {
                                        if (responseEntitlementModel.data[i].recurringOffer.trialPeriod != null) {
                                            if (responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialType != null && !responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialType.equals(
                                                    "",
                                                    ignoreCase = true
                                                )
                                            ) {
                                                model.trialType =
                                                    responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialType
                                            }
                                            if (responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialDuration > 0) {
                                                model.trialDuration =
                                                    responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialDuration
                                            }
                                        }
                                        if (responseEntitlementModel.data[i].recurringOffer.offerPeriod != null) {
                                            if (responseEntitlementModel.data[i].recurringOffer.offerPeriod.equals(
                                                    VodOfferType.MONTHLY.name,
                                                    ignoreCase = true
                                                )
                                            ) {
                                                model.offerPeriod = VodOfferType.MONTHLY.name
                                            } else if (responseEntitlementModel.data[i].recurringOffer.offerPeriod.equals(
                                                    VodOfferType.ANNUAL.name,
                                                    ignoreCase = true
                                                )
                                            ) {
                                                model.offerPeriod = VodOfferType.ANNUAL.name
                                            }
                                        }
                                    }
                                    if (responseEntitlementModel.data[i].description != null) {
                                        model.description =
                                            responseEntitlementModel.data[i].description
                                    }
                                    if (responseEntitlementModel.data[i].customData != null) {
                                        model.paymentProvider =
                                            responseEntitlementModel.data[i].customData.paymentProvider
                                        model.title_en =
                                            responseEntitlementModel.data[i].customData.title_en
                                        model.title_es =
                                            responseEntitlementModel.data[i].customData.title_es
                                        model.description_en =
                                            responseEntitlementModel.data[i].customData.description_en
                                        model.description_es =
                                            responseEntitlementModel.data[i].customData.description_es
                                        model.trialType_en =
                                            responseEntitlementModel.data[i].customData.trialType_en
                                        model.trialType_es =
                                            responseEntitlementModel.data[i].customData.trialType_es
                                        model.isCancelled =
                                            responseEntitlementModel.data[i].customData.isCancelled
                                    }

                                    model.subscriptionList = subSkuList
                                    if (responseEntitlementModel.data[i].expiryDate != null) {
                                        model.expiryDate =
                                            responseEntitlementModel.data[i].expiryDate
                                    }
                                    model.entitlementState =
                                        responseEntitlementModel.data[i].entitlementState != null && responseEntitlementModel.data[i].entitlementState
                                    if (responseEntitlementModel.data[i].customData != null) {
                                        model.customData =
                                            responseEntitlementModel.data[i].customData
                                    }
                                    if (responseEntitlementModel.data[i].currentExpiry != null && responseEntitlementModel.data[i].currentExpiry > 0) {
                                        model.currentExpiryDate =
                                            responseEntitlementModel.data[i].currentExpiry
                                    }

                                    if (responseEntitlementModel.data[i].nextChargeDate != null && responseEntitlementModel.data[i].nextChargeDate > 0) {
                                        model.nextChargeDate =
                                            responseEntitlementModel.data[i].nextChargeDate
                                    }
                                    model.isOnTrial = responseEntitlementModel.data[i].isOnTrial
                                    modelList.add(model)
                                } else {
                                    val identifier: String =
                                        responseEntitlementModel.data[i].customData.androidProductId
                                    model.subscriptionType = "PRODUCT"
                                    productSkuList.add(identifier)
                                    if (responseEntitlementModel.data[i].recurringOffer != null) {
                                        if (responseEntitlementModel.data[i].recurringOffer.trialPeriod != null) {
                                            if (responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialType != null && !responseEntitlementModel.data.get(
                                                    i
                                                )
                                                    .recurringOffer.trialPeriod.trialType.equals(
                                                        "",
                                                        ignoreCase = true
                                                    )
                                            ) {
                                                model.trialType =
                                                    responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialType
                                            }
                                            if (responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialDuration > 0) {
                                                model.trialDuration =
                                                    responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialDuration
                                            }
                                        }
                                        if (responseEntitlementModel.data[i].recurringOffer.offerPeriod != null) {
                                            if (responseEntitlementModel.data[i].recurringOffer.offerPeriod.equals(
                                                    VodOfferType.MONTHLY.name,
                                                    ignoreCase = true
                                                )
                                            ) {
                                                model.offerPeriod = VodOfferType.MONTHLY.name
                                            } else if (responseEntitlementModel.data[i].recurringOffer.offerPeriod.equals(
                                                    VodOfferType.ANNUAL.name,
                                                    ignoreCase = true
                                                )
                                            ) {
                                                model.offerPeriod = VodOfferType.ANNUAL.name
                                            }
                                        }
                                    }
                                    val allowedTrial: Boolean =
                                        responseEntitlementModel.data[i].isAllowedTrial
                                    model.allowedTrial = allowedTrial
                                    model.identifier =
                                        responseEntitlementModel.data[i].identifier

                                    if (responseEntitlementModel.data[i].customData != null) {
                                        model.paymentProvider =
                                            responseEntitlementModel.data[i].customData.paymentProvider
                                        model.title_en =
                                            responseEntitlementModel.data[i].customData.title_en
                                        model.title_es =
                                            responseEntitlementModel.data[i].customData.title_es
                                        model.description_en =
                                            responseEntitlementModel.data[i].customData.description_en
                                        model.description_es =
                                            responseEntitlementModel.data[i].customData.description_es
                                        model.trialType_en =
                                            responseEntitlementModel.data[i].customData.trialType_en
                                        model.trialType_es =
                                            responseEntitlementModel.data[i].customData.trialType_es
                                        model.isCancelled =
                                            responseEntitlementModel.data[i].customData.isCancelled

                                    }
                                    model.entitlementState =
                                        responseEntitlementModel.data[i].entitlementState != null && responseEntitlementModel.data[i].entitlementState
                                    if (responseEntitlementModel.data[i].customData != null) {
                                        model.customData =
                                            responseEntitlementModel.data[i].customData
                                    }
                                    model.subscriptionList = productSkuList
                                    if (responseEntitlementModel.data[i].expiryDate != null) {
                                        model.expiryDate =
                                            responseEntitlementModel.data[i].expiryDate
                                    }
                                    if (responseEntitlementModel.data.get(i).subscriptionOrder != null) {
                                        model.subscriptionOrder =
                                            responseEntitlementModel.data[i].subscriptionOrder
                                    }
                                    if (responseEntitlementModel.data[i].description != null) {
                                        model.description =
                                            responseEntitlementModel.data[i].description
                                    }
                                    if (responseEntitlementModel.data[i].currentExpiry != null && responseEntitlementModel.data[i].currentExpiry > 0) {
                                        model.currentExpiryDate =
                                            responseEntitlementModel.data[i].currentExpiry
                                    }
                                    if (responseEntitlementModel.data[i].nextChargeDate != null && responseEntitlementModel.data[i].nextChargeDate > 0) {
                                        model.nextChargeDate =
                                            responseEntitlementModel.data[i].nextChargeDate
                                    }
                                    model.isOnTrial = responseEntitlementModel.data[i].isOnTrial
                                    modelList.add(model)
                                }
                            }

                        }
                    }
                } else {
                    if (responseEntitlementModel.data != null && responseEntitlementModel.data.isNotEmpty()) {
                        for (i in responseEntitlementModel.data.indices) {
                            if (responseEntitlementModel.data[i].offerStatus.equals(
                                    AppConstants.PUBLISHED
                                ) && responseEntitlementModel.data[i].customData.androidProductId != null
                            ) {
                                val model =
                                    PurchaseModel()
                                if (responseEntitlementModel.data != null && responseEntitlementModel.data[i].offerType != null && responseEntitlementModel.data[i].offerType.contains(
                                        VodOfferType.RECURRING_SUBSCRIPTION.name
                                    )
                                ) {
                                    model.title = responseEntitlementModel.data[i].title
                                    val identifier: String =
                                        responseEntitlementModel.data[i].customData.androidProductId
                                    if (responseEntitlementModel.data[i]?.isAllowedTrial != null) {
                                        val allowedTrial: Boolean =
                                            responseEntitlementModel.data[i].isAllowedTrial
                                        model.allowedTrial = allowedTrial
                                    }
                                    model.identifier = responseEntitlementModel.data[i].identifier
                                    model.customIdentifier = identifier
                                    if (responseEntitlementModel.data[i].subscriptionOrder != null) {
                                        model.subscriptionOrder =
                                            responseEntitlementModel.data[i].subscriptionOrder
                                    }
                                    model.subscriptionType =
                                        responseEntitlementModel.data[i].offerType

                                    subSkuList.add(identifier)
                                    if (responseEntitlementModel.data[i].recurringOffer != null) {
                                        if (responseEntitlementModel.data[i].recurringOffer.trialPeriod != null) {
                                            if (responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialType != null && !responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialType.equals(
                                                    "",
                                                    ignoreCase = true
                                                )
                                            ) {
                                                model.trialType =
                                                    responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialType
                                            }
                                            if (responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialDuration > 0) {
                                                model.trialDuration =
                                                    responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialDuration
                                            }
                                        }
                                        if (responseEntitlementModel.data[i].recurringOffer.offerPeriod != null) {
                                            if (responseEntitlementModel.data[i].recurringOffer.offerPeriod.equals(
                                                    VodOfferType.MONTHLY.name,
                                                    ignoreCase = true
                                                )
                                            ) {
                                                model.offerPeriod = VodOfferType.MONTHLY.name
                                            } else if (responseEntitlementModel.data[i].recurringOffer.offerPeriod.equals(
                                                    VodOfferType.ANNUAL.name,
                                                    ignoreCase = true
                                                )
                                            ) {
                                                model.offerPeriod = VodOfferType.ANNUAL.name
                                            }
                                        }
                                    }
                                    if (responseEntitlementModel.data[i].description != null) {
                                        model.description =
                                            responseEntitlementModel.data[i].description
                                    }
                                    if (responseEntitlementModel.data[i].customData != null) {
                                        model.paymentProvider =
                                            responseEntitlementModel.data[i].customData.paymentProvider
                                        model.title_en =
                                            responseEntitlementModel.data[i].customData.title_en
                                        model.title_es =
                                            responseEntitlementModel.data[i].customData.title_es
                                        model.description_en =
                                            responseEntitlementModel.data[i].customData.description_en
                                        model.description_es =
                                            responseEntitlementModel.data[i].customData.description_es
                                        model.trialType_en =
                                            responseEntitlementModel.data[i].customData.trialType_en
                                        model.trialType_es =
                                            responseEntitlementModel.data[i].customData.trialType_es
                                        model.isCancelled =
                                            responseEntitlementModel.data[i].customData.isCancelled
                                    }

                                    model.subscriptionList = subSkuList
                                    if (responseEntitlementModel.data[i].expiryDate != null) {
                                        model.expiryDate =
                                            responseEntitlementModel.data[i].expiryDate
                                    }
                                    model.entitlementState =
                                        responseEntitlementModel.data[i].entitlementState != null && responseEntitlementModel.data[i].entitlementState
                                    if (responseEntitlementModel.data[i].customData != null) {
                                        model.customData =
                                            responseEntitlementModel.data[i].customData
                                    }
                                    if (responseEntitlementModel.data[i].currentExpiry != null && responseEntitlementModel.data[i].currentExpiry > 0) {
                                        model.currentExpiryDate =
                                            responseEntitlementModel.data[i].currentExpiry
                                    }

                                    if (responseEntitlementModel.data[i].nextChargeDate != null && responseEntitlementModel.data[i].nextChargeDate > 0) {
                                        model.nextChargeDate =
                                            responseEntitlementModel.data[i].nextChargeDate
                                    }
                                    model.isOnTrial = responseEntitlementModel.data[i].isOnTrial
                                    modelList.add(model)
                                } else {
                                    val identifier: String =
                                        responseEntitlementModel.data[i].customData.androidProductId
                                    model.subscriptionType = "PRODUCT"
                                    productSkuList.add(identifier)
                                    if (responseEntitlementModel.data[i].recurringOffer != null) {
                                        if (responseEntitlementModel.data[i].recurringOffer.trialPeriod != null) {
                                            if (responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialType != null && !responseEntitlementModel.data.get(
                                                    i
                                                )
                                                    .recurringOffer.trialPeriod.trialType.equals(
                                                        "",
                                                        ignoreCase = true
                                                    )
                                            ) {
                                                model.trialType =
                                                    responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialType
                                            }
                                            if (responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialDuration > 0) {
                                                model.trialDuration =
                                                    responseEntitlementModel.data[i].recurringOffer.trialPeriod.trialDuration
                                            }
                                        }
                                        if (responseEntitlementModel.data[i].recurringOffer.offerPeriod != null) {
                                            if (responseEntitlementModel.data[i].recurringOffer.offerPeriod.equals(
                                                    VodOfferType.MONTHLY.name,
                                                    ignoreCase = true
                                                )
                                            ) {
                                                model.offerPeriod = VodOfferType.MONTHLY.name
                                            } else if (responseEntitlementModel.data[i].recurringOffer.offerPeriod.equals(
                                                    VodOfferType.ANNUAL.name,
                                                    ignoreCase = true
                                                )
                                            ) {
                                                model.offerPeriod = VodOfferType.ANNUAL.name
                                            }
                                        }
                                    }
                                    val allowedTrial: Boolean =
                                        responseEntitlementModel.data[i].isAllowedTrial
                                    model.allowedTrial = allowedTrial
                                    model.identifier = responseEntitlementModel.data[i].identifier

                                    if (responseEntitlementModel.data[i].customData != null) {
                                        model.paymentProvider =
                                            responseEntitlementModel.data[i].customData.paymentProvider
                                        model.title_en =
                                            responseEntitlementModel.data[i].customData.title_en
                                        model.title_es =
                                            responseEntitlementModel.data[i].customData.title_es
                                        model.description_en =
                                            responseEntitlementModel.data[i].customData.description_en
                                        model.description_es =
                                            responseEntitlementModel.data[i].customData.description_es
                                        model.trialType_en =
                                            responseEntitlementModel.data[i].customData.trialType_en
                                        model.trialType_es =
                                            responseEntitlementModel.data[i].customData.trialType_es
                                        model.isCancelled =
                                            responseEntitlementModel.data[i].customData.isCancelled

                                    }
                                    model.entitlementState =
                                        responseEntitlementModel.data[i].entitlementState != null && responseEntitlementModel.data[i].entitlementState
                                    if (responseEntitlementModel.data[i].customData != null) {
                                        model.customData =
                                            responseEntitlementModel.data[i].customData
                                    }
                                    model.subscriptionList = productSkuList
                                    if (responseEntitlementModel.data[i].expiryDate != null) {
                                        model.expiryDate =
                                            responseEntitlementModel.data[i].expiryDate
                                    }
                                    if (responseEntitlementModel.data.get(i).subscriptionOrder != null) {
                                        model.subscriptionOrder =
                                            responseEntitlementModel.data[i].subscriptionOrder
                                    }
                                    if (responseEntitlementModel.data[i].description != null) {
                                        model.description =
                                            responseEntitlementModel.data[i].description
                                    }
                                    if (responseEntitlementModel.data[i].currentExpiry != null && responseEntitlementModel.data[i].currentExpiry > 0) {
                                        model.currentExpiryDate =
                                            responseEntitlementModel.data[i].currentExpiry
                                    }
                                    if (responseEntitlementModel.data[i].nextChargeDate != null && responseEntitlementModel.data[i].nextChargeDate > 0) {
                                        model.nextChargeDate =
                                            responseEntitlementModel.data[i].nextChargeDate
                                    }
                                    model.isOnTrial = responseEntitlementModel.data[i].isOnTrial
                                    modelList.add(model)
                                }
                            }

                        }
                    }
                }

            } catch (e: Exception) {
                return modelList
            }
            return modelList
        }

        fun createPurchaseList(
            purchaseModelList: List<PurchaseModel>?,
            purchases: List<SkuDetails>,
            purchaseFinalList: ArrayList<PurchaseModel?>,
            bp: BillingProcessor
        ): List<PurchaseModel?> {
            var skuDetails: SkuDetails
            for (i in purchases.indices) {
                val identifier: String = purchases[i].sku
                for (j in purchaseModelList!!.indices) {
                    if (identifier.equals(purchaseModelList[j].customIdentifier, ignoreCase = true)) {
                        val purchaseModel = PurchaseModel()
                        skuDetails = bp.getLocalSubscriptionSkuDetail(identifier)
                        purchaseModel.price = "" + skuDetails.price
                        purchaseModel.allowedTrial = purchaseModelList[j].allowedTrial
                        purchaseModel.trialDuration = purchaseModelList[j].trialDuration
                        purchaseModel.isSelected = false
                        purchaseModel.purchaseOptions = purchaseModelList[j].purchaseOptions
                        purchaseModel.offerPeriod = purchaseModelList[j].offerPeriod
                        purchaseModel.identifier = purchaseModelList[j].identifier
                        purchaseModel.customIdentifier = purchaseModelList[j].customIdentifier
                        purchaseModel.currency = skuDetails.priceCurrencyCode
                        purchaseModel.subscriptionOrder = purchaseModelList[j].subscriptionOrder
                        purchaseModel.title = purchaseModelList[j].title
                        purchaseModel.title_en = purchaseModelList[j].title_en
                        purchaseModel.title_es = purchaseModelList[j].title_es
                        purchaseModel.onGrace = purchaseModelList[j].onGrace
                        purchaseModel.isOnHold = purchaseModelList[j].isOnHold
                        purchaseModel.description_en = purchaseModelList[j].description_en
                        purchaseModel.description_nl = purchaseModelList[j].description_nl
                        purchaseModel.description_es = purchaseModelList[j].description_es
                        purchaseModel.trialType_es = purchaseModelList[j].trialType_es
                        purchaseModel.trialType_en = purchaseModelList[j].trialType_en
                        purchaseModel.isCancelled = purchaseModelList[j].isCancelled
                        purchaseModel.description = purchaseModelList[j].description
                        purchaseModel.purchaseSku = purchaseModelList[j].purchaseSku
                        purchaseModel.rentalPeriod = purchaseModelList?.get(j)?.rentalPeriod
                        purchaseFinalList.add(purchaseModel)
                        break
                    }
                }
            }
            return purchaseFinalList
        }

        fun activeBtn(binding: ActivityPurchaseBinding?, color: Int) {
            assert(binding != null)
            binding!!.btnBuy.setBackgroundResource(R.drawable.roundedcornerforbtn)
            binding.txtBtn.setTextColor(color)
        }

        fun activeBtn(binding: ActivitySelectSubscriptionPlanBinding?, color: Int) {
            assert(binding != null)
            binding!!.btnBuy.setBackgroundResource(R.drawable.roundedcornerforbtn)
            binding.txtBtn.setTextColor(color)
        }

        fun activeBtn(binding: ActivityPaymentDetailPagePlanBinding?, color: Int) {
            assert(binding != null)
            binding!!.btnBuy.setBackgroundResource(R.drawable.roundedcornerforbtn)
            binding.txtBtn.setTextColor(color)

        }

        @JvmStatic
        fun getEpisodeAssetDetail(
            railCommonData: RailCommonData,
            response: Response<EnvVideoDetailsBean>
        ) {
            try {
                val enveuVideoDetailsBean = EnvVideoDetailsBean()
                val enveuVideoDetails: Data? = response.body()?.data
                enveuVideoDetailsBean.data = enveuVideoDetails
                val enveuVideoItemBean = EnveuVideoItemBean(enveuVideoDetailsBean.data)
                railCommonData.enveuVideoItemBeans = ArrayList()
                railCommonData.enveuVideoItemBeans.add(enveuVideoItemBean)
            } catch (ex: Exception) {
                Logger.w(ex)
            }
        }

        private fun createShowDialog(
            title: String?,
            message: String?,
            actnBtn: String?,
            context: Context?
        ) {
            val dialog = Dialog(context!!, R.style.FullScreenDialog)
            dialog.setContentView(R.layout.custom_popup)
            val layoutParams: WindowManager.LayoutParams = dialog.window!!.attributes
            dialog.window!!.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.MATCH_PARENT
            )
            dialog.window!!.attributes = layoutParams
            val btu = dialog.findViewById<Button>(R.id.personalizeBtn)
            val titleText: TextView = dialog.findViewById(R.id.popup_title)
            btu.text = actnBtn
            titleText.text = title
            val description: TextView = dialog.findViewById(R.id.popup_discription)
            description.text = message
            dialog.show()
            btu.setOnClickListener {
                if (isUserNotVerify) {
                    dialog.dismiss()
                    ActivityLauncher.getInstance().goToEnterOTP(
                        context as Activity?,
                        EnterOTPActivity::class.java,
                        "DetailPage"
                    )
                } else if (isUserNotEntitle) {
                    dialog.dismiss()
                    ActivityLauncher.getInstance().goToDetailPlanScreen(
                        context as Activity?,
                        PaymentDetailPage::class.java,
                        true,
                        resEntitle
                    )
                } else {
                    dialog.dismiss()
                }
            }
        }

        @JvmStatic
        fun getDateFromTimeStamp(expiryDate: Double): String {
            val date: String
            val formatter = SimpleDateFormat("dd MMMM yyyy")
            date = formatter.format(Date(expiryDate.toLong()))
            Log.w("expiryDate", date)
            return date
        }

        fun createManagePurchaseList(
            purchaseModelList: List<PurchaseModel>?,
            purchases: List<SkuDetails>,
            purchaseFinalList: ArrayList<PurchaseModel?>,
            bp: BillingProcessor
        ): List<PurchaseModel?> {
            var skuDetails: SkuDetails
            for (i in purchases.indices) {
                val identifier: String = purchases[i].sku
                if (purchaseModelList != null) {
                    for (j in purchaseModelList.indices) {
                        if (identifier.equals(
                                purchaseModelList[j].customIdentifier,
                                ignoreCase = true
                            )
                        ) {
                            val purchaseModel = PurchaseModel()
                            skuDetails = bp.getLocalSubscriptionSkuDetail(identifier)
                            purchaseModel.price = "" + skuDetails.price
                            purchaseModel.trialType = "" + purchaseModelList[j].trialType
                            purchaseModel.trialDuration = purchaseModelList[j].trialDuration
                            purchaseModel.isSelected = false
                            purchaseModel.allowedTrial = purchaseModelList[j].allowedTrial
                            purchaseModel.purchaseOptions = purchaseModelList[j].purchaseOptions
                            purchaseModel.offerPeriod = VodOfferType.WEEKLY.name
                            purchaseModel.identifier = purchaseModelList[j].identifier
                            purchaseModel.customIdentifier = purchaseModelList[j].customIdentifier
                            purchaseModel.currency = skuDetails.priceCurrencyCode
                            purchaseModel.createdDate = purchaseModelList[j].createdDate
                            purchaseModel.title = purchaseModelList[j].title
                            purchaseModel.description = purchaseModelList[j].description
                            purchaseModel.description_nl = purchaseModelList[j].description_nl
                            purchaseModel.title_en = purchaseModelList[j].title_en
                            purchaseModel.title_es = purchaseModelList[j].title_es
                            purchaseModel.description_en = purchaseModelList[j].description_en
                            purchaseModel.description_es = purchaseModelList[j].description_es
                            purchaseModel.trialType_es = purchaseModelList[j].trialType_es
                            purchaseModel.trialType_en = purchaseModelList[j].trialType_en
                            purchaseModel.rentalPeriod = purchaseModelList?.get(j)?.rentalPeriod

                            purchaseModel.subscriptionOrder = purchaseModelList[j].subscriptionOrder
                            if (purchaseModelList[j].entitlementState != null && purchaseModelList[j].entitlementState == true) {
                                purchaseModel.entitlementState =
                                    purchaseModelList[j].entitlementState
                                purchaseFinalList.add(purchaseModel)
                            }
                        }
                    }
                }
            }
            return purchaseFinalList
        }

        fun fetchEntitleRecSubscriptionModel(responseEntitlementModel: ResponseEntitle, subSkuList: MutableList<String>, productSkuList: MutableList<String>): List<PurchaseModel> {
            val modelList: MutableList<PurchaseModel> = ArrayList<PurchaseModel>()
            try {
                if (responseEntitlementModel.data != null) {
                    if (responseEntitlementModel.data.purchaseAs != null && responseEntitlementModel.data.purchaseAs.isNotEmpty()) {
                        for (i in responseEntitlementModel.data.purchaseAs.indices) {
                            if (responseEntitlementModel.data.purchaseAs[i].offerStatus.equals(AppConstants.PUBLISHED) && responseEntitlementModel.data.purchaseAs[i].customData.androidProductId != null) {
                                val model = PurchaseModel()
                                if (responseEntitlementModel.data != null && responseEntitlementModel.data.purchaseAs[i].voDOfferType != null && responseEntitlementModel.data.purchaseAs[i].voDOfferType.contains(VodOfferType.RENTAL.name)) {
                                    if (responseEntitlementModel.data.purchaseAs[i].customData != null) {
                                        val identifier: String = responseEntitlementModel.data.purchaseAs[i].customData.androidProductId
                                        model.customIdentifier = identifier
                                        subSkuList.add(identifier)
                                    }
                                    model.title = responseEntitlementModel.data.purchaseAs[i].title
                                    model.purchaseOptions = responseEntitlementModel.data.purchaseAs[i].voDOfferType
                                    model.purchaseSku = responseEntitlementModel?.data?.sku
                                    model.identifier = responseEntitlementModel.data.purchaseAs[i].identifier
                                    model.rentalPeriod = responseEntitlementModel?.data?.purchaseAs?.get(i)?.rentalPeriod
                                    if (responseEntitlementModel.data.purchaseAs[i].subscriptionOrder != null) {
                                        model.subscriptionOrder = responseEntitlementModel.data.purchaseAs[i].subscriptionOrder
                                    }
                                    val allowedTrial: Boolean? = responseEntitlementModel?.data?.purchaseAs?.get(i)?.allowedTrial
                                    model.allowedTrial = allowedTrial
                                    model.subscriptionType = VodOfferType.RENTAL.name
                                    if (responseEntitlementModel.data.purchaseAs[i].recurringOffer != null) {
                                        if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod != null) {
                                            if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod.trialType != null && !responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod.trialType.equals("", ignoreCase = true)) {
                                                model.trialType = responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod.trialType
                                            }
                                            if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod.trialDuration > 0) {
                                                model.trialDuration = responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod.trialDuration
                                            }
                                        }
                                        if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.offerPeriod != null) {
                                            if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.offerPeriod.equals(VodOfferType.MONTHLY.name, ignoreCase = true)) {
                                                model.offerPeriod = VodOfferType.MONTHLY.name
                                            } else if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.offerPeriod.equals(VodOfferType.ANNUAL.name, ignoreCase = true)) {
                                                model.offerPeriod = VodOfferType.ANNUAL.name
                                            }
                                        }
                                    }
                                    if (responseEntitlementModel.data.purchaseAs[i].customData != null) {
                                        model.title_en = responseEntitlementModel.data.purchaseAs[i].customData.title_en
                                        model.title_es = responseEntitlementModel.data.purchaseAs[i].customData.title_es
                                        model.description_en = responseEntitlementModel.data.purchaseAs[i].customData.description_en
                                        model.description_es = responseEntitlementModel.data.purchaseAs[i].customData.description_es
                                        model.trialType_en = responseEntitlementModel.data.purchaseAs[i].customData.trialType_en
                                        model.trialType_es = responseEntitlementModel.data.purchaseAs[i].customData.trialType_es
                                        model.isCancelled = responseEntitlementModel.data.purchaseAs[i].customData.isCancelled
                                    }

                                    model.subscriptionList = subSkuList
                                    if (responseEntitlementModel.data.purchaseAs[i].expiryDate != null) {
                                        model.expiryDate = responseEntitlementModel.data.purchaseAs.get(i).expiryDate
                                    }
                                    model.entitlementState =
                                        responseEntitlementModel.data.purchaseAs.get(i).entitlementState != null && responseEntitlementModel.data.purchaseAs.get(i).entitlementState
                                    if (responseEntitlementModel.data.purchaseAs.get(i).customData != null) {
                                        model.customData = responseEntitlementModel.data.purchaseAs.get(i).customData
                                    }
                                    if (responseEntitlementModel.data.purchaseAs.get(i).currentExpiry != null && responseEntitlementModel.data.purchaseAs.get(i).currentExpiry > 0) {
                                        model.currentExpiryDate = responseEntitlementModel.data.purchaseAs.get(i).currentExpiry
                                    }
                                    if (responseEntitlementModel.data.purchaseAs.get(i).description != null) {
                                        model.description = responseEntitlementModel.data.purchaseAs.get(i).description
                                    }
                                    if (responseEntitlementModel.data.purchaseAs.get(i).nextChargeDate != null && responseEntitlementModel.data.purchaseAs.get(i).nextChargeDate > 0) {
                                        model.nextChargeDate = responseEntitlementModel.data.purchaseAs.get(i).nextChargeDate
                                    }
                                    model.isOnTrial = responseEntitlementModel.data.purchaseAs.get(i).isOnTrial
                                    modelList.add(model)
                                } else {
                                    if (null != responseEntitlementModel.data.purchaseAs[i].customData) {
                                        if (responseEntitlementModel.data.purchaseAs[i].customData.androidProductId != null) {
                                            val identifier: String = responseEntitlementModel.data.purchaseAs[i].customData.androidProductId
                                            productSkuList.add(identifier)
                                        }
                                    }
                                    model.subscriptionType = "PRODUCT"
                                    if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer != null) {
                                        if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod != null) {
                                            if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod.trialType != null && !responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod.trialType.equals("", ignoreCase = true)) {
                                                model.trialType = responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod.trialType
                                            }
                                            if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod.trialDuration > 0) {
                                                model.trialDuration = responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.trialPeriod.trialDuration
                                            }
                                        }
                                        if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.offerPeriod != null) {
                                            if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.offerPeriod.equals(VodOfferType.MONTHLY.name, ignoreCase = true)) {
                                                model.offerPeriod = VodOfferType.MONTHLY.name
                                            } else if (responseEntitlementModel.data.purchaseAs.get(i).recurringOffer.offerPeriod.equals(VodOfferType.ANNUAL.name, ignoreCase = true)) {
                                                model.offerPeriod = VodOfferType.ANNUAL.name
                                            }
                                        }
                                    }

                                    if (responseEntitlementModel.data.purchaseAs[i].customData != null) {
                                        model.title_en =
                                            responseEntitlementModel.data.purchaseAs[i].customData.title_en
                                        model.title_es =
                                            responseEntitlementModel.data.purchaseAs[i].customData.title_es
                                        model.description_en =
                                            responseEntitlementModel.data.purchaseAs[i].customData.description_en
                                        model.description_es =
                                            responseEntitlementModel.data.purchaseAs[i].customData.description_es
                                        model.trialType_en =
                                            responseEntitlementModel.data.purchaseAs[i].customData.trialType_en
                                        model.trialType_es =
                                            responseEntitlementModel.data.purchaseAs[i].customData.trialType_es
                                        model.isCancelled =
                                            responseEntitlementModel.data.purchaseAs[i].customData.isCancelled

                                    }
                                    model.entitlementState =
                                        responseEntitlementModel.data.purchaseAs.get(i).entitlementState != null && responseEntitlementModel.data.purchaseAs.get(
                                            i
                                        )
                                            .entitlementState
                                    if (responseEntitlementModel.data.purchaseAs.get(i).customData != null) {
                                        model.customData =
                                            responseEntitlementModel.data.purchaseAs.get(i).customData
                                    }
                                    model.subscriptionList = productSkuList
                                    if (responseEntitlementModel.data.purchaseAs.get(i).expiryDate != null) {
                                        model.expiryDate =
                                            responseEntitlementModel.data.purchaseAs.get(i).expiryDate
                                    }
                                    if (responseEntitlementModel.data.purchaseAs.get(i).subscriptionOrder != null) {
                                        model.subscriptionOrder =
                                            responseEntitlementModel.data.purchaseAs.get(i).subscriptionOrder
                                    }
                                    if (responseEntitlementModel.data.purchaseAs.get(i).description != null) {
                                        model.description =
                                            responseEntitlementModel.data.purchaseAs.get(i).description
                                    }
                                    if (responseEntitlementModel.data.purchaseAs.get(i).currentExpiry != null && responseEntitlementModel.data.purchaseAs.get(
                                            i
                                        )
                                            .currentExpiry > 0
                                    ) {
                                        model.currentExpiryDate =
                                            responseEntitlementModel.data.purchaseAs.get(i).currentExpiry
                                    }
                                    if (responseEntitlementModel.data.purchaseAs.get(i).nextChargeDate != null && responseEntitlementModel.data.purchaseAs.get(
                                            i
                                        )
                                            .nextChargeDate > 0
                                    ) {
                                        model.nextChargeDate =
                                            responseEntitlementModel.data.purchaseAs.get(i).nextChargeDate
                                    }
                                    model.isOnTrial = responseEntitlementModel.data.purchaseAs.get(i).isOnTrial
                                    val allowedTrial: Boolean = responseEntitlementModel.data.purchaseAs[i].allowedTrial
                                    model.allowedTrial = allowedTrial
                                    modelList.add(model)
                                }
                            }

                        }
                    }
                }
            } catch (e: java.lang.Exception) {
                Log.d("fetchEntitleRecSubscriptionModel", "fetchEntitleRecSubscriptionModel: $e")
            }

            return modelList
        }

        fun createManagePurchaseListNew(
            purchaseModelList: List<PurchaseModel>,
            plans: ResponseMembershipAndPlan?,
            purchaseFinalList: ArrayList<PurchaseModel?>
        ): List<PurchaseModel?> {
            try {
                if (purchaseModelList != null) {
                    for (j in purchaseModelList.indices) {
                        if ((purchaseModelList[j].entitlementState != null && purchaseModelList[j].entitlementState == true) || purchaseModelList[j].isOnHold) {
                            val purchaseModel =
                                PurchaseModel()
                            purchaseModel.price = "" + plans?.data?.get(j)?.prices?.get(0)?.price
                            purchaseModel.currency =
                                "" + plans?.data?.get(j)?.prices?.get(0)?.currencyCode
                            purchaseModel.paymentProvider =
                                "" + purchaseModelList[j].paymentProvider
                            purchaseModel.trialType = "" + purchaseModelList[j].trialType
                            purchaseModel.allowedTrial = purchaseModelList[j].allowedTrial
                            purchaseModel.trialDuration = purchaseModelList[j].trialDuration
                            purchaseModel.isSelected = false
                            purchaseModel.purchaseOptions = VodOfferType.RECURRING_SUBSCRIPTION.name
                            purchaseModel.offerPeriod = VodOfferType.WEEKLY.name
                            purchaseModel.title = purchaseModelList[j].title
                            purchaseModel.title_en = purchaseModelList[j].title_en
                            purchaseModel.title_es = purchaseModelList[j].title_es
                            purchaseModel.description_en = purchaseModelList[j].description_en
                            purchaseModel.description_es = purchaseModelList[j].description_es
                            purchaseModel.trialType_es = purchaseModelList[j].trialType_es
                            purchaseModel.trialType_en = purchaseModelList[j].trialType_en
                            purchaseModel.description = plans?.data?.get(j)?.description
                            purchaseModel.isCancelled = purchaseModelList[j].isCancelled
                            purchaseModel.description_nl = purchaseModelList[j].description_nl
                            purchaseModel.identifier = purchaseModelList[j].identifier
                            purchaseModel.subscriptionType = purchaseModelList[j].subscriptionType
                            purchaseModel.customIdentifier = purchaseModelList[j].customIdentifier
                            purchaseModel.onGrace = plans?.data?.get(j)?.onGrace!!
                            purchaseModel.isOnHold = plans?.data?.get(j)?.onHold!!
                            purchaseModel.graceStartDate = plans?.data?.get(j)?.graceStartDate!!
                            purchaseModel.graceEndDate = plans?.data?.get(j)?.graceEndDate!!
                            // purchaseModel.setCurrency(purchaseModelList.get(j).getCurrency());
                            purchaseModel.createdDate = purchaseModelList[j].createdDate
                            purchaseModel.subscriptionOrder = purchaseModelList[j].subscriptionOrder
                            purchaseModel.entitlementState = purchaseModelList[j].entitlementState
                            if (purchaseModelList[j].expiryDate != null) {
                                purchaseModel.expiryDate = purchaseModelList[j].expiryDate
                            }
                            purchaseModel.customData = purchaseModelList[j].customData
                            purchaseFinalList.add(purchaseModel)

                        }
                    }
                }
            } catch (e: Exception) {
                e.message?.let { Logger.w(it) }
            }

            return purchaseFinalList
        }

        fun MoEngageEventTrack(
            context: Context?,
            screenName: String?,
            id: String?,
            tittle: String?,
            contentType: String?,
            eventType: String?,
            contentPlayed: String?,
            contentDuration: String?
        ) {
            val properties = Properties()
            properties
                .addAttribute(AppConstants.SCREEN_NAME, screenName)
                .addAttribute(AppConstants.CONTENT_ID, id)
                .addAttribute(AppConstants.TITLE, tittle)
                .addAttribute(AppConstants.CONTENT_TYPE, contentType)
                .addAttribute(AppConstants.CONTENT_PLAYED, contentPlayed)
                .addAttribute(AppConstants.CONTENT_DURATION, contentDuration)
                .setNonInteractive()
            MoEHelper.getInstance(context!!).trackEvent(eventType.toString(), properties)
        }

        fun MoEngageShareEventTrack(
            context: Context?,
            screenName: String?,
            id: String?,
            tittle: String?,
            contentType: String?,
            eventType: String?
        ) {
            val properties = Properties()
            properties
                .addAttribute(AppConstants.SCREEN_NAME, screenName)
                .addAttribute(AppConstants.CONTENT_ID, id)
                .addAttribute(AppConstants.CONTENT_TITTLE, tittle)
                .addAttribute(AppConstants.CONTENT_TYPE, contentType)
                .setNonInteractive()
            MoEHelper.getInstance(context!!).trackEvent(eventType.toString(), properties)
        }

        fun searchEventTrack(context: Context?, searchKeyword: String?) {
            val properties = Properties()
            properties
                .addAttribute(AppConstants.SCREEN_NAME, AppConstants.HOME)
                .addAttribute(AppConstants.SEARCH_TERMS, searchKeyword)
                .setNonInteractive()
            MoEHelper.getInstance(context!!).trackEvent(AppConstants.SEARCH, properties)
        }

        fun screenViewedTrack(context: Context?, eventType: String?, screenName: String?) {
            val properties = Properties()
            properties
                .addAttribute(AppConstants.SCREEN_NAME, screenName)
                .setNonInteractive()
            MoEHelper.getInstance(context!!).trackEvent(eventType.toString(), properties)
        }

        @JvmStatic
        fun MoEngageSeeAllEventTrack(
            context: Context?,
            screenName: String?,
            id: String?,
            railName: String?,
            eventType: String?
        ) {
            val properties = Properties()
            properties
                .addAttribute(AppConstants.SCREEN_NAME, screenName)
                .addAttribute(AppConstants.RAIL_ID, id)
                .addAttribute(AppConstants.RAIL_NAME, railName)
                .setNonInteractive()
            MoEHelper.getInstance(context!!).trackEvent(eventType.toString(), properties)
        }

        fun MoEngageContentSelectTrack(
            context: Context?,
            screenName: String?,
            railId: String?,
            railName: String?,
            contentId: String?,
            contentTittle: String?,
            contentType: String?,
            eventType: String?
        ) {
            val properties = Properties()
            properties
                .addAttribute(AppConstants.SCREEN_NAME, screenName)
                .addAttribute(AppConstants.RAIL_ID, "")
                .addAttribute(AppConstants.RAIL_NAME, railName)
                .addAttribute(AppConstants.CONTENT_ID, contentId)
                .addAttribute(AppConstants.CONTENT_TITTLE, contentTittle)
                .addAttribute(AppConstants.CONTENT_TYPE, contentType)
                .setNonInteractive()
            MoEHelper.getInstance(context!!).trackEvent(eventType.toString(), properties)
        }

        private fun hitUserProfileApi(context: Context?) {
            try {
                val preference: KsPreferenceKeys = KsPreferenceKeys.getInstance()
                val registrationLoginViewModel: RegistrationLoginViewModel =
                    ViewModelProvider((context as BaseActivity?)!!).get(
                        RegistrationLoginViewModel::class.java
                    )
                registrationLoginViewModel.hitUserProfile(context, preference.appPrefAccessToken)
                    .observe(
                        context as BaseActivity
                    ) { userProfileResponse ->
                        if (userProfileResponse != null) {
                            if (userProfileResponse.status) {
                            } else {
                                if (userProfileResponse.responseCode == 4302) {
                                    preference.appPrefRegisterStatus =
                                        AppConstants.UserStatus.Logout.toString()
                                }
                            }
                        }
                    }
            } catch (e: Exception) {
            }
        }

        fun checkLoginStatus(context: Context?) {
            val preference: KsPreferenceKeys = KsPreferenceKeys.getInstance()
            var isLoggedIn = false
            if (preference.appPrefLoginStatus.equals(
                    AppConstants.UserStatus.Login.toString(),
                    ignoreCase = true
                )
            ) {
                isLoggedIn = true
            }
            if (isLoggedIn) {
                hitUserProfileApi(context)
            }
        }

        fun callMoEngageUserTypeSubscription(context: Context?, userType: String?) {
            val properties = Properties()
            properties.addAttribute(AppConstants.USERTYPE, userType)
            MoEHelper.getInstance(context!!).trackEvent(AppConstants.USERTYPE, properties)
        }

        @JvmStatic
        fun getHomeTabId(configBean: ConfigBean?, name: String?): String {
            var screenId = ""
            if (configBean != null) {
                for (i in configBean.data.appConfig.navScreens.indices) {
                    if (configBean.data.appConfig.navScreens[i].screenName.equals(
                            name,
                            ignoreCase = true
                        )
                    ) {
                        screenId = configBean.data.appConfig.navScreens[i].id.toString()
                        break
                    }
                }
            }
            return screenId
        }


        @JvmStatic
        fun getEnvAssetHeroDetail(
            railCommonData: RailCommonData,
            response: EnveuVideoDetails
        ) {
            try {
                var isMusicEnabled = getAppTypeByFeature()
                val enveuVideoDetailsBean = EnveuVideoDetailsBean()
                val enveuVideoDetails = response
                enveuVideoDetailsBean.data = enveuVideoDetails
                val enveuVideoItemBean = EnveuVideoItemBean(enveuVideoDetailsBean, isMusicEnabled)
                railCommonData.enveuVideoItemBeans = ArrayList()
                railCommonData.enveuVideoItemBeans.add(enveuVideoItemBean)
            } catch (ex: Exception) {
                Logger.w(ex)
            }
        }

        fun heroAssetRedirections(
            tabId: String?,
            railCommonData: RailCommonData,
            activity: Context?
        ) {
            try {
                var landingPageAssetId =
                    railCommonData.screenWidget.landingPageAssetId!!.trim { it <= ' ' }
                if (ObjectHelper.isEmpty(landingPageAssetId)) {
                    landingPageAssetId = "0"
                }
                redirectionLogic(
                    activity!!,
                    railCommonData,
                    0,
                    tabId,
                    railCommonData.screenWidget.contentID,
                    railCommonData.screenWidget.name.toString()
                )


            } catch (ex: Exception) {
                Logger.w(ex)
            }
        }

        @JvmStatic
        fun getImageContentForRailItem(images: MutableList<ImagesItem>): ImageContent {
            val imageContent = ImageContent()
            images.forEach { imagesItem ->
                if (imagesItem.imageContent?.imageType.equals("1x1")) {
                    imageContent.src = imagesItem.imageContent.src
                    imageContent.dominantColor = imagesItem.imageContent.dominantColor
                    imageContent.colorPalette = imagesItem.imageContent.colorPalette
                    return@forEach
                }
            }
            return imageContent
        }

        fun getDrmKeyValue(drm: Boolean): String {
            return if (drm) {
                AppConstants.JW_DRM_KEY
            } else {
                AppConstants.JW_NON_DRM_KEY
            }
        }

        @JvmStatic
        fun getImageContent(videoItem: com.enveu.beanModelV3.playListModelV2.VideosItem?): ImageContent {
            var imageContent = ImageContent()
            if (videoItem?.images != null && videoItem.images.size > 0) {
                videoItem?.images?.forEach { imagesItem ->
                    if (imagesItem.imageContent?.imageType.equals("1x1")) {
                        imageContent.src = imagesItem.imageContent.src
                        imageContent.dominantColor = imagesItem.imageContent.dominantColor
                        imageContent.colorPalette = imagesItem.imageContent.colorPalette
                        return@forEach
                    }
                }
            } else {
                if (videoItem != null && videoItem.customData != null && videoItem.customData.songs_albums_id != null && videoItem.customData.songs_albums_id.images != null && videoItem.customData.songs_albums_id.images.size > 0) {
                    // Ensure getImages() is not null and has items before accessing the list
                    if (videoItem.customData.songs_albums_id.images != null && videoItem.customData.songs_albums_id.images.isNotEmpty()
                    ) {
                        videoItem.customData.songs_albums_id.images.forEachIndexed { index, imagesItem ->
                            if (imagesItem.imageType.equals("1x1")) {
                                imageContent.src =
                                    videoItem.customData.songs_albums_id.images[0].src
                                imageContent.dominantColor =
                                    videoItem.customData.songs_albums_id.images[0].dominantColor
                                imageContent.colorPalette =
                                    videoItem.customData.songs_albums_id.images[0].colorPalette
                                return@forEachIndexed
                            }
                        }
                    }
                }
            }
            return imageContent
        }


        @JvmStatic
        fun getImageContent(videoItem: com.enveu.beanModelV3.continueWatching.DataItem?): ImageContent {
            var imageContent = ImageContent()
            if (videoItem?.images != null && videoItem.images.size > 0) {
                videoItem?.images?.forEach { imagesItem ->
                    if (imagesItem.imageContent?.imageType.equals("1x1")) {
                        imageContent.src = imagesItem.imageContent.src
                        imageContent.dominantColor = imagesItem.imageContent.dominantColor
                        imageContent.colorPalette = imagesItem.imageContent.colorPalette
                        return@forEach
                    }
                }
            } else {
                if (videoItem != null && videoItem.customData != null && videoItem.customData.songs_albums_id.images != null && videoItem.customData.songs_albums_id.images.size > 0) {
                    // Ensure getImages() is not null and has items before accessing the list
                    if (videoItem.customData.songs_albums_id.images != null && videoItem.customData.songs_albums_id.images.isNotEmpty()
                    ) {
                        videoItem.customData.songs_albums_id.images.forEachIndexed { index, imagesItem ->
                            if (imagesItem.imageType.equals("1x1")) {
                                imageContent.src =
                                    videoItem.customData.songs_albums_id.images[0].src
                                imageContent.dominantColor =
                                    videoItem.customData.songs_albums_id.images[0].dominantColor
                                imageContent.colorPalette =
                                    videoItem.customData.songs_albums_id.images[0].colorPalette
                                return@forEachIndexed
                            }
                        }
                    }
                }
            }
            return imageContent
        }

        @JvmStatic
        fun getImageContent(videoItem: ItemsItem?): ImageContent {
            var imageContent = ImageContent()
            if (videoItem?.images != null && videoItem.images.size > 0) {
                videoItem?.images?.forEach { imagesItem ->
                    if (imagesItem.imageContent?.imageType.equals("1x1")) {
                        imageContent.src = imagesItem.imageContent.src
                        imageContent.dominantColor = imagesItem.imageContent.dominantColor
                        imageContent.colorPalette = imagesItem.imageContent.colorPalette
                        return@forEach
                    } else if (imagesItem.imageContent?.imageType.equals("16x9")) {
                        if (videoItem?.contentType.equals("VIDEO")) {
                            imageContent.src = imagesItem.imageContent.src
                            imageContent.dominantColor = imagesItem.imageContent.dominantColor
                            imageContent.colorPalette = imagesItem.imageContent.colorPalette
                            return@forEach
                        } else if (videoItem?.contentType.equals(AppConstants.CUSTOM) && videoItem?.customContent != null && !videoItem?.customContent?.customType.equals(
                                AppConstants.ALBUM
                            )
                        ) {
                            imageContent.src = imagesItem.imageContent.src
                            imageContent.dominantColor = imagesItem.imageContent.dominantColor
                            imageContent.colorPalette = imagesItem.imageContent.colorPalette
                            return@forEach
                        }
                    }
                }
            } else {
                if (videoItem != null && videoItem.customData != null && videoItem.customData.songs_albums_id.images != null && videoItem.customData.songs_albums_id.images.size > 0) {
                    // Ensure getImages() is not null and has items before accessing the list
                    if (videoItem.customData.songs_albums_id.images != null && videoItem.customData.songs_albums_id.images.isNotEmpty()
                    ) {
                        videoItem.customData.songs_albums_id.images.forEachIndexed { index, imagesItem ->
                            if (imagesItem.imageType.equals("1x1")) {
                                imageContent.src =
                                    videoItem.customData.songs_albums_id.images[0].src
                                imageContent.dominantColor =
                                    videoItem.customData.songs_albums_id.images[0].dominantColor
                                imageContent.colorPalette =
                                    videoItem.customData.songs_albums_id.images[0].colorPalette
                                return@forEachIndexed
                            }
                        }
                    }
                }
            }
            return imageContent
        }

        @JvmStatic
        fun getImageContent(videoItem: DataItem?): ImageContent {
            var imageContent = ImageContent()
            if (videoItem?.images != null && videoItem.images.size > 0) {
                videoItem?.images?.forEach { imagesItem ->
                    if (imagesItem.imageContent?.imageType.equals("1x1")) {
                        imageContent.src = imagesItem.imageContent.src
                        imageContent.dominantColor = imagesItem.imageContent.dominantColor
                        imageContent.colorPalette = imagesItem.imageContent.colorPalette
                        return@forEach
                    }
                }
            } else if (videoItem != null && videoItem.imageContent != null) {
                imageContent.src = videoItem.imageContent.src
                imageContent.dominantColor = videoItem.imageContent.dominantColor
                imageContent.colorPalette = videoItem.imageContent.colorPalette
            } else if (videoItem != null && videoItem.customData != null && videoItem.customData.songsAlbumsId != null && videoItem.customData.songsAlbumsId.images != null && videoItem.customData.songsAlbumsId.images.size > 0) {
                // Ensure getImages() is not null and has items before accessing the list
                if (videoItem.customData.songsAlbumsId.images != null && videoItem.customData.songsAlbumsId.images.isNotEmpty()
                ) {
                    videoItem.customData.songsAlbumsId.images.forEachIndexed { index, imagesItem ->
                        if (imagesItem.imageType.equals("1x1")) {
                            imageContent.src = videoItem.customData.songsAlbumsId.images[0].src
                            imageContent.dominantColor =
                                videoItem.customData.songsAlbumsId.images[0].dominantColor
                            imageContent.colorPalette =
                                videoItem.customData.songsAlbumsId.images[0].colorPalette
                            return@forEachIndexed
                        }
                    }
                }

            } else {
                if (videoItem != null && videoItem.customData != null && videoItem.customData.songsArtistIds != null && !videoItem.customData.songsArtistIds.isNullOrEmpty() && videoItem.customData.songsArtistIds[0].images != null && videoItem.customData.songsArtistIds[0].images.size > 0) {
                    if (videoItem.customData.songsArtistIds[0].images != null && videoItem.customData.songsArtistIds[0].images.isNotEmpty()
                    ) {
                        videoItem.customData.songsArtistIds[0].images.forEachIndexed { index, imagesItem ->
                            if (imagesItem.imageType.equals("1x1")) {
                                imageContent.src =
                                    videoItem.customData.songsArtistIds[0].images[0].src
                                imageContent.dominantColor =
                                    videoItem.customData.songsArtistIds[0].images[0].dominantColor
                                imageContent.colorPalette =
                                    videoItem.customData.songsArtistIds[0].images[0].colorPalette
                                return@forEachIndexed
                            }
                        }
                    }
                }
            }
            return imageContent
        }

        @JvmStatic
        fun getImageContent(videoItem: EnveuVideoDetails?): ImageContent {
            var imageContent = ImageContent()
            if (videoItem?.images != null && videoItem.images.size > 0) {
                videoItem?.images?.forEach { imagesItem ->
                    if (imagesItem.imageContent?.imageType.equals("1x1")) {
                        imageContent.src = imagesItem.imageContent.src
                        imageContent.dominantColor = imagesItem.imageContent.dominantColor
                        imageContent.colorPalette = imagesItem.imageContent.colorPalette
                        return@forEach
                    }
                }
            } else {
                if (videoItem != null && videoItem.customData != null && videoItem.customData.songs_albums_id.images != null && videoItem.customData.songs_albums_id.images.size > 0) {
                    // Ensure getImages() is not null and has items before accessing the list
                    if (videoItem.customData.songs_albums_id.images != null && videoItem.customData.songs_albums_id.images.isNotEmpty()
                    ) {
                        videoItem.customData.songs_albums_id.images.forEachIndexed { index, imagesItem ->
                            if (imagesItem.imageType.equals("1x1")) {
                                imageContent.src =
                                    videoItem.customData.songs_albums_id.images[0].src
                                imageContent.dominantColor =
                                    videoItem.customData.songs_albums_id.images[0].dominantColor
                                imageContent.colorPalette =
                                    videoItem.customData.songs_albums_id.images[0].colorPalette
                                return@forEachIndexed
                            }
                        }
                    }
                }
            }
            return imageContent
        }

        @JvmStatic
        fun getImageContent(videoItem: com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetails?): ImageContent {
            var imageContent = ImageContent()
            if (videoItem?.images != null && videoItem.images.size > 0) {
                videoItem?.images?.forEach { imagesItem ->
                    if (imagesItem.imageContent?.imageType.equals("1x1")) {
                        imageContent.src = imagesItem.imageContent.src
                        imageContent.dominantColor = imagesItem.imageContent.dominantColor
                        imageContent.colorPalette = imagesItem.imageContent.colorPalette
                        return@forEach
                    }
                }
            } else {
                if (videoItem != null && videoItem.customData != null && videoItem.customData.songs_albums_id != null && videoItem.customData.songs_albums_id.images != null && videoItem.customData.songs_albums_id.images.size > 0) {
                    // Ensure getImages() is not null and has items before accessing the list
                    if (videoItem.customData.songs_albums_id.images != null && videoItem.customData.songs_albums_id.images.isNotEmpty()
                    ) {
                        videoItem.customData.songs_albums_id.images.forEachIndexed { index, imagesItem ->
                            if (imagesItem.imageType.equals("1x1")) {
                                imageContent.src =
                                    videoItem.customData.songs_albums_id.images[0].src
                                imageContent.dominantColor =
                                    videoItem.customData.songs_albums_id.images[0].dominantColor
                                imageContent.colorPalette =
                                    videoItem.customData.songs_albums_id.images[0].colorPalette
                                return@forEachIndexed
                            }
                        }
                    }
                }
            }
            return imageContent
        }

        fun getPlayerUrl(playBackEndPointUrl: String?): String {
            val fullPlayerUrl = if (!SDKConfig.getInstance().playbacK_URL.isNullOrEmpty()) {
                "${SDKConfig.getInstance().playbacK_URL}${playBackEndPointUrl}.m3u8"
            } else {
                "${AppConstants.PLAY_BACK_BASE_URL}${playBackEndPointUrl}.m3u8"
            }
            return fullPlayerUrl
        }

        // Method to get value based on key
        fun getImageRatio(key: String): String {
            return when (key) {
                "PR1" -> PR1
                "PR2" -> PR2
                "LDS" -> LDS
                "SQR" -> SQR
                "CIR" -> CIR
                "CST" -> CST
                "LDS2" -> LDS2
                "LDS3" -> LDS3
                "LDS4" -> LDS4
                "PR3" -> PR3
                "LDS3_2" -> LDS3_2
                else -> LDS
            }
        }

        fun animateScaleUp(view: View) {
            val scaleUpX = ObjectAnimator.ofFloat(view, View.SCALE_X, 1f, 1.3f)
            val scaleUpY = ObjectAnimator.ofFloat(view, View.SCALE_Y, 1f, 1.3f)
            val scaleDownX = ObjectAnimator.ofFloat(view, View.SCALE_X, 1.3f, 1f)
            val scaleDownY = ObjectAnimator.ofFloat(view, View.SCALE_Y, 1.3f, 1f)
            scaleUpX.duration = 250
            scaleUpY.duration = 250
            scaleDownX.duration = 250
            scaleDownY.duration = 250
            val scaleUp = AnimatorSet().apply { playTogether(scaleUpX, scaleUpY) }
            val scaleDown = AnimatorSet().apply { playTogether(scaleDownX, scaleDownY) }
            AnimatorSet().apply {
                playSequentially(scaleUp, scaleDown)
                start()
            }
        }

        @JvmStatic
        fun <T> convertResponse(responseBody: Any?, modelClass: Class<T>?): T {
            val gson = Gson()
            val json = gson.toJson(responseBody)
            return gson.fromJson(json, modelClass)
        }

//        fun setUpdatedProfileImageFullUrl(imageUrl:String) {
//            val fullImage = imageUrl?.let { "${Constants.}$it" }
//            KsPreferenceKeys.getInstance().setAppPrefUserProfilePic(fullImage)
//        }

        fun getSdkConfigUrl(): String {
            val cloudFrontUrl = SDKConfig.getInstance().clouD_FRONT_BASE_URL
            val profileFolder = SDKConfig.getInstance().profileFolder

          //  val finalCloudFrontUrl = if (!cloudFrontUrl.isNullOrEmpty()) cloudFrontUrl else SDKConfig.CLOUD_FRONT_BASE_URL
            val finalProfileFolder = if (!profileFolder.isNullOrEmpty()) profileFolder else SDKConfig.PROFILE_PICTURE_BASE_FOLDER

            return "${SDKConfig.CLOUD_FRONT_BASE_URL}${finalProfileFolder}"
        }

        @JvmStatic
        fun getArabicTimeFormatter(isWithYear: Boolean):DateTimeFormatter {
            val arabicLocale = Locale("ar", "SA")
            return if(isWithYear) DateTimeFormat.forPattern("d MMMM yyyy").withLocale(arabicLocale) else DateTimeFormat.forPattern("d MMMM").withLocale(arabicLocale)
        }

        @JvmStatic
        fun getEnglishTimeFormatter():DateTimeFormatter{
            return DateTimeFormat.forPattern("dd MMM")
                .withLocale(Locale.ENGLISH)
        }
    }

}