package com.enveu.utils.commonMethods

import android.content.Context
import android.util.Log
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.appLevelModel.MediaConfig
import com.enveu.appLevelModel.MediaInfo
import com.enveu.appLevelModel.MediaMappingInfo
import com.enveu.utils.SearchApiParams
import com.enveu.utils.SearchParmsByMediaType
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import org.json.JSONException
import org.json.JSONObject


object AppConfigMethod {

    private fun loadJsonFromAsset(context: Context, jsonFile: String): String? {
        var json: String? = null
        try {
            context.assets.open(jsonFile).use { inputStream ->
                val size = inputStream.available()
                val buffer = ByteArray(size)
                inputStream.read(buffer)
                json = String(buffer, Charsets.UTF_8)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
        return json
    }

    // Similarly, create functions for other media types as needed

    fun getMediaConfigByType(mediaType: String): MediaConfig? {
        val mediaTypeConfigString = KsPreferenceKeys.getInstance().getString(
            KsPreferenceKeys.MEDIA_TYPE_CONFIG, "")
        val jsonObject = JSONObject(mediaTypeConfigString!!)
        return getObjectByType(jsonObject, if (jsonObject.has(mediaType)) mediaType else "MOVIE")
    }

    private fun getObjectByType(jsonObject: JSONObject, targetType: String): MediaConfig? {
        // Check if the target type exists in the JSON object
        val jsonObject = jsonObject.getJSONObject(targetType)
        return Gson().fromJson(jsonObject.toString(), MediaConfig::class.java)
    }

    fun parseMediaConfig(json: String): MediaConfig {
        return Gson().fromJson(json, MediaConfig::class.java)
    }

    fun setMediaTypeJson(context: Context) {
        val mediaTypeMapping = loadJsonFromAsset(context, "media-type-mapping.json")
        val mediaTypeConfig = loadJsonFromAsset(context, "media-types-config.json")
        val featureFlagConfig = loadJsonFromAsset(context, "featureFlags.json")
        val searchParmsByMediaType= loadJsonFromAsset(context,"search_parm_by_media_type.json")
        val filterGenres= loadJsonFromAsset(context,"Genres.json")
        KsPreferenceKeys.getInstance().setString(
            KsPreferenceKeys.MEDIA_TYPE_MAPPING, mediaTypeMapping)
        KsPreferenceKeys.getInstance().setString(
            KsPreferenceKeys.MEDIA_TYPE_CONFIG, mediaTypeConfig)
        KsPreferenceKeys.getInstance().setString(
            KsPreferenceKeys.FEATURE_FLAG_CONFIG, featureFlagConfig)
        KsPreferenceKeys.getInstance().setString(
            KsPreferenceKeys.SEARCH_PARAM_BY_MEDIA_TYPE,searchParmsByMediaType
        )
        KsPreferenceKeys.getInstance().setString(
            KsPreferenceKeys.FILTER_GENRES,filterGenres
        )
    }

    //get mediaConfig info for dynamic click of content according to type
    fun getMediaMappingByType(mediaType: String?): MediaInfo? {
        val mediaTypeMappingString =
            KsPreferenceKeys.getInstance().getString(
                KsPreferenceKeys.MEDIA_TYPE_MAPPING, "")
        return mediaType?.let { parseJSON(mediaTypeMappingString, it) }
        /* return parseMediaMappingByType(
             jsonObject,
             if (jsonObject.has(mediaType)) mediaType else "MOVIE"
         )*/
    }

    private fun parseJSON(jsonString: String?, targetType: String): MediaInfo {
        try {
            val jsonObject = JSONObject(jsonString!!)
            val dataArray = jsonObject.getJSONArray("data")
            for (i in 0 until dataArray.length()) {
                val dataObject = dataArray.getJSONObject(i)
                val mediaType = dataObject.getString("mediaType")
                if (mediaType.equals(targetType, true)) {
                    // Convert the JSON object to MediaConfig using Gson
                    return Gson().fromJson(dataObject.toString(), MediaInfo::class.java)
                }
            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return MediaInfo("MOVIE", "MOVIE", false)
    }

    private fun parseMediaMappingByType(
        jsonObject: JSONObject,
        targetType: String
    ): MediaMappingInfo? {
        // Check if the target type exists in the JSON object
        // Get the JSON object based on the target type
        val targetJsonObject = jsonObject.getJSONObject(targetType)
        // Convert the JSON object to MediaConfig using Gson
        return Gson().fromJson(targetJsonObject.toString(), MediaMappingInfo::class.java)

    }


    //To get featureFlagList
    fun parseFeatureFlagList(): FeatureFlagModel {
        val featureFlagConfigString = KsPreferenceKeys.getInstance().getString(
            KsPreferenceKeys.FEATURE_FLAG_CONFIG, "")
        return Gson().fromJson(featureFlagConfigString, FeatureFlagModel::class.java)
    }

    @JvmStatic
    //get media key on the basis of isSearchable true for search api
    fun getSearchableKeys(): MutableList<String> {
        val mediaTypeMappingString =
            KsPreferenceKeys.getInstance().getString(
                KsPreferenceKeys.MEDIA_TYPE_MAPPING, "")
        val otherValue=Gson().fromJson(mediaTypeMappingString,MediaMappingInfo::class.java)
        val searchableKeys = mutableListOf<String>()
        val jsonObject = JSONObject(mediaTypeMappingString)
        val dataArray = jsonObject.getJSONArray("data")
        for (i in 0 until dataArray.length()) {
            val dataObject = dataArray.getJSONObject(i)
            val isSearchable = dataObject.getBoolean("isSearchable")
            if (isSearchable) {
                val mediaType = dataObject.optString("mediaType")
                searchableKeys.add(mediaType)
            }
        }
        return searchableKeys
    }
    @JvmStatic
    fun getSearchApiParameters(searchKeyList: MutableList<String>): MutableList<SearchApiParams> {
        val searchApiParams= mutableListOf<SearchApiParams>()
        val searchParmsByMediaType=KsPreferenceKeys.getInstance().getString(KsPreferenceKeys.SEARCH_PARAM_BY_MEDIA_TYPE,"")
        val searchParmsByMediaTypeList = Gson().fromJson(searchParmsByMediaType,SearchParmsByMediaType::class.java)
        searchParmsByMediaTypeList?.let { searchParmsByMediaType1 ->
            for (searchApiParam in searchParmsByMediaType1.data){
                if (searchKeyList.contains(searchApiParam.mediaType)){
                    searchApiParams.add(searchApiParam)
                }
            }
        }
        return searchApiParams
    }
    @JvmStatic
    fun getFilterGenres():Map<String,List<Int>>{
        val json=KsPreferenceKeys.getInstance().getString(KsPreferenceKeys.FILTER_GENRES,"")
        Log.d("FilterGeners",json)
        val gson = Gson()
        val type = object : TypeToken<Map<String, List<Int>>>() {}.type
        val genresMap: Map<String, List<Int>> = gson.fromJson(json, type)
        return genresMap
    }
}

