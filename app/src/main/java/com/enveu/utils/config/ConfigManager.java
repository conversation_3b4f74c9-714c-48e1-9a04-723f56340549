package com.enveu.utils.config;

import android.content.ContentResolver;
import android.content.Context;
import android.provider.Settings;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.enveu.BuildConfig;
import com.enveu.R;
import com.enveu.SDKConfig;
import com.enveu.callbacks.apicallback.ApiResponseModel;
import com.enveu.networking.apiendpoints.ApiInterface;
import com.enveu.networking.apiendpoints.RequestConfig;
import com.enveu.networking.errormodel.ApiErrorModel;
import com.enveu.sdk.baseCategoryServices.ENSdkBaseCategoryServices;
import com.enveu.sdk.baseClient.ENSdkBaseClient;
import com.enveu.sdk.baseClient.ENSdkBaseConfiguration;
import com.enveu.sdk.baseClient.ENSdkBaseGateway;
import com.enveu.sdk.baseClient.ENSdkBasePlatform;
import com.enveu.sdk.callBack.ColorConfigEnveuCallBack;
import com.enveu.sdk.callBack.ConfigEnveuCallBack;
import com.enveu.sdk.logging.ENSdkEnveuLogs;
import com.enveu.utils.Logger;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.config.bean.dmsResponse.ConfigBean;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.text.SimpleDateFormat;

public class ConfigManager {
    private static ConfigManager configManagerInstance;
    private static ApiInterface endpoint;
    private static boolean isDmsDataStored = false;
    ConfigBean configBean;
    ApiResponseModel callBack;
    private String API_KEY="";
    private boolean isTablet=false;

    private ConfigManager() {

    }

    public static ConfigManager getInstance() {
        if (configManagerInstance == null) {
            endpoint = RequestConfig.getConfigClient().create(ApiInterface.class);
            configManagerInstance = new ConfigManager();
        }
        return (configManagerInstance);
    }

    public boolean getConfig(Context context,ApiResponseModel listener) {
        this.callBack = listener;
        callBack.onStart();
        boolean isTablet = context.getResources().getBoolean(R.bool.isTablet);
        if (isTablet){
            API_KEY= SDKConfig.API_KEY_TAB;
        }else {
            API_KEY=SDKConfig.API_KEY_MOB;
        }

        ENSdkBaseClient client = new ENSdkBaseClient(ENSdkBaseGateway.ENVEU, SDKConfig.CONFIG_BASE_URL, API_KEY, SDKConfig.CONFIG_VERSION, ENSdkBasePlatform.android.name(), isTablet, getDeviceId(context.getContentResolver()), BuildConfig.VERSION_NAME);
        ENSdkBaseConfiguration.Companion.getInstance().clientSetup(client);

        boolean _date = verifyDmsDate(KsPreferenceKeys.getInstance().getString("DMS_Date", "mDate"));
        Logger.w("configResponse", _date + "");
        if (_date) {
            ENSdkBaseCategoryServices.Companion.getInstance().configServiceV1_0(new ConfigEnveuCallBack() {

                @Override
                public void success(boolean status, @Nullable Object configResponse) {
                    if (status) {
                        if (configResponse!=null){
                            Gson gson = new Gson();
                            String json = gson.toJson(configResponse);
                            Logger.w("configResponse str", json);
                            KsPreferenceKeys.getInstance().setString("DMS_Response", json);
                            KsPreferenceKeys.getInstance().setString("DMS_Date", "" + System.currentTimeMillis());
                            ConfigBean config = gson.fromJson(json,ConfigBean.class);
                            KsPreferenceKeys.getInstance().setSUBSCRIPTION_BASE_URL(config == null ? "" : config.getData().getAppConfig().getSubscriptionBaseUrl());
                            KsPreferenceKeys.getInstance().setPAYMENT_BASE_URL(config == null ? "" : config.getData().getAppConfig().getPaymentBaseUrl());
                            KsPreferenceKeys.getInstance().setEXPERIENCE_BASE_URL(config == null ? "" : config.getData().getAppConfig().getExperienceBaseUrl());
                            KsPreferenceKeys.getInstance().setMONETIZATION_BASE_URL(config == null ? "" : config.getData().getAppConfig().getMonetizationBaseURL());
                            assert config != null;
                            Logger.w("configResponse str2", config.getData().getAppConfig().getNavScreens().get(0).getScreenName()+ "");
                            ENSdkBaseCategoryServices.Companion.getInstance().colorConfigService(2,2,context,new ColorConfigEnveuCallBack() {
                                @Override
                                public void colorConfigSuccess(boolean status, @Nullable JsonObject configBean) {
                                    Logger.w("colorConfigResponse str1", configBean+ "");
                                    assert configBean != null;
                                }

                                @Override
                                public void colorConfigfailure(boolean status, int errorCode, @NonNull String message) {
                                    Logger.w("colorConfigResponse str2", status+ "");
                                }
                            });
                            callBack.onSuccess(config.getData());
                        }
                        }
                        else {
                        ENSdkEnveuLogs.INSTANCE.printWarning("errorOccurred f");

                        isDmsDataStored = checkPreviousDmsResponse();
                        if (isDmsDataStored) {
                            KsPreferenceKeys.getInstance().setString("DMS_Date", "" + System.currentTimeMillis());
                            callBack.onSuccess(configBean);
                        } else {
                            ApiErrorModel errorModel = new ApiErrorModel(400, "");
                            callBack.onError(errorModel);
                        }
                    }
                }

                @Override
                public void failure(boolean status, int errorCode, String errorMessage) {
                    ENSdkEnveuLogs.INSTANCE.printWarning("errorOccurred ff");

                    isDmsDataStored = checkPreviousDmsResponse();
                    if (isDmsDataStored) {
                        KsPreferenceKeys.getInstance().setString("DMS_Date", "" + System.currentTimeMillis());
                        callBack.onSuccess(configBean);
                    } else {
                        ApiErrorModel errorModel = new ApiErrorModel(400, "");
                        callBack.onError(errorModel);
                        Logger.d("redirectionss inthree");
                    }
                }
            });

        } else {
            Logger.d("redirectionss intwo");
            callBack.onSuccess(configBean);
        }

        return false;
    }

    private boolean checkPreviousDmsResponse() {
        return AppCommonMethod.getConfigResponse() != null;
    }

    private boolean verifyDmsDate(String storedDate) {
        boolean verifyDms = false;
        if (storedDate == null || storedDate.equalsIgnoreCase("mDate")) {
            verifyDms = true;
            return verifyDms;
        }

        String currentDate = getDateTimeStamp(System.currentTimeMillis());
        String temp = getDateTimeStamp(Long.parseLong(storedDate));
        verifyDms = !currentDate.equalsIgnoreCase(temp);

        return verifyDms;
    }

    private String getDateTimeStamp(Long timeStamp) {
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
        return formatter.format(timeStamp);
    }

    public static String getDeviceId(ContentResolver contentResolver) {
        return Settings.Secure.getString(contentResolver,
                Settings.Secure.ANDROID_ID);
    }

}
