package com.enveu.utils.config;

import android.util.Log;

import com.enveu.beanModelV3.continueWatching.DataItem;
import com.enveu.beanModelV3.playListModelV2.VideosItem;
import com.enveu.beanModelV3.searchV2.ItemsItem;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent;
import com.enveu.beanModelV3.videoDetailsV2.EnveuVideoDetails;
import com.enveu.bean_model_v2_0.listAll.Item;
import com.enveu.bean_model_v2_0.videoDetailBean.Data;
import com.enveu.client.epgListing.epgResponseNew.MediaContent;
import com.enveu.utils.Constants;
import com.enveu.utils.Logger;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.StringUtils;

import java.util.Objects;

public class ImageLayer {
    private static ImageLayer imageLayerInstance;

    private ImageLayer() {

    }

    public static ImageLayer getInstance() {
        if (imageLayerInstance == null) {
            imageLayerInstance = new ImageLayer();
        }
        return (imageLayerInstance);
    }


    public String getPosterImageUrl1(ItemsItem videoItem,boolean isMusicApp) {
        String finalUrl = "";
        String imageTpe = "";
        if (isMusicApp) {
            imageTpe = AppConstants.SQR;
        } else  {
            imageTpe = AppConstants.LDS;
        }
        try {
            if (videoItem.getImages()!=null) {
                if (videoItem.getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getImages().size(); i++) {
                        if (videoItem.getImages().get(i).getImageContent().getTag().equalsIgnoreCase(imageTpe)) {
                            finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                            Log.w("imageURL", finalUrl);
                            break;
                        }
                    }
                }
            }
        } catch (Exception ignored) {

        }
        Log.w("imageURL", finalUrl);
        return finalUrl;
    }


    public String getLivePosterImageUrl(MediaContent itemsItem) {
        String finalUrl = "";
        if (itemsItem.getLiveContent().getImages().size() > 0) {
            for (int i = 0; i < itemsItem.getLiveContent().getImages().size(); i++) {
                if (itemsItem.getLiveContent().getImages().get(i).isIsDefault()) {
                    finalUrl = itemsItem.getLiveContent().getImages().get(i).getSrc();
                    Log.w("imageURL", finalUrl);
                    break;
                }
            }
        }
        return finalUrl;
    }

    public String getPosterImageUrl(VideosItem videoItem, String imageType) {
        String finalUrl = "";
        try {
            if (videoItem.getImages() != null && !StringUtils.isNullOrEmpty(imageType) && videoItem.getImages().size() > 0) {
                for (int i = 0; i < videoItem.getImages().size(); i++) {
                    if (videoItem.getImages().get(i).getImageContent().getImageType().equalsIgnoreCase(imageType)) {
                        finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                        Log.w("imageURL", finalUrl);
                        break;
                    }
                }
            }
        } catch (Exception ignored) {
        }
        Log.d("Images", "getPosterImageUrl: " + finalUrl);
        return finalUrl;
    }


    public String getSongsPosterImageUrl(ItemsItem videoItem, String imageType) {
        String finalUrl = "";
        try {
            if (videoItem != null && videoItem.getImages() != null && !videoItem.getImages().isEmpty() && !videoItem.getImages().get(0).getImageContent().getSrc().isEmpty()) {
                for (int i = 0; i < videoItem.getImages().size(); i++) {
                    if (videoItem.getImages().get(i).getImageType().equals(imageType))
                     finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                }
            } else {
                if (videoItem != null && videoItem.getCustomData() != null && videoItem.getCustomData().getSongs_albums_id() != null && !StringUtils.isNullOrEmpty(imageType)) {
                    // Ensure getImages() is not null and has items before accessing the list
                    if (videoItem.getCustomData().getSongs_albums_id().getImages() != null
                            && !videoItem.getCustomData().getSongs_albums_id().getImages().isEmpty()) {
                        for (int i = 0; i < videoItem.getCustomData().getSongs_albums_id().getImages().size(); i++) {
                            if (videoItem.getCustomData().getSongs_albums_id().getImages().get(i).getImageType().equals(imageType)) {
                                finalUrl = videoItem.getCustomData().getSongs_albums_id().getImages().get(i).getSrc();
                                Log.w("imageURL", finalUrl);
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception ignored) {
            Log.d("Images", "SongsPosterUrl: " + finalUrl);
        }
        Log.d("Images", "SongsPosterUrl: " + finalUrl);
        return finalUrl;
    }


    public String getSongsPosterImageUrl(com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetailsBean videoItem, String imageType) {
        String finalUrl = "";
        try {
            if (videoItem != null && videoItem.getData().getImages() != null && !videoItem.getData().getImages().isEmpty() && !videoItem.getData().getImages().get(0).getImageContent().getSrc().isEmpty()) {
                for (int i = 0; i < videoItem.getData().getImages().size(); i++) {
                    finalUrl =  videoItem.getData().getImages().get(i).getImageContent().getSrc();
                }
            } else {
                if (videoItem != null && videoItem.getData().getCustomData() != null && videoItem.getData().getCustomData().getSongs_albums_id() != null && !StringUtils.isNullOrEmpty(imageType)) {
                    // Ensure getImages() is not null and has items before accessing the list
                    if (videoItem.getData().getCustomData().getSongs_albums_id().getImages() != null
                            && !videoItem.getData().getCustomData().getSongs_albums_id().getImages().isEmpty()) {
                        for (int i = 0; i < videoItem.getData().getCustomData().getSongs_albums_id().getImages().size(); i++) {
                            if (videoItem.getData().getCustomData().getSongs_albums_id().getImages().get(i).getImageType().equals(imageType)) {
                                finalUrl = videoItem.getData().getCustomData().getSongs_albums_id().getImages().get(i).getSrc();
                                Log.w("imageURL", finalUrl);
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception ignored) {
            Log.d("Images", "SongsPosterUrl: " + finalUrl);
        }
        Log.d("Images", "SongsPosterUrl: " + finalUrl);
        return finalUrl;
    }

    public String getPosterImageUrl(com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetailsBean videoItem, String imageType) {
        String finalUrl = "";
        try {
            if (videoItem != null && videoItem.getData().getImages() != null && !videoItem.getData().getImages().isEmpty()) {
                for (int i = 0; i < videoItem.getData().getImages().size(); i++) {
                    if (videoItem.getData().getImages().get(i).getImageContent() !=  null && videoItem.getData().getImages().get(i).getImageContent().getSrc() != null){
                        if (videoItem.getData().getImages().get(i).getImageContent().getImageType() != null && Objects.equals(videoItem.getData().getImages().get(i).getImageContent().getImageType(), Constants.SIXTEEN_INTO_NINE)) {
                            finalUrl = videoItem.getData().getImages().get(i).getImageContent().getSrc();
                            break;
                        }
                    }
                }
            }
        } catch (Exception ignored) {
            Log.d("Images", "SongsPosterUrl: " + finalUrl);
        }
        Log.d("Images", "SongsPosterUrl: " + finalUrl);
        return finalUrl;
    }

    public boolean getShowTitle(com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetailsBean videoItem, String imageType) {
        boolean showTitle = true;
        try {
            if (videoItem != null && videoItem.getData().getImages() != null && !videoItem.getData().getImages().isEmpty()) {
                for (int i = 0; i < videoItem.getData().getImages().size(); i++) {
                    if (videoItem.getData().getImages().get(i).getImageContent() !=  null && videoItem.getData().getImages().get(i).getImageContent().getSrc() != null){
                        if (videoItem.getData().getImages().get(i).getImageContent().getImageType() != null && Objects.equals(videoItem.getData().getImages().get(i).getImageContent().getImageType(), Constants.SIXTEEN_INTO_NINE)) {
                            showTitle = videoItem.getData().getImages().get(i).getImageContent().isShowTitle();
                            break;
                        }
                    }
                }
            }
        } catch (Exception ignored) {
            Log.d("Images", "SongsPosterUrl: " + showTitle);
        }
        Log.d("Images", "SongsPosterUrl: " + showTitle);
        return showTitle;
    }


    public String getSongsPosterImageUrl(VideosItem videoItem, String imageType) {
        String finalUrl = "";
        try {
            if (videoItem != null && videoItem.getImages() != null && !videoItem.getImages().isEmpty() && !videoItem.getImages().get(0).getImageContent().getSrc().isEmpty()) {
                for (int i = 0; i < videoItem.getImages().size(); i++) {
                    if (videoItem.getImages().get(i).getImageContent().getImageType().equalsIgnoreCase(imageType)) {
                        finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                    }
                }
            } else {
                if (videoItem != null && videoItem.getCustomData() != null && videoItem.getCustomData().getSongs_albums_id() != null && !StringUtils.isNullOrEmpty(imageType)) {
                    // Ensure getImages() is not null and has items before accessing the list
                    if (videoItem.getCustomData().getSongs_albums_id().getImages() != null
                            && !videoItem.getCustomData().getSongs_albums_id().getImages().isEmpty()) {
                        for (int i = 0; i < videoItem.getCustomData().getSongs_albums_id().getImages().size(); i++) {
                            if (videoItem.getCustomData().getSongs_albums_id().getImages().get(i).getImageType().equals(imageType)) {
                                finalUrl = videoItem.getCustomData().getSongs_albums_id().getImages().get(i).getSrc();
                                Log.w("imageURL", finalUrl);
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception ignored) {
            Log.d("Images", "SongsPosterUrl: " + finalUrl);
        }
        Log.d("Images", "SongsPosterUrl: " + finalUrl);
        return finalUrl;
    }


    public String getSongsPosterImageUrl(DataItem videoItem, String imageType) {
        String finalUrl = "";
        try {
            if (videoItem != null && videoItem.getImages() != null && !videoItem.getImages().isEmpty() && !videoItem.getImages().get(0).getImageContent().getSrc().isEmpty()) {
                for (int i = 0; i < videoItem.getImages().size(); i++) {
                    if (videoItem.getImages().get(i).getImageContent().getImageType().equalsIgnoreCase(imageType)) {
                        finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                    }
                }
            } else {
                if (videoItem != null && videoItem.getCustomData() != null && videoItem.getCustomData().getSongs_albums_id() != null && !StringUtils.isNullOrEmpty(imageType)) {
                    // Ensure getImages() is not null and has items before accessing the list
                    if (videoItem.getCustomData().getSongs_albums_id().getImages() != null
                            && !videoItem.getCustomData().getSongs_albums_id().getImages().isEmpty()) {
                        for (int i = 0; i < videoItem.getCustomData().getSongs_albums_id().getImages().size(); i++) {
                            if (videoItem.getCustomData().getSongs_albums_id().getImages().get(i).getImageType().equals(imageType)) {
                                finalUrl = videoItem.getCustomData().getSongs_albums_id().getImages().get(i).getSrc();
                                Log.w("imageURL", finalUrl);
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception ignored) {
            Log.d("Images", "SongsPosterUrl: " + finalUrl);
        }
        Log.d("Images", "SongsPosterUrl: " + finalUrl);
        return finalUrl;
    }



    public String getProfilePosterUrl(VideosItem videoItem) {
        String finalUrl = "";
        try {
            if (videoItem.getContentType().equalsIgnoreCase(AppConstants.CUSTOM)) {
                if (videoItem.getParentContent().getCustomContent().getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getParentContent().getCustomContent().getImages().size(); i++) {
                        if (videoItem.getParentContent().getCustomContent().getImages().get(i).getTag().toString().equalsIgnoreCase("img4")) {
                            finalUrl = videoItem.getParentContent().getCustomContent().getImages().get(i).getSrc();
                            Log.w("imageURL", finalUrl);
                            break;
                        }
                    }
                }
            } else if (videoItem.getContentType().equalsIgnoreCase(AppConstants.VIDEO)) {
                if (videoItem.getParentContent().getVideo().getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getParentContent().getVideo().getImages().size(); i++) {
                        if (videoItem.getParentContent().getVideo().getImages().get(i).getTag().toString().equalsIgnoreCase("img4")) {
                            finalUrl = videoItem.getParentContent().getVideo().getImages().get(i).getSrc();
                            Log.w("imageURL", finalUrl);
                            break;
                        }
                    }
                }
            }
        } catch (Exception ignored) {

        }

        return finalUrl;
    }
    public String getProfilePosterUrl(Item videoItem) {
        String finalUrl = "";
        try {
            if (videoItem.getContentType().equalsIgnoreCase(AppConstants.CUSTOM)) {
                if (videoItem.getParentContent().getCustomContent().getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getParentContent().getCustomContent().getImages().size(); i++) {
                        if (videoItem.getParentContent().getCustomContent().getImages().get(i).getTag().toString().equalsIgnoreCase("img4")) {
                            finalUrl = videoItem.getParentContent().getCustomContent().getImages().get(i).getSrc();
                            Log.w("imageURL", finalUrl);
                            break;
                        }
                    }
                }
            } else if (videoItem.getContentType().equalsIgnoreCase(AppConstants.VIDEO)) {
                if (videoItem.getParentContent().getVideo().getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getParentContent().getVideo().getImages().size(); i++) {
                        if (videoItem.getParentContent().getVideo().getImages().get(i).getTag().toString().equalsIgnoreCase("img4")) {
                            finalUrl = videoItem.getParentContent().getVideo().getImages().get(i).getSrc();
                            Log.w("imageURL", finalUrl);
                            break;
                        }
                    }
                }
            }
        } catch (Exception ignored) {

        }

        return finalUrl;
    }


    public String getPopularSearchImageUrl(VideosItem videoItem,boolean isMusicApp) {
        String finalUrl = "";
        String imageType = "";
        if (isMusicApp) {
            imageType = AppConstants.SQR;
        } else  {
            imageType = AppConstants.LDS;
        }
        try {
            if (videoItem.getImages()!=null && videoItem.getImages().size() > 0  ) {
                    for (int i = 0; i < videoItem.getImages().size(); i++) {
                        if (videoItem.getImages().get(i).getImageContent().getImageType().equalsIgnoreCase(imageType)) {
                            finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                            Log.w("imageURL", finalUrl);
                            break;
                        }
                    }
            }
        } catch (Exception ignored) {

        }

        return finalUrl;
    }

    public String getLiveImageUrl(VideosItem videoItem) {
        String finalUrl = "";
            if (videoItem.getContentType().equalsIgnoreCase(AppConstants.LIVE)) {
                if (videoItem.getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getImages().size(); i++) {
                        if (videoItem.getImages().get(i).getImageContent().getImageType().equals(Constants.SIXTEEN_INTO_NINE)) {
                            finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                            break;
                        }
                    }
                }
            }
        return finalUrl;
    }

    public String getLiveImageUrl(EnveuVideoDetails videoItem) {
        String finalUrl = "";
        if (videoItem.getContentType().equalsIgnoreCase(AppConstants.LIVE)) {
            if (videoItem.getLiveContent().getImages().size() > 0) {
                for (int i = 0; i < videoItem.getLiveContent().getImages().size(); i++) {
                    if (videoItem.getLiveContent().getImages().get(i).isDefault()) {
                        finalUrl = videoItem.getLiveContent().getImages().get(i).getSrc();
                        break;
                    }
                }
            }
        }
        return finalUrl;
    }



    public String getPosterImageUrl(ItemsItem videoItem) {
        String finalUrl = "";
        try {
            if (videoItem.getVideo().getImages().size() > 0) {
                for (int i = 0; i < videoItem.getVideo().getImages().size(); i++) {
                    if (videoItem.getVideo().getImages().get(i).isDefault()) {
                        finalUrl = videoItem.getVideo().getImages().get(i).getSrc();
                        Log.w("imageURL", finalUrl);
                        break;
                    }
                }
            }


        } catch (Exception ignored) {

        }
        Log.w("imageURL", finalUrl);
        return finalUrl;
    }



    public String getPosterImageUrl(DataItem videoItem) {
        String finalUrl = "";
        try {
            if (videoItem.getImages()!=null) {
                if (videoItem.getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getImages().size(); i++) {
                        if (videoItem.getImages().get(i).getImageContent().getTag().equalsIgnoreCase(AppConstants.IMG_0)) {
                            finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                            Log.w("imageURL", finalUrl);
                            break;
                        }
                    }
                }
            }
        } catch (Exception ignored) {

        }
        return finalUrl;
    }
    public ImageContent getPosterImageUrl(com.enveu.beanModelV3.videoDetailV3.EnveuVideoDetails videoItem, String imageIdentifier ) {
        ImageContent imageContent =new ImageContent();
        try {
            if (videoItem.getImages()!=null) {
                if (videoItem.getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getImages().size(); i++) {
                        if (videoItem.getImages().get(i).getImageContent().getImageType().equalsIgnoreCase(imageIdentifier)) {
                            imageContent.setSrc(videoItem.getImages().get(i).getImageContent().getSrc());
                            imageContent.setDominantColor(videoItem.getImages().get(i).getImageContent().getDominantColor());
                            imageContent.setImageType(videoItem.getImages().get(i).getImageContent().getImageType());
                           imageContent.setColorPalette(videoItem.getImages().get(i).getImageContent().getColorPalette());
                            break;
                        }
                    }
                }
            }
        } catch (Exception ignored) {

        }
        return imageContent;
    }
    public String getPosterImageUrl(EnveuVideoDetails videoItem,String imageType ) {
        String finalUrl = "";
        try {
            if (videoItem.getImages()!=null) {
                if (videoItem.getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getImages().size(); i++) {
                        if (videoItem.getImages().get(i).getImageContent().getImageType().equalsIgnoreCase(imageType)) {
                            finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                            Log.w("imageURL", finalUrl);
                            break;
                        }
                    }
                }
            }
        } catch (Exception ignored) {

        }
        return finalUrl;
    }


    public String getEpisodePosterImageUrl(Item videoItem, String imageType) {
        String finalUrl = "";
        try {
            if (videoItem.getImages()!=null) {
                if (videoItem.getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getImages().size(); i++) {
                        if (videoItem.getImages().get(i).getImageContent().getImageType().equalsIgnoreCase(imageType)) {
                            finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                            Log.w("imageURL", finalUrl);
                            break;
                        }
                    }
                }
            }
        } catch (Exception ignored) {

        }


        Log.w("imageURL", finalUrl);
        return finalUrl;
    }


    public String getEpisodePosterImageUrl(Data videoItem, String imageType) {
        String finalUrl = "";
        try {
            if (videoItem.getImages()!=null) {
                if (videoItem.getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getImages().size(); i++) {
                        if (videoItem.getImages().get(i).getImageContent().getImageType().equalsIgnoreCase(imageType)) {
                            finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                            Log.w("imageURL", finalUrl);
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            Logger.e(e);
        }
        return finalUrl;
    }

    public boolean getShowTitle(Data videoItem, String imageType) {
        boolean showTitle = true;
        try {
            if (videoItem.getImages()!=null) {
                if (videoItem.getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getImages().size(); i++) {
                        if (videoItem.getImages().get(i).getImageContent().getImageType().equalsIgnoreCase(imageType)) {
                            showTitle = videoItem.getImages().get(i).getImageContent().isShowTitle();
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            Logger.e(e);
        }
        return showTitle;
    }

    public String getRelatedContentImageUrl(VideosItem videoItem, boolean isMusicApp) {
        String imageType = "";
        if (isMusicApp) {
            imageType = AppConstants.SQR;
        } else  {
            imageType = AppConstants.LDS;
        }
        String finalUrl = "";
        try {
            if (videoItem.getImages()!=null) {
                if (videoItem.getImages().size() > 0) {
                    for (int i = 0; i < videoItem.getImages().size(); i++) {
                        if (videoItem.getImages().get(i).getImageContent().getImageType().equalsIgnoreCase(imageType)) {
                            finalUrl = videoItem.getImages().get(i).getImageContent().getSrc();
                            Log.w("imageURL", finalUrl);
                            break;
                        }
                    }
                }

            }
        } catch (Exception ignored) {
            Log.d("Exception", "getRelatedContentImageUrl: $ignored");
        }

        return finalUrl;
    }


}
