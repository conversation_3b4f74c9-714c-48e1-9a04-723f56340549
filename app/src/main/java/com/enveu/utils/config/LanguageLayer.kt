package com.enveu.utils.config

import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys

object LanguageLayer {

    fun getCurrentLanguageCode():String {
        val preference:KsPreferenceKeys? = KsPreferenceKeys.getInstance()
      val getLocale = if (preference?.appLanguage.equals(AppConstants.LANGUAGE_ARABIC, ignoreCase = true)){
          preference?.configLocaleData?.arabic?:AppConstants.LANGUAGE_ARABIC
       }
       else {
          preference?.configLocaleData?.english?:AppConstants.ENGLISH_LAN_CODE
      }
        return getLocale
    }
}

