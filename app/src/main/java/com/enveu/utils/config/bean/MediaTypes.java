
package com.enveu.utils.config.bean;

import com.google.gson.annotations.SerializedName;


public class MediaTypes {

    @SerializedName("trailer")
    private String trailer;

    @SerializedName("movie")
    private String movie;

    @SerializedName("series")
    private String series;

    @SerializedName("show")
    private String show;

    @SerializedName("episode")
    private String episode;

    @SerializedName("live")
    private String live;

    @SerializedName("news")
    private String news;

    @SerializedName("contest")
    private String contest;

    @SerializedName("expedition")
    private String expedition;

    @SerializedName("event")
    private String event;

    @SerializedName("offer")
    private String offer;

    @SerializedName("agency")
    private String agency;

    @SerializedName("image")
    private String image;

    @SerializedName("marketPlace")
    private String marketPlace;

    @SerializedName("podcast")
    private String podcast;

    @SerializedName("gaming")
    private String gaming;

    @SerializedName("reel")
    private String reel;

    @SerializedName("documentary")
    private String documentary;

    public String getTrailer() {
        return trailer;
    }

    public void setTrailer(String trailer) {
        this.trailer = trailer;
    }

    public String getMovie() {
        return movie;
    }

    public void setMovie(String movie) {
        this.movie = movie;
    }

    public String getSeries() {
        return series;
    }

    public void setSeries(String series) {
        this.series = series;
    }

    public String getShow() {
        return show;
    }

    public void setShow(String show) {
        this.show = show;
    }

    public String getEpisode() {
        return episode;
    }

    public void setEpisode(String episode) {
        this.episode = episode;
    }

    public String getLive() {
        return live;
    }

    public void setLive(String live) {
        this.live = live;
    }

    public String getNews() {
        return news;
    }

    public void setNews(String news) {
        this.news = news;
    }

    public String getContest() {
        return contest;
    }

    public void setContest(String contest) {
        this.contest = contest;
    }

    public String getExpedition() {
        return expedition;
    }

    public void setExpedition(String expedition) {
        this.expedition = expedition;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getOffer() {
        return offer;
    }

    public void setOffer(String offer) {
        this.offer = offer;
    }

    public String getAgency() {
        return agency;
    }

    public void setAgency(String agency) {
        this.agency = agency;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getMarketPlace() {
        return marketPlace;
    }

    public void setMarketPlace(String marketPlace) {
        this.marketPlace = marketPlace;
    }

    public String getPodcast() {
        return podcast;
    }

    public void setPodcast(String podcast) {
        this.podcast = podcast;
    }

    public String getGaming() {
        return gaming;
    }

    public void setGaming(String gaming) {
        this.gaming = gaming;
    }

    public String getReel() {
        return reel;
    }

    public void setReel(String reel) {
        this.reel = reel;
    }

    public String getDocumentary() {
        return documentary;
    }

    public void setDocumentary(String documentary) {
        this.documentary = documentary;
    }
}
