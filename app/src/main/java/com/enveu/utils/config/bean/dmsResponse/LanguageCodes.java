package com.enveu.utils.config.bean.dmsResponse;

import com.google.gson.annotations.SerializedName;

 public class LanguageCodes {

    @SerializedName("arabic")
    private String arabic;

    @SerializedName("english")
    private String english;

    @SerializedName("spanish")
    private String spanish;

     public String getArabic() {
         return arabic;
     }

     public String getSpanish(){ return spanish; }

     public void setArabic(String arabic) {
         this.arabic = arabic;
     }

     public void setSpanish(String spanish){ this.spanish = spanish; }

     public String getEnglish() {
         return english;
     }

     public void setEnglish(String english) {
         this.english = english;
     }
 }
