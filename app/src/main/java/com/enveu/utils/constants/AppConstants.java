package com.enveu.utils.constants;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface AppConstants {
    String CONTENT_PREFERENCE = "contentPreference";
    String MY_VIPA_ENCRYPTION_KEY="MYMVHUB$KEY";
    String APP_CONTINUE_WATCHING = "CONTINUE WATCHING";
    String EMAIL_REGEX = "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@"
            + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";
    String PLATFORM = "MOBILE";
    String PROFILE_FOLDER = "/180x180/filters:format(jpeg):max_bytes(400):quality(100)/app/user/profilePicture/";
    String GENRE_IMAGES_BASE_KEY = "/cms/images/taxonomy/genre/";
    String PERSONALIZED = "PERSONALIZED";
    String CAST_CREW_IMAGES_BASE_KEY = "/cms/images/taxonomy/castCrew/";
    String SERIES_IMAGES_BASE_KEY = "/cms/images/library/series/";
    String VIDEO_IMAGE_BASE_KEY = "/cms/images/library/video/";
    String FILTER = "/filters:";
    String SEARCH_GENRE_CONSTATNT = "genre:";
    String SEARCH_SORT_CONSTATNT = "title.keyword:";
    String FILTER_CUSTOM_FIELDS = "customFields.";
    String CF_IS_AUDIO_DESC = "is_audio_description";
    String CF_IS_CLOSE_CAPTION = "is_close_caption";
    String CF_IS_SOUND_TRACK = "is_sound_track";
    String CF_IS_SIGN_LANG = "is_signed_language_enabled";
    String CF_IS_4K = "is4k";
    String QUALITY = "quality";
    String VOD = "VIDEO";
    String GROUP = "GROUP";
    String SERIES = "SERIES";
    String PUBLISHED = "PUBLISHED";
    String NOT_FOR_SALE = "NOT_FOR_SALE";
    String CAST_AND_CREW = "CAST_AND_CREW";
    String GENRE = "GENRE";
    String SQUARE = "SQUARE";
    String CIRCLE = "CIRCLE";
    String LANDSCAPE = "WIDE_SCREEN_LANDSCAPE";
    String POTRAIT = "WIDE_SCREEN_PORTRAIT";
    String POSTER_LANDSCAPE = "POSTER_LANDSCAPE";
    String POSTER_POTRAIT = "POSTER_PORTRAIT";
    String Season = "SEASON";
    String Episode = "EPISODE";
    String Shows = "SHOWS";
    String Trailer = "TRAILER";
    String ENGLISH_LAN_CODE = "en-US";
    String LANGUAGE_ARABIC = "ar-SA";
    String LANGUAGE_ARABIC_CODE = "ar";
    String LANGUAGE_ENGLISH_CODE = "en";
    String Series = "SERIES";
    String Movie = "MOVIE";
    String MATCHES = "MATCHES";
    String CLIP = "CLIP";
    String ARTISTS= "ARTISTS";
    String ALBUM= "ALBUMS";
    String QUEUE= "QUEUE";
    String SINGLES= "SINGLES";
    String Movies = "MOVIES";


    String MY_FAVOURITE = "MY_FAVOURITE";
    String MY_PLAYLIST = "MY_PLAYLIST";
    String SCREEN = "SCREEN";
    String MAIN = "MAIN";
    String CUSTOM_VALUE = "CUSTOM_VALUE";

    String Video = "VIDEO";
    String API_PARAM_NAME = "name";
    String API_PARAM_EMAIL = "email";
    String API_PARAM_PASSWORD = "password";
    String API_PARAM_STATUS = "status";
    String API_PARAM_PROFILE_PIC = "profilePicURL";
    String API_PARAM_GENDER = "gender";
    String API_PARAM_DOB = "dateOfBirth";
    String API_PARAM_IS_VERIFIED = "verified";
    String API_PARAM_VERIFICATION_DATE = "verificationDate";
    String API_PARAM_PHONE_NUMBER = "phoneNumber";
    String API_PARAM_EXPIRY_DATE = "expiryDate";
    String API_PARAM_PROFILE_STEP = "profileStep";
    String API_PARAM_NEW_PWD = "newPassword";
    String API_PARAM_FB_ID = "fbId";
    String API_PARAM_EMAIL_ID = "emailId";
    String API_PARAM_FB_TOKEN = "accessToken";
    String API_PARAM_IS_FB_EMAIL = "fbMail";
    String API_PARAM_FB_PIC = "profilePicUrl";
    String API_PARAM_WATCHLIST_ID = "watchListItemId";
    String API_PARAM_LIKE_ID = "id";
    String API_PARAM_LIKE_TYPE = "type";
    String API_PARAM_PAGE = "page";
    String API_PARAM_SIZE = "size";
    String API_PARAM_SERIES_ID = "seriesId";
    String API_PARAM_SEASON_ID = "seasonId";
    String API_PARAM_TYPE = "type";
    String API_PARAM_ID = "id";
    String API_PARAM_FETCH_DATA = "fetchData";
    String API_RESPONSE_CODE = "responseCode";
    String API_PARAM_USER_ASSET_LIST_DTO = "userAssetListDTOList";
    int RESPONSE_CODE_LOGOUT=401;
    int RESPONSE_CODE_ERROR = 500;
    String EXTRA_REGISTER_USER = "extraRegisterUser";
    String UNPUBLISHED = "UNPUBLISHED";
    String APP_PREF_PROFILE = "profileDetail";
    String APP_PREF_ACCESS_TOKEN = "accessToken";
    String APP_PREF_LOGIN_STATUS = "loginStatus";
    String FINAL_APP_PREF_LOGIN_STATUS = "status";
    String APP_PREF_GET_USER = "geLtUser";
    String APP_PREF_LOGIN_TYPE = "userLoginType";
    String APP_PREF_USER_ID = "userId";
    String APP_PREF_AVAILABLE_VERSION = "availableVersion";
    String APP_PREF_CFEP = "cloudFrontEndpoint";
    String APP_PREF_VIDEO_URL = "cloudFrontVideoUrl";
    String APP_PREF_CONFIG_VERSION = "configVersion";
    String APP_PREF_SERVER_BASE_URL = "serverBaseURL";
    String APP_PREF_CONFIG_RESPONSE = "Config_Response";
    String APP_PREF_LAST_CONFIG_HIT = "LastConfigTime";
    String APP_PREF_JUMP_BACK = "returnBack";
    String SAVE_PRIMARY_ACCOUNT = "save_primary_account";
    String APP_PREF_IS_EPISODE = "isEpisode";
    String APP_PREF_JUMP_TO = "returnTo";
    String USER_ID = "user_id";
    String APP_PREF_VIDEO_POSITION = "videoPosition";
    String APP_PREF_GOTO_PURCHASE = "returnPurchase";
    String APP_PREF_ASSET_ID = "assetId";
    String APP_PREF_SELECTED_SEASON_ID = "seasonId";
    String APP_PREF_HAS_SELECTED_ID = "hasSelectedId";
    String APP_PREF_IS_RESTORE_STATE = "isRestoreState";
    String HOME_ENVEU = "0";
    String ORIGINAL_ENVEU = "1";
    String PREMIUM_ENVEU = "2";
    String SINETRON_ENVEU = "3";
    String WIDGET_TYPE_AD = "ADS";

    String WIDGET_ID = "widget_id";
    String WIDGET_NAME = "widget_name";
    String PLAY_LIST_ID = "playlist_id";
    String CONTENT_ID = "content_id";
    String CONTENT_TITTLE = "content_title";
    String CONTENT_TYPE = "content_type";


    String WIDGET_TYPE_CONTENT = "CNT";
    String KEY_MREC = "MREC";
    String KEY_BANNER = "BANNER";
    int CAROUSEL_LDS_LANDSCAPE = 10;
    int CAROUSEL_LDS_BANNER = 11;
    int CAROUSEL_PR_POTRAIT = 12;
    int CAROUSEL_PR_POSTER = 13;
    int CAROUSEL_SQR_SQUARE = 14;
    int CAROUSEL_CIR_CIRCLE = 15;
    int CAROUSEL_CST_CUSTOM = 16;
    int HERO_LDS_LANDSCAPE = 20;
    int HERO_LDS_BANNER = 21;
    int HERO_PR_POTRAIT = 22;
    int HERO_PR_POSTER = 23;
    int HERO_SQR_SQUARE = 24;
    int HERO_CIR_CIRCLE = 25;
    int HERO_CST_CUSTOM = 26;
    int HORIZONTAL_LDS_LANDSCAPE = 31;
    int HORIZONTAL_PR_POTRAIT = 33;
    int HORIZONTAL_PR_POSTER = 34;
    int HORIZONTAL_SQR_SQUARE = 35;
    int HORIZONTAL_CIR_CIRCLE = 36;
    int ADS_BANNER = 41;
    int ADS_MREC = 42;
    int GRD_HORIZONTAL_LDS_LANDSCAPE = 43;
    int GRD_HORIZONTAL_PR_PORTRAIT = 44;
    int GRD_HORIZONTAL_PR_POSTER = 45;
    int GRD_HORIZONTAL_SQR_SQUARE = 46;
    int GRD_HORIZONTAL_CIR_CIRCLE = 47;
    int HORIZONTAL_3BY2_LANDSCAPE=48;
    int CAROUSEL_3BY2 = 49;
    int HERO_LANDSCAPE_3X2=50;

    String BUNDLE_VIDEO_ID_BRIGHTCOVE = "videoId";
    String BUNDLE_TAB_ID = "tabId";
    String BUNDLE_ASSET_ID = "assestId";
    String BUNDLE_SERIES_ID = "seriesId";
    String BUNDLE_IS_PREMIUM = "isPremium";
    String BUNDLE_DURATION = "duration";
    String BUNDLE_ASSET_BUNDLE = "assestIdBundle";
    String BUNDLE_CONTENT_SLUG = "contentSlugBundle";
    String BUNDLE_BIO_TITLE = "titleBioBundle";
    String BIO_DISCRIPTION="discriptionBio";
    String THUMBNAIL_IMG = "thumbnail_img";
    String WEB_VIEW_HEADING = "WebViewHeading";
    String WEB_VIEW_URL = "WebVieweURl";
    String BUNDLE_BANNER_IMAGE = "bannerImage";
    String POSTER_URL = "posterUrl";
    String BUNDLE_SEASON_COUNT="seasonCount";
    String BUNDLE_SEASON_ARRAY="seasonArray";
    String BUNDLE_SEASON_NUMBER = "seasonPosition";
    String BUNDLE_TRAILER_REF_ID = "trailerRefId";
    String EXTRA_TRAILER_DETAILS = "extra_trailer_details";
    String EXTRA_SHOW_PRE_ROLL_VIDEO = "extra_show_pre_roll_video";
    String IS_SIGN_LANG_ENABLE = "signLangParentRefId";
    String AUDIO_TRACK_ITEM = "audioTracks";
    String SIGN_LANG_ID = "signLangId";
    String IS_PODCAST = "podcast";
    String BUNDLE_SELECTED_SEASON="selectedSeasonId";
    String BUNDLE_DETAIL_TYPE = "detailType";
    String BUNDLE_SERIES_DETAIL = "seriesDetail";
    String BOOKMARK_POSITION = "bookmarkPosition";
    String PLAYER_ASSET_TITLE = "playerAssetTitle";
    String PLAYER_ASSET_MEDIATYPE = "playerMediaType";
    int PAGE_SIZE = 20;
    String BUNDLE_ASSET_TYPE = "assetType";
    String BUNDLE_MEDIA_TYPE = "mediaType";
    String GAME_ASSET_TYPE =  "ASSET_TYPE";
    String GAME_CUSTOM_TYPE = "CUSTOM_TYPE";
    String BUNDLE_CURRENT_ASSET_ID = "currentAssetId";
    String SEARCH_TYPE_PROGRAM = "search_type_program";
    String SHOULD_PLAY_QUEUE_ITEM = "should_play_queue_item";
    @Nullable String AUDIO_INTERACTION_REDIRECTION="AUDIO_INTERACTION_REDIRECTION";
    String BOTTOMDAILOGFRAG = "BOTTOMDAILOGFRAG";
    @NotNull String WATCHLISTACTIVITY="WATCHLISTACTIVITY";
    @NotNull String PLAYLISTDETAIL="PLAYLISTDETAIL";

    @Nullable String ALL_SONG_LIST="all_song_list";
    @Nullable String WATCH_HISTORY="WATCH_HISTORY";
    @Nullable String WATCH_LIST="WATCH_LIST";
    String COUNTRY_CODE = "countryCode";
    @Nullable String SECOND_CONTAINER="SECOND_CONTAINER";
    @Nullable String FULL_PLAYER="FULL_PLAYER";
    @Nullable String ENT_SERIES="ENT_SERIES";
    String PLAY_BACK_BASE_URL = "https://cdn.jwplayer.com/manifests/";
    @NotNull Double PREVIOUS_SONG_CHANGE_COUNTER=3.0;
    @Nullable String EPG_FRAGMENT = "EPG_FRAGMENT";
    String NO_SCHEDULE = "NO_SCHEDULE";
    String EPG_ITEM = "EPG_ITEM";
    String EPG_CHANNEL_ID = "EPG_CHANNEL_ID";
    String EPG_SHOW_ID = "EPG_SHOW_ID";
    String IS_FROM_CHANNEL_CLICK = "IS_FROM_CHANNEL_CLICK";
    String DOMAIN_ID = "88fd66fa-123c-461c-bc2e-bb1794bad33f";
    String EDITOR_FRAGMENT = "EDITOR_FRAGMENT";
    String GRID_LIST_ACTIVITY = "GRID_LIST_ACTIVITY";
    String SA_REGION="Asia/Riyadh";

    enum UserLoginType {
        Manual,
        FbLogin,
        GoogleLogin
    }

    enum UserStatus {
        Login,
        Logout
    }

    enum ContentType {
        VIDEO,
        MOVIE,
        SHOW,
        EPISODE,
        SERIES,
        SEASON,
        VOD,
        CONTINUE_WATCHING,
        MY_WATCHLIST,
        LIVE,
        ARTICLE
    }
    String USER_NAME="name";
    String UNIQUE_USER_NAME="userName";
    String USER_LAST_NAME="last name";
    String USER_GENDER = "gender";
    String USER_DOB = "dateOfBirth";
    String USER_EMAIL="email";
    String EXPIRY_TIME="expirytime";
    String ACCOUNT_ID="accountid";
    String USER_PROFILE_PIC_URL="profilePicURL";

    String SPONSOR_ARTIST_ID="sponsorArtistId";
    String SPONSOR_POSTER="sponsorposter";
    String SPONSOR_TITLE = "sponsertitle";
    String ASSET_TYPE = "assettype";
    String LIGHT_THEME = "LightTheme";
    String DARK_THEME = "DarkTheme";
    String FCM_TOKEN = "fcm_token";
    String HUNTER = "Hunter";
    String FISHERMAN = "FisherMan";
    String BOTH = "Both";
    String FROM = "From";
    String TITLE = "title";
    String MESSAGE = "message";
    String ACTION_BTN = "action_btn";
    String CANCEL_BTN = "cancel_btn";
    String SHOULD_DESCRIPTION_CENTER = "should_description_center";
    String MOBILE_NUMBER="mobileNumber";
    String FB_MAIL = "fbMail";
    String PREF_ID="pref_id";
    String SPECIES_LIST = "speciesList";
    String INTENT_FROM_SETTING = "intentFromSetting";
    String PREFERENCE_PROFILE_ID = "profileId";
    String SIGN_IN_SUCCESS = "sign_in_success";
    String SIGN_UP_SUCCESS = "sign_UP_success";
    String LIVE_TV="LiveTv";
    String VOD_HOME="vod";
    String EXPENDITIONS="expedition";
    String HOME_TAG="homeTag";
    String MARKET_PLACES="marketplaces";
    String OFFERS="offers";
    String home="home";
    String vod="vod";
    String CAPS_VOD="VOD";
    String live="live";
    String expedition="expedition";
    String offers_offers="offers_offers";
    String offers_agency="offers_agency";
    String offers_species="offers_species";
    String marketPlace_product="marketPlace_product";
    String marketPlace_brand="marketPlace_brand";
    String marketPlace_categories="marketPlace_categories";
    String bigGameHunting="bigGameHunting";
    String smallGameHunting="smallGameHunting";
    String seaFishing="seaFishing";
    String continentalFishing="continentalFishing";
    String contest="contest";
    String news="news";
    String News="NEWS";
    String Event="EVENT";
    String Expedition="EXPEDITION";
    String novilites="novilites";
    String events="events";
    String series="series";
    String episode="EPISODE";
    String episodes="EPISODES";

    String INTENT_FROM = "intentFrom";
    String VIDEO = "VIDEO";
    String CUSTOM = "CUSTOM";
    String ARTICLE = "ARTICLE";
    String HIDE_TO_QUEUE = "HIDE_TO_QUEUE";

    String LIVE = "LIVE";
    String PERSON = "PERSON";
    String AUDIO = "AUDIO";
    String LINEAR_24_7 = "LINEAR_24_7";
    String VIDEO_SERIES = "VIDEO_SERIES";
    String REEL = "REEL";
    String VIDEO_SEASON = "VIDEO_SEASON";
    String VIDEO_PODCASTS_EPISODES = "VIDEO_PODCASTS_EPISODES";
    String AUDIO_SERIES = "AUDIO_SERIES";
    String AUDIO_SEASON = "AUDIO_SEASON";
    String AUDIO_EPISODES = "AUDIO_EPISODES";

    String LIVE_TVV="liveTV";
    String AGENCY = "Agency";
    String IMAGE = "image";
    String ITEM = "ITEM";
    String ENROLLED = "ENROLLED";
    String TYPE = "type";
    String SPECIES= "species";
    String LOGGED_IN= "login";
    String USER_VERIFY= "verify";
    String OFFER = "OFFER";
 /*   String UAT_LICENSE_KEY = "akIWITZoyn2qrLa73aJqHWIGSTmnPWWdx8E+SswS6T8FMHMhoNpt0sdP700=";
    String PROD_LICENSE_KEY = "Gk8hWhpetk6hSe1kA2KyaiIbLtvEcGWrv4WFiArHpdk9c4MDR9VPMrUCCvs=";
*/
    String UAT_LICENSE_KEY = "dQU071to+hwjttPkhKGRLyX8g4fqezTr7jOi6Tqsr01bj+43";
    String PROD_LICENSE_KEY = "emo9NLiNMOlsFfEOb33tG5Xz6dEu0rO0CKICdewTPRiiAC1n";
    String QA_LICENSE_KEY = "C1sJKzeiVH2jvWyNf6Mdi6tKaVOGzdCffVTGSIclagHZyDHz";
    String APPLE = "APPLE_IAP";
    String GOOGLE_IAP = "GOOGLE_IAP";
    String TWO_C_TWO_P = "TWO_C_TWO_P";
    String AMAZON_IAP = "AMAZON_IAP";
    String STRIPE = "STRIPE";
    String PAYPAL = "PAYPAL";
    String VIDEO_COMPLETED = "VIDEO_COMPLETED";
    String VIDEO_STARTED = "VIDEO_STARTED";
    String VIDEO_PAUSED="VIDEO_PAUSED";
    String TAB_SCREEN_VIEWED = "screen_viewed";
    String JOIN_US = "Join US";
    String IGO = "iGO";
    String BIG_GAME_HUNTING="Big Game Hunting";
    String SMALL_GAME_HUNTING="Small Game Hunting";
    String SEA_FISHING="Sea Fishing";
    String CONTINENTAL_FISHING = "Continental Fishing";
    String MY_LIST="My List";
    String VIEW_PROFILE="View Profile";
    String UPDATE_PROFILE= "Update Profile";
    String UPDATE_BIO= "Update Bio";
    String SETTING_CHANGE_LAN="Settings - Change language";
    String STREAMING_SETTING="Settings - Streaming settings";
    String CONTENT_PREF_PROFILE="Content Perference - Profile";
    String CONTENT_PREF_SPECIES="Content Perference - Species";
    String CONTENT_PREF_TYPES="Content Perference - Types";
    String TERMS_CONDTIONS="Terms & Conditions";
    String PRIVACY_POLICY= "Privacy Policy";
    String CONTACT_US= "Contact US";
    String FAQ="FAQ";
    String HELP_CENTER="Help Center";
    String LOGIN= "Login";
    String MANAGE_DEVICES= "Manage Devices";
    String REGISTER="Register";
    String USER_VERIFICATION="User Verification";
    String CHANGE_PASSWORD="Change Password";
    String FORGOT_PASSWORD="Forgot Password";
    String SUBSCRIPTION="Subscription";
    String SPONSOR_ARTIST="Sponsor Artist";
    String ORDER_HISTORY="Order History";
    String SEARCH_SEE_ALL="Search - See All";
    String SEARCH_TEXT = "Search text";
    String SEE_ALL_RAIL_TITTLE="See All Rail tittle";
    String CONTENT_DETAIL_TITTLE="Content Detail tittle";
    String ID ="id";
    String LANG = "lang";
    String APP_PLATFPRM = "platform";
    String ANDROID= "Android";
    String SCREEN_NAME = "screen_name";
    String CONTENT_PLAYED = "content_played";
    String CONTENT_DURATION = "content_duration";
    String PLAYBACK_POSITION = "position";
    String SEARCH_TERMS= "search_term";
    String CONTENT_PLAY = "content_play";
    String CONTENT_COMPLETED = "content_completed";
    String CONTENT_PAUSE = "content_pause";
    String SHARE_CONTENT= "share_content";
    String ADD_TO_WATCHLIST="add_to_watchlist";
    String REMOVE_WATCHLIST= "remove_watchlist";
    String SEARCH = "search";
    String SEARCH_TERM = "search_term";
    String SEARCH_CONTENT_SELECT = "search_content_select";
    String HOME = "home";
    String LOGOUT="logout";
    String GALLERY_SELECT= "gallery_select";
    String CONTENT_SELECT= "content_select";
    String RAIL_SELECT= "rail_select";
    String RAIL_NAME = "category_name";
    String RAIL_ID = "category_id";
    String CONTENT_EXIT= "content_exit";
    String LIVEACTIVITY = "LIVEACTIVITY";
    String EPISODEACTIVITY = "EpisodeActivity";
    String SERIESDEATILACTIVITY = "SeriesDetailActivity";
    String DETAILACTIVITY="DetailActivity";
    String MOVIE_DETAIL="movie_detail";
    String SERIES_DETAIL="movie_detail";
    String CONTENT_DETAIL="Content Detail";
    String FREE_RAIL="OnlyFreeUser";
    String HUNTING_HOME="hunting_home";
    String FISHING_HOME="fishing_home";
    String BOTH_HOME="both_home";
    String ENGLISH = "English";
    String SPANISH = "Spanish";
    String DUTCH = "Dutch";
    String DUTCH_LAN_CODE = "nl";
    String CONTENT_PREFERENCES= "content_preferences";
    String USER_PROFILE="user_profile";
    String REGISTER_DATE="register_date";
    String USERTYPE = "userType";
    String FREE_USER ="FreeUser";
    String PAID_USER = "PaidUser";
    String USER_SUBSCRIPTION="user_subscription";
    String CANCEL_SUBSCRIPTION="cancel_subscription";
    String CURRENCY = "currency";
    String PRICE= "price";
    String SUBSCRIPTION_TITTLE = "subscription_tittle";
    String PAYMENT_METHOD = "payment_method";
    String GOOGLE="Google";
    String Podcast = "PODCAST";
    String Gaming = "GAMING";
    String Reel = "REEL";
    String SEARCH_RESULT= "Search result";
    String SPANISH_SEARCH_RESULT= "Resultado de búsqueda";
    String QA_YOUBORA_ACCOUNT_CODE = "enveudev";
    String PROD_YOUBORA_ACCOUNT_CODE = "enveu";
    String SETTINGS = "settings";


    String IMG_0 = "img0";
    String SQUARE_IMAGE_TYPE = "1x1";

    String IMG_1 = "img1";
    String IMG_2 = "img2";
    String IMG_3 = "img3";
    String IMG_4 = "img4";

    String MORE_LIKE_TAB = "moreLike";
    int MORE_LIKE_THIS_TAB = 0;

    int TRAILER_AND_MORE = 1;
    int OTHER = 1;

    String TIMER = "timer";
    String BINGE_WATCH_POSITION = "bingeWatchPosition";
    String BINGE_WATCHING_ENABLED = "bingeWatchingEnabled";

    String CONTENT_META_DATA = "CONTENT_META_DATA";


    //Api customData key do not change this key
    String CUSTOM_DATA= "customData";
    String TRAILER_CUSTOM_DATA= "trailer-linked-with-id|OR:";
    String CLIPS_AND_MORE_CUSTOM_DATA= "parent-reference-id|OR:";

    String INTERVIEW_CUSTOM_DATA= "interview-tournament-id|OR:";
    String HIGHLIHTS_CUSTOM_DATA= "highlight-tournament-id|OR:";
    String REPLAYS_CUSTOM_DATA = "replay-tournament-id|OR:";


    String MATCH_INTERVIEW_CUSTOM_DATA = "interview-match-id|OR:";
    String MATCH_HIGHLIHTS_CUSTOM_DATA = "highlight-match-id|OR:";
    String MATCH_REPLAYS_CUSTOM_DATA = "replay-match-id|OR:";

    //ENT SEASON
    String ENT_SEASON_CUSTOM_DATA = "season-series-id|OR:";

    //TOURNAMENT SEASON
    String TOURNAMENT_SEASON_CUSTOM_DATA = "season-tournament-id|OR:";

    //ROUND FOR SEASON
    String ROUND_FOR_SEASON_CUSTOM_DATA = "round-season-id|OR:";

    //MATCH FOR ROUND
    String MATCH_FOR_ROUND_CUSTOM_DATA = "matches-round-id|OR:";

    String SONG = "song";
    String IMAGE_CONTENT = "imageContent";
    String SRC = "src";
    String ASSET_ID = "assetId";
    String SONG_LIST = "songList";

    String SONGS = "SONGS";

    String EPISODES = "EPISODES";

    String BUNDLE_EDIT_PROFILE = "bundle_edit_profile";
    String PRIMARY_ACCOUNT = "primary_account";
    String CURRENTLY_PRIMARY_ACCOUNT = "currently_primary_account";
    String ADD_SECONDARY_ACCOUNT = "add_secondary_account";
    String HIDE_BACK_BTN = "hide_back_btn";

    String PLAYER_STATE = "PlayerState";

    String DESCRIPTION = "description";
    String FROM_REDIRECTION = "fromRedirection";


    String CHANNEL_NOT_CREATED ="CHANNEL_NOT_CREATED";
    String CHANNEL_CREATED ="CHANNEL_CREATED";
    String STREAM_STARTING ="STREAM_STARTING";
    String STREAMING ="STREAMING";
    String STREAM_STOPPING ="STREAM_STOPPING";
    String STREAM_STOPPED ="STREAM_STOPPED";
    String STREAM_ERROR ="STREAM_ERROR";
    String CHANNEL_DELETED ="CHANNEL_DELETED";


    String STARTED = "STARTED";
    String END = "END";

    String ERROR = "ERROR";
    String ABOUT_END = "ABOUT_END";

    String SCHEDULED = "SCHEDULED";
    String READY = "READY";
    String NOT_ATTENDED = "NOT_ATTENDED";

    String REPLAY = "REPLAY";

    String MOVIES_AUDIO = "MOVIES_AUDIO";
    String SHOW_AUDIO = "SHOW_AUDIO";
    String TRAILER_AUDIO = "TRAILER_AUDIO";

    String JW_DRM_KEY = "D9FOc/XJD+nwIiSFIN1ZNSsT5kkyEoGjJzvmp1C1cnzoSODE";
    String JW_NON_DRM_KEY = "mqxsoU43RysI+Y2sZ6879dNLbCFckk9ik3UnV3OcnzzWyyRL";

    String PR1_9x16 = "9x16";
    String PR2_2x3 = "2x3";
    String LDS_16x9 = "16x9";
    String SQR_1x1 = "1x1";
    String CIR_1x1 = "1x1";
    String CST_NOT_SURE = "CST NOT SURE";
    String LDS2_NOT_SURE = "LDS2 NOT SURE";
    String LDS3_2_3x2 = "3x2";
    String LDS4_3_4x3 = "4x3";
    String PR3_4_3x4 = "3x4";

    String ARTICLE_CONTENT_URL = "articleUrl";

    // Constants
    String PR1 = "9x16";
    String PR2 = "2x3";
    String LDS = "16x9";
    String SQR = "1x1";
    String CIR = "1x1";
    String CST = "16x9";
    String LDS2 = "16x9";
    String LDS3 = "3x2";
    String LDS4 = "4x3";
    String PR3 = "3x4";

    String UPDATE_NAME = "updateName";
    String UPDATE_MULTI_PROFILE = "UPDATE_MULTI_PROFILE";
    String UPDATE_PRIMARY_PROFILE = "update_primary_profile";
    String GET_IMAGE_URL = "get_image_url";

    String DEVICE_NAME = "device_name";
    String PREFS_SELECTED_IDS = "selected_ids_prefs";
    String SELECTED_IDS = "selected_ids";
    String VIA_LOGIN = "VIA_LOGIN";


    //Fragment tag

    String TAG_PLAYER_FRAGMENT = "PLAYER_FRAGMENT";
    String TAG_ALBUM_FRAGMENT = "ALBUM_FRAGMENT";
    String TAG_SINGLE_FRAGMENT = "SINGLE_FRAGMENT";
    String TAG_ARTIST_FRAGMENT = "ARTIST_FRAGMENT";
    String TAG_SEARCH_FRAGMENT = "SEARCH_FRAGMENT";
    String TAG_PLAYLIST_FRAGMENT = "PLAYLIST_FRAGMENT";
    String TAG_PLAYLIST_DETAIL_FRAGMENT = "PLAYLIST_DETAIL_FRAGMENT";
    String TAG_GRID_FRAGMENT = "TAG_GRID_FRAGMENT";




}
