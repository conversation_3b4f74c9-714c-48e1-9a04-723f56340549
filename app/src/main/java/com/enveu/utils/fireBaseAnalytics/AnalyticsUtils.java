package com.enveu.utils.fireBaseAnalytics;

import android.content.Context;
import android.os.Bundle;

import com.enveu.utils.constants.AppConstants;
import com.google.firebase.analytics.FirebaseAnalytics;

public class AnalyticsUtils {

    private static FirebaseAnalytics getFirebaseAnalytics(Context context) {
        return FirebaseAnalytics.getInstance(context);
    }

    public static void setUserProperty(Context context, String key, String value) {
        FirebaseAnalytics firebaseAnalytics = getFirebaseAnalytics(context);
        firebaseAnalytics.setUserProperty(key, value);
    }


    public static void trackUserAttributes(Context context, String userId, String userName, String userEmail) {
        setUserProperty(context, "user_id", userId);
        setUserProperty(context, "name", userName);
        setUserProperty(context, "email", userEmail);
    }

    public static void trackScreenView(Context context, String screenName) {
        FirebaseAnalytics firebaseAnalytics = getFirebaseAnalytics(context);
        Bundle params = new Bundle();
        params.putString(AppConstants.SCREEN_NAME, screenName);
        firebaseAnalytics.logEvent(AppConstants.TAB_SCREEN_VIEWED, params);
    }

    public static void logEvent(Context context, String eventName) {
        FirebaseAnalytics firebaseAnalytics = getFirebaseAnalytics(context);
        firebaseAnalytics.logEvent(eventName, null);
    }


    public static void logSubscriptionEvent(Context context,String eventName, String subscriptionTittle,  String price, String currency,String paymentMethod) {
        FirebaseAnalytics firebaseAnalytics = getFirebaseAnalytics(context);
        Bundle params = new Bundle();
        params.putString(AppConstants.SUBSCRIPTION_TITTLE, subscriptionTittle);
        params.putString(AppConstants.PRICE, price);
        params.putString(AppConstants.CURRENCY, currency);
        params.putString(AppConstants.PAYMENT_METHOD, paymentMethod);
        firebaseAnalytics.logEvent(eventName, params);
    }


    public static void logSearchEvent(Context context, String searchTerm) {
        Bundle params = new Bundle();
        params.putString(AppConstants.SEARCH, searchTerm);
        FirebaseAnalytics firebaseAnalytics = getFirebaseAnalytics(context);
        firebaseAnalytics.logEvent(AppConstants.SEARCH_TERM,  params);
    }

    public static void logSearchContentSelectEvent(Context context, String contentId,String tittle ,String contentType,String searchTerm) {
        FirebaseAnalytics firebaseAnalytics = getFirebaseAnalytics(context);
        Bundle params = new Bundle();
        params.putString(AppConstants.CONTENT_ID, contentId);
        params.putString(AppConstants.CONTENT_TITTLE, tittle);
        params.putString(AppConstants.CONTENT_TYPE, contentType);
        params.putString(AppConstants.SEARCH_TERM, searchTerm);
        firebaseAnalytics.logEvent(AppConstants.SEARCH_CONTENT_SELECT,params);

    }

    public static void logUserInteractionEvent(Context context, String eventName,String contentId,String tittle ,String contentType) {
        FirebaseAnalytics firebaseAnalytics = getFirebaseAnalytics(context);
        Bundle params = new Bundle();
        params.putString(AppConstants.CONTENT_ID, contentId);
        params.putString(AppConstants.CONTENT_TITTLE, tittle);
        params.putString(AppConstants.CONTENT_TYPE, contentType);
        firebaseAnalytics.logEvent(eventName,  params);
    }

    public static void logContentSelectEvent(Context context, String widgetId, String widgetName,String playListId, String contentId,String tittle,String contentType) {
        FirebaseAnalytics firebaseAnalytics = getFirebaseAnalytics(context);
        Bundle params = new Bundle();
        params.putString(AppConstants.WIDGET_ID, widgetId);
        params.putString(AppConstants.WIDGET_NAME, widgetName);
        params.putString(AppConstants.PLAY_LIST_ID, playListId);
        params.putString(AppConstants.CONTENT_ID, contentId);
        params.putString(AppConstants.CONTENT_TITTLE, tittle);
        params.putString(AppConstants.CONTENT_TYPE, contentType);
        firebaseAnalytics.logEvent(AppConstants.CONTENT_SELECT,  params);

    }

    public static void logPlayerEvent(Context context, String eventName, String contentId, String contentTittle, String contentType,String position,String contentDuration) {
        FirebaseAnalytics firebaseAnalytics = getFirebaseAnalytics(context);
        Bundle params = new Bundle();
        params.putString(AppConstants.CONTENT_ID, contentId);
        params.putString(AppConstants.CONTENT_TITTLE, contentTittle);
        params.putString(AppConstants.CONTENT_TYPE, contentType);
        params.putString(AppConstants.PLAYBACK_POSITION, position);
        params.putString(AppConstants.CONTENT_DURATION, contentDuration);
        params.putString(AppConstants.CONTENT_TYPE, contentType);
        firebaseAnalytics.logEvent(eventName,  params);

    }

    public static void logWidgetMoreEvent(Context context, String widgetId,String widgetName) {
        FirebaseAnalytics firebaseAnalytics = getFirebaseAnalytics(context);
        Bundle params = new Bundle();
        params.putString(AppConstants.WIDGET_ID, widgetId);
        params.putString(AppConstants.WIDGET_NAME, widgetName);
        firebaseAnalytics.logEvent(AppConstants.RAIL_SELECT,  params);

    }
}
