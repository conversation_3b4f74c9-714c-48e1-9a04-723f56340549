package com.enveu.utils.helpers;

import static com.enveu.utils.constants.AppConstants.TAG_PLAYLIST_FRAGMENT;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.enveu.Bookmarking.BookmarkingViewModel;
import com.enveu.R;
import com.enveu.activities.mainPLayer.MainPlayerActivity;
import com.enveu.activities.multiplePlaylist.MultiplePlaylistViewModel;
import com.enveu.activities.multiplePlaylist.MyPlaylistFragment;
import com.enveu.activities.multiplePlaylist.RemoveSongListener;
import com.enveu.activities.usermanagment.ui.ActivityLogin;
import com.enveu.appLevelModel.FeatureFlagModel;
import com.enveu.beanModel.emptyResponse.ResponseEmpty;
import com.enveu.beanModel.responseGetWatchlist.ResponseGetIsWatchlist;
import com.enveu.beanModel.responseIsLike.ResponseIsLike;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.beanModelV3.videoDetailV3.list.DataItem;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent;
import com.enveu.beanModelV3.videoDetailV3.queue_response.AddToQueueRequestModel;
import com.enveu.bean_model_v2_0.MusicPlaylist.AddSongPlaylist;
import com.enveu.bean_model_v2_0.MusicPlaylist.MyPlaylistData;
import com.enveu.callbacks.commonCallbacks.WatchListUpdateCallback;
import com.enveu.client.baseCollection.baseCategoryServices.BaseCategoryServices;
import com.enveu.client.userManagement.callBacks.LogoutCallBack;
import com.enveu.client.utils.ClickHandler;
import com.enveu.cms.HelpActivity;
import com.enveu.databinding.CustomBottomSheetBinding;
import com.enveu.fragments.artist.SongsCreditsFragment;
import com.enveu.menuManager.model.MenuManagerModel;
import com.enveu.player.utils.TimeUtils;
import com.enveu.utils.CustomProgressBar;
import com.enveu.utils.Logger;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.colorsJson.model.ColorsModel;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.commonMethods.AppConfigMethod;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;
import com.enveu.utils.stringsJson.converter.StringsHelper;
import com.enveu.utils.stringsJson.model.StringsData;
import com.facebook.login.LoginManager;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import retrofit2.Response;


public class BottomDialogFragment extends BottomSheetDialogFragment {
    private static DataItem currentSong;
    private static String contentSlugForAlbum;
    private static String contentType;
    private static List<DataItem> allSongListData;
    private static boolean multipleSongsAddToQueue = false;
    private boolean isLoggedOut = false;

    private RailInjectionHelper railInjectionHelper = null;

    private TextView watchlistIcon,share, like, queue , addToPlaylist,credits,report, remove ;

    private MultiplePlaylistViewModel playlistViewModel;

    private int watchListCounter = 0;
    private String redirectionFrom="";
    private String tumbnailImg="";
    private String imgDefaultFromItem="";

    private MainPlayerActivity.PlayerListener playerListener;
    private RemoveSongListener removeSongListener;

    private CustomBottomSheetBinding binding;
    private FeatureFlagModel featureFlag ;
    private int likeCounter = 0;

    private boolean isWatchlistUpdated = true;
    private CustomProgressBar customProgressBar;

    private BookmarkingViewModel bookmarkingViewModel = null;

    private MultiplePlaylistViewModel multiplePlaylistViewModel;

    private  static WatchListUpdateCallback watchListUpdateCallback;
    private String  playlistID = "";


    public static BottomDialogFragment getInstance(DataItem song,String redirection) {
        currentSong = song;
        multipleSongsAddToQueue = false;
        Bundle bundle=new Bundle();
        bundle.putString(AppConstants.FROM_REDIRECTION,redirection);
        BottomDialogFragment fragment=new BottomDialogFragment();
        fragment.setArguments(bundle);
        return fragment;
    }
    public static BottomDialogFragment getInstance(DataItem song, String redirection, String img, List<DataItem> songListData,String contentSlug,String mediaType, WatchListUpdateCallback callback) {
        currentSong = song;
        allSongListData = songListData;
        multipleSongsAddToQueue = true;
        watchListUpdateCallback = callback;
        contentSlugForAlbum = contentSlug;
        contentType = mediaType;
        Logger.d("songListBottomSheet", new Gson().toJson(allSongListData));
        Bundle bundle=new Bundle();
        bundle.putString(AppConstants.FROM_REDIRECTION, redirection);
        bundle.putString(AppConstants.THUMBNAIL_IMG,img);
        BottomDialogFragment fragment=new BottomDialogFragment();
        fragment.setArguments(bundle);
        return fragment;
    }



    @Override
    public void onStart() {
        super.onStart();

    }

    @SuppressLint("MissingInflatedId")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
//        final View view = inflater.inflate(R.layout.custom_bottom_sheet, container, false);
      // ImageView imageView= view.findViewById(R.id.song_image);
        binding=CustomBottomSheetBinding.inflate(inflater,container,false);
        TextView title= binding.textSongName;
        TextView artist= binding.textArtistName;
        TextView songDuration= binding.songDuration;
        TextView imageTitle= binding.imageTitle;
        watchlistIcon = binding.watchlistIcon;
        addToPlaylist = binding.addToPlaylist;
        customProgressBar = binding.progressBar;
        queue = binding.addToQueue;
        credits = binding.credits;
        share = binding.share;
        like = binding.like;
        remove = binding.remove;
        report = binding.songsReport;
        featureFlag = AppConfigMethod.INSTANCE.parseFeatureFlagList();
        String jsonDetails=new Gson().toJson(currentSong);
        Log.d("BottomDialogFragment","detail of obj: "+jsonDetails);
        if (!getArguments().isEmpty()){
            redirectionFrom=getArguments().getString(AppConstants.FROM_REDIRECTION);
            tumbnailImg=getArguments().getString(AppConstants.THUMBNAIL_IMG);
        }
        if (featureFlag.getFeatureFlag().getSONGS_CREDITS()){
            credits.setVisibility(View.VISIBLE);
        }
        if (featureFlag.getFeatureFlag().getSONGS_REPORT()){
            report.setVisibility(View.VISIBLE);
        }
        if (!playlistID.isEmpty()){
            remove.setVisibility(View.VISIBLE);
        }else{
            remove.setVisibility(View.GONE);
        }
        customProgressBar.setVisibility(View.VISIBLE);
        setViewModel();
        isContentWatchlist(currentSong.getId());
        hitApiIsLike();
        title.setText(currentSong.getTitle());
        loadTopImg();
        setClick();
        if (redirectionFrom.equalsIgnoreCase("")){
            songDuration.setText(TimeUtils.INSTANCE.formatDuration(currentSong.getAudioContent().getDuration()).toString());
        }else {
            binding.songDuration.setVisibility(View.GONE);
        }
        StringBuilder artistName = new StringBuilder();
        if (currentSong != null && currentSong.getCustomData() != null  && currentSong.getCustomData().getSongsArtistIds() != null && !currentSong.getCustomData().getSongsArtistIds().isEmpty()) {
            for (int index = 0; index < currentSong.getCustomData().getSongsArtistIds().size(); index++) {
                if (index == currentSong.getCustomData().getSongsArtistIds().size() - 1) {
                    artistName.append(currentSong.getCustomData().getSongsArtistIds().get(index).getTitle());
                } else {
                    artistName.append(currentSong.getCustomData().getSongsArtistIds().get(index).getTitle()).append(", ");
                }
            }
        }else {
            binding.textArtistName.setVisibility(View.GONE);
        }
        artist.setText(artistName.toString());

        if (currentSong != null && currentSong.getCustomData() != null && currentSong.getCustomData().getSongsAlbumsId() != null) {
            String finalUrl = "" ;
            if (currentSong.getCustomData().getSongsAlbumsId().getImages() != null
                    && !currentSong.getCustomData().getSongsAlbumsId().getImages().isEmpty()) {
                for (int i = 0; i < currentSong.getCustomData().getSongsAlbumsId().getImages().size(); i++) {
                    if (currentSong.getCustomData().getSongsAlbumsId().getImages().get(i).getTag().equals(AppConstants.IMG_0)) {
                        finalUrl = currentSong.getCustomData().getSongsAlbumsId().getImages().get(i).getSrc();
                       /* if required image in any project just enable this and there layout to
                        ImageHelper.getInstance(imageView.getContext()).loadListImage(imageView, finalUrl);*/
                        Log.w("imageURL", finalUrl);
                        break;
                    } else  {
                        imageTitle.setText(currentSong != null ? currentSong.getTitle() : "");

                    }
                }
            }
        } else {
            imageTitle.setText(currentSong != null ? currentSong.getTitle() : "");
        }


        return binding.getRoot();
    }
    private void loadTopImg() {
        try {
            if (!redirectionFrom.equalsIgnoreCase(AppConstants.QUEUE)) {
                if (redirectionFrom.equalsIgnoreCase(AppConstants.ALBUM) || redirectionFrom.equalsIgnoreCase(AppConstants.ARTISTS)){
                    ImageHelper.getInstance(getContext()).loadListImage(binding.songImage, tumbnailImg);
                } else if (redirectionFrom.equalsIgnoreCase(AppConstants.FULL_PLAYER) && !imgDefaultFromItem.isEmpty()) {
                    ImageHelper.getInstance(getContext()).loadListImage(binding.songImage, imgDefaultFromItem);
                } else {
                    Log.d("BottomDialogFragment", "" + currentSong.getCustomData().getSongsAlbumsId().getImages());
                    if (!currentSong.getCustomData().getSongsAlbumsId().getImages().isEmpty() &&
                            currentSong.getCustomData().getSongsAlbumsId().getImages().get(0).getSrc() != null) {
                        ImageHelper.getInstance(getContext()).loadListImage(binding.songImage, currentSong.getCustomData().getSongsAlbumsId().getImages().get(0).getSrc());
                    }else if (!imgDefaultFromItem.isEmpty()){
                        ImageHelper.getInstance(getContext()).loadListImage(binding.songImage, imgDefaultFromItem);
                    }
                }
            }  else  {
                binding.linearLayoutView.setVisibility(View.GONE);
            }

        } catch (Exception e) {
            e.printStackTrace();
            Log.d("BottomDialogFragment",e.getMessage());
        }
    }
    public void PlayerListener(MainPlayerActivity.PlayerListener playerListener){
        this.playerListener = playerListener;
    }

    public void RemoveSongListener(RemoveSongListener removeSongFromPlaylist){
        this.removeSongListener = removeSongFromPlaylist;
    }
    private void hitApiIsLike() {
        bookmarkingViewModel.hitApiIsLike(KsPreferenceKeys.getInstance().getAppPrefAccessToken(),currentSong.getId()).observe(this, new Observer<ResponseIsLike>() {
            @Override
            public void onChanged(ResponseIsLike responseEmpty) {
                if (Objects.requireNonNull(responseEmpty).isStatus()) {
                    if (StringUtils.isNullOrEmptyOrZero(responseEmpty.getData().getId())) {
                        resetLike();
                        customProgressBar.setVisibility(View.GONE);
                    } else {
                        callLikePlayListApi();

                    }
                } else {
                    if (responseEmpty.getResponseCode() == 4302) {
                        isLoggedOut = true;
                        logoutCall();
                    } else if (responseEmpty.getResponseCode() == 500) {
                      //  showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                    }
                    customProgressBar.setVisibility(View.GONE);
                }
            }
        });
    }

    private void resetLike() {
        likeCounter = 0;
        Drawable newDrawable = ContextCompat.getDrawable(requireContext(), R.drawable.ic_baseline_favorite_border_24);
        like.setCompoundDrawablesWithIntrinsicBounds(newDrawable, null, null, null);
        like.setText(getString(R.string.add_to_liked_songs));
    }

    private void setLike() {
        likeCounter = 1;
        Drawable newDrawable = ContextCompat.getDrawable(requireContext(), R.drawable.heart_filled);
        like.setCompoundDrawablesWithIntrinsicBounds(newDrawable, null, null, null);
        like.setText(getString(R.string.added_to_liked_songs));
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (redirectionFrom.equalsIgnoreCase(AppConstants.ALBUM)){
            binding.watchlistIcon.setVisibility(View.GONE);
            binding.like.setVisibility(View.GONE);
            binding.credits.setVisibility(View.GONE);
            binding.share.setText(getString(R.string.share_album_txt));
            binding.songsReport.setText(getString(R.string.report_album_txt));
        } else if (redirectionFrom.equalsIgnoreCase(AppConstants.ARTISTS)) {
            binding.like.setVisibility(View.GONE);
            binding.watchlistIcon.setVisibility(View.GONE);
            binding.addToPlaylist.setVisibility(View.GONE);
            binding.addToQueue.setVisibility(View.VISIBLE);
            binding.credits.setVisibility(View.VISIBLE);
            binding.songsReport.setVisibility(View.VISIBLE);
            binding.share.setText(getString(R.string.share_artist));
            binding.credits.setText(getString(R.string.credit_artist));
            binding.songsReport.setText(getString(R.string.report_artist));
            int paddingBottom=binding.bottomSheetDetailRoot.getPaddingBottom();
            binding.bottomSheetDetailRoot.setPadding(0,0,0,(paddingBottom+100));
        } else if (redirectionFrom.equalsIgnoreCase(AppConstants.VIDEO)) {
            binding.like.setVisibility(View.VISIBLE);
            binding.watchlistIcon.setVisibility(View.VISIBLE);
            binding.addToPlaylist.setVisibility(View.GONE);
            binding.addToQueue.setVisibility(View.GONE);
            binding.credits.setVisibility(View.GONE);
            binding.songsReport.setVisibility(View.GONE);
            binding.share.setText(getString(R.string.share));
            binding.like.setText(getString(R.string.detail_page_like));
        } else if (redirectionFrom.equalsIgnoreCase(AppConstants.HIDE_TO_QUEUE)){
           binding.addToQueue.setVisibility(View.GONE);
        } else if (redirectionFrom.equalsIgnoreCase(AppConstants.FULL_PLAYER)) {
            binding.watchlistIcon.setVisibility(View.GONE);
            binding.addToQueue.setVisibility(View.GONE);
            binding.addToPlaylist.setVisibility(View.GONE);
            binding.credits.setVisibility(View.GONE);
            binding.songsReport.setVisibility(View.GONE);
        }
    }

    private void setClick() {
        addToPlaylist.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MyPlaylistFragment playlistFragment = new MyPlaylistFragment();
                Bundle bundle = new Bundle();
                if (playerListener != null){
                    playerListener.onBack();
                }
                bundle.putString("from", "playlist_selection");
                bundle.putInt("songID", currentSong.getId());
                playlistFragment.setArguments(bundle);
                requireActivity().getSupportFragmentManager().beginTransaction().add(R.id.content_frame, playlistFragment,TAG_PLAYLIST_FRAGMENT).addToBackStack(null).commit();
                dismiss();
            }
        });
        watchlistIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                callWatchlistApi(watchListCounter);
            }
        });

        share.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                openShareDialog();
                dismiss();
            }
        });

        remove.setOnClickListener(v -> multiplePlaylistViewModel.removeContentPlaylist(
                KsPreferenceKeys.getInstance().getAppPrefAccessToken(),
                currentSong.getId(),
                "",
                playlistID,
                false
        ).observe(requireActivity(), result -> {
            removeSongListener.removeSong(currentSong.getId());
            Toast.makeText(requireContext(),getString(R.string.remove_from_playlist),Toast.LENGTH_LONG).show();
            dismiss();
        }));

        like.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                setLikeForAsset(1);
                //  dismiss();
            }
        });

        queue.setOnClickListener(v -> {
            if (featureFlag.getFeatureFlag().getIS_MY_PLAYLIST_ENABLE()){
                if (!multipleSongsAddToQueue){
                     multiplePlaylistViewModel.addContentPlaylist(KsPreferenceKeys.getInstance().getAppPrefAccessToken(), currentSong.getId(), "QUEUED", null, false);
                }
                else {
                    addToQueueApi();
                }
                Toast.makeText(requireContext(),"Song add on My Queue",Toast.LENGTH_LONG).show();
            }
            multipleSongsAddToQueue = false;
            dismiss();
        });

        Bundle bundleForCreditsFrag=new Bundle();
        bundleForCreditsFrag.putString(AppConstants.FROM_REDIRECTION,AppConstants.BOTTOMDAILOGFRAG);
        credits.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SongsCreditsFragment creditsFragment = new SongsCreditsFragment(currentSong);
                creditsFragment.setArguments(bundleForCreditsFrag);
                if (playerListener != null){
                    playerListener.onBack();
                    requireActivity().getSupportFragmentManager().beginTransaction().add(R.id.player_frame, creditsFragment).addToBackStack(null).commit();
                }else{
                    requireActivity().getSupportFragmentManager().beginTransaction().add(R.id.content_frame, creditsFragment).addToBackStack(null).commit();
                }
                dismiss();
            }
        });

        report.setOnClickListener(new View.OnClickListener() {
            @SuppressLint("ResourceType")
            @Override
            public void onClick(View v) {
                Intent webViewIntent = new Intent(requireActivity(), HelpActivity.class);
                webViewIntent.putExtra("type","9");
                startActivity(webViewIntent);
            }
        });

    }

    private void addToQueueApi() {
        if (allSongListData != null && allSongListData.size() != 0)  {
            List<Integer> allSongContentId = new ArrayList<>();
            for (DataItem contentId : allSongListData) {
                allSongContentId.add(contentId.getId());
            }
            AddToQueueRequestModel addToQueueRequestModel = new AddToQueueRequestModel();
            addToQueueRequestModel.setAddToTop(false);
            addToQueueRequestModel.setType("QUEUED");
            addToQueueRequestModel.setMediaContentIds(allSongContentId);
            railInjectionHelper.addToQueueApi(addToQueueRequestModel);
        }
    }

    private void setLikeForAsset(int from) {
        customProgressBar.setVisibility(View.VISIBLE);
        if (KsPreferenceKeys.getInstance().getAppPrefLoginStatus().equalsIgnoreCase(AppConstants.UserStatus.Login.toString())){
            if (likeCounter == 0) {
                hitApiAddLike(from);
            }
            else {
                hitApiRemoveLike();
            }
        }else {
            goToLogin();
        }

    }

    private void hitApiRemoveLike() {
        bookmarkingViewModel.hitApiDeleteLike(KsPreferenceKeys.getInstance().getAppPrefAccessToken(), currentSong.getId()).observe(this, new Observer<ResponseEmpty>() {
            @Override
            public void onChanged(ResponseEmpty responseEmpty) {
                if (Objects.requireNonNull(responseEmpty).isStatus()) {
                    customProgressBar.setVisibility(View.GONE);
                    resetLike();
                    removeLikePlayList();
                }else {
                    customProgressBar.setVisibility(View.GONE);
                    if (responseEmpty.getResponseCode() == 4302) {
                        isLoggedOut = true;
                       // showDialog(getString(R.string.logged_out), resources.getString(R.string.you_are_logged_out))
                    } else if (responseEmpty.getResponseCode() == 500) {
                        //showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                    }
                }
            }
        });
    }

    private void removeLikePlayList() {
        multiplePlaylistViewModel.removeContentPlaylist(KsPreferenceKeys.getInstance().getAppPrefAccessToken(), currentSong.getId(), "LIKED","", true).observe(requireActivity(), new Observer<MyPlaylistData>() {
            @Override
            public void onChanged(MyPlaylistData myPlaylistData) {
                if (myPlaylistData.getResponseCode() == 2000){
                    dismiss();
                    Toast.makeText(requireContext(),getString(R.string.song_remove_playlist),Toast.LENGTH_LONG).show();
                }
            }
        });
    }

    private void hitApiAddLike(int from) {
        bookmarkingViewModel.hitApiAddLike(KsPreferenceKeys.getInstance().getAppPrefAccessToken(),currentSong.getId()).observe(this, new Observer<ResponseEmpty>() {
            @Override
            public void onChanged(ResponseEmpty responseEmpty) {
                if (Objects.requireNonNull(responseEmpty).isStatus()) {
                    customProgressBar.setVisibility(View.GONE);
                 //   setLike();
                    callLikePlayListApi();
                }else {
                    customProgressBar.setVisibility(View.GONE);
                    if (responseEmpty.getResponseCode() == 4302) {
                        isLoggedOut = true;
                        logoutCall();
                    } else if (responseEmpty.getResponseCode() == 4902) {
                        setLike();
                        String debugMessage = responseEmpty.getDebugMessage();
                        //from value will bedepends on how user click of watchlist icon-->>if loggedout=2 else=2
                        if (from == 1) {
                           // showDialog(getString(R.string.error), debugMessage);
                        }
                    } else if (responseEmpty.getResponseCode() == 500) {
                       // showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                    } else {
                       // showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                    }
                }
            }
        });
    }

    private void callLikePlayListApi() {
        if (featureFlag.getFeatureFlag().getIS_MY_PLAYLIST_ENABLE()){
            multiplePlaylistViewModel.addContentPlaylist(KsPreferenceKeys.getInstance().getAppPrefAccessToken(),currentSong.getId(),"LIKED" , "", false).observe(requireActivity(), new Observer<AddSongPlaylist>() {
                @Override
                public void onChanged(AddSongPlaylist addSongPlaylist) {
                    if (addSongPlaylist != null){
                        if (addSongPlaylist.getResponseCode() != null){
                            if (addSongPlaylist.getResponseCode() == 2000){
                                setLike();
                                dismiss();
                                Toast.makeText(requireContext(),getString(R.string.song_added_playlist),Toast.LENGTH_LONG).show();
                            }else if (addSongPlaylist.getResponseCode() == 409){
                                setLike();
                            }else if (addSongPlaylist.getResponseCode() == 404){
                                Toast.makeText(requireActivity(),getString(R.string.something_went_wrong),Toast.LENGTH_LONG).show();
                            }
                        }
                    }else {
                        Toast.makeText(requireActivity(),getString(R.string.something_went_wrong),Toast.LENGTH_LONG).show();
                    }
                    customProgressBar.setVisibility(View.GONE);
                }
            });



        }
    }

    private void openShareDialog() {
        String assetType = "";
        String contentSlug = "";
        ImageContent imgUrl = null;
        int id = currentSong.getId();
        String title = currentSong.getTitle();
        if (currentSong.getContentType() != null && currentSong.getContentType().equalsIgnoreCase("SONGS")){
            assetType = currentSong.getContentType();
        }else if (currentSong.getContentType() != null && currentSong.getContentType().equalsIgnoreCase("AUDIO")){
            assetType = currentSong.getAudioContent().getAudioType();
        }else {
            assetType = contentType;
        }

        if (!StringUtils.isNullOrEmptyOrZero(currentSong.getContentSlug())) {
            contentSlug = currentSong.getContentSlug();
        }else {
            contentSlug = contentSlugForAlbum;
        }
        if (currentSong.getImages() != null) {
             imgUrl = AppCommonMethod.getImageContent(currentSong);
        }
        if (ClickHandler.disallowClick()) {
            return;
        }

        EnveuVideoItemBean enveuVideoItemBean = new EnveuVideoItemBean();

        if (currentSong != null) {
            if (currentSong.getImages() != null && !currentSong.getImages().isEmpty()) {
                enveuVideoItemBean.setPosterURL(currentSong.getImages().get(0).getSrc());
            }
            enveuVideoItemBean.setTitle(currentSong.getTitle());
            enveuVideoItemBean.setAssetType(currentSong.getContentType());
            enveuVideoItemBean.setDescription(currentSong.getDescription());
            enveuVideoItemBean.setContentSlug(currentSong.getContentSlug());
        }
        AppCommonMethod.openShareFirebaseDynamicLinks(requireActivity(), enveuVideoItemBean);
    }

    public void setPlaylistID(String playlistID) {
        this.playlistID = playlistID;
    }

    private void setViewModel() {
        railInjectionHelper = new ViewModelProvider(requireActivity()).get(RailInjectionHelper.class);
        bookmarkingViewModel = new ViewModelProvider(requireActivity()).get(BookmarkingViewModel.class);
        multiplePlaylistViewModel = new ViewModelProvider(requireActivity()).get(MultiplePlaylistViewModel.class);
    }


    public void isContentWatchlist(int assetId) {
        if (KsPreferenceKeys.getInstance().getAppPrefLoginStatus().equalsIgnoreCase(AppConstants.UserStatus.Login.toString())) {
            railInjectionHelper.hitApiIsWatchList(KsPreferenceKeys.getInstance().getAppPrefAccessToken(), assetId).observe(this, new Observer<ResponseGetIsWatchlist>() {
                @Override
                public void onChanged(ResponseGetIsWatchlist responseEmpty) {
                    if (responseEmpty != null && responseEmpty.isStatus()) {
                            setWatchlist();
                    } else {
                        if (responseEmpty != null && responseEmpty.getResponseCode() == 4302) {
                            isLoggedOut = true;
                            logoutCall();
                        } else if (responseEmpty != null && responseEmpty.getResponseCode() == 500) {
                            // showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                        }
                    }
                }
            });
        }
    }

    private void logoutCall() {
        if (CheckInternetConnection.isOnline(requireActivity())) {
            clearCredientials(KsPreferenceKeys.getInstance());
            hitApiLogout(requireContext(), KsPreferenceKeys.getInstance().getAppPrefAccessToken());
        } else {
            ToastHandler.getInstance().show(requireActivity(), getString(R.string.no_internet_connection));
        }
    }


    public void clearCredientials(KsPreferenceKeys preference) {
        try {
            String json = KsPreferenceKeys.getInstance().getString("DMS_Response", "");
            String isFacebook = preference.getAppPrefLoginType();
            if (isFacebook.equalsIgnoreCase(AppConstants.UserLoginType.FbLogin.toString())) {
                LoginManager.getInstance().logOut();
            }
            String strCurrentTheme = KsPreferenceKeys.getInstance().getCurrentTheme();
            boolean encrypt = preference.getEncryptionUpdate();
            String strCurrentLanguage = KsPreferenceKeys.getInstance().getAppLanguage();
            String strSubscriptionURL = KsPreferenceKeys.getInstance().getSUBSCRIPTION_BASE_URL();
            String strPaymentURL = KsPreferenceKeys.getInstance().getPAYMENT_BASE_URL();
            boolean isBingeWatchEnable = KsPreferenceKeys.getInstance().getBingeWatchEnable();
            preference.setAppPrefRegisterStatus(AppConstants.UserStatus.Logout.toString());
            ColorsModel colorsModel = ColorsHelper.INSTANCE.loadDataFromJson();
            StringsData stringsHelper = StringsHelper.INSTANCE.loadDataFromJson();
            List<MenuManagerModel.Data.OrderedMenuItem> menuItem=preference.getDataMenuKeyValue();
            preference.clear();
            KsPreferenceKeys.getInstance().saveDataMenuKeyValue(menuItem);
            AppConfigMethod.INSTANCE.setMediaTypeJson(requireContext());
            SharedPrefHelper.getInstance().setColorJson(colorsModel);
            SharedPrefHelper.getInstance().setStringJson(stringsHelper);
            preference.setEncryptionUpdate(encrypt);
            KsPreferenceKeys.getInstance().setString("DMS_Response", json);
            KsPreferenceKeys.getInstance().setfirstTimeUserForKidsPIn(false);

            KsPreferenceKeys.getInstance().setSUBSCRIPTION_BASE_URL(strSubscriptionURL);
            KsPreferenceKeys.getInstance().setPAYMENT_BASE_URL(strPaymentURL);

            KsPreferenceKeys.getInstance().setCurrentTheme(strCurrentTheme);
            KsPreferenceKeys.getInstance().setAppLanguage(strCurrentLanguage);
            AppCommonMethod.updateLanguage(strCurrentLanguage, requireContext());
            KsPreferenceKeys.getInstance().setBingeWatchEnable(isBingeWatchEnable);
        } catch (Exception e) {
            Logger.w(e);
        }
    }

    public void hitApiLogout(Context context, String token) {

        String isFacebook = KsPreferenceKeys.getInstance().getAppPrefLoginType();

        if (isFacebook.equalsIgnoreCase(AppConstants.UserLoginType.FbLogin.toString())) {
            LoginManager.getInstance().logOut();
        }
        BaseCategoryServices.Companion.getInstance().logoutService(token, new LogoutCallBack() {
            @Override
            public void failure(boolean status, int errorCode, String message) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, 500);
            }
            @Override
            public void success(boolean status, Response<JsonObject> response) {
                if (status) {
                    try {
                        if (response.code() == 404) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());
                        }
                        if (response.code() == 403) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        } else if (response.code() == 200) {
                            Objects.requireNonNull(response.body()).addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        } else if (response.code() == 401) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        } else if (response.code() == 500) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        }
                    } catch (Exception e) {
                        JsonObject jsonObject = new JsonObject();
                        jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                    }

                }
            }
        });


    }

    private void setWatchlist() {
        isWatchlistUpdated = true;
        if (watchListUpdateCallback != null) {
            watchListUpdateCallback.onClick(true);
        }
        watchListCounter = 1;
        binding.watchlistIcon.setText("Remove from Your Library");
        Drawable newDrawable = ContextCompat.getDrawable(requireContext(), R.drawable.added_to_watch_list);
        watchlistIcon.setCompoundDrawablesWithIntrinsicBounds(newDrawable, null, null, null);
    }

    private void resetWatchList() {
        isWatchlistUpdated = false;
        if (watchListUpdateCallback != null) {
            watchListUpdateCallback.onClick(false);
        }
        watchListCounter = 0;
        binding.watchlistIcon.setText(getString(R.string.add_watch_list));
        Drawable newDrawable = ContextCompat.getDrawable(requireContext(), R.drawable.add_to_your_libtrary);
        watchlistIcon.setCompoundDrawablesWithIntrinsicBounds(newDrawable, null, null, null);

    }

    private void callWatchlistApi(int watchlistCounter) {
        if (KsPreferenceKeys.getInstance().getAppPrefLoginStatus().equalsIgnoreCase(AppConstants.UserStatus.Login.toString())) {
            setWatchListForAsset(1,watchlistCounter);
        } else {
            ActivityTrackers.getInstance().setAction(
                    ActivityTrackers.WATCHLIST);
            goToLogin();
        }
    }

    private void goToLogin() {
        ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin.class,"");
    }

    private void setWatchListForAsset(int from, int watchListCounter) {
        customProgressBar.setVisibility(View.VISIBLE);
        if (watchListCounter == 0) {
            hitApiAddWatchList(from);
        } else {
            hitApiRemoveList();
        }
    }

    private void hitApiAddWatchList(int from) {

        railInjectionHelper.hitApiAddWatchList(KsPreferenceKeys.getInstance().getAppPrefAccessToken(),currentSong.getId()).observe(this, new Observer<ResponseEmpty>() {
            @Override
            public void onChanged(ResponseEmpty responseEmpty) {
                if (Objects.requireNonNull(responseEmpty).isStatus()){
                    customProgressBar.setVisibility(View.GONE);
                    setWatchlist();
                }else {
                    customProgressBar.setVisibility(View.GONE);
                    if (responseEmpty.getResponseCode() == 4302) {
                        isLoggedOut = true;
                        logoutCall();
                    } else if (responseEmpty.getResponseCode() == 4904) {
                        setWatchlist();
                        String debugMessage = responseEmpty.getDebugMessage();
                        //from value will bedepends on how user click of watchlist icon-->>if loggedout=2 else=2
                        if (from == 1) {
                            //  showDialog(getString(R.string.error), debugMessage)
                        }
                    } else if (responseEmpty.getResponseCode() == 500) {
                        //showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                    } else {
                        // showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                    }
                }
            }
        });


    }

    private void hitApiRemoveList(){
        railInjectionHelper.hitRemoveWatchlist(KsPreferenceKeys.getInstance().getAppPrefAccessToken(),currentSong.getId()).observe(this, new Observer<ResponseEmpty>() {
            @Override
            public void onChanged(ResponseEmpty responseEmpty) {
                if (Objects.requireNonNull(responseEmpty).isStatus()){
                    customProgressBar.setVisibility(View.GONE);
                    resetWatchList();
                }else {
                    customProgressBar.setVisibility(View.GONE);
                    if (responseEmpty.getResponseCode() == 4302) {
                        isLoggedOut = true;
                        logoutCall();
                    } else if (responseEmpty.getResponseCode() == 500) {
                        //  showDialog(getString(R.string.error), getString(R.string.something_went_wrong))
                    }
                }
            }
        });
    }

    public void setDefaultImgOfTopPoster(@NotNull String it) {
        imgDefaultFromItem=it;
    }

    //    private Drawable setDrawableTopCorners(int bgColor, int cornerRadius){
//        Drawable drawable= new Drawable() {
//
//            @Override
//            public void draw(@NonNull Canvas canvas) {
//                canvas.
//            }
//
//            @Override
//            public void setAlpha(int alpha) {
//
//            }
//
//            @Override
//            public void setColorFilter(@Nullable ColorFilter colorFilter) {
//
//            }
//
//            @Override
//            public int getOpacity() {
//                return 0;
//            }
//        }
//    }

}