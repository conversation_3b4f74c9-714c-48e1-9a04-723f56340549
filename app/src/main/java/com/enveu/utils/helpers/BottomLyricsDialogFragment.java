package com.enveu.utils.helpers;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.enveu.Bookmarking.BookmarkingViewModel;
import com.enveu.R;
import com.enveu.activities.mainPLayer.MainPlayerActivity;
import com.enveu.activities.multiplePlaylist.MultiplePlaylistViewModel;
import com.enveu.activities.multiplePlaylist.MyPlaylistFragment;
import com.enveu.activities.usermanagment.ui.ActivityLogin;
import com.enveu.appLevelModel.FeatureFlagModel;
import com.enveu.beanModel.emptyResponse.ResponseEmpty;
import com.enveu.beanModel.responseGetWatchlist.ResponseGetIsWatchlist;
import com.enveu.beanModel.responseIsLike.ResponseIsLike;
import com.enveu.beanModelV3.videoDetailV3.list.DataItem;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent;
import com.enveu.beanModelV3.videoDetailV3.queue_response.AddToQueueRequestModel;
import com.enveu.bean_model_v2_0.MusicPlaylist.AddSongPlaylist;
import com.enveu.bean_model_v2_0.MusicPlaylist.MyPlaylistData;
import com.enveu.client.baseCollection.baseCategoryServices.BaseCategoryServices;
import com.enveu.client.userManagement.callBacks.LogoutCallBack;
import com.enveu.client.utils.ClickHandler;
import com.enveu.cms.HelpActivity;
import com.enveu.databinding.CustomBottomLyricsSheetBinding;
import com.enveu.databinding.CustomBottomSheetBinding;
import com.enveu.fragments.artist.SongsCreditsFragment;
import com.enveu.menuManager.model.MenuManagerModel;
import com.enveu.player.utils.TimeUtils;
import com.enveu.utils.CustomProgressBar;
import com.enveu.utils.Logger;
import com.enveu.utils.colorsJson.converter.ColorsHelper;
import com.enveu.utils.colorsJson.model.ColorsModel;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.commonMethods.AppConfigMethod;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;
import com.enveu.utils.stringsJson.converter.StringsHelper;
import com.enveu.utils.stringsJson.model.StringsData;
import com.facebook.login.LoginManager;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import retrofit2.Response;


public class BottomLyricsDialogFragment extends BottomSheetDialogFragment {
    private static DataItem currentSong;
    private static List<DataItem> allSongListData;
    private static boolean multipleSongsAddToQueue = false;
    private boolean isLoggedOut = false;

    private RailInjectionHelper railInjectionHelper = null;

    private TextView watchlistIcon,share, like, queue , addToPlaylist,credits,report ;

    private MultiplePlaylistViewModel playlistViewModel;

    private int watchListCounter = 0;
    private String redirectionFrom="";
    private String tumbnailImg="";
    private String imgDefaultFromItem="";

    private MainPlayerActivity.PlayerListener playerListener;

    private CustomBottomLyricsSheetBinding binding;
    private FeatureFlagModel featureFlag ;
    private int likeCounter = 0;

    private CustomProgressBar customProgressBar;

    private BookmarkingViewModel bookmarkingViewModel = null;

    private MultiplePlaylistViewModel multiplePlaylistViewModel;


    public static BottomLyricsDialogFragment getInstance(DataItem song, String redirection) {
        currentSong = song;
        multipleSongsAddToQueue = false;
        Bundle bundle=new Bundle();
        bundle.putString(AppConstants.FROM_REDIRECTION,redirection);
        BottomLyricsDialogFragment fragment=new BottomLyricsDialogFragment();
        fragment.setArguments(bundle);
        return fragment;
    }
    public static BottomLyricsDialogFragment getInstance(DataItem song, String redirection, String img, List<DataItem> songListData) {
        currentSong = song;
        allSongListData = songListData;
        multipleSongsAddToQueue = true;
        Logger.d("songListBottomSheet", new Gson().toJson(allSongListData));
        Bundle bundle=new Bundle();
        bundle.putString(AppConstants.FROM_REDIRECTION, redirection);
        bundle.putString(AppConstants.THUMBNAIL_IMG,img);
        BottomLyricsDialogFragment fragment=new BottomLyricsDialogFragment();
        fragment.setArguments(bundle);
        return fragment;
    }



    @Override
    public void onStart() {
        super.onStart();

    }

    @SuppressLint("MissingInflatedId")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding= CustomBottomLyricsSheetBinding.inflate(inflater,container,false);
        TextView title= binding.textSongName;
        TextView artist= binding.textArtistName;
        TextView songDuration= binding.songDuration;
        TextView imageTitle= binding.imageTitle;
        TextView like = binding.like;
        featureFlag = AppConfigMethod.INSTANCE.parseFeatureFlagList();
        String jsonDetails=new Gson().toJson(currentSong);
        Log.d("BottomDialogFragment","detail of obj: "+jsonDetails);
        if (!getArguments().isEmpty()){
            redirectionFrom=getArguments().getString(AppConstants.FROM_REDIRECTION);
            tumbnailImg=getArguments().getString(AppConstants.THUMBNAIL_IMG);
        }
        setViewModel();
        title.setText(currentSong.getTitle());
        if (currentSong.getAudioContent().getLyrics() != null && !currentSong.getAudioContent().getLyrics().equalsIgnoreCase("")){
            like.setText(currentSong.getAudioContent().getLyrics());
        }else {
            like.setText(R.string.no_lyrics);
        }
        loadTopImg();
        if (redirectionFrom.equalsIgnoreCase("")){
            songDuration.setText(TimeUtils.INSTANCE.formatDuration(currentSong.getAudioContent().getDuration()).toString());
        }else {
            binding.songDuration.setVisibility(View.GONE);
        }
        StringBuilder artistName = new StringBuilder();
        if (currentSong != null && currentSong.getCustomData() != null  && currentSong.getCustomData().getSongsArtistIds() != null && !currentSong.getCustomData().getSongsArtistIds().isEmpty()) {
            for (int index = 0; index < currentSong.getCustomData().getSongsArtistIds().size(); index++) {
                if (index == currentSong.getCustomData().getSongsArtistIds().size() - 1) {
                    artistName.append(currentSong.getCustomData().getSongsArtistIds().get(index).getTitle());
                } else {
                    artistName.append(currentSong.getCustomData().getSongsArtistIds().get(index).getTitle()).append(", ");
                }
            }
        }else {
            binding.textArtistName.setVisibility(View.GONE);
        }
        artist.setText(artistName.toString());

        if (currentSong != null && currentSong.getCustomData() != null && currentSong.getCustomData().getSongsAlbumsId() != null) {
            String finalUrl = "" ;
            if (currentSong.getCustomData().getSongsAlbumsId().getImages() != null
                    && !currentSong.getCustomData().getSongsAlbumsId().getImages().isEmpty()) {
                for (int i = 0; i < currentSong.getCustomData().getSongsAlbumsId().getImages().size(); i++) {
                    if (currentSong.getCustomData().getSongsAlbumsId().getImages().get(i).getTag().equals(AppConstants.IMG_0)) {
                        finalUrl = currentSong.getCustomData().getSongsAlbumsId().getImages().get(i).getSrc();
                       /* if required image in any project just enable this and there layout to
                        ImageHelper.getInstance(imageView.getContext()).loadListImage(imageView, finalUrl);*/
                        Log.w("imageURL", finalUrl);
                        break;
                    } else  {
                        imageTitle.setText(currentSong != null ? currentSong.getTitle() : "");

                    }
                }
            }
        } else {
            imageTitle.setText(currentSong != null ? currentSong.getTitle() : "");
        }


        return binding.getRoot();
    }
    private void loadTopImg() {
        try {
            if (redirectionFrom.equalsIgnoreCase(AppConstants.ALBUM) || redirectionFrom.equalsIgnoreCase(AppConstants.ARTISTS)){
                ImageHelper.getInstance(getContext()).loadListImage(binding.songImage, tumbnailImg);
            }else {
                Log.d("BottomDialogFragment", "" + currentSong.getCustomData().getSongsAlbumsId().getImages());
                if (!currentSong.getCustomData().getSongsAlbumsId().getImages().isEmpty() &&
                        currentSong.getCustomData().getSongsAlbumsId().getImages().get(0).getSrc() != null) {
                    ImageHelper.getInstance(getContext()).loadListImage(binding.songImage, currentSong.getCustomData().getSongsAlbumsId().getImages().get(0).getSrc());
                }else if (!imgDefaultFromItem.isEmpty()){
                    ImageHelper.getInstance(getContext()).loadListImage(binding.songImage, imgDefaultFromItem);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.d("BottomDialogFragment",e.getMessage());
        }
    }
    public void PlayerListener(MainPlayerActivity.PlayerListener playerListener){
        this.playerListener = playerListener;
    }




    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }

    private void setClick() {
        addToPlaylist.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MyPlaylistFragment playlistFragment = new MyPlaylistFragment();
                Bundle bundle = new Bundle();
                if (playerListener != null) {
                    playerListener.onBack();
                }
                bundle.putString("from", "playlist_selection");
                bundle.putInt("songID", currentSong.getId());
                playlistFragment.setArguments(bundle);
                requireActivity().getSupportFragmentManager().beginTransaction().add(R.id.content_frame, playlistFragment).addToBackStack(null).commit();
                dismiss();
            }
        });




        Bundle bundleForCreditsFrag = new Bundle();
        bundleForCreditsFrag.putString(AppConstants.FROM_REDIRECTION, AppConstants.BOTTOMDAILOGFRAG);
        credits.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SongsCreditsFragment creditsFragment = new SongsCreditsFragment(currentSong);
                creditsFragment.setArguments(bundleForCreditsFrag);
                if (playerListener != null) {
                    playerListener.onBack();
                    requireActivity().getSupportFragmentManager().beginTransaction().add(R.id.player_frame, creditsFragment).addToBackStack(null).commit();
                } else {
                    requireActivity().getSupportFragmentManager().beginTransaction().add(R.id.content_frame, creditsFragment).addToBackStack(null).commit();
                }
                dismiss();
            }
        });


    }












    private void setViewModel() {
        railInjectionHelper = new ViewModelProvider(requireActivity()).get(RailInjectionHelper.class);
        bookmarkingViewModel = new ViewModelProvider(requireActivity()).get(BookmarkingViewModel.class);
        multiplePlaylistViewModel = new ViewModelProvider(requireActivity()).get(MultiplePlaylistViewModel.class);
    }



    private void logoutCall() {
        if (CheckInternetConnection.isOnline(requireActivity())) {
            clearCredientials(KsPreferenceKeys.getInstance());
            hitApiLogout(requireContext(), KsPreferenceKeys.getInstance().getAppPrefAccessToken());
        } else {
            ToastHandler.getInstance().show(requireActivity(), getString(R.string.no_internet_connection));
        }
    }


    public void clearCredientials(KsPreferenceKeys preference) {
        try {
            String json = KsPreferenceKeys.getInstance().getString("DMS_Response", "");
            String isFacebook = preference.getAppPrefLoginType();
            if (isFacebook.equalsIgnoreCase(AppConstants.UserLoginType.FbLogin.toString())) {
                LoginManager.getInstance().logOut();
            }
            String strCurrentTheme = KsPreferenceKeys.getInstance().getCurrentTheme();
            boolean encrypt = preference.getEncryptionUpdate();
            String strCurrentLanguage = KsPreferenceKeys.getInstance().getAppLanguage();
            String strSubscriptionURL = KsPreferenceKeys.getInstance().getSUBSCRIPTION_BASE_URL();
            String strPaymentURL = KsPreferenceKeys.getInstance().getPAYMENT_BASE_URL();
            boolean isBingeWatchEnable = KsPreferenceKeys.getInstance().getBingeWatchEnable();
            preference.setAppPrefRegisterStatus(AppConstants.UserStatus.Logout.toString());
            ColorsModel colorsModel = ColorsHelper.INSTANCE.loadDataFromJson();
            StringsData stringsHelper = StringsHelper.INSTANCE.loadDataFromJson();
            List<MenuManagerModel.Data.OrderedMenuItem> menuItem=preference.getDataMenuKeyValue();
            preference.clear();
            KsPreferenceKeys.getInstance().saveDataMenuKeyValue(menuItem);
            AppConfigMethod.INSTANCE.setMediaTypeJson(requireContext());
            SharedPrefHelper.getInstance().setColorJson(colorsModel);
            SharedPrefHelper.getInstance().setStringJson(stringsHelper);
            preference.setEncryptionUpdate(encrypt);
            KsPreferenceKeys.getInstance().setString("DMS_Response", json);
            KsPreferenceKeys.getInstance().setfirstTimeUserForKidsPIn(false);

            KsPreferenceKeys.getInstance().setSUBSCRIPTION_BASE_URL(strSubscriptionURL);
            KsPreferenceKeys.getInstance().setPAYMENT_BASE_URL(strPaymentURL);

            KsPreferenceKeys.getInstance().setCurrentTheme(strCurrentTheme);
            KsPreferenceKeys.getInstance().setAppLanguage(strCurrentLanguage);
            AppCommonMethod.updateLanguage(strCurrentLanguage, requireContext());
            KsPreferenceKeys.getInstance().setBingeWatchEnable(isBingeWatchEnable);
        } catch (Exception e) {
            Logger.w(e);
        }
    }

    public void hitApiLogout(Context context, String token) {

        String isFacebook = KsPreferenceKeys.getInstance().getAppPrefLoginType();

        if (isFacebook.equalsIgnoreCase(AppConstants.UserLoginType.FbLogin.toString())) {
            LoginManager.getInstance().logOut();
        }
        BaseCategoryServices.Companion.getInstance().logoutService(token, new LogoutCallBack() {
            @Override
            public void failure(boolean status, int errorCode, String message) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, 500);
            }
            @Override
            public void success(boolean status, Response<JsonObject> response) {
                if (status) {
                    try {
                        if (response.code() == 404) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());
                        }
                        if (response.code() == 403) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        } else if (response.code() == 200) {
                            Objects.requireNonNull(response.body()).addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        } else if (response.code() == 401) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        } else if (response.code() == 500) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                        }
                    } catch (Exception e) {
                        JsonObject jsonObject = new JsonObject();
                        jsonObject.addProperty(AppConstants.API_RESPONSE_CODE, response.code());

                    }

                }
            }
        });


    }

    private void setWatchlist() {
        watchListCounter = 1;
       // binding.watchlistIcon.setText("Remove from Your Library");
        Drawable newDrawable = ContextCompat.getDrawable(requireContext(), R.drawable.added_to_watch_list);
        watchlistIcon.setCompoundDrawablesWithIntrinsicBounds(newDrawable, null, null, null);
    }

    private void resetWatchList() {
        watchListCounter = 0;
       // binding.watchlistIcon.setText(getString(R.string.add_watch_list));
        Drawable newDrawable = ContextCompat.getDrawable(requireContext(), R.drawable.add_to_your_libtrary);
        watchlistIcon.setCompoundDrawablesWithIntrinsicBounds(newDrawable, null, null, null);

    }

    private void callWatchlistApi(int watchlistCounter) {
        if (KsPreferenceKeys.getInstance().getAppPrefLoginStatus().equalsIgnoreCase(AppConstants.UserStatus.Login.toString())) {
            setWatchListForAsset(1,watchlistCounter);
        } else {
            ActivityTrackers.getInstance().setAction(
                    ActivityTrackers.WATCHLIST);
            goToLogin();
        }
    }

    private void goToLogin() {
        ActivityLauncher.getInstance().loginActivity(requireActivity(), ActivityLogin.class,"");
    }

    private void setWatchListForAsset(int from, int watchListCounter) {
        customProgressBar.setVisibility(View.VISIBLE);
        if (watchListCounter == 0) {
          //  hitApiAddWatchList(from);
        } else {
          //  hitApiRemoveList();
        }
    }





}
