package com.enveu.utils.helpers

import android.graphics.Canvas
import android.widget.EdgeEffect
import androidx.dynamicanimation.animation.SpringAnimation
import androidx.dynamicanimation.animation.SpringForce
import androidx.recyclerview.widget.RecyclerView

private const val OVERSCROLL_TRANSLATION_MAGNITUDE = 0.3f
private const val FLING_TRANSLATION_MAGNITUDE = 0.6f

// Recycler View Items Bounce Effect Native Code 08-11-2024

class BounceEdgeEffectFactory(private val bounceType: Int) : RecyclerView.EdgeEffectFactory() {

    override fun createEdgeEffect(recyclerView: RecyclerView, direction: Int): EdgeEffect {
        return object : EdgeEffect(recyclerView.context) {

            private var translationAnim: SpringAnimation? = null

            override fun onPull(deltaDistance: Float) {
                super.onPull(deltaDistance)
                handlePull(deltaDistance)
            }

            override fun onPull(deltaDistance: Float, displacement: Float) {
                super.onPull(deltaDistance, displacement)
                handlePull(deltaDistance)
            }

            private fun handlePull(deltaDistance: Float) {
                val sign = if (direction == getBounceType()) -1 else 1
                val distance = if (bounceType == VERTICAL_BOUNCE) {
                    sign * recyclerView.height * deltaDistance * OVERSCROLL_TRANSLATION_MAGNITUDE
                } else {
                    sign * recyclerView.width * deltaDistance * OVERSCROLL_TRANSLATION_MAGNITUDE
                }

                if (bounceType == VERTICAL_BOUNCE) {
                    recyclerView.scrollBy(0, distance.toInt())
                } else if (bounceType == HORIZONTAL_BOUNCE) {
                    recyclerView.scrollBy(distance.toInt(), 0)
                }

                translationAnim?.cancel()
            }

            override fun onRelease() {
                super.onRelease()
                if (recyclerView.translationX != 0f || recyclerView.translationY != 0f) {
                    translationAnim = createAnim().also { it.start() }
                }
            }

            override fun onAbsorb(velocity: Int) {
                super.onAbsorb(velocity)
                val sign = if (direction == getBounceType()) -1 else 1
                val translationVelocity = sign * velocity * FLING_TRANSLATION_MAGNITUDE
                translationAnim?.cancel()

                translationAnim = createAnim().setStartVelocity(translationVelocity)?.also { it.start() }
            }

            override fun draw(canvas: Canvas?): Boolean {
                return false
            }

            override fun isFinished(): Boolean {
                return translationAnim?.isRunning?.not() ?: true
            }

            private fun getBounceType(): Int {
                return when (bounceType) {
                    VERTICAL_BOUNCE -> DIRECTION_BOTTOM
                    HORIZONTAL_BOUNCE -> DIRECTION_RIGHT
                    else -> DIRECTION_BOTTOM
                }
            }

            private fun createAnim(): SpringAnimation {
                val axis = if (bounceType == VERTICAL_BOUNCE) SpringAnimation.TRANSLATION_Y else SpringAnimation.TRANSLATION_X
                return SpringAnimation(recyclerView, axis).apply {
                    spring = SpringForce().apply {
                        finalPosition = 0f
                        dampingRatio = SpringForce.DAMPING_RATIO_MEDIUM_BOUNCY
                        stiffness = SpringForce.STIFFNESS_LOW
                    }
                }
            }
        }
    }

    companion object {
        const val VERTICAL_BOUNCE = 3
        const val HORIZONTAL_BOUNCE = 2
    }
}



