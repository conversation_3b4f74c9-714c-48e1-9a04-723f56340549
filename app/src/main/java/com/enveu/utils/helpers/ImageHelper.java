package com.enveu.utils.helpers;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SimpleTarget;


import com.bumptech.glide.request.target.Target;
import com.enveu.R;
import com.enveu.utils.Logger;
import com.enveu.utils.commonMethods.AppCommonMethod;


public class ImageHelper {

    private static final ImageHelper ourInstance = new ImageHelper();
    private static Glide mGlideObj;
    private static RequestOptions requestOptions;

    public static ImageHelper getInstance(Context context) {
        mGlideObj = Glide.get(context);
        requestOptions = new RequestOptions();

        return ourInstance;
    }

    public void loadImageTo(ImageView imageView, String imageUrl) {
        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
                load(imageUrl).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).timeout(6000).into(imageView);
    }
    public void loadImageToListPortrait(ImageView imageView, String imageUrl) {
        requestOptions.placeholder(R.color.placeholder_bg);
        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
                load(imageUrl).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).timeout(6000).into(imageView);
    }

//    public void loadListImage(ImageView imageView, String imageUrl) {
//        Logger.d("ImageURLL-->>"+imageUrl);
//        requestOptions.placeholder(R.color.placeholder_bg);
//        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
//                load(imageUrl).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).into(imageView);
//    }

    public void loadListImage(ImageView imageView, String imageUrl) {
        Logger.d("ImageURLL-->>" + imageUrl);
        RequestOptions options = new RequestOptions()
                .placeholder(R.color.placeholder_bg)
                .override(Target.SIZE_ORIGINAL);
        Glide.with(imageView.getContext())
                .setDefaultRequestOptions(options)
                .load(imageUrl)
                .thumbnail(0.6f).timeout(6000)
                .transition(DrawableTransitionOptions.withCrossFade(300))
                .into(imageView);
    }



    public void loadPortraitImage(ImageView imageView, String imageUrl) {
        Logger.d("ImageURLL-->>"+imageUrl);
        requestOptions.placeholder(R.color.placeholder_bg);
        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
                load(imageUrl).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).timeout(6000).into(imageView);
    }


    public void loadListSQRImage(ImageView imageView, String imageUrl) {
        requestOptions.placeholder(R.color.placeholder_bg);
        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
                load(imageUrl).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).timeout(6000).into(imageView);
    }
    public void loadListLandscapeImage(ImageView imageView, String imageUrl) {
        requestOptions.placeholder(R.color.placeholder_bg);
        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
                load(imageUrl).timeout(6000)
                .transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).into(imageView);
    }

    public void loadImageToProfile(ImageView imageView, String imageUrl) {
      //  requestOptions.placeholder(R.drawable.profile_dark);

        if (StringUtils.isNullOrEmptyOrZero(imageUrl)) {
           // Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(AppCommonMethod.options).load(R.drawable.profile_dark).transition(DrawableTransitionOptions.withCrossFade(250)).thumbnail(0.6f).into(imageView);
        } else {
            Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(AppCommonMethod.options).
                    load(imageUrl).timeout(6000).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).into(imageView);
        }
    }

    public void loadCircleImageTo(ImageView imageView, String imageUrl) {
        requestOptions.placeholder(R.color.placeholder_bg);
        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
                load(imageUrl).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).timeout(6000).into(imageView);

//        requestOptions.placeholder(R.color.placeholder_bg);
//        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
//                load(imageUrl).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).into(imageView);

    }

    public void loadListSQRImageForAlbumList(ImageView imageView, String imageUrl) {
        requestOptions.placeholder(R.drawable.music_note);
        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
                load(imageUrl).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).timeout(6000).into(imageView);
    }

    public void loadImageTo(ImageView imageView, String imageUrl, RequestOptions requestOptions) {
        requestOptions.placeholder(R.color.placeholder_bg);
        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
                load(imageUrl).timeout(6000).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.1f).into(imageView);
    }

    public void loadImageTo(ImageView imageView, Uri imageUrl) {
        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
                load(imageUrl).timeout(6000).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.1f).into(imageView);
    }

    public void loadImageTo(ImageView imageView, String imageUrl, SimpleTarget<Drawable> simpleTarget) {
        Glide.with(mGlideObj.getContext()).
                load(imageUrl).timeout(6000).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).into(simpleTarget);


    }


    public void tabsloadImage(ImageView imageView, String url, Drawable placeholder) {
        if (placeholder==null){
            Glide.with(imageView.getContext())
                    .load(url).timeout(6000)
                    .transition(DrawableTransitionOptions.withCrossFade(2))
                    .apply(new RequestOptions()
                    .fitCenter())
                    .into(imageView);
        }else {
            Glide.with(imageView.getContext())
                    .load(url).timeout(6000)
                    .transition(DrawableTransitionOptions.withCrossFade(2))
                    .apply(new RequestOptions().placeholder(placeholder)
                    .error(placeholder))
                    .into(imageView);
        }

    }

    public void loadCIRImage(ImageView imageView, String url, Drawable placeholder) {
        if (placeholder==null){
            if (requestOptions!=null){
                requestOptions.placeholder(R.color.placeholder_bg);
            }
            Glide.with(imageView.getContext())
                    .load(url)
                    .apply(new RequestOptions().placeholder(placeholder).override(300,300)
                            .error(placeholder))
                    .thumbnail(0.5f).timeout(6000)
                    .into(imageView);
        }else {
            Logger.e("ImageHelper", "" + url+"  "+" ");
            Glide.with(imageView.getContext())
                    .load(url)
                    .apply(new RequestOptions().placeholder(placeholder).override(300,300)
                            .error(placeholder))
                    .thumbnail(0.5f).timeout(6000)
                    .into(imageView);
           }
    }
    public void loadCIRImage2(ImageView imageView, String url, Drawable placeholder) {
        requestOptions.placeholder(R.color.placeholder_bg).circleCrop();
        Glide.with(mGlideObj.getContext()).setDefaultRequestOptions(requestOptions).
                load(url).transition(DrawableTransitionOptions.withCrossFade(2)).thumbnail(0.6f).timeout(6000).into(imageView);

//        if (placeholder==null){
//            if (requestOptions!=null){
//                requestOptions.placeholder(R.color.placeholder_bg);
//            }
//            Glide.with(imageView.getContext())
//                    .load(url)
//                    .apply(new RequestOptions().placeholder(placeholder).error(placeholder))
//                    .thumbnail(0.5f)
//                    .into(imageView);
//        }else {
//            Logger.e("ImageHelper", "" + url+"  "+" ");
//            Glide.with(imageView.getContext())
//                    .load(url)
//                    .apply(new RequestOptions().placeholder(placeholder).error(placeholder))
//                    .thumbnail(0.5f)
//                    .into(imageView);
//        }
    }
}
