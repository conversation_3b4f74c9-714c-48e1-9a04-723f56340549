package com.enveu.utils.helpers

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.enveu.R
import com.enveu.activities.multiplePlaylist.MultiplePlaylistViewModel
import com.enveu.activities.sponsorArtist.ArtistAndSponserActivity
import com.enveu.activities.usermanagment.ui.ActivityLogin
import com.enveu.activities.usermanagment.ui.PaymentDetailPage
import com.enveu.appLevelModel.FeatureFlagModel
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModelV3.videoDetailV3.list.DataItem
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ImageContent
import com.enveu.callbacks.player.callback.OnSongItemClick
import com.enveu.callbacks.player.callback.SongClick
import com.enveu.databinding.QueueBottomSheetBinding
import com.enveu.fragments.album.adapter.SongsAdapter
import com.enveu.fragments.dialog.CommonDialogFragment
import com.enveu.player.utils.TimeUtils.formatDuration
import com.enveu.utils.CustomProgressBar
import com.enveu.utils.commonMethods.AppCommonMethod
import com.enveu.utils.commonMethods.AppConfigMethod.parseFeatureFlagList
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.intentlaunchers.ActivityLauncher
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.converter.StringsHelper
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class QueueBottomDialogFragment : BottomSheetDialogFragment(), SongClick,CommonDialogFragment.EditDialogListener {
    private var songsAdapter: SongsAdapter? = null
    private val tumbnailImg = ""
    private val imgDefaultFromItem = ""
    private var binding: QueueBottomSheetBinding? = null
    private var featureFlag: FeatureFlagModel? = null
    private var customProgressBar: CustomProgressBar? = null
    private var imageContent: ImageContent? = null
    private var isLoggedIn :Boolean = false
    private var token: String? = null
    private var preference: KsPreferenceKeys? = null
    private var isUserNotEntitle = false
    private var railInjectionHelper: RailInjectionHelper? = null
    private var onSongItemClick: OnSongItemClick? = null
    private var resEntitle: ResponseEntitle? = null
    private var playlistViewModel : MultiplePlaylistViewModel?= null
    private val stringsHelper by lazy { StringsHelper }


    @SuppressLint("MissingInflatedId")
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = QueueBottomSheetBinding.inflate(inflater, container, false)
        customProgressBar = binding!!.progressBar
        featureFlag = parseFeatureFlagList()
        customProgressBar!!.visibility = View.VISIBLE
        updateMetaData(currentSong!!)
        return binding!!.root
    }

    fun updateFirstSongs(dataItem: DataItem) {
       updateMetaData(dataItem)
    }

    private fun updateMetaData(currentSong: DataItem) {
        binding!!.textSongName.text = currentSong.title
        binding!!.songDuration.text = formatDuration(currentSong.audioContent!!.duration.toLong())
        val artistName = StringBuilder()
        if (currentSong.customData != null && currentSong.customData.songsArtistIds != null && currentSong.customData.songsArtistIds.isNotEmpty()) {
            for (index in currentSong.customData.songsArtistIds.indices) {
                if (index == currentSong.customData.songsArtistIds.size - 1) {
                    artistName.append(currentSong.customData.songsArtistIds[index].title)
                } else {
                    artistName.append(currentSong.customData.songsArtistIds[index].title).append(", ")
                }
            }
        } else {
            binding!!.textArtistName.visibility = View.GONE
        }
        binding!!.textArtistName.text = artistName.toString()

        setClick()
        try {

            if (currentSong.imageContent !=null && currentSong.imageContent.src!=null) {
                imageContent = currentSong.imageContent
            } else {
                imageContent  = AppCommonMethod.getImageContent(currentSong)
            }
            binding?.mainLay?.background = AppCommonMethod.setGradientBackgroundColor(Color.parseColor(AppCommonMethod.getDominantColor(imageContent)), Color.parseColor("#000000"), "TOP_TO_BOTTOM")
            if (imageContent!=null && imageContent?.src!=null) {
                ImageHelper.getInstance(context).loadListImage(binding!!.songImage, imageContent?.src)
            } else {
                binding!!.imageTitle.text = currentSong.title
            }

            Glide.with(requireContext())
                .asGif() // Ensure the content is treated as GIF
                .load(R.drawable.sound_white_audio_wave) // Or a local resource
                .into(binding!!.gif)

        } catch (e: Exception) {
            e.printStackTrace()
            Log.d("BottomDialogFragment", e.message!!)
        }

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        preference = KsPreferenceKeys.getInstance()
        token = preference?.appPrefAccessToken
        if (preference?.appPrefLoginStatus.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            isLoggedIn = true
        }
        setViewModel()
        setAdapter()
    }
    private fun setAdapter() {
        songsAdapter = allSongListData?.subList(playingIndex+1, allSongListData?.size!!)
            ?.let { SongsAdapter(it, this@QueueBottomDialogFragment, featureFlag = featureFlag!!,true,playingIndex!!) }
        binding?.myRecycleView?.adapter = songsAdapter
        customProgressBar!!.visibility = View.GONE
    }

    private fun setClick() {}
    private fun setViewModel() {
        railInjectionHelper = ViewModelProvider(this)[RailInjectionHelper::class.java]
        playlistViewModel = ViewModelProvider(this)[MultiplePlaylistViewModel::class.java]

    }

    companion object {
        private var currentSong: DataItem? = null
        private var playingIndex = 0
        private var allSongListData: MutableList<DataItem>? = null
        fun getInstance(song: MutableList<DataItem>?, redirection: String?,currentSongs: DataItem?,playingIndex: Int): QueueBottomDialogFragment {
            allSongListData = song
            currentSong = currentSongs
            this.playingIndex=playingIndex
            val bundle = Bundle()
            bundle.putString(AppConstants.FROM_REDIRECTION, redirection)
            val fragment = QueueBottomDialogFragment()
            fragment.arguments = bundle
            return fragment
        }
    }
    override fun onAttach(context: Context) {
        super.onAttach(context)
        onSongItemClick = context as OnSongItemClick
    }

    override fun onSongClick(song: DataItem) {
        if (isLoggedIn) {
            preference?.setShuffleEnable(false)
//            KsPreferenceKeys.getInstance().setRepeatEnable(false)
            if (KsPreferenceKeys.getInstance().sponsorArtistId.isNullOrEmpty()  && !ArtistAndSponserActivity.AppState.isSkipped){
                ActivityLauncher.getInstance().artistAndSponserActivity(requireActivity(),
                    ArtistAndSponserActivity::class.java)
            }else{
                if (song.isPremium) {
                    checkEntitlement(token, song, AppCommonMethod.getImageContent(song), false)
                } else {
                    if (onSongItemClick != null) {
                        onSongItemClick!!.songItemClick(
                            allSongListData!!,
                            song,
                            song.externalRefId,
                            AppCommonMethod.getImageContent(song),
                            AppCommonMethod.getSongsPosterImageUrl(song),
                            false, isQueueItemClick = true
                        )
                    }
                }
            }
            ArtistAndSponserActivity.AppState.isSkipped = false
        } else {
            ActivityLauncher.getInstance().loginActivity(
                requireActivity(), ActivityLogin::class.java, ""
            )
        }
    }


    private fun checkEntitlement(token: String?, song: DataItem, imageContent: ImageContent?, playQueueItems: Boolean?) {
        customProgressBar?.visibility = View.VISIBLE
        if (isLoggedIn) {
            railInjectionHelper?.hitApiEntitlement(token, song.sku)?.observe(this@QueueBottomDialogFragment) { responseEntitle ->
                customProgressBar?.visibility = View.GONE
                if (responseEntitle != null && responseEntitle.data != null) {
                    resEntitle = responseEntitle
//                    preference?.planMaxAllowedConcurrency = responseEntitle.data.maxAllowedConcurrency
                    if (responseEntitle.data.entitled) {
                        railInjectionHelper?.externalRefID(responseEntitle.data?.accessToken, responseEntitle.data?.sku)?.observe(this) { drm ->
                            if (onSongItemClick != null) {
                                onSongItemClick!!.songItemClick(
                                    allSongListData!!,
                                    song,
                                    drm.data?.externalRefId!!,
                                    imageContent,
                                    AppCommonMethod.getSongsPosterImageUrl(song),
                                    playQueueItems
                                )
                            }
                        }
                    } else {
                        isUserNotEntitle = true
                        customProgressBar?.visibility = View.GONE
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_notEntitled.toString(),
                                getString(R.string.popup_notEntitled)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_select_plan.toString(),
                                getString(R.string.popup_select_plan)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_purchase.toString(),
                                getString(R.string.popup_purchase)
                            ), stringsHelper.stringParse(stringsHelper.instance()?.data?.config?.popup_cancel.toString(), getString(
                                R.string.popup_cancel))
                        )
                    }
                } else {
                    customProgressBar?.visibility = View.GONE
                    if (responseEntitle!!.responseCode != null && responseEntitle.responseCode == 4302) {
                        //  clearCredientials(preference)
                        ActivityLauncher.getInstance().loginActivity(
                            requireActivity(), ActivityLogin::class.java, ""
                        )
                    } else {
                        customProgressBar?.visibility = View.GONE
                        commonDialog(
                            stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_error.toString(),
                                getString(R.string.popup_error)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_something_went_wrong.toString(),
                                getString(R.string.popup_something_went_wrong)
                            ), stringsHelper.stringParse(
                                stringsHelper.instance()?.data?.config?.popup_continue.toString(),
                                getString(R.string.popup_continue)
                            ), ""
                        )
                    }
                }
            }
        } else {
            ActivityLauncher.getInstance().loginActivity(
                requireActivity(), ActivityLogin::class.java, ""
            )
        }

    }

    private fun commonDialog(title: String, description: String, actionBtn: String, cancelBtn : String) {
        val fm = requireActivity().supportFragmentManager
        val commonDialogFragment = CommonDialogFragment.newInstanceWithCancelBtn(title, description, actionBtn, cancelBtn)
        commonDialogFragment.setEditDialogCallBack(this)
        commonDialogFragment.show(fm, AppConstants.MESSAGE)
    }


    override fun onThreeDotClick(song: DataItem) {
        val bottomSheetDialog = BottomDialogFragment.getInstance(song,AppConstants.QUEUE)
        bottomSheetDialog.show(activity?.supportFragmentManager!!, "Bottom Sheet Dialog Fragment")
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onDeleteItemClick(song: DataItem) {
        try {
            customProgressBar?.visibility = View.VISIBLE
            playlistViewModel?.removeContentPlaylist(preference?.appPrefAccessToken!!, song.id, "QUEUED", "", true)
            allSongListData?.let { list ->
                // Find the item with the matching ID and remove it
                list.removeAll { dataItem -> dataItem.id == song.id }
            }
            // Update the adapter without clearing everything
            songsAdapter?.notifyDataSetChanged()
            customProgressBar?.visibility = View.GONE
        } catch (e:Exception) {
            Log.d("Exception", "onDeleteItemClick: ")
        }

    }

    override fun onActionBtnClicked() {
        if (isUserNotEntitle) {
            ActivityLauncher.getInstance().goToDetailPlanScreen(activity, PaymentDetailPage::class.java, true, resEntitle)
        }
    }

    override fun onCancelBtnClicked() {

    }
}
