package com.enveu.utils.helpers;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModelProvider;

import com.enveu.Bookmarking.BookmarkingViewModel;
import com.enveu.ObservableRxList;
import com.enveu.activities.layers.EntitlementLayer;
import com.enveu.beanModel.drm.DRM;
import com.enveu.beanModel.emptyResponse.ResponseEmpty;
import com.enveu.beanModel.entitle.ResponseEntitle;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModel.responseGetWatchlist.ResponseGetIsWatchlist;
import com.enveu.beanModel.responseIsLike.ResponseIsLike;
import com.enveu.beanModel.sponsorUserTracking.UserTrackingResponse;
import com.enveu.beanModelV3.PlayListResponse;
import com.enveu.beanModelV3.PlaylistItem;
import com.enveu.beanModelV3.continueWatching.DataItem;
import com.enveu.beanModelV3.playListModelV2.EnveuCommonResponse;
import com.enveu.beanModelV3.searchGenres.SearchGenres;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.beanModelV3.videoDetailV3.list.EnvSongDetail;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ArtistListResponse;
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.EnvMediaContentList;
import com.enveu.beanModelV3.videoDetailV3.queue_response.AddToQueueRequestModel;
import com.enveu.beanModelV3.videoDetailV3.queue_response.AddToQueueResponse;
import com.enveu.beanModelV3.videoDetailV3.queue_response.GetQueueResponse;
import com.enveu.beanModelV3.videoDetailV3.queue_response.RemoveQueueResponse;
import com.enveu.bean_model_v2_0.listAll.likeList.Response;
import com.enveu.callbacks.apicallback.ApiResponseModel;
import com.enveu.callbacks.commonCallbacks.CommonApiCallBack;
import com.enveu.callbacks.likelistCallback.ApiLikeList;
import com.enveu.client.api_callback.NetworkResultCallback;
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory;
import com.enveu.client.bookmarking.bean.continuewatching.ContinueWatchingBookmark;
import com.enveu.client.enums.Layouts;
import com.enveu.client.enums.PlaylistType;
import com.enveu.client.playlist.beanv2_0.EnvPlaylistContents;
import com.enveu.client.watchHistory.beans.ItemsItem;
import com.enveu.layersV2.ContinueWatchingLayer;
import com.enveu.layersV2.ListPaginationDataLayer;
import com.enveu.layersV2.SearchLayer;
import com.enveu.layersV2.SeasonEpisodesList;
import com.enveu.layersV2.SeriesDataLayer;
import com.enveu.layersV2.VideoDetailLayer;
import com.enveu.networking.apistatus.APIStatus;
import com.enveu.networking.errormodel.ApiErrorModel;
import com.enveu.networking.responsehandler.ResponseModel;
import com.enveu.networking.servicelayer.APIServiceLayer;
import com.enveu.repository.home.HomeFragmentRepository;
import com.enveu.repository.userManagement.RegistrationLoginRepository;
import com.enveu.utils.Logger;
import com.enveu.utils.ObjectHelper;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;


public class RailInjectionHelper extends AndroidViewModel {
    private final MutableLiveData<RailCommonData> mutableRailCommonData = new MutableLiveData<>();
    private final ObservableRxList<RailCommonData> observableList = new ObservableRxList<>();
    private int i = 0;
    private List<BaseCategory> baseCategories;
    private KsPreferenceKeys preference;
    private String isLogin;
    private boolean entittleStatus = false;
    String genresItems = "";

    private List<String> screenWidgetName = new ArrayList<>();
    private List<Integer> screenWidgetNumber = new ArrayList<>();
    AtomicInteger counter = new AtomicInteger(0);
    Map<Integer, String> idToIndexMap = new HashMap<>();
    Map<Integer, String> nameToIndexMap = new HashMap<>();
    Map<Integer, String> idToContentIdIndexMap = new HashMap<>();
    Map<Integer, BaseCategory> screenWidgetList = new HashMap<>();

    List<RailCommonData> collectionRailCommonData = new ArrayList<>();


    public RailInjectionHelper(@NonNull Application application) {
        super(application);
    }

    public void getScreenWidgets(Activity activity, String screenId, String accessToken, Boolean tag, CommonApiCallBack commonApiCallBack) {
        if(screenId!=null){
            if (railDataMap != null){
                railDataMap.clear();
            }
            APIServiceLayer.getInstance().getCategories(screenId).observe((LifecycleOwner) activity, baseCategoriesList -> {
                baseCategories = baseCategoriesList;
                i = 0;
                if (ObjectHelper.isNotEmpty(baseCategories)) {
                    getScreenListing(activity, accessToken, commonApiCallBack);
                } else {
                    commonApiCallBack.onFailure(new Throwable("No Data"));
                }
            });
        }
    }





    private final ConcurrentHashMap<Integer, RailCommonData> railDataMap = new ConcurrentHashMap<>();

    private void getScreenListing(Activity activity, String accessToken, CommonApiCallBack commonApiCallBack) {
        AtomicInteger completedRequests = new AtomicInteger(0);
        if (baseCategories.size() > 0) {
            int totalRequests = 0;
            for (BaseCategory baseCategory:baseCategories){
                String type = "";
                if (baseCategory.getType() != null)
                    type = baseCategory.getType();
                if (type.equalsIgnoreCase(AppConstants.WIDGET_TYPE_CONTENT)) {
                    totalRequests++;
                } else if (type.equalsIgnoreCase(AppConstants.WIDGET_TYPE_AD)) {
                    totalRequests++;
                }
            }
            for (int index = 0; index < baseCategories.size(); index++) {
                BaseCategory screenWidget = baseCategories.get(index);
                String type = "";
                if (screenWidget.getType() != null)
                    type = screenWidget.getType();
                if (type.equalsIgnoreCase(AppConstants.WIDGET_TYPE_CONTENT)) {
                    getRailDetails(activity, accessToken, screenWidget, commonApiCallBack, index, completedRequests, totalRequests);
                } else if (type.equalsIgnoreCase(AppConstants.WIDGET_TYPE_AD)) {
                    getAdsDetails(activity, screenWidget, commonApiCallBack, index, completedRequests, totalRequests);
                }
            }
        } else {
            commonApiCallBack.onFinish();
        }
    }

    private void checkAndFinishIfComplete(CommonApiCallBack commonApiCallBack, AtomicInteger completedRequests, int totalRequests) {
        if (completedRequests.incrementAndGet() == totalRequests) {
//            for (int i = 0; i < totalRequests; i++) {
//                RailCommonData railData = railDataMap.get(i);
//                if (railData != null) {
//                    commonApiCallBack.onSuccess(railData);  // Display the result in sequence
//                }
//            }
            List<RailCommonData> orderedData = new ArrayList<>(railDataMap.values());
            Collections.sort(orderedData, Comparator.comparingInt(RailCommonData::getIndex));

            // Notify the fragment with ordered data

            commonApiCallBack.onDataLoaded(orderedData);
            commonApiCallBack.onFinish();
        }
        collectionRailCommonData.size();
        if (collectionRailCommonData.size() > 0) {
        if (completedRequests.get() == totalRequests) {
                for (RailCommonData data : collectionRailCommonData) {
                    int index = data.getIndex();
                    for (int i = 0; i < totalRequests; i++) {
                        if(i== index){
                            railDataMap.put(index, data);
                        }
                    }

                }
            }
            Log.d("checkAndFinishIfComplete", "checkAndFinishIfComplete Inside: "+completedRequests +" and " + totalRequests);

            List<RailCommonData> orderedData = new ArrayList<>(railDataMap.values());
            Collections.sort(orderedData, (data1, data2) -> Integer.compare(data1.getIndex(), data2.getIndex()));
            commonApiCallBack.onDataLoaded(orderedData);

            commonApiCallBack.onFinish();
        }
    }




    private void getAdsDetails(Activity activity, BaseCategory screenWidget, CommonApiCallBack commonApiCallBack,int index, AtomicInteger completedRequests, int totalRequests) {
        if (!KsPreferenceKeys.getInstance().getEntitlementStatus()) {
            RailCommonData railCommonData = new RailCommonData(screenWidget, index);
            railCommonData.setIsAd(true);
            railDataMap.put(index, railCommonData); // Store data by index if needed
            commonApiCallBack.onSuccess(railCommonData);
            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
        }
    }


    private void getRailDetails(Activity activity, String accessToken, BaseCategory screenWidget, CommonApiCallBack commonApiCallBack,int index, AtomicInteger completedRequests, int totalRequests) {
        if (screenWidget.getLayout() != null && screenWidget.getLayout().equalsIgnoreCase(Layouts.HRO.name())) {
            getHeroDetails(activity, screenWidget, commonApiCallBack,index, completedRequests, totalRequests);
        } else if (screenWidget.getLayout() != null && screenWidget.getLayout().equalsIgnoreCase("COL")) {
            getPlayListIdForCollection(activity, screenWidget, commonApiCallBack, index, completedRequests, totalRequests);
        }else {
            if (screenWidget.getContentPlayListType() != null) {
                String contentType = screenWidget.getContentPlayListType();
                if (contentType.equalsIgnoreCase(PlaylistType.BVC.name()) || contentType.equalsIgnoreCase(PlaylistType.EN_OVP.name())) {
                    getPlaylistDetailsById(activity, accessToken, screenWidget, commonApiCallBack,index, completedRequests, totalRequests);
                } else if (contentType.equalsIgnoreCase(PlaylistType.K_PDF.name())) {
                    // Get Playlist data from Predefined Kaltura
                } else if (contentType.equalsIgnoreCase(PlaylistType.KTM.name())) {
                    // Get Playlist data from Kaltura
                }
            }
        }
    }



    private void getHeroDetails(Activity activity, BaseCategory screenWidget, CommonApiCallBack commonApiCallBack,int index, AtomicInteger completedRequests, int totalRequests) {
        RailCommonData railCommonData = new RailCommonData();
        railCommonData.getHeroRailCommonData(screenWidget, activity, new CommonApiCallBack() {
            @Override
            public void onSuccess(Object item) {
                railCommonData.setIndex(index);
                railDataMap.put(index, railCommonData); // Store data by index if needed
                checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
            }

            @Override
            public void onDataLoaded(List<RailCommonData> orderedData) {

            }

            @Override
            public void onFailure(Throwable throwable) {
                commonApiCallBack.onFailure(new Throwable("ASSET NOT FOUND"));
                checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
            }

            @Override
            public void onFinish() {

            }
        });

    }


    public void getPlayListIdForCollection(Activity activity,
                                           BaseCategory screenWidget,
                                           CommonApiCallBack commonApiCallBack,
                                           int index,
                                           AtomicInteger completedRequests,
                                           int totalRequests) {
        int index1=index;
        String assetID = screenWidget.getCollectionContentID(); // Or however you get it
        idToContentIdIndexMap.put(index,assetID);

        screenWidgetNumber.add(index);
        screenWidgetList.put(index1,screenWidget);
        SeriesDataLayer.getInstance().getSeriesData(assetID, false, new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {
                // Optional: show loading UI
                Log.d("Error", "onStart: starting");
            }

            @Override
            public void onSuccess(RailCommonData response) {
                // On success, directly call getPlaylistDetailsById
                Log.d("Error", "onSuccess: PlaylistIds" +response.getEnveuVideoItemBeans().get(0).getSeriesCustomData().getPlaylist_ids());

                if(response.getEnveuVideoItemBeans().get(0).getTitle()!=null){
                    screenWidgetName.add(response.getEnveuVideoItemBeans().get(0).getTitle());
                }
                // playlistIds
                String idsStr = response.getEnveuVideoItemBeans().get(0).getSeriesCustomData().getPlaylist_ids();
                //Rail Title
                String railName = response.getEnveuVideoItemBeans().get(0).getTitle();
                //
                String contentIdOfCollection = String.valueOf(response.getEnveuVideoItemBeans().get(0).getId());

                for (Map.Entry<Integer, String> entry : idToContentIdIndexMap.entrySet()) {
                    int index = entry.getKey();
                    String assetId = entry.getValue();

                    if (assetId.equals(contentIdOfCollection)) {
                        idToIndexMap.put(index, idsStr);
                        nameToIndexMap.put(index, railName);
                    }
                }

                APIServiceLayer.getInstance().getPlayListDataByIds(idsStr.toString(), 0, 20).observe((LifecycleOwner) activity, enveuCommonResponse -> {
                    if (enveuCommonResponse != null) {
                        Gson gson = new Gson();
                        PlayListResponse playListDetailsResponse = gson.fromJson(enveuCommonResponse.toString(), PlayListResponse.class);
                        List<PlaylistItem> items = playListDetailsResponse.getData();

                        List<EnveuVideoItemBean> enveuVideoItemBeanList= new ArrayList<>();
                        for (PlaylistItem itemsItem :items){
                            EnveuVideoItemBean enveuVideoItemBean = new EnveuVideoItemBean(itemsItem);
                            enveuVideoItemBeanList.add(enveuVideoItemBean);
                        }
                        int indexing = 0;
                        Boolean matchCondition = true;
                        BaseCategory screenWidgetNew=null;

                        screenWidget.setShowHeader(true);

                        for (Map.Entry<Integer, String> entry : idToIndexMap.entrySet()) {

                            int index = entry.getKey();
                            String assetIds = entry.getValue();
                            String[] splitIds = assetIds.split(",");
                            Arrays.sort(splitIds);
                            String sortedCommaSeparated = String.join(",", splitIds);


                            List<String> idList = new ArrayList<>();

                            for (int i = 0; i < enveuVideoItemBeanList.size(); i++) {
                                String id = String.valueOf(enveuVideoItemBeanList.get(i).getId());
                                if (id != null && !id.trim().isEmpty()) {
                                    idList.add(id.trim());
                                }
                            }
                            Collections.sort(idList);
                            String sortedIds = String.join(",", idList);

                            if(sortedCommaSeparated.equals(sortedIds)){
                                indexing=index;
                                screenWidgetNew=screenWidgetList.get(index);
                                String railIndexName = nameToIndexMap.get(index);
                                screenWidgetNew.setShowHeader(true);
                                screenWidgetNew.setName(railIndexName);
                            }
                        }
                        if (enveuVideoItemBeanList.size()>0 && screenWidgetNew != null){
                            Log.d("myCheck","at rail data set, screenWidget.getCollectionContentID(): "+screenWidgetNew.getCollectionContentID()+" and stored index: "+screenWidgetNumber.get(counter.get()));
                            RailCommonData railCommonData = new RailCommonData(enveuVideoItemBeanList, screenWidgetNew, false, indexing);
                            collectionRailCommonData.add(railCommonData);
                        }
                    }
                    checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                });

            }

            @Override
            public void onError(ApiErrorModel apiError) {
                // Optional: error logging or UI
                Log.d("Error", "onError: "+apiError.toString());
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                // Optional: error logging or UI
                Log.d("Error", "onFailure: " + httpError.toString());
            }
        });
    }


    private void getPlaylistDetailsById(Activity activity, String accessToken, BaseCategory screenWidget, CommonApiCallBack commonApiCallBack,int index, AtomicInteger completedRequests, int totalRequests) {
        int contentSize = 0;
        if (screenWidget.getContentSize() != null) {
            contentSize = screenWidget.getContentSize();
        }
        if (screenWidget.getName() != null && screenWidget.getReferenceName() != null && (screenWidget.getReferenceName().equalsIgnoreCase("special_playlist") || screenWidget.getReferenceName().equalsIgnoreCase(AppConstants.ContentType.CONTINUE_WATCHING.name()))) {
            injectContinueWatchingRail(activity, contentSize, screenWidget, commonApiCallBack,index, completedRequests,totalRequests);
        } else if (screenWidget.getReferenceName() != null && (screenWidget.getReferenceName().equalsIgnoreCase("you_may_like"))) {
            injectYouMayLikeRail(activity, contentSize, screenWidget, commonApiCallBack,index, completedRequests,totalRequests);
        } else if (screenWidget.getName() != null && screenWidget.getReferenceName() != null && screenWidget.getReferenceName().equalsIgnoreCase(AppConstants.ContentType.MY_WATCHLIST.name())) {
            injectWatchlistRail(activity, contentSize, screenWidget, commonApiCallBack,index, completedRequests,totalRequests);
        } else {
            APIServiceLayer.getInstance().getPlayListById(screenWidget.getContentID(), accessToken, 0, contentSize, getGenresItems(),new NetworkResultCallback<EnvPlaylistContents>(){
                @Override
                public void failure(boolean status, int errorCode, @NonNull String message) {
                    checkAndFinishIfComplete(commonApiCallBack,completedRequests,totalRequests);
                }

                @Override
                public void success(@androidx.annotation.Nullable Boolean status, @androidx.annotation.Nullable retrofit2.Response<EnvPlaylistContents> response) {
                    Gson gson =new Gson();
                    if (response != null && response.body() != null) {
                        String json = gson.toJson(response.body());
                        EnveuCommonResponse enveuCommonResponse = gson.fromJson(json, EnveuCommonResponse.class);
                        if (enveuCommonResponse != null) {
                            RailCommonData railCommonData = new RailCommonData(enveuCommonResponse.getData(), screenWidget, false, index);
                            railDataMap.put(index, railCommonData);  // Store data by index
                            commonApiCallBack.onSuccess(railCommonData);
                        }
                        checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                    }
                }

                @Override
                public void loading(boolean isLoading) {

                }
            });
//                    .observe((LifecycleOwner) activity, enveuCommonResponse -> {
//                        if (enveuCommonResponse != null) {
//                            RailCommonData railCommonData = new RailCommonData(enveuCommonResponse.getData(), screenWidget, false, index);
//                            railDataMap.put(index, railCommonData);  // Store data by index
//                            commonApiCallBack.onSuccess(railCommonData);
////                            i++;
////                            getScreenListing(activity, commonApiCallBack);
//                            // commonApiCallBack.onSuccess(railCommonData);
//                        }
//                        checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
//                    });
        }
    }

    private void injectWatchlistRail(Activity activity, int contentSize, BaseCategory screenWidget, CommonApiCallBack commonApiCallBack,int index, AtomicInteger completedRequests, int totalRequests) {
        if (preference == null)
            preference = KsPreferenceKeys.getInstance();
        String isLogin = preference.getAppPrefLoginStatus();
        if (isLogin.equalsIgnoreCase(AppConstants.UserStatus.Login.toString())) {
            String token = preference.getAppPrefAccessToken();
            BookmarkingViewModel bookmarkingViewModel = new ViewModelProvider((FragmentActivity) activity).get(BookmarkingViewModel.class);
            bookmarkingViewModel.getMywatchListData(token, 0, contentSize).observe((LifecycleOwner) activity, getContinueWatchingBean -> {
                String videoIds = "";
                if (getContinueWatchingBean != null && getContinueWatchingBean.getData() != null) {
                    List<ItemsItem> continueWatchingBookmarkList = getContinueWatchingBean.getData().getItems();
                    for (ItemsItem continueWatchingBookmark : continueWatchingBookmarkList
                    ) {
                        videoIds = videoIds.concat(String.valueOf(continueWatchingBookmark.getAssetId())).concat(",");
                    }
                    ContinueWatchingLayer.getInstance().getWatchHistoryVideos(continueWatchingBookmarkList, videoIds, new CommonApiCallBack() {
                        @Override
                        public void onSuccess(Object item) {
                            if (item instanceof List) {
                                ArrayList<DataItem> enveuVideoDetails = (ArrayList<DataItem>) item;
                                RailCommonData railCommonData = new RailCommonData();
                                railCommonData.setContinueWatchingData(screenWidget, false, enveuVideoDetails, new CommonApiCallBack() {
                                    @Override
                                    public void onSuccess(Object item) {
                                        railDataMap.put(index, railCommonData); // Store data by index if needed
                                        checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                                    }

                                    @Override
                                    public void onDataLoaded(List<RailCommonData> orderedData) {

                                    }

                                    @Override
                                    public void onFailure(Throwable throwable) {
                                        commonApiCallBack.onFailure(new Throwable("ASSET NOT FOUND"));
                                        checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                                    }

                                    @Override
                                    public void onFinish() {

                                    }
                                });
                            }
                        }

                        @Override
                        public void onDataLoaded(List<RailCommonData> orderedData) {

                        }
                        @Override
                        public void onFailure(Throwable throwable) {
                            commonApiCallBack.onFailure(new Throwable("ASSET NOT FOUND"));
                            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                        }

                        @Override
                        public void onFinish() {

                        }
                    });
                } else {
                    checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                }

            });
        } else {
            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
        }
    }


    // Helper method to handle completion logic


    private void injectContinueWatchingRail(Activity activity, int contentSize, BaseCategory screenWidget, CommonApiCallBack commonApiCallBack,int index, AtomicInteger completedRequests, int totalRequests) {
        if (preference == null)
            preference = KsPreferenceKeys.getInstance();
        String isLogin = preference.getAppPrefLoginStatus();
        if (isLogin.equalsIgnoreCase(AppConstants.UserStatus.Login.toString())) {
            String token = preference.getAppPrefAccessToken();
            BookmarkingViewModel bookmarkingViewModel = new ViewModelProvider((FragmentActivity) activity).get(BookmarkingViewModel.class);
            bookmarkingViewModel.getContinueWatchingData(token, 0, contentSize).observe((LifecycleOwner) activity, getContinueWatchingBean -> {
                String videoIds = "";
                if (getContinueWatchingBean != null && getContinueWatchingBean.getData() != null) {
                    List<ContinueWatchingBookmark> continueWatchingBookmarkLists = getContinueWatchingBean.getData().getContinueWatchingBookmarks();
                    List<ContinueWatchingBookmark> continueWatchingBookmarkList = removeDuplicates(continueWatchingBookmarkLists);
                    for (ContinueWatchingBookmark continueWatchingBookmark : continueWatchingBookmarkList) {
                        videoIds = videoIds.concat(String.valueOf(continueWatchingBookmark.getAssetId())).concat(",");
                    }
                    ContinueWatchingLayer.getInstance().getContinueWatchingVideos(continueWatchingBookmarkList, videoIds, new CommonApiCallBack() {
                        @Override
                        public void onSuccess(Object item) {
                            if (item instanceof List) {
                                ArrayList<DataItem> enveuVideoDetails = (ArrayList<DataItem>) item;
                                // create new List type Array

                                List<DataItem> enveuItem = new ArrayList<>();
                                // add item enveuVideoDetails in enveuItem List
                                enveuItem.addAll(enveuVideoDetails);
                                // sorting enveuItem List based peverious assetId
                                Collections.sort(enveuItem);
                                // stored item new stringArrayList Array
                                ArrayList<DataItem> stringArrayList = new ArrayList<>();
                                stringArrayList.addAll(enveuItem);
                                RailCommonData railCommonData = new RailCommonData();
                                railCommonData.setContinueWatchingData(screenWidget, true, stringArrayList, new CommonApiCallBack() {
                                    @Override
                                    public void onSuccess(Object item) {
                                        railDataMap.put(index, railCommonData); // Store data by index if needed
                                        checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                                    }

                                    @Override
                                    public void onDataLoaded(List<RailCommonData> orderedData) {

                                    }
                                    @Override
                                    public void onFailure(Throwable throwable) {
                                        commonApiCallBack.onFailure(new Throwable("ASSET NOT FOUND"));
                                        checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                                    }

                                    @Override
                                    public void onFinish() {

                                    }
                                });
                            }
                        }

                        @Override
                        public void onDataLoaded(List<RailCommonData> orderedData) {

                        }
                        @Override
                        public void onFailure(Throwable throwable) {
                            commonApiCallBack.onFailure(new Throwable("ASSET NOT FOUND"));
                            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                        }

                        @Override
                        public void onFinish() {

                        }
                    });
                } else {
                    checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                }

            });
        } else {
            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
        }
    }
    private void injectYouMayLikeRail(Activity activity, int contentSize, BaseCategory screenWidget, CommonApiCallBack commonApiCallBack, int index, AtomicInteger completedRequests, int totalRequests) {
        if (preference == null)
            preference = KsPreferenceKeys.getInstance();
        String isLogin = preference.getAppPrefLoginStatus();
        if (isLogin.equalsIgnoreCase(AppConstants.UserStatus.Login.toString())) {
            BookmarkingViewModel bookmarkingViewModel = new ViewModelProvider((FragmentActivity) activity).get(BookmarkingViewModel.class);
            bookmarkingViewModel.getWatchHistory(preference.getAppPrefAccessToken(),0,40).observe((LifecycleOwner) activity,response->{
                String videoIds = "";
                if (response != null && response.getData() != null){
                    List<ItemsItem> list=response.getData().getItems();
                    for (ItemsItem item:list){
                        videoIds = videoIds.concat(String.valueOf(item.getAssetId())).concat(",");
                    }
                    ContinueWatchingLayer.getInstance().getWatchHistoryVideos(list, videoIds, new CommonApiCallBack() {
                        @Override
                        public void onSuccess(Object item) {
                            if (item instanceof List) {
                                ArrayList<DataItem> enveuVideoDetails = (ArrayList<DataItem>) item;
                                String genres=getGenresFromList(enveuVideoDetails);
                                APIServiceLayer.getInstance().getListForRail(genres,0,40).observe((LifecycleOwner) activity, artistListResponse -> {
                                    if (artistListResponse != null && artistListResponse.getData() != null){
                                        List<EnveuVideoItemBean> enveuItem = new ArrayList<EnveuVideoItemBean>();
                                        // add item enveuVideoDetails in enveuItem List
                                        Gson gson=new Gson();
                                        RailCommonData railCommonData = new RailCommonData();
                                        for (com.enveu.beanModelV3.videoDetailV3.list.DataItem dataItem:artistListResponse.getData().getItems()){
                                            String json=gson.toJson(dataItem);
                                            DataItem item1=gson.fromJson(json,DataItem.class);
                                            EnveuVideoItemBean enveuVideoItemBean=new EnveuVideoItemBean(item1);
                                            enveuItem.add(enveuVideoItemBean);
                                        }
                                        railCommonData.setEnveuVideoItemBeans(enveuItem);
                                        if (railCommonData.getEnveuVideoItemBeans().size()>0){
                                            railDataMap.put(index,railCommonData);
                                            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                                        }else {
                                            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                                        }
                                    }else {
                                        checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                                    }
                                });
                            }else {
                                checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                            }
                        }

                        @Override
                        public void onDataLoaded(List<RailCommonData> orderedData) {

                        }

                        @Override
                        public void onFailure(Throwable throwable) {
                            commonApiCallBack.onFailure(new Throwable("ASSET NOT FOUND"));
                            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
                        }

                        @Override
                        public void onFinish() {

                        }
                    });
                }
            });
        }else {
            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests);
        }
    }

    private String getGenresFromList(ArrayList<DataItem> enveuVideoDetails) {
        String geners="";
        for (DataItem item:enveuVideoDetails){
            if (item.getCustomData().getGenres() != null) {
                for (String gener : item.getGenres()) {
                    geners = geners.concat(gener).concat(",");
                }
            }
        }
        return geners;
    }

    private List<ContinueWatchingBookmark> removeDuplicates(List<ContinueWatchingBookmark> continueWatchingBookmarkList) {
        List<ContinueWatchingBookmark> noRepeat = new ArrayList<>();
        List<ContinueWatchingBookmark> duplicateRepeat = new ArrayList<>();
        try {
            for (ContinueWatchingBookmark event : continueWatchingBookmarkList) {
                boolean isFound = false;
                // check if the event name exists in noRepeat
                for (ContinueWatchingBookmark e : noRepeat) {
                    if (e.getAssetId().equals(event.getAssetId()) || (e.equals(event))) {
                        isFound = true;
                        break;
                    }
                }
                if (!isFound) noRepeat.add(event);
            }
        } catch (Exception ignored) {

        }


        return noRepeat;
    }


    public MutableLiveData<RailCommonData> getPlayListDetailsWithPagination(String playlistID, String accessToken, int pageNumber, int pageSize, BaseCategory screenWidget) {
        return APIServiceLayer.getInstance().getSearchPopularPlayList(playlistID, accessToken, pageNumber, pageSize, screenWidget);
    }

    public LiveData<List<RailCommonData>> getSearch(String keyword, int size, int page,boolean isV2Search,boolean isMusicApp) {
        return SearchLayer.getInstance().getSearchData(keyword, size, page,isV2Search,isMusicApp);
    }
    @NotNull
    public LiveData<List<RailCommonData>> getSearchForMusicContents(@Nullable String searchKeyword, int size, int page, @NotNull String assetType) {
        return SearchLayer.getInstance().getSearchData(searchKeyword, size, page,assetType);
    }
    public LiveData<RailCommonData> getSearchSingleCategory(String keyword, String type, int size, int page, boolean applyFilter,String customContentType,String videoType,String header) {
        return SearchLayer.getInstance().getSingleCategorySearch(keyword, type, size, page, applyFilter,customContentType,videoType,header);
    }

    public LiveData<RailCommonData> getSearchProgram(String keyword, int size, int page, boolean applyFilter) {
        return SearchLayer.getInstance().getProgramSearch(keyword, size, page, applyFilter);
    }

    public LiveData<ResponseGetIsWatchlist> hitApiIsWatchList(String token, int seriesId) {
        return HomeFragmentRepository.getInstance().hitApiIsToWatchList(token, seriesId);
    }

    public LiveData<ResponseEmpty> hitApiAddWatchList(String token, int seriesId) {
        return HomeFragmentRepository.getInstance().hitApiAddToWatchList(token, seriesId);
    }

    public LiveData<ResponseEmpty> hitApiAddWatchHistory(String token, int assetId) {
        return HomeFragmentRepository.getInstance().hitApiAddToWatchHistory(token, assetId);
    }


    public LiveData<ResponseIsLike> hitApiIsLike(String token, int assetId) {
        return HomeFragmentRepository.getInstance().hitApiIsLike(token, assetId);
    }

    public LiveData<ResponseEmpty> hitApiDeleteLike(String token, int assetId) {
        return HomeFragmentRepository.getInstance().hitApiDeleteLike(token, assetId);
    }

    public LiveData<ResponseEmpty> hitRemoveWatchlist(String token, int assetId) {
        return HomeFragmentRepository.getInstance().hitRemoveWatchlist(token, assetId);
    }

    public LiveData<ResponseEmpty> hitApiAddLike(String token, int assetId) {
        return HomeFragmentRepository.getInstance().hitApiAddLike(token, assetId);
    }


    public LiveData<RailCommonData> getWatchHistoryAssets(List<ItemsItem> watchHistoryList, String videoIds,Boolean squareRailType) {
        MutableLiveData<RailCommonData> railCommonDataMutableLiveData = new MutableLiveData<>();

        ContinueWatchingLayer.getInstance().getWatchHistoryVideos(watchHistoryList, videoIds, new CommonApiCallBack() {
            @Override
            public void onSuccess(Object item) {
                if (item instanceof List) {
                    ArrayList<DataItem> enveuVideoDetails = (ArrayList<DataItem>) item;
                    RailCommonData railCommonData = new RailCommonData();
                    railCommonData.setWatchHistoryData(enveuVideoDetails,squareRailType, new CommonApiCallBack() {
                        @Override
                        public void onSuccess(Object item) {
                            railCommonDataMutableLiveData.postValue(railCommonData);
                        }

                        @Override
                        public void onDataLoaded(List<RailCommonData> orderedData) {

                        }

                        @Override
                        public void onFailure(Throwable throwable) {
                            railCommonDataMutableLiveData.postValue(null);
                        }

                        @Override
                        public void onFinish() {
                        }
                    });
                } else {
                    railCommonDataMutableLiveData.postValue(null);
                }
            }

            @Override
            public void onDataLoaded(List<RailCommonData> orderedData) {

            }
            @Override
            public void onFailure(Throwable throwable) {
                railCommonDataMutableLiveData.postValue(null);
            }

            @Override
            public void onFinish() {
            }
        });
        return railCommonDataMutableLiveData;
    }

    public LiveData<JsonObject> hitLogout(boolean session, String token) {
        return RegistrationLoginRepository.getInstance().hitApiLogout(session, token);
    }


    public LiveData<ResponseModel> getCatData(Activity activity, String screenId) {
        MutableLiveData<ResponseModel> liveData = new MutableLiveData<>();
        HomeFragmentRepository homeFragmentRepository = new HomeFragmentRepository();

        homeFragmentRepository.getCat(screenId, new ApiResponseModel<BaseCategory>() {
            @Override
            public void onStart() {
                liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(List<BaseCategory> t) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), t, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });
        return liveData;
    }

    public MutableLiveData<ResponseModel> getPlayListDetailsWithPaginationV2(String playlistID,
                                                                             int pageNumber, int pageSize,
                                                                             BaseCategory screenWidget) {
        MutableLiveData liveData = new MutableLiveData();
        ListPaginationDataLayer.getInstance().getPlayListByWithPagination(playlistID, pageNumber, pageSize, screenWidget, new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {
                liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(RailCommonData response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });

        return liveData;
    }

    public LiveData<ResponseModel> getSeriesDetailsV2(String asseetID ,boolean isIntentFromExpedition) {
        MutableLiveData liveData = new MutableLiveData();
        SeriesDataLayer.getInstance().getSeriesData(asseetID,isIntentFromExpedition, new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {
                liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(RailCommonData response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });

        return liveData;
    }

    public LiveData<Response> getIsLikeGOI(String token, int pageNo, int size) {
        MutableLiveData<Response> liveData = new MutableLiveData();
         SeasonEpisodesList.getInstance().getIsLikeGOI(token, pageNo, size, new ApiLikeList() {
             @Override
             public void onSuccess(boolean isStatus, Response likeListResponse) {
                 liveData.postValue(likeListResponse);
             }

             @Override
             public void onFailure(boolean isStatus, String errorCode, String errorMessage) {
                 liveData.postValue(null);
             }
         });
        return liveData;
    }


    public LiveData<ResponseModel> getSeasonEpisodesV2(int seriesId, int pageNo, int size,String customData) {
        MutableLiveData liveData = new MutableLiveData();
           new SeasonEpisodesList().getSeasonEpisodesV2(seriesId, pageNo, size,customData, new ApiResponseModel<RailCommonData>() {
                @Override
                public void onStart() {
                    liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
                }

                @Override
                public void onSuccess(RailCommonData response) {
                    Log.d("DetailPage","at getSeasonEpisodesV2, success");
                    liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
                }

                @Override
                public void onError(ApiErrorModel apiError) {
                    Log.d("DetailPage","at getSeasonEpisodesV2, error");
                    liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
                }

                @Override
                public void onFailure(ApiErrorModel httpError) {
                    Log.d("DetailPage","at getSeasonEpisodesV2, failure");
                    liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
                }
            });

        return liveData;
    }
    public LiveData<ResponseModel> getRoundForSeason(int seriesId, int pageNo, int size,String customData) {
        MutableLiveData liveData = new MutableLiveData();
           new SeasonEpisodesList().getRoundForSeason(seriesId, pageNo, size,customData, new ApiResponseModel<RailCommonData>() {
                @Override
                public void onStart() {
                    liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
                }

                @Override
                public void onSuccess(RailCommonData response) {
                    liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
                }

                @Override
                public void onError(ApiErrorModel apiError) {
                    liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
                }

                @Override
                public void onFailure(ApiErrorModel httpError) {
                    liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
                }
            });

        return liveData;
    }
    public LiveData<ResponseModel> getMatchForRound(int seriesId, int pageNo, int size,String customData) {
        MutableLiveData liveData = new MutableLiveData();
           new SeasonEpisodesList().getMatchForRound(seriesId, pageNo, size,customData, new ApiResponseModel<RailCommonData>() {
                @Override
                public void onStart() {
                    liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
                }

                @Override
                public void onSuccess(RailCommonData response) {
                    liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
                }

                @Override
                public void onError(ApiErrorModel apiError) {
                    liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
                }

                @Override
                public void onFailure(ApiErrorModel httpError) {
                    liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
                }
            });

        return liveData;
    }
    public LiveData<ResponseModel> getEpisodeFromSeason(int seasonId, int pageNo, int size) {
        MutableLiveData liveData = new MutableLiveData();
        new SeasonEpisodesList().getAllEpisodesV2(seasonId, pageNo, size, new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {
                liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(RailCommonData response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });


        return liveData;
    }


    public LiveData<ResponseModel> getLiveEventMatch(int matchId, int pageNo, int size) {
        MutableLiveData liveData = new MutableLiveData();
        new SeasonEpisodesList().getLiveEventMatch(matchId, pageNo, size, new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {
                liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(RailCommonData response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });


        return liveData;
    }


    public LiveData<ResponseModel> getAssetDetailsV2(String asseetID, Context context) {
        MutableLiveData liveData = new MutableLiveData();
        VideoDetailLayer.getInstance().getVideoDetails(asseetID, new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {
                liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(RailCommonData response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });

        return liveData;

    }

    public LiveData<ResponseModel> getAssetDetailsV3(String asseetID) {
        MutableLiveData liveData = new MutableLiveData();
        VideoDetailLayer.getInstance().getVideoDetailsV3(asseetID, new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {
                liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(RailCommonData response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });

        return liveData;

    }

    public LiveData<ResponseModel> getRelatedContentV3(int id, int pageNumber, int pageSize, String contentType) {
        MutableLiveData liveData = new MutableLiveData();
        VideoDetailLayer.getInstance().getRelatedContentV3(id, pageNumber, pageSize, contentType, new ApiResponseModel<EnvSongDetail>() {
            @Override
            public void onStart() {
                liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));

            }

            @Override
            public void onSuccess(EnvSongDetail response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));

            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });

        return liveData;

    }

    public LiveData<ResponseModel> getMediaContentList(String customData,int pageNumber,int pageSize) {
        MutableLiveData liveData = new MutableLiveData();
        VideoDetailLayer.getInstance().getEnvMediaContentList(customData,pageNumber,pageSize, new ApiResponseModel<EnvMediaContentList>() {
            @Override
            public void onStart() {
                liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(EnvMediaContentList response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });

        return liveData;

    }

    public LiveData<ResponseModel> getAssetDetailsbySlug(String contentSlug) {
        MutableLiveData liveData = new MutableLiveData();
        VideoDetailLayer.getInstance().getVideoDetailsBySlug(contentSlug, new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {
                liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(RailCommonData response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });

        return liveData;

    }

    public LiveData<ResponseModel> getRelatedContent(int pageNumber, int size,String contentType,int id) {
        MutableLiveData liveData = new MutableLiveData();
       new  SeasonEpisodesList().getRelatedContent(pageNumber,size,contentType,id, new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {
             //   liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(RailCommonData response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });

        return liveData;
    }

    public LiveData<ResponseModel> getRecommendedSongs(int pageNumber, int size,String contentType,String sortBy) {
        MutableLiveData liveData = new MutableLiveData();
        new  SeasonEpisodesList().getRecommendedSongs(pageNumber,size,contentType,sortBy, new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {
                //   liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(RailCommonData response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });

        return liveData;
    }


    public LiveData<ResponseModel> getCommonListAll(int pageNumber, int size,String interViewAssetId,String customData ) {
        MutableLiveData liveData = new MutableLiveData();
        new SeasonEpisodesList().getCommonListAll(pageNumber,size,interViewAssetId,customData,new ApiResponseModel<RailCommonData>() {
            @Override
            public void onStart() {
              //  liveData.postValue(new ResponseModel(APIStatus.START.name(), null, null));
            }

            @Override
            public void onSuccess(RailCommonData response) {
                liveData.postValue(new ResponseModel(APIStatus.SUCCESS.name(), response, null));
            }

            @Override
            public void onError(ApiErrorModel apiError) {
                liveData.postValue(new ResponseModel(APIStatus.ERROR.name(), null, apiError));
            }

            @Override
            public void onFailure(ApiErrorModel httpError) {
                liveData.postValue(new ResponseModel(APIStatus.FAILURE.name(), null, httpError));
            }
        });

        return liveData;
    }

    public LiveData<ResponseEntitle> hitApiEntitlement(String token, String sku) {
        return Objects.requireNonNull(EntitlementLayer.getInstance()).hitApiEntitlement(token, sku);
    }

    public LiveData<DRM> externalRefID (String token , String Sku){
        return  EntitlementLayer.getInstance().hitAPIForREfID(token, Sku);
    }

    public LiveData<List<RailCommonData>> getAllArtist(String keyword, int size, int page) {
            return SearchLayer.getInstance().getAllArtist(keyword, size, page);
    }

    public LiveData<List<RailCommonData>> hitGenreData(Context context, String contentType, String customType, int offSet, int size, String trackEvent, String locale) {
        return SearchLayer.getInstance().hitGenreData(context, contentType, customType, offSet, size, trackEvent, locale);
    }

    public LiveData<List<RailCommonData>> hitGenreDataFilter(Context context, String contentType, String customType,String id,String keyword , int offSet, int size, String trackEvent,String personType, String locale) {
        return SearchLayer.getInstance().hitGenreDataFilter(context, contentType, customType,id,keyword, offSet, size, trackEvent,personType, locale);
    }

    public LiveData<AddToQueueResponse> addToQueueApi(AddToQueueRequestModel addToQueueRequestModel) {
       return APIServiceLayer.getInstance().addToQueueApi(addToQueueRequestModel);
    }

    public LiveData<GetQueueResponse> allGetQueueListApi(String type) {
        return APIServiceLayer.getInstance().allGetQueueListApi(type);
    }

    public LiveData<ArtistListResponse> getAssetDetailsV3Api(String assetId, int pageNumber, int pageSize) {
       return APIServiceLayer.getInstance().getAssetDetailsV3Api(assetId, pageNumber, pageSize);
    }

    public LiveData<ArtistListResponse> getAssetDetailsV3Api(String assetId, int pageNumber, int pageSize, String genresId) {
        return APIServiceLayer.getInstance().getAssetDetailsV3Api(assetId, pageNumber, pageSize, genresId);
    }
    public LiveData<RemoveQueueResponse> removeAllQueueApi() {
        return APIServiceLayer.getInstance().removeAllQueueApi();
    }
    public LiveData<SearchGenres> getSearchGenres(String customType , String contentType) {
        return APIServiceLayer.getInstance().getSearchGenres(contentType ,customType );
    }

    public LiveData<UserTrackingResponse> getSponsorUserTracking(JsonObject jsonObject,String token) {
        return APIServiceLayer.getInstance().getSponsorUserTracking(jsonObject,token);
    }

    private String getGenresItems() {
        if (Objects.equals(genresItems, "")){
            KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
            if (preference.isActiveUserProfileData() != null) {
                if (preference.isActiveUserProfileData().getPreferenceSettings() != null) {
                    if (Objects.requireNonNull(preference.isActiveUserProfileData().getPreferenceSettings()).getGenresIds() != null){
                        List<Integer> genreIds = Objects.requireNonNull(preference.isActiveUserProfileData().getPreferenceSettings()).getGenresIds();
                        if (genreIds != null && !genreIds.isEmpty()) {
                            StringBuilder genresBuilder = new StringBuilder();
                            for (int i = 0; i < genreIds.size(); i++) {
                                if (i > 0) {
                                    genresBuilder.append(",");
                                }
                                genresBuilder.append(genreIds.get(i));
                            }
                            genresItems = genresBuilder.toString();
                        }
                    }
                }
            }
        }
        return genresItems;
    }

}
