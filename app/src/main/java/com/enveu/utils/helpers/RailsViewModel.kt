package com.enveu.utils.helpers

import android.app.Activity
import android.content.Context
import android.util.Log
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.enveu.Bookmarking.BookmarkingViewModel
import com.enveu.ObservableRxList
import com.enveu.activities.layers.EntitlementLayer.Companion.instance
import com.enveu.beanModel.drm.DRM
import com.enveu.beanModel.emptyResponse.ResponseEmpty
import com.enveu.beanModel.entitle.ResponseEntitle
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModel.responseGetWatchlist.ResponseGetIsWatchlist
import com.enveu.beanModel.responseIsLike.ResponseIsLike
import com.enveu.beanModel.sponsorUserTracking.UserTrackingResponse
import com.enveu.beanModelV3.continueWatching.DataItem
import com.enveu.beanModelV3.playListModelV2.EnveuCommonResponse
import com.enveu.beanModelV3.searchGenres.SearchGenres
import com.enveu.beanModelV3.videoDetailV3.mediaContentList.ArtistListResponse
import com.enveu.beanModelV3.videoDetailV3.queue_response.AddToQueueRequestModel
import com.enveu.beanModelV3.videoDetailV3.queue_response.AddToQueueResponse
import com.enveu.beanModelV3.videoDetailV3.queue_response.GetQueueResponse
import com.enveu.beanModelV3.videoDetailV3.queue_response.RemoveQueueResponse
import com.enveu.callbacks.apicallback.ApiResponseModel
import com.enveu.callbacks.commonCallbacks.CommonApiCallBack
import com.enveu.client.api_callback.NetworkResultCallback
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory
import com.enveu.client.bookmarking.bean.continuewatching.ContinueWatchingBookmark
import com.enveu.client.bookmarking.bean.continuewatching.GetContinueWatchingBean
import com.enveu.client.enums.Layouts
import com.enveu.client.enums.PlaylistType
import com.enveu.client.playlist.beanv2_0.EnvPlaylistContents
import com.enveu.client.watchHistory.beans.ItemsItem
import com.enveu.client.watchHistory.beans.ResponseWatchHistoryAssetList
import com.enveu.layersV2.ContinueWatchingLayer
import com.enveu.layersV2.SearchLayer
import com.enveu.networking.apistatus.APIStatus
import com.enveu.networking.errormodel.ApiErrorModel
import com.enveu.networking.responsehandler.ResponseModel
import com.enveu.networking.servicelayer.APIServiceLayer
import com.enveu.repository.home.HomeFragmentRepository
import com.enveu.repository.userManagement.RegistrationLoginRepository
import com.enveu.utils.Logger
import com.enveu.utils.ObjectHelper
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.google.gson.Gson
import com.google.gson.JsonObject
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import retrofit2.Response
import java.util.Collections
import java.util.Objects
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger


class RailsViewModel() : ViewModel() {
    private val mutableRailCommonData = MutableLiveData<RailCommonData>()
    private val observableList = ObservableRxList<RailCommonData>()
    private var i = 0
    private var baseCategories: List<BaseCategory>? = null
    private var preference: KsPreferenceKeys? = null
    private val isLogin: String? = null
    private val entittleStatus = false
    var genresItems: String = ""


    fun getScreenWidgets(
        activity: Activity?,
        screenId: String?,
        tag: Boolean?,
        commonApiCallBack: CommonApiCallBack,
    ) {
        if (screenId != null) {
                APIServiceLayer.getInstance().getCategories(screenId).observe((activity as LifecycleOwner?)!!) { baseCategoriesList: List<BaseCategory>? ->
                        baseCategories = baseCategoriesList
                        val gson = Gson()
                        val json = gson.toJson(baseCategories)
                        Log.w("screenData-->>", json)
                        i = 0
                        if (ObjectHelper.isNotEmpty(baseCategories)) {
                            getScreenListing(activity, commonApiCallBack)
                        } else {
                            commonApiCallBack.onFailure(Throwable("No Data"))
                        }
                }
            }
        }

    private val railDataMap = ConcurrentHashMap<Int, RailCommonData>()

    private fun getScreenListing(activity: Activity?, commonApiCallBack: CommonApiCallBack) {
        val completedRequests = AtomicInteger(0)
        if ((baseCategories?.size ?: 0) > 0) {
            val totalRequests = baseCategories!!.size
            for (index in baseCategories!!.indices) {
                val screenWidget = baseCategories!![index]
                var type: String? = ""
                if (screenWidget.type != null) type = screenWidget.type
                if (type.equals(AppConstants.WIDGET_TYPE_CONTENT, ignoreCase = true)) {
                    getRailDetails(
                        activity,
                        screenWidget,
                        commonApiCallBack,
                        index,
                        completedRequests,
                        totalRequests
                    )
                } else if (type.equals(AppConstants.WIDGET_TYPE_AD, ignoreCase = true)) {
                    getAdsDetails(
                        activity,
                        screenWidget,
                        commonApiCallBack,
                        index,
                        completedRequests,
                        totalRequests
                    )
                }
            }
        } else {
            commonApiCallBack.onFinish()
        }
    }

    private fun checkAndFinishIfComplete(
        commonApiCallBack: CommonApiCallBack,
        completedRequests: AtomicInteger,
        totalRequests: Int,
    ) {
        if (completedRequests.incrementAndGet() == totalRequests) {
            // Iterate through railDataMap in order and update UI
            for (i in 0 until totalRequests) {
                val railData = railDataMap[i]
                if (railData != null) {
                    commonApiCallBack.onSuccess(railData) // Display the result in sequence
                }
            }
            commonApiCallBack.onFinish()
        }
    }


    private fun getAdsDetails(
        activity: Activity?,
        screenWidget: BaseCategory,
        commonApiCallBack: CommonApiCallBack,
        index: Int,
        completedRequests: AtomicInteger,
        totalRequests: Int,
    ) {
        if (!KsPreferenceKeys.getInstance().entitlementStatus) {
            val railCommonData = RailCommonData(screenWidget, 0)
            railCommonData.setIsAd(true)
            commonApiCallBack.onSuccess(railCommonData)
            railDataMap[index] = railCommonData // Store data by index if needed
            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests)
        }
    }


    private fun getRailDetails(
        activity: Activity?,
        screenWidget: BaseCategory,
        commonApiCallBack: CommonApiCallBack,
        index: Int,
        completedRequests: AtomicInteger,
        totalRequests: Int,
    ) {
        if (screenWidget.layout != null && screenWidget.layout.equals(
                Layouts.HRO.name,
                ignoreCase = true
            )
        ) {
            getHeroDetails(
                activity,
                screenWidget,
                commonApiCallBack,
                index,
                completedRequests,
                totalRequests
            )
        } else {
            if (screenWidget.contentPlayListType != null) {
                val contentType = screenWidget.contentPlayListType
                if (contentType.equals(
                        PlaylistType.BVC.name,
                        ignoreCase = true
                    ) || contentType.equals(PlaylistType.EN_OVP.name, ignoreCase = true)
                ) {
                    getPlaylistDetailsById(
                        activity,
                        screenWidget,
                        commonApiCallBack,
                        index,
                        completedRequests,
                        totalRequests
                    )
                } else if (contentType.equals(PlaylistType.K_PDF.name, ignoreCase = true)) {
                    // Get Playlist data from Predefined Kaltura
                } else if (contentType.equals(PlaylistType.KTM.name, ignoreCase = true)) {
                    // Get Playlist data from Kaltura
                }
            }
        }
    }


    private fun getHeroDetails(
        activity: Activity?,
        screenWidget: BaseCategory,
        commonApiCallBack: CommonApiCallBack,
        index: Int,
        completedRequests: AtomicInteger,
        totalRequests: Int,
    ) {
        val railCommonData = RailCommonData()
        railCommonData.getHeroRailCommonData(screenWidget, activity, object : CommonApiCallBack {
            override fun onSuccess(item: Any) {
                railDataMap[index] = railCommonData // Store data by index if needed
                checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests)
            }

            override fun onDataLoaded(orderedData: MutableList<RailCommonData>?) {
                TODO("Not yet implemented")
            }

            override fun onFailure(throwable: Throwable) {
                commonApiCallBack.onFailure(Throwable("ASSET NOT FOUND"))
                checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests)
            }

            override fun onFinish() {
            }
        })
    }


    private fun getPlaylistDetailsById(
        activity: Activity?,
        screenWidget: BaseCategory,
        commonApiCallBack: CommonApiCallBack,
        index: Int,
        completedRequests: AtomicInteger,
        totalRequests: Int,
    ) {
        var contentSize = 0
        if (screenWidget.contentSize != null) {
            contentSize = screenWidget.contentSize!!
        }
        if (screenWidget.name != null && screenWidget.referenceName != null && (screenWidget.referenceName.equals(
                "special_playlist",
                ignoreCase = true
            ) || screenWidget.referenceName.equals(
                AppConstants.ContentType.CONTINUE_WATCHING.name,
                ignoreCase = true
            ))
        ) {
            injectContinueWatchingRail(
                activity,
                contentSize,
                screenWidget,
                commonApiCallBack,
                index,
                completedRequests,
                totalRequests
            )
        } else if (screenWidget.name != null && screenWidget.referenceName != null && screenWidget.referenceName.equals(
                AppConstants.ContentType.MY_WATCHLIST.name,
                ignoreCase = true
            )
        ) {
            injectWatchlistRail(
                activity,
                contentSize,
                screenWidget,
                commonApiCallBack,
                index,
                completedRequests,
                totalRequests
            )
        } else {
            APIServiceLayer.getInstance()
                .getPlayListById(
                    screenWidget.contentID,
                    KsPreferenceKeys.getInstance().appPrefAccessToken,
                    0,
                    contentSize,
                    getGenresListItems(),
                    object : NetworkResultCallback<EnvPlaylistContents> {
                        override fun loading(isLoading: Boolean) {

                        }

                        override fun failure(status: Boolean, errorCode: Int, message: String) {
                            Logger.w("API Failed, Error Code: $errorCode message: $message")
                            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests)
                        }

                        override fun success(status: Boolean?, response: Response<EnvPlaylistContents>?) {
                            val gson = Gson()
                            val json = gson.toJson(response!!.body())
                            Log.w("playlistCall-->>>>>", json)
                            val enveuCommonResponse = gson.fromJson(json, EnveuCommonResponse::class.java)
                            if (enveuCommonResponse != null) {
                                CoroutineScope(Dispatchers.Main).launch {
                                    val railCommonData = RailCommonData(enveuCommonResponse.data, screenWidget, false,0)
                                    railDataMap[index] = railCommonData // Store data by index
                                    // commonApiCallBack.onSuccess(railCommonData);
                                }
                            }
                            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests)
                        }
                    })
//                .observe((activity as LifecycleOwner?)!!) { enveuCommonResponse: EnveuCommonResponse? ->
//                    if (enveuCommonResponse != null) {
//                        CoroutineScope(Dispatchers.Main).launch {
//                            val railCommonData = RailCommonData(enveuCommonResponse.data, screenWidget, false,0)
//                            railDataMap[index] = railCommonData // Store data by index
//                            // commonApiCallBack.onSuccess(railCommonData);
//                        }
//                    }
//                    checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests)
//                }
        }
    }

    private fun injectWatchlistRail(
        activity: Activity?,
        contentSize: Int,
        screenWidget: BaseCategory,
        commonApiCallBack: CommonApiCallBack,
        index: Int,
        completedRequests: AtomicInteger,
        totalRequests: Int,
    ) {
        if (preference == null) preference = KsPreferenceKeys.getInstance()
        val isLogin = preference!!.appPrefLoginStatus
        if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            val token = preference!!.appPrefAccessToken
            val bookmarkingViewModel = ViewModelProvider((activity as FragmentActivity?)!!).get(
                BookmarkingViewModel::class.java
            )
            bookmarkingViewModel.getMywatchListData(token, 0, contentSize).observe(
                (activity as LifecycleOwner?)!!
            ) { getContinueWatchingBean: ResponseWatchHistoryAssetList? ->
                var videoIds = ""
                if (getContinueWatchingBean != null && getContinueWatchingBean.data != null) {
                    val continueWatchingBookmarkList =
                        getContinueWatchingBean.data.items
                    for (continueWatchingBookmark in continueWatchingBookmarkList
                    ) {
                        videoIds = videoIds + continueWatchingBookmark.assetId
                            .toString() + ","
                    }
                    ContinueWatchingLayer.getInstance().getWatchHistoryVideos(
                        continueWatchingBookmarkList,
                        videoIds,
                        object : CommonApiCallBack {
                            override fun onSuccess(item: Any) {
                                if (item is List<*>) {
                                    val enveuVideoDetails =
                                        item as ArrayList<DataItem>
                                    val railCommonData = RailCommonData()
                                    railCommonData.setContinueWatchingData(
                                        screenWidget,
                                        false,
                                        enveuVideoDetails,
                                        object : CommonApiCallBack {
                                            override fun onSuccess(item: Any) {
                                                railDataMap[index] =
                                                    railCommonData // Store data by index if needed
                                                checkAndFinishIfComplete(
                                                    commonApiCallBack,
                                                    completedRequests,
                                                    totalRequests
                                                )
                                            }

                                            override fun onDataLoaded(orderedData: MutableList<RailCommonData>?) {
                                                TODO("Not yet implemented")
                                            }

                                            override fun onFailure(throwable: Throwable) {
                                                commonApiCallBack.onFailure(Throwable("ASSET NOT FOUND"))
                                                checkAndFinishIfComplete(
                                                    commonApiCallBack,
                                                    completedRequests,
                                                    totalRequests
                                                )
                                            }

                                            override fun onFinish() {
                                            }
                                        })
                                }
                            }

                            override fun onDataLoaded(orderedData: MutableList<RailCommonData>?) {
                                TODO("Not yet implemented")
                            }

                            override fun onFailure(throwable: Throwable) {
                                commonApiCallBack.onFailure(Throwable("ASSET NOT FOUND"))
                                checkAndFinishIfComplete(
                                    commonApiCallBack,
                                    completedRequests,
                                    totalRequests
                                )
                            }

                            override fun onFinish() {
                            }
                        })
                } else {
                    checkAndFinishIfComplete(
                        commonApiCallBack,
                        completedRequests,
                        totalRequests
                    )
                }
            }
        } else {
            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests)
        }
    }


    // Helper method to handle completion logic
    private fun injectContinueWatchingRail(
        activity: Activity?,
        contentSize: Int,
        screenWidget: BaseCategory,
        commonApiCallBack: CommonApiCallBack,
        index: Int,
        completedRequests: AtomicInteger,
        totalRequests: Int,
    ) {
        if (preference == null) preference = KsPreferenceKeys.getInstance()
        val isLogin = preference!!.appPrefLoginStatus
        if (isLogin.equals(AppConstants.UserStatus.Login.toString(), ignoreCase = true)) {
            val token = preference!!.appPrefAccessToken
            val bookmarkingViewModel = ViewModelProvider((activity as FragmentActivity?)!!).get(
                BookmarkingViewModel::class.java
            )
            bookmarkingViewModel.getContinueWatchingData(token, 0, contentSize).observe(
                (activity as LifecycleOwner?)!!
            ) { getContinueWatchingBean: GetContinueWatchingBean? ->
                var videoIds = ""
                if (getContinueWatchingBean != null && getContinueWatchingBean.data != null) {
                    val continueWatchingBookmarkLists =
                        getContinueWatchingBean.data.continueWatchingBookmarks
                    val continueWatchingBookmarkList =
                        removeDuplicates(continueWatchingBookmarkLists)
                    for (continueWatchingBookmark in continueWatchingBookmarkList) {
                        videoIds = videoIds + continueWatchingBookmark.assetId
                            .toString() + ","
                    }
                    ContinueWatchingLayer.getInstance().getContinueWatchingVideos(
                        continueWatchingBookmarkList,
                        videoIds,
                        object : CommonApiCallBack {
                            override fun onSuccess(item: Any) {
                                if (item is List<*>) {
                                    val enveuVideoDetails =
                                        item as ArrayList<DataItem>

                                    // create new List type Array
                                    val enveuItem: MutableList<DataItem> =
                                        ArrayList()
                                    // add item enveuVideoDetails in enveuItem List
                                    enveuItem.addAll(enveuVideoDetails)
                                    // sorting enveuItem List based peverious assetId
                                    Collections.sort(
                                        enveuItem
                                    )
                                    // stored item new stringArrayList Array
                                    val stringArrayList =
                                        ArrayList<DataItem>()
                                    stringArrayList.addAll(enveuItem)
                                    val railCommonData = RailCommonData()
                                    railCommonData.setContinueWatchingData(
                                        screenWidget,
                                        true,
                                        stringArrayList,
                                        object : CommonApiCallBack {
                                            override fun onSuccess(item: Any) {
                                                railDataMap[index] =
                                                    railCommonData // Store data by index if needed
                                                checkAndFinishIfComplete(
                                                    commonApiCallBack,
                                                    completedRequests,
                                                    totalRequests
                                                )
                                            }

                                            override fun onDataLoaded(orderedData: List<RailCommonData?>?) {

                                            }

                                            override fun onFailure(throwable: Throwable) {
                                                commonApiCallBack.onFailure(Throwable("ASSET NOT FOUND"))
                                                checkAndFinishIfComplete(
                                                    commonApiCallBack,
                                                    completedRequests,
                                                    totalRequests
                                                )
                                            }

                                            override fun onFinish() {
                                            }
                                        })
                                }
                            }
                            override fun onDataLoaded(orderedData: List<RailCommonData?>?) {

                            }
                            override fun onFailure(throwable: Throwable) {
                                commonApiCallBack.onFailure(Throwable("ASSET NOT FOUND"))
                                checkAndFinishIfComplete(
                                    commonApiCallBack,
                                    completedRequests,
                                    totalRequests
                                )
                            }

                            override fun onFinish() {
                            }
                        })
                } else {
                    checkAndFinishIfComplete(
                        commonApiCallBack,
                        completedRequests,
                        totalRequests
                    )
                }
            }
        } else {
            checkAndFinishIfComplete(commonApiCallBack, completedRequests, totalRequests)
        }
    }

    private fun removeDuplicates(continueWatchingBookmarkList: List<ContinueWatchingBookmark>): List<ContinueWatchingBookmark> {
        val noRepeat: MutableList<ContinueWatchingBookmark> = ArrayList()
        val duplicateRepeat: List<ContinueWatchingBookmark> = ArrayList()
        try {
            for (event in continueWatchingBookmarkList) {
                var isFound = false
                // check if the event name exists in noRepeat
                for (e in noRepeat) {
                    if (e.assetId == event.assetId || (e == event)) {
                        isFound = true
                        break
                    }
                }
                if (!isFound) noRepeat.add(event)
            }
        } catch (ignored: Exception) {
        }


        return noRepeat
    }


    fun getPlayListDetailsWithPagination(
        playlistID: String?,
        accessToken:String?,
        pageNumber: Int,
        pageSize: Int,
        screenWidget: BaseCategory?,
    ): MutableLiveData<RailCommonData> {
        return APIServiceLayer.getInstance()
            .getSearchPopularPlayList(playlistID, accessToken, pageNumber, pageSize, screenWidget)
    }

    fun getSearch(
        keyword: String?,
        size: Int,
        page: Int,
        isV2Search: Boolean,
        isMusicApp: Boolean,
    ): LiveData<List<RailCommonData>> {
        return SearchLayer.getInstance().getSearchData(keyword, size, page, isV2Search, isMusicApp)
    }

    fun getSearchForMusicContents(
        searchKeyword: String?,
        size: Int,
        page: Int,
        assetType: String,
    ): LiveData<List<RailCommonData>> {
        return SearchLayer.getInstance().getSearchData(searchKeyword, size, page, assetType)
    }

    fun getSearchSingleCategory(
        keyword: String?,
        type: String?,
        size: Int,
        page: Int,
        applyFilter: Boolean,
        customContentType: String?,
        videoType: String?,
        header: String?,
    ): LiveData<RailCommonData> {
        return SearchLayer.getInstance().getSingleCategorySearch(
            keyword,
            type,
            size,
            page,
            applyFilter,
            customContentType,
            videoType,
            header
        )
    }

    fun getSearchProgram(
        keyword: String?,
        size: Int,
        page: Int,
        applyFilter: Boolean,
    ): LiveData<RailCommonData> {
        return SearchLayer.getInstance().getProgramSearch(keyword, size, page, applyFilter)
    }

    fun hitApiIsWatchList(token: String?, seriesId: Int): LiveData<ResponseGetIsWatchlist> {
        return HomeFragmentRepository.getInstance().hitApiIsToWatchList(token, seriesId)
    }

    fun hitApiAddWatchList(token: String?, seriesId: Int): LiveData<ResponseEmpty> {
        return HomeFragmentRepository.getInstance().hitApiAddToWatchList(token, seriesId)
    }

    fun hitApiAddWatchHistory(token: String?, assetId: Int): LiveData<ResponseEmpty> {
        return HomeFragmentRepository.getInstance().hitApiAddToWatchHistory(token, assetId)
    }


    fun hitApiIsLike(token: String?, assetId: Int): LiveData<ResponseIsLike> {
        return HomeFragmentRepository.getInstance().hitApiIsLike(token, assetId)
    }

    fun hitApiDeleteLike(token: String?, assetId: Int): LiveData<ResponseEmpty> {
        return HomeFragmentRepository.getInstance().hitApiDeleteLike(token, assetId)
    }

    fun hitRemoveWatchlist(token: String?, assetId: Int): LiveData<ResponseEmpty> {
        return HomeFragmentRepository.getInstance().hitRemoveWatchlist(token, assetId)
    }

    fun hitApiAddLike(token: String?, assetId: Int): LiveData<ResponseEmpty> {
        return HomeFragmentRepository.getInstance().hitApiAddLike(token, assetId)
    }


    fun getWatchHistoryAssets(
        watchHistoryList: List<ItemsItem?>?,
        videoIds: String?,
        squareRailType: Boolean?,
    ): LiveData<RailCommonData?> {
        val railCommonDataMutableLiveData = MutableLiveData<RailCommonData?>()

        ContinueWatchingLayer.getInstance()
            .getWatchHistoryVideos(watchHistoryList, videoIds, object : CommonApiCallBack {
                override fun onSuccess(item: Any) {
                    if (item is List<*>) {
                        val enveuVideoDetails = item as ArrayList<DataItem>
                        val railCommonData = RailCommonData()
                        railCommonData.setWatchHistoryData(
                            enveuVideoDetails,
                            squareRailType,
                            object : CommonApiCallBack {
                                override fun onSuccess(item: Any) {
                                    railCommonDataMutableLiveData.postValue(railCommonData)
                                }

                                override fun onDataLoaded(orderedData: MutableList<RailCommonData>?) {
                                    TODO("Not yet implemented")
                                }

                                override fun onFailure(throwable: Throwable) {
                                    railCommonDataMutableLiveData.postValue(null)
                                }

                                override fun onFinish() {
                                }
                            })
                    } else {
                        railCommonDataMutableLiveData.postValue(null)
                    }
                }

                override fun onDataLoaded(orderedData: MutableList<RailCommonData>?) {
                    TODO("Not yet implemented")
                }
                override fun onFailure(throwable: Throwable) {
                    railCommonDataMutableLiveData.postValue(null)
                }

                override fun onFinish() {
                }
            })
        return railCommonDataMutableLiveData
    }

    fun hitLogout(session: Boolean, token: String?): LiveData<JsonObject> {
        return RegistrationLoginRepository.getInstance().hitApiLogout(session, token)
    }


    fun getCatData(activity: Activity?, screenId: String?): LiveData<ResponseModel<*>> {
        val liveData = MutableLiveData<ResponseModel<*>>()
        val homeFragmentRepository = HomeFragmentRepository()

        homeFragmentRepository.getCat(screenId, object : ApiResponseModel<BaseCategory?> {
            override fun onStart() {
                liveData.postValue(ResponseModel<Any?>(APIStatus.START.name, null, null))
            }

            override fun onSuccess(t: List<BaseCategory>) {
                liveData.postValue(ResponseModel<Any?>(APIStatus.SUCCESS.name, t, null))
            }

            override fun onError(apiError: ApiErrorModel) {
                liveData.postValue(ResponseModel<Any?>(APIStatus.ERROR.name, null, apiError))
            }

            override fun onFailure(httpError: ApiErrorModel) {
                liveData.postValue(ResponseModel<Any?>(APIStatus.FAILURE.name, null, httpError))
            }
        })
        return liveData
    }


    fun hitApiEntitlement(token: String?, sku: String?): LiveData<ResponseEntitle>? {
        return Objects.requireNonNull(instance)?.hitApiEntitlement(token, sku)
    }

    fun externalRefID(token: String?, Sku: String?): LiveData<DRM?> {
        return instance!!.hitAPIForREfID(token!!, Sku)
    }

    fun getAllArtist(keyword: String?, size: Int, page: Int): LiveData<List<RailCommonData>> {
        return SearchLayer.getInstance().getAllArtist(keyword, size, page)
    }

    fun hitGenreData(
        context: Context?,
        contentType: String?,
        customType: String?,
        offSet: Int,
        size: Int,
        trackEvent: String?,
        locale: String?,
    ): LiveData<List<RailCommonData>> {
        return SearchLayer.getInstance()
            .hitGenreData(context, contentType, customType, offSet, size, trackEvent, locale)
    }

    fun hitGenreDataFilter(
        context: Context?,
        contentType: String?,
        customType: String?,
        id: String?,
        keyword: String?,
        offSet: Int,
        size: Int,
        trackEvent: String?,
        personType: String?,
        locale: String?,
    ): LiveData<List<RailCommonData>> {
        return SearchLayer.getInstance().hitGenreDataFilter(
            context,
            contentType,
            customType,
            id,
            keyword,
            offSet,
            size,
            trackEvent,
            personType,
            locale
        )
    }

    fun addToQueueApi(addToQueueRequestModel: AddToQueueRequestModel?): LiveData<AddToQueueResponse> {
        return APIServiceLayer.getInstance().addToQueueApi(addToQueueRequestModel)
    }

    fun allGetQueueListApi(type: String?): LiveData<GetQueueResponse> {
        return APIServiceLayer.getInstance().allGetQueueListApi(type)
    }

    fun getAssetDetailsV3Api(
        assetId: String?,
        pageNumber: Int,
        pageSize: Int,
    ): LiveData<ArtistListResponse> {
        return APIServiceLayer.getInstance().getAssetDetailsV3Api(assetId, pageNumber, pageSize)
    }

    fun removeAllQueueApi(): LiveData<RemoveQueueResponse> {
        return APIServiceLayer.getInstance().removeAllQueueApi()
    }

    fun getSearchGenres(customType: String?, contentType: String?): LiveData<SearchGenres> {
        return APIServiceLayer.getInstance().getSearchGenres(contentType, customType)
    }

    fun getSponsorUserTracking(
        jsonObject: JsonObject?,
        token: String?,
    ): LiveData<UserTrackingResponse> {
        return APIServiceLayer.getInstance().getSponsorUserTracking(jsonObject, token)
    }

    private fun getGenresListItems(): String {
        if (genresItems == "") {
            val preference = KsPreferenceKeys.getInstance()
            if (preference.isActiveUserProfileData() != null) {
                if (preference.isActiveUserProfileData().preferenceSettings != null) {
                    if (Objects.requireNonNull(preference.isActiveUserProfileData().preferenceSettings)?.genresIds != null) {
                        val genreIds =
                            Objects.requireNonNull(preference.isActiveUserProfileData().preferenceSettings)?.genresIds
                        if (!genreIds.isNullOrEmpty()) {
                            val genresBuilder = StringBuilder()
                            for (i in genreIds.indices) {
                                if (i > 0) {
                                    genresBuilder.append(",")
                                }
                                genresBuilder.append(genreIds[i])
                            }
                            genresItems = genresBuilder.toString()
                        }
                    }
                }
            }
        }
        return genresItems
    }
}