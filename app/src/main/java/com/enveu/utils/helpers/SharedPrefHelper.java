package com.enveu.utils.helpers;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;

import com.enveu.OttApplication;
import com.enveu.beanModel.userProfile.UserProfileResponse;
import com.enveu.beanModelV3.mutli_profile_response.SecondaryProfileData;
import com.enveu.menuManager.model.MenuManagerModel;
import com.enveu.utils.colorsJson.model.ColorsModel;
import com.enveu.utils.config.bean.dmsResponse.LanguageCodes;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.security.CryptUtil;
import com.enveu.utils.stringsJson.model.StringsData;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class SharedPrefHelper {
    private static SharedPrefHelper instance;
    private static final String PREF_FILE = "Session";
    private static final String COLOR_JSON = "ColorJson";
    private static final String STRING_JSON = "StringJson";
    private static final String USER_PROFILE_DATA = "user_profile_data";
    private static final String ACTIVE_USER_PROFILE_DATA = "active_user_profile_data";
    private static final String SEARCH_HISTORY_LIST = "SEARCH_HISTORY_LIST";
    private static final String SEARCH_IDENTIFIER = "SEARCH_IDENTIFIER";
    private static final String CONFIG_LOCALE_DATA = "CONFIG_LOCALE_DATA";
    private final SharedPreferences mSharedPreferences;
    private final SharedPreferences.Editor mEditor;
    private CryptUtil cryptUtil;

    @SuppressLint("CommitPrefEdits")
    protected SharedPrefHelper(Context context) {
        mSharedPreferences = context.getSharedPreferences(PREF_FILE, Context.MODE_PRIVATE);
        mEditor = mSharedPreferences.edit();
        cryptUtil = CryptUtil.getInstance();
    }

    public static SharedPrefHelper getInstance() {
        if (instance == null) {
            instance = new SharedPrefHelper(OttApplication.Companion.getInstance());
        }
        return instance;
    }

    @SuppressLint("CommitPrefEdits")
    public void clear() {
        mSharedPreferences.edit();
        mEditor.clear();
        mEditor.commit();
    }

   /* public String getString(String key, String defValue) {
        return mSharedPreferences.getString(key, defValue);
    }

    public void setString(String key, String value) {
        mEditor.putString(key, value);
        mEditor.commit();
    }*/

    public String getString(String key, String defValue) {
        String decryptedValue = cryptUtil.decrypt(mSharedPreferences.getString(key, defValue), AppConstants.MY_VIPA_ENCRYPTION_KEY);
        if (decryptedValue == null || decryptedValue.equalsIgnoreCase("")||key.equalsIgnoreCase("DMS_Response")) {
            decryptedValue = mSharedPreferences.getString(key, defValue);
        }
        return decryptedValue;
    }

    public void setString(String key, String value) {
        String encryptedValue;
        encryptedValue = cryptUtil.encrypt(value, AppConstants.MY_VIPA_ENCRYPTION_KEY);
        if (key.equalsIgnoreCase("DMS_Response")||value.equalsIgnoreCase("")) {
            mEditor.putString(key, value);
        } else {
            mEditor.putString(key, encryptedValue);
        }
        mEditor.commit();
    }

    public int getInt(String key, int defValue) {
        return mSharedPreferences.getInt(key, defValue);
    }

    public void setInt(String key, int value) {
        mEditor.putInt(key, value);
        mEditor.commit();
    }

    protected boolean getBoolean(String key, boolean defValue) {
        return mSharedPreferences.getBoolean(key, defValue);
    }

    protected void setBoolean(String key, boolean value) {
        mEditor.putBoolean(key, value);
        mEditor.commit();
    }
    public void saveDataGenre(List<String> data) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(data);
        editor.putString("genre", json);
        editor.apply();     // This line is IMPORTANT !!!

    }

    public List<String> getDataGenreList(){
        Gson gson = new Gson();
        String json = mSharedPreferences.getString("genre", null);
        Type type = new TypeToken<ArrayList<String>>() {}.getType();
        return gson.fromJson(json, type);
    }

    public void saveSpeciesList(List<String> data) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(data);
        editor.putString("speciesList", json);
        editor.apply();     // This line is IMPORTANT !!!

    }

    public List<String> getSpeciesList(){
        Gson gson = new Gson();
        String json = mSharedPreferences.getString("speciesList", null);
        Type type = new TypeToken<ArrayList<String>>() {}.getType();
        return gson.fromJson(json, type);
    }

    public void saveTypeList(List<String> data) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(data);
        editor.putString("typesList", json);
        editor.apply();     // This line is IMPORTANT !!!
    }


    public List<String> getTypeList(){
        Gson gson = new Gson();
        String json = mSharedPreferences.getString("typesList", null);
        Type type = new TypeToken<ArrayList<String>>() {}.getType();
        return gson.fromJson(json, type);
    }
    public void saveDataGenreKeyValue(List<String> data) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(data);
        editor.putString("genreKey", json);
        editor.apply();     // This line is IMPORTANT !!!

    }

    public List<String> getDataGenreListKeyValue(){
        Gson gson = new Gson();
        String json = mSharedPreferences.getString("genreKey", null);
        Type type = new TypeToken<ArrayList<String>>() {}.getType();
        return gson.fromJson(json, type);
    }

    public void saveDataFeature(List<String> data) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(data);
        editor.putString("feature", json);
        editor.apply();     // This line is IMPORTANT !!!
    }

    public List<String> getDataFeatureList() {
        Gson gson = new Gson();
        String json = mSharedPreferences.getString("feature", null);
        Type type = new TypeToken<ArrayList<String>>() {
        }.getType();
        return gson.fromJson(json, type);
    }

    public void saveDataFeatureKeyValue(List<String> data) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(data);
        editor.putString("featureKey", json);
        editor.apply();     // This line is IMPORTANT !!!
    }

    public List<String> getDataFeatureListKeyValue() {
        Gson gson = new Gson();
        String json = mSharedPreferences.getString("featureKey", null);
        Type type = new TypeToken<ArrayList<String>>() {
        }.getType();
        return gson.fromJson(json, type);
    }
    public void saveDataMenuKeyValue(List<MenuManagerModel.Data.OrderedMenuItem> data) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(data);
        editor.putString("menuListItem", json);
        editor.apply();
    }

    public List<MenuManagerModel.Data.OrderedMenuItem> getDataMenuKeyValue(){
        Gson gson = new Gson();
        String json = mSharedPreferences.getString("menuListItem", null);
        Type type = new TypeToken<ArrayList<MenuManagerModel.Data.OrderedMenuItem>>() {}.getType();
        return gson.fromJson(json, type);
    }

    public void saveDataSort(List<String> data) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(data);
        editor.putString("sort", json);
        editor.apply();     // This line is IMPORTANT !!!
    }

    public List<String> getDataSortList(){
        Gson gson = new Gson();
        String json = mSharedPreferences.getString("sort", null);
        Type type = new TypeToken<ArrayList<String>>() {}.getType();
        return gson.fromJson(json, type);
    }


    public void saveDataSortKeyValue(List<String> data) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(data);
        editor.putString("sortKey", json);
        editor.apply();     // This line is IMPORTANT !!!

    }

    public List<String> getDataSortListKeyValue(){
        Gson gson = new Gson();
        String json = mSharedPreferences.getString("sortKey", null);
        Type type = new TypeToken<ArrayList<String>>() {}.getType();
        return gson.fromJson(json, type);
    }

    public  void saveKidsMode( boolean kidsMode) {

        SharedPreferences.Editor editor = mSharedPreferences.edit();
        editor.putBoolean("kidMode", kidsMode);
        editor.apply();
    }

    public  boolean getKidsMode() {
        boolean text = mSharedPreferences.getBoolean("kidMode", false);
        return text;
    }

    public  void savePrimaryAccountId( String primaryId) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        editor.putString("primaryId", primaryId);
        editor.apply();
    }

    public  String getPrimaryAccountId() {
        String text = mSharedPreferences.getString("primaryId", "");
        return text;
    }

    public  void saveSecondaryAccountId( String secondaryId) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        editor.putString("secondaryId", secondaryId);
        editor.apply();
    }

    public  String getSecondaryAccountId() {
        String text = mSharedPreferences.getString("secondaryId", "");
        return text;
    }

    public  void saveNotificationEnable( boolean noti) {

        SharedPreferences.Editor editor = mSharedPreferences.edit();
        editor.putBoolean("notificationEnable", noti);
        editor.apply();
    }

    public  boolean getNotificationEnable() {
        boolean text = mSharedPreferences.getBoolean("notificationEnable", false);
        return text;
    }

    public  void saveVia( String via) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        editor.putString("via", via);
        editor.apply();
    }

    public  String getVia() {
        String text = mSharedPreferences.getString("via", "");
        return text;
    }

    public void setColorJson(ColorsModel colorsModel) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        editor.putString(COLOR_JSON, new Gson().toJson(colorsModel));
        editor.apply();
    }

    public String getColorJson() {
        return mSharedPreferences.getString(COLOR_JSON, "");
    }

     public void setStringJson(StringsData stringData) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        editor.putString(STRING_JSON, new Gson().toJson(stringData));
        editor.apply();
    }

    public String getStringJson() {
        return mSharedPreferences.getString(STRING_JSON, "");
    }

    public void saveUserProfileData(UserProfileResponse userProfileData) {
        mSharedPreferences.edit()
                .putString(USER_PROFILE_DATA, new Gson().toJson(userProfileData))
                .apply();
    }

    public UserProfileResponse getUserProfile() {
        String json = mSharedPreferences.getString(USER_PROFILE_DATA, null);
        return new Gson().fromJson(json, UserProfileResponse.class);
    }

    public void saveActiveProfileData(SecondaryProfileData userProfileData) {
        mSharedPreferences.edit()
                .putString(ACTIVE_USER_PROFILE_DATA, new Gson().toJson(userProfileData))
                .apply();
    }

    public SecondaryProfileData isActiveUserProfileData() {
        String json = mSharedPreferences.getString(ACTIVE_USER_PROFILE_DATA, null);
        return new Gson().fromJson(json, SecondaryProfileData.class);
    }

    public void saveConfigLocaleData(LanguageCodes languageCodes) {
        mSharedPreferences.edit()
                .putString(CONFIG_LOCALE_DATA, new Gson().toJson(languageCodes))
                .apply();
    }

    public LanguageCodes getConfigLocaleData() {
        String json = mSharedPreferences.getString(CONFIG_LOCALE_DATA, null);
        return new Gson().fromJson(json, LanguageCodes.class);
    }

    public void saveSearchHistoryList(ArrayList<String> searchHistoryList) {
        mSharedPreferences.edit()
                .putString(SEARCH_HISTORY_LIST, new Gson().toJson(searchHistoryList))
                .apply();
    }

    public ArrayList<String> getSearchHistoryList() {
        String json = mSharedPreferences.getString(SEARCH_HISTORY_LIST, null);
        if (json == null) {
            return new ArrayList<>();
        }
        Type type = new TypeToken<ArrayList<String>>() {}.getType();
        return new Gson().fromJson(json, type);
    }

    public  void saveSearchScreenIdentifier( String identifier) {
        SharedPreferences.Editor editor = mSharedPreferences.edit();
        editor.putString(SEARCH_IDENTIFIER, identifier);
        editor.apply();
    }

    public  String getSearchScreenIdentifier() {
        return mSharedPreferences.getString(SEARCH_IDENTIFIER, "");
    }
}
