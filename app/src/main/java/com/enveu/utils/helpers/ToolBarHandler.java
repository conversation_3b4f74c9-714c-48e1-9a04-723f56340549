package com.enveu.utils.helpers;

import android.app.Activity;
import android.view.View;

import com.enveu.client.utils.ClickHandler;
import com.enveu.databinding.ActivityHelpBinding;
import com.enveu.databinding.ActivityMainBinding;
import com.enveu.databinding.ActivityWebViewBinding;
import com.enveu.databinding.ToolbarBinding;


public final class ToolBarHandler {
    private static ToolBarHandler instance;

    private ToolBarHandler() {
    }

    public static ToolBarHandler getInstance() {
        if (instance == null) {
            instance = new ToolBarHandler();
        }
        return instance;
    }

    public void setHomeAction(ToolbarBinding toolbar, Activity context) {
       /* toolbar.llSearchIcon.setOnClickListener(view -> {
            if (ClickHandler.allowClick()) {
                ActivityOptionsCompat activityOptionsCompat =
                        ActivityOptionsCompat.makeSceneTransitionAnimation(context,
                                toolbar.searchIcon, "imageMain");
                Intent in = new Intent(context, ActivitySearch.class);
                context.startActivity(in, activityOptionsCompat.toBundle());
            }
        });*/
        toolbar.clNotification.setOnClickListener(view -> {
            if (ClickHandler.allowClick()) {
               // ActivityLauncher.getInstance().notificationActivity(context, NotificationActivity.class);
            }
        });
    }

    public void setHelpAction(ActivityHelpBinding binding, String title, Activity context) {
        binding.toolbarFaq.backLayout.setVisibility(View.VISIBLE);
        binding.toolbarFaq.titleMid.setVisibility(View.VISIBLE);
        binding.toolbarFaq.titleMid.setText(title);
    }

    public void setArticleAction(ActivityWebViewBinding binding, String title, Activity context) {
        binding.toolbarFaq.backLayout.setVisibility(View.VISIBLE);
        binding.toolbarFaq.titleMid.setVisibility(View.VISIBLE);
        binding.toolbarFaq.titleMid.setText(title);
    }

    public void setSubMenuAction(ActivityMainBinding binding , int view){
        binding.toolbar.subMenu.setVisibility(view);
        binding.toolbar.leftScroll.bringToFront();
        binding.toolbar.logoLeft.setVisibility(view);
        binding.toolbar.logoMain2.setVisibility(View.GONE);
    }

}