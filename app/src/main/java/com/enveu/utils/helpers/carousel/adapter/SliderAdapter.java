package com.enveu.utils.helpers.carousel.adapter;

import android.content.Context;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.viewpager.widget.PagerAdapter;

import com.enveu.BR;
import com.enveu.R;
import com.enveu.beanModel.enveuCommonRailData.RailCommonData;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.callbacks.commonCallbacks.CommonRailtItemClickListner;
import com.enveu.utils.Logger;
import com.enveu.utils.commonMethods.AppCommonMethod;
import com.enveu.utils.constants.AppConstants;

import java.util.List;


public class SliderAdapter extends PagerAdapter {

    private final LayoutInflater layoutInflater;
    private final Context context;
    private final RailCommonData items;
    private long mLastClickTime = 0;
    private  String Tag = "img5";
    private final CommonRailtItemClickListner listner;
    private final int viewType;
    private final List<EnveuVideoItemBean> videos;
    private final int pos;

    public SliderAdapter(@NonNull Context context, RailCommonData items, int viewType, CommonRailtItemClickListner listner,int position) {
        this.context = context;
        this.items = items;
       // notifyDataSetChanged();
        layoutInflater = LayoutInflater.from(context);
        this.listner = listner;
        this.viewType = viewType;
        this.videos = items.getEnveuVideoItemBeans();
        this.pos=position;
    }

    @Override
    public int getCount() {
        return items.getEnveuVideoItemBeans().size();
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        // The object returned by instantiateItem() is a key/identifier. This method checks whether
        // the View passed to it (representing the page) is associated with that key or not.
        // It is required by a PagerAdapter to function properly.
        return view == object;
    }


    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, final int position) {

        ViewDataBinding viewDataBinding;

        switch (viewType) {
            case AppConstants.CAROUSEL_PR_POTRAIT:
                viewDataBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_carousal_potrait_item, container, false);
                break;
            case AppConstants.CAROUSEL_SQR_SQUARE:
                viewDataBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_carousel_square_item, container, false);
                break;
            case AppConstants.CAROUSEL_CIR_CIRCLE:
                viewDataBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_carousel_circle_item, container, false);
                break;
            case AppConstants.CAROUSEL_CST_CUSTOM:
                viewDataBinding = DataBindingUtil.inflate(layoutInflater, R.layout.row_slider_live, container, false);
                break;
            default:
                viewDataBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_carousal_landscape_item, container, false);
                TextView btTitle = viewDataBinding.getRoot().findViewById(R.id.image_title);
                try{
                    if (videos.get(position).getPosterURL().equalsIgnoreCase("") ) {
                        btTitle.bringToFront();
                        btTitle.setVisibility(View.VISIBLE);
                        btTitle.setText(videos.get(position).getTitle());
                    } else {
                        btTitle.setVisibility(View.GONE);
                    }
                } catch (Exception e) {

                }
                break;

        }

        viewDataBinding.setVariable(BR.assetItem, videos.get(position));
        viewDataBinding.setVariable(BR.adapter, this);
        viewDataBinding.setVariable(BR.position, position);
        container.addView(viewDataBinding.getRoot());
        return viewDataBinding.getRoot();
    }


    public void itemClick(int position) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1200) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        listner.railItemClick(items, position);

    }


    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        // Removes the page from the container for the given position. We simply removed object using removeView()
        // but could’ve also used removeViewAt() by passing it the position.
        try {
            // Remove the view from the container
            container.removeView((View) object);
            // Invalidate the object
          //  object = null;
        } catch (Exception e) {
            Logger.e("SliderAdapter", "" + e);
        }
    }
}