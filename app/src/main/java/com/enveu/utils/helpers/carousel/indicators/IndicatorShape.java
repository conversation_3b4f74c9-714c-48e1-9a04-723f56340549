package com.enveu.utils.helpers.carousel.indicators;

import android.content.Context;
import android.view.Gravity;
import android.view.animation.Animation;
import android.view.animation.ScaleAnimation;
import android.widget.LinearLayout;

import androidx.appcompat.widget.AppCompatImageView;

import com.enveu.R;


/**
 * PRATIK RAO
 */
public abstract class IndicatorShape extends AppCompatImageView {
    public static final int CIRCLE = 0;
    public static final int SQUARE = 1;
    public static final int ROUND_SQUARE = 2;
    public static final int DASH = 3;
    private static final int ANIMATION_DURATION = 150;
    private boolean isChecked = false;
    private int indicatorSize;
    private int unselectedIndicatorWidth = 0;
    private Integer selectedIndicatorWidth = 0;
    private Integer indicatorHeight = 0;
    private boolean mustAnimateChange;

    public IndicatorShape(Context context, int indicatorSize, boolean mustAnimateChange) {
        super(context);
        this.indicatorSize = indicatorSize;
        this.mustAnimateChange = mustAnimateChange;
        if (isChecked){
            setup(true);
        }else {
            setup(false);
        }
    }

    private void setup(boolean isSelected) {
        if (this.unselectedIndicatorWidth == 0) {
            unselectedIndicatorWidth = (int) getResources().getDimension(com.intuit.sdp.R.dimen._8sdp);
        }

        if (this.selectedIndicatorWidth == 0) {
            selectedIndicatorWidth = (int) getResources().getDimension(com.intuit.sdp.R.dimen._12sdp);
        }

        // Set the default height for both indicators
        if (this.indicatorHeight == 0) {
            indicatorHeight = (int) getResources().getDimension(com.intuit.sdp.R.dimen._8sdp);  // Example height
        }

        // Determine the width based on whether the indicator is selected or unselected
        int indicatorWidth = isSelected ? selectedIndicatorWidth : unselectedIndicatorWidth;

        // Create LayoutParams with different width but same height for both selected and unselected indicators
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(indicatorWidth, indicatorHeight);

        // Set margins and alignment
        int margin = getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._5sdp);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        layoutParams.setMargins(margin, 0, margin, 24);
        setLayoutParams(layoutParams);
    }

//    private void setup() {
//        if (this.indicatorSize == 0) {
//            indicatorSize = (int) getResources().getDimension(com.intuit.sdp.R.dimen._8sdp);
//        }
//        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(indicatorSize, indicatorSize);
//        int margin = getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._5sdp);
//        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
//        layoutParams.setMargins(margin, 0, margin, 0);
//        setLayoutParams(layoutParams);
//    }

    public void onCheckedChange(boolean isChecked) {
        if (this.isChecked != isChecked) {
//            if (mustAnimateChange) {
//                if (isChecked) {
//                    scale();
//                } else {
//                    descale();
//                }
//            }else {
//                if (isChecked) {
//                    scale(0);
//                } else {
//                    descale(0);
//                }
//            }
            this.isChecked = isChecked;
        }
    }

    private void scale() {
        scale(ANIMATION_DURATION);
    }

    private void scale(int duration) {
        ScaleAnimation scaleAnimation = new ScaleAnimation(1f, 1.5f, 1f, 1.5f, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        scaleAnimation.setDuration(duration);
        scaleAnimation.setFillEnabled(true);
        startAnimation(scaleAnimation);
    }


    private void descale() {
        descale(ANIMATION_DURATION);
    }

    private void descale(int duration) {
        ScaleAnimation scaleAnimation = new ScaleAnimation(1.5f, 1f, 1.5f, 1f, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        scaleAnimation.setDuration(duration);
        scaleAnimation.setFillEnabled(true);
        startAnimation(scaleAnimation);
    }

    public void setMustAnimateChange(boolean mustAnimateChange) {
        this.mustAnimateChange = mustAnimateChange;
    }


}
