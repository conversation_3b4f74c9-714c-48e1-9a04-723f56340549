package com.enveu.utils.helpers.carousel.indicators;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;


import com.enveu.R;
import com.enveu.client.enums.CRIndicator;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.carousel.listener.OnSlideChangeListener;

import java.util.ArrayList;
import java.util.List;


/**
 * PRATIK RAO
 */

public class SlideIndicatorsGroup extends LinearLayout implements OnSlideChangeListener {
    private final Context context;
    private final int layoutType;
    private final String indicatorPosition;
    private  String redirectedFrom = "";
    private final int indicatorSize;
    private final int selectedIndicatorSize;
    private final int unselectedIndicatorSize;
    private final List<IndicatorShape> indicatorShapes = new ArrayList<>();
    private int slidesCount;
    private Drawable selectedSlideIndicator;
    private Drawable unselectedSlideIndicator;
    private int defaultIndicator;
    private boolean mustAnimateIndicators = false;

    public SlideIndicatorsGroup(Context context, Drawable selectedSlideIndicator, Drawable unselectedSlideIndicator, int defaultIndicator, int indicatorSize, boolean mustAnimateIndicators, int layoutType, String indicatorPosition,String redirectedFrom,  int selectedIndicatorSize, int unselectedIndicatorSize) {
        super(context);
        this.context = context;
        this.selectedSlideIndicator = selectedSlideIndicator;
        this.unselectedSlideIndicator = unselectedSlideIndicator;
        this.defaultIndicator = defaultIndicator;
        this.indicatorSize = indicatorSize;
        this.mustAnimateIndicators = mustAnimateIndicators;
        this.indicatorPosition = indicatorPosition;
        this.layoutType = layoutType;
        this.redirectedFrom = redirectedFrom;
        this.selectedIndicatorSize = selectedIndicatorSize;
        this.unselectedIndicatorSize = unselectedIndicatorSize;
        setup();
    }

    public void setSlides(int slidesCount) {
        removeAllViews();
        indicatorShapes.clear();
        this.slidesCount = 0;
        for (int i = 0; i < slidesCount; i++) {
            onSlideAdd();
        }
        this.slidesCount = slidesCount;
    }

    public void onSlideAdd() {
        this.slidesCount += 1;
        addIndicator();
    }

    private void addIndicator() {
        IndicatorShape indicatorShape;
        if (selectedSlideIndicator != null && unselectedSlideIndicator != null) {
            indicatorShape = new IndicatorShape(context, indicatorSize, mustAnimateIndicators) {
                @Override
                public void onCheckedChange(boolean isChecked) {
                    super.onCheckedChange(isChecked);
                    if (isChecked) {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                            setBackground(selectedSlideIndicator);
                        } else {
                            setBackgroundDrawable(selectedSlideIndicator);
                        }
                    } else {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                            setBackground(unselectedSlideIndicator);
                        } else {
                            setBackgroundDrawable(unselectedSlideIndicator);
                        }
                    }
                }
            };
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                indicatorShape.setBackground(unselectedSlideIndicator);
            } else {
                indicatorShape.setBackgroundDrawable(unselectedSlideIndicator);
            }
            indicatorShapes.add(indicatorShape);
            addView(indicatorShape);

        } else {
            switch (defaultIndicator) {
                case IndicatorShape.SQUARE:
                    indicatorShape = new SquareIndicator(context, indicatorSize, mustAnimateIndicators);
                    indicatorShapes.add(indicatorShape);
                    addView(indicatorShape);
                    break;
                case IndicatorShape.CIRCLE:
                    indicatorShape = new CircleIndicator(context, indicatorSize, mustAnimateIndicators);
                    indicatorShapes.add(indicatorShape);
                    addView(indicatorShape);
                    break;
                default:
                    break;
            }
        }
    }

    public void setup() {
        setOrientation(LinearLayout.HORIZONTAL);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.gravity = Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL;
        if (redirectedFrom.equalsIgnoreCase(AppConstants.home)){
            layoutParams.setMargins(0, 0, 0, (int) getResources().getDimension(R.dimen.indicator_padding_bottom_landscape));
        }else {
            layoutParams.setMargins(0, 0, 0, (int) getResources().getDimension(R.dimen.indicator_padding_bottom));
        }
//        if (indicatorPosition.equalsIgnoreCase(CRIndicator.TOP.name()))
//            layoutParams.setMargins(0, 0, 0, (int) getResources().getDimension(R.dimen.indicator_padding_bottom));
//        else
//            layoutParams.setMargins(0, 0, 0, (int) getResources().getDimension(R.dimen.indicator_padding_bottom_landscape));

        setLayoutParams(layoutParams);
    }

    @Override
    public void onSlideChange(int selectedSlidePosition) {
//        for (int i = 0; i < indicatorShapes.size(); i++) {
//            indicatorShapes.get(i).onCheckedChange(i == selectedSlidePosition);
//        }

        for (int i = 0; i < indicatorShapes.size(); i++) {
            IndicatorShape indicatorView = indicatorShapes.get(i);
            indicatorView.onCheckedChange(i == selectedSlidePosition);
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) indicatorShapes.get(i).getLayoutParams();
            if (i == selectedSlidePosition) {
                indicatorView.setBackgroundResource(R.drawable.selected_indicator_shape);
                layoutParams.width = selectedIndicatorSize;
                layoutParams.height = unselectedIndicatorSize;
            } else {
                indicatorView.setBackgroundResource(R.drawable.indicator_circle_unselected);
                layoutParams.width = unselectedIndicatorSize;
                layoutParams.height = unselectedIndicatorSize;
            }
            indicatorShapes.get(i).setLayoutParams(layoutParams);
        }
    }

    public void changeIndicator(int defaultIndicator) {
        this.defaultIndicator = defaultIndicator;
        selectedSlideIndicator = null;
        unselectedSlideIndicator = null;
        setSlides(slidesCount);
    }

    public void changeIndicator(Drawable selectedSlideIndicator, Drawable unselectedSlideIndicator) {
        this.selectedSlideIndicator = selectedSlideIndicator;
        this.unselectedSlideIndicator = unselectedSlideIndicator;
        setSlides(slidesCount);
    }

    public void setMustAnimateIndicators(boolean mustAnimateIndicators) {
        this.mustAnimateIndicators = mustAnimateIndicators;
        for (IndicatorShape indicatorShape :
                indicatorShapes) {
            indicatorShape.setMustAnimateChange(mustAnimateIndicators);
        }
    }
}