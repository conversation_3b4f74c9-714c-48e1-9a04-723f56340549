package com.enveu.utils.helpers.carousel.indicators

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.res.ResourcesCompat
import com.enveu.R

/**
 * PRATIK RAO
 */

class SquareIndicator(context: Context, attrs: Int, defStyleAttr: Boolean) : IndicatorShape(context, attrs, defStyleAttr) {

    init {
        setBackgroundDrawable(
            ResourcesCompat.getDrawable(resources, R.drawable.indicator_circle_unselected, null)
        )
    }

    override fun onCheckedChange(isChecked: Boolean) {
        super.onCheckedChange(isChecked)
        val drawableRes = if (isChecked) {
            R.drawable.selected_indicator_shape
        } else {
            R.drawable.indicator_circle_unselected
        }
        val drawable: Drawable? = ResourcesCompat.getDrawable(resources, drawableRes, null)
        setBackgroundDrawable(drawable)
    }
}