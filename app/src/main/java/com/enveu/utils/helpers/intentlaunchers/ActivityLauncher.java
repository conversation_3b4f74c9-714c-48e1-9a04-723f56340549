package com.enveu.utils.helpers.intentlaunchers;

import static android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP;
import static android.content.Intent.FLAG_ACTIVITY_NEW_TASK;

import android.app.Activity;
import android.app.TaskStackBuilder;
import android.content.Intent;
import android.os.Bundle;

import com.enveu.activities.Novelties.ui.NoveltiesActivity;
import com.enveu.activities.biography.BiographyActivity;
import com.enveu.activities.customservices.EntBackgroundAudioActivity;
import com.enveu.activities.detail.ui.DetailActivity;
import com.enveu.activities.detail.ui.EpisodeActivity;
import com.enveu.activities.detail.ui.MatchDetailActivity;
import com.enveu.activities.device_management.DeviceManagerActivity;
import com.enveu.activities.downloads.MyDownloads;
import com.enveu.activities.follow_follower.FollowFollowingProfileActivity;
import com.enveu.activities.follow_follower.FollowerFollowingActivity;
import com.enveu.activities.homeactivity.ui.HomeActivity;
import com.enveu.activities.listing.listui.ListActivity;
import com.enveu.activities.listing.ui.GridActivity;
import com.enveu.activities.listing.ui.MyListActivity;
import com.enveu.activities.notification.ui.NotificationActivity;
import com.enveu.activities.profile.CountryListActivity;
import com.enveu.activities.profile.activate_device.ActivateDeviceActivity;
import com.enveu.activities.profile.order_history.ui.OrderHistoryActivity;
import com.enveu.activities.profile.ui.AccountSettingActivity;
import com.enveu.activities.profile.ui.ManageSubscriptionAccount;
import com.enveu.activities.profile.ui.ProfileActivityNew;
import com.enveu.activities.purchase.ui.PurchaseActivity;
import com.enveu.activities.search.ui.ActivityResults;
import com.enveu.activities.series.ui.SeriesDetailActivity;
import com.enveu.activities.settings.ActivitySettings;
import com.enveu.activities.settings.UserInterestActivity;
import com.enveu.activities.splash.ui.ActivitySplash;
import com.enveu.activities.sponsorArtist.ArtistActivity;
import com.enveu.activities.sponsorArtist.ArtistAndSponserActivity;
import com.enveu.activities.usermanagment.ui.ActivityForgotPassword;
import com.enveu.activities.usermanagment.ui.ActivityLogin;
import com.enveu.activities.usermanagment.ui.ActivitySelectSubscriptionPlan;
import com.enveu.activities.usermanagment.ui.ActivitySignUp;
import com.enveu.activities.usermanagment.ui.ChangePasswordActivity;
import com.enveu.activities.usermanagment.ui.EnterOTPActivity;
import com.enveu.activities.usermanagment.ui.PaymentDetailPage;
import com.enveu.activities.watchList.ui.WatchListActivity;
import com.enveu.beanModel.entitle.ResponseEntitle;
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean;
import com.enveu.bean_model_v2_0.listAll.AudioTrackListItem;
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory;
import com.enveu.jwplayer.player.PlayerActivity;
import com.enveu.ugc.GridListActivity;
import com.enveu.utils.Constants;
import com.enveu.utils.Logger;
import com.enveu.utils.constants.AppConstants;
import com.enveu.utils.helpers.ADHelper;
import com.enveu.utils.helpers.StringUtils;
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys;
import com.google.gson.Gson;

import java.io.Serializable;
import java.util.List;


public final class ActivityLauncher {
    private static ActivityLauncher instance;

    private ActivityLauncher() {

    }

    public static ActivityLauncher getInstance() {
        if (instance == null) {
            instance = new ActivityLauncher();
        }
        return instance;
    }

    public void activitySignUp(Activity source, Class<ActivitySignUp> destination) {
        Intent intent = new Intent(source, destination);
        source.startActivity(intent);
    }

    /*public void liveTvActivity(Activity source, Class<ActivityLiveTv> destination) {
        Intent intent = new Intent(source, destination);
        source.startActivity(intent);
    }*/
    public void homeActivity(Activity source, Class<HomeActivity> destination) {
        Intent intent = new Intent(source, destination);
        source.startActivity(intent);
        source.finish();
    }

    public void ProfileActivityNew(Activity source, Class<ProfileActivityNew> destination) {
        Intent intent = new Intent(source, destination);
        source.startActivity(intent);
    }

    public void CountryListActivity(Activity source, Class<CountryListActivity> destination) {
        Intent intent = new Intent(source, destination);
        source.startActivity(intent);
    }

    public void AccountSettingActivity(Activity source, Class<AccountSettingActivity> destination) {

        Intent intent = new Intent(source, destination);
        source.startActivity(intent);
    }

    public void manageAccount(Activity source, Class<ManageSubscriptionAccount> destination) {

        Intent intent = new Intent(source, destination);
        source.startActivity(intent);
    }

    public void goToPlanSubscription(Activity source, Class<ActivitySelectSubscriptionPlan> destination) {
        Intent intent = new Intent(source, destination);
        source.startActivity(intent);
    }

    public void goToPurchaseActivity(Activity source, Class<PurchaseActivity> destination) {

        Intent intent = new Intent(source, destination);
        source.startActivity(intent);
    }


    public void loginActivity(Activity source, Class<ActivityLogin> destination, String loginFrom) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("loginFrom", loginFrom);
        source.startActivity(intent);
    }

    public void biographyActivity(Activity source, Class<BiographyActivity> destination,Bundle bundle) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("bio_bundle",bundle);
        source.startActivity(intent);
    }


    public void DeviceManagement(Activity source, Class<DeviceManagerActivity> destination) {
        Intent intent = new Intent(source, destination);
        source.startActivity(intent);
    }


    public void loginActivityFromLogout(Activity source, Class<ActivityLogin> destination) {
        Intent intent = new Intent(source, destination);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        TaskStackBuilder.create(source).addNextIntentWithParentStack(intent).startActivities();
    }

    public void homeActivityWithParams(Activity source, Class<HomeActivity> destination, int postion) {
        Intent intent = new Intent(source, destination);
        intent.putExtra(AppConstants.HOME_TAG, postion);
        source.startActivity(intent);
    }

    public void homeActivityFromSponser(Activity source, Class<HomeActivity> destination, String assetType, String contentSlugSponser, int id, boolean from) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("assetType", assetType);
        intent.putExtra("contentSlug", contentSlugSponser);
        intent.putExtra("id", id);
        intent.putExtra("from", from);
        source.startActivity(intent);
        source.finish();
    }

    public void launchPlayerActitivity(Activity source, Class<PlayerActivity> destination, String playbackurl, boolean IsBingeWatchEnable, List<EnveuVideoItemBean> seasonEpisodesList, int currentEpisodeId, String seriesTittle, String tittle, String contentType, Boolean isTrailer, Boolean isLive, String posterUrl, String screenName, String externalRefId, String skipIntroStartTime, String skipIntroEndTime, String tag, List<AudioTrackListItem> audioTrackListItems) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("activity", String.valueOf(source));
        intent.putExtra("playBackUrl", playbackurl);
        intent.putExtra("binge_watch", IsBingeWatchEnable);
        intent.putExtra("episodeId", currentEpisodeId);
        intent.putExtra("seriesTittle", seriesTittle);
        intent.putExtra("tittle", tittle);
        intent.putExtra("contentType", contentType);
        intent.putExtra("episodeList", (Serializable) seasonEpisodesList);
        intent.putExtra("isTrailer", isTrailer);
        intent.putExtra("isLive", isLive);
        intent.putExtra("posterUrl", posterUrl);
        intent.putExtra("screenName", screenName);
        intent.putExtra("externalRefId", externalRefId);
        intent.putExtra("skipIntroStartTime", skipIntroStartTime);
        intent.putExtra("skipIntroEndTime", skipIntroEndTime);
        intent.putExtra("tag", tag);
        intent.putExtra(AppConstants.AUDIO_TRACK_ITEM, (Serializable) audioTrackListItems);
        source.startActivity(intent);
    }


    public void podcastPlayer(Activity source, Class<EntBackgroundAudioActivity> destination, String externalRefId, List<EnveuVideoItemBean> audioList, String tittle, int id, boolean isDrmDisabled) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("externalRefId", externalRefId);
        intent.putExtra("audioList", (Serializable) audioList);
        intent.putExtra("tittle", tittle);
        intent.putExtra("id", id);
        intent.putExtra("isDrmDisabled", isDrmDisabled);
        source.startActivity(intent);
    }

    public void homeActivityWithIntent(Activity source, Class<HomeActivity> destination, int postion, String intentFrom) {
        Intent intent = new Intent(source, destination);
        intent.putExtra(AppConstants.HOME_TAG, postion);
        intent.putExtra(AppConstants.INTENT_FROM, intentFrom);
        source.startActivity(intent);
    }

    public void forceLogin(Activity source, Class<ActivityForgotPassword> destination, String token, String fid, String name, String picUrl, boolean forceLogin) {
        Bundle args = new Bundle();
        args.putString("fbName", name);
        args.putString("fbToken", token);
        args.putString("fbId", fid);
        args.putString("fbProfilePic", picUrl);
        args.putBoolean("forceLogin", forceLogin);
        Intent intent = new Intent(source, destination);
        intent.putExtra(AppConstants.EXTRA_REGISTER_USER, args);
        source.startActivity(intent);
    }


    public void homeScreen(Activity source, Class<HomeActivity> destination, boolean fromDeeplink, String contentSlug, String contentType, int assertID) {
        Intent intent = new Intent(source, destination);
        intent.putExtra(AppConstants.FROM_REDIRECTION, fromDeeplink);
        intent.putExtra(AppConstants.BUNDLE_CONTENT_SLUG, contentSlug);
        if (assertID != 0) intent.putExtra(AppConstants.ASSET_ID, assertID);
        intent.putExtra(AppConstants.CONTENT_TYPE, contentType);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        TaskStackBuilder.create(source).addNextIntentWithParentStack(intent).startActivities();
    }

    public void splashScreenKill(Activity source, Class<ActivitySplash> destination) {
        Intent intent = new Intent(source, destination);
        //intent.putExtra( AppConstants.KIDS_MODE,kidsMode);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        TaskStackBuilder.create(source).addNextIntentWithParentStack(intent).startActivities();
    }

    public void detailScreen(Activity source, Class<DetailActivity> destination, int id, String duration, boolean isPremium) {
        Bundle args = new Bundle();
        args.putInt(AppConstants.BUNDLE_ASSET_ID, id);
        args.putInt(AppConstants.BUNDLE_VIDEO_ID_BRIGHTCOVE, id);

        args.putBoolean(AppConstants.BUNDLE_IS_PREMIUM, isPremium);
        if (StringUtils.isNullOrEmpty(duration))
            args.putString(AppConstants.BUNDLE_DURATION, "0");
        else
            args.putString(AppConstants.BUNDLE_DURATION, duration);
        Intent intent = new Intent(source, destination);
        intent.putExtra(AppConstants.BUNDLE_ASSET_BUNDLE, args);
        intent.setFlags(FLAG_ACTIVITY_CLEAR_TOP);
        intent.setFlags(FLAG_ACTIVITY_NEW_TASK);
        KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
        preference.setAppPrefAssetId(0);
        if (ADHelper.getInstance(source).getPipAct() != null) {
            ADHelper.getInstance(source).getPipAct().moveTaskToBack(false);
            intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
        }
        source.startActivity(intent);
    }

    public void detailScreenBrightCove(Activity source, Class<DetailActivity> destination, int id,boolean isContinueWatching, String assetType, String customType) {
        Bundle args = new Bundle();
        args.putString(Constants.GAME_ASSET_TYPE, assetType);
        args.putString(Constants.GAME_CUSTOM_TYPE, customType);
        args.putInt(AppConstants.BUNDLE_ASSET_ID, id);
        Intent intent = new Intent(source, destination);
        intent.putExtra(AppConstants.BUNDLE_ASSET_BUNDLE, args);
        intent.putExtra("isContinueWatching", isContinueWatching);
        intent.setFlags(FLAG_ACTIVITY_CLEAR_TOP);
        KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
        preference.setAppPrefAssetId(0);
        if (ADHelper.getInstance(source).getPipAct() != null) {
            ADHelper.getInstance(source).getPipAct().moveTaskToBack(false);
            ADHelper.getInstance(source).getPipAct().finish();
        }
        source.startActivity(intent);
    }

    public void detailScreenForDeepLink(Activity source, Class<DetailActivity> destination, int id,String contentSlug, String duration, boolean isPremium) {
        Bundle args = new Bundle();
        args.putBoolean(AppConstants.BUNDLE_IS_PREMIUM, isPremium);
        args.putString(AppConstants.BUNDLE_CONTENT_SLUG, contentSlug); // Replace with actual value
        if (StringUtils.isNullOrEmpty(duration))
            args.putString(AppConstants.BUNDLE_DURATION, "0");
        else
            args.putString(AppConstants.BUNDLE_DURATION, duration);
        Intent intent = new Intent(source, destination);
        intent.putExtra(AppConstants.BUNDLE_ASSET_BUNDLE, args);
        intent.setFlags(FLAG_ACTIVITY_CLEAR_TOP);
        intent.setFlags(FLAG_ACTIVITY_NEW_TASK);
        KsPreferenceKeys preference = KsPreferenceKeys.getInstance();
        preference.setAppPrefAssetId(0);
        if (ADHelper.getInstance(source).getPipAct() != null) {
            ADHelper.getInstance(source).getPipAct().moveTaskToBack(false);
            intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
        }
        source.startActivity(intent);
    }


    public void matchDetailScreen(Activity source, Class<MatchDetailActivity> destination, int Id) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("Id", Id);
        source.startActivity(intent);
    }

    public void navigateUserInterestActivity(Activity source, Class<UserInterestActivity> destination) {
        Intent intent = new Intent(source, destination);
        intent.putExtra(AppConstants.VIA_LOGIN, true);
        source.startActivity(intent);
    }


    public void episodeScreenBrightcove(Activity source, Class<EpisodeActivity> destination, int id, int seasonNumber) {
        KsPreferenceKeys preference = KsPreferenceKeys.getInstance();

        Bundle args = new Bundle();
        args.putInt(AppConstants.BUNDLE_ASSET_ID, id);
        args.putInt(AppConstants.BUNDLE_SEASON_NUMBER, seasonNumber);
        Intent intent = new Intent(source, destination);
        intent.putExtra(AppConstants.BUNDLE_ASSET_BUNDLE, args);
        intent.setFlags(FLAG_ACTIVITY_CLEAR_TOP);
        preference.setAppPrefAssetId(0);
        if (ADHelper.getInstance(source).getPipAct() != null) {
            ADHelper.getInstance(source).getPipAct().moveTaskToBack(false);
            ADHelper.getInstance(source).getPipAct().finish();
        }
        source.startActivity(intent);
    }


    public void episodeScreen(Activity source, Class<EpisodeActivity> destination, int id, String duration, boolean isPremium) {
        KsPreferenceKeys preference = KsPreferenceKeys.getInstance();

        Bundle args = new Bundle();
        args.putInt(AppConstants.BUNDLE_ASSET_ID, id);
        args.putBoolean(AppConstants.BUNDLE_IS_PREMIUM, isPremium);
        if (StringUtils.isNullOrEmpty(duration))
            args.putString(AppConstants.BUNDLE_DURATION, "0");
        else
            args.putString(AppConstants.BUNDLE_DURATION, duration);
        Intent intent = new Intent(source, destination);
        intent.putExtra(AppConstants.BUNDLE_ASSET_BUNDLE, args);
        intent.setFlags(FLAG_ACTIVITY_CLEAR_TOP);
        preference.setAppPrefAssetId(0);
        if (ADHelper.getInstance(source).getPipAct() != null) {
            ADHelper.getInstance(source).getPipAct().moveTaskToBack(false);
            intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
        }
        source.startActivity(intent);
    }

    public void portraitListing(Activity source, Class<GridActivity> destination, String currentData,String i, String title, int flag, int type, BaseCategory baseCategory, boolean continueWatching, int requestCode) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("playListId", i);
        intent.putExtra("title", title);
        intent.putExtra("currentData", currentData);
        intent.putExtra("flag", flag);
        intent.putExtra("shimmerType", type);
        intent.putExtra("baseCategory", new Gson().toJson(baseCategory));
        intent.putExtra("isContinueWatching", continueWatching);
        source.startActivityForResult(intent, requestCode);
    }

    public void listActivity(Activity source, Class<ListActivity> destination, String i, String title, int flag, int type, BaseCategory baseCategory) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("playListId", i);
        intent.putExtra("title", title);
        intent.putExtra("flag", flag);
        intent.putExtra("shimmerType", type);
        intent.putExtra("baseCategory", new Gson().toJson(baseCategory));
        source.startActivity(intent);
    }

    public void notificationActivity(Activity source, Class<NotificationActivity> destination) {
        Intent intent = new Intent(source, destination);
        source.startActivity(intent);
    }


    public void goToEnterOTP(Activity source, Class<EnterOTPActivity> destination, String screenName) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("fromWhich", screenName);
        source.startActivity(intent);
    }

    public void goToPlanScreen(Activity source, Class<ActivitySelectSubscriptionPlan> destination, String screenName) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("intentFrom", screenName);
        source.startActivity(intent);
    }

    public void goToPlanScreenFromOtp(Activity source, Class<ActivitySelectSubscriptionPlan> destination, String screenName) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("intentFrom", screenName);
        source.startActivity(intent);
        source.finish();
    }

    public void changePassword(Activity activity, Class<ChangePasswordActivity> destination) {
        activity.startActivity(new Intent(activity, destination));
    }

    public void artistActivity(Activity activity, Class<ArtistActivity> destination) {
        activity.startActivity(new Intent(activity, destination));
    }

    public void artistAndSponserActivity(Activity activity, Class<ArtistAndSponserActivity> destination) {
        activity.startActivity(new Intent(activity, destination));
    }


    public void orderHistroy(Activity activity, Class<OrderHistoryActivity> destination) {
        activity.startActivity(new Intent(activity, destination));
    }

    public void ActivateTV(Activity activity, Class<ActivateDeviceActivity> destination) {
        activity.startActivity(new Intent(activity, destination));
    }

    public void goToLogin(Activity activity, Class<ActivityLogin> destination) {
        activity.startActivity(new Intent(activity, destination));
    }

    public void goToLoginFromSplash(Activity activity, Class<ActivityLogin> destination, boolean fromSplash) {
        Intent intent = new Intent(activity, destination);
        intent.putExtra("fromSplash", fromSplash);
        activity.startActivity(intent);
        activity.finish();

    }

    public void gotoList(Activity activity, Class<MyListActivity> destination) {
        activity.startActivity(new Intent(activity, destination));
    }


    public void goToNovelties(Activity activity, Class<NoveltiesActivity> destination) {
        activity.startActivity(new Intent(activity, destination));
    }

    public void goToAccountSetting(Activity activity, Class<AccountSettingActivity> destination) {
        activity.startActivity(new Intent(activity, destination));
    }

    public void goToSetting(Activity activity, Class<ActivitySettings> destination) {
        activity.startActivity(new Intent(activity, destination));
    }


    public void resultActivityBundle(Activity source, Class<ActivityResults> destination, String assetType, String searchKey, int total, boolean applyFilter, String customContentType, String videoType, String header) {
        Bundle args = new Bundle();
        args.putString("assetType", assetType);
        args.putString("Search_Key", searchKey);
        args.putInt("Total_Result", total);
        args.putBoolean("apply_filter", applyFilter);
        args.putString("customContentType", customContentType);
        args.putString("videoType", videoType);
        args.putString("header", header);
        Intent intent = new Intent(source, destination);
        intent.putExtra("SearchResult", args);
        source.startActivity(intent);
    }


    public void seriesDetailScreen(Activity source, Class<SeriesDetailActivity> destination, int seriesId) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("seriesId", seriesId);
        source.startActivity(intent);
    }

    public void seriesDetailScreenDeepLink(Activity source, Class<SeriesDetailActivity> destination, int seriesId,String contentSlug) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("seriesId", seriesId);
        intent.putExtra(AppConstants.BUNDLE_CONTENT_SLUG, contentSlug);
        source.startActivity(intent);
    }

    public void seriesDetailScreen(Activity source, Class<SeriesDetailActivity> destination, int seriesId, String assetType) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("seriesId", seriesId);
        intent.putExtra("assetType", assetType);
        source.startActivity(intent);
    }

    public void seriesDetailScreen(Activity source, Class<SeriesDetailActivity> destination, int seriesId, String assetType,boolean isContinueWatching) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("seriesId", seriesId);
        intent.putExtra("assetType", assetType);
        source.startActivity(intent);
    }


    public void watchHistory(Activity source, Class<WatchListActivity> destination, String type, boolean isWatchHistory) {
        Bundle args = new Bundle();
        args.putString("viewType", type);
        Intent intent = new Intent(source, destination);
        intent.putExtra("bundleId", args);
        intent.putExtra("isWatchHistory", isWatchHistory);
        source.startActivity(intent);
    }

    public void gridList(Activity source, Class<GridListActivity> destination, String assetType,String screenTitle){
        Bundle args = new Bundle();
        args.putString("assetType", assetType);
        args.putString(AppConstants.TITLE, screenTitle);
        Intent intent = new Intent(source, destination);
        intent.putExtra(AppConstants.BUNDLE_ASSET_BUNDLE, args);
        source.startActivityForResult(intent, Constants.REQUEST_CODE);
    }

    public void launchMyDownloads(Activity source) {
        source.startActivity(new Intent(source, MyDownloads.class));
    }

    public void goToDetailPlanScreen(Activity source, Class<PaymentDetailPage> destination, boolean isFrom, ResponseEntitle responseEntitle) {
        Intent intent = new Intent(source, destination);
        intent.putExtra("fromWhich", isFrom);
        intent.putExtra("responseEntitle", (Serializable) responseEntitle);
        source.startActivity(intent);
    }

    public void goToFollowFollowing(Activity activity, Class<FollowerFollowingActivity> destination) {
        activity.startActivity(new Intent(activity, destination));
    }

    public void goToFollowFollowingProfileActivity(Activity activity, Class<FollowFollowingProfileActivity> destination) {
        activity.startActivity(new Intent(activity, destination));
    }


}
