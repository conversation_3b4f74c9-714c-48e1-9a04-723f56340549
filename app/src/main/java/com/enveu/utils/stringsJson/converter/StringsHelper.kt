package com.enveu.utils.stringsJson.converter

import android.util.Log
import com.enveu.OttApplication
import com.enveu.utils.constants.AppConstants
import com.enveu.utils.helpers.SharedPrefHelper
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.enveu.utils.stringsJson.model.StringsData
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.InputStream
import java.nio.charset.StandardCharsets

object StringsHelper {

    fun loadDataFromJson(): StringsData? {
        var users: StringsData? = null
        val json: String = try {
            var `is`:InputStream? = null
            Log.d("loadDataFromJson", "loadDataFromJson: " + KsPreferenceKeys.getInstance().appLanguage)
            `is` = if (KsPreferenceKeys.getInstance().appLanguage == AppConstants.LANGUAGE_ARABIC) {
                OttApplication.context.assets.open("ArabicStringsData.json")
            } else {
                OttApplication.context.assets.open("StringsData.json")
            }

            val size = `is`.available()
            val buffer = ByteArray(size)
            `is`.read(buffer)
            `is`.close()
            String(buffer, StandardCharsets.UTF_8)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            return null
        }

        val listType = object : TypeToken<StringsData?>() {}.type
        if (null != listType) {
            users = Gson().fromJson(json, listType)
        }
        return users
    }

    fun instance(): StringsData? {
        var stringData: StringsData? = null
        val jsonData = SharedPrefHelper.getInstance().stringJson
        val listType = object : TypeToken<StringsData?>() {}.type
        if (null != listType) {
            stringData = Gson().fromJson(jsonData, listType)
        }
        return stringData
    }

    fun stringParse(jsonKey: String, localKey: String): String {
        val value: String = if (jsonKey != null && jsonKey != "null" && jsonKey != "") {
            jsonKey
        } else {
            localKey
        }
        return value
    }
}