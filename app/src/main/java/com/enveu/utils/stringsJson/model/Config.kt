package com.enveu.utils.stringsJson.model

data class Config(
    val user_setting: String,
    val login_sign_in: String,
    val login_forgot_pwd: String,
    val artist_button_text: String,
    val artist_result_not_found : String,
    val login_or_sign_in_with: String,
    val login_do_not_an_account: String,
    val login_or_log_in_with: String,
    val sign_up_with_tesliya: String,
    val login_let_s_get_started_text: String,
    val login_signup: String,
    val sign_up_to_continue: String,
    val app_setting: String,
    val manage_profile_sub_heading: String,
    val this_subscibtion_was_activated_by_bussiness : String,
    val this_subscibtion_web : String,
    val this_subscibtion_ios : String,
    val grace_period_alert : String,
    val grace_period_alert_desc : String,
    val hold_period_alert : String,
    val hold_period_alert_desc : String,
    val nothing_your_watchHistory_tittle : String,
    val nothing_your_watchHistory_description : String,
    val nothing_your_watchHistory : String,
    val my_history_more : String,
    val my_history : String,
    val my_history_desc : String,
    val my_list_desc : String,
    val delete_account_tittle : String,
    val signup_email_address: String,
    val signup_enter_your_city: String,
    val signup_enter_your_state: String,
    val forgot_description: String,
    val signup_create_pwd: String,
    val signup_confirm_pwd: String,
    val signup_term_condition: String,
    val signup_signup: String,
    val signup_or_signup_with: String,
    val signup_already_have_an_account: String,
    val signup_signin: String,
    val sign_up_now_and_enjoy_our_amazing_library_of_creation_research_content: String,
    val forgot_enter_email: String,
    val forgot_continue: String,
    val otp_enter_otp: String,
    val otp_digit_code_send: String,
    val otp_continue: String,
    val otp_resend_otp: String,
    val otp_code_valid: String,
    val profile_enter_name: String,
    val enter_your_email: String,
    val profile_mobile_number: String,
    val profile_dob: String,
    val profile_country: String,
    val profile_city: String,
    val profile_update_btn: String,
    val profile_dlt_btn: String,
    val profile_my_profile: String,
    val change_pwd_change_pwd: String,
    val sign_out_all_device: String,
    val sign_out1: String,
    val sign_out: String,
    val enter_email_address : String,
    val re_enter_password : String,
    val create_password : String,
    val change_pwd_new_pwd: String,
    val change_pwd_confirm_pwd: String,
    val change_pwd_update_pwd: String,
    val buy_select_subscription: String,
    val buy_restore_subscription: String,
    val buy_pay_now: String,
    val buy_in_case_query: String,
    val order_history_title: String,
    val order_history_payment_mode: String,
    val purchase_date : String,
    val duration : String,
    val order_history_payment_status: String,
    val order_history_order_Id: String,
    val order_history_transaction_type: String,
    val settings_title: String,
    val settings_change_lang: String,
    val settings_streaming_settings: String,
    val settings_interests: String,
    val streaming_settings_title: String,
    val streaming_auto: String,
    val streaming_hd: String,
    val streaming_full_hd: String,
    val streaming_sd: String,
    val lang_change_lang: String,
    val lang_english: String,
    val lang_arabic: String,
    val my_list: String,
    val my_watch_list_title: String,
    val my_playlist: String,
    val ugc_create: String,
    val my_favorite_for_ugc: String,
    val not_verified:String,
    val could_not_find_any_result: String,
    val already_registered_just_sign_in: String,
    val select_gender: String,
    val user_name: String,
    val terms_and_privacy_policy_text: String,
    val detail_page_episodes: String,
    val detail_page_related_videos: String,
    val detail_page_produced_by: String,
    val detail_page_sponsored_by: String,
    val detail_page_my_list: String,
    val detail_page_share: String,
    val detail_page_like: String,
    val detail_page_play: String,
    val detail_page_trailer: String,
    val skip: String,
    val noti_title: String,
    val noti_no_new_notifications: String,
    val popular_search: String,
    val recent_searches: String,
    val cancel_search: String,
    val no_result: String,
    val more_privacy_policy: String,
    val more_video_quality: String,
    val more_buy_now: String,
    val more_term_condition: String,
    val more_account: String,
    val more_contact_us: String,
    val manage_profile: String,
    val parental_control_menu_text: String,
    val more_my_playlist: String,
    val header_my_playlist: String,
    val more_gaming: String,
    val more_manage_subscription: String,
    val more_settings: String,
    val more_order_history: String,
    val account_edit_profile: String,
    val account_change_password: String,
    val change_password : String,
    val sponsor:String,
    val account_logout: String,
    val popup_username_pwd_does_not_match: String,
    val popup_empty_message_Password: String,
    val popup_request_sent: String,
    val popup_thanks_for_putting_request: String,
    val popup_check_your_email: String,
    val popup_pwd_must_be_8_to_16_char: String,
    val popup_user_already_has_an_account: String,
    val popup_error: String,
    val logout_confirmation: String,
    val popup_mobile_number_digit: String,
    val popup_update: String,
    val popup_update_successfully: String,
    val thanks_for_sponsership :String,
    val popup_delete_account: String,
    val popup_want_to_delete_account: String,
    val popup_continue: String,
    val popup_cancel: String,
    val popup_under_review: String,
    val popup_your_previous_account_already_review: String,
    val popup_pls_enter_new_pwd: String,
    val popup_pwd_pwd_changed: String,
    val popup_pwd_has_been_changed: String,
    val popup_check_internet_connection: String,
    val popup_no_internet_connection_found: String,
    val popup_remove_content_from_my_list: String,
    val popup_no_new_notification: String,
    val popup_recent_search: String,
    val popup_delete_search_history: String,
    val popup_search_yes: String,
    val popup_search_no: String,
    val popup_subscribe_access_to_premium: String,
    val popup_subscribe_purchase: String,
    val profile_enter_user_name: String,
    val popup_logout: String,
    val popup_logout_you_want_to_logout: String,
    val popup_change_language_tittle: String,
    val popup_do_you_want_to_change_lang: String,
    val popup_enter_valid_otp: String,
    val popup_expire_time: String,
    val popup_please_click_on_the_resend_button: String,
    val popup_empty_email_tittle: String,
    val popup_empty_last_name_tittle: String,
    val popup_empty_email_subtitle: String,
    val popup_empty_last_name_subtitle: String,
    val popup_empty_first_name_subtitle: String,
    val popup_invalid_mobile_subtitle: String,
    val popup_invalid_email_tittle: String,
    val popup_invalid_mobile_tittle: String,
    val popup_invalid_email_subtitle: String,
    val popup_continue_btn: String,
    val popup_incorrect_pwd_tittle: String,
    val popup_login_enter_pwd_tittle: String,
    val popup_profile_error: String,
    val geo_blocking_title:String,
    val popup_something_went_wrong: String,
    val popup_change_pwd_enter_confirm_pwd: String,
    val popup_change_pwd_your_pwd_does_not_match: String,
    val popup_user_not_verify: String,
    val popup_verify: String,
    val email_verify_title: String,
    val email_verified_title: String,
    val enter_the_code_displayed_on_your_tv: String,
    val popup_select_plan: String,
    val popup_ok: String,
    val popup_minutes: String,
    val popup_purchase: String,
    val popup_remove: String,
    val popup_user_does_not_exists: String,
    val popup_payment_error: String,
    val popup_payment_success_Desc: String,
    val subscription_activated: String,
    val popup_confirm_pwd: String,
    val popup_your_account_has_been_verified: String,
    val popup_date_difference: String,
    val popup_payment_description: String,
    val popup_empty_Password: String,
    val popup_empty_Confirm_Password: String,
    val popup_invalid_Confirm_Password: String,
    val popup_invalid_dob_tittle:String,
    val movie_tabbar: String,
    val kids_tabbar: String,
    val series_tabbar: String,
    val more_tabbar: String,
    val more_title_btn: String,
    val detail_page_no_data_available: String,
    val detail_page_season: String,
    val detail_page_now_playing: String,
    val detail_page_all_episode: String,
    val search_hint: String,
    val search_result: String,
    val search_artist_hint: String,
    val search_results: String,
    val related_data_see_all: String,
    val no_connection_title: String,
    val no_connection_description: String,
    val no_connection_btn_txt: String,
    val popup_empty_dob_tittle: String,
    val popup_empty_dob_subtitle: String,
    val popup_continue_watching: String,
    val popup_continue_watching_for: String,
    val device_management : String,
    val activate_tv_device : String,
    val popup_change_pwd_incorrect_confirm_pwd: String,
    val search_movies: String,
    val search_documentaries: String,
    val search_see_all: String,
    val rate_the_app:String,
    val free_trail : String,
    val subscription_Active: String,
    val exclusive_content: String,
    val popup_notEntitled: String,
    val MATCH_START_ON: String,
    val MATCH_READY: String,
    val MATCH_NOT_ATTENDED: String,
    val MATCH_END: String,
    val STREAM_ERROR: String,
    val MATCH_ABOUT_END: String,
    val full_name: String
)