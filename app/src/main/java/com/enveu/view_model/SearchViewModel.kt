package com.enveu.view_model

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.enveu.beanModel.enveuCommonRailData.RailCommonData
import com.enveu.beanModelV3.playListModelV2.EnveuCommonResponse
import com.enveu.beanModelV3.searchV2.ItemsItem
import com.enveu.beanModelV3.searchV2.ResponseSearch
import com.enveu.beanModelV3.uiConnectorModelV2.EnveuVideoItemBean
import com.enveu.callbacks.PlaylistApiSuccessListener
import com.enveu.client.api_callback.NetworkResultCallback
import com.enveu.client.baseCollection.baseCategoryModel.BaseCategory
import com.enveu.client.networkRequestManager.RequestManager
import com.enveu.networking.response.HashTagsSearchResponse
import com.enveu.networking.response.screen_id.ScreenIdResponse
import com.enveu.networking.response.searchModel.Data
import com.enveu.networking.response.searchModel.SearchResultResponse
import com.enveu.utils.config.LanguageLayer
import com.google.gson.Gson
import com.google.gson.JsonObject
import retrofit2.Response

class SearchViewModel : ViewModel() {
    val screenIdApiResponse = MutableLiveData<ScreenIdResponse>()
    val searchResultResponse = MutableLiveData<SearchResultResponse>()
    val hashTagsSearchResponse = MutableLiveData<HashTagsSearchResponse>()


    fun callScreenIdApi(screenId: String?) {
            RequestManager.instance.callScreenIdApi("v2/", screenId, object : NetworkResultCallback<JsonObject> {
                    override fun loading(isLoading: Boolean) {}

                    override fun success(status: Boolean?, response: Response<JsonObject>?) {
                        val responseModel = Gson().fromJson(response?.body(), ScreenIdResponse::class.java)
                        screenIdApiResponse.value = responseModel
                    }

                    override fun failure(status: Boolean, errorCode: Int, message: String) {
                        screenIdApiResponse.value = ScreenIdResponse()
                    }
                })
        }

    fun getPlaylistDetailsById(playlistID: String, languageCode: String, pageNumber: Int, pageSize: Int, baseCategory: BaseCategory, playlistApiSuccessListener: PlaylistApiSuccessListener) {
            RequestManager.instance.getPlaylistDetailsById(playlistID, languageCode, pageNumber, pageSize, object : NetworkResultCallback<JsonObject> {
                    override fun loading(isLoading: Boolean) {}
                    override fun success(status: Boolean?, response: Response<JsonObject>?) {
                        val responseModel = Gson().fromJson(response?.body(), EnveuCommonResponse::class.java)
                        val railCommonData = RailCommonData(responseModel.data, baseCategory, false, 0)
                        railCommonData.status = true
                        playlistApiSuccessListener.onSuccessListener(railCommonData)
                    }

                    override fun failure(status: Boolean, errorCode: Int, message: String) {
                        playlistApiSuccessListener.onSuccessListener(null)
                    }
                })
        }

    fun getSearchResult(keyword: String?, contentType:Pair<String, String>, videoType:Pair<String, String>, offSet:Int?, size:Int?, trackEvent:Boolean, languageCode: String?) {
            RequestManager.instance.getSearchResult(keyword, contentType, videoType, offSet, size, trackEvent, languageCode, object : NetworkResultCallback<JsonObject> {
                    override fun loading(isLoading: Boolean) {}
                    override fun success(status: Boolean?, response: Response<JsonObject>?) {
                        val responseModel = Gson().fromJson(response?.body(), ResponseSearch::class.java)
                        val searchItems: List<ItemsItem> = responseModel.data.items
                        val enveuVideoItemBeans: ArrayList<EnveuVideoItemBean?> = ArrayList()
                        for (videoItem in searchItems) {
                            val enveuVideoItemBean = EnveuVideoItemBean(videoItem, false)
                            enveuVideoItemBeans.add(enveuVideoItemBean)
                        }
                        val searchResult = SearchResultResponse()
                        searchResult.data = Data()
                        searchResult.data?.items = enveuVideoItemBeans
                        searchResult.data?.pageInfo = responseModel.data.pageInfo
                        searchResultResponse.value = searchResult
                    }

                    override fun failure(status: Boolean, errorCode: Int, message: String) {
                        Log.d("checkFailureApi", message)
                    }
                })
        }

    fun getScreenIdResponse(): ScreenIdResponse? {
        return screenIdApiResponse.value
    }

    fun getHasTagsSearch(hasTags: String?, offSet:Int?, size:Int?, trackEvent:Boolean) {
        RequestManager.instance.getHasTagsSearch(hasTags,  offSet, size, trackEvent, LanguageLayer.getCurrentLanguageCode(), object : NetworkResultCallback<JsonObject> {
            override fun loading(isLoading: Boolean) {}
            override fun success(status: Boolean?, response: Response<JsonObject>?) {
                 val responseModel = Gson().fromJson(response?.body(), HashTagsSearchResponse::class.java)
                hashTagsSearchResponse.value = responseModel
            }

            override fun failure(status: Boolean, errorCode: Int, message: String) {
                Log.d("checkFailureApi", message)
            }
        })
    }
}