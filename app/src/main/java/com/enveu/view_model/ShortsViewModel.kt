package com.enveu.view_model

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.enveu.SDKConfig
import com.enveu.beanModel.sponsorUserTracking.UserTrackingResponse
import com.enveu.client.api_callback.NetworkResultCallback
import com.enveu.client.networkRequestManager.RequestManager
import com.enveu.jwplayer.player.ChromeCastUrlResponse
import com.enveu.modelClasses.ContentItem
import com.enveu.modelClasses.UserInteractionOfContent
import com.enveu.networking.response.AddCommentResponse
import com.enveu.networking.response.AddLikeResponse
import com.enveu.networking.response.CreateContentDetailsResponse
import com.enveu.networking.response.CreatorShortsResponse
import com.enveu.networking.response.FollowingListResponse
import com.enveu.networking.response.GetPlaylistDetailsResponse
import com.enveu.networking.response.ProfileReportResponse
import com.enveu.networking.response.RemoveLikeResponse
import com.enveu.networking.response.ShortsCommentResponse
import com.enveu.networking.response.SimpleResponse
import com.enveu.networking.response.SubmitReasonResponse
import com.enveu.networking.servicelayer.APIServiceLayer
import com.enveu.ugc.model.PlaylistData
import com.enveu.ugc.model.PlaylistItem
import com.enveu.utils.Constants
import com.enveu.utils.config.LanguageLayer
import com.enveu.utils.helpers.ksPreferenceKeys.KsPreferenceKeys
import com.google.gson.Gson
import com.google.gson.JsonObject
import kotlinx.coroutines.launch
import retrofit2.Response

class ShortsViewModel :ViewModel() {

    private val createUnFollowMutableResponse: MutableLiveData<SimpleResponse> = MutableLiveData()
    val createUnFollowResponse: LiveData<SimpleResponse> = createUnFollowMutableResponse

    private val createFollowerMutableResponse: MutableLiveData<SimpleResponse> = MutableLiveData()
    val createFollowResponse: LiveData<SimpleResponse> = createFollowerMutableResponse

    private val mediaContentDetailsMutableResponse: MutableLiveData<CreateContentDetailsResponse> = MutableLiveData()
    val mediaContentDetailsResponse: LiveData<CreateContentDetailsResponse> = mediaContentDetailsMutableResponse
    var fragment:Fragment? = null
    val userInteractionOfContents = MutableLiveData<List<ContentItem>>()
    val userInteractionOfContentsResponse : LiveData<List<ContentItem>> = userInteractionOfContents


    fun getPlaylistDetails(filterType: String, pageNumber: Int, pageSize: Int): MutableLiveData<GetPlaylistDetailsResponse> {
        val getPlaylistDetailsResponse = MutableLiveData<GetPlaylistDetailsResponse>()
        RequestManager.instance.getPlaylistDetails(KsPreferenceKeys.getInstance().appPrefAccessToken, filterType, LanguageLayer.getCurrentLanguageCode(), pageNumber, pageSize,
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), GetPlaylistDetailsResponse::class.java)
                    getPlaylistDetailsResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = GetPlaylistDetailsResponse()
                    errorModel.responseCode = errorCode
                    getPlaylistDetailsResponse.value = errorModel
                }
            })
        return getPlaylistDetailsResponse
    }

    fun getFollowingList(token: String, appUserId: String?, pageNumber: Int, pageSize: Int, sort: String): MutableLiveData<FollowingListResponse> {
        val followingListResponse = MutableLiveData<FollowingListResponse>()
        RequestManager.instance.getFollowingList(token, appUserId, pageNumber, pageSize, sort,
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel =
                        Gson().fromJson(response?.body(), FollowingListResponse::class.java)
                    followingListResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = FollowingListResponse()
                    errorModel.responseCode = errorCode
                    followingListResponse.value = errorModel
                }
            })
        return followingListResponse


    }

    fun getFollowersList(token: String, appUserId: String?, pageNumber: Int, pageSize: Int, sort: String): MutableLiveData<FollowingListResponse> {
        val followingListResponse = MutableLiveData<FollowingListResponse>()
        RequestManager.instance.getFollowersList(token, appUserId, pageNumber, pageSize, sort,
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel =
                        Gson().fromJson(response?.body(), FollowingListResponse::class.java)
                    followingListResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = FollowingListResponse()
                    errorModel.responseCode = errorCode
                    followingListResponse.value = errorModel
                }
            })
        return followingListResponse
    }

    fun setFollowUser(token: String, appUserId: Int?) {
        RequestManager.instance.setFollowUser(token, appUserId, object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), SimpleResponse::class.java)
                    createFollowerMutableResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = SimpleResponse()
                    errorModel.responseCode = errorCode
                    createFollowerMutableResponse.value = errorModel
                }
            })
    }

    fun setUnFollowUser(token: String, appUserId: Int?) {
        RequestManager.instance.setUnFollowUser(token, appUserId,
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), SimpleResponse::class.java)
                    createUnFollowMutableResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = SimpleResponse()
                    errorModel.responseCode = errorCode
                    createUnFollowMutableResponse.value = errorModel
                }
            })
    }

    fun getShortsCommentList(contentId: Int?, pageNumber: Int, pageSize: Int): MutableLiveData<ShortsCommentResponse> {
        val shortsCommentResponse = MutableLiveData<ShortsCommentResponse>()
        RequestManager.instance.getShortsCommentList(SDKConfig.getInstance().monetizationBaseURL, KsPreferenceKeys.getInstance().appPrefAccessToken, contentId, pageNumber, pageSize, object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), ShortsCommentResponse::class.java)
                    shortsCommentResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = ShortsCommentResponse()
                    errorModel.responseCode = errorCode
                    shortsCommentResponse.value = errorModel
                }
            })
        return shortsCommentResponse
    }

    fun addShortsCommentApi(token: String, jsonObject: JsonObject):MutableLiveData<AddCommentResponse> {
            val addCommentResponse = MutableLiveData<AddCommentResponse>()
            RequestManager.instance.addShortsCommentApi(SDKConfig.getInstance().monetizationBaseURL, token, jsonObject,
                object : NetworkResultCallback<JsonObject> {
                    override fun loading(isLoading: Boolean) {}
                    override fun success(status: Boolean?, response: Response<JsonObject>?) {
                        val responseModel = Gson().fromJson(response?.body(), AddCommentResponse::class.java)
                        addCommentResponse.value = responseModel
                    }

                    override fun failure(status: Boolean, errorCode: Int, message: String) {
                        val errorModel = AddCommentResponse()
                        errorModel.responseCode = errorCode
                        addCommentResponse.value = errorModel
                    }
                })
            return addCommentResponse
    }

    fun getMediaContentDetails(mediaContentId: Long?) {
        RequestManager.instance.getMediaContentDetails(mediaContentId, LanguageLayer.getCurrentLanguageCode(), object : NetworkResultCallback<JsonObject> {
            override fun loading(isLoading: Boolean) {}
            override fun success(status: Boolean?, response: Response<JsonObject>?) {
                val responseModel = Gson().fromJson(response?.body(), CreateContentDetailsResponse::class.java)
                mediaContentDetailsMutableResponse.value = responseModel
            }

            override fun failure(status: Boolean, errorCode: Int, message: String) {
                val errorModel = CreateContentDetailsResponse()
                errorModel.responseCode = errorCode
                mediaContentDetailsMutableResponse.value = errorModel
            }
        })
    }

    fun getAllReasons(identifier: String?):MutableLiveData<ProfileReportResponse> {
        val profileReportResponse = MutableLiveData<ProfileReportResponse>()
        RequestManager.instance.getAllReasons(SDKConfig.getInstance().monetizationBaseURL, identifier, LanguageLayer.getCurrentLanguageCode(),
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), ProfileReportResponse::class.java)
                    profileReportResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = ProfileReportResponse()
                    errorModel.responseCode = errorCode
                    profileReportResponse.value = errorModel
                }
            })
        return profileReportResponse
    }

    fun selectReasonSubmit(token: String, jsonObject: JsonObject):MutableLiveData<SubmitReasonResponse> {
        val submitReasonResponse = MutableLiveData<SubmitReasonResponse>()
        RequestManager.instance.selectReasonSubmit(SDKConfig.getInstance().monetizationBaseURL, token, jsonObject,
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), SubmitReasonResponse::class.java)
                    submitReasonResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = SubmitReasonResponse()
                    errorModel.responseCode = errorCode
                    submitReasonResponse.value = errorModel
                }
            })
        return submitReasonResponse
    }

    fun getCreatorShorts(reelCreatorId:Long?, pageNumber: Int, pageSize: Int):MutableLiveData<CreatorShortsResponse> {
        val creatorShortsResponse = MutableLiveData<CreatorShortsResponse>()
        RequestManager.instance.getCreatorShorts("${Constants.CREATOR_ID}${reelCreatorId}", Constants.SORT_BY, Constants.SORT_ORDER, pageNumber,  pageSize, LanguageLayer.getCurrentLanguageCode(),
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), CreatorShortsResponse::class.java)
                    creatorShortsResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = CreatorShortsResponse()
                    errorModel.responseCode = errorCode
                    creatorShortsResponse.value = errorModel
                }
            })
        return creatorShortsResponse
    }

    fun setRemoveFollower(token: String, appUserId: Int?): MutableLiveData<SimpleResponse> {
        val removeFollowerResponse = MutableLiveData<SimpleResponse>()
        RequestManager.instance.setRemoveFollower(token, appUserId, object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), SimpleResponse::class.java)
                    removeFollowerResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = SimpleResponse()
                    errorModel.responseCode = errorCode
                    removeFollowerResponse.value = errorModel
                }
            })
        return removeFollowerResponse
    }


    fun setLikeReels(token: String, jsonObject: JsonObject):MutableLiveData<AddLikeResponse> {
        val addLikeResponse = MutableLiveData<AddLikeResponse>()
        RequestManager.instance.setLikeReels(SDKConfig.getInstance().monetizationBaseURL, token, jsonObject,
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), AddLikeResponse::class.java)
                    addLikeResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = AddLikeResponse()
                    errorModel.responseCode = errorCode
                    addLikeResponse.value = errorModel
                }
            }
        )
        return addLikeResponse
    }

    fun setUnLikeReels(token: String, jsonObject: JsonObject):MutableLiveData<RemoveLikeResponse> {
        val removeLikeResponse = MutableLiveData<RemoveLikeResponse>()
        RequestManager.instance.setUnLikeReels(SDKConfig.getInstance().monetizationBaseURL, token, jsonObject,
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), RemoveLikeResponse::class.java)
                    removeLikeResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = RemoveLikeResponse()
                    errorModel.responseCode = errorCode
                    removeLikeResponse.value = errorModel
                }
            }
        )
        return removeLikeResponse
    }


    fun reelsShareApi(jsonObject: JsonObject):MutableLiveData<SimpleResponse> {
        val shareResponse = MutableLiveData<SimpleResponse>()
        RequestManager.instance.reelsShareApi(SDKConfig.getInstance().monetizationBaseURL, jsonObject,
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), SimpleResponse::class.java)
                    shareResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = SimpleResponse()
                    errorModel.responseCode = errorCode
                    shareResponse.value = errorModel
                }
            })
        return shareResponse
    }

    fun setEventTracking(jsonObject: JsonObject?, token: String?): LiveData<UserTrackingResponse> {
        return APIServiceLayer.getInstance().getSponsorUserTracking(jsonObject, token)
    }

    fun checkAndCallFollow(mediaContentIds: String){
        APIServiceLayer.getInstance().callToCheckContentFollowLikeByUser(mediaContentIds).observeForever { jsonObject ->
            if (jsonObject != null) {
                val parsed = Gson().fromJson(jsonObject, UserInteractionOfContent::class.java)
                parsed?.data?.let { contentItems ->
                    userInteractionOfContents.value = contentItems
                }
            }
        }
    }

    fun callFavoriteListApi(token: String?,page:Int,size:Int): MutableLiveData<PlaylistItem?> {
        val playlistDataList = MutableLiveData<PlaylistItem?>()
        viewModelScope.launch {
            APIServiceLayer.getInstance().callFavoriteListApi(token,page,size,object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val gson=Gson()
                    val responseModel = gson.fromJson(response?.body(), SimpleResponse::class.java)
                    val data = gson.toJson(responseModel.data)
                    val playListData = gson.fromJson(data, PlaylistData::class.java)
                    if (playListData.items?.isEmpty()==false){
                        playListData?.items?.get(0)?.playlistSlug?.let {
                            token?.let { it1 -> callForFavoriteListByPlaylistSlug(it1,it,playlistDataList) }
                        }
                    } else {
                        playlistDataList.postValue(null)
                    }
//                    playlistDataList.postValue(playListData)
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = SimpleResponse()
                    errorModel.responseCode = errorCode
                }
            })
        }
        return playlistDataList
    }

    private fun callForFavoriteListByPlaylistSlug(
        token: String,
        playListSlug: String,
        playlistDataList: MutableLiveData<PlaylistItem?>
    ) {
        viewModelScope?.launch {
            APIServiceLayer.getInstance().callFavoriteListApiBySlug(token,playListSlug,object : NetworkResultCallback<JsonObject>{
                override fun failure(status: Boolean, errorCode: Int, message: String) {

                }

                override fun loading(isLoading: Boolean) {

                }

                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val gson=Gson()
                    val responseModel = gson.fromJson(response?.body(), SimpleResponse::class.java)
                    val data = gson.toJson(responseModel.data)
                    val playlistData = gson.fromJson(data, PlaylistItem::class.java)

                    playlistDataList.postValue(playlistData)
                }

            })
        }
    }

    fun getChromeCastUrl(accountId: String, videoId: String?): MutableLiveData<String?> {
        val acceptKey = "application/json;pk=${SDKConfig.BRIGHT_COVE_POLICY_KEY}"
        val baseUrl = "https://edge.api.brightcove.com/playback/v1/accounts/${accountId}/videos/${videoId}"
        val chromeCastBaeUrl = MutableLiveData<String?>()
        APIServiceLayer.getInstance().getChromeCastUrl(baseUrl, acceptKey, object : NetworkResultCallback<ChromeCastUrlResponse> {
            override fun loading(isLoading: Boolean) {}
            override fun success(status: Boolean?, response: Response<ChromeCastUrlResponse>?) {
                val sources = response?.body()?.sources
                if (!sources.isNullOrEmpty()) {
                    for (source in sources) {
                        if (source?.codecs == "avc1,mp4a") {
                            chromeCastBaeUrl.value = source.src
                            break
                        }
                    }
                }
            }
            override fun failure(status: Boolean, errorCode: Int, message: String) {
                chromeCastBaeUrl.value = null
            }
        })
        return chromeCastBaeUrl
    }

    fun getHasTagsReels(hasTags: String?, contentType: String?, videoType: String?, offSet: Int?, size: Int?, trackEvent: Boolean): LiveData<CreatorShortsResponse?> {
        val data = MutableLiveData<CreatorShortsResponse?>()
        RequestManager.instance.getHasTagsReels(hasTags, contentType, videoType, offSet, size, trackEvent, LanguageLayer.getCurrentLanguageCode(), object : NetworkResultCallback<JsonObject> {
            override fun loading(isLoading: Boolean) {}
            override fun success(status: Boolean?, response: Response<JsonObject>?) {
                response?.body()?.let {
                    val response = Gson().fromJson(it, CreatorShortsResponse::class.java)
                    data.value = response
                }
            }
            override fun failure(status: Boolean, errorCode: Int, message: String) {
                data.value?.responseCode = errorCode
            }
        })
        return data
    }

    fun setReelsLike(token: String, contentId: Int?):MutableLiveData<SimpleResponse> {
        val addLikeResponse = MutableLiveData<SimpleResponse>()
        RequestManager.instance.setReelsLike(SDKConfig.getInstance().subscriptioN_BASE_URL, token, contentId,
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), SimpleResponse::class.java)
                    addLikeResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = SimpleResponse()
                    errorModel.responseCode = errorCode
                    addLikeResponse.value = errorModel
                }
            }
        )
        return addLikeResponse
    }

    fun setReelsUnLike(token: String, contentId: Int?):MutableLiveData<SimpleResponse> {
        val addLikeResponse = MutableLiveData<SimpleResponse>()
        RequestManager.instance.setUnReelsLike(SDKConfig.getInstance().subscriptioN_BASE_URL, token, contentId,
            object : NetworkResultCallback<JsonObject> {
                override fun loading(isLoading: Boolean) {}
                override fun success(status: Boolean?, response: Response<JsonObject>?) {
                    val responseModel = Gson().fromJson(response?.body(), SimpleResponse::class.java)
                    addLikeResponse.value = responseModel
                }

                override fun failure(status: Boolean, errorCode: Int, message: String) {
                    val errorModel = SimpleResponse()
                    errorModel.responseCode = errorCode
                    addLikeResponse.value = errorModel
                }
            }
        )
        return addLikeResponse
    }
}